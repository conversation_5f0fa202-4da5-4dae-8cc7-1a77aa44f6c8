import 'package:flutter/material.dart';
import '../interactive_coordinate_plane_grapher_widget.dart';

/// A simple test app for the Interactive Coordinate Plane Grapher widget
void main() {
  runApp(const CoordinatePlaneGrapherTestApp());
}

class CoordinatePlaneGrapherTestApp extends StatelessWidget {
  const CoordinatePlaneGrapherTestApp({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return MaterialApp(
      title: 'Coordinate Plane Grapher Test',
      theme: ThemeData(
        primarySwatch: Colors.blue,
        visualDensity: VisualDensity.adaptivePlatformDensity,
      ),
      home: const CoordinatePlaneGrapherTestScreen(),
    );
  }
}

class CoordinatePlaneGrapherTestScreen extends StatelessWidget {
  const CoordinatePlaneGrapherTestScreen({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    // Sample data for testing
    final testData = {
      'title': 'Coordinate Plane Grapher Test',
      'description': 'Test the coordinate plane grapher widget with various features.',
      'xMin': -10.0,
      'xMax': 10.0,
      'yMin': -10.0,
      'yMax': 10.0,
      'gridSpacing': 1.0,
      'showGrid': true,
      'showAxes': true,
      'gridColor': '#CCCCCC',
      'axisColor': '#000000',
      'pointColor': '#FF5722',
      'lineColor': '#2196F3',
      'functionColor': '#4CAF50',
      'initialMode': 'point',
      'initialPoints': [
        {
          'x': 2.0,
          'y': 3.0,
          'label': 'A',
        },
        {
          'x': -4.0,
          'y': 1.0,
          'label': 'B',
        },
      ],
      'initialLines': [
        {
          'startX': 2.0,
          'startY': 3.0,
          'endX': -4.0,
          'endY': 1.0,
          'label': 'Line AB',
        },
      ],
      'initialFunctions': [
        {
          'expression': 'y = x^2',
          'color': '#4CAF50',
        },
      ],
      'showNameTag': true,
    };

    return Scaffold(
      appBar: AppBar(
        title: const Text('Coordinate Plane Grapher Test'),
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'Interactive Coordinate Plane Grapher Widget',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 16),
            const Text(
              'This is a test for the Interactive Coordinate Plane Grapher widget. '
              'You can add points, lines, and functions to the coordinate plane.',
              style: TextStyle(fontSize: 14),
            ),
            const SizedBox(height: 24),
            InteractiveCoordinatePlaneGrapherWidget(
              data: testData,
              onStateChanged: (isCompleted) {
                print('Widget state changed: $isCompleted');
              },
            ),
            const SizedBox(height: 24),
            const Text(
              'Test Instructions:',
              style: TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 8),
            const Text(
              '1. Try adding points by clicking on the coordinate plane in "Point" mode\n'
              '2. Try adding lines by clicking two points in "Line" mode\n'
              '3. Try adding functions by entering expressions like "y = x^2" in "Function" mode\n'
              '4. Verify that the grid, axes, points, lines, and functions are displayed correctly\n'
              '5. Test the clear all button to reset the coordinate plane',
              style: TextStyle(fontSize: 14),
            ),
          ],
        ),
      ),
    );
  }
}
