import 'package:flutter/material.dart';
import 'dart:math' as math;

class InteractiveGrowingPatternsVisualizerWidget extends StatefulWidget {
  final Map<String, dynamic> data;

  const InteractiveGrowingPatternsVisualizerWidget({
    Key? key,
    required this.data,
  }) : super(key: key);

  factory InteractiveGrowingPatternsVisualizerWidget.fromData(Map<String, dynamic> data) {
    return InteractiveGrowingPatternsVisualizerWidget(data: data);
  }

  @override
  _InteractiveGrowingPatternsVisualizerWidgetState createState() =>
      _InteractiveGrowingPatternsVisualizerWidgetState();
}

class _InteractiveGrowingPatternsVisualizerWidgetState
    extends State<InteractiveGrowingPatternsVisualizerWidget> with SingleTickerProviderStateMixin {
  late String _title;
  late List<String> _patternTypes;
  late String _selectedPatternType;
  late int _currentStep;
  late int _maxSteps;
  late bool _isAnimating;
  late AnimationController _animationController;
  late List<Color> _colors;
  late bool _showNameTag;

  @override
  void initState() {
    super.initState();
    _title = widget.data['title'] ?? 'Growing Patterns Visualizer';
    _patternTypes = List<String>.from(widget.data['patternTypes'] ?? [
      'Arithmetic',
      'Geometric',
      'Fibonacci',
      'Triangular',
      'Square',
    ]);
    _selectedPatternType = widget.data['defaultPatternType'] ?? _patternTypes[0];
    _currentStep = widget.data['initialStep'] ?? 1;
    _maxSteps = widget.data['maxSteps'] ?? 10;
    _isAnimating = false;
    _showNameTag = widget.data['showNameTag'] ?? true;

    // Initialize colors
    final List<dynamic> colorData = widget.data['colors'] ?? [
      '#4CAF50',
      '#2196F3',
      '#FFC107',
      '#E91E63',
      '#9C27B0',
    ];
    
    _colors = colorData.map((color) {
      if (color is String) {
        return Color(int.parse(color.substring(1, 7), radix: 16) + 0xFF000000);
      }
      return Colors.blue;
    }).toList();

    // Initialize animation controller
    _animationController = AnimationController(
      vsync: this,
      duration: Duration(milliseconds: widget.data['animationDuration'] ?? 1000),
    );

    _animationController.addStatusListener((status) {
      if (status == AnimationStatus.completed) {
        if (_isAnimating && _currentStep < _maxSteps) {
          setState(() {
            _currentStep++;
          });
          _animationController.reset();
          _animationController.forward();
        } else {
          setState(() {
            _isAnimating = false;
          });
        }
      }
    });
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  void _toggleAnimation() {
    setState(() {
      _isAnimating = !_isAnimating;
      if (_isAnimating) {
        if (_currentStep >= _maxSteps) {
          _currentStep = 1;
        }
        _animationController.reset();
        _animationController.forward();
      } else {
        _animationController.stop();
      }
    });
  }

  void _resetPattern() {
    setState(() {
      _currentStep = 1;
      _isAnimating = false;
      _animationController.reset();
    });
  }

  void _incrementStep() {
    if (_currentStep < _maxSteps) {
      setState(() {
        _currentStep++;
      });
    }
  }

  void _decrementStep() {
    if (_currentStep > 1) {
      setState(() {
        _currentStep--;
      });
    }
  }

  List<int> _generatePatternValues() {
    List<int> values = [];
    
    switch (_selectedPatternType) {
      case 'Arithmetic':
        final int firstTerm = widget.data['arithmeticFirstTerm'] ?? 1;
        final int commonDifference = widget.data['arithmeticCommonDifference'] ?? 2;
        for (int i = 0; i < _currentStep; i++) {
          values.add(firstTerm + i * commonDifference);
        }
        break;
        
      case 'Geometric':
        final int firstTerm = widget.data['geometricFirstTerm'] ?? 1;
        final int commonRatio = widget.data['geometricCommonRatio'] ?? 2;
        for (int i = 0; i < _currentStep; i++) {
          values.add(firstTerm * math.pow(commonRatio, i).toInt());
        }
        break;
        
      case 'Fibonacci':
        if (_currentStep >= 1) values.add(1);
        if (_currentStep >= 2) values.add(1);
        for (int i = 2; i < _currentStep; i++) {
          values.add(values[i - 1] + values[i - 2]);
        }
        break;
        
      case 'Triangular':
        for (int i = 1; i <= _currentStep; i++) {
          values.add((i * (i + 1)) ~/ 2);
        }
        break;
        
      case 'Square':
        for (int i = 1; i <= _currentStep; i++) {
          values.add(i * i);
        }
        break;
        
      default:
        for (int i = 1; i <= _currentStep; i++) {
          values.add(i);
        }
    }
    
    return values;
  }

  String _getPatternFormula() {
    switch (_selectedPatternType) {
      case 'Arithmetic':
        final int firstTerm = widget.data['arithmeticFirstTerm'] ?? 1;
        final int commonDifference = widget.data['arithmeticCommonDifference'] ?? 2;
        return 'a_n = $firstTerm + (n - 1) × $commonDifference';
      case 'Geometric':
        final int firstTerm = widget.data['geometricFirstTerm'] ?? 1;
        final int commonRatio = widget.data['geometricCommonRatio'] ?? 2;
        return 'a_n = $firstTerm × ${commonRatio}^(n-1)';
      case 'Fibonacci':
        return 'F_n = F_(n-1) + F_(n-2), where F_1 = F_2 = 1';
      case 'Triangular':
        return 'T_n = n(n+1)/2';
      case 'Square':
        return 'S_n = n²';
      default:
        return '';
    }
  }

  @override
  Widget build(BuildContext context) {
    final patternValues = _generatePatternValues();
    final patternFormula = _getPatternFormula();
    
    return Card(
      elevation: 4,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            if (_showNameTag)
              Padding(
                padding: const EdgeInsets.only(bottom: 8.0),
                child: Text(
                  _title,
                  style: Theme.of(context).textTheme.titleLarge,
                ),
              ),
            
            // Pattern type selector
            DropdownButton<String>(
              value: _selectedPatternType,
              isExpanded: true,
              onChanged: (String? newValue) {
                if (newValue != null) {
                  setState(() {
                    _selectedPatternType = newValue;
                    _resetPattern();
                  });
                }
              },
              items: _patternTypes.map<DropdownMenuItem<String>>((String value) {
                return DropdownMenuItem<String>(
                  value: value,
                  child: Text(value),
                );
              }).toList(),
            ),
            
            const SizedBox(height: 16),
            
            // Pattern formula
            Container(
              padding: const EdgeInsets.all(8),
              decoration: BoxDecoration(
                color: Colors.grey[200],
                borderRadius: BorderRadius.circular(8),
              ),
              child: Text(
                'Formula: $patternFormula',
                style: const TextStyle(
                  fontFamily: 'Courier',
                  fontWeight: FontWeight.bold,
                ),
              ),
            ),
            
            const SizedBox(height: 16),
            
            // Pattern visualization
            Container(
              height: 200,
              decoration: BoxDecoration(
                border: Border.all(color: Colors.grey),
                borderRadius: BorderRadius.circular(8),
              ),
              child: _buildPatternVisualization(patternValues),
            ),
            
            const SizedBox(height: 16),
            
            // Pattern sequence
            Container(
              padding: const EdgeInsets.all(8),
              decoration: BoxDecoration(
                color: Colors.grey[200],
                borderRadius: BorderRadius.circular(8),
              ),
              child: Text(
                'Sequence: ${patternValues.join(', ')}',
                style: const TextStyle(fontFamily: 'Courier'),
              ),
            ),
            
            const SizedBox(height: 16),
            
            // Controls
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceEvenly,
              children: [
                IconButton(
                  icon: const Icon(Icons.refresh),
                  onPressed: _resetPattern,
                  tooltip: 'Reset',
                ),
                IconButton(
                  icon: const Icon(Icons.arrow_back),
                  onPressed: _currentStep > 1 ? _decrementStep : null,
                  tooltip: 'Previous Step',
                ),
                Text('Step $_currentStep of $_maxSteps'),
                IconButton(
                  icon: const Icon(Icons.arrow_forward),
                  onPressed: _currentStep < _maxSteps ? _incrementStep : null,
                  tooltip: 'Next Step',
                ),
                IconButton(
                  icon: Icon(_isAnimating ? Icons.pause : Icons.play_arrow),
                  onPressed: _toggleAnimation,
                  tooltip: _isAnimating ? 'Pause' : 'Play',
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildPatternVisualization(List<int> values) {
    switch (_selectedPatternType) {
      case 'Arithmetic':
      case 'Geometric':
      case 'Fibonacci':
        return _buildBarVisualization(values);
      case 'Triangular':
        return _buildTriangularVisualization(values.last);
      case 'Square':
        return _buildSquareVisualization(values.last);
      default:
        return _buildBarVisualization(values);
    }
  }

  Widget _buildBarVisualization(List<int> values) {
    final maxValue = values.isEmpty ? 1 : values.reduce(math.max);
    
    return Padding(
      padding: const EdgeInsets.all(16.0),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.end,
        mainAxisAlignment: MainAxisAlignment.spaceEvenly,
        children: List.generate(values.length, (index) {
          final double heightPercentage = values[index] / maxValue;
          return Expanded(
            child: Padding(
              padding: const EdgeInsets.symmetric(horizontal: 4.0),
              child: Column(
                mainAxisAlignment: MainAxisAlignment.end,
                children: [
                  Text('${values[index]}'),
                  const SizedBox(height: 4),
                  AnimatedContainer(
                    duration: const Duration(milliseconds: 300),
                    height: 120 * heightPercentage,
                    decoration: BoxDecoration(
                      color: _colors[index % _colors.length],
                      borderRadius: BorderRadius.circular(4),
                    ),
                  ),
                  const SizedBox(height: 4),
                  Text('${index + 1}'),
                ],
              ),
            ),
          );
        }),
      ),
    );
  }

  Widget _buildTriangularVisualization(int n) {
    return Center(
      child: CustomPaint(
        size: const Size(200, 200),
        painter: TriangularNumberPainter(n, _colors),
      ),
    );
  }

  Widget _buildSquareVisualization(int n) {
    return Center(
      child: CustomPaint(
        size: const Size(200, 200),
        painter: SquareNumberPainter(n, _colors),
      ),
    );
  }
}

class TriangularNumberPainter extends CustomPainter {
  final int n;
  final List<Color> colors;

  TriangularNumberPainter(this.n, this.colors);

  @override
  void paint(Canvas canvas, Size size) {
    final double dotRadius = math.min(size.width, size.height) / (2 * n + 2);
    final double centerX = size.width / 2;
    final double centerY = size.height / 2;
    
    int dotCount = 0;
    for (int row = 0; row < n; row++) {
      for (int col = 0; col <= row; col++) {
        final double x = centerX - (row * dotRadius) + (col * 2 * dotRadius);
        final double y = centerY - (n * dotRadius) / 2 + (row * dotRadius * 1.5);
        
        final paint = Paint()
          ..color = colors[dotCount % colors.length]
          ..style = PaintingStyle.fill;
        
        canvas.drawCircle(Offset(x, y), dotRadius * 0.8, paint);
        dotCount++;
      }
    }
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => true;
}

class SquareNumberPainter extends CustomPainter {
  final int n;
  final List<Color> colors;

  SquareNumberPainter(this.n, this.colors);

  @override
  void paint(Canvas canvas, Size size) {
    final double dotRadius = math.min(size.width, size.height) / (2 * n + 2);
    final double startX = (size.width - (n * dotRadius * 2)) / 2;
    final double startY = (size.height - (n * dotRadius * 2)) / 2;
    
    int dotCount = 0;
    for (int row = 0; row < n; row++) {
      for (int col = 0; col < n; col++) {
        final double x = startX + col * dotRadius * 2;
        final double y = startY + row * dotRadius * 2;
        
        final paint = Paint()
          ..color = colors[dotCount % colors.length]
          ..style = PaintingStyle.fill;
        
        canvas.drawCircle(Offset(x, y), dotRadius * 0.8, paint);
        dotCount++;
      }
    }
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => true;
}
