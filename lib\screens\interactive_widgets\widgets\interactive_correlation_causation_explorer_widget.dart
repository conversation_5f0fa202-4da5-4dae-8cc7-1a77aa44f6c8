import 'package:flutter/material.dart';
import 'dart:math' as math;

/// A widget that allows users to explore the difference between correlation and causation
class InteractiveCorrelationCausationExplorerWidget extends StatefulWidget {
  /// The data for this widget
  final Map<String, dynamic> data;

  /// Optional callback when the widget state changes
  final Function(bool isCompleted)? onStateChanged;

  const InteractiveCorrelationCausationExplorerWidget({
    super.key,
    required this.data,
    this.onStateChanged,
  });

  /// Factory constructor to create the widget from JSON data
  factory InteractiveCorrelationCausationExplorerWidget.fromData(Map<String, dynamic> data) {
    return InteractiveCorrelationCausationExplorerWidget(
      data: data,
    );
  }

  @override
  State<InteractiveCorrelationCausationExplorerWidget> createState() => _InteractiveCorrelationCausationExplorerWidgetState();
}

class _InteractiveCorrelationCausationExplorerWidgetState extends State<InteractiveCorrelationCausationExplorerWidget> {
  // Scenarios
  late List<CorrelationScenario> _scenarios;
  late int _currentScenarioIndex;
  
  // Analysis state
  late RelationshipType _selectedRelationship;
  late String _explanation;
  late bool _hasSubmitted;
  late bool _isCorrect;
  
  // UI state
  late bool _isCompleted;
  late bool _showExplanation;
  late TextEditingController _explanationController;
  
  // Colors
  late Color _primaryColor;
  late Color _secondaryColor;
  late Color _accentColor;
  late Color _backgroundColor;
  late Color _textColor;

  @override
  void initState() {
    super.initState();
    _initializeWidget();
  }

  @override
  void dispose() {
    _explanationController.dispose();
    super.dispose();
  }

  void _initializeWidget() {
    // Initialize colors
    _primaryColor = _parseColor(widget.data['primaryColor'] ?? '#2196F3');
    _secondaryColor = _parseColor(widget.data['secondaryColor'] ?? '#FF9800');
    _accentColor = _parseColor(widget.data['accentColor'] ?? '#4CAF50');
    _backgroundColor = _parseColor(widget.data['backgroundColor'] ?? '#FFFFFF');
    _textColor = _parseColor(widget.data['textColor'] ?? '#212121');

    // Initialize scenarios
    final List<dynamic> scenariosData = widget.data['scenarios'] ?? [];
    _scenarios = scenariosData.map((scenarioData) => CorrelationScenario.fromJson(scenarioData)).toList();
    _currentScenarioIndex = 0;
    
    // Initialize analysis state
    _explanationController = TextEditingController();
    _resetAnalysis();
  }

  void _resetAnalysis() {
    _selectedRelationship = RelationshipType.unknown;
    _explanation = '';
    _explanationController.clear();
    _hasSubmitted = false;
    _isCorrect = false;
    _isCompleted = false;
    _showExplanation = false;
  }

  Color _parseColor(String colorString) {
    if (colorString.startsWith('#')) {
      return Color(int.parse('FF${colorString.substring(1)}', radix: 16));
    }
    return Colors.blue;
  }

  void _selectRelationship(RelationshipType type) {
    setState(() {
      _selectedRelationship = type;
    });
  }

  void _submitAnalysis() {
    if (_scenarios.isEmpty) return;
    
    CorrelationScenario scenario = _scenarios[_currentScenarioIndex];
    _explanation = _explanationController.text;
    _isCorrect = _selectedRelationship == scenario.correctRelationship;
    
    setState(() {
      _hasSubmitted = true;
    });
  }

  void _nextScenario() {
    if (_currentScenarioIndex < _scenarios.length - 1) {
      setState(() {
        _currentScenarioIndex++;
        _resetAnalysis();
      });
    } else if (!_isCompleted) {
      setState(() {
        _isCompleted = true;
      });
      widget.onStateChanged?.call(true);
    }
  }

  void _toggleExplanation() {
    setState(() {
      _showExplanation = !_showExplanation;
    });
  }

  @override
  Widget build(BuildContext context) {
    if (_scenarios.isEmpty) {
      return const Center(child: Text('No scenarios available'));
    }

    CorrelationScenario scenario = _scenarios[_currentScenarioIndex];

    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
        side: BorderSide(color: _primaryColor.withOpacity(0.5), width: 1),
      ),
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Title
            Text(
              widget.data['title'] ?? 'Correlation vs. Causation Explorer',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
                color: _textColor,
              ),
            ),

            const SizedBox(height: 8),

            // Scenario navigation
            Row(
              children: [
                Text(
                  'Scenario ${_currentScenarioIndex + 1} of ${_scenarios.length}',
                  style: TextStyle(
                    fontSize: 14,
                    fontWeight: FontWeight.bold,
                    color: _primaryColor,
                  ),
                ),
                const Spacer(),
                IconButton(
                  icon: Icon(_showExplanation ? Icons.info_outline : Icons.info),
                  onPressed: _hasSubmitted ? _toggleExplanation : null,
                  tooltip: _showExplanation ? 'Hide Explanation' : 'Show Explanation',
                  color: _secondaryColor,
                ),
              ],
            ),

            const SizedBox(height: 8),

            // Scenario description
            Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: _backgroundColor,
                borderRadius: BorderRadius.circular(8),
                border: Border.all(color: _primaryColor.withOpacity(0.3)),
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'Scenario:',
                    style: TextStyle(
                      fontWeight: FontWeight.bold,
                      color: _textColor,
                    ),
                  ),
                  const SizedBox(height: 4),
                  Text(
                    scenario.description,
                    style: TextStyle(color: _textColor.withOpacity(0.8)),
                  ),
                ],
              ),
            ),

            const SizedBox(height: 16),

            // Data visualization (if provided)
            if (scenario.chartUrl.isNotEmpty) ...[
              Container(
                height: 200,
                width: double.infinity,
                decoration: BoxDecoration(
                  color: Colors.grey.withOpacity(0.1),
                  borderRadius: BorderRadius.circular(8),
                  border: Border.all(color: _primaryColor.withOpacity(0.3)),
                  image: DecorationImage(
                    image: NetworkImage(scenario.chartUrl),
                    fit: BoxFit.contain,
                  ),
                ),
              ),
              const SizedBox(height: 16),
            ],

            // Instructions
            Text(
              'What type of relationship is most likely present in this scenario?',
              style: TextStyle(
                fontWeight: FontWeight.bold,
                color: _textColor,
              ),
            ),

            const SizedBox(height: 12),

            // Relationship selection
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceEvenly,
              children: [
                _buildRelationshipButton(
                  RelationshipType.causal,
                  'Causal',
                  'A causes B',
                ),
                _buildRelationshipButton(
                  RelationshipType.correlation,
                  'Correlation',
                  'A and B are related',
                ),
                _buildRelationshipButton(
                  RelationshipType.coincidence,
                  'Coincidence',
                  'No real relationship',
                ),
              ],
            ),

            const SizedBox(height: 16),

            // Explanation input
            TextField(
              controller: _explanationController,
              decoration: InputDecoration(
                labelText: 'Explain your reasoning',
                hintText: 'Why did you select this relationship type?',
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(8),
                ),
                enabled: !_hasSubmitted,
              ),
              maxLines: 3,
            ),

            const SizedBox(height: 16),

            // Submit button
            if (!_hasSubmitted)
              Center(
                child: ElevatedButton(
                  onPressed: _selectedRelationship != RelationshipType.unknown ? _submitAnalysis : null,
                  style: ElevatedButton.styleFrom(
                    backgroundColor: _primaryColor,
                    foregroundColor: Colors.white,
                    padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(8),
                    ),
                  ),
                  child: const Text('Submit Analysis'),
                ),
              ),

            // Results
            if (_hasSubmitted) ...[
              // Feedback
              Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: _isCorrect ? Colors.green.withOpacity(0.1) : Colors.red.withOpacity(0.1),
                  borderRadius: BorderRadius.circular(8),
                  border: Border.all(color: _isCorrect ? Colors.green : Colors.red),
                ),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      children: [
                        Icon(
                          _isCorrect ? Icons.check_circle : Icons.error,
                          color: _isCorrect ? Colors.green : Colors.red,
                        ),
                        const SizedBox(width: 8),
                        Text(
                          _isCorrect ? 'Correct!' : 'Not quite right',
                          style: TextStyle(
                            fontWeight: FontWeight.bold,
                            color: _isCorrect ? Colors.green : Colors.red,
                          ),
                        ),
                      ],
                    ),
                    const SizedBox(height: 8),
                    Text(
                      'The relationship is: ${_getRelationshipName(scenario.correctRelationship)}',
                      style: TextStyle(
                        fontWeight: FontWeight.bold,
                        color: _textColor,
                      ),
                    ),
                    const SizedBox(height: 4),
                    Text(
                      _isCorrect ? scenario.correctFeedback : scenario.incorrectFeedback,
                      style: TextStyle(color: _textColor.withOpacity(0.8)),
                    ),
                  ],
                ),
              ),

              // User explanation
              if (_explanation.isNotEmpty) ...[
                const SizedBox(height: 16),
                Container(
                  padding: const EdgeInsets.all(12),
                  decoration: BoxDecoration(
                    color: _backgroundColor,
                    borderRadius: BorderRadius.circular(8),
                    border: Border.all(color: _primaryColor.withOpacity(0.3)),
                  ),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'Your Explanation:',
                        style: TextStyle(
                          fontWeight: FontWeight.bold,
                          color: _textColor,
                        ),
                      ),
                      const SizedBox(height: 4),
                      Text(
                        _explanation,
                        style: TextStyle(
                          fontStyle: FontStyle.italic,
                          color: _textColor.withOpacity(0.8),
                        ),
                      ),
                    ],
                  ),
                ),
              ],

              // Expert explanation (if shown)
              if (_showExplanation) ...[
                const SizedBox(height: 16),
                Container(
                  padding: const EdgeInsets.all(12),
                  decoration: BoxDecoration(
                    color: _secondaryColor.withOpacity(0.1),
                    borderRadius: BorderRadius.circular(8),
                    border: Border.all(color: _secondaryColor.withOpacity(0.3)),
                  ),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'Expert Explanation:',
                        style: TextStyle(
                          fontWeight: FontWeight.bold,
                          color: _secondaryColor,
                        ),
                      ),
                      const SizedBox(height: 4),
                      Text(
                        scenario.expertExplanation,
                        style: TextStyle(color: _textColor.withOpacity(0.8)),
                      ),
                    ],
                  ),
                ),
              ],

              const SizedBox(height: 16),

              // Navigation buttons
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                children: [
                  OutlinedButton(
                    onPressed: () => setState(() => _resetAnalysis()),
                    style: OutlinedButton.styleFrom(
                      foregroundColor: _primaryColor,
                      side: BorderSide(color: _primaryColor),
                      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                    ),
                    child: const Text('Try Again'),
                  ),
                  ElevatedButton(
                    onPressed: _nextScenario,
                    style: ElevatedButton.styleFrom(
                      backgroundColor: _primaryColor,
                      foregroundColor: Colors.white,
                      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                    ),
                    child: Text(_currentScenarioIndex < _scenarios.length - 1
                        ? 'Next Scenario'
                        : 'Finish'),
                  ),
                ],
              ),
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildRelationshipButton(
    RelationshipType type,
    String label,
    String description,
  ) {
    final bool isSelected = _selectedRelationship == type;
    
    return Expanded(
      child: Padding(
        padding: const EdgeInsets.symmetric(horizontal: 4.0),
        child: ElevatedButton(
          onPressed: _hasSubmitted ? null : () => _selectRelationship(type),
          style: ElevatedButton.styleFrom(
            backgroundColor: isSelected ? _primaryColor : _backgroundColor,
            foregroundColor: isSelected ? Colors.white : _primaryColor,
            side: BorderSide(color: _primaryColor),
            padding: const EdgeInsets.symmetric(vertical: 12),
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(8),
            ),
          ),
          child: Column(
            children: [
              Text(
                label,
                style: const TextStyle(
                  fontWeight: FontWeight.bold,
                ),
              ),
              const SizedBox(height: 4),
              Text(
                description,
                style: TextStyle(
                  fontSize: 10,
                ),
                textAlign: TextAlign.center,
              ),
            ],
          ),
        ),
      ),
    );
  }

  String _getRelationshipName(RelationshipType type) {
    switch (type) {
      case RelationshipType.causal:
        return 'Causal';
      case RelationshipType.correlation:
        return 'Correlation';
      case RelationshipType.coincidence:
        return 'Coincidence';
      case RelationshipType.unknown:
        return 'Unknown';
    }
  }
}

/// Represents a scenario for exploring correlation vs. causation
class CorrelationScenario {
  final String id;
  final String description;
  final String chartUrl;
  final RelationshipType correctRelationship;
  final String correctFeedback;
  final String incorrectFeedback;
  final String expertExplanation;

  CorrelationScenario({
    required this.id,
    required this.description,
    required this.chartUrl,
    required this.correctRelationship,
    required this.correctFeedback,
    required this.incorrectFeedback,
    required this.expertExplanation,
  });

  factory CorrelationScenario.fromJson(Map<String, dynamic> json) {
    return CorrelationScenario(
      id: json['id'] as String,
      description: json['description'] as String,
      chartUrl: json['chartUrl'] as String? ?? '',
      correctRelationship: _parseRelationshipType(json['correctRelationship'] as String),
      correctFeedback: json['correctFeedback'] as String,
      incorrectFeedback: json['incorrectFeedback'] as String,
      expertExplanation: json['expertExplanation'] as String,
    );
  }

  static RelationshipType _parseRelationshipType(String type) {
    switch (type.toLowerCase()) {
      case 'causal':
        return RelationshipType.causal;
      case 'correlation':
        return RelationshipType.correlation;
      case 'coincidence':
        return RelationshipType.coincidence;
      default:
        return RelationshipType.unknown;
    }
  }
}

/// Enum representing the possible relationship types
enum RelationshipType {
  causal,
  correlation,
  coincidence,
  unknown,
}
