import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import '../../../../models/course_models.dart';

class InteractiveBalanceScaleAnalogyWidget extends StatefulWidget {
  final InteractiveBalanceScaleAnalogyElement balanceScaleElement;
  final VoidCallback onNextAction;
  final bool isLastSlideInLesson;

  const InteractiveBalanceScaleAnalogyWidget({
    Key? key,
    required this.balanceScaleElement,
    required this.onNextAction,
    this.isLastSlideInLesson = false,
  }) : super(key: key);

  @override
  State<InteractiveBalanceScaleAnalogyWidget> createState() =>
      _InteractiveBalanceScaleAnalogyWidgetState();
}

class _InteractiveBalanceScaleAnalogyWidgetState
    extends State<InteractiveBalanceScaleAnalogyWidget>
    with SingleTickerProviderStateMixin {
  double _currentXValue = 0;
  bool _isBalanced = false;
  bool _hasChecked = false;
  late AnimationController _animationController;
  late Animation<double> _balanceAnimation;

  @override
  void initState() {
    super.initState();
    _currentXValue =
        widget.balanceScaleElement.target_x_value_for_balance.toDouble();

    // Setup animation for the balance scale
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 500),
      vsync: this,
    );

    _balanceAnimation = Tween<double>(begin: -0.1, end: 0.1).animate(
      CurvedAnimation(parent: _animationController, curve: Curves.easeInOut),
    );

    _animationController.addStatusListener((status) {
      if (status == AnimationStatus.completed) {
        _animationController.reverse();
      } else if (status == AnimationStatus.dismissed && !_isBalanced) {
        _animationController.forward();
      }
    });
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  void _checkBalance() {
    final targetValue = widget.balanceScaleElement.target_x_value_for_balance;
    final isBalanced = _currentXValue == targetValue;

    setState(() {
      _isBalanced = isBalanced;
      _hasChecked = true;
    });

    // Provide haptic feedback
    HapticFeedback.mediumImpact();

    // Show feedback
    ScaffoldMessenger.of(context).clearSnackBars();
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(
          isBalanced
              ? widget.balanceScaleElement.feedback_on_balance
              : widget.balanceScaleElement.feedback_on_unbalance,
        ),
        backgroundColor: isBalanced ? Colors.green : Colors.orange,
        duration: const Duration(seconds: 3),
      ),
    );

    // Start or stop animation based on balance
    if (isBalanced) {
      _animationController.stop();
    } else {
      _animationController.forward();
    }
  }

  @override
  Widget build(BuildContext context) {
    // Calculate the total weight on each side
    final leftSideVariableWeight =
        widget.balanceScaleElement.left_side_setup.variable_blocks?.fold<int>(
          0,
          (sum, block) => sum + (block.count * _currentXValue.toInt()),
        ) ??
        0;
    final leftSideTotal =
        leftSideVariableWeight +
        widget.balanceScaleElement.left_side_setup.unit_blocks;
    final rightSideTotal =
        widget.balanceScaleElement.right_side_setup.unit_blocks;

    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withOpacity(0.2),
            spreadRadius: 1,
            blurRadius: 3,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Prompt
          Text(
            widget.balanceScaleElement.prompt,
            style: const TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
          ),
          const SizedBox(height: 24),

          // Balance scale visualization
          AnimatedBuilder(
            animation: _balanceAnimation,
            builder: (context, child) {
              final rotationAngle =
                  _isBalanced
                      ? 0.0
                      : leftSideTotal > rightSideTotal
                      ? -0.05
                      : leftSideTotal < rightSideTotal
                      ? 0.05
                      : _balanceAnimation.value;

              return Container(
                height: 200,
                decoration: BoxDecoration(
                  color: Colors.blue.withAlpha(25),
                  borderRadius: BorderRadius.circular(12),
                ),
                child: Stack(
                  alignment: Alignment.center,
                  children: [
                    // Fulcrum
                    Positioned(
                      bottom: 40,
                      child: Container(
                        width: 20,
                        height: 60,
                        decoration: BoxDecoration(
                          color: Colors.grey[700],
                          borderRadius: const BorderRadius.only(
                            topLeft: Radius.circular(4),
                            topRight: Radius.circular(4),
                          ),
                        ),
                      ),
                    ),

                    // Balance beam
                    Positioned(
                      bottom: 100,
                      child: Transform.rotate(
                        angle: rotationAngle,
                        alignment: Alignment.center,
                        child: Container(
                          width: 300,
                          height: 10,
                          decoration: BoxDecoration(
                            color: Colors.brown[700],
                            borderRadius: BorderRadius.circular(4),
                          ),
                        ),
                      ),
                    ),

                    // Left pan
                    Positioned(
                      bottom: 100 + (rotationAngle * 300),
                      left: 50,
                      child: Transform.rotate(
                        angle: rotationAngle,
                        child: Container(
                          width: 80,
                          height: 40,
                          decoration: BoxDecoration(
                            color: Colors.grey[300],
                            borderRadius: const BorderRadius.only(
                              bottomLeft: Radius.circular(12),
                              bottomRight: Radius.circular(12),
                            ),
                            border: Border.all(color: Colors.grey[400]!),
                          ),
                          child: Column(
                            mainAxisAlignment: MainAxisAlignment.center,
                            children: [
                              // Variable blocks
                              if (widget
                                      .balanceScaleElement
                                      .left_side_setup
                                      .variable_blocks !=
                                  null)
                                ...widget
                                    .balanceScaleElement
                                    .left_side_setup
                                    .variable_blocks!
                                    .map(
                                      (block) => Text(
                                        '${block.count} × ${widget.balanceScaleElement.variable_name} = ${block.count * _currentXValue.toInt()}',
                                        style: const TextStyle(
                                          fontSize: 12,
                                          fontWeight: FontWeight.bold,
                                        ),
                                      ),
                                    ),

                              // Unit blocks
                              Text(
                                '${widget.balanceScaleElement.left_side_setup.unit_blocks} units',
                                style: const TextStyle(
                                  fontSize: 12,
                                  fontWeight: FontWeight.bold,
                                ),
                              ),

                              // Total
                              Text(
                                'Total: $leftSideTotal',
                                style: const TextStyle(
                                  fontSize: 14,
                                  fontWeight: FontWeight.bold,
                                  color: Colors.blue,
                                ),
                              ),
                            ],
                          ),
                        ),
                      ),
                    ),

                    // Right pan
                    Positioned(
                      bottom: 100 - (rotationAngle * 300),
                      right: 50,
                      child: Transform.rotate(
                        angle: rotationAngle,
                        child: Container(
                          width: 80,
                          height: 40,
                          decoration: BoxDecoration(
                            color: Colors.grey[300],
                            borderRadius: const BorderRadius.only(
                              bottomLeft: Radius.circular(12),
                              bottomRight: Radius.circular(12),
                            ),
                            border: Border.all(color: Colors.grey[400]!),
                          ),
                          child: Column(
                            mainAxisAlignment: MainAxisAlignment.center,
                            children: [
                              // Variable blocks
                              if (widget
                                      .balanceScaleElement
                                      .right_side_setup
                                      .variable_blocks !=
                                  null)
                                ...widget
                                    .balanceScaleElement
                                    .right_side_setup
                                    .variable_blocks!
                                    .map(
                                      (block) => Text(
                                        '${block.count} × ${widget.balanceScaleElement.variable_name} = ${block.count * _currentXValue.toInt()}',
                                        style: const TextStyle(
                                          fontSize: 12,
                                          fontWeight: FontWeight.bold,
                                        ),
                                      ),
                                    ),

                              // Unit blocks
                              Text(
                                '${widget.balanceScaleElement.right_side_setup.unit_blocks} units',
                                style: const TextStyle(
                                  fontSize: 12,
                                  fontWeight: FontWeight.bold,
                                ),
                              ),

                              // Total
                              Text(
                                'Total: $rightSideTotal',
                                style: const TextStyle(
                                  fontSize: 14,
                                  fontWeight: FontWeight.bold,
                                  color: Colors.blue,
                                ),
                              ),
                            ],
                          ),
                        ),
                      ),
                    ),

                    // Equation representation
                    Positioned(
                      top: 10,
                      child: Container(
                        padding: const EdgeInsets.symmetric(
                          horizontal: 16,
                          vertical: 8,
                        ),
                        decoration: BoxDecoration(
                          color: Colors.white,
                          borderRadius: BorderRadius.circular(8),
                          border: Border.all(color: Colors.blue),
                        ),
                        child: Text(
                          _getEquationRepresentation(),
                          style: const TextStyle(
                            fontSize: 16,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                      ),
                    ),
                  ],
                ),
              );
            },
          ),
          const SizedBox(height: 24),

          // Variable value slider
          Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                'Value of ${widget.balanceScaleElement.variable_name}:',
                style: const TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.bold,
                ),
              ),
              Row(
                children: [
                  Expanded(
                    child: Slider(
                      value: _currentXValue,
                      min: 0,
                      max: 10,
                      divisions: 10,
                      label: _currentXValue.toInt().toString(),
                      onChanged:
                          _hasChecked && _isBalanced
                              ? null
                              : (value) {
                                setState(() {
                                  _currentXValue = value;
                                });
                              },
                    ),
                  ),
                  Container(
                    width: 50,
                    height: 50,
                    decoration: BoxDecoration(
                      color: Colors.blue.withAlpha(25),
                      borderRadius: BorderRadius.circular(8),
                      border: Border.all(color: Colors.blue),
                    ),
                    child: Center(
                      child: Text(
                        _currentXValue.toInt().toString(),
                        style: const TextStyle(
                          fontSize: 20,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ),
                  ),
                ],
              ),
            ],
          ),
          const SizedBox(height: 16),

          // Action buttons
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              // Check balance button
              if (!_hasChecked || !_isBalanced)
                ElevatedButton.icon(
                  onPressed: _checkBalance,
                  icon: const Icon(Icons.balance),
                  label: const Text('Check Balance'),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Colors.blue,
                    foregroundColor: Colors.white,
                  ),
                ),

              // Continue button
              if (_hasChecked && _isBalanced)
                ElevatedButton.icon(
                  onPressed: widget.onNextAction,
                  icon: const Icon(Icons.arrow_forward),
                  label: Text(
                    widget.isLastSlideInLesson ? 'Complete Lesson' : 'Continue',
                  ),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Colors.green,
                    foregroundColor: Colors.white,
                  ),
                ),
            ],
          ),
        ],
      ),
    );
  }

  String _getEquationRepresentation() {
    String leftSide = '';
    String rightSide = '';

    // Build left side of equation
    if (widget.balanceScaleElement.left_side_setup.variable_blocks != null) {
      for (final block
          in widget.balanceScaleElement.left_side_setup.variable_blocks!) {
        if (leftSide.isNotEmpty) leftSide += ' + ';
        leftSide +=
            block.count > 1
                ? '${block.count}${widget.balanceScaleElement.variable_name}'
                : widget.balanceScaleElement.variable_name;
      }
    }

    if (widget.balanceScaleElement.left_side_setup.unit_blocks > 0) {
      if (leftSide.isNotEmpty) leftSide += ' + ';
      leftSide +=
          widget.balanceScaleElement.left_side_setup.unit_blocks.toString();
    }

    // Build right side of equation
    if (widget.balanceScaleElement.right_side_setup.variable_blocks != null) {
      for (final block
          in widget.balanceScaleElement.right_side_setup.variable_blocks!) {
        if (rightSide.isNotEmpty) rightSide += ' + ';
        rightSide +=
            block.count > 1
                ? '${block.count}${widget.balanceScaleElement.variable_name}'
                : widget.balanceScaleElement.variable_name;
      }
    }

    if (widget.balanceScaleElement.right_side_setup.unit_blocks > 0) {
      if (rightSide.isNotEmpty) rightSide += ' + ';
      rightSide +=
          widget.balanceScaleElement.right_side_setup.unit_blocks.toString();
    }

    return '$leftSide = $rightSide';
  }
}
