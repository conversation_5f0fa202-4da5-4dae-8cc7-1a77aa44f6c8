import 'package:flutter/material.dart';
import 'dart:math' as math;

/// A widget that visualizes matrix operations step by step
class InteractiveMatrixOperationsVisualizerWidget extends StatefulWidget {
  /// The data for this widget
  final Map<String, dynamic> data;

  /// Optional callback when the widget state changes
  final Function(bool isCompleted)? onStateChanged;

  const InteractiveMatrixOperationsVisualizerWidget({
    Key? key,
    required this.data,
    this.onStateChanged,
  }) : super(key: key);

  /// Factory constructor to create the widget from JSON data
  factory InteractiveMatrixOperationsVisualizerWidget.fromData(
      Map<String, dynamic> data) {
    return InteractiveMatrixOperationsVisualizerWidget(
      data: data,
    );
  }

  @override
  State<InteractiveMatrixOperationsVisualizerWidget> createState() =>
      _InteractiveMatrixOperationsVisualizerWidgetState();
}

class _InteractiveMatrixOperationsVisualizerWidgetState
    extends State<InteractiveMatrixOperationsVisualizerWidget> {
  // State variables
  late String _title;
  late List<String> _operations;
  late String _selectedOperation;
  late Color _primaryColor;
  late Color _secondaryColor;
  late Color _highlightColor;
  late Color _resultColor;
  late bool _showSteps;
  late bool _isCompleted;
  
  // Matrix state
  late int _matrixARows;
  late int _matrixACols;
  late int _matrixBRows;
  late int _matrixBCols;
  late List<List<double>> _matrixA;
  late List<List<double>> _matrixB;
  late List<List<double>> _resultMatrix;
  late List<String> _steps;
  int _currentStep = 0;
  bool _showResult = false;
  String _errorMessage = '';

  @override
  void initState() {
    super.initState();
    _initializeFromData();
  }

  void _initializeFromData() {
    _title = widget.data['title'] ?? 'Matrix Operations Visualizer';
    _operations = List<String>.from(widget.data['operations'] ?? [
      'Addition',
      'Subtraction',
      'Multiplication',
      'Transpose',
      'Determinant',
      'Inverse'
    ]);
    _selectedOperation = widget.data['defaultOperation'] ?? _operations.first;
    _primaryColor = _colorFromHex(widget.data['primaryColor'] ?? '#2196F3');
    _secondaryColor = _colorFromHex(widget.data['secondaryColor'] ?? '#4CAF50');
    _highlightColor = _colorFromHex(widget.data['highlightColor'] ?? '#FF9800');
    _resultColor = _colorFromHex(widget.data['resultColor'] ?? '#9C27B0');
    _showSteps = widget.data['showSteps'] ?? true;
    _isCompleted = false;

    // Initialize matrices with default values
    _matrixARows = widget.data['matrixARows'] ?? 2;
    _matrixACols = widget.data['matrixACols'] ?? 2;
    _matrixBRows = widget.data['matrixBRows'] ?? 2;
    _matrixBCols = widget.data['matrixBCols'] ?? 2;
    
    // Initialize matrix A
    _matrixA = List.generate(
      _matrixARows,
      (i) => List.generate(
        _matrixACols,
        (j) => widget.data['matrixA'] != null && 
               i < widget.data['matrixA'].length && 
               j < widget.data['matrixA'][i].length
            ? (widget.data['matrixA'][i][j] as num).toDouble()
            : (i == j ? 1.0 : 0.0), // Default to identity matrix pattern
      ),
    );
    
    // Initialize matrix B
    _matrixB = List.generate(
      _matrixBRows,
      (i) => List.generate(
        _matrixBCols,
        (j) => widget.data['matrixB'] != null && 
               i < widget.data['matrixB'].length && 
               j < widget.data['matrixB'][i].length
            ? (widget.data['matrixB'][i][j] as num).toDouble()
            : (i == j ? 1.0 : 0.0), // Default to identity matrix pattern
      ),
    );
    
    // Initialize result matrix
    _resultMatrix = [];
    _steps = [];
    
    // Perform initial operation
    _performOperation();
  }

  Color _colorFromHex(String hexColor) {
    hexColor = hexColor.replaceAll('#', '');
    if (hexColor.length == 6) {
      hexColor = 'FF$hexColor';
    }
    return Color(int.parse(hexColor, radix: 16));
  }

  void _performOperation() {
    setState(() {
      _errorMessage = '';
      _steps = [];
      _currentStep = 0;
      _showResult = false;
      
      try {
        switch (_selectedOperation) {
          case 'Addition':
            _performAddition();
            break;
          case 'Subtraction':
            _performSubtraction();
            break;
          case 'Multiplication':
            _performMultiplication();
            break;
          case 'Transpose':
            _performTranspose();
            break;
          case 'Determinant':
            _performDeterminant();
            break;
          case 'Inverse':
            _performInverse();
            break;
          default:
            _errorMessage = 'Unknown operation: $_selectedOperation';
        }
      } catch (e) {
        _errorMessage = 'Error: $e';
      }
    });
  }

  void _performAddition() {
    // Check if matrices have the same dimensions
    if (_matrixARows != _matrixBRows || _matrixACols != _matrixBCols) {
      _errorMessage = 'Matrices must have the same dimensions for addition';
      return;
    }
    
    _steps.add('To add two matrices, we add corresponding elements.');
    
    _resultMatrix = List.generate(
      _matrixARows,
      (i) => List.generate(
        _matrixACols,
        (j) => 0.0,
      ),
    );
    
    for (int i = 0; i < _matrixARows; i++) {
      for (int j = 0; j < _matrixACols; j++) {
        _resultMatrix[i][j] = _matrixA[i][j] + _matrixB[i][j];
        _steps.add('Add elements at position (${i+1},${j+1}): ${_matrixA[i][j]} + ${_matrixB[i][j]} = ${_resultMatrix[i][j]}');
      }
    }
    
    _showResult = true;
  }

  void _performSubtraction() {
    // Check if matrices have the same dimensions
    if (_matrixARows != _matrixBRows || _matrixACols != _matrixBCols) {
      _errorMessage = 'Matrices must have the same dimensions for subtraction';
      return;
    }
    
    _steps.add('To subtract two matrices, we subtract corresponding elements.');
    
    _resultMatrix = List.generate(
      _matrixARows,
      (i) => List.generate(
        _matrixACols,
        (j) => 0.0,
      ),
    );
    
    for (int i = 0; i < _matrixARows; i++) {
      for (int j = 0; j < _matrixACols; j++) {
        _resultMatrix[i][j] = _matrixA[i][j] - _matrixB[i][j];
        _steps.add('Subtract elements at position (${i+1},${j+1}): ${_matrixA[i][j]} - ${_matrixB[i][j]} = ${_resultMatrix[i][j]}');
      }
    }
    
    _showResult = true;
  }

  void _performMultiplication() {
    // Check if matrices can be multiplied
    if (_matrixACols != _matrixBRows) {
      _errorMessage = 'Number of columns in first matrix must equal number of rows in second matrix';
      return;
    }
    
    _steps.add('To multiply matrices, we calculate the dot product of rows from the first matrix with columns from the second matrix.');
    
    _resultMatrix = List.generate(
      _matrixARows,
      (i) => List.generate(
        _matrixBCols,
        (j) => 0.0,
      ),
    );
    
    for (int i = 0; i < _matrixARows; i++) {
      for (int j = 0; j < _matrixBCols; j++) {
        _steps.add('Calculate element at position (${i+1},${j+1}):');
        
        for (int k = 0; k < _matrixACols; k++) {
          _resultMatrix[i][j] += _matrixA[i][k] * _matrixB[k][j];
          _steps.add('  Add ${_matrixA[i][k]} × ${_matrixB[k][j]} = ${_matrixA[i][k] * _matrixB[k][j]}');
        }
        
        _steps.add('  Result for position (${i+1},${j+1}) = ${_resultMatrix[i][j]}');
      }
    }
    
    _showResult = true;
  }

  void _performTranspose() {
    _steps.add('To transpose a matrix, we swap rows and columns.');
    
    _resultMatrix = List.generate(
      _matrixACols,
      (i) => List.generate(
        _matrixARows,
        (j) => _matrixA[j][i],
      ),
    );
    
    for (int i = 0; i < _matrixACols; i++) {
      for (int j = 0; j < _matrixARows; j++) {
        _steps.add('Element at position (${j+1},${i+1}) becomes element at position (${i+1},${j+1})');
      }
    }
    
    _showResult = true;
  }

  void _performDeterminant() {
    // Check if matrix is square
    if (_matrixARows != _matrixACols) {
      _errorMessage = 'Matrix must be square to calculate determinant';
      return;
    }
    
    _steps.add('To calculate the determinant of a matrix:');
    
    double det = _calculateDeterminant(_matrixA);
    
    _resultMatrix = [
      [det]
    ];
    
    _showResult = true;
  }

  double _calculateDeterminant(List<List<double>> matrix) {
    int n = matrix.length;
    
    if (n == 1) {
      _steps.add('For a 1×1 matrix, the determinant is the single element: ${matrix[0][0]}');
      return matrix[0][0];
    }
    
    if (n == 2) {
      double det = matrix[0][0] * matrix[1][1] - matrix[0][1] * matrix[1][0];
      _steps.add('For a 2×2 matrix, the determinant is (${matrix[0][0]} × ${matrix[1][1]}) - (${matrix[0][1]} × ${matrix[1][0]}) = $det');
      return det;
    }
    
    _steps.add('For a ${n}×${n} matrix, we use the cofactor expansion method.');
    
    double det = 0;
    for (int j = 0; j < n; j++) {
      List<List<double>> subMatrix = _getSubMatrix(matrix, 0, j);
      double subDet = _calculateDeterminant(subMatrix);
      double cofactor = matrix[0][j] * math.pow(-1, j).toDouble() * subDet;
      _steps.add('Cofactor for element (1,${j+1}) = ${matrix[0][j]} × ${math.pow(-1, j).toDouble()} × $subDet = $cofactor');
      det += cofactor;
    }
    
    _steps.add('The determinant is $det');
    return det;
  }

  List<List<double>> _getSubMatrix(List<List<double>> matrix, int excludeRow, int excludeCol) {
    int n = matrix.length;
    List<List<double>> subMatrix = List.generate(
      n - 1,
      (i) => List.generate(
        n - 1,
        (j) => 0.0,
      ),
    );
    
    int rowIndex = 0;
    for (int i = 0; i < n; i++) {
      if (i == excludeRow) continue;
      
      int colIndex = 0;
      for (int j = 0; j < n; j++) {
        if (j == excludeCol) continue;
        subMatrix[rowIndex][colIndex] = matrix[i][j];
        colIndex++;
      }
      
      rowIndex++;
    }
    
    return subMatrix;
  }

  void _performInverse() {
    // Check if matrix is square
    if (_matrixARows != _matrixACols) {
      _errorMessage = 'Matrix must be square to calculate inverse';
      return;
    }
    
    _steps.add('To calculate the inverse of a matrix:');
    
    // Calculate determinant
    double det = _calculateDeterminant(_matrixA);
    
    if (det == 0) {
      _errorMessage = 'Matrix is singular (determinant = 0), inverse does not exist';
      return;
    }
    
    _steps.add('1. Calculate the determinant: $det');
    _steps.add('2. Calculate the adjugate matrix (transpose of cofactor matrix)');
    
    int n = _matrixARows;
    List<List<double>> cofactorMatrix = List.generate(
      n,
      (i) => List.generate(
        n,
        (j) => 0.0,
      ),
    );
    
    for (int i = 0; i < n; i++) {
      for (int j = 0; j < n; j++) {
        List<List<double>> subMatrix = _getSubMatrix(_matrixA, i, j);
        double subDet = _calculateDeterminant(subMatrix);
        cofactorMatrix[i][j] = math.pow(-1, i + j).toDouble() * subDet;
        _steps.add('  Cofactor for position (${i+1},${j+1}) = ${math.pow(-1, i + j).toDouble()} × $subDet = ${cofactorMatrix[i][j]}');
      }
    }
    
    // Transpose cofactor matrix to get adjugate
    List<List<double>> adjugate = List.generate(
      n,
      (i) => List.generate(
        n,
        (j) => cofactorMatrix[j][i],
      ),
    );
    
    _steps.add('3. Divide each element of the adjugate by the determinant');
    
    _resultMatrix = List.generate(
      n,
      (i) => List.generate(
        n,
        (j) => adjugate[i][j] / det,
      ),
    );
    
    _showResult = true;
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withOpacity(0.2),
            spreadRadius: 1,
            blurRadius: 3,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            _title,
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
              color: _primaryColor,
            ),
          ),
          const SizedBox(height: 16),
          
          // Operation selector
          Row(
            children: [
              Text(
                'Operation:',
                style: TextStyle(
                  fontWeight: FontWeight.bold,
                  color: Colors.grey[700],
                ),
              ),
              const SizedBox(width: 8),
              Expanded(
                child: DropdownButton<String>(
                  value: _selectedOperation,
                  isExpanded: true,
                  onChanged: (String? newValue) {
                    if (newValue != null) {
                      setState(() {
                        _selectedOperation = newValue;
                        _performOperation();
                      });
                    }
                  },
                  items: _operations
                      .map<DropdownMenuItem<String>>((String value) {
                    return DropdownMenuItem<String>(
                      value: value,
                      child: Text(value),
                    );
                  }).toList(),
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),
          
          // Matrix inputs
          Row(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Matrix A
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Matrix A',
                      style: TextStyle(
                        fontWeight: FontWeight.bold,
                        color: _primaryColor,
                      ),
                    ),
                    const SizedBox(height: 8),
                    _buildMatrixInput(_matrixA, (i, j, value) {
                      setState(() {
                        _matrixA[i][j] = value;
                        _performOperation();
                      });
                    }),
                  ],
                ),
              ),
              const SizedBox(width: 16),
              
              // Matrix B (only show for operations that need it)
              if (_selectedOperation == 'Addition' ||
                  _selectedOperation == 'Subtraction' ||
                  _selectedOperation == 'Multiplication')
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'Matrix B',
                        style: TextStyle(
                          fontWeight: FontWeight.bold,
                          color: _secondaryColor,
                        ),
                      ),
                      const SizedBox(height: 8),
                      _buildMatrixInput(_matrixB, (i, j, value) {
                        setState(() {
                          _matrixB[i][j] = value;
                          _performOperation();
                        });
                      }),
                    ],
                  ),
                ),
            ],
          ),
          const SizedBox(height: 16),
          
          // Error message
          if (_errorMessage.isNotEmpty)
            Container(
              padding: const EdgeInsets.all(8),
              decoration: BoxDecoration(
                color: Colors.red[50],
                borderRadius: BorderRadius.circular(4),
                border: Border.all(color: Colors.red[300]!),
              ),
              child: Text(
                _errorMessage,
                style: TextStyle(color: Colors.red[700]),
              ),
            ),
          
          // Steps
          if (_showSteps && _steps.isNotEmpty && _errorMessage.isEmpty)
            Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                const SizedBox(height: 16),
                Text(
                  'Steps:',
                  style: TextStyle(
                    fontWeight: FontWeight.bold,
                    color: Colors.grey[700],
                  ),
                ),
                const SizedBox(height: 8),
                Container(
                  padding: const EdgeInsets.all(8),
                  decoration: BoxDecoration(
                    color: Colors.grey[50],
                    borderRadius: BorderRadius.circular(4),
                    border: Border.all(color: Colors.grey[300]!),
                  ),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      for (int i = 0; i <= _currentStep && i < _steps.length; i++)
                        Padding(
                          padding: const EdgeInsets.symmetric(vertical: 2),
                          child: Text(
                            _steps[i],
                            style: TextStyle(
                              color: i == _currentStep ? _highlightColor : Colors.black87,
                              fontWeight: i == _currentStep ? FontWeight.bold : FontWeight.normal,
                            ),
                          ),
                        ),
                    ],
                  ),
                ),
                const SizedBox(height: 8),
                Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    ElevatedButton(
                      onPressed: _currentStep > 0
                          ? () {
                              setState(() {
                                _currentStep--;
                              });
                            }
                          : null,
                      style: ElevatedButton.styleFrom(
                        backgroundColor: _primaryColor,
                      ),
                      child: const Text('Previous'),
                    ),
                    const SizedBox(width: 8),
                    ElevatedButton(
                      onPressed: _currentStep < _steps.length - 1
                          ? () {
                              setState(() {
                                _currentStep++;
                              });
                            }
                          : null,
                      style: ElevatedButton.styleFrom(
                        backgroundColor: _primaryColor,
                      ),
                      child: const Text('Next'),
                    ),
                  ],
                ),
              ],
            ),
          
          // Result
          if (_showResult && _errorMessage.isEmpty)
            Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                const SizedBox(height: 16),
                Text(
                  'Result:',
                  style: TextStyle(
                    fontWeight: FontWeight.bold,
                    color: _resultColor,
                  ),
                ),
                const SizedBox(height: 8),
                _buildMatrixDisplay(_resultMatrix, _resultColor),
              ],
            ),
          
          // Widget name tag
          if (widget.data['showNameTag'] ?? true)
            Padding(
              padding: const EdgeInsets.only(top: 8),
              child: Text(
                'InteractiveMatrixOperationsVisualizer',
                style: TextStyle(
                  color: Colors.grey[600],
                  fontSize: 12,
                  fontStyle: FontStyle.italic,
                ),
              ),
            ),
        ],
      ),
    );
  }

  Widget _buildMatrixInput(List<List<double>> matrix, Function(int, int, double) onChanged) {
    return Container(
      padding: const EdgeInsets.all(8),
      decoration: BoxDecoration(
        color: Colors.grey[50],
        borderRadius: BorderRadius.circular(4),
        border: Border.all(color: Colors.grey[300]!),
      ),
      child: Column(
        children: [
          for (int i = 0; i < matrix.length; i++)
            Row(
              children: [
                for (int j = 0; j < matrix[i].length; j++)
                  Expanded(
                    child: Padding(
                      padding: const EdgeInsets.all(2),
                      child: SizedBox(
                        height: 40,
                        child: TextField(
                          controller: TextEditingController(
                            text: matrix[i][j].toString(),
                          ),
                          keyboardType: const TextInputType.numberWithOptions(
                            decimal: true,
                            signed: true,
                          ),
                          textAlign: TextAlign.center,
                          decoration: InputDecoration(
                            contentPadding: const EdgeInsets.symmetric(
                              horizontal: 8,
                              vertical: 8,
                            ),
                            border: OutlineInputBorder(
                              borderRadius: BorderRadius.circular(4),
                            ),
                          ),
                          onChanged: (value) {
                            double? parsedValue = double.tryParse(value);
                            if (parsedValue != null) {
                              onChanged(i, j, parsedValue);
                            }
                          },
                        ),
                      ),
                    ),
                  ),
              ],
            ),
        ],
      ),
    );
  }

  Widget _buildMatrixDisplay(List<List<double>> matrix, Color color) {
    return Container(
      padding: const EdgeInsets.all(8),
      decoration: BoxDecoration(
        color: Colors.grey[50],
        borderRadius: BorderRadius.circular(4),
        border: Border.all(color: color.withOpacity(0.5)),
      ),
      child: Column(
        children: [
          for (int i = 0; i < matrix.length; i++)
            Row(
              children: [
                for (int j = 0; j < matrix[i].length; j++)
                  Expanded(
                    child: Padding(
                      padding: const EdgeInsets.all(2),
                      child: Container(
                        height: 40,
                        alignment: Alignment.center,
                        decoration: BoxDecoration(
                          color: Colors.white,
                          borderRadius: BorderRadius.circular(4),
                          border: Border.all(color: color.withOpacity(0.3)),
                        ),
                        child: Text(
                          matrix[i][j].toStringAsFixed(2),
                          style: TextStyle(
                            color: color,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                      ),
                    ),
                  ),
              ],
            ),
        ],
      ),
    );
  }
}
