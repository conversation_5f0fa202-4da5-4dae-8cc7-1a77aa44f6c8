{"id": "exploring-types-of-functions-visually", "title": "EXPLORING TYPES OF FUNCTIONS VISUALLY", "description": "Discover key function families through their distinctive graphical representations.", "order": 2, "lessons": [{"id": "linear-functions", "title": "Linear Functions: The Straight Path", "description": "See constant change as straight lines.", "order": 1, "type": "interactive_lesson", "estimatedTimeMinutes": 10, "contentBlocks": [{"id": "lf-screen1-intro", "type": "lesson_screen", "order": 1, "estimatedTimeSeconds": 30, "content": {"headline": "Linear Functions: The Straight Path", "body_md": "Linear functions are the simplest type of function, creating straight lines when graphed. They have a constant rate of change, meaning they increase or decrease at a steady pace.", "visual": {"type": "giphy_search", "value": "linear function graph"}, "interactive_element": {"type": "button", "text": "Let's Explore Linear Functions!", "action": "next_screen"}}}, {"id": "lf-screen2-equation", "type": "lesson_screen", "order": 2, "estimatedTimeSeconds": 60, "content": {"headline": "The Linear Function Equation", "body_md": "A linear function has the form: **f(x) = mx + b**\n\nWhere:\n- **m** is the slope (rate of change)\n- **b** is the y-intercept (where the line crosses the y-axis)", "visual": {"type": "unsplash_search", "value": "linear graph mathematics"}, "interactive_element": {"type": "multiple_choice_text", "question": "In the function f(x) = 3x + 4, what is the slope?", "options": [{"id": "a", "text": "3", "is_correct": true, "feedback_correct": "Correct! The slope (m) is 3, which means the y-value increases by 3 for every 1-unit increase in x."}, {"id": "b", "text": "4", "is_correct": false, "feedback_incorrect": "Not quite. The number 4 is the y-intercept (b), not the slope."}, {"id": "c", "text": "7", "is_correct": false, "feedback_incorrect": "Incorrect. The slope is the coefficient of x, which is 3."}, {"id": "d", "text": "0", "is_correct": false, "feedback_incorrect": "Incorrect. A slope of 0 would mean a horizontal line, but this function has a slope of 3."}]}}}, {"id": "lf-screen3-slope", "type": "lesson_screen", "order": 3, "estimatedTimeSeconds": 60, "content": {"headline": "Understanding Slope", "body_md": "The slope tells us how steep the line is:\n\n- **Positive slope**: Line goes up from left to right (increasing)\n- **Negative slope**: Line goes down from left to right (decreasing)\n- **Zero slope**: Horizontal line (constant)\n- **Undefined slope**: Vertical line (not a function)", "visual": {"type": "giphy_search", "value": "slope of line"}, "interactive_element": {"type": "multiple_choice_text", "question": "Which of these functions has a negative slope?", "options": [{"id": "a", "text": "f(x) = 2x + 5", "is_correct": false, "feedback_incorrect": "Incorrect. The slope is 2, which is positive."}, {"id": "b", "text": "f(x) = -3x + 1", "is_correct": true, "feedback_correct": "Correct! The slope is -3, which is negative. This line decreases as x increases."}, {"id": "c", "text": "f(x) = 5", "is_correct": false, "feedback_incorrect": "Incorrect. This is a horizontal line with a slope of 0 (neither positive nor negative)."}, {"id": "d", "text": "f(x) = x", "is_correct": false, "feedback_incorrect": "Incorrect. The slope is 1, which is positive."}]}}}, {"id": "lf-screen4-real-world", "type": "lesson_screen", "order": 4, "estimatedTimeSeconds": 60, "content": {"headline": "Linear Functions in the Real World", "body_md": "Linear functions appear everywhere in daily life:\n\n- Distance traveled at constant speed: d = rt\n- Simple interest: I = prt\n- Temperature conversion: F = 1.8C + 32\n- Cost calculations: Cost = fixed cost + (unit cost × quantity)", "visual": {"type": "unsplash_search", "value": "graph data linear"}, "interactive_element": {"type": "text_input_quick", "question": "A car rental costs $30 plus $0.25 per mile. Write this as a linear function C(m) where m is miles driven.", "correct_answer_regex": "^[Cc]\\s*\\(\\s*[mM]\\s*\\)\\s*=\\s*0*\\.25\\s*[mM]\\s*\\+\\s*30$|^[Cc]\\s*\\(\\s*[mM]\\s*\\)\\s*=\\s*30\\s*\\+\\s*0*\\.25\\s*[mM]$", "placeholder": "Enter the function", "feedback_correct": "Excellent! C(m) = 0.25m + 30 correctly represents this situation.", "feedback_incorrect": "Not quite. Try using the form C(m) = 0.25m + 30, where 0.25 is the cost per mile and 30 is the fixed cost."}}}, {"id": "lf-screen5-explorer", "type": "lesson_screen", "order": 5, "estimatedTimeSeconds": 120, "content": {"headline": "Explore Linear Functions Interactively", "body_md": "Now let's explore linear functions interactively! Use the sliders to change the slope (m) and y-intercept (b) and see how the graph changes. Try the real-world examples to see linear functions in action.", "interactive_element": {"type": "interactive_widget", "widget_type": "interactive_linear_function_explorer", "data": {"title": "Linear Function Explorer", "description": "Explore how changing the slope and y-intercept affects the graph of a linear function.", "primaryColor": "4287f5", "secondaryColor": "42c5f5", "accentColor": "f54242", "backgroundColor": "ffffff", "textColor": "000000", "examples": [{"title": "Distance vs. Time (Constant Speed)", "description": "Distance traveled at a constant speed of 60 mph with no initial distance.", "slope": 60, "y_intercept": 0}, {"title": "Temperature Conversion", "description": "Converting from Celsius to Fahrenheit: F = 1.8C + 32", "slope": 1.8, "y_intercept": 32}, {"title": "Car Rental Cost", "description": "Cost = $30 base fee + $0.25 per mile", "slope": 0.25, "y_intercept": 30}]}}}}, {"id": "lf-screen6-conclusion", "type": "lesson_screen", "order": 6, "estimatedTimeSeconds": 30, "content": {"headline": "Linear Functions: Simple but Powerful", "body_md": "Great job! You now understand linear functions and their graphs. These simple but powerful functions form the foundation for understanding more complex relationships.", "visual": {"type": "unsplash_search", "value": "mathematics linear"}, "interactive_element": {"type": "button", "text": "Continue to Next Lesson", "action": "next_lesson"}}}]}, {"id": "quadratic-functions", "title": "Quadratic Functions: The Curved Path", "description": "Visualize the symmetrical parabola.", "order": 2, "type": "interactive_lesson", "estimatedTimeMinutes": 10, "contentBlocks": [{"id": "qf-screen1-intro", "type": "lesson_screen", "order": 1, "estimatedTimeSeconds": 30, "content": {"headline": "Quadratic Functions: The U-Shaped Curve", "body_md": "Quadratic functions create U-shaped curves called parabolas. They're perfect for modeling situations where values increase, reach a maximum (or minimum), and then decrease.", "visual": {"type": "giphy_search", "value": "parabola function"}, "interactive_element": {"type": "button", "text": "Let's Explore Parabolas!", "action": "next_screen"}}}, {"id": "qf-screen2-equation", "type": "lesson_screen", "order": 2, "estimatedTimeSeconds": 60, "content": {"headline": "The Quadratic Function Equation", "body_md": "A quadratic function has the form: **f(x) = ax² + bx + c**\n\nWhere:\n- **a** determines whether the parabola opens up (a > 0) or down (a < 0)\n- **b** affects the axis of symmetry\n- **c** is the y-intercept (where the parabola crosses the y-axis)", "visual": {"type": "unsplash_search", "value": "parabola graph"}, "interactive_element": {"type": "multiple_choice_text", "question": "For the function f(x) = -2x² + 4x + 1, which way does the parabola open?", "options": [{"id": "a", "text": "Upward", "is_correct": false, "feedback_incorrect": "Incorrect. Look at the coefficient of x²."}, {"id": "b", "text": "Downward", "is_correct": true, "feedback_correct": "Correct! Since a = -2 is negative, the parabola opens downward."}, {"id": "c", "text": "To the right", "is_correct": false, "feedback_incorrect": "Incorrect. Parabolas open either upward or downward, not sideways."}, {"id": "d", "text": "To the left", "is_correct": false, "feedback_incorrect": "Incorrect. Parabolas open either upward or downward, not sideways."}]}}}, {"id": "qf-screen3-vertex", "type": "lesson_screen", "order": 3, "estimatedTimeSeconds": 60, "content": {"headline": "The Vertex: The Turning Point", "body_md": "The vertex is the highest or lowest point of a parabola:\n\n- For a > 0: The vertex is the minimum point\n- For a < 0: The vertex is the maximum point\n\nThe x-coordinate of the vertex is: x = -b/(2a)", "visual": {"type": "giphy_search", "value": "parabola vertex"}, "interactive_element": {"type": "text_input_quick", "question": "Find the x-coordinate of the vertex for f(x) = 2x² - 8x + 7", "correct_answer_regex": "^2$", "placeholder": "Enter the x-coordinate", "feedback_correct": "Correct! x = -b/(2a) = -(-8)/(2(2)) = 8/4 = 2", "feedback_incorrect": "Not quite. Use the formula x = -b/(2a) where a = 2 and b = -8."}}}, {"id": "qf-screen4-real-world", "type": "lesson_screen", "order": 4, "estimatedTimeSeconds": 60, "content": {"headline": "Quadratic Functions in the Real World", "body_md": "Quadratic functions model many real-world scenarios:\n\n- Projectile motion (height of a thrown ball)\n- Area as a function of length\n- Revenue as a function of price\n- Stopping distance of a vehicle", "visual": {"type": "unsplash_search", "value": "projectile motion parabola"}, "interactive_element": {"type": "multiple_choice_text", "question": "A ball is thrown upward. Which of these best describes its height over time?", "options": [{"id": "a", "text": "A linear function", "is_correct": false, "feedback_incorrect": "Incorrect. The height doesn't change at a constant rate."}, {"id": "b", "text": "A quadratic function with a > 0", "is_correct": false, "feedback_incorrect": "Incorrect. The ball reaches a maximum height and then falls."}, {"id": "c", "text": "A quadratic function with a < 0", "is_correct": true, "feedback_correct": "Correct! The height follows a parabola that opens downward due to gravity."}, {"id": "d", "text": "An exponential function", "is_correct": false, "feedback_incorrect": "Incorrect. The height doesn't grow or decay exponentially."}]}}}, {"id": "qf-screen5-explorer", "type": "lesson_screen", "order": 5, "estimatedTimeSeconds": 120, "content": {"headline": "Explore Quadratic Functions Interactively", "body_md": "Now let's explore quadratic functions interactively! Use the sliders to change the coefficients a, b, and c in the equation f(x) = ax² + bx + c and see how the graph changes. Try the real-world examples to see quadratic functions in action.", "interactive_element": {"type": "interactive_widget", "widget_type": "interactive_quadratic_function_explorer", "data": {"title": "Quadratic Function Explorer", "description": "Explore how changing the coefficients affects the graph of a quadratic function.", "primaryColor": "4287f5", "secondaryColor": "42c5f5", "accentColor": "f54242", "backgroundColor": "ffffff", "textColor": "000000", "examples": [{"title": "Projectile Motion", "description": "Height of a ball thrown upward with initial velocity of 20 m/s from ground level.", "a": -4.9, "b": 20, "c": 0}, {"title": "Profit Function", "description": "Profit based on number of items produced: P(x) = -0.5x² + 100x - 1000", "a": -0.5, "b": 100, "c": -1000}, {"title": "Parabolic Mirror", "description": "Shape of a parabolic mirror or satellite dish", "a": 0.25, "b": 0, "c": 0}]}}}}, {"id": "qf-screen6-conclusion", "type": "lesson_screen", "order": 6, "estimatedTimeSeconds": 30, "content": {"headline": "Quadratic Functions: Modeling Curves", "body_md": "Excellent! You now understand quadratic functions and their parabolic graphs. These functions are essential for modeling situations with maximum or minimum values.", "visual": {"type": "unsplash_search", "value": "parabola mathematics"}, "interactive_element": {"type": "button", "text": "Continue to Next Lesson", "action": "next_lesson"}}}]}, {"id": "exponential-functions", "title": "Exponential Functions: The Growth Spurt", "description": "Observe rapid increase or decrease.", "order": 3, "type": "interactive_lesson", "estimatedTimeMinutes": 10, "contentBlocks": [{"id": "ef-screen1-intro", "type": "lesson_screen", "order": 1, "estimatedTimeSeconds": 30, "content": {"headline": "Exponential Functions: Explosive Growth", "body_md": "Exponential functions show growth or decay at a rate proportional to the current value. This creates curves that grow increasingly steep or approach zero without ever reaching it.", "visual": {"type": "giphy_search", "value": "exponential growth"}, "interactive_element": {"type": "button", "text": "Let's Explore Exponential Growth!", "action": "next_screen"}}}, {"id": "ef-screen2-equation", "type": "lesson_screen", "order": 2, "estimatedTimeSeconds": 60, "content": {"headline": "The Exponential Function Equation", "body_md": "An exponential function has the form: **f(x) = a × b^x**\n\nWhere:\n- **a** is the initial value (when x = 0)\n- **b** is the base (growth factor)\n  - If b > 1: exponential growth\n  - If 0 < b < 1: exponential decay", "visual": {"type": "unsplash_search", "value": "exponential graph"}, "interactive_element": {"type": "multiple_choice_text", "question": "For the function f(x) = 3 × 2^x, what is the initial value?", "options": [{"id": "a", "text": "2", "is_correct": false, "feedback_incorrect": "Incorrect. The base is 2, not the initial value."}, {"id": "b", "text": "3", "is_correct": true, "feedback_correct": "Correct! When x = 0, f(0) = 3 × 2^0 = 3 × 1 = 3."}, {"id": "c", "text": "6", "is_correct": false, "feedback_incorrect": "Incorrect. Calculate f(0) to find the initial value."}, {"id": "d", "text": "0", "is_correct": false, "feedback_incorrect": "Incorrect. The function never equals zero."}]}}}, {"id": "ef-screen3-growth-decay", "type": "lesson_screen", "order": 3, "estimatedTimeSeconds": 60, "content": {"headline": "Growth vs. Decay", "body_md": "Exponential functions come in two flavors:\n\n- **Growth (b > 1)**: Values increase rapidly (e.g., compound interest, population growth)\n- **Decay (0 < b < 1)**: Values decrease, approaching but never reaching zero (e.g., radioactive decay, depreciation)", "visual": {"type": "giphy_search", "value": "exponential growth vs decay"}, "interactive_element": {"type": "multiple_choice_text", "question": "Which function represents exponential decay?", "options": [{"id": "a", "text": "f(x) = 5 × 2^x", "is_correct": false, "feedback_incorrect": "Incorrect. Since the base (2) is greater than 1, this represents growth."}, {"id": "b", "text": "f(x) = 10 × (0.5)^x", "is_correct": true, "feedback_correct": "Correct! Since the base (0.5) is between 0 and 1, this represents decay."}, {"id": "c", "text": "f(x) = 3 × 1.05^x", "is_correct": false, "feedback_incorrect": "Incorrect. Since the base (1.05) is greater than 1, this represents growth."}, {"id": "d", "text": "f(x) = 8 × e^x", "is_correct": false, "feedback_incorrect": "Incorrect. Since e (approximately 2.718) is greater than 1, this represents growth."}]}}}, {"id": "ef-screen4-real-world", "type": "lesson_screen", "order": 4, "estimatedTimeSeconds": 60, "content": {"headline": "Exponential Functions in the Real World", "body_md": "Exponential functions model many important phenomena:\n\n- Compound interest: A = P(1 + r)^t\n- Population growth: P(t) = P₀e^rt\n- Radioactive decay: N(t) = N₀e^(-λt)\n- Viral spread: I(t) = I₀e^kt", "visual": {"type": "unsplash_search", "value": "compound interest growth"}, "interactive_element": {"type": "text_input_quick", "question": "If $1000 is invested at 8% annual compound interest, what will be the balance after 2 years? Use A = P(1 + r)^t", "correct_answer_regex": "^1166\\.4$|^1166\\.40$|^\\$1166\\.4$|^\\$1166\\.40$", "placeholder": "Enter the amount", "feedback_correct": "Correct! A = 1000(1 + 0.08)^2 = 1000(1.08)^2 = 1000(1.1664) = $1166.40", "feedback_incorrect": "Not quite. Calculate A = 1000(1 + 0.08)^2 = 1000(1.08)^2"}}}, {"id": "ef-screen5-explorer", "type": "lesson_screen", "order": 5, "estimatedTimeSeconds": 120, "content": {"headline": "Explore Exponential Functions Interactively", "body_md": "Now let's explore exponential functions interactively! Use the sliders to change the initial value (a) and base (b) in the equation f(x) = a × b^x and see how the graph changes. Try the real-world examples to see exponential functions in action.", "interactive_element": {"type": "interactive_widget", "widget_type": "interactive_exponential_function_explorer", "data": {"title": "Exponential Function Explorer", "description": "Explore how changing the initial value and base affects the graph of an exponential function.", "primaryColor": "4287f5", "secondaryColor": "42c5f5", "accentColor": "f54242", "backgroundColor": "ffffff", "textColor": "000000", "examples": [{"title": "Compound Interest", "description": "$1000 invested at 8% annual compound interest", "initialValue": 1000, "base": 1.08}, {"title": "Population Growth", "description": "Population starting at 10,000 with 3% annual growth rate", "initialValue": 10000, "base": 1.03}, {"title": "Radioactive Decay", "description": "Decay of 100g of a radioactive substance with half-life factor", "initialValue": 100, "base": 0.5}]}}}}, {"id": "ef-screen6-conclusion", "type": "lesson_screen", "order": 6, "estimatedTimeSeconds": 30, "content": {"headline": "Exponential Functions: Modeling Rapid Change", "body_md": "Excellent! You now understand exponential functions and their distinctive graphs. These powerful functions are essential for modeling situations with rapid growth or decay.", "visual": {"type": "unsplash_search", "value": "exponential mathematics"}, "interactive_element": {"type": "button", "text": "Continue to Next Lesson", "action": "next_lesson"}}}]}, {"id": "probability-basics", "title": "Probability Basics: Quantifying Chance", "description": "Introduce the concept of likelihood.", "order": 4, "type": "interactive_lesson", "estimatedTimeMinutes": 10, "contentBlocks": [{"id": "pb-screen1-intro", "type": "lesson_screen", "order": 1, "estimatedTimeSeconds": 30, "content": {"headline": "Probability: Measuring Uncertainty", "body_md": "Probability measures how likely an event is to occur. It's expressed as a number between 0 (impossible) and 1 (certain), or as a percentage from 0% to 100%.", "visual": {"type": "giphy_search", "value": "probability dice"}, "interactive_element": {"type": "button", "text": "Let's Explore Probability!", "action": "next_screen"}}}, {"id": "pb-screen2-basic-formula", "type": "lesson_screen", "order": 2, "estimatedTimeSeconds": 60, "content": {"headline": "The Basic Probability Formula", "body_md": "The probability of an event is:\n\n**P(event) = Number of favorable outcomes / Total number of possible outcomes**\n\nFor example, the probability of rolling a 3 on a fair six-sided die is 1/6.", "visual": {"type": "unsplash_search", "value": "dice probability"}, "interactive_element": {"type": "multiple_choice_text", "question": "What is the probability of drawing a king from a standard deck of 52 cards?", "options": [{"id": "a", "text": "1/13", "is_correct": false, "feedback_incorrect": "Incorrect. There are 4 kings in a deck of 52 cards."}, {"id": "b", "text": "1/4", "is_correct": false, "feedback_incorrect": "Incorrect. There are 4 kings out of 52 cards, not 16."}, {"id": "c", "text": "4/52 or 1/13", "is_correct": true, "feedback_correct": "Correct! There are 4 kings in a deck of 52 cards, so P(king) = 4/52 = 1/13."}, {"id": "d", "text": "4/13", "is_correct": false, "feedback_incorrect": "Incorrect. There are 4 kings out of 52 cards, not 13."}]}}}, {"id": "pb-screen3-probability-scale", "type": "lesson_screen", "order": 3, "estimatedTimeSeconds": 60, "content": {"headline": "The Probability Scale", "body_md": "Probability exists on a scale from 0 to 1:\n\n- **0 (0%)**: Impossible event\n- **0.5 (50%)**: Equally likely to happen or not happen\n- **1 (100%)**: Certain event\n\nFor example, the probability of rolling an even number on a fair die is 3/6 = 0.5 or 50%.", "visual": {"type": "giphy_search", "value": "probability scale"}, "interactive_element": {"type": "multiple_choice_text", "question": "Which of these has a probability closest to 0.75?", "options": [{"id": "a", "text": "Rolling a number less than 5 on a fair six-sided die", "is_correct": true, "feedback_correct": "Correct! P(less than 5) = 4/6 = 2/3 ≈ 0.67, which is closest to 0.75."}, {"id": "b", "text": "Getting heads when flipping a fair coin", "is_correct": false, "feedback_incorrect": "Incorrect. P(heads) = 1/2 = 0.5, which is not close to 0.75."}, {"id": "c", "text": "Drawing a red card from a standard deck", "is_correct": false, "feedback_incorrect": "Incorrect. P(red card) = 26/52 = 0.5, which is not close to 0.75."}, {"id": "d", "text": "Rolling a 6 on a fair six-sided die", "is_correct": false, "feedback_incorrect": "Incorrect. P(rolling 6) = 1/6 ≈ 0.17, which is not close to 0.75."}]}}}, {"id": "pb-screen4-real-world", "type": "lesson_screen", "order": 4, "estimatedTimeSeconds": 60, "content": {"headline": "Probability in the Real World", "body_md": "Probability is used in countless real-world applications:\n\n- Weather forecasting (\"40% chance of rain\")\n- Insurance risk assessment\n- Medical diagnosis and treatment decisions\n- Quality control in manufacturing\n- Games of chance and gambling", "visual": {"type": "unsplash_search", "value": "weather forecast probability"}, "interactive_element": {"type": "text_input_quick", "question": "If a weather forecast says there's a 30% chance of rain, what is this probability expressed as a decimal?", "correct_answer_regex": "^0\\.3$|^0\\.30$", "placeholder": "Enter the decimal", "feedback_correct": "Correct! 30% = 0.3 as a decimal.", "feedback_incorrect": "Not quite. To convert from percentage to decimal, divide by 100."}}}, {"id": "pb-screen5-calculator", "type": "lesson_screen", "order": 5, "estimatedTimeSeconds": 120, "content": {"headline": "Calculate Probabilities Interactively", "body_md": "Now let's calculate probabilities interactively! Use this calculator to explore different probability scenarios. You can calculate probabilities for coin flips, dice rolls, card draws, and more.", "interactive_element": {"type": "interactive_widget", "widget_type": "interactive_probability_calculator", "data": {"title": "Probability Calculator", "description": "Calculate probabilities for different scenarios.", "primaryColor": "4287f5", "secondaryColor": "42c5f5", "accentColor": "f54242", "backgroundColor": "ffffff", "textColor": "000000", "scenarios": [{"title": "Coin Flips", "description": "Calculate probabilities for flipping coins", "type": "coin_flip", "numCoins": 3, "targetEvent": "exactly 2 heads"}, {"title": "<PERSON><PERSON>", "description": "Calculate probabilities for rolling dice", "type": "dice_roll", "numDice": 2, "targetEvent": "sum equals 7"}, {"title": "Card Draws", "description": "Calculate probabilities for drawing cards", "type": "card_draw", "numCards": 5, "targetEvent": "at least one ace"}]}}}}, {"id": "pb-screen6-conclusion", "type": "lesson_screen", "order": 6, "estimatedTimeSeconds": 30, "content": {"headline": "Probability: The Mathematics of Chance", "body_md": "Great job! You now understand the basic concept of probability as a measure of likelihood. This fundamental idea will help us explore more complex probability concepts and their connection to functions.", "visual": {"type": "unsplash_search", "value": "probability mathematics"}, "interactive_element": {"type": "button", "text": "Continue to Next Lesson", "action": "next_lesson"}}}]}, {"id": "sample-space-events", "title": "Sample Space and Events Visualized", "description": "See all possible outcomes and specific subsets.", "order": 5, "type": "interactive_lesson", "estimatedTimeMinutes": 10, "contentBlocks": [{"id": "sse-screen1-intro", "type": "lesson_screen", "order": 1, "estimatedTimeSeconds": 30, "content": {"headline": "The Universe of Possibilities", "body_md": "In probability, the **sample space** is the set of all possible outcomes of an experiment. An **event** is a subset of outcomes that we're interested in.", "visual": {"type": "giphy_search", "value": "venn diagram probability"}, "interactive_element": {"type": "button", "text": "Let's Explore Sample Spaces!", "action": "next_screen"}}}, {"id": "sse-screen2-sample-space", "type": "lesson_screen", "order": 2, "estimatedTimeSeconds": 60, "content": {"headline": "Visualizing the Sample Space", "body_md": "The sample space can be represented in different ways:\n\n- **List**: S = {1, 2, 3, 4, 5, 6} for rolling a die\n- **Table**: All possible outcomes of flipping two coins\n- **Tree diagram**: Branching possibilities for sequential events\n- **<PERSON>enn diagram**: Visual representation of sets and their relationships", "visual": {"type": "unsplash_search", "value": "probability tree diagram"}, "interactive_element": {"type": "multiple_choice_text", "question": "What is the sample space when flipping two coins?", "options": [{"id": "a", "text": "{H, T}", "is_correct": false, "feedback_incorrect": "Incorrect. This is the sample space for flipping one coin, not two."}, {"id": "b", "text": "{HH, HT, TH, TT}", "is_correct": true, "feedback_correct": "Correct! These are all possible outcomes when flipping two coins."}, {"id": "c", "text": "{0 heads, 1 head, 2 heads}", "is_correct": false, "feedback_incorrect": "Incorrect. This counts the number of heads but doesn't distinguish between different arrangements."}, {"id": "d", "text": "{HH, TT}", "is_correct": false, "feedback_incorrect": "Incorrect. This only includes matching outcomes, not all possibilities."}]}}}, {"id": "sse-screen3-events", "type": "lesson_screen", "order": 3, "estimatedTimeSeconds": 60, "content": {"headline": "Events as Subsets", "body_md": "An event is a subset of the sample space that we're interested in:\n\n- **Simple event**: A single outcome (e.g., rolling a 3)\n- **Compound event**: Multiple outcomes (e.g., rolling an even number)\n- **Complement**: All outcomes NOT in an event (e.g., not rolling a 6)\n- **Empty event**: No outcomes (impossible event, probability = 0)\n- **Certain event**: All outcomes (the entire sample space, probability = 1)", "visual": {"type": "giphy_search", "value": "subset probability"}, "interactive_element": {"type": "multiple_choice_text", "question": "For a standard deck of 52 cards, which is a compound event?", "options": [{"id": "a", "text": "Drawing the ace of spades", "is_correct": false, "feedback_incorrect": "Incorrect. This is a simple event (single outcome)."}, {"id": "b", "text": "Drawing a red card", "is_correct": true, "feedback_correct": "Correct! This is a compound event that includes multiple outcomes (26 different red cards)."}, {"id": "c", "text": "Not drawing any card", "is_correct": false, "feedback_incorrect": "Incorrect. This is not an event in the sample space of drawing a card."}, {"id": "d", "text": "Drawing a purple card", "is_correct": false, "feedback_incorrect": "Incorrect. This is an empty event (impossible) since there are no purple cards in a standard deck."}]}}}, {"id": "sse-screen4-venn-diagrams", "type": "lesson_screen", "order": 4, "estimatedTimeSeconds": 60, "content": {"headline": "Visualizing Events with <PERSON><PERSON>n <PERSON>ag<PERSON>", "body_md": "Venn diagrams are powerful tools for visualizing events and their relationships:\n\n- The rectangle represents the sample space\n- Circles represent events\n- Overlapping regions show outcomes in multiple events\n- Regions can be labeled with probabilities", "visual": {"type": "unsplash_search", "value": "venn diagram probability"}, "interactive_element": {"type": "multiple_choice_text", "question": "In a <PERSON><PERSON><PERSON> diagram for a die roll, if one circle represents 'even numbers' and another represents 'numbers greater than 3', which outcomes would be in the overlap?", "options": [{"id": "a", "text": "{2, 4, 6}", "is_correct": false, "feedback_incorrect": "Incorrect. Not all of these numbers are greater than 3."}, {"id": "b", "text": "{4, 5, 6}", "is_correct": false, "feedback_incorrect": "Incorrect. Not all of these numbers are even."}, {"id": "c", "text": "{4, 6}", "is_correct": true, "feedback_correct": "Correct! 4 and 6 are both even AND greater than 3."}, {"id": "d", "text": "{1, 2, 3}", "is_correct": false, "feedback_incorrect": "Incorrect. None of these numbers are both even and greater than 3."}]}}}, {"id": "sse-screen5-visualizer", "type": "lesson_screen", "order": 5, "estimatedTimeSeconds": 120, "content": {"headline": "Visualize Sample Spaces Interactively", "body_md": "Now let's visualize sample spaces interactively! Use this tool to explore the sample spaces for different probability experiments. You can see all possible outcomes and identify specific events within the sample space.", "interactive_element": {"type": "interactive_widget", "widget_type": "interactive_sample_space_visualizer", "data": {"title": "Sample Space Visualizer", "description": "Explore sample spaces for different probability experiments.", "primaryColor": "4287f5", "secondaryColor": "42c5f5", "accentColor": "f54242", "backgroundColor": "ffffff", "textColor": "000000", "experiments": [{"title": "Coin Flips", "description": "Sample space for flipping multiple coins", "type": "coin_flip", "numCoins": 3}, {"title": "<PERSON><PERSON>", "description": "Sample space for rolling dice", "type": "dice_roll", "numDice": 2}, {"title": "Card Draws", "description": "Sample space for drawing cards from a deck", "type": "card_draw", "numCards": 2}, {"title": "Spinner", "description": "Sample space for spinning a spinner with different sections", "type": "spinner", "sections": 8}, {"title": "Urn Model", "description": "Sample space for drawing colored balls from an urn", "type": "urn", "balls": {"red": 3, "blue": 2, "green": 1}, "draws": 2}]}}}}, {"id": "sse-screen6-conclusion", "type": "lesson_screen", "order": 6, "estimatedTimeSeconds": 30, "content": {"headline": "Sample Spaces and Events: The Foundation of Probability", "body_md": "Excellent! You now understand sample spaces and events, which form the foundation of probability theory. These concepts will help us explore more complex probability rules and their connection to functions.", "visual": {"type": "unsplash_search", "value": "probability mathematics"}, "interactive_element": {"type": "button", "text": "Continue to Module Test", "action": "next_lesson"}}}]}], "endOfModuleAssessment": {"id": "exploring-types-of-functions-visually-test", "title": "Function and Probability Introduction", "description": "Test your understanding of different function types and basic probability concepts.", "type": "module_test_interactive", "estimatedTimeMinutes": 15, "passingScorePercentage": 70, "contentBlocks": [{"id": "etfv-test-intro", "type": "test_screen_intro", "order": 1, "estimatedTimeSeconds": 30, "content": {"headline": "Function Types and Probability: Test Your Knowledge", "body_md": "Let's see how well you understand different types of functions and basic probability concepts!", "visual": {"type": "unsplash_search", "value": "mathematics test"}, "interactive_element": {"type": "button", "text": "Begin Test", "action": "next_screen"}}}, {"id": "etfv-test-comprehensive", "type": "test_screen_interactive", "order": 2, "estimatedTimeSeconds": 600, "content": {"headline": "Comprehensive Function and Probability Test", "body_md": "This comprehensive test will assess your understanding of linear, quadratic, and exponential functions, as well as basic probability concepts and sample spaces.", "interactive_element": {"type": "interactive_widget", "widget_type": "interactive_function_probability_test", "data": {"title": "Function and Probability Test", "description": "Test your understanding of functions and probability concepts.", "primaryColor": "4287f5", "secondaryColor": "42c5f5", "accentColor": "f54242", "backgroundColor": "ffffff", "textColor": "000000", "questions": [{"type": "multiple_choice", "question": "Which of the following is NOT a characteristic of a linear function?", "options": ["The graph is a straight line", "The rate of change is constant", "The graph is a parabola", "It can be written in the form f(x) = mx + b"], "correctAnswer": 2, "explanation": "A linear function always graphs as a straight line, not a parabola. Parabolas are the graphs of quadratic functions."}, {"type": "multiple_choice", "question": "What is the vertex of the quadratic function f(x) = 2(x - 3)² + 4?", "options": ["(3, 4)", "(3, 0)", "(0, 4)", "(4, 3)"], "correctAnswer": 0, "explanation": "When a quadratic function is in the form f(x) = a(x - h)² + k, the vertex is at (h, k). For f(x) = 2(x - 3)² + 4, the vertex is at (3, 4)."}, {"type": "multiple_choice", "question": "If f(x) = 2^x, what happens to the function value as x increases?", "options": ["It increases at an increasing rate", "It increases at a constant rate", "It increases at a decreasing rate", "It decreases at a decreasing rate"], "correctAnswer": 0, "explanation": "Exponential functions of the form f(x) = b^x where b > 1 (like 2^x) increase at an increasing rate as x increases. This means the growth becomes faster and faster."}, {"type": "multiple_choice", "question": "What is the probability of rolling a sum of 7 with two fair dice?", "options": ["1/6", "1/12", "1/36", "1/3"], "correctAnswer": 0, "explanation": "When rolling two dice, there are 36 possible outcomes (6 × 6). The sum of 7 can be achieved in 6 ways: (1,6), (2,5), (3,4), (4,3), (5,2), (6,1). Therefore, the probability is 6/36 = 1/6."}, {"type": "multiple_choice", "question": "In a standard deck of 52 cards, what is the probability of drawing a face card (<PERSON>, Queen, or King)?", "options": ["3/13", "1/4", "1/13", "3/26"], "correctAnswer": 1, "explanation": "There are 12 face cards in a standard deck (3 face cards in each of the 4 suits). Therefore, the probability is 12/52 = 3/13 = 1/4."}, {"type": "multiple_choice", "question": "Which of the following is NOT a characteristic of a linear function?", "options": ["The graph is a parabola", "The rate of change is constant", "The graph is a straight line", "It can be written in the form f(x) = mx + b"], "correctAnswer": 0, "explanation": "A parabola is the graph of a quadratic function, not a linear function. Linear functions have a constant rate of change, their graphs are straight lines, and they can be written in the form f(x) = mx + b."}, {"type": "multiple_choice", "question": "If you flip a fair coin 3 times, what is the probability of getting exactly 2 heads?", "options": ["3/8", "1/2", "1/4", "1/8"], "correctAnswer": 0, "explanation": "When flipping a coin 3 times, there are 2^3 = 8 possible outcomes. There are 3 ways to get exactly 2 heads: HHT, HTH, THH. Therefore, the probability is 3/8."}, {"type": "multiple_choice", "question": "Which function has a horizontal asymptote at y = 0?", "options": ["f(x) = 1/x", "f(x) = x²", "f(x) = x + 2", "f(x) = √x"], "correctAnswer": 0, "explanation": "The function f(x) = 1/x has a horizontal asymptote at y = 0 because as x approaches infinity (or negative infinity), 1/x approaches 0."}]}}}}, {"id": "etfv-test-conclusion", "type": "test_screen_conclusion", "order": 3, "estimatedTimeSeconds": 30, "content": {"headline": "Function Types and Probability: Test Complete", "body_md": "Great job completing the test! You've demonstrated your understanding of different function types and basic probability concepts.", "visual": {"type": "unsplash_search", "value": "mathematics success"}, "interactive_element": {"type": "button", "text": "Return to Module", "action": "return_to_module"}}}]}}