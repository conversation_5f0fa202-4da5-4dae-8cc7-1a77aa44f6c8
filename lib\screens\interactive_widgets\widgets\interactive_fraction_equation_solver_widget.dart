import 'package:flutter/material.dart';
import 'dart:math' as math;

/// A widget that demonstrates solving equations with fractions.
class InteractiveFractionEquationSolverWidget extends StatefulWidget {
  final Map<String, dynamic> data;
  final Function(bool)? onStateChanged;
  final Color primaryColor;
  final Color secondaryColor;
  final Color backgroundColor;
  final Color textColor;

  const InteractiveFractionEquationSolverWidget({
    super.key,
    this.data = const {},
    this.onStateChanged,
    this.primaryColor = Colors.blue,
    this.secondaryColor = Colors.orange,
    this.backgroundColor = Colors.white,
    this.textColor = Colors.black87,
  });

  @override
  State<InteractiveFractionEquationSolverWidget> createState() =>
      _InteractiveFractionEquationSolverWidgetState();
}

class _InteractiveFractionEquationSolverWidgetState
    extends State<InteractiveFractionEquationSolverWidget>
    with SingleTickerProviderStateMixin {
  // Animation controller
  late AnimationController _animationController;
  late Animation<double> _animation;

  // State variables
  bool _isCompleted = false;
  bool _isAnimating = false;
  int _currentEquationIndex = 0;
  int _currentStep = 0;
  List<EquationData> _equations = [];
  late EquationData _currentEquation;
  bool _showQuiz = false;
  String? _selectedAnswer;
  bool _isCorrect = false;
  String? _feedbackMessage;

  @override
  void initState() {
    super.initState();

    // Initialize animation controller
    _animationController = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 800),
    );

    _animation = CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeInOut,
    );

    // Add listener to animation controller
    _animationController.addStatusListener((status) {
      if (status == AnimationStatus.completed) {
        setState(() {
          _currentStep++;
          if (_currentStep < _currentEquation.steps.length) {
            _animationController.reset();
            _animationController.forward();
          } else {
            _isAnimating = false;
          }
        });
      }
    });

    // Initialize equations
    _initializeEquations();
    _currentEquation = _equations[_currentEquationIndex];
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  void _initializeEquations() {
    // Check if equations are provided in the data
    if (widget.data.containsKey('equations') &&
        widget.data['equations'] is List &&
        widget.data['equations'].isNotEmpty) {

      final equationsData = widget.data['equations'] as List;
      for (final eqData in equationsData) {
        if (eqData is Map<String, dynamic>) {
          final equation = EquationData.fromJson(eqData);
          _equations.add(equation);
        }
      }
    }

    // If no equations were provided, create default ones
    if (_equations.isEmpty) {
      _equations = [
        EquationData(
          initialEquation: '\\frac{x}{3} = 4',
          solution: 'x = 12',
          steps: [
            EquationStep(
              equation: '\\frac{x}{3} = 4',
              explanation: 'Start with the original equation.',
              operation: 'Original equation',
              fractionHighlights: [
                FractionHighlight(
                  numerator: 'x',
                  denominator: '3',
                  color: Colors.grey.withOpacity(0.3),
                ),
              ],
            ),
            EquationStep(
              equation: '\\frac{x}{3} = 4',
              explanation: 'To solve for x, we need to multiply both sides by 3 to eliminate the fraction.',
              operation: 'Identify the fraction',
              fractionHighlights: [
                FractionHighlight(
                  numerator: 'x',
                  denominator: '3',
                  color: Colors.orange.withOpacity(0.3),
                ),
              ],
            ),
            EquationStep(
              equation: '\\frac{x}{3} \\cdot 3 = 4 \\cdot 3',
              explanation: 'Multiply both sides by 3.',
              operation: 'Multiply both sides by 3',
              fractionHighlights: [
                FractionHighlight(
                  numerator: 'x',
                  denominator: '3',
                  color: Colors.orange.withOpacity(0.3),
                ),
              ],
              highlightRanges: [
                HighlightRange(start: 7, end: 10, color: Colors.green.withOpacity(0.3)),
                HighlightRange(start: 14, end: 17, color: Colors.green.withOpacity(0.3)),
              ],
            ),
            EquationStep(
              equation: 'x = 12',
              explanation: 'Simplify both sides. On the left, x/3 × 3 = x. On the right, 4 × 3 = 12.',
              operation: 'Simplify',
              highlightRanges: [
                HighlightRange(start: 0, end: 6, color: Colors.green.withOpacity(0.3)),
              ],
            ),
          ],
          quiz: {
            'question': 'Solve for x: \\frac{x}{5} = 7',
            'options': ['x = 35', 'x = 7/5', 'x = 12', 'x = 2'],
            'correctAnswer': 'x = 35',
            'explanation': 'To solve \\frac{x}{5} = 7, multiply both sides by 5: \\frac{x}{5} \\cdot 5 = 7 \\cdot 5, which gives x = 35.'
          },
        ),
        EquationData(
          initialEquation: '\\frac{2x}{5} = 8',
          solution: 'x = 20',
          steps: [
            EquationStep(
              equation: '\\frac{2x}{5} = 8',
              explanation: 'Start with the original equation.',
              operation: 'Original equation',
              fractionHighlights: [
                FractionHighlight(
                  numerator: '2x',
                  denominator: '5',
                  color: Colors.grey.withOpacity(0.3),
                ),
              ],
            ),
            EquationStep(
              equation: '\\frac{2x}{5} = 8',
              explanation: 'To solve for x, we need to multiply both sides by 5 to eliminate the fraction.',
              operation: 'Identify the fraction',
              fractionHighlights: [
                FractionHighlight(
                  numerator: '2x',
                  denominator: '5',
                  color: Colors.orange.withOpacity(0.3),
                ),
              ],
            ),
            EquationStep(
              equation: '\\frac{2x}{5} \\cdot 5 = 8 \\cdot 5',
              explanation: 'Multiply both sides by 5.',
              operation: 'Multiply both sides by 5',
              fractionHighlights: [
                FractionHighlight(
                  numerator: '2x',
                  denominator: '5',
                  color: Colors.orange.withOpacity(0.3),
                ),
              ],
              highlightRanges: [
                HighlightRange(start: 8, end: 11, color: Colors.green.withOpacity(0.3)),
                HighlightRange(start: 15, end: 18, color: Colors.green.withOpacity(0.3)),
              ],
            ),
            EquationStep(
              equation: '2x = 40',
              explanation: 'Simplify both sides. On the left, 2x/5 × 5 = 2x. On the right, 8 × 5 = 40.',
              operation: 'Simplify',
              highlightRanges: [
                HighlightRange(start: 0, end: 7, color: Colors.green.withOpacity(0.3)),
              ],
            ),
            EquationStep(
              equation: '\\frac{2x}{2} = \\frac{40}{2}',
              explanation: 'To isolate x, divide both sides by 2.',
              operation: 'Divide both sides by 2',
              fractionHighlights: [
                FractionHighlight(
                  numerator: '2x',
                  denominator: '2',
                  color: Colors.orange.withOpacity(0.3),
                ),
                FractionHighlight(
                  numerator: '40',
                  denominator: '2',
                  color: Colors.orange.withOpacity(0.3),
                ),
              ],
            ),
            EquationStep(
              equation: 'x = 20',
              explanation: 'Simplify both sides. On the left, 2x/2 = x. On the right, 40/2 = 20.',
              operation: 'Simplify',
              highlightRanges: [
                HighlightRange(start: 0, end: 6, color: Colors.green.withOpacity(0.3)),
              ],
            ),
          ],
          quiz: {
            'question': 'Solve for x: \\frac{3x}{4} = 9',
            'options': ['x = 12', 'x = 27/4', 'x = 12/3', 'x = 12'],
            'correctAnswer': 'x = 12',
            'explanation': 'To solve \\frac{3x}{4} = 9, multiply both sides by 4: \\frac{3x}{4} \\cdot 4 = 9 \\cdot 4, which gives 3x = 36. Then divide both sides by 3: x = 12.'
          },
        ),
        EquationData(
          initialEquation: '\\frac{x+2}{4} = 5',
          solution: 'x = 18',
          steps: [
            EquationStep(
              equation: '\\frac{x+2}{4} = 5',
              explanation: 'Start with the original equation.',
              operation: 'Original equation',
              fractionHighlights: [
                FractionHighlight(
                  numerator: 'x+2',
                  denominator: '4',
                  color: Colors.grey.withOpacity(0.3),
                ),
              ],
            ),
            EquationStep(
              equation: '\\frac{x+2}{4} = 5',
              explanation: 'To solve for x, we need to multiply both sides by 4 to eliminate the fraction.',
              operation: 'Identify the fraction',
              fractionHighlights: [
                FractionHighlight(
                  numerator: 'x+2',
                  denominator: '4',
                  color: Colors.orange.withOpacity(0.3),
                ),
              ],
            ),
            EquationStep(
              equation: '\\frac{x+2}{4} \\cdot 4 = 5 \\cdot 4',
              explanation: 'Multiply both sides by 4.',
              operation: 'Multiply both sides by 4',
              fractionHighlights: [
                FractionHighlight(
                  numerator: 'x+2',
                  denominator: '4',
                  color: Colors.orange.withOpacity(0.3),
                ),
              ],
              highlightRanges: [
                HighlightRange(start: 8, end: 11, color: Colors.green.withOpacity(0.3)),
                HighlightRange(start: 15, end: 18, color: Colors.green.withOpacity(0.3)),
              ],
            ),
            EquationStep(
              equation: 'x + 2 = 20',
              explanation: 'Simplify both sides. On the left, (x+2)/4 × 4 = x+2. On the right, 5 × 4 = 20.',
              operation: 'Simplify',
              highlightRanges: [
                HighlightRange(start: 0, end: 9, color: Colors.green.withOpacity(0.3)),
              ],
            ),
            EquationStep(
              equation: 'x + 2 - 2 = 20 - 2',
              explanation: 'To isolate x, subtract 2 from both sides.',
              operation: 'Subtract 2 from both sides',
              highlightRanges: [
                HighlightRange(start: 6, end: 9, color: Colors.orange.withOpacity(0.3)),
                HighlightRange(start: 14, end: 17, color: Colors.orange.withOpacity(0.3)),
              ],
            ),
            EquationStep(
              equation: 'x = 18',
              explanation: 'Simplify both sides. On the left, x+2-2 = x. On the right, 20-2 = 18.',
              operation: 'Simplify',
              highlightRanges: [
                HighlightRange(start: 0, end: 6, color: Colors.green.withOpacity(0.3)),
              ],
            ),
          ],
          quiz: {
            'question': 'Solve for x: \\frac{x-3}{2} = 6',
            'options': ['x = 15', 'x = 6', 'x = 3', 'x = 9'],
            'correctAnswer': 'x = 15',
            'explanation': 'To solve \\frac{x-3}{2} = 6, multiply both sides by 2: \\frac{x-3}{2} \\cdot 2 = 6 \\cdot 2, which gives x-3 = 12. Then add 3 to both sides: x = 15.'
          },
        ),
      ];
    }
  }

  void _startAnimation() {
    if (_currentEquation.steps.isEmpty) return;

    setState(() {
      _currentStep = 0;
      _isAnimating = true;
      _showQuiz = false;
      _selectedAnswer = null;
      _feedbackMessage = null;
    });

    _animationController.reset();
    _animationController.forward();
  }

  void _stopAnimation() {
    setState(() {
      _isAnimating = false;
    });
    _animationController.stop();
  }

  void _resetAnimation() {
    setState(() {
      _currentStep = 0;
      _isAnimating = false;
      _showQuiz = false;
      _selectedAnswer = null;
      _feedbackMessage = null;
    });
    _animationController.reset();
  }

  void _showQuizQuestion() {
    setState(() {
      _showQuiz = true;
      _selectedAnswer = null;
      _feedbackMessage = null;
    });
  }

  void _checkAnswer(String answer) {
    final isCorrect = answer == _currentEquation.quiz['correctAnswer'];

    setState(() {
      _selectedAnswer = answer;
      _isCorrect = isCorrect;

      if (isCorrect) {
        _feedbackMessage = 'Correct! ${_currentEquation.quiz['explanation']}';
      } else {
        _feedbackMessage = 'Not quite. ${_currentEquation.quiz['explanation']}';
      }
    });
  }

  void _nextEquation() {
    if (_currentEquationIndex < _equations.length - 1) {
      setState(() {
        _currentEquationIndex++;
        _currentEquation = _equations[_currentEquationIndex];
        _resetAnimation();
      });
    } else {
      // All equations completed
      setState(() {
        _isCompleted = true;
      });
      widget.onStateChanged?.call(true);
    }
  }

  void _previousEquation() {
    if (_currentEquationIndex > 0) {
      setState(() {
        _currentEquationIndex--;
        _currentEquation = _equations[_currentEquationIndex];
        _resetAnimation();
      });
    }
  }

  void _resetWidget() {
    setState(() {
      _currentEquationIndex = 0;
      _currentEquation = _equations[_currentEquationIndex];
      _resetAnimation();
      _isCompleted = false;
    });
    widget.onStateChanged?.call(false);
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: widget.backgroundColor,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withOpacity(0.2),
            spreadRadius: 1,
            blurRadius: 3,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: _isCompleted ? _buildCompletionScreen() : _buildMainContent(),
    );
  }

  Widget _buildMainContent() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Title and progress indicator
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Text(
              'Fraction Equation Solver',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
                color: widget.primaryColor,
              ),
            ),
            Text(
              'Example ${_currentEquationIndex + 1}/${_equations.length}',
              style: TextStyle(
                fontSize: 14,
                color: widget.textColor.withOpacity(0.7),
              ),
            ),
          ],
        ),

        const SizedBox(height: 16),

        // Fraction equations reminder
        Container(
          width: double.infinity,
          padding: const EdgeInsets.all(12),
          decoration: BoxDecoration(
            color: widget.primaryColor.withOpacity(0.1),
            borderRadius: BorderRadius.circular(8),
            border: Border.all(color: widget.primaryColor.withOpacity(0.3)),
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                'Solving Equations with Fractions',
                style: TextStyle(
                  fontWeight: FontWeight.bold,
                  color: widget.primaryColor,
                ),
              ),
              const SizedBox(height: 8),
              Text(
                'Key Strategy: Multiply both sides by the denominator to eliminate fractions.',
                style: TextStyle(
                  color: widget.textColor,
                  fontWeight: FontWeight.w500,
                ),
              ),
              const SizedBox(height: 4),
              Text(
                'Example: To solve \\frac{x}{4} = 5, multiply both sides by 4 to get x = 20.',
                style: TextStyle(
                  color: widget.textColor,
                  fontStyle: FontStyle.italic,
                ),
              ),
            ],
          ),
        ),

        const SizedBox(height: 24),

        // Current equation
        if (!_showQuiz) ...[
          Text(
            'Equation:',
            style: TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.w500,
              color: widget.textColor,
            ),
          ),
          const SizedBox(height: 8),
          Container(
            width: double.infinity,
            padding: const EdgeInsets.symmetric(vertical: 16, horizontal: 16),
            decoration: BoxDecoration(
              color: Colors.grey.shade50,
              borderRadius: BorderRadius.circular(8),
              border: Border.all(color: Colors.grey.shade300),
            ),
            child: _isAnimating && _currentStep < _currentEquation.steps.length
                ? _buildEquationDisplay(_currentEquation.steps[_currentStep])
                : _buildEquationDisplay(EquationStep(
                    equation: _currentEquation.initialEquation,
                    explanation: '',
                    operation: '',
                  )),
          ),

          const SizedBox(height: 16),

          // Current step explanation
          if (_isAnimating && _currentStep < _currentEquation.steps.length) ...[
            Container(
              width: double.infinity,
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: widget.secondaryColor.withOpacity(0.1),
                borderRadius: BorderRadius.circular(8),
                border: Border.all(color: widget.secondaryColor.withOpacity(0.3)),
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'Step ${_currentStep + 1}: ${_currentEquation.steps[_currentStep].operation}',
                    style: TextStyle(
                      fontWeight: FontWeight.bold,
                      color: widget.secondaryColor,
                    ),
                  ),
                  const SizedBox(height: 4),
                  Text(
                    _currentEquation.steps[_currentStep].explanation,
                    style: TextStyle(
                      color: widget.textColor,
                    ),
                  ),
                ],
              ),
            ),
          ],

          const Spacer(),

          // Animation controls
          Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              if (!_isAnimating) ...[
                ElevatedButton.icon(
                  onPressed: _startAnimation,
                  icon: const Icon(Icons.play_arrow),
                  label: const Text('Start Animation'),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: widget.primaryColor,
                  ),
                ),
                const SizedBox(width: 16),
                ElevatedButton.icon(
                  onPressed: _showQuizQuestion,
                  icon: const Icon(Icons.quiz),
                  label: const Text('Try a Quiz'),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: widget.secondaryColor,
                  ),
                ),
              ] else ...[
                ElevatedButton.icon(
                  onPressed: _stopAnimation,
                  icon: const Icon(Icons.pause),
                  label: const Text('Pause'),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Colors.red,
                  ),
                ),
              ],
            ],
          ),
        ],

        // Quiz section
        if (_showQuiz) ...[
          Text(
            'Quiz:',
            style: TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.w500,
              color: widget.textColor,
            ),
          ),
          const SizedBox(height: 8),
          Container(
            width: double.infinity,
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: widget.primaryColor.withOpacity(0.1),
              borderRadius: BorderRadius.circular(8),
              border: Border.all(color: widget.primaryColor.withOpacity(0.3)),
            ),
            child: Text(
              _currentEquation.quiz['question'],
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
                color: widget.textColor,
              ),
            ),
          ),

          const SizedBox(height: 16),

          // Answer options
          ...(_currentEquation.quiz['options'] as List<String>).map((option) {
            final bool isSelected = _selectedAnswer == option;
            final bool isCorrect = option == _currentEquation.quiz['correctAnswer'];

            return Padding(
              padding: const EdgeInsets.only(bottom: 8.0),
              child: InkWell(
                onTap: _selectedAnswer == null ? () => _checkAnswer(option) : null,
                child: Container(
                  width: double.infinity,
                  padding: const EdgeInsets.all(12),
                  decoration: BoxDecoration(
                    color: isSelected
                        ? (isCorrect ? Colors.green.withOpacity(0.2) : Colors.red.withOpacity(0.2))
                        : Colors.grey.shade50,
                    borderRadius: BorderRadius.circular(8),
                    border: Border.all(
                      color: isSelected
                          ? (isCorrect ? Colors.green : Colors.red)
                          : Colors.grey.shade300,
                    ),
                  ),
                  child: Row(
                    children: [
                      Expanded(
                        child: Text(
                          option,
                          style: TextStyle(
                            fontWeight: isSelected ? FontWeight.bold : FontWeight.normal,
                            color: widget.textColor,
                          ),
                        ),
                      ),
                      if (isSelected)
                        Icon(
                          isCorrect ? Icons.check_circle : Icons.cancel,
                          color: isCorrect ? Colors.green : Colors.red,
                        ),
                    ],
                  ),
                ),
              ),
            );
          }).toList(),

          if (_feedbackMessage != null)
            Container(
              width: double.infinity,
              padding: const EdgeInsets.all(12),
              margin: const EdgeInsets.only(top: 16),
              decoration: BoxDecoration(
                color: _isCorrect
                    ? Colors.green.withOpacity(0.1)
                    : Colors.red.withOpacity(0.1),
                borderRadius: BorderRadius.circular(8),
                border: Border.all(
                  color: _isCorrect ? Colors.green : Colors.red,
                ),
              ),
              child: Text(
                _feedbackMessage!,
                style: TextStyle(
                  color: _isCorrect ? Colors.green : Colors.red,
                ),
              ),
            ),

          const Spacer(),

          // Back to animation button
          ElevatedButton.icon(
            onPressed: _startAnimation,
            icon: const Icon(Icons.arrow_back),
            label: const Text('Back to Animation'),
            style: ElevatedButton.styleFrom(
              backgroundColor: widget.primaryColor,
            ),
          ),
        ],

        const SizedBox(height: 16),

        // Navigation buttons
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            // Previous button
            ElevatedButton(
              onPressed: _currentEquationIndex > 0 ? _previousEquation : null,
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.grey.shade200,
                foregroundColor: Colors.black87,
              ),
              child: const Text('Previous'),
            ),

            // Next button
            ElevatedButton(
              onPressed: _nextEquation,
              style: ElevatedButton.styleFrom(
                backgroundColor: widget.primaryColor,
                foregroundColor: Colors.white,
              ),
              child: Text(_currentEquationIndex < _equations.length - 1 ? 'Next' : 'Finish'),
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildEquationDisplay(EquationStep step) {
    // Parse the equation string to identify fractions and other parts
    String equation = step.equation;

    // If there are fraction highlights, render them specially
    if (step.fractionHighlights != null && step.fractionHighlights!.isNotEmpty) {
      return Row(
        mainAxisAlignment: MainAxisAlignment.center,
        children: _buildFractionEquation(equation, step),
      );
    }

    // If there are regular highlights, use those
    if (step.highlightRanges != null && step.highlightRanges!.isNotEmpty) {
      return _buildHighlightedText(equation, step.highlightRanges!);
    }

    // Default rendering
    return Text(
      equation,
      style: TextStyle(
        fontSize: 24,
        fontWeight: FontWeight.bold,
        color: widget.textColor,
      ),
      textAlign: TextAlign.center,
    );
  }

  List<Widget> _buildFractionEquation(String equation, EquationStep step) {
    List<Widget> parts = [];

    // Simple parser for fraction equations
    // This is a basic implementation and would need to be more sophisticated for complex equations
    RegExp fractionRegex = RegExp(r'\\frac\{([^}]*)\}\{([^}]*)\}');

    int currentIndex = 0;

    // Find all fractions in the equation
    Iterable<RegExpMatch> matches = fractionRegex.allMatches(equation);

    for (RegExpMatch match in matches) {
      // Add text before the fraction
      if (match.start > currentIndex) {
        parts.add(
          Text(
            equation.substring(currentIndex, match.start),
            style: TextStyle(
              fontSize: 24,
              fontWeight: FontWeight.bold,
              color: widget.textColor,
            ),
          ),
        );
      }

      // Extract numerator and denominator
      String numerator = match.group(1) ?? '';
      String denominator = match.group(2) ?? '';

      // Check if this fraction should be highlighted
      Color? highlightColor;
      if (step.fractionHighlights != null) {
        for (FractionHighlight highlight in step.fractionHighlights!) {
          if (highlight.numerator == numerator && highlight.denominator == denominator) {
            highlightColor = highlight.color;
            break;
          }
        }
      }

      // Add the fraction widget
      parts.add(
        Container(
          padding: EdgeInsets.symmetric(horizontal: 4, vertical: 2),
          decoration: BoxDecoration(
            color: highlightColor,
            borderRadius: BorderRadius.circular(4),
          ),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              // Numerator
              Text(
                numerator,
                style: TextStyle(
                  fontSize: 20,
                  fontWeight: FontWeight.bold,
                  color: widget.textColor,
                ),
              ),
              // Fraction line
              Container(
                height: 2,
                width: 30,
                color: widget.textColor,
                margin: EdgeInsets.symmetric(vertical: 2),
              ),
              // Denominator
              Text(
                denominator,
                style: TextStyle(
                  fontSize: 20,
                  fontWeight: FontWeight.bold,
                  color: widget.textColor,
                ),
              ),
            ],
          ),
        ),
      );

      currentIndex = match.end;
    }

    // Add any remaining text after the last fraction
    if (currentIndex < equation.length) {
      parts.add(
        Text(
          equation.substring(currentIndex),
          style: TextStyle(
            fontSize: 24,
            fontWeight: FontWeight.bold,
            color: widget.textColor,
          ),
        ),
      );
    }

    return parts;
  }

  Widget _buildHighlightedText(String text, List<HighlightRange> highlights) {
    // Sort highlights by start index
    highlights.sort((a, b) => a.start.compareTo(b.start));

    final List<TextSpan> spans = [];
    int currentIndex = 0;

    for (final highlight in highlights) {
      if (highlight.start > currentIndex) {
        // Add non-highlighted text before this highlight
        spans.add(
          TextSpan(
            text: text.substring(currentIndex, highlight.start),
            style: TextStyle(
              fontSize: 24,
              fontWeight: FontWeight.bold,
              color: widget.textColor,
            ),
          ),
        );
      }

      // Add highlighted text
      spans.add(
        TextSpan(
          text: text.substring(highlight.start, highlight.end),
          style: TextStyle(
            fontSize: 24,
            fontWeight: FontWeight.bold,
            color: widget.textColor,
            backgroundColor: highlight.color,
          ),
        ),
      );

      currentIndex = highlight.end;
    }

    // Add any remaining text after the last highlight
    if (currentIndex < text.length) {
      spans.add(
        TextSpan(
          text: text.substring(currentIndex),
          style: TextStyle(
            fontSize: 24,
            fontWeight: FontWeight.bold,
            color: widget.textColor,
          ),
        ),
      );
    }

    return RichText(
      text: TextSpan(children: spans),
      textAlign: TextAlign.center,
    );
  }

  Widget _buildCompletionScreen() {
    return Column(
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        Icon(
          Icons.check_circle,
          size: 80,
          color: Colors.green,
        ),
        const SizedBox(height: 24),
        Text(
          'Congratulations!',
          style: TextStyle(
            fontSize: 24,
            fontWeight: FontWeight.bold,
            color: widget.primaryColor,
          ),
        ),
        const SizedBox(height: 16),
        Text(
          'You\'ve completed all the fraction equation examples!',
          textAlign: TextAlign.center,
          style: TextStyle(
            fontSize: 16,
            color: widget.textColor,
          ),
        ),
        const SizedBox(height: 32),
        ElevatedButton.icon(
          onPressed: _resetWidget,
          icon: const Icon(Icons.refresh),
          label: const Text('Start Again'),
          style: ElevatedButton.styleFrom(
            backgroundColor: widget.secondaryColor,
            padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
          ),
        ),
      ],
    );
  }
}

/// Data class for equation information
class EquationData {
  final String initialEquation;
  final String solution;
  final List<EquationStep> steps;
  final Map<String, dynamic> quiz;

  EquationData({
    required this.initialEquation,
    required this.solution,
    required this.steps,
    required this.quiz,
  });

  factory EquationData.fromJson(Map<String, dynamic> json) {
    final steps = <EquationStep>[];
    if (json.containsKey('steps') && json['steps'] is List) {
      for (final stepData in json['steps']) {
        if (stepData is Map<String, dynamic>) {
          steps.add(EquationStep.fromJson(stepData));
        }
      }
    }

    return EquationData(
      initialEquation: json['initialEquation'] ?? '\\frac{x}{3} = 4',
      solution: json['solution'] ?? 'x = 12',
      steps: steps,
      quiz: json['quiz'] ?? {
        'question': 'Solve for x: \\frac{x}{5} = 7',
        'options': ['x = 35', 'x = 7/5', 'x = 12', 'x = 2'],
        'correctAnswer': 'x = 35',
        'explanation': 'To solve \\frac{x}{5} = 7, multiply both sides by 5: \\frac{x}{5} \\cdot 5 = 7 \\cdot 5, which gives x = 35.'
      },
    );
  }
}

/// Data class for equation solution steps
class EquationStep {
  final String equation;
  final String explanation;
  final String operation;
  final List<HighlightRange>? highlightRanges;
  final List<FractionHighlight>? fractionHighlights;

  EquationStep({
    required this.equation,
    required this.explanation,
    required this.operation,
    this.highlightRanges,
    this.fractionHighlights,
  });

  factory EquationStep.fromJson(Map<String, dynamic> json) {
    final highlightRanges = <HighlightRange>[];
    if (json.containsKey('highlightRanges') && json['highlightRanges'] is List) {
      for (final rangeData in json['highlightRanges']) {
        if (rangeData is Map<String, dynamic>) {
          highlightRanges.add(HighlightRange.fromJson(rangeData));
        }
      }
    }

    final fractionHighlights = <FractionHighlight>[];
    if (json.containsKey('fractionHighlights') && json['fractionHighlights'] is List) {
      for (final highlightData in json['fractionHighlights']) {
        if (highlightData is Map<String, dynamic>) {
          fractionHighlights.add(FractionHighlight.fromJson(highlightData));
        }
      }
    }

    return EquationStep(
      equation: json['equation'] ?? '',
      explanation: json['explanation'] ?? '',
      operation: json['operation'] ?? '',
      highlightRanges: highlightRanges.isEmpty ? null : highlightRanges,
      fractionHighlights: fractionHighlights.isEmpty ? null : fractionHighlights,
    );
  }
}

/// Data class for highlighting parts of expressions
class HighlightRange {
  final int start;
  final int end;
  final Color color;

  HighlightRange({
    required this.start,
    required this.end,
    required this.color,
  });

  factory HighlightRange.fromJson(Map<String, dynamic> json) {
    return HighlightRange(
      start: json['start'] ?? 0,
      end: json['end'] ?? 0,
      color: Color(json['color'] ?? 0x4D9E9E9E), // Default to grey with 30% opacity
    );
  }
}

/// Data class for highlighting fractions
class FractionHighlight {
  final String numerator;
  final String denominator;
  final Color color;

  FractionHighlight({
    required this.numerator,
    required this.denominator,
    required this.color,
  });

  factory FractionHighlight.fromJson(Map<String, dynamic> json) {
    return FractionHighlight(
      numerator: json['numerator'] ?? '',
      denominator: json['denominator'] ?? '',
      color: Color(json['color'] ?? 0x4D9E9E9E), // Default to grey with 30% opacity
    );
  }
}
