{"id": "connecting-functions-to-probability-distributions", "title": "CONNECTING FUNCTIONS TO PROBABILITY DISTRIBUTIONS", "description": "See how functions can describe the probabilities of different outcomes.", "order": 4, "lessons": [{"id": "random-variables-as-functions", "title": "Random Variables as Function Outputs", "description": "Understand variables whose values depend on chance.", "order": 1, "type": "interactive_lesson", "estimatedTimeMinutes": 10, "contentBlocks": [{"id": "rvf-screen1-intro", "type": "lesson_screen", "order": 1, "estimatedTimeSeconds": 30, "content": {"headline": "Random Variables: Quantifying Chance", "body_md": "A random variable is a variable whose value depends on the outcome of a random event. It's like a function that maps outcomes from a sample space to numerical values.", "visual": {"type": "giphy_search", "value": "random variable probability"}, "interactive_element": {"type": "button", "text": "Let's Explore Random Variables!", "action": "next_screen"}}}, {"id": "rvf-screen2-definition", "type": "lesson_screen", "order": 2, "estimatedTimeSeconds": 60, "content": {"headline": "Defining Random Variables", "body_md": "A random variable X assigns a numerical value to each outcome in a sample space.\n\nFor example, if we roll a die, we can define X as the number that appears on the die. So X can take values 1, 2, 3, 4, 5, or 6, each with probability 1/6.", "visual": {"type": "unsplash_search", "value": "dice probability"}, "interactive_element": {"type": "multiple_choice_text", "question": "Which of these is a random variable?", "options": [{"id": "a", "text": "The number of heads when flipping 10 coins", "is_correct": true, "feedback_correct": "Correct! This is a random variable because it assigns a numerical value (0-10) to each possible outcome of flipping 10 coins."}, {"id": "b", "text": "The probability of getting heads on a fair coin", "is_correct": false, "feedback_incorrect": "Incorrect. This is a fixed value (0.5), not a random variable."}, {"id": "c", "text": "The formula for calculating the area of a circle", "is_correct": false, "feedback_incorrect": "Incorrect. This is a deterministic formula, not a random variable."}, {"id": "d", "text": "The number of sides on a standard die", "is_correct": false, "feedback_incorrect": "Incorrect. This is a fixed value (6), not a random variable."}]}}}, {"id": "rvf-screen3-types", "type": "lesson_screen", "order": 3, "estimatedTimeSeconds": 60, "content": {"headline": "Types of Random Variables", "body_md": "There are two main types of random variables:\n\n- **Discrete**: Takes on a countable number of distinct values (e.g., number of heads when flipping coins)\n- **Continuous**: Can take any value within a range (e.g., height of a randomly selected person)\n\nEach type has its own way of describing probabilities.", "visual": {"type": "giphy_search", "value": "discrete vs continuous"}, "interactive_element": {"type": "multiple_choice_text", "question": "Which of these is a continuous random variable?", "options": [{"id": "a", "text": "Number of students in a randomly selected classroom", "is_correct": false, "feedback_incorrect": "Incorrect. This is discrete because it can only take whole number values."}, {"id": "b", "text": "Time it takes to complete a marathon", "is_correct": true, "feedback_correct": "Correct! Time can take any value within a range, making it continuous."}, {"id": "c", "text": "Number of cars passing through an intersection in an hour", "is_correct": false, "feedback_incorrect": "Incorrect. This is discrete because it can only take whole number values."}, {"id": "d", "text": "Number of heads when flipping 5 coins", "is_correct": false, "feedback_incorrect": "Incorrect. This is discrete because it can only take values 0, 1, 2, 3, 4, or 5."}]}}}, {"id": "rvf-screen4-functions", "type": "lesson_screen", "order": 4, "estimatedTimeSeconds": 60, "content": {"headline": "Random Variables as Functions", "body_md": "A random variable X can be viewed as a function that maps outcomes to numbers:\n\nX: Sample Space → Real Numbers\n\nFor example, if we flip 3 coins, the sample space is {HHH, HHT, HTH, HTT, THH, THT, TTH, TTT}. If X counts the number of heads, then:\nX(HHH) = 3\nX(HHT) = X(HTH) = X(THH) = 2\nX(HTT) = X(THT) = X(TTH) = 1\nX(TTT) = 0", "visual": {"type": "unsplash_search", "value": "coin flip probability"}, "interactive_element": {"type": "text_input_quick", "question": "When rolling two dice, let X be the sum of the values. What is P(X = 7)?", "correct_answer_regex": "^1\\/6$|^0\\.1667$|^0\\.167$|^0\\.17$|^6\\/36$", "placeholder": "Enter your answer as a fraction or decimal", "feedback_correct": "Correct! There are 6 ways to get a sum of 7 (1+6, 2+5, 3+4, 4+3, 5+2, 6+1) out of 36 possible outcomes, so P(X = 7) = 6/36 = 1/6", "feedback_incorrect": "Not quite. Count the number of ways to get a sum of 7 when rolling two dice, then divide by the total number of possible outcomes (36)."}}}, {"id": "rvf-screen5-conclusion", "type": "lesson_screen", "order": 5, "estimatedTimeSeconds": 30, "content": {"headline": "Random Variables: Bridging Probability and Functions", "body_md": "Great job! You now understand random variables as functions that map outcomes to numerical values. This concept forms the bridge between probability theory and functions, allowing us to analyze chance mathematically.", "visual": {"type": "unsplash_search", "value": "probability mathematics"}, "interactive_element": {"type": "button", "text": "Continue to Next Lesson", "action": "next_lesson"}}}]}, {"id": "discrete-probability-functions", "title": "Discrete Probability Functions", "description": "Visualize probabilities for countable outcomes.", "order": 2, "type": "interactive_lesson", "estimatedTimeMinutes": 10, "contentBlocks": [{"id": "dpf-screen1-intro", "type": "lesson_screen", "order": 1, "estimatedTimeSeconds": 30, "content": {"headline": "Probability Mass Functions (PMFs)", "body_md": "For discrete random variables, we use a Probability Mass Function (PMF) to describe the probability of each possible value. It's like a function that maps each value to its probability.", "visual": {"type": "giphy_search", "value": "discrete probability function"}, "interactive_element": {"type": "button", "text": "Let's Explore PMFs!", "action": "next_screen"}}}, {"id": "dpf-screen2-definition", "type": "lesson_screen", "order": 2, "estimatedTimeSeconds": 60, "content": {"headline": "Defining the PMF", "body_md": "The Probability Mass Function (PMF) of a discrete random variable X is defined as:\n\np(x) = P(X = x)\n\nFor example, if X is the number on a fair die, then p(1) = p(2) = p(3) = p(4) = p(5) = p(6) = 1/6", "visual": {"type": "unsplash_search", "value": "dice probability"}, "interactive_element": {"type": "multiple_choice_text", "question": "Which of these is a valid PMF for a discrete random variable?", "options": [{"id": "a", "text": "p(1) = 0.3, p(2) = 0.4, p(3) = 0.5", "is_correct": false, "feedback_incorrect": "Incorrect. The probabilities must sum to 1, but 0.3 + 0.4 + 0.5 = 1.2"}, {"id": "b", "text": "p(1) = 0.2, p(2) = 0.3, p(3) = 0.5", "is_correct": true, "feedback_correct": "Correct! The probabilities sum to 1 (0.2 + 0.3 + 0.5 = 1), making this a valid PMF."}, {"id": "c", "text": "p(1) = -0.1, p(2) = 0.6, p(3) = 0.5", "is_correct": false, "feedback_incorrect": "Incorrect. Probabilities cannot be negative."}, {"id": "d", "text": "p(1) = 0.3, p(2) = 0.3, p(3) = 0.3", "is_correct": false, "feedback_incorrect": "Incorrect. The probabilities must sum to 1, but 0.3 + 0.3 + 0.3 = 0.9"}]}}}, {"id": "dpf-screen3-properties", "type": "lesson_screen", "order": 3, "estimatedTimeSeconds": 60, "content": {"headline": "Properties of PMFs", "body_md": "A valid Probability Mass Function must satisfy these properties:\n\n1. p(x) ≥ 0 for all x (probabilities are non-negative)\n2. Σp(x) = 1 (probabilities sum to 1)\n\nThe PMF can be represented as a formula, a table, or a graph (often as a bar chart).", "visual": {"type": "giphy_search", "value": "probability mass function"}, "interactive_element": {"type": "multiple_choice_text", "question": "For a fair coin flipped twice, let X be the number of heads. What is the PMF of X?", "options": [{"id": "a", "text": "p(0) = 1/4, p(1) = 1/2, p(2) = 1/4", "is_correct": true, "feedback_correct": "Correct! There's 1 way to get 0 heads (TT), 2 ways to get 1 head (HT, TH), and 1 way to get 2 heads (HH), out of 4 possible outcomes."}, {"id": "b", "text": "p(0) = 1/3, p(1) = 1/3, p(2) = 1/3", "is_correct": false, "feedback_incorrect": "Incorrect. The outcomes are not equally likely. There are more ways to get 1 head than 0 or 2 heads."}, {"id": "c", "text": "p(0) = 1/4, p(1) = 1/4, p(2) = 1/2", "is_correct": false, "feedback_incorrect": "Incorrect. This doesn't match the correct distribution for flipping a fair coin twice."}, {"id": "d", "text": "p(0) = 1/2, p(1) = 1/4, p(2) = 1/4", "is_correct": false, "feedback_incorrect": "Incorrect. This doesn't match the correct distribution for flipping a fair coin twice."}]}}}, {"id": "dpf-screen4-examples", "type": "lesson_screen", "order": 4, "estimatedTimeSeconds": 60, "content": {"headline": "Common Discrete Distributions", "body_md": "Several common discrete probability distributions have well-known PMFs:\n\n- **Binomial**: Number of successes in n independent trials (e.g., number of heads in n coin flips)\n- **Poisson**: Number of events in a fixed time interval (e.g., number of emails received per hour)\n- **Geometric**: Number of trials until the first success (e.g., number of coin flips until first heads)\n- **Uniform**: Equal probability for all values in a range (e.g., fair die roll)", "visual": {"type": "unsplash_search", "value": "probability distribution"}, "interactive_element": {"type": "text_input_quick", "question": "A fair die is rolled. Let X be the outcome. What is P(X is even)?", "correct_answer_regex": "^1\\/2$|^0\\.5$|^0\\.50$|^3\\/6$", "placeholder": "Enter your answer as a fraction or decimal", "feedback_correct": "Correct! P(X is even) = P(X = 2) + P(X = 4) + P(X = 6) = 1/6 + 1/6 + 1/6 = 3/6 = 1/2", "feedback_incorrect": "Not quite. Calculate P(X = 2) + P(X = 4) + P(X = 6) = 1/6 + 1/6 + 1/6 = 3/6 = 1/2"}}}, {"id": "dpf-screen5-conclusion", "type": "lesson_screen", "order": 5, "estimatedTimeSeconds": 30, "content": {"headline": "Discrete Probability Functions: Mapping Values to Probabilities", "body_md": "Excellent! You now understand Probability Mass Functions (PMFs) as functions that map each possible value of a discrete random variable to its probability. This powerful concept allows us to model and analyze many real-world random phenomena.", "visual": {"type": "unsplash_search", "value": "probability distribution"}, "interactive_element": {"type": "button", "text": "Continue to Next Lesson", "action": "next_lesson"}}}]}, {"id": "continuous-probability-functions", "title": "Continuous Probability Functions (Intuitive)", "description": "Get a feel for probabilities over a range.", "order": 3, "type": "interactive_lesson", "estimatedTimeMinutes": 10, "contentBlocks": [{"id": "cpf-screen1-intro", "type": "lesson_screen", "order": 1, "estimatedTimeSeconds": 30, "content": {"headline": "Probability Density Functions (PDFs)", "body_md": "For continuous random variables, we use a Probability Density Function (PDF) to describe the distribution of probabilities. Unlike PMFs, PDFs give the relative likelihood of the random variable falling within a particular range of values.", "visual": {"type": "giphy_search", "value": "continuous probability density function"}, "interactive_element": {"type": "button", "text": "Let's Explore PDFs!", "action": "next_screen"}}}, {"id": "cpf-screen2-definition", "type": "lesson_screen", "order": 2, "estimatedTimeSeconds": 60, "content": {"headline": "Understanding PDFs Intuitively", "body_md": "A Probability Density Function f(x) has these key properties:\n\n1. f(x) ≥ 0 for all x (the curve never goes below the x-axis)\n2. The total area under the curve equals 1\n3. The probability of X falling in an interval [a, b] is the area under the curve between a and b\n\nUnlike PMFs, the value of f(x) itself is NOT a probability!", "visual": {"type": "giphy_search", "value": "probability density function area"}, "interactive_element": {"type": "multiple_choice_text", "question": "For a continuous random variable X with PDF f(x), what does f(3) = 0.2 mean?", "options": [{"id": "a", "text": "P(X = 3) = 0.2", "is_correct": false, "feedback_incorrect": "Incorrect. For a continuous random variable, P(X = 3) = 0 for any specific point."}, {"id": "b", "text": "P(X ≤ 3) = 0.2", "is_correct": false, "feedback_incorrect": "Incorrect. P(X ≤ 3) would be the area under the curve from -∞ to 3, not just the height at x = 3."}, {"id": "c", "text": "The relative likelihood of X being near 3 is 0.2", "is_correct": true, "feedback_correct": "Correct! The value of the PDF at a point represents the relative likelihood or density of the random variable being near that point."}, {"id": "d", "text": "The probability that X is between 3 and 3.2 is 0.2", "is_correct": false, "feedback_incorrect": "Incorrect. The probability would be approximately 0.2 × 0.2 = 0.04 (assuming the PDF is roughly constant over this small interval)."}]}}}, {"id": "cpf-screen3-area", "type": "lesson_screen", "order": 3, "estimatedTimeSeconds": 60, "content": {"headline": "Probability as Area", "body_md": "For a continuous random variable X with PDF f(x), the probability that X falls in the interval [a, b] is:\n\nP(a ≤ X ≤ b) = ∫[a to b] f(x) dx\n\nThis is the area under the curve f(x) from a to b. For example, if X follows a uniform distribution on [0, 1], then f(x) = 1 for 0 ≤ x ≤ 1, and P(0.2 ≤ X ≤ 0.5) = 0.5 - 0.2 = 0.3.", "visual": {"type": "giphy_search", "value": "probability area under curve"}, "interactive_element": {"type": "multiple_choice_text", "question": "For a uniform distribution on [0, 10], what is P(3 ≤ X ≤ 7)?", "options": [{"id": "a", "text": "0.4", "is_correct": true, "feedback_correct": "Correct! For a uniform distribution on [0, 10], f(x) = 1/10, so P(3 ≤ X ≤ 7) = (7 - 3) × (1/10) = 4/10 = 0.4"}, {"id": "b", "text": "0.3", "is_correct": false, "feedback_incorrect": "Incorrect. Calculate the width of the interval (7 - 3 = 4) and multiply by the height of the PDF (1/10)."}, {"id": "c", "text": "0.5", "is_correct": false, "feedback_incorrect": "Incorrect. Calculate the width of the interval (7 - 3 = 4) and multiply by the height of the PDF (1/10)."}, {"id": "d", "text": "0.7", "is_correct": false, "feedback_incorrect": "Incorrect. Calculate the width of the interval (7 - 3 = 4) and multiply by the height of the PDF (1/10)."}]}}}, {"id": "cpf-screen4-examples", "type": "lesson_screen", "order": 4, "estimatedTimeSeconds": 60, "content": {"headline": "Common Continuous Distributions", "body_md": "Several common continuous probability distributions have well-known PDFs:\n\n- **Uniform**: Equal likelihood across an interval (e.g., random number generator)\n- **Normal (Gaussian)**: Bell-shaped curve (e.g., heights of people, measurement errors)\n- **Exponential**: Models time between events (e.g., time until next customer arrives)\n- **Beta**: Flexible distribution on [0, 1] (e.g., probability of success in Bayesian statistics)", "visual": {"type": "unsplash_search", "value": "normal distribution bell curve"}, "interactive_element": {"type": "multiple_choice_text", "question": "Which distribution would best model the heights of adult humans?", "options": [{"id": "a", "text": "Uniform distribution", "is_correct": false, "feedback_incorrect": "Incorrect. Heights are not equally likely across a range; they cluster around an average."}, {"id": "b", "text": "Normal distribution", "is_correct": true, "feedback_correct": "Correct! Heights tend to cluster around a mean value with symmetric variation, making the normal distribution appropriate."}, {"id": "c", "text": "Exponential distribution", "is_correct": false, "feedback_incorrect": "Incorrect. Exponential distributions are skewed and typically model waiting times, not physical measurements like height."}, {"id": "d", "text": "Binomial distribution", "is_correct": false, "feedback_incorrect": "Incorrect. Binomial is a discrete distribution for counting successes, not appropriate for continuous measurements like height."}]}}}, {"id": "cpf-screen5-conclusion", "type": "lesson_screen", "order": 5, "estimatedTimeSeconds": 30, "content": {"headline": "Continuous Probability Functions: Area Under the Curve", "body_md": "Great job! You now have an intuitive understanding of Probability Density Functions (PDFs) and how they relate to continuous random variables. Remember that with PDFs, probability is represented by area, not height.", "visual": {"type": "unsplash_search", "value": "probability density function"}, "interactive_element": {"type": "button", "text": "Continue to Next Lesson", "action": "next_lesson"}}}]}, {"id": "expected-value", "title": "Expected Value: The Average Outcome (Functionally)", "description": "Calculate the mean using the probability function.", "order": 4, "type": "interactive_lesson", "estimatedTimeMinutes": 10, "contentBlocks": [{"id": "ev-screen1-intro", "type": "lesson_screen", "order": 1, "estimatedTimeSeconds": 30, "content": {"headline": "Expected Value: The Long-Run Average", "body_md": "The expected value (or mean) of a random variable is the long-run average value we would observe if we repeated the random experiment many times. It's a weighted average of all possible values, where the weights are the probabilities.", "visual": {"type": "giphy_search", "value": "expected value probability"}, "interactive_element": {"type": "button", "text": "Let's Explore Expected Value!", "action": "next_screen"}}}, {"id": "ev-screen2-definition", "type": "lesson_screen", "order": 2, "estimatedTimeSeconds": 60, "content": {"headline": "Calculating Expected Value", "body_md": "For a discrete random variable X with PMF p(x), the expected value is:\n\nE[X] = Σ x·p(x)\n\nFor a continuous random variable X with PDF f(x), the expected value is:\n\nE[X] = ∫ x·f(x) dx\n\nIn both cases, we're multiplying each possible value by its probability and summing (or integrating) over all values.", "visual": {"type": "unsplash_search", "value": "expected value probability"}, "interactive_element": {"type": "text_input_quick", "question": "A fair six-sided die is rolled. What is the expected value?", "correct_answer_regex": "^3\\.5$|^7\\/2$", "placeholder": "Enter your answer", "feedback_correct": "Correct! E[X] = 1·(1/6) + 2·(1/6) + 3·(1/6) + 4·(1/6) + 5·(1/6) + 6·(1/6) = 21/6 = 3.5", "feedback_incorrect": "Not quite. Calculate the weighted average: E[X] = 1·(1/6) + 2·(1/6) + 3·(1/6) + 4·(1/6) + 5·(1/6) + 6·(1/6)"}}}, {"id": "ev-screen3-interpretation", "type": "lesson_screen", "order": 3, "estimatedTimeSeconds": 60, "content": {"headline": "Interpreting Expected Value", "body_md": "The expected value has several important interpretations:\n\n- It's the long-run average outcome if the experiment is repeated many times\n- It represents the \"center of mass\" of the probability distribution\n- It's the value that minimizes the expected squared error\n- In gambling, it's the average amount you would win (or lose) per play over many games", "visual": {"type": "giphy_search", "value": "average long run"}, "interactive_element": {"type": "multiple_choice_text", "question": "In a lottery where you have a 1/1,000,000 chance of winning $2,000,000 and the ticket costs $3, what is your expected value per ticket?", "options": [{"id": "a", "text": "$2,000,000", "is_correct": false, "feedback_incorrect": "Incorrect. This is the prize amount, not the expected value."}, {"id": "b", "text": "$-1", "is_correct": true, "feedback_correct": "Correct! E[X] = ($2,000,000)·(1/1,000,000) + ($-3)·(999,999/1,000,000) ≈ $2 - $3 = $-1"}, {"id": "c", "text": "$-3", "is_correct": false, "feedback_incorrect": "Incorrect. This is the cost of the ticket, but you need to account for the possibility of winning."}, {"id": "d", "text": "$2", "is_correct": false, "feedback_incorrect": "Incorrect. This is the expected prize ($2,000,000 × 1/1,000,000), but you need to subtract the ticket cost."}]}}}, {"id": "ev-screen4-properties", "type": "lesson_screen", "order": 4, "estimatedTimeSeconds": 60, "content": {"headline": "Properties of Expected Value", "body_md": "Expected value has several useful properties:\n\n1. **Linearity**: E[aX + b] = a·E[X] + b\n2. **Additivity**: E[X + Y] = E[X] + E[Y]\n3. **Independence**: If X and Y are independent, E[X·Y] = E[X]·E[Y]\n\nThese properties make expected value a powerful tool for analyzing random variables.", "visual": {"type": "unsplash_search", "value": "probability mathematics"}, "interactive_element": {"type": "multiple_choice_text", "question": "If E[X] = 5 and E[Y] = 3, what is E[2X + 4Y - 1]?", "options": [{"id": "a", "text": "8", "is_correct": false, "feedback_incorrect": "Incorrect. Use the linearity property: E[2X + 4Y - 1] = 2·E[X] + 4·E[Y] - 1"}, {"id": "b", "text": "21", "is_correct": true, "feedback_correct": "Correct! E[2X + 4Y - 1] = 2·E[X] + 4·E[Y] - 1 = 2·5 + 4·3 - 1 = 10 + 12 - 1 = 21"}, {"id": "c", "text": "22", "is_correct": false, "feedback_incorrect": "Incorrect. Double-check your calculation: 2·5 + 4·3 - 1 = 10 + 12 - 1 = 21"}, {"id": "d", "text": "15", "is_correct": false, "feedback_incorrect": "Incorrect. Make sure to apply the coefficients: 2·5 + 4·3 - 1 = 10 + 12 - 1 = 21"}]}}}, {"id": "ev-screen5-conclusion", "type": "lesson_screen", "order": 5, "estimatedTimeSeconds": 30, "content": {"headline": "Expected Value: The Center of Probability", "body_md": "Great job! You now understand expected value as a weighted average that gives us the long-run average outcome of a random variable. This concept is fundamental in statistics, finance, decision theory, and many other fields.", "visual": {"type": "unsplash_search", "value": "expected value probability"}, "interactive_element": {"type": "button", "text": "Continue to Next Lesson", "action": "next_lesson"}}}]}, {"id": "visualizing-distributions", "title": "Visualizing Distributions", "description": "Use graphs to represent probability distributions.", "order": 5, "type": "interactive_lesson", "estimatedTimeMinutes": 10, "contentBlocks": [{"id": "vd-screen1-intro", "type": "lesson_screen", "order": 1, "estimatedTimeSeconds": 30, "content": {"headline": "Seeing Probability: Visualizing Distributions", "body_md": "Visualizing probability distributions helps us understand their properties at a glance. Different types of plots reveal different aspects of a distribution, such as its shape, center, spread, and outliers.", "visual": {"type": "giphy_search", "value": "probability distribution visualization"}, "interactive_element": {"type": "button", "text": "Let's Explore Visualizations!", "action": "next_screen"}}}, {"id": "vd-screen2-discrete", "type": "lesson_screen", "order": 2, "estimatedTimeSeconds": 60, "content": {"headline": "Visualizing Discrete Distributions", "body_md": "For discrete random variables, we commonly use:\n\n- **Bar Charts**: Height of each bar represents the probability of that value\n- **Probability Mass Function (PMF) Plots**: Similar to bar charts, but often shown as points connected by lines\n- **Cumulative Distribution Function (CDF) Plots**: Show the probability of X being less than or equal to each value\n\nThese visualizations help us see the shape and key features of the distribution.", "visual": {"type": "giphy_search", "value": "discrete probability distribution"}, "interactive_element": {"type": "multiple_choice_text", "question": "Which visualization would best show the probability of getting at most 3 heads when flipping a coin 5 times?", "options": [{"id": "a", "text": "Bar chart of the PMF", "is_correct": false, "feedback_incorrect": "Incorrect. A bar chart shows individual probabilities, not cumulative probabilities."}, {"id": "b", "text": "Cumulative Distribution Function (CDF) plot", "is_correct": true, "feedback_correct": "Correct! The CDF shows P(X ≤ x) for each value x, so it would directly show the probability of getting at most 3 heads."}, {"id": "c", "text": "Scatter plot of outcomes", "is_correct": false, "feedback_incorrect": "Incorrect. A scatter plot is not typically used for visualizing probability distributions."}, {"id": "d", "text": "Line graph of individual trials", "is_correct": false, "feedback_incorrect": "Incorrect. This would show the results of specific trials, not the theoretical probability distribution."}]}}}, {"id": "vd-screen3-continuous", "type": "lesson_screen", "order": 3, "estimatedTimeSeconds": 60, "content": {"headline": "Visualizing Continuous Distributions", "body_md": "For continuous random variables, we commonly use:\n\n- **Probability Density Function (PDF) Curves**: Show the relative likelihood of different values\n- **Cumulative Distribution Function (CDF) Curves**: Show the probability of X being less than or equal to each value\n- **Histograms**: For visualizing actual data that follows a continuous distribution\n- **Box Plots**: Show the median, quartiles, and potential outliers\n\nThese visualizations help us understand the shape, center, and spread of the distribution.", "visual": {"type": "giphy_search", "value": "normal distribution curve"}, "interactive_element": {"type": "multiple_choice_text", "question": "What does the area under a PDF curve between x = 2 and x = 5 represent?", "options": [{"id": "a", "text": "The expected value of X", "is_correct": false, "feedback_incorrect": "Incorrect. The expected value is the weighted average of all possible values, not the area between specific points."}, {"id": "b", "text": "The probability that X equals 3.5", "is_correct": false, "feedback_incorrect": "Incorrect. For a continuous random variable, the probability of any specific point is zero."}, {"id": "c", "text": "The probability that X is between 2 and 5", "is_correct": true, "feedback_correct": "Correct! The area under a PDF curve between two points represents the probability that the random variable falls within that interval."}, {"id": "d", "text": "The variance of the distribution", "is_correct": false, "feedback_incorrect": "Incorrect. The variance measures the spread of the distribution and is not represented by the area between specific points."}]}}}, {"id": "vd-screen4-interpreting", "type": "lesson_screen", "order": 4, "estimatedTimeSeconds": 60, "content": {"headline": "Interpreting Distribution Shapes", "body_md": "The shape of a probability distribution tells us important information:\n\n- **Symmetry**: Is the distribution balanced around its center?\n- **Skewness**: Does it have a longer tail on one side?\n- **Modality**: Does it have one peak (unimodal), two peaks (bimodal), or more?\n- **Tails**: Are the tails heavy (many extreme values) or light?\n\nFor example, a normal distribution is symmetric and unimodal, while an exponential distribution is skewed to the right.", "visual": {"type": "unsplash_search", "value": "probability distribution shapes"}, "interactive_element": {"type": "multiple_choice_text", "question": "Which of these distributions is likely to be right-skewed (has a longer tail on the right side)?", "options": [{"id": "a", "text": "Heights of adult humans", "is_correct": false, "feedback_incorrect": "Incorrect. Heights of adults typically follow a normal distribution, which is symmetric."}, {"id": "b", "text": "<PERSON><PERSON> scores in a very easy test", "is_correct": false, "feedback_incorrect": "Incorrect. Scores on a very easy test would likely be left-skewed, with most scores clustered near the maximum."}, {"id": "c", "text": "Annual incomes in a population", "is_correct": true, "feedback_correct": "Correct! Income distributions are typically right-skewed, with many people having moderate incomes and a few having very high incomes."}, {"id": "d", "text": "Measurement errors in a scientific experiment", "is_correct": false, "feedback_incorrect": "Incorrect. Measurement errors typically follow a normal distribution, which is symmetric."}]}}}, {"id": "vd-screen5-conclusion", "type": "lesson_screen", "order": 5, "estimatedTimeSeconds": 30, "content": {"headline": "Visualizing Distributions: Seeing Probability", "body_md": "Excellent! You now understand how to visualize and interpret probability distributions. These visual tools help us grasp the key features of a distribution and communicate probability concepts effectively.", "visual": {"type": "unsplash_search", "value": "data visualization probability"}, "interactive_element": {"type": "button", "text": "Continue to Module Test", "action": "next_lesson"}}}]}], "endOfModuleAssessment": {"id": "connecting-functions-to-probability-distributions-test", "title": "Functions and Probability Distributions", "description": "Test your understanding of probability distributions expressed as functions and expected values.", "type": "module_test_interactive", "estimatedTimeMinutes": 15, "passingScorePercentage": 70, "contentBlocks": [{"id": "cfpd-test-intro", "type": "test_screen_intro", "order": 1, "estimatedTimeSeconds": 30, "content": {"headline": "Functions and Probability Distributions: Test Your Knowledge", "body_md": "Let's see how well you understand probability distributions expressed as functions and expected values!", "visual": {"type": "unsplash_search", "value": "mathematics test"}, "interactive_element": {"type": "button", "text": "Begin Test", "action": "next_screen"}}}, {"id": "cfpd-test-q1", "type": "test_screen_question", "order": 2, "estimatedTimeSeconds": 60, "content": {"headline": "Question 1: Random Variables", "body_md": "Which of the following is a continuous random variable?", "interactive_element": {"type": "multiple_choice_text", "options": [{"id": "a", "text": "Number of customers who enter a store in a day", "is_correct": false, "feedback_incorrect": "Incorrect. This is a discrete random variable because it can only take whole number values."}, {"id": "b", "text": "Number of heads when flipping 10 coins", "is_correct": false, "feedback_incorrect": "Incorrect. This is a discrete random variable because it can only take values from 0 to 10."}, {"id": "c", "text": "Weight of a randomly selected person", "is_correct": true, "feedback_correct": "Correct! Weight can take any value within a range, making it a continuous random variable."}, {"id": "d", "text": "Number of dots showing on a rolled die", "is_correct": false, "feedback_incorrect": "Incorrect. This is a discrete random variable because it can only take values 1, 2, 3, 4, 5, or 6."}]}}}, {"id": "cfpd-test-q2", "type": "test_screen_question", "order": 3, "estimatedTimeSeconds": 60, "content": {"headline": "Question 2: Probability Mass Function", "body_md": "A discrete random variable X has the following PMF: p(1) = 0.2, p(2) = 0.3, p(3) = 0.4, p(4) = 0.1. What is P(X ≤ 2)?", "interactive_element": {"type": "text_input_quick", "correct_answer_regex": "^0\\.5$|^1\\/2$|^0\\.50$", "placeholder": "Enter your answer as a decimal or fraction", "feedback_correct": "Correct! P(X ≤ 2) = P(X = 1) + P(X = 2) = 0.2 + 0.3 = 0.5", "feedback_incorrect": "Incorrect. Calculate P(X ≤ 2) = P(X = 1) + P(X = 2) = 0.2 + 0.3 = 0.5"}}}, {"id": "cfpd-test-q3", "type": "test_screen_question", "order": 4, "estimatedTimeSeconds": 60, "content": {"headline": "Question 3: Probability Density Function", "body_md": "For a continuous random variable with PDF f(x), which of the following statements is true?", "interactive_element": {"type": "multiple_choice_text", "options": [{"id": "a", "text": "f(x) represents the probability that X = x", "is_correct": false, "feedback_incorrect": "Incorrect. For a continuous random variable, P(X = x) = 0 for any specific point x."}, {"id": "b", "text": "The value of f(x) must be between 0 and 1", "is_correct": false, "feedback_incorrect": "Incorrect. While f(x) must be non-negative, it can exceed 1 as long as the total area under the curve equals 1."}, {"id": "c", "text": "The area under the curve f(x) from a to b equals P(a ≤ X ≤ b)", "is_correct": true, "feedback_correct": "Correct! The area under the PDF curve between two points represents the probability that the random variable falls within that interval."}, {"id": "d", "text": "The integral of f(x) from -∞ to ∞ equals the expected value of X", "is_correct": false, "feedback_incorrect": "Incorrect. The integral of f(x) from -∞ to ∞ equals 1, not the expected value."}]}}}, {"id": "cfpd-test-q4", "type": "test_screen_question", "order": 5, "estimatedTimeSeconds": 60, "content": {"headline": "Question 4: Expected Value", "body_md": "A discrete random variable X has the following PMF: p(1) = 0.2, p(2) = 0.3, p(3) = 0.4, p(4) = 0.1. What is the expected value of X?", "interactive_element": {"type": "text_input_quick", "correct_answer_regex": "^2\\.4$", "placeholder": "Enter your answer", "feedback_correct": "Correct! E[X] = 1(0.2) + 2(0.3) + 3(0.4) + 4(0.1) = 0.2 + 0.6 + 1.2 + 0.4 = 2.4", "feedback_incorrect": "Incorrect. Calculate E[X] = 1(0.2) + 2(0.3) + 3(0.4) + 4(0.1) = 0.2 + 0.6 + 1.2 + 0.4 = 2.4"}}}, {"id": "cfpd-test-q5", "type": "test_screen_question", "order": 6, "estimatedTimeSeconds": 60, "content": {"headline": "Question 5: Distribution Visualization", "body_md": "Which of the following distributions would likely have a bell-shaped, symmetric curve when visualized?", "interactive_element": {"type": "multiple_choice_text", "options": [{"id": "a", "text": "Time until the next customer arrives at a store", "is_correct": false, "feedback_incorrect": "Incorrect. This would likely follow an exponential distribution, which is right-skewed, not symmetric."}, {"id": "b", "text": "Annual incomes in a population", "is_correct": false, "feedback_incorrect": "Incorrect. Income distributions are typically right-skewed, not symmetric."}, {"id": "c", "text": "IQ scores in a large population", "is_correct": true, "feedback_correct": "Correct! IQ scores are designed to follow a normal distribution, which has a bell-shaped, symmetric curve."}, {"id": "d", "text": "Number of accidents per day at a busy intersection", "is_correct": false, "feedback_incorrect": "Incorrect. This would likely follow a Poisson distribution, which is discrete and often right-skewed for small means."}]}}}, {"id": "cfpd-test-conclusion", "type": "test_screen_conclusion", "order": 7, "estimatedTimeSeconds": 30, "content": {"headline": "Functions and Probability Distributions: Test Complete", "body_md": "Great job completing the test! You've demonstrated your understanding of probability distributions expressed as functions and expected values.", "visual": {"type": "unsplash_search", "value": "mathematics success"}, "interactive_element": {"type": "button", "text": "Return to Module", "action": "return_to_module"}}}]}}