{"id": "basic-circuit-analysis-tools", "title": "Basic Circuit Analysis Tools and Techniques", "description": "Introduce common tools and methods used in electronics for analysis and measurement.", "order": 4, "lessons": [{"id": "multimeters", "title": "Multimeters: Measuring Voltage, Current, and Resistance", "description": "Learn how to use a multimeter.", "order": 1, "type": "interactive_lesson", "estimatedTimeMinutes": 20, "contentBlocks": [{"id": "multimeters-screen-1", "type": "lesson_screen", "order": 1, "estimatedTimeSeconds": 60, "content": {"headline": "The Electronics Swiss Army Knife!", "body_md": "A **multimeter** is an essential tool for anyone working with electronics. It's like a Swiss Army knife, capable of measuring several electrical properties: primarily **Voltage (V)**, **Current (A)**, and **Resistance (Ω)**.", "visual": {"type": "unsplash_search", "value": "digital multimeter"}, "interactive_element": {"type": "button", "button_text": "How do I use it?", "action": "next_screen"}}}, {"id": "multimeters-screen-2", "type": "lesson_screen", "order": 2, "estimatedTimeSeconds": 90, "content": {"headline": "Setting Up: Di<PERSON> and <PERSON>bes", "body_md": "Most multimeters have:\n\n- A **rotary dial** to select the quantity to measure (Voltage, Current, Resistance) and the range (e.g., mV, V, kΩ, MΩ).\n- **Ports** to plug in test probes (typically a black COMmon probe and a red VΩmA probe).\n\n**Safety First!** Always start with the highest range if unsure, and never measure resistance or continuity on a powered circuit.", "visual": {"type": "local_asset", "value": "assets/images/course_specific/multimeter_dial_probes.png"}, "interactive_element": {"type": "button", "button_text": "Let's measure Voltage!", "action": "next_screen"}}}, {"id": "multimeters-screen-3", "type": "lesson_screen", "order": 3, "estimatedTimeSeconds": 120, "content": {"headline": "Measuring Voltage (Voltmeter)", "body_md": "To measure voltage:\n\n1. Set the dial to DC Voltage (V⎓ or VDC) or AC Voltage (V~ or VAC).\n2. Connect the probes **in parallel** across the component or points where you want to measure the voltage difference.\n   - Red probe to the more positive point, Black probe to the more negative point (for DC).\n\nThink of it as 'tapping into' the circuit to see the potential difference.", "visual": {"type": "local_asset", "value": "assets/images/course_specific/multimeter_voltage_measurement.gif"}, "interactive_element": {"type": "multiple_choice_text", "question_text": "How should a voltmeter be connected to measure voltage across a component?", "options": [{"id": "opt1", "text": "In series with the component"}, {"id": "opt2", "text": "In parallel with the component"}, {"id": "opt3", "text": "With the circuit powered off"}], "correct_option_id": "opt2", "feedback_correct": "Correct! Voltage is measured *across* a component, so in parallel.", "feedback_incorrect": "Voltage is a difference *between two points*. How would you connect to see that?"}}}, {"id": "multimeters-screen-4", "type": "lesson_screen", "order": 4, "estimatedTimeSeconds": 120, "content": {"headline": "Measuring Current (Ammeter)", "body_md": "To measure current:\n\n1. Set the dial to DC Current (A⎓ or ADC) or AC Current (A~ or AAC). (May need to move red probe to a dedicated 'A' or 'mA' port for higher currents).\n2. **Break the circuit** where you want to measure current.\n3. Connect the multimeter **in series**, so current flows *through* the meter.\n   - Current enters the red probe and exits the black probe.\n\n**Caution:** Incorrectly connecting an ammeter (e.g., in parallel like a voltmeter) can blow a fuse in the meter or damage it!", "visual": {"type": "local_asset", "value": "assets/images/course_specific/multimeter_current_measurement.gif"}, "interactive_element": {"type": "multiple_choice_text", "question_text": "Why must an ammeter be connected in series?", "options": [{"id": "opt1", "text": "To measure voltage drop"}, {"id": "opt2", "text": "So all the current being measured flows through it"}, {"id": "opt3", "text": "To avoid blowing its fuse"}], "correct_option_id": "opt2", "feedback_correct": "Exactly! Current is the flow of charge, so the meter needs to be part of the path.", "feedback_incorrect": "Think about what current is. The meter needs to 'count' the charges passing through."}}}, {"id": "multimeters-screen-5", "type": "lesson_screen", "order": 5, "estimatedTimeSeconds": 100, "content": {"headline": "Measuring Resistance (Ohmmeter)", "body_md": "To measure resistance:\n\n1. **IMPORTANT: Ensure the component is NOT powered and is isolated from the circuit (ideally remove it).**\n2. Set the dial to Resistance (Ω).\n3. Connect the probes across the component.\n\nThe multimeter sends a small current through the component and measures the resulting voltage drop to calculate resistance.", "visual": {"type": "local_asset", "value": "assets/images/course_specific/multimeter_resistance_measurement.gif"}, "interactive_element": {"type": "multiple_choice_text", "question_text": "Why must a circuit be unpowered when measuring resistance?", "options": [{"id": "opt1", "text": "To get a more accurate reading"}, {"id": "opt2", "text": "To prevent damage to the multimeter and avoid incorrect readings"}, {"id": "opt3", "text": "It doesn't matter if it's powered"}], "correct_option_id": "opt2", "feedback_correct": "Correct! External voltage will interfere with the meter's own current source and can damage it.", "feedback_incorrect": "The ohmmeter uses its own internal battery. External power will cause problems."}}}, {"id": "multimeters-screen-6", "type": "lesson_screen", "order": 6, "estimatedTimeSeconds": 75, "content": {"headline": "Continuity Test", "body_md": "Most multimeters have a **continuity test** mode (often shares a setting with resistance, looks like a sound wave or diode symbol).\n\n- It checks for a low-resistance path (a short circuit or good connection).\n- If continuity exists, the meter usually **beeps**.\n- Great for checking wires, fuses, and traces on a PCB.", "visual": {"type": "giphy_search", "value": "sound wave beep"}, "interactive_element": {"type": "button", "button_text": "Let's recap the multimeter!", "action": "next_screen"}}}, {"id": "multimeters-screen-7", "type": "lesson_screen", "order": 7, "estimatedTimeSeconds": 60, "content": {"headline": "Multimeter Mastered (Basics)!", "body_md": "You've got the multimeter basics!\n\n- Measures Voltage (parallel), Current (series), Resistance (unpowered).\n- Select correct function and range.\n- Continuity test checks for connections.\n- **Always prioritize safety!**", "visual": {"type": "giphy_search", "value": "tool success checkmark"}, "interactive_element": {"type": "button", "button_text": "Finish Lesson", "action": "complete_lesson"}}}]}, {"id": "oscilloscopes-intro", "title": "Oscilloscopes (Introduction)", "description": "Visualize time-varying signals.", "order": 2, "type": "interactive_lesson", "estimatedTimeMinutes": 25, "contentBlocks": [{"id": "oscilloscopes-screen-1", "type": "lesson_screen", "order": 1, "estimatedTimeSeconds": 60, "content": {"headline": "Seeing Signals: The Oscilloscope!", "body_md": "While a multimeter gives you numbers, an **oscilloscope** (often called a 'scope') lets you *see* electrical signals as waveforms on a screen. It's like a TV for electricity, showing voltage changes over time.", "visual": {"type": "unsplash_search", "value": "oscilloscope screen waveform"}, "interactive_element": {"type": "button", "button_text": "What does it show?", "action": "next_screen"}}}, {"id": "oscilloscopes-screen-2", "type": "lesson_screen", "order": 2, "estimatedTimeSeconds": 120, "content": {"headline": "The Display: Voltage vs. Time", "body_md": "The oscilloscope display is a graph:\n\n- **Vertical axis (Y-axis):** Represents Voltage.\n- **Horizontal axis (X-axis):** Represents Time.\n\nThis allows you to see the shape, amplitude, frequency, and other characteristics of a signal as it changes.", "visual": {"type": "local_asset", "value": "assets/images/course_specific/oscilloscope_display_axes.png"}, "interactive_element": {"type": "multiple_choice_text", "question_text": "On an oscilloscope, what does the Y-axis typically represent?", "options": [{"id": "opt1", "text": "Time"}, {"id": "opt2", "text": "Current"}, {"id": "opt3", "text": "Voltage"}, {"id": "opt4", "text": "Frequency"}], "correct_option_id": "opt3", "feedback_correct": "Correct! The vertical axis shows the voltage of the signal.", "feedback_incorrect": "Think about what the scope is primarily designed to visualize against time."}}}, {"id": "oscilloscopes-screen-3", "type": "lesson_screen", "order": 3, "estimatedTimeSeconds": 100, "content": {"headline": "Key Controls (Conceptual)", "body_md": "Oscilloscopes have several important controls:\n\n- **Volts/Division (Y-axis):** Sets how many volts each vertical grid square represents.\n- **Time/Division (X-axis):** Sets how much time each horizontal grid square represents.\n- **Trigger:** Stabilizes a repeating waveform on the screen by starting the sweep at the same point in each cycle.", "visual": {"type": "unsplash_search", "value": "oscilloscope knobs controls"}, "interactive_element": {"type": "button", "button_text": "How do we connect it?", "action": "next_screen"}}}, {"id": "oscilloscopes-screen-4", "type": "lesson_screen", "order": 4, "estimatedTimeSeconds": 90, "content": {"headline": "Probing the Circuit", "body_md": "Oscilloscopes use special **probes** to connect to the circuit. Like a voltmeter, the scope probe is typically connected **in parallel** across the points where you want to observe the signal.\n\nThe probe has a sharp tip for making contact and a ground clip that must be connected to the circuit's ground reference.", "visual": {"type": "local_asset", "value": "assets/images/course_specific/oscilloscope_probe_connection.gif"}, "interactive_element": {"type": "multiple_choice_text", "question_text": "Where should the oscilloscope probe's ground clip be connected?", "options": [{"id": "opt1", "text": "To the positive voltage source"}, {"id": "opt2", "text": "To the circuit's ground reference"}, {"id": "opt3", "text": "To the probe tip"}, {"id": "opt4", "text": "It's optional"}], "correct_option_id": "opt2", "feedback_correct": "Correct! A proper ground reference is essential for accurate measurements.", "feedback_incorrect": "The ground clip provides the reference point for the voltage measurement."}}}, {"id": "oscilloscopes-screen-5", "type": "lesson_screen", "order": 5, "estimatedTimeSeconds": 90, "content": {"headline": "What Can You See?", "body_md": "With an oscilloscope, you can:\n\n- View AC waveforms (sine, square, triangle, etc.).\n- Measure peak voltage, peak-to-peak voltage, and frequency.\n- See DC levels.\n- Observe noise or distortion in a signal.\n- Compare the phase of two different signals (with multi-channel scopes).", "visual": {"type": "giphy_search", "value": "analyzing data screen"}, "interactive_element": {"type": "button", "button_text": "Let's wrap this up!", "action": "next_screen"}}}, {"id": "oscilloscopes-screen-6", "type": "lesson_screen", "order": 6, "estimatedTimeSeconds": 75, "content": {"headline": "Oscilloscope Intro: Complete!", "body_md": "You've scoped out the basics!\n\n- Oscilloscopes display voltage vs. time.\n- Key controls: Volts/Div, Time/Div, Trigger.\n- Probes connect in parallel (with ground).\n- Essential for visualizing and analyzing dynamic signals.\n\nThey are powerful tools for any electronics enthusiast or professional!", "visual": {"type": "giphy_search", "value": "science scope success"}, "interactive_element": {"type": "button", "button_text": "Finish Lesson", "action": "complete_lesson"}}}]}, {"id": "circuit-simulation-software", "title": "Circuit Simulation Software (Conceptual)", "description": "Understand the role of simulation in design.", "order": 3, "type": "interactive_lesson", "estimatedTimeMinutes": 15, "contentBlocks": [{"id": "simulation-screen-1", "type": "lesson_screen", "order": 1, "estimatedTimeSeconds": 60, "content": {"headline": "Virtual Test Bench: Simulators!", "body_md": "Building and testing physical circuits takes time and components. **Circuit simulation software** lets you design and test circuits virtually on a computer before ever touching a real part!\n\nThink of it as a flight simulator for electronics.", "visual": {"type": "unsplash_search", "value": "computer circuit design software"}, "interactive_element": {"type": "button", "button_text": "What can they do?", "action": "next_screen"}}}, {"id": "simulation-screen-2", "type": "lesson_screen", "order": 2, "estimatedTimeSeconds": 90, "content": {"headline": "Benefits of Simulation", "body_md": "Simulators offer many advantages:\n\n- **Test ideas quickly:** No need to solder or wire.\n- **Safe experimentation:** Blow up virtual components without cost or danger!\n- **Ideal components:** Explore theoretical behavior without real-world imperfections.\n- **Advanced analysis:** Perform complex measurements and analyses that are difficult or impossible on a real bench (e.g., frequency sweeps, Monte Carlo analysis).", "visual": {"type": "giphy_search", "value": "virtual reality design"}, "interactive_element": {"type": "multiple_choice_text", "question_text": "Which is a key benefit of using circuit simulation software?", "options": [{"id": "opt1", "text": "It replaces all need for physical prototyping."}, {"id": "opt2", "text": "It allows for safe testing of potentially destructive scenarios."}, {"id": "opt3", "text": "It only works for DC circuits."}], "correct_option_id": "opt2", "feedback_correct": "Correct! You can push virtual circuits to their limits without real-world consequences.", "feedback_incorrect": "While powerful, simulation doesn't replace all physical testing. It's a complementary tool."}}}, {"id": "simulation-screen-3", "type": "lesson_screen", "order": 3, "estimatedTimeSeconds": 75, "content": {"headline": "Popular Simulators (Examples)", "body_md": "There are many circuit simulators available, from free to professional-grade:\n\n- **LTspice:** Very popular, free, and powerful (especially for analog).\n- **Multisim:** Widely used in education and industry.\n- **TINA-TI:** Texas Instruments' free simulator.\n- **EveryCircuit (App):** A great mobile option for simpler circuits.\n\n(This is just a conceptual introduction; we won't be using specific software in this course.)", "visual": {"type": "unsplash_search", "value": "software logos collage"}, "interactive_element": {"type": "button", "button_text": "Got the concept!", "action": "next_screen"}}}, {"id": "simulation-screen-4", "type": "lesson_screen", "order": 4, "estimatedTimeSeconds": 60, "content": {"headline": "Simulation: A Powerful Ally", "body_md": "You've grasped the idea!\n\n- Circuit simulators let you design and test virtually.\n- They save time, money, and allow for safe, complex analysis.\n- Many types exist, from free to professional.\n\nSimulation is a valuable step in the modern electronics design process.", "visual": {"type": "giphy_search", "value": "computer design success"}, "interactive_element": {"type": "button", "button_text": "Finish Lesson", "action": "complete_lesson"}}}]}, {"id": "breadboarding", "title": "Breadboarding: Prototyping Circuits", "description": "Learn to build temporary circuits for testing.", "order": 4, "type": "interactive_lesson", "estimatedTimeMinutes": 25, "contentBlocks": [{"id": "breadboarding-screen-1", "type": "lesson_screen", "order": 1, "estimatedTimeSeconds": 60, "content": {"headline": "Hands-On: The Breadboard!", "body_md": "A **breadboard** (or protoboard) is a reusable, solderless device for building temporary electronic circuit prototypes. It's perfect for testing ideas quickly without permanent connections.", "visual": {"type": "unsplash_search", "value": "electronic breadboard with components"}, "interactive_element": {"type": "button", "button_text": "How are the holes connected?", "action": "next_screen"}}}, {"id": "breadboarding-screen-2", "type": "lesson_screen", "order": 2, "estimatedTimeSeconds": 120, "content": {"headline": "Inside the Breadboard", "body_md": "Breadboards have rows and columns of holes. Underneath, metal clips connect these holes in specific patterns:\n\n- **Terminal Strips:** The main area has short vertical strips of 5 connected holes (e.g., column 'a' holes 1-5 are connected, 'b' 1-5 are connected, but 'a1' is NOT connected to 'b1').\n- **Bus Strips (Power Rails):** Usually run horizontally along the sides. All holes in a power rail (often marked + and -) are connected. Used for VCC (power) and GND (ground).", "visual": {"type": "local_asset", "value": "assets/images/course_specific/breadboard_internal_connections.png"}, "interactive_element": {"type": "multiple_choice_text", "question_text": "On a typical breadboard, how are the holes in the main terminal strips usually connected?", "options": [{"id": "opt1", "text": "All holes in a row are connected"}, {"id": "opt2", "text": "All holes in a column are connected"}, {"id": "opt3", "text": "In short vertical strips of 5 holes"}, {"id": "opt4", "text": "Diagonally"}], "correct_option_id": "opt3", "feedback_correct": "Correct! The main area has short, isolated vertical connections.", "feedback_incorrect": "The main terminal strips have short vertical connections. Power rails run horizontally."}}}, {"id": "breadboarding-screen-3", "type": "lesson_screen", "order": 3, "estimatedTimeSeconds": 90, "content": {"headline": "Placing Components", "body_md": "To connect components:\n\n- Insert component leads (wires) into holes that share a common metal strip.\n- Integrated Circuits (ICs / chips) are typically placed straddling the central channel/divider.\n- Use jumper wires to make connections between different strips or to power rails.", "visual": {"type": "giphy_search", "value": "connecting wires electronics"}, "interactive_element": {"type": "button", "button_text": "Show me an example circuit!", "action": "next_screen"}}}, {"id": "breadboarding-screen-4", "type": "lesson_screen", "order": 4, "estimatedTimeSeconds": 120, "content": {"headline": "Simple LED Circuit on a Breadboard", "body_md": "Let's build a simple circuit: an LED, a current-limiting resistor, and a power source.\n\n1. Connect power (+) and ground (-) to the breadboard's power rails.\n2. Place the resistor. One end to a terminal strip.\n3. Place the LED. Anode (longer leg) to the same strip as the resistor's other end. Cathode (shorter leg) to another strip.\n4. Jumper wire from power rail (+) to the resistor's first end.\n5. Jumper wire from LED's cathode strip to ground rail (-).", "visual": {"type": "local_asset", "value": "assets/images/course_specific/breadboard_led_circuit.gif"}, "interactive_element": {"type": "multiple_choice_text", "question_text": "Why is a current-limiting resistor important for an LED?", "options": [{"id": "opt1", "text": "To make it brighter"}, {"id": "opt2", "text": "To prevent too much current from damaging the LED"}, {"id": "opt3", "text": "To change its color"}, {"id": "opt4", "text": "It's not always needed"}], "correct_option_id": "opt2", "feedback_correct": "Correct! LEDs can be easily damaged by excessive current.", "feedback_incorrect": "LEDs are sensitive. What happens if too much current flows through them?"}}}, {"id": "breadboarding-screen-5", "type": "lesson_screen", "order": 5, "estimatedTimeSeconds": 90, "content": {"headline": "Tips for Good Breadboarding", "body_md": "- Keep wires short and neat.\n- Use color-coded wires (e.g., red for VCC, black for GND).\n- Double-check connections before applying power.\n- Don't force components into holes.\n- Ensure ICs are oriented correctly (notch or dot indicates pin 1).", "visual": {"type": "unsplash_search", "value": "neatly organized wires"}, "interactive_element": {"type": "button", "button_text": "Ready to summarize!", "action": "next_screen"}}}, {"id": "breadboarding-screen-6", "type": "lesson_screen", "order": 6, "estimatedTimeSeconds": 75, "content": {"headline": "Breadboarding Basics: Done!", "body_md": "You're now ready to prototype!\n\n- Breadboards allow solderless circuit building.\n- Understand terminal strips and power rails.\n- Place components and use jumper wires carefully.\n- Great for testing and iterating designs quickly.", "visual": {"type": "giphy_search", "value": "building blocks success"}, "interactive_element": {"type": "button", "button_text": "Finish Lesson", "action": "complete_lesson"}}}]}, {"id": "troubleshooting-basic-circuits", "title": "Troubleshooting Basic Circuits", "description": "Develop skills in identifying and fixing common problems.", "order": 5, "type": "interactive_lesson", "estimatedTimeMinutes": 20, "contentBlocks": [{"id": "troubleshooting-screen-1", "type": "lesson_screen", "order": 1, "estimatedTimeSeconds": 60, "content": {"headline": "When Circuits Go Wrong: Troubleshooting!", "body_md": "Even the simplest circuits can have problems. **Troubleshooting** is the systematic process of identifying and fixing issues in electronic circuits.\n\nIt's a critical skill for any electronics enthusiast or professional, and often feels like detective work!", "visual": {"type": "giphy_search", "value": "detective magnifying glass"}, "interactive_element": {"type": "button", "button_text": "The Troubleshooting Mindset", "action": "next_screen"}}}, {"id": "troubleshooting-screen-2", "type": "lesson_screen", "order": 2, "estimatedTimeSeconds": 90, "content": {"headline": "The Troubleshooting Approach", "body_md": "Effective troubleshooting follows a methodical process:\n\n1. **Understand the circuit**: Know what it's supposed to do\n2. **Observe symptoms**: What's actually happening?\n3. **Form hypotheses**: What might cause these symptoms?\n4. **Test systematically**: Check each possibility\n5. **Fix and verify**: Make repairs and confirm they worked\n\nAvoid random guessing or changing multiple things at once!", "visual": {"type": "unsplash_search", "value": "problem solving steps"}, "interactive_element": {"type": "multiple_choice_text", "question": "What's the first step in effective circuit troubleshooting?", "options": [{"id": "opt1", "text": "Replace components until it works", "is_correct": false, "feedback": "Random replacement is inefficient and may introduce new problems."}, {"id": "opt2", "text": "Understand what the circuit is supposed to do", "is_correct": true, "feedback": "Correct! You need to know the expected behavior before you can identify what's wrong."}, {"id": "opt3", "text": "Apply power and see what happens", "is_correct": false, "feedback": "Applying power to a faulty circuit without understanding could cause damage."}, {"id": "opt4", "text": "Check every component with a multimeter", "is_correct": false, "feedback": "While testing is important, blindly checking everything without a plan is inefficient."}]}}}, {"id": "troubleshooting-screen-3", "type": "lesson_screen", "order": 3, "estimatedTimeSeconds": 90, "content": {"headline": "Common Circuit Problems", "body_md": "Most circuit issues fall into a few categories:\n\n- **Open circuits**: Breaks in the current path (disconnected wires, broken components)\n- **Short circuits**: Unintended low-resistance paths (touching wires, solder bridges)\n- **Component failures**: Damaged or defective parts\n- **Power issues**: Incorrect voltage, polarity, or insufficient current\n- **Design errors**: Incorrect component values or connections\n- **Intermittent problems**: Issues that come and go (loose connections, temperature effects)", "visual": {"type": "giphy_search", "value": "broken circuit animation"}, "interactive_element": {"type": "button", "button_text": "Troubleshooting <PERSON><PERSON>", "action": "next_screen"}}}, {"id": "troubleshooting-screen-4", "type": "lesson_screen", "order": 4, "estimatedTimeSeconds": 120, "content": {"headline": "Essential Troubleshooting Tools", "body_md": "Your key tools for troubleshooting include:\n\n- **Multimeter**: Measure voltage, current, resistance, continuity\n- **Power supply**: Provide controlled voltage/current\n- **Oscilloscope**: Visualize signals and detect anomalies\n- **Logic probe**: For digital circuits\n- **Magnifier**: Inspect for physical damage or solder issues\n- **Documentation**: Schematics, datasheets, expected values\n\nThe multimeter is your most versatile and essential troubleshooting tool.", "visual": {"type": "unsplash_search", "value": "electronics tools workbench"}, "interactive_element": {"type": "multiple_choice_text", "question": "Which tool would be MOST useful for checking if a wire is broken inside its insulation?", "options": [{"id": "opt1", "text": "Oscilloscope", "is_correct": false, "feedback": "An oscilloscope is better for analyzing signal waveforms, not checking for continuity."}, {"id": "opt2", "text": "Multimeter in continuity mode", "is_correct": true, "feedback": "Correct! The continuity test on a multimeter will quickly tell you if there's a complete path through the wire."}, {"id": "opt3", "text": "Logic probe", "is_correct": false, "feedback": "Logic probes are for testing digital logic states, not checking wire continuity."}, {"id": "opt4", "text": "Power supply", "is_correct": false, "feedback": "A power supply provides power but doesn't directly test for breaks in wires."}]}}}, {"id": "troubleshooting-screen-5", "type": "lesson_screen", "order": 5, "estimatedTimeSeconds": 120, "content": {"headline": "Voltage Measurements: Following the Path", "body_md": "Voltage measurements are often your first diagnostic step:\n\n1. **Check power supply**: Verify correct voltage at the source\n2. **Follow the path**: Measure voltage at key points along the circuit\n3. **Look for drops**: Unexpected voltage drops indicate problems\n4. **Check ground**: Ensure proper ground connections\n\nRemember: Voltage is always measured *relative* to a reference point (usually ground) and *across* components.", "visual": {"type": "giphy_search", "value": "voltage measurement circuit"}, "interactive_element": {"type": "multiple_choice_text", "question": "If you measure 0V across a resistor that should have voltage, what might this indicate?", "options": [{"id": "opt1", "text": "The resistor is functioning normally", "is_correct": false, "feedback": "A resistor in a powered circuit should have a voltage drop if current is flowing through it."}, {"id": "opt2", "text": "The resistor is shorted (resistance too low)", "is_correct": false, "feedback": "A shorted resistor would have very low resistance and thus a very small voltage drop, but not necessarily 0V."}, {"id": "opt3", "text": "There's no current flowing through that part of the circuit", "is_correct": true, "feedback": "Correct! 0V across a resistor typically means no current is flowing through it, suggesting an open circuit somewhere in that path."}, {"id": "opt4", "text": "Your multimeter is broken", "is_correct": false, "feedback": "While possible, it's more likely a circuit issue than a meter problem, especially if the meter works elsewhere."}]}}}, {"id": "troubleshooting-screen-6", "type": "lesson_screen", "order": 6, "estimatedTimeSeconds": 90, "content": {"headline": "Signal Tracing: Following the Flow", "body_md": "For more complex circuits, **signal tracing** helps locate problems:\n\n1. Start at the input and follow the signal path\n2. Check each stage for expected behavior\n3. The problem is likely between the last good point and first bad point\n\nThis is especially useful for audio circuits, radio receivers, and multi-stage systems.", "visual": {"type": "unsplash_search", "value": "signal path flow"}, "interactive_element": {"type": "button", "button_text": "Common Mistakes", "action": "next_screen"}}}, {"id": "troubleshooting-screen-7", "type": "lesson_screen", "order": 7, "estimatedTimeSeconds": 90, "content": {"headline": "Common Troubleshooting Mistakes", "body_md": "Avoid these common pitfalls:\n\n- **Overlooking the obvious**: Check power, connections, and switches first\n- **Assuming only one problem**: Sometimes multiple issues exist\n- **Forgetting to document**: Note what you've tested and changed\n- **Skipping verification**: Always confirm your fix worked\n- **Ignoring safety**: Never troubleshoot high-voltage circuits when powered\n- **Changing too much**: Modify one thing at a time\n- **Misusing tools**: Incorrect meter settings can give misleading results", "visual": {"type": "giphy_search", "value": "facepalm mistake"}, "interactive_element": {"type": "multiple_choice_text", "question": "Why should you change only one thing at a time when troubleshooting?", "options": [{"id": "opt1", "text": "To save components", "is_correct": false, "feedback": "While this might be a side benefit, it's not the main reason."}, {"id": "opt2", "text": "To know exactly what fixed the problem", "is_correct": true, "feedback": "Correct! If you change multiple things and the circuit works, you won't know which change actually fixed the issue."}, {"id": "opt3", "text": "Because it's faster", "is_correct": false, "feedback": "Changing multiple things might seem faster initially but often leads to confusion and wasted time later."}, {"id": "opt4", "text": "To avoid damaging test equipment", "is_correct": false, "feedback": "This isn't directly related to making one change at a time."}]}}}, {"id": "troubleshooting-screen-8", "type": "lesson_screen", "order": 8, "estimatedTimeSeconds": 60, "content": {"headline": "Troubleshooting: A Valuable Skill!", "body_md": "You've learned the basics of circuit troubleshooting:\n\n- Follow a systematic approach\n- Understand common problems\n- Use the right tools for the job\n- Measure voltages and trace signals\n- Avoid common mistakes\n\nRemember: Troubleshooting is both a science and an art. It improves with practice and patience!", "visual": {"type": "giphy_search", "value": "success fix repair"}, "interactive_element": {"type": "button", "button_text": "Finish Lesson", "action": "complete_lesson"}}}]}, {"id": "module-test-electronics-technician", "title": "Module Test: Electronics Technician", "description": "Understand the use of basic electronic measurement tools and circuit prototyping.", "order": 6, "type": "module_test_interactive", "estimatedTimeMinutes": 30, "passingScorePercentage": 70, "contentBlocks": []}]}