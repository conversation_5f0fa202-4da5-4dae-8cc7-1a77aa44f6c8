import 'package:flutter/material.dart';
import 'dart:math' as math;

class InteractiveEnergyInActionTestWidget extends StatefulWidget {
  final Map<String, dynamic>? data;

  const InteractiveEnergyInActionTestWidget({
    super.key,
    this.data,
  });

  factory InteractiveEnergyInActionTestWidget.fromData(
      Map<String, dynamic> data) {
    return InteractiveEnergyInActionTestWidget(
      data: data,
    );
  }

  @override
  State<InteractiveEnergyInActionTestWidget> createState() =>
      _InteractiveEnergyInActionTestWidgetState();
}

class _InteractiveEnergyInActionTestWidgetState
    extends State<InteractiveEnergyInActionTestWidget>
    with SingleTickerProviderStateMixin {
  // UI parameters
  late Color _primaryColor;
  late Color _secondaryColor;
  late Color _tertiaryColor;
  late Color _textColor;
  late Color _backgroundColor;
  late Color _correctColor;
  late Color _incorrectColor;

  // Test state
  bool _testStarted = false;
  bool _testCompleted = false;
  int _currentQuestionIndex = 0;
  List<bool> _questionAnswered = [];
  List<bool> _questionCorrect = [];
  List<String> _userAnswers = [];
  int _score = 0;
  int _maxScore = 0;

  // Timer
  int _timeRemaining = 600; // 10 minutes in seconds
  bool _timerActive = false;
  late DateTime _timerStartTime;

  // Animation controller
  late AnimationController _animationController;
  late Animation<double> _animation;

  // Questions
  late List<Map<String, dynamic>> _questions;

  @override
  void initState() {
    super.initState();
    _initializeFromData();
    _animationController = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 500),
    );
    _animation = CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeInOut,
    );
    _animationController.forward();
    _initializeQuestions();
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  void _initializeFromData() {
    final data = widget.data;
    if (data != null) {
      _primaryColor = Color(data['primary_color'] ?? 0xFF2196F3);
      _secondaryColor = Color(data['secondary_color'] ?? 0xFFFFA000);
      _tertiaryColor = Color(data['tertiary_color'] ?? 0xFF4CAF50);
      _textColor = Color(data['text_color'] ?? 0xFF333333);
      _backgroundColor = Color(data['background_color'] ?? 0xFFF5F5F5);
      _correctColor = Color(data['correct_color'] ?? 0xFF4CAF50);
      _incorrectColor = Color(data['incorrect_color'] ?? 0xFFF44336);
    } else {
      _primaryColor = Colors.blue;
      _secondaryColor = Colors.orange;
      _tertiaryColor = Colors.green;
      _textColor = Colors.black87;
      _backgroundColor = Colors.grey.shade100;
      _correctColor = Colors.green;
      _incorrectColor = Colors.red;
    }
  }

  void _initializeQuestions() {
    _questions = [
      {
        'type': 'multiple_choice',
        'question': 'Which of the following is the correct formula for work?',
        'options': [
          'W = F × d × cos(θ)',
          'W = m × g × h',
          'W = F / d',
          'W = P × t',
        ],
        'correctIndex': 0,
        'explanation': 'Work is calculated as the dot product of force and displacement: W = F × d × cos(θ), where θ is the angle between the force and displacement vectors.',
      },
      {
        'type': 'multiple_choice',
        'question': 'A 2 kg object is lifted 5 meters against gravity (g = 9.8 m/s²). How much work is done?',
        'options': [
          '98 J',
          '49 J',
          '10 J',
          '19.6 J',
        ],
        'correctIndex': 0,
        'explanation': 'Work = mass × gravity × height = 2 kg × 9.8 m/s² × 5 m = 98 J',
      },
      {
        'type': 'multiple_choice',
        'question': 'Which form of energy is associated with motion?',
        'options': [
          'Potential energy',
          'Kinetic energy',
          'Thermal energy',
          'Nuclear energy',
        ],
        'correctIndex': 1,
        'explanation': 'Kinetic energy is the energy of motion, calculated as KE = (1/2)mv².',
      },
      {
        'type': 'multiple_choice',
        'question': 'What is the formula for gravitational potential energy?',
        'options': [
          'PE = mgh',
          'PE = (1/2)mv²',
          'PE = (1/2)kx²',
          'PE = mc²',
        ],
        'correctIndex': 0,
        'explanation': 'Gravitational potential energy is calculated as PE = mgh, where m is mass, g is gravitational acceleration, and h is height.',
      },
      {
        'type': 'numerical',
        'question': 'A 1500 kg car is moving at 20 m/s. What is its kinetic energy in joules?',
        'answer': '300000',
        'tolerance': 1000, // Allow for rounding errors
        'unit': 'J',
        'explanation': 'Kinetic energy = (1/2)mv² = 0.5 × 1500 kg × (20 m/s)² = 300,000 J',
      },
      {
        'type': 'multiple_choice',
        'question': 'According to the law of conservation of energy:',
        'options': [
          'Energy can be created but not destroyed',
          'Energy can be destroyed but not created',
          'Energy can neither be created nor destroyed, only transformed',
          'Energy can both be created and destroyed',
        ],
        'correctIndex': 2,
        'explanation': 'The law of conservation of energy states that energy cannot be created or destroyed, only transformed from one form to another.',
      },
      {
        'type': 'multiple_choice',
        'question': 'A pendulum is at its highest point. Which statement is true?',
        'options': [
          'It has maximum kinetic energy and minimum potential energy',
          'It has minimum kinetic energy and maximum potential energy',
          'It has equal amounts of kinetic and potential energy',
          'It has zero energy',
        ],
        'correctIndex': 1,
        'explanation': 'At the highest point, a pendulum momentarily stops, so it has zero kinetic energy and maximum potential energy.',
      },
      {
        'type': 'numerical',
        'question': 'A machine does 5000 J of work in 25 seconds. What is its power output in watts?',
        'answer': '200',
        'tolerance': 1, // Allow for rounding errors
        'unit': 'W',
        'explanation': 'Power = Work/Time = 5000 J / 25 s = 200 W',
      },
      {
        'type': 'multiple_choice',
        'question': 'Which of the following is the correct formula for power?',
        'options': [
          'P = W × t',
          'P = W / t',
          'P = F × d',
          'P = m × g',
        ],
        'correctIndex': 1,
        'explanation': 'Power is the rate of doing work, calculated as P = W/t, where W is work and t is time.',
      },
      {
        'type': 'multiple_choice',
        'question': 'A force of 20 N moves an object at a constant velocity of 5 m/s. What is the power output?',
        'options': [
          '4 W',
          '25 W',
          '100 W',
          '500 W',
        ],
        'correctIndex': 2,
        'explanation': 'Power = Force × Velocity = 20 N × 5 m/s = 100 W',
      },
      {
        'type': 'drag_drop',
        'question': 'Match each energy type with its correct description:',
        'items': [
          'Kinetic Energy',
          'Gravitational Potential Energy',
          'Elastic Potential Energy',
          'Thermal Energy',
        ],
        'targets': [
          'Energy of motion',
          'Energy due to height',
          'Energy stored in a spring',
          'Energy due to temperature',
        ],
        'correctMapping': [0, 1, 2, 3], // Index of target for each item
        'explanation': 'Kinetic energy is the energy of motion. Gravitational potential energy is energy due to height. Elastic potential energy is stored in springs or elastic materials. Thermal energy is related to temperature.',
      },
      {
        'type': 'numerical',
        'question': 'A 60 W light bulb is left on for 5 hours. How much energy does it consume in joules?',
        'answer': '1080000',
        'tolerance': 10000, // Allow for rounding errors
        'unit': 'J',
        'explanation': 'Energy = Power × Time = 60 W × 5 h × 3600 s/h = 1,080,000 J',
      },
    ];

    _maxScore = _questions.length;
    _questionAnswered = List.filled(_questions.length, false);
    _questionCorrect = List.filled(_questions.length, false);
    _userAnswers = List.filled(_questions.length, '');
  }

  void _startTest() {
    setState(() {
      _testStarted = true;
      _timerActive = true;
      _timerStartTime = DateTime.now();
      _currentQuestionIndex = 0;
    });

    // Start timer
    _startTimer();
  }

  void _startTimer() {
    Future.delayed(const Duration(seconds: 1), () {
      if (!mounted || !_timerActive) return;

      final elapsedSeconds = DateTime.now().difference(_timerStartTime).inSeconds;
      setState(() {
        _timeRemaining = math.max(0, 600 - elapsedSeconds);

        if (_timeRemaining <= 0) {
          _timerActive = false;
          _completeTest();
        }
      });

      if (_timerActive) {
        _startTimer();
      }
    });
  }

  void _nextQuestion() {
    if (_currentQuestionIndex < _questions.length - 1) {
      setState(() {
        _currentQuestionIndex++;
      });
    } else {
      _completeTest();
    }
  }

  void _previousQuestion() {
    if (_currentQuestionIndex > 0) {
      setState(() {
        _currentQuestionIndex--;
      });
    }
  }

  void _answerQuestion(String answer, bool correct) {
    setState(() {
      _questionAnswered[_currentQuestionIndex] = true;
      _questionCorrect[_currentQuestionIndex] = correct;
      _userAnswers[_currentQuestionIndex] = answer;

      if (correct) {
        _score++;
      }
    });
  }

  void _completeTest() {
    setState(() {
      _testCompleted = true;
      _timerActive = false;
    });
  }

  String _formatTime(int seconds) {
    final minutes = seconds ~/ 60;
    final remainingSeconds = seconds % 60;
    return '$minutes:${remainingSeconds.toString().padLeft(2, '0')}';
  }

  Widget _buildStartScreen() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.science_outlined,
            size: 64,
            color: _primaryColor,
          ),
          const SizedBox(height: 16),
          Text(
            'Physics Module 3: Work, Energy, and Power',
            style: TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.bold,
              color: _textColor,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            'This test contains ${_questions.length} questions covering work, energy transformations, conservation of energy, and power concepts.',
            textAlign: TextAlign.center,
            style: TextStyle(
              fontSize: 14,
              color: _textColor,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            'You have 10 minutes to complete the test.',
            textAlign: TextAlign.center,
            style: TextStyle(
              fontSize: 14,
              fontWeight: FontWeight.bold,
              color: _secondaryColor,
            ),
          ),
          const SizedBox(height: 24),
          ElevatedButton.icon(
            onPressed: _startTest,
            icon: const Icon(Icons.play_arrow),
            label: const Text('Start Test'),
            style: ElevatedButton.styleFrom(
              backgroundColor: _primaryColor,
              foregroundColor: Colors.white,
              padding: const EdgeInsets.symmetric(horizontal: 32, vertical: 12),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildResultScreen() {
    final percentage = (_score / _maxScore) * 100;
    final passed = percentage >= 70;

    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            passed ? Icons.check_circle_outline : Icons.error_outline,
            size: 64,
            color: passed ? _correctColor : _incorrectColor,
          ),
          const SizedBox(height: 16),
          Text(
            passed ? 'Congratulations!' : 'Keep Learning!',
            style: TextStyle(
              fontSize: 20,
              fontWeight: FontWeight.bold,
              color: passed ? _correctColor : _incorrectColor,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            'Your score: $_score out of $_maxScore (${percentage.toStringAsFixed(1)}%)',
            style: TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.bold,
              color: _textColor,
            ),
          ),
          const SizedBox(height: 24),

          // Question review
          Container(
            width: double.infinity,
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: _backgroundColor,
              borderRadius: BorderRadius.circular(8),
              border: Border.all(color: _primaryColor.withOpacity(0.3)),
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'Question Review',
                  style: TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                    color: _textColor,
                  ),
                ),
                const SizedBox(height: 8),
                SizedBox(
                  height: 200,
                  child: ListView.builder(
                    itemCount: _questions.length,
                    itemBuilder: (context, index) {
                      final question = _questions[index];
                      final answered = _questionAnswered[index];
                      final correct = _questionCorrect[index];

                      return ListTile(
                        leading: Icon(
                          answered
                              ? (correct ? Icons.check_circle : Icons.cancel)
                              : Icons.help_outline,
                          color: answered
                              ? (correct ? _correctColor : _incorrectColor)
                              : _textColor.withOpacity(0.5),
                        ),
                        title: Text(
                          'Question ${index + 1}',
                          style: TextStyle(
                            fontWeight: FontWeight.bold,
                            color: _textColor,
                          ),
                        ),
                        subtitle: Text(
                          answered
                              ? (correct ? 'Correct' : 'Incorrect')
                              : 'Not answered',
                          style: TextStyle(
                            color: answered
                                ? (correct ? _correctColor : _incorrectColor)
                                : _textColor.withOpacity(0.5),
                          ),
                        ),
                        trailing: IconButton(
                          icon: const Icon(Icons.info_outline),
                          onPressed: () {
                            _showExplanationDialog(question);
                          },
                        ),
                      );
                    },
                  ),
                ),
              ],
            ),
          ),
          const SizedBox(height: 24),

          // Feedback based on score
          Container(
            width: double.infinity,
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: passed ? _correctColor.withOpacity(0.1) : _incorrectColor.withOpacity(0.1),
              borderRadius: BorderRadius.circular(8),
              border: Border.all(
                color: passed ? _correctColor.withOpacity(0.3) : _incorrectColor.withOpacity(0.3),
              ),
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'Feedback',
                  style: TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                    color: _textColor,
                  ),
                ),
                const SizedBox(height: 8),
                Text(
                  _getFeedbackText(percentage),
                  style: TextStyle(
                    fontSize: 14,
                    color: _textColor,
                  ),
                ),
              ],
            ),
          ),
          const SizedBox(height: 24),

          // Restart button
          ElevatedButton.icon(
            onPressed: () {
              setState(() {
                _testStarted = false;
                _testCompleted = false;
                _currentQuestionIndex = 0;
                _score = 0;
                _questionAnswered = List.filled(_questions.length, false);
                _questionCorrect = List.filled(_questions.length, false);
                _userAnswers = List.filled(_questions.length, '');
                _timeRemaining = 600;
              });
            },
            icon: const Icon(Icons.refresh),
            label: const Text('Restart Test'),
            style: ElevatedButton.styleFrom(
              backgroundColor: _secondaryColor,
              foregroundColor: Colors.white,
              padding: const EdgeInsets.symmetric(horizontal: 32, vertical: 12),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildQuestionScreen() {
    final question = _questions[_currentQuestionIndex];
    final questionType = question['type'] as String;
    final questionText = question['question'] as String;
    final answered = _questionAnswered[_currentQuestionIndex];

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Timer and progress
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            // Question progress
            Text(
              'Question ${_currentQuestionIndex + 1} of ${_questions.length}',
              style: TextStyle(
                fontSize: 14,
                fontWeight: FontWeight.bold,
                color: _textColor,
              ),
            ),

            // Timer
            Row(
              children: [
                Icon(
                  Icons.timer,
                  size: 16,
                  color: _timeRemaining < 60 ? _incorrectColor : _textColor,
                ),
                const SizedBox(width: 4),
                Text(
                  _formatTime(_timeRemaining),
                  style: TextStyle(
                    fontSize: 14,
                    fontWeight: FontWeight.bold,
                    color: _timeRemaining < 60 ? _incorrectColor : _textColor,
                  ),
                ),
              ],
            ),
          ],
        ),

        // Progress bar
        const SizedBox(height: 8),
        LinearProgressIndicator(
          value: (_currentQuestionIndex + 1) / _questions.length,
          backgroundColor: _primaryColor.withOpacity(0.2),
          valueColor: AlwaysStoppedAnimation<Color>(_primaryColor),
        ),
        const SizedBox(height: 24),

        // Question
        Text(
          questionText,
          style: TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.bold,
            color: _textColor,
          ),
        ),
        const SizedBox(height: 16),

        // Question content based on type
        if (questionType == 'multiple_choice')
          _buildMultipleChoiceQuestion(question, answered)
        else if (questionType == 'numerical')
          _buildNumericalQuestion(question, answered)
        else if (questionType == 'drag_drop')
          _buildDragDropQuestion(question, answered),

        const SizedBox(height: 24),

        // Navigation buttons
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            // Previous button
            ElevatedButton.icon(
              onPressed: _currentQuestionIndex > 0 ? _previousQuestion : null,
              icon: const Icon(Icons.arrow_back),
              label: const Text('Previous'),
              style: ElevatedButton.styleFrom(
                backgroundColor: _secondaryColor,
                foregroundColor: Colors.white,
                disabledBackgroundColor: _secondaryColor.withOpacity(0.3),
              ),
            ),

            // Next/Finish button
            ElevatedButton.icon(
              onPressed: answered
                  ? (_currentQuestionIndex < _questions.length - 1
                      ? _nextQuestion
                      : _completeTest)
                  : null,
              icon: Icon(_currentQuestionIndex < _questions.length - 1
                  ? Icons.arrow_forward
                  : Icons.check),
              label: Text(
                  _currentQuestionIndex < _questions.length - 1 ? 'Next' : 'Finish'),
              style: ElevatedButton.styleFrom(
                backgroundColor: _primaryColor,
                foregroundColor: Colors.white,
                disabledBackgroundColor: _primaryColor.withOpacity(0.3),
              ),
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildMultipleChoiceQuestion(Map<String, dynamic> question, bool answered) {
    final options = question['options'] as List<dynamic>;
    final correctIndex = question['correctIndex'] as int;

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        for (int i = 0; i < options.length; i++)
          Padding(
            padding: const EdgeInsets.only(bottom: 8.0),
            child: RadioListTile<int>(
              title: Text(
                options[i] as String,
                style: TextStyle(
                  fontSize: 14,
                  color: _textColor,
                ),
              ),
              value: i,
              groupValue: answered && _userAnswers[_currentQuestionIndex] == i.toString()
                  ? i
                  : null,
              onChanged: answered
                  ? null
                  : (value) {
                      if (value != null) {
                        _answerQuestion(
                          value.toString(),
                          value == correctIndex,
                        );
                      }
                    },
              activeColor: _primaryColor,
              tileColor: answered
                  ? (_userAnswers[_currentQuestionIndex] == i.toString()
                      ? (i == correctIndex
                          ? _correctColor.withOpacity(0.2)
                          : _incorrectColor.withOpacity(0.2))
                      : (i == correctIndex
                          ? _correctColor.withOpacity(0.1)
                          : null))
                  : null,
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(8),
                side: BorderSide(
                  color: answered
                      ? (_userAnswers[_currentQuestionIndex] == i.toString()
                          ? (i == correctIndex
                              ? _correctColor
                              : _incorrectColor)
                          : (i == correctIndex
                              ? _correctColor
                              : Colors.grey.withOpacity(0.3)))
                      : Colors.grey.withOpacity(0.3),
                ),
              ),
            ),
          ),

        // Explanation if answered
        if (answered)
          Padding(
            padding: const EdgeInsets.only(top: 8.0),
            child: Container(
              width: double.infinity,
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: _questionCorrect[_currentQuestionIndex]
                    ? _correctColor.withOpacity(0.1)
                    : _incorrectColor.withOpacity(0.1),
                borderRadius: BorderRadius.circular(8),
                border: Border.all(
                  color: _questionCorrect[_currentQuestionIndex]
                      ? _correctColor
                      : _incorrectColor,
                ),
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    _questionCorrect[_currentQuestionIndex]
                        ? 'Correct!'
                        : 'Incorrect',
                    style: TextStyle(
                      fontSize: 14,
                      fontWeight: FontWeight.bold,
                      color: _questionCorrect[_currentQuestionIndex]
                          ? _correctColor
                          : _incorrectColor,
                    ),
                  ),
                  const SizedBox(height: 4),
                  Text(
                    question['explanation'] as String,
                    style: TextStyle(
                      fontSize: 14,
                      color: _textColor,
                    ),
                  ),
                ],
              ),
            ),
          ),
      ],
    );
  }

  Widget _buildNumericalQuestion(Map<String, dynamic> question, bool answered) {
    final correctAnswer = question['answer'] as String;
    final tolerance = question['tolerance'] as int;
    final unit = question['unit'] as String;

    final TextEditingController answerController = TextEditingController();
    if (answered) {
      answerController.text = _userAnswers[_currentQuestionIndex];
    }

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            Expanded(
              child: TextField(
                controller: answerController,
                enabled: !answered,
                keyboardType: TextInputType.number,
                decoration: InputDecoration(
                  labelText: 'Your answer',
                  hintText: 'Enter a numerical value',
                  suffixText: unit,
                  border: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(8),
                  ),
                  enabledBorder: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(8),
                    borderSide: BorderSide(color: _primaryColor),
                  ),
                  focusedBorder: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(8),
                    borderSide: BorderSide(color: _primaryColor, width: 2),
                  ),
                ),
                onSubmitted: answered
                    ? null
                    : (value) {
                        _checkNumericalAnswer(value, correctAnswer, tolerance);
                      },
              ),
            ),
            if (!answered)
              Padding(
                padding: const EdgeInsets.only(left: 8.0),
                child: ElevatedButton(
                  onPressed: () {
                    _checkNumericalAnswer(
                        answerController.text, correctAnswer, tolerance);
                  },
                  style: ElevatedButton.styleFrom(
                    backgroundColor: _primaryColor,
                    foregroundColor: Colors.white,
                    padding: const EdgeInsets.symmetric(vertical: 16),
                  ),
                  child: const Text('Submit'),
                ),
              ),
          ],
        ),

        // Explanation if answered
        if (answered)
          Padding(
            padding: const EdgeInsets.only(top: 16.0),
            child: Container(
              width: double.infinity,
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: _questionCorrect[_currentQuestionIndex]
                    ? _correctColor.withOpacity(0.1)
                    : _incorrectColor.withOpacity(0.1),
                borderRadius: BorderRadius.circular(8),
                border: Border.all(
                  color: _questionCorrect[_currentQuestionIndex]
                      ? _correctColor
                      : _incorrectColor,
                ),
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    _questionCorrect[_currentQuestionIndex]
                        ? 'Correct!'
                        : 'Incorrect',
                    style: TextStyle(
                      fontSize: 14,
                      fontWeight: FontWeight.bold,
                      color: _questionCorrect[_currentQuestionIndex]
                          ? _correctColor
                          : _incorrectColor,
                    ),
                  ),
                  const SizedBox(height: 4),
                  Text(
                    'The correct answer is $correctAnswer $unit',
                    style: TextStyle(
                      fontSize: 14,
                      fontWeight: FontWeight.bold,
                      color: _textColor,
                    ),
                  ),
                  const SizedBox(height: 4),
                  Text(
                    question['explanation'] as String,
                    style: TextStyle(
                      fontSize: 14,
                      color: _textColor,
                    ),
                  ),
                ],
              ),
            ),
          ),
      ],
    );
  }

  void _checkNumericalAnswer(String userAnswer, String correctAnswer, int tolerance) {
    if (userAnswer.isEmpty) return;

    try {
      final userValue = double.parse(userAnswer);
      final correctValue = double.parse(correctAnswer);

      final difference = (userValue - correctValue).abs();
      final isCorrect = difference <= tolerance;

      _answerQuestion(userAnswer, isCorrect);
    } catch (e) {
      // Invalid number format
      _answerQuestion(userAnswer, false);
    }
  }

  Widget _buildDragDropQuestion(Map<String, dynamic> question, bool answered) {
    final items = question['items'] as List<dynamic>;
    final targets = question['targets'] as List<dynamic>;
    final correctMapping = question['correctMapping'] as List<dynamic>;

    // For simplicity, we'll use a different approach for drag and drop
    // Instead of actual drag and drop, we'll use dropdowns
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        for (int i = 0; i < items.length; i++)
          Padding(
            padding: const EdgeInsets.only(bottom: 12.0),
            child: Row(
              children: [
                Expanded(
                  flex: 2,
                  child: Container(
                    padding: const EdgeInsets.all(8),
                    decoration: BoxDecoration(
                      color: _primaryColor.withOpacity(0.1),
                      borderRadius: BorderRadius.circular(8),
                      border: Border.all(color: _primaryColor),
                    ),
                    child: Text(
                      items[i] as String,
                      style: TextStyle(
                        fontSize: 14,
                        fontWeight: FontWeight.bold,
                        color: _textColor,
                      ),
                    ),
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  flex: 3,
                  child: answered
                      ? Container(
                          padding: const EdgeInsets.all(8),
                          decoration: BoxDecoration(
                            color: _userAnswers[_currentQuestionIndex].split(',')[i] ==
                                    correctMapping[i].toString()
                                ? _correctColor.withOpacity(0.1)
                                : _incorrectColor.withOpacity(0.1),
                            borderRadius: BorderRadius.circular(8),
                            border: Border.all(
                              color: _userAnswers[_currentQuestionIndex]
                                          .split(',')[i] ==
                                      correctMapping[i].toString()
                                  ? _correctColor
                                  : _incorrectColor,
                            ),
                          ),
                          child: Text(
                            targets[int.parse(
                                _userAnswers[_currentQuestionIndex].split(',')[i])] as String,
                            style: TextStyle(
                              fontSize: 14,
                              color: _textColor,
                            ),
                          ),
                        )
                      : DropdownButtonFormField<int>(
                          decoration: InputDecoration(
                            border: OutlineInputBorder(
                              borderRadius: BorderRadius.circular(8),
                            ),
                            contentPadding: const EdgeInsets.symmetric(
                              horizontal: 12,
                              vertical: 8,
                            ),
                          ),
                          hint: const Text('Select matching description'),
                          items: List.generate(
                            targets.length,
                            (index) => DropdownMenuItem<int>(
                              value: index,
                              child: Text(targets[index] as String),
                            ),
                          ),
                          onChanged: (value) {
                            if (value != null) {
                              _handleDragDropAnswer(i, value, correctMapping);
                            }
                          },
                        ),
                ),
              ],
            ),
          ),

        // Explanation if answered
        if (answered)
          Padding(
            padding: const EdgeInsets.only(top: 16.0),
            child: Container(
              width: double.infinity,
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: _questionCorrect[_currentQuestionIndex]
                    ? _correctColor.withOpacity(0.1)
                    : _incorrectColor.withOpacity(0.1),
                borderRadius: BorderRadius.circular(8),
                border: Border.all(
                  color: _questionCorrect[_currentQuestionIndex]
                      ? _correctColor
                      : _incorrectColor,
                ),
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    _questionCorrect[_currentQuestionIndex]
                        ? 'Correct!'
                        : 'Incorrect',
                    style: TextStyle(
                      fontSize: 14,
                      fontWeight: FontWeight.bold,
                      color: _questionCorrect[_currentQuestionIndex]
                          ? _correctColor
                          : _incorrectColor,
                    ),
                  ),
                  const SizedBox(height: 4),
                  Text(
                    question['explanation'] as String,
                    style: TextStyle(
                      fontSize: 14,
                      color: _textColor,
                    ),
                  ),
                ],
              ),
            ),
          ),
      ],
    );
  }

  // Track drag and drop answers
  final Map<int, int> _dragDropAnswers = {};

  void _handleDragDropAnswer(int itemIndex, int targetIndex, List<dynamic> correctMapping) {
    setState(() {
      _dragDropAnswers[itemIndex] = targetIndex;

      // Check if all items have been mapped
      if (_dragDropAnswers.length == correctMapping.length) {
        // Check if all mappings are correct
        bool allCorrect = true;
        List<String> answerParts = [];

        for (int i = 0; i < correctMapping.length; i++) {
          final userMapping = _dragDropAnswers[i];
          answerParts.add(userMapping.toString());

          if (userMapping != correctMapping[i]) {
            allCorrect = false;
          }
        }

        _answerQuestion(answerParts.join(','), allCorrect);
      }
    });
  }

  void _showExplanationDialog(Map<String, dynamic> question) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(
          'Explanation',
          style: TextStyle(
            fontSize: 18,
            fontWeight: FontWeight.bold,
            color: _textColor,
          ),
        ),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              question['question'] as String,
              style: TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.bold,
                color: _textColor,
              ),
            ),
            const SizedBox(height: 8),
            Text(
              question['explanation'] as String,
              style: TextStyle(
                fontSize: 14,
                color: _textColor,
              ),
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Close'),
          ),
        ],
      ),
    );
  }

  String _getFeedbackText(double percentage) {
    if (percentage >= 90) {
      return 'Excellent! You have a strong understanding of work, energy, and power concepts. You can confidently apply these principles to solve complex physics problems.';
    } else if (percentage >= 70) {
      return 'Good job! You understand most of the key concepts related to work, energy, and power. Review the questions you missed to strengthen your understanding.';
    } else if (percentage >= 50) {
      return 'You have a basic understanding of work, energy, and power concepts, but there are some areas that need improvement. Focus on reviewing the fundamental principles and formulas.';
    } else {
      return 'You need more practice with work, energy, and power concepts. Review the module materials, focus on understanding the fundamental principles, and try the test again.';
    }
  }

  @override
  Widget build(BuildContext context) {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
      ),
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Title and description
            Text(
              'Energy in Action: Module Test',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
                color: _textColor,
              ),
            ),
            const SizedBox(height: 8),
            Text(
              'Test your knowledge of work, energy, and power concepts',
              style: TextStyle(
                fontSize: 14,
                color: _textColor.withOpacity(0.7),
              ),
            ),
            const SizedBox(height: 16),

            // Test content
            Expanded(
              child: !_testStarted
                ? _buildStartScreen()
                : _testCompleted
                  ? _buildResultScreen()
                  : _buildQuestionScreen(),
            ),
          ],
        ),
      ),
    );
  }
}