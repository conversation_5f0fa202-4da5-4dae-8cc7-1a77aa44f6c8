import 'package:flutter/material.dart';
import 'dart:math' as math;

/// A widget that demonstrates the scientific method process with an interactive flowchart
/// Users can explore each step of the scientific method and see how they connect
class InteractiveScientificMethodFlowchartWidget extends StatefulWidget {
  /// The data for this widget
  final Map<String, dynamic> data;

  /// Optional callback when the widget state changes
  final Function(bool isCompleted)? onStateChanged;

  const InteractiveScientificMethodFlowchartWidget({
    Key? key,
    required this.data,
    this.onStateChanged,
  }) : super(key: key);

  /// Factory constructor to create the widget from JSON data
  factory InteractiveScientificMethodFlowchartWidget.fromData(Map<String, dynamic> data) {
    return InteractiveScientificMethodFlowchartWidget(
      data: data,
    );
  }

  @override
  State<InteractiveScientificMethodFlowchartWidget> createState() => _InteractiveScientificMethodFlowchartWidgetState();
}

class _InteractiveScientificMethodFlowchartWidgetState extends State<InteractiveScientificMethodFlowchartWidget> with SingleTickerProviderStateMixin {
  // Scientific method steps
  late List<ScientificMethodStep> _steps;
  late int _currentStepIndex;
  late bool _isCompleted;
  late bool _showExplanation;
  
  // Animation controller for transitions
  late AnimationController _animationController;
  late Animation<double> _animation;
  
  // Colors
  late Color _primaryColor;
  late Color _secondaryColor;
  late Color _accentColor;
  late Color _backgroundColor;
  late Color _textColor;

  @override
  void initState() {
    super.initState();
    
    // Initialize animation controller
    _animationController = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 500),
    );
    
    _animation = CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeInOut,
    );
    
    // Initialize colors
    _primaryColor = _getColorFromHex(widget.data['primaryColor'] ?? '#2196F3');
    _secondaryColor = _getColorFromHex(widget.data['secondaryColor'] ?? '#FF9800');
    _accentColor = _getColorFromHex(widget.data['accentColor'] ?? '#4CAF50');
    _backgroundColor = _getColorFromHex(widget.data['backgroundColor'] ?? '#FFFFFF');
    _textColor = _getColorFromHex(widget.data['textColor'] ?? '#212121');
    
    // Initialize steps
    _initializeSteps();
    
    // Initialize state
    _currentStepIndex = 0;
    _isCompleted = false;
    _showExplanation = false;
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  // Initialize the scientific method steps
  void _initializeSteps() {
    final List<dynamic> stepsData = widget.data['steps'] ?? _getDefaultSteps();
    
    _steps = stepsData.map((stepData) {
      return ScientificMethodStep(
        title: stepData['title'] ?? '',
        description: stepData['description'] ?? '',
        example: stepData['example'] ?? '',
        icon: _getIconData(stepData['icon'] ?? 'help_outline'),
      );
    }).toList();
  }

  // Get default steps if none are provided
  List<Map<String, dynamic>> _getDefaultSteps() {
    return [
      {
        'title': 'Ask a Question',
        'description': 'The scientific method begins with a question about something you observe.',
        'example': 'Example: Why do plants grow toward light?',
        'icon': 'help_outline',
      },
      {
        'title': 'Research',
        'description': 'Gather information and see what is already known about your question.',
        'example': 'Example: Read about phototropism and plant biology.',
        'icon': 'search',
      },
      {
        'title': 'Form a Hypothesis',
        'description': 'Propose an explanation that can be tested.',
        'example': 'Example: Plants grow toward light because it helps them photosynthesize more efficiently.',
        'icon': 'lightbulb_outline',
      },
      {
        'title': 'Test with an Experiment',
        'description': 'Design and perform an experiment to test your hypothesis.',
        'example': 'Example: Place plants in different lighting conditions and measure growth direction.',
        'icon': 'science',
      },
      {
        'title': 'Analyze Data',
        'description': 'Collect and analyze the data from your experiment.',
        'example': 'Example: Measure the angle of growth and compare between different light conditions.',
        'icon': 'assessment',
      },
      {
        'title': 'Draw Conclusions',
        'description': 'Determine if your hypothesis is supported or refuted by the data.',
        'example': 'Example: Plants consistently grew toward the light source, supporting the hypothesis.',
        'icon': 'fact_check',
      },
      {
        'title': 'Communicate Results',
        'description': 'Share your findings with others so they can be reviewed and verified.',
        'example': 'Example: Write a report or present your findings to peers.',
        'icon': 'share',
      },
      {
        'title': 'Refine and Repeat',
        'description': 'Based on feedback and new questions, refine your experiment or ask new questions.',
        'example': 'Example: Now investigate which wavelengths of light cause the strongest growth response.',
        'icon': 'refresh',
      },
    ];
  }

  // Get icon data from string
  IconData _getIconData(String iconName) {
    switch (iconName) {
      case 'help_outline':
        return Icons.help_outline;
      case 'search':
        return Icons.search;
      case 'lightbulb_outline':
        return Icons.lightbulb_outline;
      case 'science':
        return Icons.science;
      case 'assessment':
        return Icons.assessment;
      case 'fact_check':
        return Icons.fact_check;
      case 'share':
        return Icons.share;
      case 'refresh':
        return Icons.refresh;
      default:
        return Icons.help_outline;
    }
  }

  // Get color from hex string
  Color _getColorFromHex(String hexColor) {
    hexColor = hexColor.replaceAll('#', '');
    if (hexColor.length == 6) {
      hexColor = 'FF$hexColor';
    }
    return Color(int.parse(hexColor, radix: 16));
  }

  // Navigate to the next step
  void _nextStep() {
    if (_currentStepIndex < _steps.length - 1) {
      setState(() {
        _showExplanation = false;
        _currentStepIndex++;
      });
      _animationController.forward(from: 0.0);
    } else if (!_isCompleted) {
      setState(() {
        _isCompleted = true;
      });
      widget.onStateChanged?.call(true);
    }
  }

  // Navigate to the previous step
  void _previousStep() {
    if (_currentStepIndex > 0) {
      setState(() {
        _showExplanation = false;
        _currentStepIndex--;
      });
      _animationController.forward(from: 0.0);
    }
  }

  // Toggle explanation visibility
  void _toggleExplanation() {
    setState(() {
      _showExplanation = !_showExplanation;
    });
  }

  // Reset the flowchart
  void _resetFlowchart() {
    setState(() {
      _currentStepIndex = 0;
      _isCompleted = false;
      _showExplanation = false;
    });
    _animationController.forward(from: 0.0);
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: _backgroundColor,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withOpacity(0.2),
            spreadRadius: 1,
            blurRadius: 3,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Title
          Text(
            widget.data['title'] ?? 'Scientific Method Flowchart',
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
              color: _textColor,
            ),
          ),
          
          const SizedBox(height: 16),
          
          // Flowchart visualization
          SizedBox(
            height: 200,
            child: _buildFlowchart(),
          ),
          
          const SizedBox(height: 16),
          
          // Current step details
          AnimatedBuilder(
            animation: _animation,
            builder: (context, child) {
              return FadeTransition(
                opacity: _animation,
                child: child,
              );
            },
            child: _buildStepDetails(),
          ),
          
          const SizedBox(height: 16),
          
          // Navigation buttons
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              ElevatedButton(
                onPressed: _currentStepIndex > 0 ? _previousStep : null,
                style: ElevatedButton.styleFrom(
                  backgroundColor: _secondaryColor,
                  foregroundColor: Colors.white,
                ),
                child: const Text('Previous'),
              ),
              ElevatedButton(
                onPressed: _toggleExplanation,
                style: ElevatedButton.styleFrom(
                  backgroundColor: _accentColor,
                  foregroundColor: Colors.white,
                ),
                child: Text(_showExplanation ? 'Hide Example' : 'Show Example'),
              ),
              ElevatedButton(
                onPressed: _currentStepIndex < _steps.length - 1 ? _nextStep : (_isCompleted ? _resetFlowchart : _nextStep),
                style: ElevatedButton.styleFrom(
                  backgroundColor: _primaryColor,
                  foregroundColor: Colors.white,
                ),
                child: Text(_currentStepIndex < _steps.length - 1 ? 'Next' : (_isCompleted ? 'Restart' : 'Complete')),
              ),
            ],
          ),
          
          // Widget name tag at the bottom
          if (widget.data['showNameTag'] ?? true)
            Padding(
              padding: const EdgeInsets.only(top: 8),
              child: Text(
                'InteractiveScientificMethodFlowchartWidget',
                style: TextStyle(
                  color: Colors.grey[600],
                  fontSize: 12,
                  fontStyle: FontStyle.italic,
                ),
              ),
            ),
        ],
      ),
    );
  }

  // Build the flowchart visualization
  Widget _buildFlowchart() {
    return CustomPaint(
      painter: ScientificMethodFlowchartPainter(
        steps: _steps,
        currentStepIndex: _currentStepIndex,
        primaryColor: _primaryColor,
        secondaryColor: _secondaryColor,
        accentColor: _accentColor,
        textColor: _textColor,
      ),
      child: Container(),
    );
  }

  // Build the current step details
  Widget _buildStepDetails() {
    final step = _steps[_currentStepIndex];
    
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(8),
        side: BorderSide(color: _primaryColor.withOpacity(0.5), width: 1),
      ),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(step.icon, color: _primaryColor, size: 24),
                const SizedBox(width: 8),
                Text(
                  'Step ${_currentStepIndex + 1}: ${step.title}',
                  style: TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                    color: _textColor,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 8),
            Text(
              step.description,
              style: TextStyle(
                fontSize: 14,
                color: _textColor.withOpacity(0.8),
              ),
            ),
            if (_showExplanation) ...[
              const SizedBox(height: 8),
              Container(
                padding: const EdgeInsets.all(8),
                decoration: BoxDecoration(
                  color: _accentColor.withOpacity(0.1),
                  borderRadius: BorderRadius.circular(4),
                  border: Border.all(color: _accentColor.withOpacity(0.3)),
                ),
                child: Text(
                  step.example,
                  style: TextStyle(
                    fontSize: 14,
                    fontStyle: FontStyle.italic,
                    color: _textColor.withOpacity(0.9),
                  ),
                ),
              ),
            ],
          ],
        ),
      ),
    );
  }
}

// Scientific method step model
class ScientificMethodStep {
  final String title;
  final String description;
  final String example;
  final IconData icon;
  
  ScientificMethodStep({
    required this.title,
    required this.description,
    required this.example,
    required this.icon,
  });
}

// Custom painter for the flowchart
class ScientificMethodFlowchartPainter extends CustomPainter {
  final List<ScientificMethodStep> steps;
  final int currentStepIndex;
  final Color primaryColor;
  final Color secondaryColor;
  final Color accentColor;
  final Color textColor;
  
  ScientificMethodFlowchartPainter({
    required this.steps,
    required this.currentStepIndex,
    required this.primaryColor,
    required this.secondaryColor,
    required this.accentColor,
    required this.textColor,
  });
  
  @override
  void paint(Canvas canvas, Size size) {
    final paint = Paint()
      ..style = PaintingStyle.stroke
      ..strokeWidth = 2.0;
    
    final fillPaint = Paint()
      ..style = PaintingStyle.fill;
    
    final textPainter = TextPainter(
      textDirection: TextDirection.ltr,
      textAlign: TextAlign.center,
    );
    
    final double centerX = size.width / 2;
    final double startY = 20.0;
    final double stepRadius = 25.0;
    final double arrowLength = (size.width - (2 * stepRadius)) / (steps.length - 1);
    
    // Draw the flowchart steps and connections
    for (int i = 0; i < steps.length; i++) {
      final double x = i * arrowLength + stepRadius;
      final double y = startY + stepRadius;
      
      // Draw connection line to next step
      if (i < steps.length - 1) {
        paint.color = i < currentStepIndex ? accentColor : Colors.grey.withOpacity(0.5);
        canvas.drawLine(
          Offset(x + stepRadius, y),
          Offset(x + arrowLength - stepRadius, y),
          paint,
        );
        
        // Draw arrow tip
        final Path arrowPath = Path()
          ..moveTo(x + arrowLength - stepRadius - 10, y - 5)
          ..lineTo(x + arrowLength - stepRadius, y)
          ..lineTo(x + arrowLength - stepRadius - 10, y + 5)
          ..close();
        
        fillPaint.color = i < currentStepIndex ? accentColor : Colors.grey.withOpacity(0.5);
        canvas.drawPath(arrowPath, fillPaint);
      }
      
      // Draw step circle
      if (i == currentStepIndex) {
        // Current step
        paint.color = primaryColor;
        fillPaint.color = primaryColor.withOpacity(0.2);
      } else if (i < currentStepIndex) {
        // Completed step
        paint.color = accentColor;
        fillPaint.color = accentColor.withOpacity(0.2);
      } else {
        // Future step
        paint.color = Colors.grey.withOpacity(0.5);
        fillPaint.color = Colors.grey.withOpacity(0.1);
      }
      
      canvas.drawCircle(Offset(x, y), stepRadius, fillPaint);
      canvas.drawCircle(Offset(x, y), stepRadius, paint);
      
      // Draw step number
      textPainter.text = TextSpan(
        text: '${i + 1}',
        style: TextStyle(
          color: i <= currentStepIndex ? textColor : Colors.grey,
          fontSize: 14,
          fontWeight: i == currentStepIndex ? FontWeight.bold : FontWeight.normal,
        ),
      );
      
      textPainter.layout();
      textPainter.paint(
        canvas,
        Offset(x - textPainter.width / 2, y - textPainter.height / 2),
      );
      
      // Draw step title below
      textPainter.text = TextSpan(
        text: steps[i].title,
        style: TextStyle(
          color: i <= currentStepIndex ? textColor : Colors.grey,
          fontSize: 10,
          fontWeight: i == currentStepIndex ? FontWeight.bold : FontWeight.normal,
        ),
      );
      
      textPainter.layout(maxWidth: arrowLength);
      textPainter.paint(
        canvas,
        Offset(x - textPainter.width / 2, y + stepRadius + 5),
      );
    }
  }
  
  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) {
    return true;
  }
}
