{"id": "systems-engineering-project-management", "title": "Systems Engineering and Project Management", "description": "Explore the principles of designing and managing complex engineering projects.", "order": 5, "lessons": [{"id": "systems-thinking-complex-interactions", "title": "Systems Thinking: Understanding Complex Interactions", "description": "Analyze systems as a whole.", "order": 1, "type": "interactive_lesson", "estimatedTimeMinutes": 20, "contentBlocks": []}, {"id": "system-requirements-specifications", "title": "System Requirements and Specifications", "description": "Define the goals and constraints of a project.", "order": 2, "type": "interactive_lesson", "estimatedTimeMinutes": 25, "contentBlocks": []}, {"id": "project-planning-scheduling", "title": "Project Planning and Scheduling", "description": "Develop timelines and allocate resources.", "order": 3, "type": "interactive_lesson", "estimatedTimeMinutes": 25, "contentBlocks": []}, {"id": "risk-management-mitigation", "title": "Risk Management and Mitigation", "description": "Identify and address potential problems.", "order": 4, "type": "interactive_lesson", "estimatedTimeMinutes": 20, "contentBlocks": []}, {"id": "teamwork-communication-engineering-projects", "title": "Teamwork and Communication in Engineering Projects", "description": "Understand effective collaboration.", "order": 5, "type": "interactive_lesson", "estimatedTimeMinutes": 20, "contentBlocks": []}, {"id": "module-test-systems-strategist", "title": "Module Test: The Systems Strategist", "description": "Understand the principles of systems engineering and project management.", "order": 6, "type": "module_test_interactive", "estimatedTimeMinutes": 30, "passingScorePercentage": 70, "contentBlocks": []}]}