{"id": "computer_science", "title": "Computer Science", "description": "Learn programming, algorithms, and computational thinking.", "courses": [{"id": "computational_thinking", "title": "Computational Thinking", "description": "Develop essential problem-solving skills by learning to break down complex problems, recognize patterns, design algorithms, and evaluate solutions.", "difficulty": "beginner", "estimatedTimeHours": 5, "imageUrl": "computational_thinking.jpg"}, {"id": "algorithms_data_structures", "title": "Algorithms & Data Structures", "description": "Explore fundamental data structures and algorithms that form the backbone of computer science, from arrays and linked lists to sorting algorithms and graph traversal.", "difficulty": "intermediate", "estimatedTimeHours": 6, "imageUrl": "algorithms_data_structures.jpg"}, {"id": "ai_machine_learning", "title": "AI & Machine Learning Kickstart", "description": "Explore the fascinating world of Artificial Intelligence and Machine Learning, from foundational concepts to practical applications, with intuitive explanations and interactive examples.", "difficulty": "intermediate", "estimatedTimeHours": 6, "imageUrl": "ai_machine_learning.jpg"}, {"id": "ethical_hacking", "title": "Ethical Hacking Essentials", "description": "Learn the fundamentals of ethical hacking and cybersecurity, from reconnaissance techniques to vulnerability assessment and reporting, with a strong emphasis on ethical responsibilities and legal frameworks.", "difficulty": "intermediate", "estimatedTimeHours": 6, "imageUrl": "ethical_hacking.jpg"}]}