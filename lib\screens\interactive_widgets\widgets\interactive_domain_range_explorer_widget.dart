import 'package:flutter/material.dart';
import 'dart:math' as math;

/// A widget that helps users understand and visualize the domain and range of functions
class InteractiveDomainRangeExplorerWidget extends StatefulWidget {
  /// The data for this widget
  final Map<String, dynamic> data;

  /// Optional callback when the widget state changes
  final Function(bool isCompleted)? onStateChanged;

  const InteractiveDomainRangeExplorerWidget({
    Key? key,
    required this.data,
    this.onStateChanged,
  }) : super(key: key);

  /// Factory constructor to create the widget from JSON data
  factory InteractiveDomainRangeExplorerWidget.fromData(Map<String, dynamic> data) {
    return InteractiveDomainRangeExplorerWidget(
      data: data,
    );
  }

  @override
  State<InteractiveDomainRangeExplorerWidget> createState() => _InteractiveDomainRangeExplorerWidgetState();
}

class _InteractiveDomainRangeExplorerWidgetState extends State<InteractiveDomainRangeExplorerWidget> with SingleTickerProviderStateMixin {
  // Current function index
  int _currentFunctionIndex = 0;

  // List of functions to explore
  late List<Map<String, dynamic>> _functions;

  // Whether domain is highlighted
  bool _showDomain = true;

  // Whether range is highlighted
  bool _showRange = true;

  // Whether the widget is completed
  bool _isCompleted = false;

  // Whether to show explanation
  bool _showExplanation = false;

  // Colors
  late Color _primaryColor;
  late Color _secondaryColor;
  late Color _accentColor;
  late Color _backgroundColor;
  late Color _textColor;
  late Color _domainColor;
  late Color _rangeColor;

  // Animation controller for transitions
  late AnimationController _animationController;
  late Animation<double> _animation;

  // User's domain and range answers
  String _userDomainAnswer = '';
  String _userRangeAnswer = '';

  // Whether feedback is shown
  bool _showFeedback = false;

  @override
  void initState() {
    super.initState();

    // Initialize colors
    _primaryColor = _parseColor(widget.data['primary_color']) ?? Colors.blue;
    _secondaryColor = _parseColor(widget.data['secondary_color']) ?? Colors.orange;
    _accentColor = _parseColor(widget.data['accent_color']) ?? Colors.green;
    _backgroundColor = _parseColor(widget.data['background_color']) ?? Colors.white;
    _textColor = _parseColor(widget.data['text_color']) ?? Colors.black87;
    _domainColor = _parseColor(widget.data['domain_color']) ?? Colors.purple;
    _rangeColor = _parseColor(widget.data['range_color']) ?? Colors.teal;

    // Initialize animation controller
    _animationController = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 500),
    );

    _animation = CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeInOut,
    );

    // Initialize functions
    _functions = widget.data['functions'] != null
        ? List<Map<String, dynamic>>.from(widget.data['functions'])
        : _getDefaultFunctions();
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  // Parse color from hex string
  Color? _parseColor(String? hexString) {
    if (hexString == null) return null;

    hexString = hexString.replaceAll('#', '');
    if (hexString.length == 6) {
      hexString = 'FF$hexString';
    }

    return Color(int.parse(hexString, radix: 16));
  }

  // Get default functions if none provided
  List<Map<String, dynamic>> _getDefaultFunctions() {
    return [
      {
        'name': 'Linear Function',
        'equation': 'f(x) = 2x + 3',
        'domain': 'All real numbers',
        'domain_latex': 'x \\in \\mathbb{R}',
        'range': 'All real numbers',
        'range_latex': 'y \\in \\mathbb{R}',
        'domain_min': -5.0,
        'domain_max': 5.0,
        'range_min': -7.0,
        'range_max': 13.0,
        'points': _generateLinearPoints(-5, 5, (x) => 2 * x + 3),
        'explanation': 'This linear function has no restrictions on its domain or range. Any real number can be an input (domain), and any real number can be an output (range).',
      },
      {
        'name': 'Square Root Function',
        'equation': 'f(x) = √x',
        'domain': 'All non-negative real numbers',
        'domain_latex': 'x \\geq 0',
        'range': 'All non-negative real numbers',
        'range_latex': 'y \\geq 0',
        'domain_min': 0.0,
        'domain_max': 5.0,
        'range_min': 0.0,
        'range_max': 2.5,
        'points': _generatePoints(0, 5, 0.1, (x) => math.sqrt(x)),
        'explanation': 'The square root function has a domain restriction: you can only take the square root of non-negative numbers. The range is also restricted to non-negative numbers since the square root of a positive number is always positive.',
      },
      {
        'name': 'Reciprocal Function',
        'equation': 'f(x) = 1/x',
        'domain': 'All real numbers except x = 0',
        'domain_latex': 'x \\in \\mathbb{R}, x \\neq 0',
        'range': 'All real numbers except y = 0',
        'range_latex': 'y \\in \\mathbb{R}, y \\neq 0',
        'domain_min': -5.0,
        'domain_max': 5.0,
        'range_min': -5.0,
        'range_max': 5.0,
        'points': _generateReciprocalPoints(-5, 5),
        'explanation': 'The reciprocal function has a domain restriction: x cannot equal 0 because division by zero is undefined. The range also excludes 0 because 1/x can never equal 0 for any value of x.',
      },
      {
        'name': 'Quadratic Function',
        'equation': 'f(x) = x²',
        'domain': 'All real numbers',
        'domain_latex': 'x \\in \\mathbb{R}',
        'range': 'All non-negative real numbers',
        'range_latex': 'y \\geq 0',
        'domain_min': -5.0,
        'domain_max': 5.0,
        'range_min': 0.0,
        'range_max': 25.0,
        'points': _generatePoints(-5, 5, 0.1, (x) => x * x),
        'explanation': 'The quadratic function f(x) = x² has no restrictions on its domain - any real number can be an input. However, the range is restricted to non-negative numbers because the square of any real number is always non-negative.',
      },
      {
        'name': 'Sine Function',
        'equation': 'f(x) = sin(x)',
        'domain': 'All real numbers',
        'domain_latex': 'x \\in \\mathbb{R}',
        'range': 'All real numbers between -1 and 1',
        'range_latex': '-1 \\leq y \\leq 1',
        'domain_min': -2 * math.pi,
        'domain_max': 2 * math.pi,
        'range_min': -1.0,
        'range_max': 1.0,
        'points': _generatePoints(-2 * math.pi, 2 * math.pi, 0.1, (x) => math.sin(x)),
        'explanation': 'The sine function has no restrictions on its domain - any real number can be an input. However, the range is restricted to values between -1 and 1 inclusive, as the sine function oscillates between these values.',
      },
    ];
  }

  // Generate points for a linear function
  static List<Map<String, dynamic>> _generateLinearPoints(double min, double max, double Function(double) f) {
    return _generatePoints(min, max, 0.5, f);
  }

  // Generate points for a function with the given step
  static List<Map<String, dynamic>> _generatePoints(double min, double max, double step, double Function(double) f) {
    final List<Map<String, dynamic>> points = [];
    for (double x = min; x <= max; x += step) {
      points.add({
        'x': x,
        'y': f(x),
      });
    }
    return points;
  }

  // Generate points for a reciprocal function, avoiding x = 0
  static List<Map<String, dynamic>> _generateReciprocalPoints(double min, double max) {
    final List<Map<String, dynamic>> points = [];
    for (double x = min; x <= max; x += 0.1) {
      if (x.abs() < 0.1) continue; // Skip values close to 0
      points.add({
        'x': x,
        'y': 1 / x,
      });
    }
    return points;
  }

  // Toggle domain visibility
  void _toggleDomain() {
    setState(() {
      _showDomain = !_showDomain;
    });
  }

  // Toggle range visibility
  void _toggleRange() {
    setState(() {
      _showRange = !_showRange;
    });
  }

  // Move to the next function
  void _nextFunction() {
    if (_currentFunctionIndex < _functions.length - 1) {
      // Start animation
      _animationController.forward().then((_) {
        setState(() {
          _currentFunctionIndex++;
          _showFeedback = false;
          _showExplanation = false;
          _userDomainAnswer = '';
          _userRangeAnswer = '';
        });
        _animationController.reverse();
      });
    } else {
      // Reset to the first function if completed
      _animationController.forward().then((_) {
        setState(() {
          _currentFunctionIndex = 0;
          _showFeedback = false;
          _showExplanation = false;
          _userDomainAnswer = '';
          _userRangeAnswer = '';
          _isCompleted = false;
        });
        _animationController.reverse();
      });
    }
  }

  // Toggle explanation visibility
  void _toggleExplanation() {
    setState(() {
      _showExplanation = !_showExplanation;
    });
  }

  // Check user's answers
  void _checkAnswers() {
    final currentFunction = _functions[_currentFunctionIndex];

    setState(() {
      _showFeedback = true;

      // If this is the last function and both answers are correct, mark as completed
      if (_currentFunctionIndex == _functions.length - 1 &&
          _isDomainAnswerCorrect() &&
          _isRangeAnswerCorrect()) {
        _isCompleted = true;
      }
    });

    // Notify parent of state change
    widget.onStateChanged?.call(_isCompleted);
  }

  // Check if domain answer is correct
  bool _isDomainAnswerCorrect() {
    final currentFunction = _functions[_currentFunctionIndex];
    final correctDomain = currentFunction['domain'] as String;

    // Simple string comparison for now
    // In a real app, you would use a more sophisticated answer checking system
    return _userDomainAnswer.toLowerCase().contains(correctDomain.toLowerCase()) ||
           correctDomain.toLowerCase().contains(_userDomainAnswer.toLowerCase());
  }

  // Check if range answer is correct
  bool _isRangeAnswerCorrect() {
    final currentFunction = _functions[_currentFunctionIndex];
    final correctRange = currentFunction['range'] as String;

    // Simple string comparison for now
    return _userRangeAnswer.toLowerCase().contains(correctRange.toLowerCase()) ||
           correctRange.toLowerCase().contains(_userRangeAnswer.toLowerCase());
  }

  // Build function information
  Widget _buildFunctionInfo(Map<String, dynamic> function) {
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: _primaryColor.withOpacity(0.1),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: _primaryColor),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Function: ${function['name']}',
            style: TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.bold,
              color: _primaryColor,
            ),
          ),
          const SizedBox(height: 4),
          Text(
            'Equation: ${function['equation']}',
            style: TextStyle(
              fontSize: 16,
              color: _textColor,
            ),
          ),
        ],
      ),
    );
  }

  // Build graph visualization
  Widget _buildGraphVisualization(Map<String, dynamic> function) {
    return Container(
      height: 250,
      width: double.infinity,
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: Colors.grey[300]!),
      ),
      child: Stack(
        children: [
          ClipRRect(
            borderRadius: BorderRadius.circular(8),
            child: CustomPaint(
              painter: DomainRangeGraphPainter(
                points: List<Map<String, dynamic>>.from(function['points']),
                color: _primaryColor,
                domainColor: _domainColor,
                rangeColor: _rangeColor,
                showDomain: _showDomain,
                showRange: _showRange,
                domainMin: function['domain_min'] as double,
                domainMax: function['domain_max'] as double,
                rangeMin: function['range_min'] as double,
                rangeMax: function['range_max'] as double,
              ),
              child: Container(),
            ),
          ),
          Positioned(
            bottom: 8,
            left: 8,
            child: Row(
              children: [
                ElevatedButton.icon(
                  onPressed: _toggleDomain,
                  icon: Icon(
                    _showDomain ? Icons.visibility : Icons.visibility_off,
                    size: 16,
                  ),
                  label: Text(
                    'Domain',
                    style: const TextStyle(fontSize: 12),
                  ),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: _domainColor,
                    foregroundColor: Colors.white,
                    padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                  ),
                ),
                const SizedBox(width: 8),
                ElevatedButton.icon(
                  onPressed: _toggleRange,
                  icon: Icon(
                    _showRange ? Icons.visibility : Icons.visibility_off,
                    size: 16,
                  ),
                  label: Text(
                    'Range',
                    style: const TextStyle(fontSize: 12),
                  ),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: _rangeColor,
                    foregroundColor: Colors.white,
                    padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  // Build domain and range input
  Widget _buildDomainRangeInput() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Identify the domain and range:',
          style: TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.bold,
            color: _textColor,
          ),
        ),
        const SizedBox(height: 8),
        Row(
          children: [
            Container(
              width: 80,
              child: Text(
                'Domain:',
                style: TextStyle(
                  fontSize: 14,
                  fontWeight: FontWeight.bold,
                  color: _domainColor,
                ),
              ),
            ),
            Expanded(
              child: TextField(
                decoration: InputDecoration(
                  hintText: 'e.g., "All real numbers" or "x ≥ 0"',
                  border: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(8),
                  ),
                  contentPadding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
                ),
                enabled: !_showFeedback,
                onChanged: (value) {
                  setState(() {
                    _userDomainAnswer = value;
                  });
                },
              ),
            ),
          ],
        ),
        const SizedBox(height: 8),
        Row(
          children: [
            Container(
              width: 80,
              child: Text(
                'Range:',
                style: TextStyle(
                  fontSize: 14,
                  fontWeight: FontWeight.bold,
                  color: _rangeColor,
                ),
              ),
            ),
            Expanded(
              child: TextField(
                decoration: InputDecoration(
                  hintText: 'e.g., "All real numbers" or "y ≥ 0"',
                  border: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(8),
                  ),
                  contentPadding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
                ),
                enabled: !_showFeedback,
                onChanged: (value) {
                  setState(() {
                    _userRangeAnswer = value;
                  });
                },
              ),
            ),
          ],
        ),
        const SizedBox(height: 16),
        Center(
          child: ElevatedButton(
            onPressed: _showFeedback ? null : _checkAnswers,
            style: ElevatedButton.styleFrom(
              backgroundColor: _primaryColor,
              foregroundColor: Colors.white,
              padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
            ),
            child: const Text('Check Answers'),
          ),
        ),
      ],
    );
  }

  // Build feedback
  Widget _buildFeedback() {
    final currentFunction = _functions[_currentFunctionIndex];
    final isDomainCorrect = _isDomainAnswerCorrect();
    final isRangeCorrect = _isRangeAnswerCorrect();
    final isAllCorrect = isDomainCorrect && isRangeCorrect;

    return Container(
      margin: const EdgeInsets.only(top: 16),
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: isAllCorrect ? _accentColor.withOpacity(0.1) : _secondaryColor.withOpacity(0.1),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: isAllCorrect ? _accentColor : _secondaryColor),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(
                isAllCorrect ? Icons.check_circle : Icons.info,
                color: isAllCorrect ? _accentColor : _secondaryColor,
              ),
              const SizedBox(width: 8),
              Text(
                isAllCorrect ? 'Correct!' : 'Let\'s review your answers:',
                style: TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.bold,
                  color: isAllCorrect ? _accentColor : _secondaryColor,
                ),
              ),
            ],
          ),
          const SizedBox(height: 8),
          Row(
            children: [
              Container(
                width: 80,
                child: Text(
                  'Domain:',
                  style: TextStyle(
                    fontSize: 14,
                    fontWeight: FontWeight.bold,
                    color: _domainColor,
                  ),
                ),
              ),
              Expanded(
                child: Row(
                  children: [
                    Icon(
                      isDomainCorrect ? Icons.check_circle : Icons.cancel,
                      color: isDomainCorrect ? _accentColor : _secondaryColor,
                      size: 16,
                    ),
                    const SizedBox(width: 4),
                    Text(
                      currentFunction['domain'] as String,
                      style: TextStyle(
                        fontSize: 14,
                        color: _textColor,
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
          const SizedBox(height: 4),
          Row(
            children: [
              Container(
                width: 80,
                child: Text(
                  'Range:',
                  style: TextStyle(
                    fontSize: 14,
                    fontWeight: FontWeight.bold,
                    color: _rangeColor,
                  ),
                ),
              ),
              Expanded(
                child: Row(
                  children: [
                    Icon(
                      isRangeCorrect ? Icons.check_circle : Icons.cancel,
                      color: isRangeCorrect ? _accentColor : _secondaryColor,
                      size: 16,
                    ),
                    const SizedBox(width: 4),
                    Text(
                      currentFunction['range'] as String,
                      style: TextStyle(
                        fontSize: 14,
                        color: _textColor,
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
          if (_showExplanation)
            Padding(
              padding: const EdgeInsets.only(top: 8),
              child: Text(
                currentFunction['explanation'] as String,
                style: TextStyle(
                  fontSize: 14,
                  fontStyle: FontStyle.italic,
                  color: _textColor,
                ),
              ),
            ),
          const SizedBox(height: 8),
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              ElevatedButton(
                onPressed: _toggleExplanation,
                style: ElevatedButton.styleFrom(
                  backgroundColor: _accentColor,
                  foregroundColor: Colors.white,
                ),
                child: Text(_showExplanation ? 'Hide Explanation' : 'Show Explanation'),
              ),
              ElevatedButton(
                onPressed: _nextFunction,
                style: ElevatedButton.styleFrom(
                  backgroundColor: _primaryColor,
                  foregroundColor: Colors.white,
                ),
                child: Text(_currentFunctionIndex < _functions.length - 1 ? 'Next Function' : 'Start Over'),
              ),
            ],
          ),
        ],
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    final currentFunction = _functions[_currentFunctionIndex];

    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: _backgroundColor,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withOpacity(0.2),
            spreadRadius: 1,
            blurRadius: 3,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Title
          Text(
            widget.data['title'] ?? 'Domain and Range Explorer',
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
              color: _textColor,
            ),
          ),

          const SizedBox(height: 8),

          // Description
          if (widget.data['description'] != null)
            Text(
              widget.data['description'],
              style: TextStyle(
                fontSize: 14,
                color: _textColor.withOpacity(0.8),
              ),
            ),

          const SizedBox(height: 16),

          // Function information
          _buildFunctionInfo(currentFunction),

          const SizedBox(height: 16),

          // Graph visualization
          _buildGraphVisualization(currentFunction),

          const SizedBox(height: 16),

          // Domain and range input
          _buildDomainRangeInput(),

          // Feedback
          if (_showFeedback)
            _buildFeedback(),

          // Widget name tag
          if (widget.data['showNameTag'] ?? true)
            Padding(
              padding: const EdgeInsets.only(top: 8),
              child: Text(
                'InteractiveDomainRangeExplorer',
                style: TextStyle(
                  color: Colors.grey[600],
                  fontSize: 12,
                  fontStyle: FontStyle.italic,
                ),
              ),
            ),
        ],
      ),
    );
  }
}

/// Custom painter for drawing function graphs with domain and range highlighting
class DomainRangeGraphPainter extends CustomPainter {
  final List<Map<String, dynamic>> points;
  final Color color;
  final Color domainColor;
  final Color rangeColor;
  final bool showDomain;
  final bool showRange;
  final double domainMin;
  final double domainMax;
  final double rangeMin;
  final double rangeMax;

  // Constants for graph scaling
  final double minX = -5.0;
  final double maxX = 5.0;
  final double minY = -5.0;
  final double maxY = 5.0;

  DomainRangeGraphPainter({
    required this.points,
    required this.color,
    required this.domainColor,
    required this.rangeColor,
    required this.showDomain,
    required this.showRange,
    required this.domainMin,
    required this.domainMax,
    required this.rangeMin,
    required this.rangeMax,
  });

  @override
  void paint(Canvas canvas, Size size) {
    final paint = Paint()
      ..color = color
      ..strokeWidth = 2.0
      ..style = PaintingStyle.stroke;

    final gridPaint = Paint()
      ..color = Colors.grey[300]!
      ..strokeWidth = 0.5
      ..style = PaintingStyle.stroke;

    final axisPaint = Paint()
      ..color = Colors.black87
      ..strokeWidth = 1.0
      ..style = PaintingStyle.stroke;

    final domainPaint = Paint()
      ..color = domainColor.withOpacity(0.3)
      ..style = PaintingStyle.fill;

    final rangePaint = Paint()
      ..color = rangeColor.withOpacity(0.3)
      ..style = PaintingStyle.fill;

    // Draw grid
    _drawGrid(canvas, size, gridPaint);

    // Draw domain and range highlights
    if (showDomain) {
      _drawDomainHighlight(canvas, size, domainPaint);
    }

    if (showRange) {
      _drawRangeHighlight(canvas, size, rangePaint);
    }

    // Draw axes
    _drawAxes(canvas, size, axisPaint);

    // Draw function
    _drawFunction(canvas, size, paint);

    // Draw points
    _drawPoints(canvas, size, paint);

    // Draw domain and range labels
    _drawDomainRangeLabels(canvas, size);
  }

  void _drawGrid(Canvas canvas, Size size, Paint paint) {
    // Draw vertical grid lines
    for (double x = minX; x <= maxX; x += 1) {
      final screenX = _mapXToScreen(x, size);
      canvas.drawLine(
        Offset(screenX, 0),
        Offset(screenX, size.height),
        paint,
      );
    }

    // Draw horizontal grid lines
    for (double y = minY; y <= maxY; y += 1) {
      final screenY = _mapYToScreen(y, size);
      canvas.drawLine(
        Offset(0, screenY),
        Offset(size.width, screenY),
        paint,
      );
    }
  }

  void _drawAxes(Canvas canvas, Size size, Paint paint) {
    // Draw x-axis
    final yZero = _mapYToScreen(0, size);
    canvas.drawLine(
      Offset(0, yZero),
      Offset(size.width, yZero),
      paint,
    );

    // Draw y-axis
    final xZero = _mapXToScreen(0, size);
    canvas.drawLine(
      Offset(xZero, 0),
      Offset(xZero, size.height),
      paint,
    );

    // Draw axis labels
    final textStyle = TextStyle(
      color: Colors.black87,
      fontSize: 10,
    );
    final textPainter = TextPainter(
      textDirection: TextDirection.ltr,
    );

    // X-axis labels
    for (int i = minX.toInt(); i <= maxX.toInt(); i++) {
      if (i == 0) continue; // Skip zero as it's the origin
      final x = i.toDouble();
      final screenX = _mapXToScreen(x, size);

      textPainter.text = TextSpan(
        text: i.toString(),
        style: textStyle,
      );
      textPainter.layout();
      textPainter.paint(
        canvas,
        Offset(screenX - textPainter.width / 2, yZero + 5),
      );
    }

    // Y-axis labels
    for (int i = minY.toInt(); i <= maxY.toInt(); i++) {
      if (i == 0) continue; // Skip zero as it's the origin
      final y = i.toDouble();
      final screenY = _mapYToScreen(y, size);

      textPainter.text = TextSpan(
        text: i.toString(),
        style: textStyle,
      );
      textPainter.layout();
      textPainter.paint(
        canvas,
        Offset(xZero + 5, screenY - textPainter.height / 2),
      );
    }

    // Origin label
    textPainter.text = TextSpan(
      text: "0",
      style: textStyle,
    );
    textPainter.layout();
    textPainter.paint(
      canvas,
      Offset(xZero + 5, yZero + 5),
    );
  }

  void _drawDomainHighlight(Canvas canvas, Size size, Paint paint) {
    // Map domain bounds to screen coordinates
    final screenDomainMin = _mapXToScreen(domainMin, size);
    final screenDomainMax = _mapXToScreen(domainMax, size);

    // Draw domain highlight rectangle
    final rect = Rect.fromLTRB(
      screenDomainMin,
      0,
      screenDomainMax,
      size.height,
    );
    canvas.drawRect(rect, paint);

    // Draw domain bounds lines
    final boundsPaint = Paint()
      ..color = domainColor
      ..strokeWidth = 2.0
      ..style = PaintingStyle.stroke;

    canvas.drawLine(
      Offset(screenDomainMin, 0),
      Offset(screenDomainMin, size.height),
      boundsPaint,
    );

    canvas.drawLine(
      Offset(screenDomainMax, 0),
      Offset(screenDomainMax, size.height),
      boundsPaint,
    );
  }

  void _drawRangeHighlight(Canvas canvas, Size size, Paint paint) {
    // Map range bounds to screen coordinates
    final screenRangeMin = _mapYToScreen(rangeMin, size);
    final screenRangeMax = _mapYToScreen(rangeMax, size);

    // Draw range highlight rectangle
    final rect = Rect.fromLTRB(
      0,
      screenRangeMax, // Note: screen coordinates are flipped for y
      size.width,
      screenRangeMin,
    );
    canvas.drawRect(rect, paint);

    // Draw range bounds lines
    final boundsPaint = Paint()
      ..color = rangeColor
      ..strokeWidth = 2.0
      ..style = PaintingStyle.stroke;

    canvas.drawLine(
      Offset(0, screenRangeMin),
      Offset(size.width, screenRangeMin),
      boundsPaint,
    );

    canvas.drawLine(
      Offset(0, screenRangeMax),
      Offset(size.width, screenRangeMax),
      boundsPaint,
    );
  }

  void _drawFunction(Canvas canvas, Size size, Paint paint) {
    if (points.isEmpty) return;

    final path = Path();
    bool started = false;

    // Sort points by x value
    final sortedPoints = List<Map<String, dynamic>>.from(points)
      ..sort((a, b) => (a['x'] as num).compareTo(b['x'] as num));

    for (final point in sortedPoints) {
      final screenX = _mapXToScreen(point['x'] as double, size);
      final screenY = _mapYToScreen(point['y'] as double, size);

      // Skip points outside the visible area
      if ((point['x'] as double) < minX ||
          (point['x'] as double) > maxX ||
          (point['y'] as double) < minY ||
          (point['y'] as double) > maxY) {
        continue;
      }

      if (!started) {
        path.moveTo(screenX, screenY);
        started = true;
      } else {
        path.lineTo(screenX, screenY);
      }
    }

    canvas.drawPath(path, paint);
  }

  void _drawPoints(Canvas canvas, Size size, Paint paint) {
    final pointPaint = Paint()
      ..color = color
      ..strokeWidth = 1.0
      ..style = PaintingStyle.fill;

    for (final point in points) {
      // Skip points outside the visible area
      if ((point['x'] as double) < minX ||
          (point['x'] as double) > maxX ||
          (point['y'] as double) < minY ||
          (point['y'] as double) > maxY) {
        continue;
      }

      final screenX = _mapXToScreen(point['x'] as double, size);
      final screenY = _mapYToScreen(point['y'] as double, size);

      canvas.drawCircle(
        Offset(screenX, screenY),
        3,
        pointPaint,
      );
    }
  }

  void _drawDomainRangeLabels(Canvas canvas, Size size) {
    final domainStyle = TextStyle(
      color: domainColor,
      fontSize: 14,
      fontWeight: FontWeight.bold,
    );

    final rangeStyle = TextStyle(
      color: rangeColor,
      fontSize: 14,
      fontWeight: FontWeight.bold,
    );

    final textPainter = TextPainter(
      textDirection: TextDirection.ltr,
    );

    // Domain label
    if (showDomain) {
      textPainter.text = TextSpan(
        text: "Domain",
        style: domainStyle,
      );
      textPainter.layout();
      textPainter.paint(
        canvas,
        Offset(10, 10),
      );
    }

    // Range label
    if (showRange) {
      textPainter.text = TextSpan(
        text: "Range",
        style: rangeStyle,
      );
      textPainter.layout();
      textPainter.paint(
        canvas,
        Offset(size.width - textPainter.width - 10, 10),
      );
    }
  }

  // Map x coordinate from math space to screen space
  double _mapXToScreen(double x, Size size) {
    return size.width * (x - minX) / (maxX - minX);
  }

  // Map y coordinate from math space to screen space
  double _mapYToScreen(double y, Size size) {
    // Note: Screen coordinates have y increasing downward, math has y increasing upward
    return size.height * (1 - (y - minY) / (maxY - minY));
  }

  @override
  bool shouldRepaint(covariant DomainRangeGraphPainter oldDelegate) {
    return oldDelegate.points != points ||
        oldDelegate.color != color ||
        oldDelegate.domainColor != domainColor ||
        oldDelegate.rangeColor != rangeColor ||
        oldDelegate.showDomain != showDomain ||
        oldDelegate.showRange != showRange ||
        oldDelegate.domainMin != domainMin ||
        oldDelegate.domainMax != domainMax ||
        oldDelegate.rangeMin != rangeMin ||
        oldDelegate.rangeMax != rangeMax;
  }
}