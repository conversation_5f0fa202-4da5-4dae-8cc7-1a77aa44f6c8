{"id": "visualizing-geometry", "title": "Visualizing Geometry", "description": "Explore shapes, spaces, and their properties through interactive visualizations.", "order": 3, "lessons": [{"id": "language-of-shapes", "title": "The Language of Shapes", "description": "Identify and understand the fundamental elements of geometry.", "order": 1, "type": "interactive_lesson", "estimatedTimeMinutes": 10, "contentBlocks": [{"id": "los-screen1-intro", "type": "lesson_screen", "order": 1, "estimatedTimeSeconds": 30, "content": {"headline": "Geometry: The Art of Shapes and Space", "body_md": "From the Pyramids of Giza to the device you're holding, geometry is all around us! It's the study of points, lines, shapes, and the space they occupy.", "visual": {"type": "giphy_search", "value": "geometric shapes animation"}, "interactive_element": {"type": "button", "text": "Let's Explore!", "action": "next_screen"}}}, {"id": "los-screen2-points-lines-planes", "type": "lesson_screen", "order": 2, "estimatedTimeSeconds": 70, "content": {"headline": "The Building Blocks: Points, Lines, Planes", "body_md": "*   **Point:** A specific location in space. It has no size (no length, width, or height).\n*   **Line:** A straight path of points that extends infinitely in both directions.\n*   **Plane:** A flat surface that extends infinitely in all directions (like an endless sheet of paper).", "visual": {"type": "local_asset", "value": "assets/images/geometry/points_lines_planes.svg"}, "interactive_element": {"type": "multiple_choice_text", "question_text": "Which of these has zero dimensions?", "options": [{"id": "los2opt1", "text": "Point", "is_correct": true, "feedback_correct": "Correct! A point just marks a location.", "feedback_incorrect": "Think about which one has no length, width, or height."}, {"id": "los2opt2", "text": "Line", "is_correct": false, "feedback_incorrect": "A line has one dimension: length."}, {"id": "los2opt3", "text": "Plane", "is_correct": false, "feedback_incorrect": "A plane has two dimensions: length and width."}], "action_button_text": "What about angles?"}}}, {"id": "los-screen3-angles", "type": "lesson_screen", "order": 3, "estimatedTimeSeconds": 80, "content": {"headline": "Angles: Measuring Turns", "body_md": "An **angle** is formed by two rays (parts of lines) that share a common endpoint called the vertex.\n*   **Acute Angle:** Less than 90°.\n*   **Right Angle:** Exactly 90° (like the corner of a square).\n*   **Obtuse Angle:** Greater than 90° but less than 180°.\n*   **Straight Angle:** Exactly 180° (a straight line).", "visual": {"type": "local_asset", "value": "assets/images/geometry/angle_types.svg"}, "interactive_element": {"type": "drag_drop_angle_sorter", "angles_to_sort": [{"id": "angle1", "degrees": 45, "type": "acute"}, {"id": "angle2", "degrees": 90, "type": "right"}, {"id": "angle3", "degrees": 120, "type": "obtuse"}], "categories": ["Acute", "Right", "Obtuse"], "feedback_correct": "Great sorting!", "action_button_text": "Polygons Next!"}}}, {"id": "los-screen4-polygons", "type": "lesson_screen", "order": 4, "estimatedTimeSeconds": 70, "content": {"headline": "Polygons: Closed Shapes with Straight Sides", "body_md": "A **polygon** is a closed 2D shape made of straight line segments.\n*   **Triangle:** 3 sides\n*   **Quadrilateral:** 4 sides (squares, rectangles, etc.)\n*   **Pentagon:** 5 sides\n*   **Hexagon:** 6 sides", "visual": {"type": "giphy_search", "value": "different polygon shapes"}, "interactive_element": {"type": "text_input", "question_text": "How many sides does an octagon have?", "correct_answer_regex": "^8$", "feedback_correct": "Correct! Like an octopus has 8 tentacles.", "feedback_incorrect": "Think of 'octo' (like octopus).", "action_button_text": "Circles!"}}}, {"id": "los-screen5-circles", "type": "lesson_screen", "order": 5, "estimatedTimeSeconds": 60, "content": {"headline": "The Perfect Round: Circles", "body_md": "A **circle** is a set of all points in a plane that are the same distance (the radius) from a central point.\nKey parts:\n*   **Radius (r):** Distance from center to any point on the circle.\n*   **Diameter (d):** Distance across the circle through the center (d = 2r).\n*   **Circumference (C):** Distance around the circle (C = 2πr).", "visual": {"type": "interactive_circle_properties_viz", "initialRadius": 50.0, "minRadius": 20.0, "maxRadius": 120.0, "circleColor": "#BBDEFB", "radiusColor": "#F44336", "diameterColor": "#4CAF50", "circumferenceColor": "#9C27B0", "showRadius": true, "showDiameter": true, "showCircumference": true, "showLabels": true, "showValues": true, "showPi": true}, "interactive_element": {"type": "button", "text": "3D Shapes?", "action": "next_screen"}}}, {"id": "los-screen6-3d-shapes", "type": "lesson_screen", "order": 6, "estimatedTimeSeconds": 70, "content": {"headline": "Into the Third Dimension: Solid Shapes", "body_md": "Geometry also includes 3D shapes (solids):\n*   **Cube:** 6 square faces.\n*   **Sphere:** Perfectly round, like a ball.\n*   **Cylinder:** Two circular bases and a curved side (like a can).\n*   **Cone:** One circular base and a vertex (like an ice cream cone).", "visual": {"type": "giphy_search", "value": "3d geometric shapes rotating"}, "interactive_element": {"type": "button", "text": "Got the Basics!", "action": "next_screen"}}}, {"id": "los-screen7-recap", "type": "lesson_screen", "order": 7, "estimatedTimeSeconds": 40, "content": {"headline": "Recap: The Language of Shapes", "body_md": "*   Basic elements: Points, Lines, Planes.\n*   Angles measure turns.\n*   Polygons are closed shapes with straight sides.\n*   Circles are perfectly round.\n*   3D shapes add depth!", "interactive_element": {"type": "button", "text": "Next Lesson", "action": "next_lesson"}}}]}, {"id": "transforming-figures", "title": "Transforming Figures", "description": "Discover how shapes move, reflect, and rotate in space.", "order": 2, "type": "interactive_lesson", "estimatedTimeMinutes": 10, "contentBlocks": [{"id": "tf-screen1-intro", "type": "lesson_screen", "order": 1, "estimatedTimeSeconds": 30, "content": {"headline": "Shapes in Motion: Transformations!", "body_md": "Shapes aren't static! They can slide, flip, and turn. These movements are called transformations, and they're key to understanding geometry and art!", "visual": {"type": "giphy_search", "value": "shapes moving transforming"}, "interactive_element": {"type": "button", "text": "Let's Make Shapes Dance!", "action": "next_screen"}}}, {"id": "tf-screen2-translation", "type": "lesson_screen", "order": 2, "estimatedTimeSeconds": 70, "content": {"headline": "Translation: The Slide", "body_md": "A **translation** simply slides a shape from one position to another without turning or resizing it.\n\nImagine pushing a book across a table. That's a translation!", "visual": {"type": "local_asset", "value": "assets/animations/translation_animation.gif"}, "interactive_element": {"type": "translation_interactive_game", "shape": "triangle", "target_position": {"x": 5, "y": -3}, "feedback_correct": "Perfect slide!", "action_button_text": "What's Reflection?"}}}, {"id": "tf-screen3-reflection", "type": "lesson_screen", "order": 3, "estimatedTimeSeconds": 80, "content": {"headline": "Reflection: The Flip", "body_md": "A **reflection** flips a shape over a line (called the line of reflection), creating a mirror image.\n\nThink of your reflection in a mirror!", "visual": {"type": "local_asset", "value": "assets/animations/reflection_animation.gif"}, "interactive_element": {"type": "reflection_interactive_game", "shape": "L_shape", "reflection_line": "y_axis", "feedback_correct": "Spot on mirror image!", "action_button_text": "How About Rotation?"}}}, {"id": "tf-screen4-rotation", "type": "lesson_screen", "order": 4, "estimatedTimeSeconds": 90, "content": {"headline": "Rotation: The Turn", "body_md": "A **rotation** turns a shape around a fixed point (called the center of rotation) by a certain angle.\n\nImagine a spinning wheel!", "visual": {"type": "local_asset", "value": "assets/animations/rotation_animation.gif"}, "interactive_element": {"type": "rotation_interactive_game", "title": "Rotation Interactive Game", "description": "Rotate the shape around the center point by the specified angle.", "shape": "arrow", "center_of_rotation": "origin", "angle_degrees": 90, "direction": "clockwise", "shapeColor": "#2196F3", "rotatedShapeColor": "#2196F380", "centerColor": "#F44336", "textColor": "#212121", "feedback_correct": "Nice turn! You rotated the shape by 90° clockwise.", "action_button_text": "Any other moves?"}}}, {"id": "tf-screen5-dilation", "type": "lesson_screen", "order": 5, "estimatedTimeSeconds": 70, "content": {"headline": "Dilation: Resize It!", "body_md": "A **dilation** changes the size of a shape but not its form. It can enlarge or shrink it.\n\nThe amount of resizing is called the **scale factor**.", "visual": {"type": "giphy_search", "value": "zoom in zoom out"}, "interactive_element": {"type": "dilation_interactive_game", "shape": "square", "scale_factor": 2, "feedback_correct": "Perfectly resized!", "action_button_text": "Let's Combine Them!"}}}, {"id": "tf-screen6-combining-transformations", "type": "lesson_screen", "order": 6, "estimatedTimeSeconds": 80, "content": {"headline": "Transformation Combos!", "body_md": "Often, complex movements are a combination of these basic transformations.\n\nFor example, to get a shape from one spot to another, facing a different way, you might translate it and then rotate it.", "visual": {"type": "unsplash_search", "value": "dance choreography steps"}, "interactive_element": {"type": "button", "text": "Cool!", "action": "next_screen"}}}, {"id": "tf-screen7-recap", "type": "lesson_screen", "order": 7, "estimatedTimeSeconds": 40, "content": {"headline": "Recap: <PERSON><PERSON><PERSON>", "body_md": "*   **Translation:** Slide\n*   **Reflection:** Flip\n*   **Rotation:** Turn\n*   **Dilation:** Resize\n\nThese transformations are fundamental in geometry, art, and computer graphics!", "interactive_element": {"type": "button", "text": "Next Lesson", "action": "next_lesson"}}}]}, {"id": "measuring-the-world", "title": "Measuring the World", "description": "Calculate lengths, areas, and volumes using geometric principles.", "order": 3, "type": "interactive_lesson", "estimatedTimeMinutes": 12, "contentBlocks": [{"id": "mtw-screen1-intro", "type": "lesson_screen", "order": 1, "estimatedTimeSeconds": 30, "content": {"headline": "How Big Is It? Measuring Shapes!", "body_md": "How long is a fence? How much paint for a wall? How much water in a pool? Geometry helps us measure lengths, areas, and volumes!", "visual": {"type": "giphy_search", "value": "measuring tape ruler"}, "interactive_element": {"type": "button", "text": "Let's Start Measuring!", "action": "next_screen"}}}, {"id": "mtw-screen2-perimeter", "type": "lesson_screen", "order": 2, "estimatedTimeSeconds": 70, "content": {"headline": "Perimeter: The Distance Around", "body_md": "The **perimeter** of a 2D shape is the total distance around its boundary.\nFor a rectangle with length (L) and width (W): Perimeter = 2L + 2W.\nFor a square with side (s): Perimeter = 4s.", "visual": {"type": "local_asset", "value": "assets/images/geometry/perimeter_rectangle.svg"}, "interactive_element": {"type": "text_input", "question_text": "A rectangle is 5 units long and 3 units wide. What's its perimeter?", "correct_answer_regex": "^16$", "feedback_correct": "Correct! 2(5) + 2(3) = 10 + 6 = 16 units.", "feedback_incorrect": "Perimeter = 2 × Length + 2 × Width.", "action_button_text": "Area Next!"}}}, {"id": "mtw-screen3-area-rectangle", "type": "lesson_screen", "order": 3, "estimatedTimeSeconds": 60, "content": {"headline": "Area: Space Inside (Rectangles)", "body_md": "The **area** of a 2D shape is the amount of surface it covers.\nFor a rectangle with length (L) and width (W): Area = L × W.\nFor a square with side (s): Area = s².", "visual": {"type": "local_asset", "value": "assets/images/geometry/area_rectangle_grid.svg"}, "interactive_element": {"type": "text_input", "question_text": "A square has a side of 4 units. What's its area?", "correct_answer_regex": "^16$", "feedback_correct": "Spot on! 4 × 4 = 16 square units.", "feedback_incorrect": "Area of a square = side × side.", "action_button_text": "Area of Triangles?"}}}, {"id": "mtw-screen4-area-triangle", "type": "lesson_screen", "order": 4, "estimatedTimeSeconds": 70, "content": {"headline": "Area of a Triangle", "body_md": "For a triangle with base (b) and height (h):\nArea = (1/2) × b × h.\n(The height is the perpendicular distance from the base to the opposite vertex).", "visual": {"type": "local_asset", "value": "assets/images/geometry/area_triangle.svg"}, "interactive_element": {"type": "text_input", "question_text": "A triangle has a base of 10 units and a height of 6 units. Area?", "correct_answer_regex": "^30$", "feedback_correct": "Perfect! (1/2) × 10 × 6 = 30 square units.", "feedback_incorrect": "Area = 0.5 × base × height.", "action_button_text": "Area of Circles?"}}}, {"id": "mtw-screen5-area-circle", "type": "lesson_screen", "order": 5, "estimatedTimeSeconds": 70, "content": {"headline": "Area of a Circle: Pi Time!", "body_md": "For a circle with radius (r):\nArea = π × r² (Pi times radius squared).\nRemember, π (pi) is approximately 3.14159.", "visual": {"type": "giphy_search", "value": "pi symbol math"}, "interactive_element": {"type": "text_input", "question_text": "A circle has a radius of 3 units. What's its area (use π ≈ 3.14)?", "correct_answer_regex": "^(28\\.26|28\\.27)$", "feedback_correct": "Great! π × 3² ≈ 3.14 × 9 ≈ 28.26 square units.", "feedback_incorrect": "Area = π × radius × radius. Use π ≈ 3.14.", "action_button_text": "Let's Go 3D: Volume!"}}}, {"id": "mtw-screen6-volume-cube", "type": "lesson_screen", "order": 6, "estimatedTimeSeconds": 70, "content": {"headline": "Volume: Space Inside 3D Shapes (Cubes)", "body_md": "The **volume** of a 3D shape is the amount of space it occupies.\nFor a cube with side length (s): Volume = s³ (s × s × s).", "visual": {"type": "local_asset", "value": "assets/images/geometry/volume_cube.svg"}, "interactive_element": {"type": "text_input", "question_text": "A cube has a side of 3 units. What's its volume?", "correct_answer_regex": "^27$", "feedback_correct": "Exactly! 3 × 3 × 3 = 27 cubic units.", "feedback_incorrect": "Volume = side × side × side.", "action_button_text": "Volume of Cylinders?"}}}, {"id": "mtw-screen7-volume-cylinder", "type": "lesson_screen", "order": 7, "estimatedTimeSeconds": 80, "content": {"headline": "Volume of a Cylinder", "body_md": "For a cylinder with radius (r) and height (h):\nVolume = Area of base × height = (π × r²) × h.", "visual": {"type": "local_asset", "value": "assets/images/geometry/volume_cylinder.svg"}, "interactive_element": {"type": "text_input", "question_text": "A cylinder has radius 2 units, height 5 units. Volume (use π ≈ 3.14)?", "correct_answer_regex": "^(62\\.8|62\\.80)$", "feedback_correct": "Excellent! (π × 2²) × 5 ≈ (3.14 × 4) × 5 ≈ 12.56 × 5 ≈ 62.8 cubic units.", "feedback_incorrect": "Volume = π × radius² × height. Use π ≈ 3.14.", "action_button_text": "Measurement Mastered!"}}}, {"id": "mtw-screen8-recap", "type": "lesson_screen", "order": 8, "estimatedTimeSeconds": 40, "content": {"headline": "Recap: Measuring Our World", "body_md": "*   **Perimeter:** Distance around (2D).\n*   **Area:** Surface covered (2D) - L×W, ½bh, πr².\n*   **Volume:** Space occupied (3D) - s³, πr²h.\n\nThese formulas help quantify the world around us!", "interactive_element": {"type": "button", "text": "Next Lesson", "action": "next_lesson"}}}]}, {"id": "symmetry-and-patterns-geo", "title": "Symmetry and Patterns in Geometry", "description": "Appreciate the beauty of repeating structures in geometry.", "order": 4, "type": "interactive_lesson", "estimatedTimeMinutes": 9, "contentBlocks": [{"id": "sapg-screen1-intro", "type": "lesson_screen", "order": 1, "estimatedTimeSeconds": 30, "content": {"headline": "Beauty in Balance: Symmetry!", "body_md": "From butterflies to snowflakes, our world is filled with symmetry. Geometry helps us understand and describe this beautiful balance.", "visual": {"type": "giphy_search", "value": "butterfly symmetry snowflake"}, "interactive_element": {"type": "button", "text": "Explore Symmetry!", "action": "next_screen"}}}, {"id": "sapg-screen2-line-symmetry", "type": "lesson_screen", "order": 2, "estimatedTimeSeconds": 70, "content": {"headline": "Line Symmetry (Reflectional Symmetry)", "body_md": "A shape has **line symmetry** if it can be folded along a line so that the two halves match exactly. This line is called the **line of symmetry**.\n\nThink of a heart shape or a human face (approximately!).", "visual": {"type": "local_asset", "value": "assets/images/geometry/line_symmetry_heart.svg"}, "interactive_element": {"type": "draw_line_of_symmetry_game", "shape": "isosceles_triangle", "feedback_correct": "Perfect line of symmetry!", "action_button_text": "What about rotational?"}}}, {"id": "sapg-screen3-rotational-symmetry", "type": "lesson_screen", "order": 3, "estimatedTimeSeconds": 80, "content": {"headline": "Rotational Symmetry", "body_md": "A shape has **rotational symmetry** if it looks the same after being rotated less than a full 360° turn around a central point.\n\nThe **order of rotation** is how many times it matches itself in one full turn.", "visual": {"type": "local_asset", "value": "assets/animations/rotational_symmetry_star.gif"}, "interactive_element": {"type": "multiple_choice_text", "question_text": "A square has rotational symmetry of what order?", "options": [{"id": "sapg3opt1", "text": "Order 1", "is_correct": false, "feedback_incorrect": "Order 1 means it only matches after a full 360° turn."}, {"id": "sapg3opt2", "text": "Order 2", "is_correct": false, "feedback_incorrect": "It matches more than twice! Think 90°, 180°, 270°, 360°."}, {"id": "sapg3opt3", "text": "Order 4", "is_correct": true, "feedback_correct": "Correct! A square looks the same after 90°, 180°, 270°, and 360° rotations.", "feedback_incorrect": "How many times does it fit onto itself in a full spin?"}], "action_button_text": "Geometric Patterns!"}}}, {"id": "sapg-screen4-tessellations", "type": "lesson_screen", "order": 4, "estimatedTimeSeconds": 70, "content": {"headline": "Tessellations: Patterns that Tile", "body_md": "A **tessellation** (or tiling) is a repeating pattern of geometric shapes that cover a plane without any gaps or overlaps.\n\nThink of bathroom tiles or a honeycomb!", "visual": {"type": "giphy_search", "value": "tessellation honeycomb pattern"}, "interactive_element": {"type": "button", "text": "Cool! What makes them work?", "action": "next_screen"}}}, {"id": "sapg-screen5-tessellation-rules", "type": "lesson_screen", "order": 5, "estimatedTimeSeconds": 80, "content": {"headline": "Why Some Shapes Tessellate", "body_md": "For regular polygons to tessellate, the sum of the angles around any vertex point must be 360°.\n*   Squares: Four 90° angles meet (4×90=360°).\n*   Equilateral Triangles: Six 60° angles meet (6×60=360°).\n*   Regular Hexagons: Three 120° angles meet (3×120=360°).\n\nRegular pentagons don't tessellate on their own!", "visual": {"type": "local_asset", "value": "assets/images/geometry/tessellation_examples.svg"}, "interactive_element": {"type": "button", "text": "Symmetry in Nature?", "action": "next_screen"}}}, {"id": "sapg-screen6-symmetry-nature-art", "type": "lesson_screen", "order": 6, "estimatedTimeSeconds": 60, "content": {"headline": "Symmetry & Patterns Everywhere!", "body_md": "Symmetry and patterns are not just math concepts; they are fundamental to:\n*   **Nature:** Flowers, crystals, animals.\n*   **Art & Design:** Architecture, logos, fabric patterns.\n*   **Music:** Repeating choruses and rhythms.", "visual": {"type": "unsplash_search", "value": "nature symmetry art collage"}, "interactive_element": {"type": "button", "text": "I See It!", "action": "next_screen"}}}, {"id": "sapg-screen7-recap", "type": "lesson_screen", "order": 7, "estimatedTimeSeconds": 40, "content": {"headline": "Recap: The Beauty of Balance", "body_md": "*   **Line Symmetry:** Foldable match.\n*   **Rotational Symmetry:** Turnable match.\n*   **Tessellations:** Tiling patterns with no gaps/overlaps.\n\nSymmetry and patterns bring order and beauty to geometry and the world!", "interactive_element": {"type": "button", "text": "Next Lesson", "action": "next_lesson"}}}]}, {"id": "geometry-in-motion", "title": "Geometry in Motion", "description": "Explore dynamic geometric concepts through interactive tools.", "order": 5, "type": "interactive_lesson", "estimatedTimeMinutes": 10, "contentBlocks": [{"id": "gim-screen1-intro", "type": "lesson_screen", "order": 1, "estimatedTimeSeconds": 30, "content": {"headline": "Dynamic Geometry: <PERSON>ha<PERSON> Alive!", "body_md": "Geometry isn't just static figures; it's about how they interact and change. Let's explore some geometric concepts that come alive with motion!", "visual": {"type": "giphy_search", "value": "dynamic animation geometric"}, "interactive_element": {"type": "button", "text": "Set Shapes in Motion!", "action": "next_screen"}}}, {"id": "gim-screen2-angle-sum-triangle", "type": "lesson_screen", "order": 2, "estimatedTimeSeconds": 90, "content": {"headline": "Triangle Angle Sum: Always 180°!", "body_md": "No matter how you stretch or reshape a triangle, the sum of its three interior angles always remains 180°.\n\nTry dragging the vertices of the triangle below. Watch how the individual angles change, but their sum stays constant!", "visual": {"type": "interactive_triangle_angle_sum", "initial_shape": "acute_triangle"}, "interactive_element": {"type": "static_text_display", "text_template": "Angle A: {{angleA}}°, Angle B: {{angleB}}°, Angle C: {{angleC}}°\nSum: {{sumAngles}}°"}, "action_button_text": "That's a Constant!"}}, {"id": "gim-screen3-pythagorean-theorem-viz", "type": "lesson_screen", "order": 3, "estimatedTimeSeconds": 100, "content": {"headline": "Visualizing Pythagoras: a² + b² = c²", "body_md": "For any right-angled triangle, the square of the hypotenuse (the side opposite the right angle, 'c') is equal to the sum of the squares of the other two sides ('a' and 'b').\n\nWatch how the areas of the squares on sides 'a' and 'b' combine to equal the area of the square on side 'c'.", "visual": {"type": "interactive_pythagorean_theorem_viz", "title": "Pythagorean Theorem Visualizer", "description": "Drag the vertices to see how a² + b² = c² holds for any right-angled triangle.", "triangleColor": "#BBDEFB", "squareAColor": "#FFCDD2", "squareBColor": "#C8E6C9", "squareCColor": "#E1BEE7", "showSquares": true, "showAreas": true, "showFormula": true, "animateSquares": true}, "interactive_element": {"type": "button", "text": "See it in Action!", "action": "next_screen"}}}, {"id": "gim-screen4-circle-properties-dynamic", "type": "lesson_screen", "order": 4, "estimatedTimeSeconds": 90, "content": {"headline": "Circle Dynamics: Radius, Diameter, Circumference", "body_md": "Explore how changing the radius of a circle affects its diameter and circumference.\nNotice that the ratio Circumference / Diameter is always π (pi)!", "visual": {"type": "interactive_circle_properties_viz"}, "interactive_element": {"type": "static_text_display", "text_template": "Radius: {{radius}}, Diameter: {{diameter}}, Circumference: {{circumference}}\nC/D Ratio: {{ratio_c_d}}"}, "action_button_text": "Pi is Constant!"}}, {"id": "gim-screen5-tessellation-creator-simple", "type": "lesson_screen", "order": 5, "estimatedTimeSeconds": 100, "content": {"headline": "Simple Tessellation Creator", "body_md": "Try arranging squares, equilateral triangles, or hexagons to see how they tessellate (tile a plane without gaps or overlaps). What happens if you try to tile with regular pentagons?", "visual": {"type": "interactive_tessellation_creator", "shapes": ["square", "equilateral_triangle", "hexagon", "pentagon"]}, "interactive_element": {"type": "button", "text": "Exploring Tilings!", "action": "next_screen"}}}, {"id": "gim-screen6-recap", "type": "lesson_screen", "order": 6, "estimatedTimeSeconds": 40, "content": {"headline": "Recap: Geometry in Motion", "body_md": "*   Geometric properties can remain constant even when shapes change (e.g., triangle angle sum).\n*   Visualizing theorems (like Pythagor<PERSON>) makes them intuitive.\n*   Interactive tools help explore concepts like π and tessellations dynamically.", "interactive_element": {"type": "button", "text": "On to the Module Test!", "action": "next_lesson"}}}]}, {"id": "shape-shifter-challenge-test", "title": "Shape Shifter Challenge", "description": "Manipulate geometric figures to solve visual puzzles and spatial reasoning tasks.", "order": 6, "type": "module_test_interactive", "estimatedTimeMinutes": 10, "contentBlocks": [{"id": "ssc-q0-intro", "type": "question_screen", "order": 1, "estimatedTimeSeconds": 20, "content": {"headline": "Shape Shifter Challenge: Begin!", "body_md": "Ready to test your spatial reasoning? Manipulate shapes, identify properties, and solve these geometric puzzles!", "visual": {"type": "giphy_search", "value": "geometric puzzle transforming"}, "interactive_element": {"type": "button", "text": "Start Challenge!", "action": "next_screen"}}}, {"id": "ssc-q1-transformation-id", "type": "question_screen", "order": 2, "estimatedTimeSeconds": 120, "content": {"headline": "Puzzle 1: Identify the Transformation", "body_md": "Observe the two figures. What single transformation (Translation, Reflection, Rotation, or Dilation) maps Figure A to Figure B?", "visual": {"type": "interactive_transformation_identification_game", "title": "Transformation Identification Game", "description": "Identify the transformation that maps Figure A to Figure B.", "figure_a_src": "assets/images/geo_puzzles/figure_A1.svg", "figure_b_src": "assets/images/geo_puzzles/figure_B1_rotated.svg", "correct_transformation": "rotation", "transformation_options": ["translation", "rotation", "reflection", "dilation"], "primaryColor": "#2196F3", "correctColor": "#4CAF50", "incorrectColor": "#F44336", "correctFeedback": "Correct! The transformation is a rotation.", "incorrectFeedback": "Not quite. Look at how the orientation changes."}, "interactive_element": {"type": "button", "text": "Next Puzzle", "action": "next_screen"}}}, {"id": "ssc-q2-area-composite-shape", "type": "question_screen", "order": 3, "estimatedTimeSeconds": 180, "content": {"headline": "Puzzle 2: Area of a Composite Shape", "body_md": "The figure below is made of a square and a triangle. Calculate its total area.\n(Grid lines are 1 unit apart. Square side = 4 units. Triangle base = 4 units, height = 3 units).", "visual": {"type": "local_asset", "value": "assets/images/geo_puzzles/composite_shape_square_triangle.svg"}, "interactive_element": {"type": "text_input", "placeholder": "Enter total area", "correct_answer_regex": "^22$", "feedback_correct": "Excellent! Area of square (16) + Area of triangle (6) = 22 square units.", "feedback_incorrect": "Calculate the area of the square and the triangle separately, then add them.", "action_button_text": "Symmetry Sleuth!"}}}, {"id": "ssc-q3-symmetry-count", "type": "question_screen", "order": 4, "estimatedTimeSeconds": 150, "content": {"headline": "Puzzle 3: Symmetry Count", "body_md": "Consider a regular hexagon. How many lines of symmetry does it have?", "visual": {"type": "local_asset", "value": "assets/images/geometry/regular_hexagon.svg"}, "interactive_element": {"type": "multiple_choice_text", "options": [{"id": "sscq3opt1", "text": "3", "is_correct": false, "feedback_incorrect": "It has more! Think about lines through opposite vertices AND lines through midpoints of opposite sides."}, {"id": "sscq3opt2", "text": "4", "is_correct": false, "feedback_incorrect": "A square has 4, but a hexagon has more."}, {"id": "sscq3opt3", "text": "6", "is_correct": true, "feedback_correct": "Correct! A regular hexagon has 6 lines of symmetry.", "feedback_incorrect": "Try drawing them out. Some go through vertices, some through sides."}, {"id": "sscq3opt4", "text": "12", "is_correct": false, "feedback_incorrect": "That's too many lines of symmetry for a hexagon."}], "action_button_text": "Finish Challenge"}}}, {"id": "ssc-q4-end", "type": "lesson_screen", "order": 5, "estimatedTimeSeconds": 30, "content": {"headline": "Shape Shifter Challenge Complete!", "body_md": "Fantastic work on the geometric puzzles! Your ability to visualize and manipulate shapes is impressive.", "visual": {"type": "giphy_search", "value": "geometric success puzzle solved"}, "interactive_element": {"type": "button", "text": "Back to Course Overview", "action": "module_complete"}}}]}]}