import 'package:flutter/material.dart';
import 'dart:math' as math;
import 'dart:math' show Random;

/// Custom painter for block visualization
class BlockVisualizationPainter extends CustomPainter {
  final int firstNumber;
  final int secondNumber;
  final String operation;
  final int currentStep;
  final double animationValue;
  final List<List<bool>> blockVisibility;
  final Color primaryColor;
  final Color secondaryColor;
  final Color textColor;
  final List<Color> blockColors;

  BlockVisualizationPainter({
    required this.firstNumber,
    required this.secondNumber,
    required this.operation,
    required this.currentStep,
    required this.animationValue,
    required this.blockVisibility,
    required this.primaryColor,
    required this.secondaryColor,
    required this.textColor,
    required this.blockColors,
  });

  @override
  void paint(Canvas canvas, Size size) {
    final double blockSize = 30;
    final double spacing = 5;
    final double startX = 20;
    final double startY = size.height / 2 - blockSize - 10;

    // Draw first number blocks
    for (int i = 0; i < firstNumber; i++) {
      if (blockVisibility[i][0]) {
        final double x = startX + (i % 10) * (blockSize + spacing);
        final double y = startY - (i ~/ 10) * (blockSize + spacing);

        final bool isHighlighted = blockVisibility[i][1];
        final Color blockColor = isHighlighted
            ? secondaryColor
            : primaryColor;

        final Rect rect = Rect.fromLTWH(x, y, blockSize, blockSize);
        canvas.drawRect(
          rect,
          Paint()..color = blockColor,
        );

        // Draw number inside block
        final textPainter = TextPainter(
          text: TextSpan(
            text: (i + 1).toString(),
            style: TextStyle(
              color: Colors.white,
              fontSize: 12,
              fontWeight: FontWeight.bold,
            ),
          ),
          textDirection: TextDirection.ltr,
        );
        textPainter.layout();
        textPainter.paint(
          canvas,
          Offset(
            x + (blockSize - textPainter.width) / 2,
            y + (blockSize - textPainter.height) / 2,
          ),
        );
      }
    }

    // For addition, draw second number blocks
    if (operation == 'Addition' && currentStep >= 1) {
      final double secondStartY = startY + blockSize + 20;

      for (int i = 0; i < secondNumber; i++) {
        final double progress = currentStep == 1 ? animationValue : 1.0;
        final double x = startX + (i % 10) * (blockSize + spacing);
        final double y = secondStartY - (i ~/ 10) * (blockSize + spacing);

        // For animation, slide in from right
        final double animatedX = size.width - (size.width - x) * progress;

        final Rect rect = Rect.fromLTWH(animatedX, y, blockSize, blockSize);
        canvas.drawRect(
          rect,
          Paint()..color = secondaryColor,
        );

        // Draw number inside block
        final textPainter = TextPainter(
          text: TextSpan(
            text: (firstNumber + i + 1).toString(),
            style: TextStyle(
              color: Colors.white,
              fontSize: 12,
              fontWeight: FontWeight.bold,
            ),
          ),
          textDirection: TextDirection.ltr,
        );
        textPainter.layout();
        textPainter.paint(
          canvas,
          Offset(
            animatedX + (blockSize - textPainter.width) / 2,
            y + (blockSize - textPainter.height) / 2,
          ),
        );
      }
    }

    // For subtraction, cross out blocks
    if (operation == 'Subtraction' && currentStep >= 1) {
      final double progress = currentStep == 1 ? animationValue : 1.0;

      for (int i = firstNumber - secondNumber; i < firstNumber; i++) {
        if (blockVisibility[i][0]) {
          final double x = startX + (i % 10) * (blockSize + spacing);
          final double y = startY - (i ~/ 10) * (blockSize + spacing);

          // Draw X over the block
          final Paint linePaint = Paint()
            ..color = Colors.red
            ..strokeWidth = 2 * progress
            ..style = PaintingStyle.stroke;

          canvas.drawLine(
            Offset(x, y),
            Offset(x + blockSize * progress, y + blockSize * progress),
            linePaint,
          );

          canvas.drawLine(
            Offset(x + blockSize, y),
            Offset(x + blockSize - blockSize * progress, y + blockSize * progress),
            linePaint,
          );
        }
      }
    }

    // Draw result in step 2
    if (currentStep >= 2) {
      final int result = operation == 'Addition'
          ? firstNumber + secondNumber
          : firstNumber - secondNumber;

      final textPainter = TextPainter(
        text: TextSpan(
          text: 'Result: $result',
          style: TextStyle(
            color: textColor,
            fontSize: 18,
            fontWeight: FontWeight.bold,
          ),
        ),
        textDirection: TextDirection.ltr,
      );
      textPainter.layout();
      textPainter.paint(
        canvas,
        Offset(
          size.width - textPainter.width - 20,
          size.height - textPainter.height - 20,
        ),
      );
    }
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) {
    return true;
  }
}

/// Custom painter for number line visualization
class NumberLineVisualizationPainter extends CustomPainter {
  final int firstNumber;
  final int secondNumber;
  final String operation;
  final int currentStep;
  final double animationValue;
  final Color primaryColor;
  final Color secondaryColor;
  final Color textColor;

  NumberLineVisualizationPainter({
    required this.firstNumber,
    required this.secondNumber,
    required this.operation,
    required this.currentStep,
    required this.animationValue,
    required this.primaryColor,
    required this.secondaryColor,
    required this.textColor,
  });

  @override
  void paint(Canvas canvas, Size size) {
    final double lineY = size.height / 2;
    final double maxNumber = math.max(firstNumber + secondNumber, firstNumber) + 2;
    final double pixelsPerUnit = (size.width - 40) / maxNumber;

    // Draw the number line
    final Paint linePaint = Paint()
      ..color = Colors.grey.shade400
      ..strokeWidth = 2
      ..style = PaintingStyle.stroke;

    canvas.drawLine(
      Offset(20, lineY),
      Offset(size.width - 20, lineY),
      linePaint,
    );

    // Draw tick marks and labels
    for (int i = 0; i <= maxNumber.toInt(); i++) {
      final double x = 20 + i * pixelsPerUnit;

      // Draw tick
      canvas.drawLine(
        Offset(x, lineY - 10),
        Offset(x, lineY + 10),
        linePaint,
      );

      // Draw label
      final textPainter = TextPainter(
        text: TextSpan(
          text: i.toString(),
          style: TextStyle(
            color: textColor,
            fontSize: 12,
          ),
        ),
        textDirection: TextDirection.ltr,
        textAlign: TextAlign.center,
      );
      textPainter.layout();
      textPainter.paint(
        canvas,
        Offset(x - textPainter.width / 2, lineY + 15),
      );
    }

    // Draw marker for first number
    if (currentStep >= 0) {
      final double x = 20 + firstNumber * pixelsPerUnit;

      canvas.drawCircle(
        Offset(x, lineY),
        10,
        Paint()..color = primaryColor,
      );

      // Draw label above
      final textPainter = TextPainter(
        text: TextSpan(
          text: firstNumber.toString(),
          style: TextStyle(
            color: Colors.white,
            fontSize: 12,
            fontWeight: FontWeight.bold,
          ),
        ),
        textDirection: TextDirection.ltr,
        textAlign: TextAlign.center,
      );
      textPainter.layout();
      textPainter.paint(
        canvas,
        Offset(x - textPainter.width / 2, lineY - 5),
      );
    }

    // Draw animation for second number
    if (currentStep >= 1) {
      final double startX = 20 + firstNumber * pixelsPerUnit;
      double endX;

      if (operation == 'Addition') {
        endX = 20 + (firstNumber + secondNumber) * pixelsPerUnit;
      } else {
        endX = 20 + (firstNumber - secondNumber) * pixelsPerUnit;
      }

      final double progress = currentStep == 1 ? animationValue : 1.0;
      final double currentX = startX + (endX - startX) * progress;

      // Draw arrow showing movement
      final Paint arrowPaint = Paint()
        ..color = secondaryColor
        ..strokeWidth = 2
        ..style = PaintingStyle.stroke;

      canvas.drawLine(
        Offset(startX, lineY - 20),
        Offset(currentX, lineY - 20),
        arrowPaint,
      );

      // Draw arrowhead
      final Path arrowPath = Path();
      arrowPath.moveTo(currentX, lineY - 20);
      arrowPath.lineTo(currentX - 5, lineY - 25);
      arrowPath.lineTo(currentX - 5, lineY - 15);
      arrowPath.close();

      canvas.drawPath(
        arrowPath,
        Paint()..color = secondaryColor,
      );

      // Draw operation text
      final String opText = operation == 'Addition' ? '+$secondNumber' : '-$secondNumber';
      final textPainter = TextPainter(
        text: TextSpan(
          text: opText,
          style: TextStyle(
            color: secondaryColor,
            fontSize: 14,
            fontWeight: FontWeight.bold,
          ),
        ),
        textDirection: TextDirection.ltr,
      );
      textPainter.layout();
      textPainter.paint(
        canvas,
        Offset(
          startX + (endX - startX) / 2 - textPainter.width / 2,
          lineY - 40,
        ),
      );
    }

    // Draw result in step 2
    if (currentStep >= 2) {
      final int result = operation == 'Addition'
          ? firstNumber + secondNumber
          : firstNumber - secondNumber;

      final double x = 20 + result * pixelsPerUnit;

      canvas.drawCircle(
        Offset(x, lineY),
        10,
        Paint()..color = Colors.green,
      );

      // Draw label above
      final textPainter = TextPainter(
        text: TextSpan(
          text: result.toString(),
          style: TextStyle(
            color: Colors.white,
            fontSize: 12,
            fontWeight: FontWeight.bold,
          ),
        ),
        textDirection: TextDirection.ltr,
        textAlign: TextAlign.center,
      );
      textPainter.layout();
      textPainter.paint(
        canvas,
        Offset(x - textPainter.width / 2, lineY - 5),
      );
    }
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) {
    return true;
  }
}

/// Custom painter for object visualization
class ObjectVisualizationPainter extends CustomPainter {
  final int firstNumber;
  final int secondNumber;
  final String operation;
  final int currentStep;
  final double animationValue;
  final List<Offset> objectPositions;
  final Color primaryColor;
  final Color secondaryColor;
  final Color textColor;

  ObjectVisualizationPainter({
    required this.firstNumber,
    required this.secondNumber,
    required this.operation,
    required this.currentStep,
    required this.animationValue,
    required this.objectPositions,
    required this.primaryColor,
    required this.secondaryColor,
    required this.textColor,
  });

  @override
  void paint(Canvas canvas, Size size) {
    final double objectSize = 20;

    // Draw first number objects
    for (int i = 0; i < firstNumber; i++) {
      final Offset position = objectPositions[i];

      // Draw object (apple shape)
      _drawApple(canvas, position, objectSize, primaryColor);
    }

    // For addition, draw second number objects
    if (operation == 'Addition' && currentStep >= 1) {
      final double progress = currentStep == 1 ? animationValue : 1.0;

      for (int i = 0; i < secondNumber; i++) {
        final Offset position = objectPositions[firstNumber + i];

        // For animation, fade in
        final Color color = secondaryColor.withAlpha((255 * progress).toInt());

        // Draw object
        _drawApple(canvas, position, objectSize, color);
      }
    }

    // For subtraction, cross out objects
    if (operation == 'Subtraction' && currentStep >= 1) {
      final double progress = currentStep == 1 ? animationValue : 1.0;

      for (int i = firstNumber - secondNumber; i < firstNumber; i++) {
        final Offset position = objectPositions[i];

        // Draw X over the object
        final Paint linePaint = Paint()
          ..color = Colors.red
          ..strokeWidth = 2 * progress
          ..style = PaintingStyle.stroke;

        canvas.drawLine(
          Offset(position.dx - objectSize/2, position.dy - objectSize/2),
          Offset(position.dx + objectSize/2, position.dy + objectSize/2),
          linePaint,
        );

        canvas.drawLine(
          Offset(position.dx + objectSize/2, position.dy - objectSize/2),
          Offset(position.dx - objectSize/2, position.dy + objectSize/2),
          linePaint,
        );
      }
    }

    // Draw result in step 2
    if (currentStep >= 2) {
      final int result = operation == 'Addition'
          ? firstNumber + secondNumber
          : firstNumber - secondNumber;

      final textPainter = TextPainter(
        text: TextSpan(
          text: 'Result: $result',
          style: TextStyle(
            color: textColor,
            fontSize: 18,
            fontWeight: FontWeight.bold,
          ),
        ),
        textDirection: TextDirection.ltr,
      );
      textPainter.layout();
      textPainter.paint(
        canvas,
        Offset(
          size.width - textPainter.width - 20,
          size.height - textPainter.height - 20,
        ),
      );
    }
  }

  void _drawApple(Canvas canvas, Offset position, double size, Color color) {
    // Draw apple body (circle)
    canvas.drawCircle(
      position,
      size / 2,
      Paint()..color = color,
    );

    // Draw apple stem
    final Paint stemPaint = Paint()
      ..color = Colors.brown
      ..strokeWidth = 2;

    canvas.drawLine(
      Offset(position.dx, position.dy - size / 2),
      Offset(position.dx, position.dy - size / 2 - 5),
      stemPaint,
    );

    // Draw leaf
    final Path leafPath = Path();
    leafPath.moveTo(position.dx, position.dy - size / 2 - 3);
    leafPath.quadraticBezierTo(
      position.dx + 5, position.dy - size / 2 - 8,
      position.dx + 8, position.dy - size / 2 - 5,
    );
    leafPath.quadraticBezierTo(
      position.dx + 5, position.dy - size / 2 - 3,
      position.dx, position.dy - size / 2 - 3,
    );

    canvas.drawPath(
      leafPath,
      Paint()..color = Colors.green,
    );
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) {
    return true;
  }
}

/// A widget that visualizes addition and subtraction operations
/// for elementary math education.
class InteractiveAdditionSubtractionVisualizerWidget extends StatefulWidget {
  final Map<String, dynamic> data;
  final Function(bool)? onStateChanged;
  final Color primaryColor;
  final Color secondaryColor;
  final Color backgroundColor;
  final Color textColor;

  const InteractiveAdditionSubtractionVisualizerWidget({
    Key? key,
    this.data = const {},
    this.onStateChanged,
    this.primaryColor = Colors.blue,
    this.secondaryColor = Colors.orange,
    this.backgroundColor = Colors.white,
    this.textColor = Colors.black87,
  }) : super(key: key);

  @override
  State<InteractiveAdditionSubtractionVisualizerWidget> createState() =>
      _InteractiveAdditionSubtractionVisualizerWidgetState();
}

class _InteractiveAdditionSubtractionVisualizerWidgetState
    extends State<InteractiveAdditionSubtractionVisualizerWidget>
    with SingleTickerProviderStateMixin {
  // Controllers
  late AnimationController _animationController;
  late Animation<double> _animation;
  final TextEditingController _firstNumberController = TextEditingController();
  final TextEditingController _secondNumberController = TextEditingController();

  // State variables
  String _operation = 'Addition';
  String _visualizationType = 'Blocks';
  bool _isAnimating = false;
  bool _isCompleted = false;
  int _currentStep = 0;
  List<String> _steps = [];
  String? _errorMessage;
  String? _feedbackMessage;
  bool _showQuestion = false;
  String? _selectedAnswer;
  String? _correctAnswer;
  List<String> _answerOptions = [];

  // Operation values
  int _firstNumber = 0;
  int _secondNumber = 0;
  int _result = 0;

  // Animation tracking
  List<List<bool>> _blockVisibility = [];
  List<Offset> _objectPositions = [];

  // Constants
  final int _maxNumber = 20;
  final List<String> _operations = ['Addition', 'Subtraction'];
  final List<String> _visualizationTypes = ['Blocks', 'Number Line', 'Objects'];
  final List<Color> _blockColors = [
    Colors.red,
    Colors.blue,
    Colors.green,
    Colors.orange,
    Colors.purple,
  ];

  @override
  void initState() {
    super.initState();

    // Initialize animation controller
    _animationController = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 1500),
    );

    _animation = CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeInOut,
    );

    // Add listener to animation controller
    _animationController.addStatusListener((status) {
      if (status == AnimationStatus.completed) {
        if (_currentStep < _steps.length - 1) {
          setState(() {
            _currentStep++;
          });
          _animationController.reset();
          _animationController.forward();
        } else {
          setState(() {
            _isAnimating = false;
            _showQuestion = true;
          });
        }
      }
    });

    // Set initial values
    _firstNumberController.text = '5';
    _secondNumberController.text = '3';

    // Initialize with default values
    _updateCalculation();
  }

  @override
  void dispose() {
    _animationController.dispose();
    _firstNumberController.dispose();
    _secondNumberController.dispose();
    super.dispose();
  }

  void _updateCalculation() {
    try {
      _firstNumber = int.parse(_firstNumberController.text);
      _secondNumber = int.parse(_secondNumberController.text);

      // Validate input
      if (_firstNumber < 0 || _firstNumber > _maxNumber ||
          _secondNumber < 0 || _secondNumber > _maxNumber) {
        setState(() {
          _errorMessage = 'Please enter numbers between 0 and $_maxNumber';
        });
        return;
      }

      // For subtraction, ensure first number is larger
      if (_operation == 'Subtraction' && _firstNumber < _secondNumber) {
        setState(() {
          _errorMessage = 'For subtraction, the first number must be larger than the second';
        });
        return;
      }

      setState(() {
        _errorMessage = null;
        _feedbackMessage = null;
        _showQuestion = false;
        _selectedAnswer = null;
        _currentStep = 0;
      });

      // Calculate result
      if (_operation == 'Addition') {
        _result = _firstNumber + _secondNumber;
        _generateAdditionSteps();
      } else {
        _result = _firstNumber - _secondNumber;
        _generateSubtractionSteps();
      }

      // Generate question and answers
      _generateQuestion();

      // Initialize visualization data
      _initializeVisualization();

    } catch (e) {
      setState(() {
        _errorMessage = 'Please enter valid numbers';
      });
    }
  }

  void _generateAdditionSteps() {
    _steps = [
      'Start with $_firstNumber',
      'Add $_secondNumber to it',
      'The result is $_result'
    ];
  }

  void _generateSubtractionSteps() {
    _steps = [
      'Start with $_firstNumber',
      'Subtract $_secondNumber from it',
      'The result is $_result'
    ];
  }

  void _generateQuestion() {
    // Generate answer options (including the correct one)
    _correctAnswer = _result.toString();

    // Generate 3 wrong answers that are close to the correct one
    List<String> options = [];
    if (_correctAnswer != null) {
      options.add(_correctAnswer!);
    }
    Random random = Random();

    while (options.length < 4) {
      int offset = random.nextInt(5) + 1;
      if (random.nextBool()) offset = -offset;
      int wrongAnswer = _result + offset;
      if (wrongAnswer >= 0) {
        String wrongAnswerStr = wrongAnswer.toString();
        if (!options.contains(wrongAnswerStr)) {
          options.add(wrongAnswerStr);
        }
      }
    }

    options.shuffle();
    _answerOptions = options;
  }

  void _initializeVisualization() {
    switch (_visualizationType) {
      case 'Blocks':
        _initializeBlockVisualization();
        break;
      case 'Number Line':
        _initializeNumberLineVisualization();
        break;
      case 'Objects':
        _initializeObjectVisualization();
        break;
    }
  }

  void _initializeBlockVisualization() {
    // Create a grid of blocks for visualization
    int maxBlocks = math.max(_firstNumber + _secondNumber, _firstNumber);

    // Initialize all blocks as invisible
    _blockVisibility = List.generate(
      maxBlocks,
      (_) => [false, false], // [isVisible, isHighlighted]
    );

    // Make the first number blocks visible
    for (int i = 0; i < _firstNumber; i++) {
      _blockVisibility[i][0] = true;
    }
  }

  void _initializeNumberLineVisualization() {
    // No initialization needed for number line
  }

  void _initializeObjectVisualization() {
    // Create positions for objects (e.g., apples, stars)
    int maxObjects = math.max(_firstNumber + _secondNumber, _firstNumber);

    _objectPositions = List.generate(
      maxObjects,
      (index) {
        // Arrange objects in a grid or circle
        double angle = (index / maxObjects) * 2 * math.pi;
        double radius = 80;
        return Offset(
          100 + radius * math.cos(angle),
          100 + radius * math.sin(angle),
        );
      },
    );
  }

  void _startAnimation() {
    if (_steps.isEmpty) return;

    setState(() {
      _currentStep = 0;
      _isAnimating = true;
      _showQuestion = false;
    });

    _animationController.reset();
    _animationController.forward();
  }

  void _stopAnimation() {
    setState(() {
      _isAnimating = false;
    });
    _animationController.stop();
  }

  void _resetAnimation() {
    setState(() {
      _currentStep = 0;
      _isAnimating = false;
      _showQuestion = false;
      _selectedAnswer = null;
      _feedbackMessage = null;
    });
    _animationController.reset();
    _initializeVisualization();
  }

  void _checkAnswer(String answer) {
    setState(() {
      _selectedAnswer = answer;
      if (answer == _correctAnswer) {
        _feedbackMessage = 'Correct! Great job!';
        _isCompleted = true;
      } else {
        _feedbackMessage = 'Not quite. Try again!';
      }
    });

    // Notify parent of completion status
    widget.onStateChanged?.call(_isCompleted);
  }

  void _generateRandomProblem() {
    Random random = Random();
    int first = random.nextInt(_maxNumber) + 1;
    int second = _operation == 'Subtraction'
        ? random.nextInt(first)
        : random.nextInt(_maxNumber) + 1;

    setState(() {
      _firstNumberController.text = first.toString();
      _secondNumberController.text = second.toString();
    });

    _updateCalculation();
  }

  Widget _buildInputControls() {
    return Column(
      children: [
        // Operation selector
        Row(
          children: [
            Text(
              'Operation:',
              style: TextStyle(
                fontWeight: FontWeight.bold,
                color: widget.textColor,
              ),
            ),
            const SizedBox(width: 16),
            DropdownButton<String>(
              value: _operation,
              onChanged: (String? newValue) {
                if (newValue != null) {
                  setState(() {
                    _operation = newValue;
                  });
                  _updateCalculation();
                }
              },
              items: _operations.map<DropdownMenuItem<String>>((String value) {
                return DropdownMenuItem<String>(
                  value: value,
                  child: Text(value),
                );
              }).toList(),
            ),
            const Spacer(),
            // Visualization type selector
            Text(
              'Visualization:',
              style: TextStyle(
                fontWeight: FontWeight.bold,
                color: widget.textColor,
              ),
            ),
            const SizedBox(width: 16),
            DropdownButton<String>(
              value: _visualizationType,
              onChanged: (String? newValue) {
                if (newValue != null) {
                  setState(() {
                    _visualizationType = newValue;
                  });
                  _updateCalculation();
                }
              },
              items: _visualizationTypes.map<DropdownMenuItem<String>>((String value) {
                return DropdownMenuItem<String>(
                  value: value,
                  child: Text(value),
                );
              }).toList(),
            ),
          ],
        ),

        const SizedBox(height: 16),

        // Number inputs
        Row(
          children: [
            Expanded(
              child: TextField(
                controller: _firstNumberController,
                keyboardType: TextInputType.number,
                decoration: InputDecoration(
                  labelText: 'First Number',
                  border: OutlineInputBorder(),
                  contentPadding: EdgeInsets.symmetric(horizontal: 12, vertical: 8),
                ),
                onChanged: (_) => _updateCalculation(),
              ),
            ),
            Padding(
              padding: const EdgeInsets.symmetric(horizontal: 16),
              child: Text(
                _operation == 'Addition' ? '+' : '-',
                style: TextStyle(
                  fontSize: 24,
                  fontWeight: FontWeight.bold,
                  color: widget.primaryColor,
                ),
              ),
            ),
            Expanded(
              child: TextField(
                controller: _secondNumberController,
                keyboardType: TextInputType.number,
                decoration: InputDecoration(
                  labelText: 'Second Number',
                  border: OutlineInputBorder(),
                  contentPadding: EdgeInsets.symmetric(horizontal: 12, vertical: 8),
                ),
                onChanged: (_) => _updateCalculation(),
              ),
            ),
            const SizedBox(width: 16),
            ElevatedButton(
              onPressed: _generateRandomProblem,
              style: ElevatedButton.styleFrom(
                backgroundColor: widget.secondaryColor,
                padding: EdgeInsets.symmetric(horizontal: 12, vertical: 8),
              ),
              child: Text('Random'),
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildVisualizationArea() {
    return Container(
      height: 200,
      width: double.infinity,
      decoration: BoxDecoration(
        color: Colors.grey.shade50,
        borderRadius: BorderRadius.circular(8),
        border: Border.all(
          color: _isCompleted ? Colors.green : Colors.grey.shade300,
          width: _isCompleted ? 2 : 1,
        ),
      ),
      child: AnimatedBuilder(
        animation: _animation,
        builder: (context, child) {
          return CustomPaint(
            painter: _getVisualizationPainter(),
            child: Container(),
          );
        },
      ),
    );
  }

  CustomPainter _getVisualizationPainter() {
    switch (_visualizationType) {
      case 'Blocks':
        return BlockVisualizationPainter(
          firstNumber: _firstNumber,
          secondNumber: _secondNumber,
          operation: _operation,
          currentStep: _currentStep,
          animationValue: _isAnimating ? _animation.value : 1.0,
          blockVisibility: _blockVisibility,
          primaryColor: widget.primaryColor,
          secondaryColor: widget.secondaryColor,
          textColor: widget.textColor,
          blockColors: _blockColors,
        );
      case 'Number Line':
        return NumberLineVisualizationPainter(
          firstNumber: _firstNumber,
          secondNumber: _secondNumber,
          operation: _operation,
          currentStep: _currentStep,
          animationValue: _isAnimating ? _animation.value : 1.0,
          primaryColor: widget.primaryColor,
          secondaryColor: widget.secondaryColor,
          textColor: widget.textColor,
        );
      case 'Objects':
        return ObjectVisualizationPainter(
          firstNumber: _firstNumber,
          secondNumber: _secondNumber,
          operation: _operation,
          currentStep: _currentStep,
          animationValue: _isAnimating ? _animation.value : 1.0,
          objectPositions: _objectPositions,
          primaryColor: widget.primaryColor,
          secondaryColor: widget.secondaryColor,
          textColor: widget.textColor,
        );
      default:
        return BlockVisualizationPainter(
          firstNumber: _firstNumber,
          secondNumber: _secondNumber,
          operation: _operation,
          currentStep: _currentStep,
          animationValue: _isAnimating ? _animation.value : 1.0,
          blockVisibility: _blockVisibility,
          primaryColor: widget.primaryColor,
          secondaryColor: widget.secondaryColor,
          textColor: widget.textColor,
          blockColors: _blockColors,
        );
    }
  }

  Widget _buildAnimationControls() {
    return Row(
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        IconButton(
          icon: Icon(_isAnimating ? Icons.pause : Icons.play_arrow),
          onPressed: _isAnimating ? _stopAnimation : _startAnimation,
          color: widget.primaryColor,
          iconSize: 32,
        ),
        IconButton(
          icon: const Icon(Icons.replay),
          onPressed: _resetAnimation,
          color: widget.primaryColor,
          iconSize: 32,
        ),
      ],
    );
  }

  Widget _buildQuestionSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          _operation == 'Addition'
              ? 'What is $_firstNumber + $_secondNumber?'
              : 'What is $_firstNumber - $_secondNumber?',
          style: TextStyle(
            fontSize: 18,
            fontWeight: FontWeight.bold,
            color: widget.textColor,
          ),
        ),
        const SizedBox(height: 12),
        Wrap(
          spacing: 8,
          runSpacing: 8,
          children: _answerOptions.map((option) {
            bool isSelected = _selectedAnswer == option;
            bool isCorrect = option == _correctAnswer;

            Color buttonColor = isSelected
                ? (isCorrect ? Colors.green : Colors.red)
                : widget.primaryColor;

            return ElevatedButton(
              onPressed: _selectedAnswer == null ? () => _checkAnswer(option) : null,
              style: ElevatedButton.styleFrom(
                backgroundColor: buttonColor,
                padding: EdgeInsets.symmetric(horizontal: 16, vertical: 8),
              ),
              child: Text(
                option,
                style: TextStyle(
                  fontSize: 16,
                  color: Colors.white,
                ),
              ),
            );
          }).toList(),
        ),
      ],
    );
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: widget.backgroundColor,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withAlpha(50),
            spreadRadius: 1,
            blurRadius: 3,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Title and controls
          Text(
            'Addition & Subtraction Visualizer',
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
              color: widget.textColor,
            ),
          ),

          const SizedBox(height: 16),

          // Input controls
          _buildInputControls(),

          if (_errorMessage != null)
            Padding(
              padding: const EdgeInsets.only(top: 8.0),
              child: Text(
                _errorMessage!,
                style: TextStyle(
                  color: Colors.red,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ),

          const SizedBox(height: 16),

          // Visualization area
          _buildVisualizationArea(),

          const SizedBox(height: 16),

          // Step description
          if (_steps.isNotEmpty && _currentStep < _steps.length)
            Text(
              _steps[_currentStep],
              style: TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.bold,
                color: widget.primaryColor,
              ),
            ),

          const SizedBox(height: 16),

          // Animation controls
          _buildAnimationControls(),

          const SizedBox(height: 16),

          // Question and feedback
          if (_showQuestion) _buildQuestionSection(),

          if (_feedbackMessage != null)
            Padding(
              padding: const EdgeInsets.only(top: 8.0),
              child: Text(
                _feedbackMessage!,
                style: TextStyle(
                  color: _selectedAnswer == _correctAnswer
                      ? Colors.green
                      : Colors.red,
                  fontWeight: FontWeight.bold,
                  fontSize: 16,
                ),
              ),
            ),
        ],
      ),
    );
  }
}
