import 'package:flutter/material.dart';
import '../interactive_system_solver_widget.dart';

/// A simple test app for the Interactive System Solver widget
void main() {
  runApp(const SystemSolverTestApp());
}

class SystemSolverTestApp extends StatelessWidget {
  const SystemSolverTestApp({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return MaterialApp(
      title: 'System Solver Test',
      theme: ThemeData(
        primarySwatch: Colors.blue,
        visualDensity: VisualDensity.adaptivePlatformDensity,
      ),
      home: const SystemSolverTestScreen(),
    );
  }
}

class SystemSolverTestScreen extends StatelessWidget {
  const SystemSolverTestScreen({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    // Sample data for testing
    final testData = {
      'title': 'System of Equations Solver Test',
      'description': 'Test the system solver widget with various methods.',
      'primaryColor': '#2196F3',
      'secondaryColor': '#FF9800',
      'accentColor': '#4CAF50',
      'textColor': '#212121',
      'initialMethod': 'substitution',
      'initialEquations': [
        {
          'a': 1.0,
          'b': 1.0,
          'c': 10.0,
        },
        {
          'a': 2.0,
          'b': -1.0,
          'c': 5.0,
        },
      ],
      'showNameTag': true,
    };

    return Scaffold(
      appBar: AppBar(
        title: const Text('System Solver Test'),
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'Interactive System Solver Widget',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 16),
            const Text(
              'This is a test for the Interactive System Solver widget. '
              'You can solve systems of linear equations using different methods.',
              style: TextStyle(fontSize: 14),
            ),
            const SizedBox(height: 24),
            InteractiveSystemSolverWidget(
              data: testData,
              onStateChanged: (isCompleted) {
                print('Widget state changed: $isCompleted');
              },
            ),
            const SizedBox(height: 24),
            const Text(
              'Test Instructions:',
              style: TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 8),
            const Text(
              '1. Try solving the system using the Substitution method\n'
              '2. Try solving the system using the Elimination method\n'
              '3. Try solving the system using the Graphical method\n'
              '4. Try modifying the equations and solving again\n'
              '5. Verify that the step-by-step solution is correct',
              style: TextStyle(fontSize: 14),
            ),
          ],
        ),
      ),
    );
  }
}
