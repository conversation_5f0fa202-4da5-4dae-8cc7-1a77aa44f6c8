import 'package:flutter/material.dart';
import 'dart:math' as math;

/// A widget that allows students to graph linear inequalities on a coordinate plane.
class InteractiveInequalityGrapherWidget extends StatefulWidget {
  final Map<String, dynamic> data;
  final Function(bool)? onStateChanged;
  final Color primaryColor;
  final Color secondaryColor;
  final Color backgroundColor;
  final Color textColor;
  final Color successColor;
  final Color errorColor;
  final Color shadingColor;

  const InteractiveInequalityGrapherWidget({
    super.key,
    this.data = const {},
    this.onStateChanged,
    this.primaryColor = Colors.blue,
    this.secondaryColor = Colors.orange,
    this.backgroundColor = Colors.white,
    this.textColor = Colors.black87,
    this.successColor = Colors.green,
    this.errorColor = Colors.red,
    this.shadingColor = Colors.blue,
  });

  @override
  State<InteractiveInequalityGrapherWidget> createState() =>
      _InteractiveInequalityGrapherWidgetState();
}

class _InteractiveInequalityGrapherWidgetState
    extends State<InteractiveInequalityGrapherWidget> with SingleTickerProviderStateMixin {
  // State variables
  bool _isCompleted = false;
  int _currentInequalityIndex = 0;
  List<InequalityData> _inequalities = [];
  late InequalityData _currentInequality;

  // Graphing variables
  double _slope = 1.0;
  double _yIntercept = 0.0;
  String _inequalityType = '>';
  bool _isLineInclusive = false;
  bool _isShaded = true;
  bool _isUpperHalfShaded = true;

  // Coordinate plane settings
  double _minX = -10.0;
  double _maxX = 10.0;
  double _minY = -10.0;
  double _maxY = 10.0;

  // Interactive variables
  String _userInequality = '';
  TextEditingController _inequalityController = TextEditingController();
  String? _feedbackMessage;
  bool _isCorrect = false;

  // Animation controller for feedback
  late AnimationController _animationController;
  late Animation<double> _animation;

  @override
  void initState() {
    super.initState();
    _initializeInequalities();
    _currentInequality = _inequalities[_currentInequalityIndex];
    _updateGraphFromInequality(_currentInequality);

    // Initialize animation controller
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 300),
      vsync: this,
    );

    _animation = CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeInOut,
    );
  }

  @override
  void dispose() {
    _inequalityController.dispose();
    _animationController.dispose();
    super.dispose();
  }

  void _initializeInequalities() {
    // Check if inequalities are provided in the data
    if (widget.data.containsKey('inequalities') &&
        widget.data['inequalities'] is List &&
        widget.data['inequalities'].isNotEmpty) {

      final inequalitiesData = widget.data['inequalities'] as List;
      for (final inequalityData in inequalitiesData) {
        if (inequalityData is Map<String, dynamic>) {
          final inequality = InequalityData.fromJson(inequalityData);
          _inequalities.add(inequality);
        }
      }
    }

    // If no inequalities were provided, create default ones
    if (_inequalities.isEmpty) {
      _inequalities = [
        InequalityData(
          inequality: 'y > 2x + 1',
          description: 'Graph the inequality y > 2x + 1',
          slope: 2.0,
          yIntercept: 1.0,
          inequalityType: '>',
          isLineInclusive: false,
          isUpperHalfShaded: true,
        ),
        InequalityData(
          inequality: 'y ≤ -x + 3',
          description: 'Graph the inequality y ≤ -x + 3',
          slope: -1.0,
          yIntercept: 3.0,
          inequalityType: '≤',
          isLineInclusive: true,
          isUpperHalfShaded: true,
        ),
        InequalityData(
          inequality: 'y < 0.5x - 2',
          description: 'Graph the inequality y < 0.5x - 2',
          slope: 0.5,
          yIntercept: -2.0,
          inequalityType: '<',
          isLineInclusive: false,
          isUpperHalfShaded: false,
        ),
        InequalityData(
          inequality: 'y ≥ -2x',
          description: 'Graph the inequality y ≥ -2x',
          slope: -2.0,
          yIntercept: 0.0,
          inequalityType: '≥',
          isLineInclusive: true,
          isUpperHalfShaded: true,
        ),
      ];
    }
  }

  void _updateGraphFromInequality(InequalityData inequality) {
    setState(() {
      _slope = inequality.slope;
      _yIntercept = inequality.yIntercept;
      _inequalityType = inequality.inequalityType;
      _isLineInclusive = inequality.isLineInclusive;
      _isUpperHalfShaded = inequality.isUpperHalfShaded;
    });
  }

  void _checkUserInequality() {
    if (_userInequality.isEmpty) return;

    // Parse the user's inequality
    bool isCorrect = _parseAndCheckInequality(_userInequality);

    setState(() {
      _isCorrect = isCorrect;

      if (isCorrect) {
        _feedbackMessage = 'Correct! That\'s the right inequality.';
      } else {
        _feedbackMessage = 'Not quite. Try again or check the solution.';
      }

      // Clear the input field
      _userInequality = '';
      _inequalityController.clear();
    });
  }

  bool _parseAndCheckInequality(String userInequality) {
    // This is a simplified parser for linear inequalities
    // In a real implementation, you would use a more robust parser

    // Normalize the inequality string
    String normalized = userInequality.replaceAll(' ', '').toLowerCase();

    // Check if it matches the current inequality
    String targetInequality = _currentInequality.inequality.replaceAll(' ', '').toLowerCase();

    // Direct comparison (simplified)
    if (normalized == targetInequality) {
      return true;
    }

    // TODO: Implement more sophisticated parsing and comparison

    return false;
  }

  void _nextInequality() {
    if (_currentInequalityIndex < _inequalities.length - 1) {
      setState(() {
        _currentInequalityIndex++;
        _currentInequality = _inequalities[_currentInequalityIndex];
        _updateGraphFromInequality(_currentInequality);
        _feedbackMessage = null;
        _isCorrect = false;
      });
    } else {
      // All inequalities completed
      setState(() {
        _isCompleted = true;
      });
      widget.onStateChanged?.call(true);
    }
  }

  void _previousInequality() {
    if (_currentInequalityIndex > 0) {
      setState(() {
        _currentInequalityIndex--;
        _currentInequality = _inequalities[_currentInequalityIndex];
        _updateGraphFromInequality(_currentInequality);
        _feedbackMessage = null;
        _isCorrect = false;
      });
    }
  }

  void _resetWidget() {
    setState(() {
      _currentInequalityIndex = 0;
      _currentInequality = _inequalities[_currentInequalityIndex];
      _updateGraphFromInequality(_currentInequality);
      _feedbackMessage = null;
      _isCorrect = false;
      _isCompleted = false;
    });
    widget.onStateChanged?.call(false);
  }

  void _updateSlope(double value) {
    setState(() {
      _slope = value;
    });
  }

  void _updateYIntercept(double value) {
    setState(() {
      _yIntercept = value;
    });
  }

  void _updateInequalityType(String type) {
    setState(() {
      _inequalityType = type;
      _isLineInclusive = type == '≤' || type == '≥';
    });
  }

  void _toggleShading() {
    setState(() {
      _isUpperHalfShaded = !_isUpperHalfShaded;
    });
  }

  void _checkGraphSettings() {
    // Check if the graph settings match the target inequality
    bool slopeMatch = (_slope - _currentInequality.slope).abs() < 0.1;
    bool yInterceptMatch = (_yIntercept - _currentInequality.yIntercept).abs() < 0.1;
    bool typeMatch = _inequalityType == _currentInequality.inequalityType;
    bool shadingMatch = _isUpperHalfShaded == _currentInequality.isUpperHalfShaded;

    bool isCorrect = slopeMatch && yInterceptMatch && typeMatch && shadingMatch;

    setState(() {
      _isCorrect = isCorrect;

      if (isCorrect) {
        _feedbackMessage = 'Correct! Your graph matches the inequality.';
      } else {
        _feedbackMessage = 'Not quite. Check your slope, y-intercept, inequality type, or shading.';
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    if (_isCompleted) {
      return _buildCompletionScreen();
    }

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Title and description
        Text(
          'Inequality Grapher',
          style: TextStyle(
            fontSize: 20,
            fontWeight: FontWeight.bold,
            color: widget.textColor,
          ),
        ),
        const SizedBox(height: 8),
        Text(
          _currentInequality.description,
          style: TextStyle(
            fontSize: 16,
            color: widget.textColor,
          ),
        ),

        const SizedBox(height: 16),

        // Coordinate plane with inequality graph
        Container(
          height: 300,
          decoration: BoxDecoration(
            color: widget.backgroundColor,
            borderRadius: BorderRadius.circular(8),
            border: Border.all(color: widget.primaryColor),
          ),
          child: ClipRRect(
            borderRadius: BorderRadius.circular(8),
            child: CustomPaint(
              painter: InequalityGraphPainter(
                minX: _minX,
                maxX: _maxX,
                minY: _minY,
                maxY: _maxY,
                slope: _slope,
                yIntercept: _yIntercept,
                inequalityType: _inequalityType,
                isLineInclusive: _isLineInclusive,
                isUpperHalfShaded: _isUpperHalfShaded,
                primaryColor: widget.primaryColor,
                secondaryColor: widget.secondaryColor,
                shadingColor: widget.shadingColor.withOpacity(0.2),
                textColor: widget.textColor,
              ),
              child: Container(),
            ),
          ),
        ),

        const SizedBox(height: 16),

        // Graph controls
        Text(
          'Adjust the graph:',
          style: TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.w500,
            color: widget.textColor,
          ),
        ),

        const SizedBox(height: 8),

        // Slope slider
        Row(
          children: [
            SizedBox(width: 80, child: Text('Slope:', style: TextStyle(color: widget.textColor))),
            Expanded(
              child: Slider(
                value: _slope,
                min: -5.0,
                max: 5.0,
                divisions: 100,
                label: _slope.toStringAsFixed(1),
                onChanged: _updateSlope,
                activeColor: widget.primaryColor,
              ),
            ),
            SizedBox(
              width: 50,
              child: Text(
                _slope.toStringAsFixed(1),
                style: TextStyle(color: widget.textColor),
              ),
            ),
          ],
        ),

        // Y-intercept slider
        Row(
          children: [
            SizedBox(width: 80, child: Text('Y-intercept:', style: TextStyle(color: widget.textColor))),
            Expanded(
              child: Slider(
                value: _yIntercept,
                min: -10.0,
                max: 10.0,
                divisions: 200,
                label: _yIntercept.toStringAsFixed(1),
                onChanged: _updateYIntercept,
                activeColor: widget.primaryColor,
              ),
            ),
            SizedBox(
              width: 50,
              child: Text(
                _yIntercept.toStringAsFixed(1),
                style: TextStyle(color: widget.textColor),
              ),
            ),
          ],
        ),

        // Inequality type selector
        Row(
          children: [
            SizedBox(width: 80, child: Text('Inequality:', style: TextStyle(color: widget.textColor))),
            DropdownButton<String>(
              value: _inequalityType,
              items: ['<', '>', '≤', '≥'].map((String value) {
                return DropdownMenuItem<String>(
                  value: value,
                  child: Text(value),
                );
              }).toList(),
              onChanged: (String? newValue) {
                if (newValue != null) {
                  _updateInequalityType(newValue);
                }
              },
              dropdownColor: widget.backgroundColor,
            ),
            const SizedBox(width: 16),
            TextButton.icon(
              icon: const Icon(Icons.swap_vert),
              label: const Text('Toggle Shading'),
              onPressed: _toggleShading,
              style: TextButton.styleFrom(
                foregroundColor: widget.secondaryColor,
              ),
            ),
          ],
        ),

        const SizedBox(height: 16),

        // Check button
        ElevatedButton(
          onPressed: _checkGraphSettings,
          style: ElevatedButton.styleFrom(
            backgroundColor: widget.primaryColor,
            foregroundColor: Colors.white,
          ),
          child: const Text('Check My Graph'),
        ),

        const SizedBox(height: 16),

        // Feedback message
        if (_feedbackMessage != null) ...[
          Container(
            padding: const EdgeInsets.all(12),
            decoration: BoxDecoration(
              color: _isCorrect
                  ? widget.successColor.withOpacity(0.1)
                  : widget.errorColor.withOpacity(0.1),
              borderRadius: BorderRadius.circular(8),
              border: Border.all(
                color: _isCorrect ? widget.successColor : widget.errorColor,
              ),
            ),
            child: Row(
              children: [
                Icon(
                  _isCorrect ? Icons.check_circle : Icons.error,
                  color: _isCorrect ? widget.successColor : widget.errorColor,
                ),
                const SizedBox(width: 8),
                Expanded(
                  child: Text(
                    _feedbackMessage!,
                    style: TextStyle(
                      color: _isCorrect ? widget.successColor : widget.errorColor,
                    ),
                  ),
                ),
              ],
            ),
          ),
          const SizedBox(height: 16),
        ],

        // Navigation buttons
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            if (_currentInequalityIndex > 0)
              ElevatedButton.icon(
                icon: const Icon(Icons.arrow_back),
                label: const Text('Previous'),
                onPressed: _previousInequality,
                style: ElevatedButton.styleFrom(
                  backgroundColor: widget.secondaryColor,
                  foregroundColor: Colors.white,
                ),
              )
            else
              const SizedBox(),
            ElevatedButton.icon(
              icon: const Icon(Icons.arrow_forward),
              label: const Text('Next'),
              onPressed: _isCorrect ? _nextInequality : null,
              style: ElevatedButton.styleFrom(
                backgroundColor: widget.primaryColor,
                foregroundColor: Colors.white,
                disabledBackgroundColor: Colors.grey.shade300,
              ),
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildCompletionScreen() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.check_circle,
            color: widget.successColor,
            size: 64,
          ),
          const SizedBox(height: 16),
          Text(
            'Great job!',
            style: TextStyle(
              fontSize: 24,
              fontWeight: FontWeight.bold,
              color: widget.textColor,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            'You\'ve completed all the inequality graphing problems.',
            textAlign: TextAlign.center,
            style: TextStyle(
              fontSize: 16,
              color: widget.textColor,
            ),
          ),
          const SizedBox(height: 24),
          ElevatedButton.icon(
            icon: const Icon(Icons.refresh),
            label: const Text('Start Over'),
            onPressed: _resetWidget,
            style: ElevatedButton.styleFrom(
              backgroundColor: widget.primaryColor,
              foregroundColor: Colors.white,
              padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
            ),
          ),
        ],
      ),
    );
  }
}

/// Data class for inequality problems
class InequalityData {
  final String inequality;
  final String description;
  final double slope;
  final double yIntercept;
  final String inequalityType;
  final bool isLineInclusive;
  final bool isUpperHalfShaded;

  InequalityData({
    required this.inequality,
    required this.description,
    required this.slope,
    required this.yIntercept,
    required this.inequalityType,
    required this.isLineInclusive,
    required this.isUpperHalfShaded,
  });

  factory InequalityData.fromJson(Map<String, dynamic> json) {
    return InequalityData(
      inequality: json['inequality'] ?? '',
      description: json['description'] ?? '',
      slope: json['slope']?.toDouble() ?? 1.0,
      yIntercept: json['yIntercept']?.toDouble() ?? 0.0,
      inequalityType: json['inequalityType'] ?? '>',
      isLineInclusive: json['isLineInclusive'] ?? false,
      isUpperHalfShaded: json['isUpperHalfShaded'] ?? true,
    );
  }
}

/// Custom painter for drawing the inequality graph
class InequalityGraphPainter extends CustomPainter {
  final double minX;
  final double maxX;
  final double minY;
  final double maxY;
  final double slope;
  final double yIntercept;
  final String inequalityType;
  final bool isLineInclusive;
  final bool isUpperHalfShaded;
  final Color primaryColor;
  final Color secondaryColor;
  final Color shadingColor;
  final Color textColor;

  InequalityGraphPainter({
    required this.minX,
    required this.maxX,
    required this.minY,
    required this.maxY,
    required this.slope,
    required this.yIntercept,
    required this.inequalityType,
    required this.isLineInclusive,
    required this.isUpperHalfShaded,
    required this.primaryColor,
    required this.secondaryColor,
    required this.shadingColor,
    required this.textColor,
  });

  @override
  void paint(Canvas canvas, Size size) {
    final double width = size.width;
    final double height = size.height;

    // Calculate scale factors
    final double xScale = width / (maxX - minX);
    final double yScale = height / (maxY - minY);

    // Set up paints
    final Paint gridPaint = Paint()
      ..color = textColor.withOpacity(0.1)
      ..strokeWidth = 1.0;

    final Paint axisPaint = Paint()
      ..color = textColor.withOpacity(0.5)
      ..strokeWidth = 2.0;

    final Paint linePaint = Paint()
      ..color = primaryColor
      ..strokeWidth = 3.0
      ..style = isLineInclusive ? PaintingStyle.stroke : PaintingStyle.stroke;

    final Paint shadingPaint = Paint()
      ..color = shadingColor
      ..style = PaintingStyle.fill;

    final TextPainter textPainter = TextPainter(
      textDirection: TextDirection.ltr,
      textAlign: TextAlign.center,
    );

    // Draw grid
    _drawGrid(canvas, size, gridPaint, xScale, yScale);

    // Draw axes
    _drawAxes(canvas, size, axisPaint, xScale, yScale);

    // Draw inequality line
    _drawInequalityLine(canvas, size, linePaint, xScale, yScale);

    // Draw shading
    _drawShading(canvas, size, shadingPaint, xScale, yScale);

    // Draw labels
    _drawLabels(canvas, size, textPainter, xScale, yScale);
  }

  void _drawGrid(Canvas canvas, Size size, Paint paint, double xScale, double yScale) {
    final double width = size.width;
    final double height = size.height;

    // Draw vertical grid lines
    for (double x = minX; x <= maxX; x += 1.0) {
      final double xPos = (x - minX) * xScale;
      canvas.drawLine(
        Offset(xPos, 0),
        Offset(xPos, height),
        paint,
      );
    }

    // Draw horizontal grid lines
    for (double y = minY; y <= maxY; y += 1.0) {
      final double yPos = height - (y - minY) * yScale;
      canvas.drawLine(
        Offset(0, yPos),
        Offset(width, yPos),
        paint,
      );
    }
  }

  void _drawAxes(Canvas canvas, Size size, Paint paint, double xScale, double yScale) {
    final double width = size.width;
    final double height = size.height;

    // X-axis
    final double yAxisPos = height - (-minY) * yScale;
    canvas.drawLine(
      Offset(0, yAxisPos),
      Offset(width, yAxisPos),
      paint,
    );

    // Y-axis
    final double xAxisPos = (-minX) * xScale;
    canvas.drawLine(
      Offset(xAxisPos, 0),
      Offset(xAxisPos, height),
      paint,
    );
  }

  void _drawInequalityLine(Canvas canvas, Size size, Paint paint, double xScale, double yScale) {
    final double width = size.width;
    final double height = size.height;

    // Calculate two points on the line
    final double x1 = minX;
    final double y1 = slope * x1 + yIntercept;
    final double x2 = maxX;
    final double y2 = slope * x2 + yIntercept;

    // Convert to canvas coordinates
    final double x1Canvas = (x1 - minX) * xScale;
    final double y1Canvas = height - (y1 - minY) * yScale;
    final double x2Canvas = (x2 - minX) * xScale;
    final double y2Canvas = height - (y2 - minY) * yScale;

    // Draw the line
    canvas.drawLine(
      Offset(x1Canvas, y1Canvas),
      Offset(x2Canvas, y2Canvas),
      paint,
    );

    // Draw dashed line if not inclusive
    if (!isLineInclusive) {
      final Paint dashedPaint = Paint()
        ..color = primaryColor
        ..strokeWidth = 1.0
        ..style = PaintingStyle.stroke;

      final Path dashPath = Path();
      dashPath.moveTo(x1Canvas, y1Canvas);
      dashPath.lineTo(x2Canvas, y2Canvas);

      canvas.drawPath(
        dashPath,
        dashedPaint,
      );
    }
  }

  void _drawShading(Canvas canvas, Size size, Paint paint, double xScale, double yScale) {
    final double width = size.width;
    final double height = size.height;

    // Create a path for the shaded region
    final Path shadingPath = Path();

    // Calculate points for the line
    final double x1 = minX;
    final double y1 = slope * x1 + yIntercept;
    final double x2 = maxX;
    final double y2 = slope * x2 + yIntercept;

    // Convert to canvas coordinates
    final double x1Canvas = (x1 - minX) * xScale;
    final double y1Canvas = height - (y1 - minY) * yScale;
    final double x2Canvas = (x2 - minX) * xScale;
    final double y2Canvas = height - (y2 - minY) * yScale;

    // Determine shading direction based on inequality type and isUpperHalfShaded
    bool shadeAbove = (inequalityType == '>' || inequalityType == '≥') == isUpperHalfShaded;

    // Create the shading path
    shadingPath.moveTo(x1Canvas, y1Canvas);
    shadingPath.lineTo(x2Canvas, y2Canvas);

    if (shadeAbove) {
      shadingPath.lineTo(width, 0);
      shadingPath.lineTo(0, 0);
    } else {
      shadingPath.lineTo(width, height);
      shadingPath.lineTo(0, height);
    }

    shadingPath.close();

    // Draw the shaded region
    canvas.drawPath(shadingPath, paint);
  }

  void _drawLabels(Canvas canvas, Size size, TextPainter textPainter, double xScale, double yScale) {
    final double width = size.width;
    final double height = size.height;

    // Draw x-axis labels
    for (double x = minX; x <= maxX; x += 2.0) {
      if (x == 0) continue; // Skip origin

      final double xPos = (x - minX) * xScale;

      textPainter.text = TextSpan(
        text: x.toInt().toString(),
        style: TextStyle(
          color: textColor.withOpacity(0.7),
          fontSize: 10,
        ),
      );

      textPainter.layout();
      textPainter.paint(
        canvas,
        Offset(xPos - textPainter.width / 2, height - (-minY) * yScale + 5),
      );
    }

    // Draw y-axis labels
    for (double y = minY; y <= maxY; y += 2.0) {
      if (y == 0) continue; // Skip origin

      final double yPos = height - (y - minY) * yScale;

      textPainter.text = TextSpan(
        text: y.toInt().toString(),
        style: TextStyle(
          color: textColor.withOpacity(0.7),
          fontSize: 10,
        ),
      );

      textPainter.layout();
      textPainter.paint(
        canvas,
        Offset((-minX) * xScale + 5, yPos - textPainter.height / 2),
      );
    }

    // Draw origin label
    textPainter.text = TextSpan(
      text: '0',
      style: TextStyle(
        color: textColor.withOpacity(0.7),
        fontSize: 10,
      ),
    );

    textPainter.layout();
    textPainter.paint(
      canvas,
      Offset(
        (-minX) * xScale - textPainter.width - 5,
        height - (-minY) * yScale + 5,
      ),
    );
  }

  @override
  bool shouldRepaint(covariant InequalityGraphPainter oldDelegate) {
    return oldDelegate.minX != minX ||
        oldDelegate.maxX != maxX ||
        oldDelegate.minY != minY ||
        oldDelegate.maxY != maxY ||
        oldDelegate.slope != slope ||
        oldDelegate.yIntercept != yIntercept ||
        oldDelegate.inequalityType != inequalityType ||
        oldDelegate.isLineInclusive != isLineInclusive ||
        oldDelegate.isUpperHalfShaded != isUpperHalfShaded ||
        oldDelegate.primaryColor != primaryColor ||
        oldDelegate.secondaryColor != secondaryColor ||
        oldDelegate.shadingColor != shadingColor ||
        oldDelegate.textColor != textColor;
  }
}
