import 'package:flutter/material.dart';
import 'dart:math' as math;

/// A widget that helps users identify whether a relation is a function
/// using the vertical line test and other criteria
class InteractiveFunctionIdentifierWidget extends StatefulWidget {
  /// The data for this widget
  final Map<String, dynamic> data;

  /// Optional callback when the widget state changes
  final Function(bool isCompleted)? onStateChanged;

  const InteractiveFunctionIdentifierWidget({
    Key? key,
    required this.data,
    this.onStateChanged,
  }) : super(key: key);

  /// Factory constructor to create the widget from JSON data
  factory InteractiveFunctionIdentifierWidget.fromData(Map<String, dynamic> data) {
    return InteractiveFunctionIdentifierWidget(
      data: data,
    );
  }

  @override
  State<InteractiveFunctionIdentifierWidget> createState() => _InteractiveFunctionIdentifierWidgetState();
}

class _InteractiveFunctionIdentifierWidgetState extends State<InteractiveFunctionIdentifierWidget> with SingleTickerProviderStateMixin {
  // Current relation index
  int _currentRelationIndex = 0;

  // List of relations to identify
  late List<Map<String, dynamic>> _relations;

  // User's answer
  bool? _userAnswer;

  // Whether feedback is shown
  bool _showFeedback = false;

  // Whether the widget is completed
  bool _isCompleted = false;

  // Whether to show explanation
  bool _showExplanation = false;

  // Whether vertical line test is active
  bool _verticalLineTestActive = false;

  // Position of vertical line
  double _verticalLinePosition = 0.0;

  // Colors
  late Color _primaryColor;
  late Color _secondaryColor;
  late Color _accentColor;
  late Color _backgroundColor;
  late Color _textColor;

  // Animation controller for transitions
  late AnimationController _animationController;
  late Animation<double> _animation;

  @override
  void initState() {
    super.initState();

    // Initialize colors
    _primaryColor = _parseColor(widget.data['primary_color']) ?? Colors.blue;
    _secondaryColor = _parseColor(widget.data['secondary_color']) ?? Colors.orange;
    _accentColor = _parseColor(widget.data['accent_color']) ?? Colors.green;
    _backgroundColor = _parseColor(widget.data['background_color']) ?? Colors.white;
    _textColor = _parseColor(widget.data['text_color']) ?? Colors.black87;

    // Initialize animation controller
    _animationController = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 500),
    );

    _animation = CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeInOut,
    );

    // Initialize relations
    _relations = widget.data['relations'] != null
        ? List<Map<String, dynamic>>.from(widget.data['relations'])
        : _getDefaultRelations();
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  // Parse color from hex string
  Color? _parseColor(String? hexString) {
    if (hexString == null) return null;

    hexString = hexString.replaceAll('#', '');
    if (hexString.length == 6) {
      hexString = 'FF$hexString';
    }

    return Color(int.parse(hexString, radix: 16));
  }

  // Get default relations if none provided
  List<Map<String, dynamic>> _getDefaultRelations() {
    return [
      {
        'name': 'Parabola',
        'equation': 'y = x²',
        'type': 'graph',
        'points': [
          {'x': -2, 'y': 4},
          {'x': -1, 'y': 1},
          {'x': 0, 'y': 0},
          {'x': 1, 'y': 1},
          {'x': 2, 'y': 4},
        ],
        'is_function': true,
        'explanation': 'This is a function because each x-value corresponds to exactly one y-value. The vertical line test confirms this - any vertical line will intersect the graph at most once.',
      },
      {
        'name': 'Circle',
        'equation': 'x² + y² = 1',
        'type': 'graph',
        'points': _generateCirclePoints(),
        'is_function': false,
        'explanation': 'This is not a function because some x-values correspond to two different y-values. The vertical line test confirms this - a vertical line can intersect the graph twice.',
      },
      {
        'name': 'Absolute Value',
        'equation': 'y = |x|',
        'type': 'graph',
        'points': [
          {'x': -2, 'y': 2},
          {'x': -1, 'y': 1},
          {'x': 0, 'y': 0},
          {'x': 1, 'y': 1},
          {'x': 2, 'y': 2},
        ],
        'is_function': true,
        'explanation': 'This is a function because each x-value corresponds to exactly one y-value. The vertical line test confirms this - any vertical line will intersect the graph at most once.',
      },
      {
        'name': 'Horizontal Line',
        'equation': 'y = 3',
        'type': 'graph',
        'points': [
          {'x': -2, 'y': 3},
          {'x': -1, 'y': 3},
          {'x': 0, 'y': 3},
          {'x': 1, 'y': 3},
          {'x': 2, 'y': 3},
        ],
        'is_function': true,
        'explanation': 'This is a function because each x-value corresponds to exactly one y-value (which is always 3). The vertical line test confirms this - any vertical line will intersect the graph exactly once.',
      },
      {
        'name': 'Vertical Line',
        'equation': 'x = 2',
        'type': 'graph',
        'points': [
          {'x': 2, 'y': -2},
          {'x': 2, 'y': -1},
          {'x': 2, 'y': 0},
          {'x': 2, 'y': 1},
          {'x': 2, 'y': 2},
        ],
        'is_function': false,
        'explanation': 'This is not a function because the x-value 2 corresponds to multiple y-values. The vertical line test confirms this - a vertical line at x = 2 intersects the graph multiple times.',
      },
    ];
  }

  // Generate points for a circle
  List<Map<String, dynamic>> _generateCirclePoints() {
    final List<Map<String, dynamic>> points = [];
    for (double angle = 0; angle < 2 * math.pi; angle += 0.1) {
      points.add({
        'x': math.cos(angle),
        'y': math.sin(angle),
      });
    }
    return points;
  }

  // Check user's answer
  void _checkAnswer(bool answer) {
    final currentRelation = _relations[_currentRelationIndex];
    final isFunction = currentRelation['is_function'] as bool;

    setState(() {
      _userAnswer = answer;
      _showFeedback = true;

      // If this is the last relation and the answer is correct, mark as completed
      if (_currentRelationIndex == _relations.length - 1 && answer == isFunction) {
        _isCompleted = true;
      }
    });

    // Notify parent of state change
    widget.onStateChanged?.call(_isCompleted);
  }

  // Move to the next relation
  void _nextRelation() {
    if (_currentRelationIndex < _relations.length - 1) {
      // Start animation
      _animationController.forward().then((_) {
        setState(() {
          _currentRelationIndex++;
          _userAnswer = null;
          _showFeedback = false;
          _showExplanation = false;
          _verticalLineTestActive = false;
        });
        _animationController.reverse();
      });
    } else {
      // Reset to the first relation if completed
      _animationController.forward().then((_) {
        setState(() {
          _currentRelationIndex = 0;
          _userAnswer = null;
          _showFeedback = false;
          _showExplanation = false;
          _verticalLineTestActive = false;
          _isCompleted = false;
        });
        _animationController.reverse();
      });
    }
  }

  // Toggle explanation visibility
  void _toggleExplanation() {
    setState(() {
      _showExplanation = !_showExplanation;
    });
  }

  // Toggle vertical line test
  void _toggleVerticalLineTest() {
    setState(() {
      _verticalLineTestActive = !_verticalLineTestActive;
      if (_verticalLineTestActive) {
        _verticalLinePosition = 0.0;
      }
    });
  }

  // Update vertical line position
  void _updateVerticalLinePosition(double position) {
    if (_verticalLineTestActive) {
      setState(() {
        _verticalLinePosition = position;
      });
    }
  }

  // Build relation information
  Widget _buildRelationInfo(Map<String, dynamic> relation) {
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: _primaryColor.withOpacity(0.1),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: _primaryColor),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Relation: ${relation['name']}',
            style: TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.bold,
              color: _primaryColor,
            ),
          ),
          const SizedBox(height: 4),
          Text(
            'Equation: ${relation['equation']}',
            style: TextStyle(
              fontSize: 16,
              color: _textColor,
            ),
          ),
        ],
      ),
    );
  }

  // Build graph visualization
  Widget _buildGraphVisualization(Map<String, dynamic> relation) {
    return Container(
      height: 250,
      width: double.infinity,
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: Colors.grey[300]!),
      ),
      child: GestureDetector(
        onHorizontalDragUpdate: (details) {
          final RenderBox box = context.findRenderObject() as RenderBox;
          final Offset localPosition = box.globalToLocal(details.globalPosition);
          final double width = box.size.width;
          final double normalizedPosition = (localPosition.dx / width) * 2 - 1;
          _updateVerticalLinePosition(normalizedPosition.clamp(-1.0, 1.0));
        },
        child: ClipRRect(
          borderRadius: BorderRadius.circular(8),
          child: CustomPaint(
            painter: FunctionGraphPainter(
              points: List<Map<String, dynamic>>.from(relation['points']),
              color: _primaryColor,
              verticalLineActive: _verticalLineTestActive,
              verticalLinePosition: _verticalLinePosition,
              verticalLineColor: _secondaryColor,
            ),
            child: Stack(
              children: [
                Positioned(
                  bottom: 8,
                  right: 8,
                  child: ElevatedButton.icon(
                    onPressed: _toggleVerticalLineTest,
                    icon: Icon(
                      _verticalLineTestActive ? Icons.close : Icons.show_chart,
                      size: 16,
                    ),
                    label: Text(
                      _verticalLineTestActive ? 'Hide Vertical Line' : 'Vertical Line Test',
                      style: const TextStyle(fontSize: 12),
                    ),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: _secondaryColor,
                      foregroundColor: Colors.white,
                      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                    ),
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  // Build question and answer buttons
  Widget _buildQuestionAndAnswers(bool isFunction) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Is this a function?',
          style: TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.bold,
            color: _textColor,
          ),
        ),
        const SizedBox(height: 8),
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceEvenly,
          children: [
            ElevatedButton(
              onPressed: _showFeedback ? null : () => _checkAnswer(true),
              style: ElevatedButton.styleFrom(
                backgroundColor: _userAnswer == true ? _primaryColor : Colors.grey[300],
                foregroundColor: _userAnswer == true ? Colors.white : _textColor,
              ),
              child: const Text('Yes, it is a function'),
            ),
            ElevatedButton(
              onPressed: _showFeedback ? null : () => _checkAnswer(false),
              style: ElevatedButton.styleFrom(
                backgroundColor: _userAnswer == false ? _primaryColor : Colors.grey[300],
                foregroundColor: _userAnswer == false ? Colors.white : _textColor,
              ),
              child: const Text('No, it is not a function'),
            ),
          ],
        ),
      ],
    );
  }

  // Build feedback
  Widget _buildFeedback(bool isFunction) {
    final bool isCorrect = _userAnswer == isFunction;
    final currentRelation = _relations[_currentRelationIndex];

    return Container(
      margin: const EdgeInsets.only(top: 16),
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: isCorrect ? _accentColor.withOpacity(0.1) : _secondaryColor.withOpacity(0.1),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: isCorrect ? _accentColor : _secondaryColor),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(
                isCorrect ? Icons.check_circle : Icons.cancel,
                color: isCorrect ? _accentColor : _secondaryColor,
              ),
              const SizedBox(width: 8),
              Text(
                isCorrect ? 'Correct!' : 'Incorrect!',
                style: TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.bold,
                  color: isCorrect ? _accentColor : _secondaryColor,
                ),
              ),
            ],
          ),
          const SizedBox(height: 8),
          Text(
            isFunction
                ? 'This is a function because each input (x-value) corresponds to exactly one output (y-value).'
                : 'This is not a function because at least one input (x-value) corresponds to multiple outputs (y-values).',
            style: TextStyle(
              fontSize: 14,
              color: _textColor,
            ),
          ),
          if (_showExplanation)
            Padding(
              padding: const EdgeInsets.only(top: 8),
              child: Text(
                currentRelation['explanation'] as String,
                style: TextStyle(
                  fontSize: 14,
                  fontStyle: FontStyle.italic,
                  color: _textColor,
                ),
              ),
            ),
          const SizedBox(height: 8),
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              ElevatedButton(
                onPressed: _toggleExplanation,
                style: ElevatedButton.styleFrom(
                  backgroundColor: _accentColor,
                  foregroundColor: Colors.white,
                ),
                child: Text(_showExplanation ? 'Hide Explanation' : 'Show Explanation'),
              ),
              ElevatedButton(
                onPressed: _nextRelation,
                style: ElevatedButton.styleFrom(
                  backgroundColor: _primaryColor,
                  foregroundColor: Colors.white,
                ),
                child: Text(_currentRelationIndex < _relations.length - 1 ? 'Next Relation' : 'Start Over'),
              ),
            ],
          ),
        ],
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    final currentRelation = _relations[_currentRelationIndex];
    final isFunction = currentRelation['is_function'] as bool;

    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: _backgroundColor,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withOpacity(0.2),
            spreadRadius: 1,
            blurRadius: 3,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Title
          Text(
            widget.data['title'] ?? 'Function Identifier',
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
              color: _textColor,
            ),
          ),

          const SizedBox(height: 8),

          // Description
          if (widget.data['description'] != null)
            Text(
              widget.data['description'],
              style: TextStyle(
                fontSize: 14,
                color: _textColor.withOpacity(0.8),
              ),
            ),

          const SizedBox(height: 16),

          // Relation information
          _buildRelationInfo(currentRelation),

          const SizedBox(height: 16),

          // Graph visualization
          _buildGraphVisualization(currentRelation),

          const SizedBox(height: 16),

          // Question and answer buttons
          _buildQuestionAndAnswers(isFunction),

          // Feedback
          if (_showFeedback)
            _buildFeedback(isFunction),

          // Widget name tag
          if (widget.data['showNameTag'] ?? true)
            Padding(
              padding: const EdgeInsets.only(top: 8),
              child: Text(
                'InteractiveFunctionIdentifier',
                style: TextStyle(
                  color: Colors.grey[600],
                  fontSize: 12,
                  fontStyle: FontStyle.italic,
                ),
              ),
            ),
        ],
      ),
    );
  }
}

/// Custom painter for drawing function graphs with vertical line test
class FunctionGraphPainter extends CustomPainter {
  final List<Map<String, dynamic>> points;
  final Color color;
  final bool verticalLineActive;
  final double verticalLinePosition;
  final Color verticalLineColor;

  // Constants for graph scaling
  final double minX = -3.0;
  final double maxX = 3.0;
  final double minY = -3.0;
  final double maxY = 3.0;

  FunctionGraphPainter({
    required this.points,
    required this.color,
    required this.verticalLineActive,
    required this.verticalLinePosition,
    required this.verticalLineColor,
  });

  @override
  void paint(Canvas canvas, Size size) {
    final paint = Paint()
      ..color = color
      ..strokeWidth = 2.0
      ..style = PaintingStyle.stroke;

    final gridPaint = Paint()
      ..color = Colors.grey[300]!
      ..strokeWidth = 0.5
      ..style = PaintingStyle.stroke;

    final axisPaint = Paint()
      ..color = Colors.black87
      ..strokeWidth = 1.0
      ..style = PaintingStyle.stroke;

    final verticalLinePaint = Paint()
      ..color = verticalLineColor
      ..strokeWidth = 2.0
      ..style = PaintingStyle.stroke;

    // Draw grid
    _drawGrid(canvas, size, gridPaint);

    // Draw axes
    _drawAxes(canvas, size, axisPaint);

    // Draw function
    _drawFunction(canvas, size, paint);

    // Draw points
    _drawPoints(canvas, size, paint);

    // Draw vertical line for vertical line test
    if (verticalLineActive) {
      _drawVerticalLine(canvas, size, verticalLinePaint);
    }
  }

  void _drawGrid(Canvas canvas, Size size, Paint paint) {
    // Draw vertical grid lines
    for (double x = minX; x <= maxX; x += 1) {
      final screenX = _mapXToScreen(x, size);
      canvas.drawLine(
        Offset(screenX, 0),
        Offset(screenX, size.height),
        paint,
      );
    }

    // Draw horizontal grid lines
    for (double y = minY; y <= maxY; y += 1) {
      final screenY = _mapYToScreen(y, size);
      canvas.drawLine(
        Offset(0, screenY),
        Offset(size.width, screenY),
        paint,
      );
    }
  }

  void _drawAxes(Canvas canvas, Size size, Paint paint) {
    // Draw x-axis
    final yZero = _mapYToScreen(0, size);
    canvas.drawLine(
      Offset(0, yZero),
      Offset(size.width, yZero),
      paint,
    );

    // Draw y-axis
    final xZero = _mapXToScreen(0, size);
    canvas.drawLine(
      Offset(xZero, 0),
      Offset(xZero, size.height),
      paint,
    );

    // Draw axis labels
    final textStyle = TextStyle(
      color: Colors.black87,
      fontSize: 10,
    );
    final textPainter = TextPainter(
      textDirection: TextDirection.ltr,
    );

    // X-axis labels
    for (int i = minX.toInt(); i <= maxX.toInt(); i++) {
      if (i == 0) continue; // Skip zero as it's the origin
      final x = i.toDouble();
      final screenX = _mapXToScreen(x, size);

      textPainter.text = TextSpan(
        text: i.toString(),
        style: textStyle,
      );
      textPainter.layout();
      textPainter.paint(
        canvas,
        Offset(screenX - textPainter.width / 2, yZero + 5),
      );
    }

    // Y-axis labels
    for (int i = minY.toInt(); i <= maxY.toInt(); i++) {
      if (i == 0) continue; // Skip zero as it's the origin
      final y = i.toDouble();
      final screenY = _mapYToScreen(y, size);

      textPainter.text = TextSpan(
        text: i.toString(),
        style: textStyle,
      );
      textPainter.layout();
      textPainter.paint(
        canvas,
        Offset(xZero + 5, screenY - textPainter.height / 2),
      );
    }

    // Origin label
    textPainter.text = TextSpan(
      text: "0",
      style: textStyle,
    );
    textPainter.layout();
    textPainter.paint(
      canvas,
      Offset(xZero + 5, yZero + 5),
    );
  }

  void _drawFunction(Canvas canvas, Size size, Paint paint) {
    if (points.isEmpty) return;

    final path = Path();
    bool started = false;

    // Sort points by x value
    final sortedPoints = List<Map<String, dynamic>>.from(points)
      ..sort((a, b) => (a['x'] as num).compareTo(b['x'] as num));

    for (final point in sortedPoints) {
      final screenX = _mapXToScreen(point['x'] as double, size);
      final screenY = _mapYToScreen(point['y'] as double, size);

      // Skip points outside the visible area
      if ((point['x'] as double) < minX ||
          (point['x'] as double) > maxX ||
          (point['y'] as double) < minY ||
          (point['y'] as double) > maxY) {
        continue;
      }

      if (!started) {
        path.moveTo(screenX, screenY);
        started = true;
      } else {
        path.lineTo(screenX, screenY);
      }
    }

    canvas.drawPath(path, paint);
  }

  void _drawPoints(Canvas canvas, Size size, Paint paint) {
    final pointPaint = Paint()
      ..color = color
      ..strokeWidth = 1.0
      ..style = PaintingStyle.fill;

    for (final point in points) {
      // Skip points outside the visible area
      if ((point['x'] as double) < minX ||
          (point['x'] as double) > maxX ||
          (point['y'] as double) < minY ||
          (point['y'] as double) > maxY) {
        continue;
      }

      final screenX = _mapXToScreen(point['x'] as double, size);
      final screenY = _mapYToScreen(point['y'] as double, size);

      canvas.drawCircle(
        Offset(screenX, screenY),
        4,
        pointPaint,
      );
    }
  }

  void _drawVerticalLine(Canvas canvas, Size size, Paint paint) {
    // Map vertical line position to screen coordinates
    final x = minX + (maxX - minX) * (verticalLinePosition + 1) / 2;
    final screenX = _mapXToScreen(x, size);

    // Draw vertical line
    canvas.drawLine(
      Offset(screenX, 0),
      Offset(screenX, size.height),
      paint,
    );

    // Draw intersections with the function
    final intersections = _findIntersections(x);
    final pointPaint = Paint()
      ..color = verticalLineColor
      ..strokeWidth = 1.0
      ..style = PaintingStyle.fill;

    for (final y in intersections) {
      final screenY = _mapYToScreen(y, size);
      canvas.drawCircle(
        Offset(screenX, screenY),
        6,
        pointPaint,
      );
    }

    // Draw intersection count
    final textStyle = TextStyle(
      color: verticalLineColor,
      fontSize: 14,
      fontWeight: FontWeight.bold,
    );
    final textPainter = TextPainter(
      textDirection: TextDirection.ltr,
    );

    textPainter.text = TextSpan(
      text: "${intersections.length} intersection${intersections.length != 1 ? 's' : ''}",
      style: textStyle,
    );
    textPainter.layout();
    textPainter.paint(
      canvas,
      Offset(screenX - textPainter.width / 2, 10),
    );
  }

  // Find intersections of the vertical line with the function
  List<double> _findIntersections(double x) {
    final List<double> intersections = [];

    // For each point, check if it's close to the vertical line
    for (int i = 0; i < points.length - 1; i++) {
      final x1 = points[i]['x'] as double;
      final y1 = points[i]['y'] as double;
      final x2 = points[i + 1]['x'] as double;
      final y2 = points[i + 1]['y'] as double;

      // If the vertical line is between these two points
      if ((x1 <= x && x <= x2) || (x2 <= x && x <= x1)) {
        // Linear interpolation to find the y value
        if (x1 != x2) {
          final t = (x - x1) / (x2 - x1);
          final y = y1 + t * (y2 - y1);

          // Check if this intersection is already in the list
          bool isDuplicate = false;
          for (final existingY in intersections) {
            if ((existingY - y).abs() < 0.1) {
              isDuplicate = true;
              break;
            }
          }

          if (!isDuplicate) {
            intersections.add(y);
          }
        }
      }
    }

    return intersections;
  }

  // Map x coordinate from math space to screen space
  double _mapXToScreen(double x, Size size) {
    return size.width * (x - minX) / (maxX - minX);
  }

  // Map y coordinate from math space to screen space
  double _mapYToScreen(double y, Size size) {
    // Note: Screen coordinates have y increasing downward, math has y increasing upward
    return size.height * (1 - (y - minY) / (maxY - minY));
  }

  @override
  bool shouldRepaint(covariant FunctionGraphPainter oldDelegate) {
    return oldDelegate.points != points ||
        oldDelegate.color != color ||
        oldDelegate.verticalLineActive != verticalLineActive ||
        oldDelegate.verticalLinePosition != verticalLinePosition ||
        oldDelegate.verticalLineColor != verticalLineColor;
  }
}