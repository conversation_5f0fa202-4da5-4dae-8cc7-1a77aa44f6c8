import 'package:flutter/material.dart';
import 'dart:math' as math;

/// Custom painter for circuit visualization
class <PERSON>Painter extends CustomPainter {
  final Circuit circuit;
  final int? selectedComponentIndex;
  final bool showValues;
  final Color primaryColor;
  final Color textColor;

  CircuitPainter({
    required this.circuit,
    this.selectedComponentIndex,
    required this.showValues,
    required this.primaryColor,
    required this.textColor,
  });

  @override
  void paint(Canvas canvas, Size size) {
    // Draw connections first (behind components)
    _drawConnections(canvas, size);

    // Draw components
    _drawComponents(canvas, size);

    // Draw current flow animation if circuit is running
    if (circuit.isRunning) {
      _drawCurrentFlow(canvas, size);
    }
  }

  // Draw connections between components
  void _drawConnections(Canvas canvas, Size size) {
    final connectionPaint = Paint()
      ..color = Colors.grey
      ..style = PaintingStyle.stroke
      ..strokeWidth = 2.0;

    // Draw connections based on the circuit type
    if (circuit.name.contains('Series')) {
      _drawSeriesConnections(canvas, connectionPaint);
    } else if (circuit.name.contains('Parallel')) {
      _drawParallelConnections(canvas, connectionPaint);
    } else {
      // Draw custom connections
      for (final connection in circuit.connections) {
        if (connection.length == 2) {
          final component1 = circuit.components[connection[0]];
          final component2 = circuit.components[connection[1]];

          canvas.drawLine(
            component1.position,
            component2.position,
            connectionPaint,
          );
        }
      }
    }
  }

  // Draw series circuit connections
  void _drawSeriesConnections(Canvas canvas, Paint paint) {
    for (int i = 0; i < circuit.components.length - 1; i++) {
      final component1 = circuit.components[i];
      final component2 = circuit.components[i + 1];

      // Draw line from component1 to component2
      canvas.drawLine(
        component1.position,
        component2.position,
        paint,
      );
    }

    // Connect last component to first component to complete the circuit
    if (circuit.components.isNotEmpty) {
      final firstComponent = circuit.components.first;
      final lastComponent = circuit.components.last;

      canvas.drawLine(
        lastComponent.position,
        firstComponent.position,
        paint,
      );
    }
  }

  // Draw parallel circuit connections
  void _drawParallelConnections(Canvas canvas, Paint paint) {
    // Find voltage source and wires
    final voltageSource = circuit.components.firstWhere(
      (component) => component.type == 'voltage_source',
      orElse: () => circuit.components.first,
    );

    final wires = circuit.components.where(
      (component) => component.type == 'wire',
    ).toList();

    final resistors = circuit.components.where(
      (component) => component.type == 'resistor',
    ).toList();

    if (wires.length >= 2 && resistors.isNotEmpty) {
      final wire1 = wires[0];
      final wire2 = wires[1];

      // Draw connection from voltage source to wires
      canvas.drawLine(
        voltageSource.position,
        wire1.position,
        paint,
      );

      canvas.drawLine(
        voltageSource.position,
        wire2.position,
        paint,
      );

      // Draw connections from wires to resistors
      for (final resistor in resistors) {
        canvas.drawLine(
          wire1.position,
          Offset(resistor.position.dx, wire1.position.dy),
          paint,
        );

        canvas.drawLine(
          Offset(resistor.position.dx, wire1.position.dy),
          resistor.position,
          paint,
        );

        canvas.drawLine(
          resistor.position,
          Offset(resistor.position.dx, wire2.position.dy),
          paint,
        );

        canvas.drawLine(
          Offset(resistor.position.dx, wire2.position.dy),
          wire2.position,
          paint,
        );
      }
    }
  }

  // Draw components
  void _drawComponents(Canvas canvas, Size size) {
    for (int i = 0; i < circuit.components.length; i++) {
      final component = circuit.components[i];
      final isSelected = selectedComponentIndex == i;

      // Draw component based on its type
      switch (component.type) {
        case 'voltage_source':
          _drawVoltageSource(canvas, component, isSelected);
          break;
        case 'resistor':
          _drawResistor(canvas, component, isSelected);
          break;
        case 'wire':
          _drawWire(canvas, component, isSelected);
          break;
        default:
          _drawGenericComponent(canvas, component, isSelected);
      }

      // Draw component value if showValues is true
      if (showValues && component.type != 'wire') {
        _drawComponentValue(canvas, component);
      }
    }
  }

  // Draw voltage source
  void _drawVoltageSource(Canvas canvas, CircuitComponent component, bool isSelected) {
    final paint = Paint()
      ..color = component.isOn ? component.color : Colors.grey
      ..style = PaintingStyle.stroke
      ..strokeWidth = isSelected ? 3.0 : 2.0;

    final fillPaint = Paint()
      ..color = component.isOn ? component.color.withAlpha(50) : Colors.grey.withAlpha(50)
      ..style = PaintingStyle.fill;

    // Draw circle
    canvas.drawCircle(
      component.position,
      30,
      fillPaint,
    );

    canvas.drawCircle(
      component.position,
      30,
      paint,
    );

    // Draw plus and minus symbols
    final textStyle = TextStyle(
      color: component.isOn ? component.color : Colors.grey,
      fontSize: 20,
      fontWeight: FontWeight.bold,
    );

    // Plus symbol
    final plusSpan = TextSpan(
      text: '+',
      style: textStyle,
    );
    final plusPainter = TextPainter(
      text: plusSpan,
      textDirection: TextDirection.ltr,
    );
    plusPainter.layout();
    plusPainter.paint(
      canvas,
      Offset(
        component.position.dx - 15,
        component.position.dy - 15,
      ),
    );

    // Minus symbol
    final minusSpan = TextSpan(
      text: '-',
      style: textStyle,
    );
    final minusPainter = TextPainter(
      text: minusSpan,
      textDirection: TextDirection.ltr,
    );
    minusPainter.layout();
    minusPainter.paint(
      canvas,
      Offset(
        component.position.dx + 5,
        component.position.dy - 15,
      ),
    );

    // Draw component symbol
    final symbolSpan = TextSpan(
      text: component.symbol,
      style: TextStyle(
        color: component.isOn ? component.color : Colors.grey,
        fontSize: 16,
        fontWeight: FontWeight.bold,
      ),
    );
    final symbolPainter = TextPainter(
      text: symbolSpan,
      textDirection: TextDirection.ltr,
    );
    symbolPainter.layout();
    symbolPainter.paint(
      canvas,
      Offset(
        component.position.dx - symbolPainter.width / 2,
        component.position.dy - symbolPainter.height / 2,
      ),
    );
  }

  // Draw resistor
  void _drawResistor(Canvas canvas, CircuitComponent component, bool isSelected) {
    final paint = Paint()
      ..color = component.isOn ? component.color : Colors.grey
      ..style = PaintingStyle.stroke
      ..strokeWidth = isSelected ? 3.0 : 2.0;

    final fillPaint = Paint()
      ..color = component.isOn ? component.color.withAlpha(50) : Colors.grey.withAlpha(50)
      ..style = PaintingStyle.fill;

    // Draw rectangle
    final rect = Rect.fromCenter(
      center: component.position,
      width: 60,
      height: 30,
    );

    canvas.drawRect(rect, fillPaint);
    canvas.drawRect(rect, paint);

    // Draw zigzag inside
    final path = Path();
    path.moveTo(rect.left + 5, rect.center.dy);

    double x = rect.left + 5;
    final zigzagWidth = 5.0;
    final zigzagHeight = 10.0;

    while (x < rect.right - 5) {
      path.lineTo(x + zigzagWidth, rect.center.dy - zigzagHeight);
      x += zigzagWidth;
      path.lineTo(x + zigzagWidth, rect.center.dy + zigzagHeight);
      x += zigzagWidth;
    }

    path.lineTo(rect.right - 5, rect.center.dy);

    canvas.drawPath(path, paint);

    // Draw component symbol
    final symbolSpan = TextSpan(
      text: component.symbol,
      style: TextStyle(
        color: component.isOn ? component.color : Colors.grey,
        fontSize: 14,
        fontWeight: FontWeight.bold,
      ),
    );
    final symbolPainter = TextPainter(
      text: symbolSpan,
      textDirection: TextDirection.ltr,
    );
    symbolPainter.layout();
    symbolPainter.paint(
      canvas,
      Offset(
        component.position.dx - symbolPainter.width / 2,
        rect.bottom + 5,
      ),
    );
  }

  // Draw wire
  void _drawWire(Canvas canvas, CircuitComponent component, bool isSelected) {
    final paint = Paint()
      ..color = isSelected ? primaryColor : Colors.grey
      ..style = PaintingStyle.stroke
      ..strokeWidth = isSelected ? 3.0 : 2.0;

    // Draw small circle
    canvas.drawCircle(
      component.position,
      5,
      paint,
    );

    // Draw component name if selected
    if (isSelected) {
      final nameSpan = TextSpan(
        text: component.name,
        style: TextStyle(
          color: primaryColor,
          fontSize: 12,
          fontWeight: FontWeight.bold,
        ),
      );
      final namePainter = TextPainter(
        text: nameSpan,
        textDirection: TextDirection.ltr,
      );
      namePainter.layout();
      namePainter.paint(
        canvas,
        Offset(
          component.position.dx - namePainter.width / 2,
          component.position.dy + 10,
        ),
      );
    }
  }

  // Draw generic component
  void _drawGenericComponent(Canvas canvas, CircuitComponent component, bool isSelected) {
    final paint = Paint()
      ..color = component.isOn ? component.color : Colors.grey
      ..style = PaintingStyle.stroke
      ..strokeWidth = isSelected ? 3.0 : 2.0;

    final fillPaint = Paint()
      ..color = component.isOn ? component.color.withAlpha(50) : Colors.grey.withAlpha(50)
      ..style = PaintingStyle.fill;

    // Draw rectangle
    final rect = Rect.fromCenter(
      center: component.position,
      width: 40,
      height: 40,
    );

    canvas.drawRect(rect, fillPaint);
    canvas.drawRect(rect, paint);

    // Draw component symbol
    final symbolSpan = TextSpan(
      text: component.symbol,
      style: TextStyle(
        color: component.isOn ? component.color : Colors.grey,
        fontSize: 16,
        fontWeight: FontWeight.bold,
      ),
    );
    final symbolPainter = TextPainter(
      text: symbolSpan,
      textDirection: TextDirection.ltr,
    );
    symbolPainter.layout();
    symbolPainter.paint(
      canvas,
      Offset(
        component.position.dx - symbolPainter.width / 2,
        component.position.dy - symbolPainter.height / 2,
      ),
    );
  }

  // Draw component value
  void _drawComponentValue(Canvas canvas, CircuitComponent component) {
    // Skip if component is a wire
    if (component.type == 'wire') return;

    final valueText = '${component.value.toStringAsFixed(component.step < 1 ? 1 : 0)} ${component.unit}';
    final valueSpan = TextSpan(
      text: valueText,
      style: TextStyle(
        color: component.isOn ? component.color : Colors.grey,
        fontSize: 12,
        fontWeight: FontWeight.bold,
      ),
    );
    final valuePainter = TextPainter(
      text: valueSpan,
      textDirection: TextDirection.ltr,
    );
    valuePainter.layout();

    // Position depends on component type
    Offset position;
    if (component.type == 'resistor') {
      position = Offset(
        component.position.dx - valuePainter.width / 2,
        component.position.dy - 25,
      );
    } else {
      position = Offset(
        component.position.dx - valuePainter.width / 2,
        component.position.dy + 35,
      );
    }

    valuePainter.paint(canvas, position);
  }

  // Draw current flow animation
  void _drawCurrentFlow(Canvas canvas, Size size) {
    final current = circuit.calculateCurrent();

    // Skip if no current is flowing
    if (current == 0) return;

    final flowPaint = Paint()
      ..color = primaryColor
      ..style = PaintingStyle.fill;

    // Animation time based on system time
    final animationTime = DateTime.now().millisecondsSinceEpoch / 300;

    // Draw flow indicators on connections
    if (circuit.name.contains('Series')) {
      _drawSeriesCurrentFlow(canvas, flowPaint, animationTime, current);
    } else if (circuit.name.contains('Parallel')) {
      _drawParallelCurrentFlow(canvas, flowPaint, animationTime, current);
    }
  }

  // Draw current flow for series circuit
  void _drawSeriesCurrentFlow(Canvas canvas, Paint paint, double animationTime, double current) {
    // Skip if no components
    if (circuit.components.isEmpty) return;

    // Draw flow indicators between components
    for (int i = 0; i < circuit.components.length; i++) {
      final component1 = circuit.components[i];
      final component2 = circuit.components[(i + 1) % circuit.components.length];

      // Calculate direction vector
      final dx = component2.position.dx - component1.position.dx;
      final dy = component2.position.dy - component1.position.dy;
      final distance = math.sqrt(dx * dx + dy * dy);

      // Skip if components are at the same position
      if (distance == 0) continue;

      // Normalize direction vector
      final dirX = dx / distance;
      final dirY = dy / distance;

      // Calculate perpendicular vector
      final perpX = -dirY;
      final perpY = dirX;

      // Number of flow indicators based on distance
      final numIndicators = (distance / 30).floor();

      // Size of flow indicators based on current
      final indicatorSize = math.min(5.0, math.max(2.0, current.abs()));

      // Draw flow indicators
      for (int j = 0; j < numIndicators; j++) {
        // Position along the line with animation
        final t = (j / numIndicators + (animationTime % 1.0)) % 1.0;
        final x = component1.position.dx + dx * t;
        final y = component1.position.dy + dy * t;

        // Draw arrow
        final path = Path();
        path.moveTo(x, y);
        path.lineTo(
          x - dirX * indicatorSize + perpX * indicatorSize,
          y - dirY * indicatorSize + perpY * indicatorSize,
        );
        path.lineTo(
          x - dirX * indicatorSize - perpX * indicatorSize,
          y - dirY * indicatorSize - perpY * indicatorSize,
        );
        path.close();

        canvas.drawPath(path, paint);
      }
    }
  }

  // Draw current flow for parallel circuit
  void _drawParallelCurrentFlow(Canvas canvas, Paint paint, double animationTime, double current) {
    // Find voltage source and wires
    final voltageSource = circuit.components.firstWhere(
      (component) => component.type == 'voltage_source',
      orElse: () => circuit.components.first,
    );

    final wires = circuit.components.where(
      (component) => component.type == 'wire',
    ).toList();

    final resistors = circuit.components.where(
      (component) => component.type == 'resistor',
    ).toList();

    if (wires.length >= 2 && resistors.isNotEmpty) {
      final wire1 = wires[0];
      final wire2 = wires[1];

      // Draw flow from voltage source to wire1
      _drawFlowBetweenPoints(
        canvas,
        paint,
        voltageSource.position,
        wire1.position,
        animationTime,
        current
      );

      // Draw flow from wire2 to voltage source
      _drawFlowBetweenPoints(
        canvas,
        paint,
        wire2.position,
        voltageSource.position,
        animationTime,
        current
      );

      // Draw flow through each resistor
      for (final resistor in resistors) {
        // Calculate current through this resistor
        double resistorCurrent = current;
        if (resistor.isOn) {
          // For parallel circuit, current splits based on resistance
          final totalResistance = _calculateParallelResistance(resistors);
          resistorCurrent = current * (totalResistance / resistor.value);
        } else {
          resistorCurrent = 0;
        }

        // Draw flow from wire1 to resistor
        _drawFlowBetweenPoints(
          canvas,
          paint,
          wire1.position,
          Offset(resistor.position.dx, wire1.position.dy),
          animationTime,
          resistorCurrent
        );

        _drawFlowBetweenPoints(
          canvas,
          paint,
          Offset(resistor.position.dx, wire1.position.dy),
          resistor.position,
          animationTime,
          resistorCurrent
        );

        // Draw flow from resistor to wire2
        _drawFlowBetweenPoints(
          canvas,
          paint,
          resistor.position,
          Offset(resistor.position.dx, wire2.position.dy),
          animationTime,
          resistorCurrent
        );

        _drawFlowBetweenPoints(
          canvas,
          paint,
          Offset(resistor.position.dx, wire2.position.dy),
          wire2.position,
          animationTime,
          resistorCurrent
        );
      }
    }
  }

  // Draw flow between two points
  void _drawFlowBetweenPoints(
    Canvas canvas,
    Paint paint,
    Offset start,
    Offset end,
    double animationTime,
    double current
  ) {
    // Calculate direction vector
    final dx = end.dx - start.dx;
    final dy = end.dy - start.dy;
    final distance = math.sqrt(dx * dx + dy * dy);

    // Skip if points are at the same position
    if (distance == 0) return;

    // Normalize direction vector
    final dirX = dx / distance;
    final dirY = dy / distance;

    // Calculate perpendicular vector
    final perpX = -dirY;
    final perpY = dirX;

    // Number of flow indicators based on distance
    final numIndicators = (distance / 30).floor();

    // Size of flow indicators based on current
    final indicatorSize = math.min(5.0, math.max(2.0, current.abs()));

    // Skip if no current
    if (indicatorSize <= 0) return;

    // Draw flow indicators
    for (int j = 0; j < numIndicators; j++) {
      // Position along the line with animation
      final t = (j / numIndicators + (animationTime % 1.0)) % 1.0;
      final x = start.dx + dx * t;
      final y = start.dy + dy * t;

      // Draw arrow
      final path = Path();
      path.moveTo(x, y);
      path.lineTo(
        x - dirX * indicatorSize + perpX * indicatorSize,
        y - dirY * indicatorSize + perpY * indicatorSize,
      );
      path.lineTo(
        x - dirX * indicatorSize - perpX * indicatorSize,
        y - dirY * indicatorSize - perpY * indicatorSize,
      );
      path.close();

      canvas.drawPath(path, paint);
    }
  }

  // Calculate parallel resistance
  double _calculateParallelResistance(List<CircuitComponent> resistors) {
    double reciprocalSum = 0;
    for (final resistor in resistors) {
      if (resistor.isOn) {
        reciprocalSum += 1 / resistor.value;
      }
    }

    if (reciprocalSum > 0) {
      return 1 / reciprocalSum;
    }

    return double.infinity;
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => true;
}

/// A widget that provides an interactive circuit simulator
class InteractiveCircuitSimulatorWidget extends StatefulWidget {
  /// The data for this widget
  final Map<String, dynamic> data;

  /// Optional callback when the widget state changes
  final Function(bool isCompleted)? onStateChanged;

  const InteractiveCircuitSimulatorWidget({
    super.key,
    required this.data,
    this.onStateChanged,
  });

  /// Factory constructor to create the widget from JSON data
  factory InteractiveCircuitSimulatorWidget.fromData(
      Map<String, dynamic> data) {
    return InteractiveCircuitSimulatorWidget(
      data: data,
    );
  }

  @override
  State<InteractiveCircuitSimulatorWidget> createState() =>
      _InteractiveCircuitSimulatorWidgetState();
}

/// Circuit component model
class CircuitComponent {
  final String type;
  final String name;
  final String symbol;
  final String description;
  final double value;
  final double minValue;
  final double maxValue;
  final double step;
  final String unit;
  final Color color;
  final Offset position;
  final List<int> connections;
  final bool isOn;

  CircuitComponent({
    required this.type,
    required this.name,
    required this.symbol,
    required this.description,
    required this.value,
    required this.minValue,
    required this.maxValue,
    required this.step,
    required this.unit,
    required this.color,
    required this.position,
    required this.connections,
    this.isOn = true,
  });

  /// Create a copy of the component with new values
  CircuitComponent copyWith({
    double? value,
    Offset? position,
    List<int>? connections,
    bool? isOn,
  }) {
    return CircuitComponent(
      type: type,
      name: name,
      symbol: symbol,
      description: description,
      value: value ?? this.value,
      minValue: minValue,
      maxValue: maxValue,
      step: step,
      unit: unit,
      color: color,
      position: position ?? this.position,
      connections: connections ?? this.connections,
      isOn: isOn ?? this.isOn,
    );
  }
}

/// Circuit model
class Circuit {
  final String name;
  final String description;
  final List<CircuitComponent> components;
  final List<List<int>> connections;
  final bool isRunning;

  Circuit({
    required this.name,
    required this.description,
    required this.components,
    required this.connections,
    this.isRunning = false,
  });

  /// Create a copy of the circuit with new values
  Circuit copyWith({
    List<CircuitComponent>? components,
    List<List<int>>? connections,
    bool? isRunning,
  }) {
    return Circuit(
      name: name,
      description: description,
      components: components ?? this.components,
      connections: connections ?? this.connections,
      isRunning: isRunning ?? this.isRunning,
    );
  }

  /// Calculate the current flowing through the circuit
  double calculateCurrent() {
    // For a simple series circuit with a voltage source and resistors
    final voltageSource = components.firstWhere(
      (component) => component.type == 'voltage_source',
      orElse: () => CircuitComponent(
        type: 'voltage_source',
        name: 'Voltage Source',
        symbol: 'V',
        description: 'A source of electrical potential',
        value: 0,
        minValue: 0,
        maxValue: 24,
        step: 0.1,
        unit: 'V',
        color: Colors.red,
        position: const Offset(0, 0),
        connections: const [],
      ),
    );

    // If the voltage source is off, no current flows
    if (!voltageSource.isOn) return 0;

    // Calculate total resistance
    double totalResistance = 0;
    for (final component in components) {
      if (component.type == 'resistor' && component.isOn) {
        totalResistance += component.value;
      }
    }

    // Prevent division by zero
    if (totalResistance == 0) return 0;

    // Ohm's Law: I = V / R
    return voltageSource.value / totalResistance;
  }

  /// Calculate the voltage across a specific component
  double calculateVoltageAcross(int componentIndex) {
    if (componentIndex < 0 || componentIndex >= components.length) {
      return 0;
    }

    final component = components[componentIndex];
    final current = calculateCurrent();

    // Ohm's Law: V = I * R
    if (component.type == 'resistor' && component.isOn) {
      return current * component.value;
    } else if (component.type == 'voltage_source' && component.isOn) {
      return component.value;
    }

    return 0;
  }

  /// Calculate the power dissipated by a specific component
  double calculatePower(int componentIndex) {
    if (componentIndex < 0 || componentIndex >= components.length) {
      return 0;
    }

    final current = calculateCurrent();
    final voltage = calculateVoltageAcross(componentIndex);

    // Power: P = V * I
    return voltage * current;
  }
}

class _InteractiveCircuitSimulatorWidgetState
    extends State<InteractiveCircuitSimulatorWidget> {
  // Colors
  late Color _primaryColor;
  late Color _textColor;
  late Color _backgroundColor;

  // Circuits
  late List<Circuit> _circuits;
  late int _currentCircuitIndex;

  // UI state
  bool _showDescription = true;
  bool _showValues = true;
  int? _selectedComponentIndex;

  @override
  void initState() {
    super.initState();

    // Initialize colors
    _primaryColor = _getColorFromHex(
        widget.data['primaryColor'] ?? '#2196F3'); // Blue
    _textColor =
        _getColorFromHex(widget.data['textColor'] ?? '#212121'); // Dark Grey
    _backgroundColor = _getColorFromHex(
        widget.data['backgroundColor'] ?? '#FFFFFF'); // White

    // Initialize circuits
    _circuits = _createCircuits();
    _currentCircuitIndex = 0;
  }

  // Convert hex color string to Color
  Color _getColorFromHex(String hexColor) {
    hexColor = hexColor.replaceAll('#', '');
    if (hexColor.length == 6) {
      hexColor = 'FF$hexColor';
    }
    return Color(int.parse(hexColor, radix: 16));
  }

  // Create predefined circuits
  List<Circuit> _createCircuits() {
    return [
      // Simple series circuit
      Circuit(
        name: 'Simple Series Circuit',
        description:
            'A basic series circuit with a voltage source and resistors. '
            'In a series circuit, the same current flows through all components.',
        components: [
          CircuitComponent(
            type: 'voltage_source',
            name: 'Voltage Source',
            symbol: 'V',
            description: 'A source of electrical potential',
            value: 9,
            minValue: 0,
            maxValue: 24,
            step: 0.1,
            unit: 'V',
            color: Colors.red,
            position: const Offset(100, 150),
            connections: const [1, 3],
          ),
          CircuitComponent(
            type: 'resistor',
            name: 'Resistor 1',
            symbol: 'R₁',
            description: 'A component that resists the flow of current',
            value: 100,
            minValue: 1,
            maxValue: 1000,
            step: 1,
            unit: 'Ω',
            color: Colors.brown,
            position: const Offset(250, 100),
            connections: const [0, 2],
          ),
          CircuitComponent(
            type: 'resistor',
            name: 'Resistor 2',
            symbol: 'R₂',
            description: 'A component that resists the flow of current',
            value: 200,
            minValue: 1,
            maxValue: 1000,
            step: 1,
            unit: 'Ω',
            color: Colors.brown,
            position: const Offset(400, 100),
            connections: const [1, 3],
          ),
          CircuitComponent(
            type: 'wire',
            name: 'Wire',
            symbol: '',
            description: 'A conductor that connects components',
            value: 0,
            minValue: 0,
            maxValue: 0,
            step: 0,
            unit: '',
            color: Colors.grey,
            position: const Offset(250, 200),
            connections: const [0, 2],
          ),
        ],
        connections: [
          [0, 1], // Voltage source to Resistor 1
          [1, 2], // Resistor 1 to Resistor 2
          [2, 3], // Resistor 2 to Wire
          [3, 0], // Wire to Voltage source
        ],
      ),

      // Simple parallel circuit
      Circuit(
        name: 'Simple Parallel Circuit',
        description:
            'A basic parallel circuit with a voltage source and resistors. '
            'In a parallel circuit, the voltage across all components is the same.',
        components: [
          CircuitComponent(
            type: 'voltage_source',
            name: 'Voltage Source',
            symbol: 'V',
            description: 'A source of electrical potential',
            value: 12,
            minValue: 0,
            maxValue: 24,
            step: 0.1,
            unit: 'V',
            color: Colors.red,
            position: const Offset(100, 150),
            connections: const [1, 4],
          ),
          CircuitComponent(
            type: 'wire',
            name: 'Wire 1',
            symbol: '',
            description: 'A conductor that connects components',
            value: 0,
            minValue: 0,
            maxValue: 0,
            step: 0,
            unit: '',
            color: Colors.grey,
            position: const Offset(250, 100),
            connections: const [0, 2, 3],
          ),
          CircuitComponent(
            type: 'resistor',
            name: 'Resistor 1',
            symbol: 'R₁',
            description: 'A component that resists the flow of current',
            value: 100,
            minValue: 1,
            maxValue: 1000,
            step: 1,
            unit: 'Ω',
            color: Colors.brown,
            position: const Offset(250, 150),
            connections: const [1, 4],
          ),
          CircuitComponent(
            type: 'resistor',
            name: 'Resistor 2',
            symbol: 'R₂',
            description: 'A component that resists the flow of current',
            value: 200,
            minValue: 1,
            maxValue: 1000,
            step: 1,
            unit: 'Ω',
            color: Colors.brown,
            position: const Offset(250, 200),
            connections: const [1, 4],
          ),
          CircuitComponent(
            type: 'wire',
            name: 'Wire 2',
            symbol: '',
            description: 'A conductor that connects components',
            value: 0,
            minValue: 0,
            maxValue: 0,
            step: 0,
            unit: '',
            color: Colors.grey,
            position: const Offset(400, 150),
            connections: const [0, 2, 3],
          ),
        ],
        connections: [
          [0, 1], // Voltage source to Wire 1
          [1, 2], // Wire 1 to Resistor 1
          [1, 3], // Wire 1 to Resistor 2
          [2, 4], // Resistor 1 to Wire 2
          [3, 4], // Resistor 2 to Wire 2
          [4, 0], // Wire 2 to Voltage source
        ],
      ),
    ];
  }

  // Update component value
  void _updateComponentValue(int componentIndex, double value) {
    setState(() {
      final circuit = _circuits[_currentCircuitIndex];
      final component = circuit.components[componentIndex];
      final updatedComponent = component.copyWith(value: value);

      final updatedComponents = List<CircuitComponent>.from(circuit.components);
      updatedComponents[componentIndex] = updatedComponent;

      _circuits[_currentCircuitIndex] = circuit.copyWith(components: updatedComponents);
    });
  }

  // Toggle component on/off
  void _toggleComponent(int componentIndex) {
    setState(() {
      final circuit = _circuits[_currentCircuitIndex];
      final component = circuit.components[componentIndex];
      final updatedComponent = component.copyWith(isOn: !component.isOn);

      final updatedComponents = List<CircuitComponent>.from(circuit.components);
      updatedComponents[componentIndex] = updatedComponent;

      _circuits[_currentCircuitIndex] = circuit.copyWith(components: updatedComponents);
    });
  }

  // Toggle circuit running state
  void _toggleCircuitRunning() {
    setState(() {
      final circuit = _circuits[_currentCircuitIndex];
      _circuits[_currentCircuitIndex] = circuit.copyWith(isRunning: !circuit.isRunning);
    });
  }

  // Build circuit selector
  Widget _buildCircuitSelector() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Select Circuit',
          style: TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.bold,
            color: _textColor,
          ),
        ),
        const SizedBox(height: 8),
        SingleChildScrollView(
          scrollDirection: Axis.horizontal,
          child: Row(
            children: List.generate(_circuits.length, (index) {
              final isSelected = index == _currentCircuitIndex;
              return Padding(
                padding: const EdgeInsets.only(right: 8),
                child: ChoiceChip(
                  label: Text(_circuits[index].name),
                  selected: isSelected,
                  onSelected: (selected) {
                    if (selected) {
                      setState(() {
                        _currentCircuitIndex = index;
                        _selectedComponentIndex = null;
                      });
                    }
                  },
                  backgroundColor: Colors.grey.withAlpha(50),
                  selectedColor: _primaryColor.withAlpha(100),
                ),
              );
            }),
          ),
        ),
      ],
    );
  }

  // Build circuit simulator
  Widget _buildCircuitSimulator(Circuit circuit) {
    return Container(
      height: 300,
      width: double.infinity,
      decoration: BoxDecoration(
        color: _backgroundColor,
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: Colors.grey.withAlpha(100)),
      ),
      child: ClipRRect(
        borderRadius: BorderRadius.circular(8),
        child: Stack(
          children: [
            // Circuit diagram
            CustomPaint(
              painter: CircuitPainter(
                circuit: circuit,
                selectedComponentIndex: _selectedComponentIndex,
                showValues: _showValues,
                primaryColor: _primaryColor,
                textColor: _textColor,
              ),
              size: const Size(double.infinity, 300),
            ),

            // Component selection
            if (!circuit.isRunning)
              GestureDetector(
                onTapDown: (details) {
                  final localPosition = details.localPosition;
                  _selectComponentAtPosition(localPosition);
                },
                child: Container(
                  color: Colors.transparent,
                ),
              ),
          ],
        ),
      ),
    );
  }

  // Select component at position
  void _selectComponentAtPosition(Offset position) {
    final circuit = _circuits[_currentCircuitIndex];

    // Check if position is within any component
    for (int i = 0; i < circuit.components.length; i++) {
      final component = circuit.components[i];
      final componentRect = Rect.fromCenter(
        center: component.position,
        width: 60,
        height: 60,
      );

      if (componentRect.contains(position)) {
        setState(() {
          _selectedComponentIndex = i;
        });
        return;
      }
    }

    // If no component was selected, clear selection
    setState(() {
      _selectedComponentIndex = null;
    });
  }

  // Build component controls
  Widget _buildComponentControls(Circuit circuit) {
    if (_selectedComponentIndex == null ||
        _selectedComponentIndex! >= circuit.components.length) {
      return const Center(
        child: Text('Select a component to adjust its properties'),
      );
    }

    final component = circuit.components[_selectedComponentIndex!];

    // Skip controls for wires
    if (component.type == 'wire') {
      return Center(
        child: Text(
          'Selected: ${component.name} - No adjustable properties',
          style: TextStyle(
            fontWeight: FontWeight.bold,
            color: _textColor,
          ),
        ),
      );
    }

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Text(
              'Selected: ${component.name}',
              style: TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.bold,
                color: _textColor,
              ),
            ),
            Switch(
              value: component.isOn,
              onChanged: (value) => _toggleComponent(_selectedComponentIndex!),
              activeColor: _primaryColor,
            ),
          ],
        ),
        const SizedBox(height: 8),

        // Value slider
        if (component.maxValue > component.minValue)
          Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                children: [
                  Text(
                    '${component.type == 'voltage_source' ? 'Voltage' : 'Resistance'}: ',
                    style: TextStyle(
                      color: _textColor,
                    ),
                  ),
                  Text(
                    '${component.value.toStringAsFixed(component.step < 1 ? 1 : 0)} ${component.unit}',
                    style: TextStyle(
                      fontWeight: FontWeight.bold,
                      color: component.color,
                    ),
                  ),
                ],
              ),
              Slider(
                value: component.value,
                min: component.minValue,
                max: component.maxValue,
                divisions: ((component.maxValue - component.minValue) /
                           component.step).round(),
                label: '${component.value.toStringAsFixed(component.step < 1 ? 1 : 0)} ${component.unit}',
                onChanged: (value) => _updateComponentValue(_selectedComponentIndex!, value),
                activeColor: component.color,
              ),
            ],
          ),

        // Circuit analysis for this component
        if (circuit.isRunning && component.isOn)
          Padding(
            padding: const EdgeInsets.only(top: 8),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'Analysis:',
                  style: TextStyle(
                    fontWeight: FontWeight.bold,
                    color: _textColor,
                  ),
                ),
                const SizedBox(height: 4),
                Text(
                  'Current: ${circuit.calculateCurrent().toStringAsFixed(3)} A',
                  style: TextStyle(
                    color: _textColor,
                  ),
                ),
                Text(
                  'Voltage: ${circuit.calculateVoltageAcross(_selectedComponentIndex!).toStringAsFixed(3)} V',
                  style: TextStyle(
                    color: _textColor,
                  ),
                ),
                Text(
                  'Power: ${circuit.calculatePower(_selectedComponentIndex!).toStringAsFixed(3)} W',
                  style: TextStyle(
                    color: _textColor,
                  ),
                ),
              ],
            ),
          ),
      ],
    );
  }

  // Build circuit controls
  Widget _buildCircuitControls() {
    final circuit = _circuits[_currentCircuitIndex];

    return Row(
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        // Run/Stop button
        ElevatedButton.icon(
          onPressed: _toggleCircuitRunning,
          icon: Icon(circuit.isRunning ? Icons.stop : Icons.play_arrow),
          label: Text(circuit.isRunning ? 'Stop' : 'Run'),
          style: ElevatedButton.styleFrom(
            backgroundColor: circuit.isRunning ? Colors.red : _primaryColor,
            foregroundColor: Colors.white,
          ),
        ),
        const SizedBox(width: 16),

        // Toggle values button
        OutlinedButton.icon(
          onPressed: () {
            setState(() {
              _showValues = !_showValues;
            });
          },
          icon: Icon(_showValues ? Icons.visibility_off : Icons.visibility),
          label: Text(_showValues ? 'Hide Values' : 'Show Values'),
          style: OutlinedButton.styleFrom(
            foregroundColor: _primaryColor,
          ),
        ),
        const SizedBox(width: 16),

        // Toggle description button
        OutlinedButton.icon(
          onPressed: () {
            setState(() {
              _showDescription = !_showDescription;
            });
          },
          icon: Icon(_showDescription ? Icons.visibility_off : Icons.visibility),
          label: Text(_showDescription ? 'Hide Info' : 'Show Info'),
          style: OutlinedButton.styleFrom(
            foregroundColor: _primaryColor,
          ),
        ),
      ],
    );
  }

  // Build circuit information
  Widget _buildCircuitInfo(Circuit circuit) {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: _primaryColor.withAlpha(20),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: _primaryColor.withAlpha(100)),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            circuit.name,
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
              color: _textColor,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            circuit.description,
            style: TextStyle(
              color: _textColor,
              fontSize: 14,
            ),
          ),
          const SizedBox(height: 8),
          if (circuit.isRunning)
            Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'Circuit Analysis:',
                  style: TextStyle(
                    fontWeight: FontWeight.bold,
                    color: _textColor,
                  ),
                ),
                const SizedBox(height: 4),
                Text(
                  'Total Current: ${circuit.calculateCurrent().toStringAsFixed(3)} A',
                  style: TextStyle(
                    color: _textColor,
                  ),
                ),
                Text(
                  'Total Resistance: ${_calculateTotalResistance(circuit).toStringAsFixed(3)} Ω',
                  style: TextStyle(
                    color: _textColor,
                  ),
                ),
                Text(
                  'Total Power: ${_calculateTotalPower(circuit).toStringAsFixed(3)} W',
                  style: TextStyle(
                    color: _textColor,
                  ),
                ),
              ],
            ),
          const SizedBox(height: 8),
          Align(
            alignment: Alignment.centerRight,
            child: TextButton(
              onPressed: () {
                widget.onStateChanged?.call(true);
              },
              child: const Text('Mark as Completed'),
            ),
          ),
        ],
      ),
    );
  }

  // Calculate total resistance of the circuit
  double _calculateTotalResistance(Circuit circuit) {
    double totalResistance = 0;

    // For a simple series circuit
    if (circuit.name.contains('Series')) {
      for (final component in circuit.components) {
        if (component.type == 'resistor' && component.isOn) {
          totalResistance += component.value;
        }
      }
    }
    // For a simple parallel circuit
    else if (circuit.name.contains('Parallel')) {
      double reciprocalSum = 0;
      for (final component in circuit.components) {
        if (component.type == 'resistor' && component.isOn) {
          reciprocalSum += 1 / component.value;
        }
      }

      if (reciprocalSum > 0) {
        totalResistance = 1 / reciprocalSum;
      }
    }

    return totalResistance;
  }

  // Calculate total power of the circuit
  double _calculateTotalPower(Circuit circuit) {
    double totalPower = 0;

    for (int i = 0; i < circuit.components.length; i++) {
      totalPower += circuit.calculatePower(i);
    }

    return totalPower;
  }

  @override
  Widget build(BuildContext context) {
    final currentCircuit = _circuits[_currentCircuitIndex];

    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
        side: BorderSide(color: _primaryColor.withAlpha(77)),
      ),
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Title and description
            Text(
              widget.data['title'] ?? 'Circuit Simulator',
              style: TextStyle(
                fontSize: 20,
                fontWeight: FontWeight.bold,
                color: _textColor,
              ),
            ),
            const SizedBox(height: 8),
            Text(
              widget.data['description'] ??
                  'Explore electrical circuits and their behavior',
              style: TextStyle(
                fontSize: 14,
                color: _textColor.withAlpha(179),
              ),
            ),
            const SizedBox(height: 16),

            // Circuit selector
            _buildCircuitSelector(),

            const SizedBox(height: 16),

            // Circuit simulator
            _buildCircuitSimulator(currentCircuit),

            const SizedBox(height: 16),

            // Component controls
            _buildComponentControls(currentCircuit),

            const SizedBox(height: 16),

            // Circuit controls
            _buildCircuitControls(),

            const SizedBox(height: 16),

            // Circuit information
            if (_showDescription) _buildCircuitInfo(currentCircuit),
          ],
        ),
      ),
    );
  }
}
