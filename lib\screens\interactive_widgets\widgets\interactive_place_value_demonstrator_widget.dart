import 'package:flutter/material.dart';
import 'dart:math' as math;
import 'dart:math' show Random;

/// Custom painter for place value visualization
class PlaceValueVisualizationPainter extends CustomPainter {
  final int number;
  final int currentStep;
  final double animationValue;
  final Color primaryColor;
  final Color secondaryColor;
  final Color textColor;
  final bool showExponents;
  final int base;
  final List<int> digits;
  final List<String> placeNames;

  PlaceValueVisualizationPainter({
    required this.number,
    required this.currentStep,
    required this.animationValue,
    required this.primaryColor,
    required this.secondaryColor,
    required this.textColor,
    required this.showExponents,
    required this.base,
    required this.digits,
    required this.placeNames,
  });

  @override
  void paint(Canvas canvas, Size size) {
    final double blockWidth = size.width * 0.8 / digits.length;
    final double blockHeight = 60;
    final double startX = size.width * 0.1;
    final double startY = size.height / 2 - blockHeight / 2;

    // Draw place value blocks
    for (int i = 0; i < digits.length; i++) {
      final int placeIndex = digits.length - 1 - i;
      final int digit = digits[placeIndex];
      final double x = startX + i * blockWidth;

      // Determine visibility based on current step and animation
      double opacity = 1.0;
      if (currentStep == 0) {
        opacity = math.min(1.0, animationValue * digits.length - i);
        if (opacity < 0) opacity = 0;
      }

      if (opacity > 0) {
        // Draw block
        final Rect rect = Rect.fromLTWH(x, startY, blockWidth, blockHeight);

        // Draw block background
        canvas.drawRect(
          rect,
          Paint()
            ..color = primaryColor.withAlpha((200 * opacity).toInt())
            ..style = PaintingStyle.fill,
        );

        // Draw block border
        canvas.drawRect(
          rect,
          Paint()
            ..color = secondaryColor.withAlpha((200 * opacity).toInt())
            ..style = PaintingStyle.stroke
            ..strokeWidth = 2,
        );

        // Draw digit
        _drawText(
          canvas,
          digit.toString(),
          x + blockWidth / 2,
          startY + blockHeight / 2,
          TextStyle(
            color: Colors.white,
            fontSize: 24,
            fontWeight: FontWeight.bold,
          ),
          opacity,
        );

        // Draw place value name
        String placeName = placeIndex < placeNames.length ? placeNames[placeIndex] : '';
        _drawText(
          canvas,
          placeName,
          x + blockWidth / 2,
          startY + blockHeight + 20,
          TextStyle(
            color: textColor,
            fontSize: 14,
            fontWeight: FontWeight.bold,
          ),
          opacity,
        );

        // Draw place value as power
        if (showExponents && currentStep >= 1) {
          double powerOpacity = currentStep == 1 ? animationValue : 1.0;
          String powerText = placeIndex == 0 ? '10⁰' : '10${_getSuperscript(placeIndex)}';
          _drawText(
            canvas,
            powerText,
            x + blockWidth / 2,
            startY - 20,
            TextStyle(
              color: secondaryColor,
              fontSize: 14,
              fontWeight: FontWeight.bold,
            ),
            powerOpacity * opacity,
          );
        }
      }
    }

    // Draw expanded form in step 2
    if (currentStep >= 2) {
      double expandedOpacity = currentStep == 2 ? animationValue : 1.0;

      String expandedForm = _getExpandedForm();
      _drawText(
        canvas,
        expandedForm,
        size.width / 2,
        size.height - 40,
        TextStyle(
          color: textColor,
          fontSize: 16,
          fontWeight: FontWeight.bold,
        ),
        expandedOpacity,
      );
    }
  }

  void _drawText(Canvas canvas, String text, double x, double y, TextStyle style, double opacity) {
    final textPainter = TextPainter(
      text: TextSpan(
        text: text,
        style: style.copyWith(
          color: style.color!.withAlpha((style.color!.alpha * opacity).toInt()),
        ),
      ),
      textDirection: TextDirection.ltr,
      textAlign: TextAlign.center,
    );
    textPainter.layout();
    textPainter.paint(
      canvas,
      Offset(x - textPainter.width / 2, y - textPainter.height / 2),
    );
  }

  String _getSuperscript(int number) {
    const Map<String, String> superscripts = {
      '0': '⁰', '1': '¹', '2': '²', '3': '³', '4': '⁴',
      '5': '⁵', '6': '⁶', '7': '⁷', '8': '⁸', '9': '⁹',
    };

    return number.toString().split('').map((digit) => superscripts[digit] ?? digit).join();
  }

  String _getExpandedForm() {
    List<String> parts = [];

    for (int i = 0; i < digits.length; i++) {
      int placeIndex = digits.length - 1 - i;
      int digit = digits[placeIndex];

      if (digit > 0) {
        if (placeIndex == 0) {
          parts.add('$digit');
        } else {
          parts.add('$digit × ${base.toString()}${_getSuperscript(placeIndex)}');
        }
      }
    }

    return parts.join(' + ');
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) {
    return true;
  }
}

/// A widget that demonstrates place value concepts for elementary math education.
class InteractivePlaceValueDemonstratorWidget extends StatefulWidget {
  final Map<String, dynamic> data;
  final Function(bool)? onStateChanged;
  final Color primaryColor;
  final Color secondaryColor;
  final Color backgroundColor;
  final Color textColor;

  const InteractivePlaceValueDemonstratorWidget({
    super.key,
    this.data = const {},
    this.onStateChanged,
    this.primaryColor = Colors.blue,
    this.secondaryColor = Colors.orange,
    this.backgroundColor = Colors.white,
    this.textColor = Colors.black87,
  });

  @override
  State<InteractivePlaceValueDemonstratorWidget> createState() =>
      _InteractivePlaceValueDemonstratorWidgetState();
}

class _InteractivePlaceValueDemonstratorWidgetState
    extends State<InteractivePlaceValueDemonstratorWidget>
    with SingleTickerProviderStateMixin {
  // Controllers
  late AnimationController _animationController;
  late Animation<double> _animation;
  final TextEditingController _numberController = TextEditingController();

  // State variables
  bool _isAnimating = false;
  bool _isCompleted = false;
  int _currentStep = 0;
  List<String> _steps = [];
  String? _errorMessage;
  String? _feedbackMessage;
  bool _showQuestion = false;
  String? _selectedAnswer;
  String? _correctAnswer;
  List<String> _answerOptions = [];
  bool _showExponents = true;
  int _base = 10;

  // Number values
  int _number = 0;
  List<int> _digits = [];

  // Place value names for base 10
  final List<String> _placeNames = [
    'Ones', 'Tens', 'Hundreds', 'Thousands',
    'Ten Thousands', 'Hundred Thousands', 'Millions'
  ];

  // Constants
  final int _maxNumber = 9999999;

  @override
  void initState() {
    super.initState();

    // Initialize animation controller
    _animationController = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 1500),
    );

    _animation = CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeInOut,
    );

    // Add listener to animation controller
    _animationController.addStatusListener((status) {
      if (status == AnimationStatus.completed) {
        if (_currentStep < _steps.length - 1) {
          setState(() {
            _currentStep++;
          });
          _animationController.reset();
          _animationController.forward();
        } else {
          setState(() {
            _isAnimating = false;
            _showQuestion = true;
          });
        }
      }
    });

    // Set initial values
    _numberController.text = '1234';

    // Initialize with default values
    _updateNumber();
  }

  @override
  void dispose() {
    _animationController.dispose();
    _numberController.dispose();
    super.dispose();
  }

  void _updateNumber() {
    try {
      _number = int.parse(_numberController.text);

      // Validate input
      if (_number < 0 || _number > _maxNumber) {
        setState(() {
          _errorMessage = 'Please enter a number between 0 and $_maxNumber';
        });
        return;
      }

      // Extract digits
      _digits = _number.toString().split('').map(int.parse).toList();

      setState(() {
        _errorMessage = null;
        _feedbackMessage = null;
        _showQuestion = false;
        _selectedAnswer = null;
        _currentStep = 0;
      });

      // Generate steps
      _generateSteps();

      // Generate question and answers
      _generateQuestion();

    } catch (e) {
      setState(() {
        _errorMessage = 'Please enter a valid number';
      });
    }
  }

  void _generateSteps() {
    _steps = [
      'Identify the digits in $_number',
      'Recognize the place value of each digit',
      'Write $_number in expanded form'
    ];
  }

  void _generateQuestion() {
    // Generate a question about place value
    Random random = Random();
    int digitPosition = random.nextInt(_digits.length);
    int digit = _digits[_digits.length - 1 - digitPosition];

    String placeName = digitPosition < _placeNames.length ? _placeNames[digitPosition] : '';

    // Create question about the value of a specific digit
    _correctAnswer = (digit * math.pow(_base, digitPosition)).toInt().toString();

    // Generate answer options
    List<String> options = [_correctAnswer!];

    // Add wrong answers
    while (options.length < 4) {
      int wrongAnswer;
      if (random.nextBool()) {
        // Wrong place value
        int wrongPosition = (digitPosition + 1 + random.nextInt(_digits.length - 1)) % _digits.length;
        wrongAnswer = (digit * math.pow(_base, wrongPosition)).toInt();
      } else {
        // Wrong digit
        int wrongDigit = (digit + 1 + random.nextInt(8)) % 10;
        wrongAnswer = (wrongDigit * math.pow(_base, digitPosition)).toInt();
      }

      String wrongAnswerStr = wrongAnswer.toString();
      if (!options.contains(wrongAnswerStr)) {
        options.add(wrongAnswerStr);
      }
    }

    options.shuffle();
    _answerOptions = options;
  }

  void _startAnimation() {
    if (_steps.isEmpty) return;

    setState(() {
      _currentStep = 0;
      _isAnimating = true;
      _showQuestion = false;
    });

    _animationController.reset();
    _animationController.forward();
  }

  void _stopAnimation() {
    setState(() {
      _isAnimating = false;
    });
    _animationController.stop();
  }

  void _resetAnimation() {
    setState(() {
      _currentStep = 0;
      _isAnimating = false;
      _showQuestion = false;
      _selectedAnswer = null;
      _feedbackMessage = null;
    });
    _animationController.reset();
  }

  void _checkAnswer(String answer) {
    setState(() {
      _selectedAnswer = answer;
      if (answer == _correctAnswer) {
        _feedbackMessage = 'Correct! Great job!';
        _isCompleted = true;
      } else {
        _feedbackMessage = 'Not quite. Try again!';
      }
    });

    // Notify parent of completion status
    widget.onStateChanged?.call(_isCompleted);
  }

  void _generateRandomNumber() {
    Random random = Random();
    int numDigits = random.nextInt(6) + 1; // 1 to 6 digits
    int randomNumber = 0;

    for (int i = 0; i < numDigits; i++) {
      randomNumber = randomNumber * 10 + random.nextInt(10);
    }

    // Ensure we don't generate 0
    if (randomNumber == 0) randomNumber = random.nextInt(9) + 1;

    setState(() {
      _numberController.text = randomNumber.toString();
    });

    _updateNumber();
  }

  void _toggleExponents() {
    setState(() {
      _showExponents = !_showExponents;
    });
  }

  Widget _buildInputControls() {
    return Column(
      children: [
        // Number input
        Row(
          children: [
            Expanded(
              child: TextField(
                controller: _numberController,
                keyboardType: TextInputType.number,
                decoration: InputDecoration(
                  labelText: 'Enter a Number',
                  border: OutlineInputBorder(),
                  contentPadding: EdgeInsets.symmetric(horizontal: 12, vertical: 8),
                ),
                onChanged: (_) => _updateNumber(),
              ),
            ),
            const SizedBox(width: 16),
            ElevatedButton(
              onPressed: _generateRandomNumber,
              style: ElevatedButton.styleFrom(
                backgroundColor: widget.secondaryColor,
                padding: EdgeInsets.symmetric(horizontal: 12, vertical: 8),
              ),
              child: Text('Random'),
            ),
          ],
        ),

        const SizedBox(height: 16),

        // Options
        Row(
          children: [
            Text(
              'Show Powers of 10:',
              style: TextStyle(
                fontWeight: FontWeight.bold,
                color: widget.textColor,
              ),
            ),
            Checkbox(
              value: _showExponents,
              onChanged: (bool? value) {
                if (value != null) {
                  _toggleExponents();
                }
              },
              activeColor: widget.primaryColor,
            ),
            const Spacer(),
            Text(
              'Base: $_base',
              style: TextStyle(
                fontWeight: FontWeight.bold,
                color: widget.textColor,
              ),
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildVisualizationArea() {
    return Container(
      height: 200,
      width: double.infinity,
      decoration: BoxDecoration(
        color: Colors.grey.shade50,
        borderRadius: BorderRadius.circular(8),
        border: Border.all(
          color: _isCompleted ? Colors.green : Colors.grey.shade300,
          width: _isCompleted ? 2 : 1,
        ),
      ),
      child: AnimatedBuilder(
        animation: _animation,
        builder: (context, child) {
          return CustomPaint(
            painter: PlaceValueVisualizationPainter(
              number: _number,
              currentStep: _currentStep,
              animationValue: _isAnimating ? _animation.value : 1.0,
              primaryColor: widget.primaryColor,
              secondaryColor: widget.secondaryColor,
              textColor: widget.textColor,
              showExponents: _showExponents,
              base: _base,
              digits: _digits,
              placeNames: _placeNames,
            ),
            child: Container(),
          );
        },
      ),
    );
  }

  Widget _buildAnimationControls() {
    return Row(
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        IconButton(
          icon: Icon(_isAnimating ? Icons.pause : Icons.play_arrow),
          onPressed: _isAnimating ? _stopAnimation : _startAnimation,
          color: widget.primaryColor,
          iconSize: 32,
        ),
        IconButton(
          icon: const Icon(Icons.replay),
          onPressed: _resetAnimation,
          color: widget.primaryColor,
          iconSize: 32,
        ),
      ],
    );
  }

  Widget _buildQuestionSection() {
    // Find the digit position for the question
    Random random = Random();
    int digitPosition = random.nextInt(_digits.length);
    int digit = _digits[_digits.length - 1 - digitPosition];
    String placeName = digitPosition < _placeNames.length ? _placeNames[digitPosition] : '';

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'What is the value of the digit $digit in the ${placeName.toLowerCase()} place?',
          style: TextStyle(
            fontSize: 18,
            fontWeight: FontWeight.bold,
            color: widget.textColor,
          ),
        ),
        const SizedBox(height: 12),
        Wrap(
          spacing: 8,
          runSpacing: 8,
          children: _answerOptions.map((option) {
            bool isSelected = _selectedAnswer == option;
            bool isCorrect = option == _correctAnswer;

            Color buttonColor = isSelected
                ? (isCorrect ? Colors.green : Colors.red)
                : widget.primaryColor;

            return ElevatedButton(
              onPressed: _selectedAnswer == null ? () => _checkAnswer(option) : null,
              style: ElevatedButton.styleFrom(
                backgroundColor: buttonColor,
                padding: EdgeInsets.symmetric(horizontal: 16, vertical: 8),
              ),
              child: Text(
                option,
                style: TextStyle(
                  fontSize: 16,
                  color: Colors.white,
                ),
              ),
            );
          }).toList(),
        ),
      ],
    );
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: widget.backgroundColor,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withAlpha(50),
            spreadRadius: 1,
            blurRadius: 3,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Title
          Text(
            'Place Value Demonstrator',
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
              color: widget.textColor,
            ),
          ),

          const SizedBox(height: 16),

          // Input controls
          _buildInputControls(),

          if (_errorMessage != null)
            Padding(
              padding: const EdgeInsets.only(top: 8.0),
              child: Text(
                _errorMessage!,
                style: TextStyle(
                  color: Colors.red,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ),

          const SizedBox(height: 16),

          // Visualization area
          _buildVisualizationArea(),

          const SizedBox(height: 16),

          // Step description
          if (_steps.isNotEmpty && _currentStep < _steps.length)
            Text(
              _steps[_currentStep],
              style: TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.bold,
                color: widget.primaryColor,
              ),
            ),

          const SizedBox(height: 16),

          // Animation controls
          _buildAnimationControls(),

          const SizedBox(height: 16),

          // Question and feedback
          if (_showQuestion) _buildQuestionSection(),

          if (_feedbackMessage != null)
            Padding(
              padding: const EdgeInsets.only(top: 8.0),
              child: Text(
                _feedbackMessage!,
                style: TextStyle(
                  color: _selectedAnswer == _correctAnswer
                      ? Colors.green
                      : Colors.red,
                  fontWeight: FontWeight.bold,
                  fontSize: 16,
                ),
              ),
            ),
        ],
      ),
    );
  }
}
