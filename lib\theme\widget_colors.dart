import 'package:flutter/material.dart';

/// Standardized color system for interactive widgets
/// Ensures consistent visual appearance across all widget categories
class WidgetColors {
  // Private constructor to prevent instantiation
  WidgetColors._();

  // ============================================================================
  // CATEGORY COLORS
  // ============================================================================
  
  /// Mathematics category color - Blue
  static const Color mathematics = Color(0xFF4285F4);
  static const Color mathematicsLight = Color(0xFFE3F2FD);
  static const Color mathematicsDark = Color(0xFF1976D2);
  
  /// Science category color - Amber
  static const Color science = Color(0xFFFFB300);
  static const Color scienceLight = Color(0xFFFFF8E1);
  static const Color scienceDark = Color(0xFFFF8F00);
  
  /// Computer Science category color - Purple
  static const Color computerScience = Color(0xFF9C27B0);
  static const Color computerScienceLight = Color(0xFFF3E5F5);
  static const Color computerScienceDark = Color(0xFF7B1FA2);
  
  /// Reasoning category color - Deep Orange
  static const Color reasoning = Color(0xFFFF5722);
  static const Color reasoningLight = Color(0xFFFBE9E7);
  static const Color reasoningDark = Color(0xFFE64A19);
  
  /// Technology category color - Cyan
  static const Color technology = Color(0xFF00BCD4);
  static const Color technologyLight = Color(0xFFE0F7FA);
  static const Color technologyDark = Color(0xFF0097A7);
  
  /// Puzzles category color - Light Green
  static const Color puzzles = Color(0xFF8BC34A);
  static const Color puzzlesLight = Color(0xFFF1F8E9);
  static const Color puzzlesDark = Color(0xFF689F38);
  
  /// Curiosity Corner category color - Orange
  static const Color curiosityCorner = Color(0xFFFF9800);
  static const Color curiosityCornerLight = Color(0xFFFFF3E0);
  static const Color curiosityCornerDark = Color(0xFFF57C00);

  // ============================================================================
  // WIDGET STATE COLORS
  // ============================================================================
  
  /// Success states (correct answers, completion)
  static const Color success = Color(0xFF4CAF50);
  static const Color successLight = Color(0xFFE8F5E8);
  static const Color successDark = Color(0xFF388E3C);
  
  /// Error states (incorrect answers, validation errors)
  static const Color error = Color(0xFFF44336);
  static const Color errorLight = Color(0xFFFFEBEE);
  static const Color errorDark = Color(0xFFD32F2F);
  
  /// Warning states (hints, cautions)
  static const Color warning = Color(0xFFFF9800);
  static const Color warningLight = Color(0xFFFFF3E0);
  static const Color warningDark = Color(0xFFF57C00);
  
  /// Info states (tips, additional information)
  static const Color info = Color(0xFF2196F3);
  static const Color infoLight = Color(0xFFE3F2FD);
  static const Color infoDark = Color(0xFF1976D2);

  // ============================================================================
  // NEUTRAL COLORS
  // ============================================================================
  
  /// Primary text color
  static const Color textPrimary = Color(0xFF212121);
  
  /// Secondary text color
  static const Color textSecondary = Color(0xFF757575);
  
  /// Disabled text color
  static const Color textDisabled = Color(0xFFBDBDBD);
  
  /// Background colors
  static const Color backgroundPrimary = Color(0xFFFFFFFF);
  static const Color backgroundSecondary = Color(0xFFFAFAFA);
  static const Color backgroundTertiary = Color(0xFFF5F5F5);
  
  /// Border colors
  static const Color borderLight = Color(0xFFE0E0E0);
  static const Color borderMedium = Color(0xFFBDBDBD);
  static const Color borderDark = Color(0xFF9E9E9E);
  
  /// Shadow colors
  static const Color shadowLight = Color(0x1A000000);
  static const Color shadowMedium = Color(0x33000000);
  static const Color shadowDark = Color(0x4D000000);

  // ============================================================================
  // INTERACTIVE ELEMENT COLORS
  // ============================================================================
  
  /// Button colors
  static const Color buttonPrimary = Color(0xFF7C42D2);
  static const Color buttonPrimaryHover = Color(0xFF6A36B8);
  static const Color buttonSecondary = Color(0xFFE0E0E0);
  static const Color buttonSecondaryHover = Color(0xFFD5D5D5);
  
  /// Input field colors
  static const Color inputBackground = Color(0xFFFAFAFA);
  static const Color inputBorder = Color(0xFFE0E0E0);
  static const Color inputBorderFocused = Color(0xFF7C42D2);
  static const Color inputBorderError = Color(0xFFF44336);
  
  /// Progress indicator colors
  static const Color progressBackground = Color(0xFFE0E0E0);
  static const Color progressForeground = Color(0xFF7C42D2);

  // ============================================================================
  // HELPER METHODS
  // ============================================================================
  
  /// Get category color by category ID
  static Color getCategoryColor(String categoryId) {
    switch (categoryId.toLowerCase()) {
      case 'maths':
      case 'mathematics':
        return mathematics;
      case 'science':
        return science;
      case 'computer_science':
      case 'cs':
        return computerScience;
      case 'reasoning':
        return reasoning;
      case 'technology':
      case 'tech':
        return technology;
      case 'puzzles':
        return puzzles;
      case 'curiosity_corner':
        return curiosityCorner;
      default:
        return mathematics; // Default fallback
    }
  }
  
  /// Get light variant of category color
  static Color getCategoryColorLight(String categoryId) {
    switch (categoryId.toLowerCase()) {
      case 'maths':
      case 'mathematics':
        return mathematicsLight;
      case 'science':
        return scienceLight;
      case 'computer_science':
      case 'cs':
        return computerScienceLight;
      case 'reasoning':
        return reasoningLight;
      case 'technology':
      case 'tech':
        return technologyLight;
      case 'puzzles':
        return puzzlesLight;
      case 'curiosity_corner':
        return curiosityCornerLight;
      default:
        return mathematicsLight; // Default fallback
    }
  }
  
  /// Get dark variant of category color
  static Color getCategoryColorDark(String categoryId) {
    switch (categoryId.toLowerCase()) {
      case 'maths':
      case 'mathematics':
        return mathematicsDark;
      case 'science':
        return scienceDark;
      case 'computer_science':
      case 'cs':
        return computerScienceDark;
      case 'reasoning':
        return reasoningDark;
      case 'technology':
      case 'tech':
        return technologyDark;
      case 'puzzles':
        return puzzlesDark;
      case 'curiosity_corner':
        return curiosityCornerDark;
      default:
        return mathematicsDark; // Default fallback
    }
  }
  
  /// Get color with opacity
  static Color withOpacity(Color color, double opacity) {
    return color.withOpacity(opacity);
  }
  
  /// Get color with alpha value (0-255)
  static Color withAlpha(Color color, int alpha) {
    return color.withAlpha(alpha);
  }
}
