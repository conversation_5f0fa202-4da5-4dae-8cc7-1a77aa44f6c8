import 'package:flutter/material.dart';

/// A widget that provides an interactive periodic table of elements
class InteractivePeriodicTableWidget extends StatefulWidget {
  /// The data for this widget
  final Map<String, dynamic> data;

  /// Optional callback when the widget state changes
  final Function(bool isCompleted)? onStateChanged;

  const InteractivePeriodicTableWidget({
    super.key,
    required this.data,
    this.onStateChanged,
  });

  /// Factory constructor to create the widget from JSON data
  factory InteractivePeriodicTableWidget.fromData(
      Map<String, dynamic> data) {
    return InteractivePeriodicTableWidget(
      data: data,
    );
  }

  @override
  State<InteractivePeriodicTableWidget> createState() =>
      _InteractivePeriodicTableWidgetState();
}

/// Element model for the periodic table
class Element {
  final int atomicNumber;
  final String symbol;
  final String name;
  final double atomicWeight;
  final String category;
  final int group;
  final int period;
  final String electronConfiguration;
  final double electronegativity;
  final double atomicRadius;
  final double ionizationEnergy;
  final String description;
  final Color color;

  Element({
    required this.atomicNumber,
    required this.symbol,
    required this.name,
    required this.atomicWeight,
    required this.category,
    required this.group,
    required this.period,
    required this.electronConfiguration,
    required this.electronegativity,
    required this.atomicRadius,
    required this.ionizationEnergy,
    required this.description,
    required this.color,
  });
}

/// Category model for element classification
class ElementCategory {
  final String name;
  final Color color;
  final String description;

  ElementCategory({
    required this.name,
    required this.color,
    required this.description,
  });
}

class _InteractivePeriodicTableWidgetState
    extends State<InteractivePeriodicTableWidget> {
  // Colors
  late Color _primaryColor;
  late Color _textColor;
  late Color _backgroundColor;

  // Element categories
  late List<ElementCategory> _categories;

  // Elements
  late List<Element> _elements;
  Element? _selectedElement;

  // UI state
  bool _showDescription = true;
  String _searchQuery = '';
  String _selectedCategory = 'All';
  String _selectedProperty = 'Atomic Number';
  bool _showLegend = true;

  // View mode
  String _viewMode = 'Standard'; // Standard, List, Detailed

  @override
  void initState() {
    super.initState();

    // Initialize colors
    _primaryColor = _getColorFromHex(
        widget.data['primaryColor'] ?? '#2196F3'); // Blue
    _textColor =
        _getColorFromHex(widget.data['textColor'] ?? '#212121'); // Dark Grey
    _backgroundColor = _getColorFromHex(
        widget.data['backgroundColor'] ?? '#FFFFFF'); // White

    // Initialize categories
    _categories = _createElementCategories();

    // Initialize elements
    _elements = _createElements();
  }

  // Convert hex color string to Color
  Color _getColorFromHex(String hexColor) {
    hexColor = hexColor.replaceAll('#', '');
    if (hexColor.length == 6) {
      hexColor = 'FF$hexColor';
    }
    return Color(int.parse(hexColor, radix: 16));
  }

  // Create element categories
  List<ElementCategory> _createElementCategories() {
    return [
      ElementCategory(
        name: 'Alkali Metals',
        color: Colors.red,
        description: 'Highly reactive metals that form strong bases when combined with other elements.',
      ),
      ElementCategory(
        name: 'Alkaline Earth Metals',
        color: Colors.orange,
        description: 'Reactive metals that form alkaline solutions and have two electrons in their outer shell.',
      ),
      ElementCategory(
        name: 'Transition Metals',
        color: Colors.yellow,
        description: 'Metals that form colored compounds and can have multiple oxidation states.',
      ),
      ElementCategory(
        name: 'Post-Transition Metals',
        color: Colors.green.shade300,
        description: 'Metals with properties between transition metals and nonmetals.',
      ),
      ElementCategory(
        name: 'Metalloids',
        color: Colors.green,
        description: 'Elements with properties of both metals and nonmetals.',
      ),
      ElementCategory(
        name: 'Nonmetals',
        color: Colors.blue,
        description: 'Elements that are poor conductors of heat and electricity.',
      ),
      ElementCategory(
        name: 'Halogens',
        color: Colors.indigo,
        description: 'Highly reactive nonmetals that readily form salts with metals.',
      ),
      ElementCategory(
        name: 'Noble Gases',
        color: Colors.purple,
        description: 'Unreactive gases with complete electron shells.',
      ),
      ElementCategory(
        name: 'Lanthanides',
        color: Colors.pink.shade200,
        description: 'Rare earth elements with atomic numbers 57-71.',
      ),
      ElementCategory(
        name: 'Actinides',
        color: Colors.pink,
        description: 'Radioactive elements with atomic numbers 89-103.',
      ),
    ];
  }

  // Create elements (first 20 elements for brevity)
  List<Element> _createElements() {
    return [
      Element(
        atomicNumber: 1,
        symbol: 'H',
        name: 'Hydrogen',
        atomicWeight: 1.008,
        category: 'Nonmetals',
        group: 1,
        period: 1,
        electronConfiguration: '1s¹',
        electronegativity: 2.20,
        atomicRadius: 38,
        ionizationEnergy: 13.598,
        description: 'Hydrogen is the lightest element and the most abundant chemical substance in the universe. It is colorless, odorless, tasteless, non-toxic, and highly combustible.',
        color: Colors.blue,
      ),
      Element(
        atomicNumber: 2,
        symbol: 'He',
        name: 'Helium',
        atomicWeight: 4.0026,
        category: 'Noble Gases',
        group: 18,
        period: 1,
        electronConfiguration: '1s²',
        electronegativity: 0,
        atomicRadius: 32,
        ionizationEnergy: 24.587,
        description: 'Helium is a colorless, odorless, tasteless, non-toxic, inert, monatomic gas. It is the second lightest and second most abundant element in the observable universe.',
        color: Colors.purple,
      ),
      Element(
        atomicNumber: 3,
        symbol: 'Li',
        name: 'Lithium',
        atomicWeight: 6.94,
        category: 'Alkali Metals',
        group: 1,
        period: 2,
        electronConfiguration: '[He] 2s¹',
        electronegativity: 0.98,
        atomicRadius: 152,
        ionizationEnergy: 5.392,
        description: 'Lithium is a soft, silvery-white alkali metal. Under standard conditions, it is the lightest metal and the lightest solid element. It is highly reactive and flammable, and must be stored in mineral oil.',
        color: Colors.red,
      ),
      Element(
        atomicNumber: 4,
        symbol: 'Be',
        name: 'Beryllium',
        atomicWeight: 9.0122,
        category: 'Alkaline Earth Metals',
        group: 2,
        period: 2,
        electronConfiguration: '[He] 2s²',
        electronegativity: 1.57,
        atomicRadius: 112,
        ionizationEnergy: 9.323,
        description: 'Beryllium is a relatively rare element in the universe. It is a divalent element which occurs naturally only in combination with other elements in minerals.',
        color: Colors.orange,
      ),
      Element(
        atomicNumber: 5,
        symbol: 'B',
        name: 'Boron',
        atomicWeight: 10.81,
        category: 'Metalloids',
        group: 13,
        period: 2,
        electronConfiguration: '[He] 2s² 2p¹',
        electronegativity: 2.04,
        atomicRadius: 85,
        ionizationEnergy: 8.298,
        description: 'Boron is a chemical element with properties between those of carbon and aluminum. It is a semiconductor rather than a metallic element.',
        color: Colors.green,
      ),
      Element(
        atomicNumber: 6,
        symbol: 'C',
        name: 'Carbon',
        atomicWeight: 12.011,
        category: 'Nonmetals',
        group: 14,
        period: 2,
        electronConfiguration: '[He] 2s² 2p²',
        electronegativity: 2.55,
        atomicRadius: 77,
        ionizationEnergy: 11.260,
        description: 'Carbon is a nonmetallic chemical element that is the basis of all known life. It is the fourth most abundant element in the universe by mass after hydrogen, helium, and oxygen.',
        color: Colors.blue,
      ),
      Element(
        atomicNumber: 7,
        symbol: 'N',
        name: 'Nitrogen',
        atomicWeight: 14.007,
        category: 'Nonmetals',
        group: 15,
        period: 2,
        electronConfiguration: '[He] 2s² 2p³',
        electronegativity: 3.04,
        atomicRadius: 75,
        ionizationEnergy: 14.534,
        description: 'Nitrogen is a colorless, odorless, tasteless gas that makes up about 78% of Earth\'s atmosphere. It is an essential component of proteins and nucleic acids.',
        color: Colors.blue,
      ),
      Element(
        atomicNumber: 8,
        symbol: 'O',
        name: 'Oxygen',
        atomicWeight: 15.999,
        category: 'Nonmetals',
        group: 16,
        period: 2,
        electronConfiguration: '[He] 2s² 2p⁴',
        electronegativity: 3.44,
        atomicRadius: 73,
        ionizationEnergy: 13.618,
        description: 'Oxygen is a highly reactive nonmetal that readily forms compounds with most elements. It is the third most abundant element in the universe and makes up about 21% of Earth\'s atmosphere.',
        color: Colors.blue,
      ),
      Element(
        atomicNumber: 9,
        symbol: 'F',
        name: 'Fluorine',
        atomicWeight: 18.998,
        category: 'Halogens',
        group: 17,
        period: 2,
        electronConfiguration: '[He] 2s² 2p⁵',
        electronegativity: 3.98,
        atomicRadius: 71,
        ionizationEnergy: 17.423,
        description: 'Fluorine is the lightest halogen and the most electronegative element. It is extremely reactive and forms compounds with most other elements, including the noble gases.',
        color: Colors.indigo,
      ),
      Element(
        atomicNumber: 10,
        symbol: 'Ne',
        name: 'Neon',
        atomicWeight: 20.180,
        category: 'Noble Gases',
        group: 18,
        period: 2,
        electronConfiguration: '[He] 2s² 2p⁶',
        electronegativity: 0,
        atomicRadius: 69,
        ionizationEnergy: 21.565,
        description: 'Neon is a colorless, odorless, inert monatomic gas. It is the second-lightest noble gas, after helium. It is known for its bright reddish-orange glow when used in gas-discharge lamps and neon signs.',
        color: Colors.purple,
      ),
    ];
  }

  // Build search and filter controls
  Widget _buildSearchAndFilterControls() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Search bar
        TextField(
          decoration: InputDecoration(
            hintText: 'Search elements...',
            prefixIcon: const Icon(Icons.search),
            border: OutlineInputBorder(
              borderRadius: BorderRadius.circular(8),
            ),
            contentPadding: const EdgeInsets.symmetric(vertical: 12),
          ),
          onChanged: (value) {
            setState(() {
              _searchQuery = value;
            });
          },
        ),
        const SizedBox(height: 12),

        // Category and property filters
        Row(
          children: [
            // Category dropdown
            Expanded(
              child: DropdownButtonFormField<String>(
                decoration: InputDecoration(
                  labelText: 'Category',
                  border: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(8),
                  ),
                  contentPadding: const EdgeInsets.symmetric(horizontal: 12),
                ),
                value: _selectedCategory,
                items: [
                  const DropdownMenuItem(
                    value: 'All',
                    child: Text('All Categories'),
                  ),
                  ..._categories.map((category) {
                    return DropdownMenuItem(
                      value: category.name,
                      child: Text(category.name),
                    );
                  }),
                ],
                onChanged: (value) {
                  if (value != null) {
                    setState(() {
                      _selectedCategory = value;
                    });
                  }
                },
              ),
            ),
            const SizedBox(width: 12),

            // Property dropdown
            Expanded(
              child: DropdownButtonFormField<String>(
                decoration: InputDecoration(
                  labelText: 'Color by',
                  border: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(8),
                  ),
                  contentPadding: const EdgeInsets.symmetric(horizontal: 12),
                ),
                value: _selectedProperty,
                items: const [
                  DropdownMenuItem(
                    value: 'Atomic Number',
                    child: Text('Atomic Number'),
                  ),
                  DropdownMenuItem(
                    value: 'Category',
                    child: Text('Category'),
                  ),
                  DropdownMenuItem(
                    value: 'Electronegativity',
                    child: Text('Electronegativity'),
                  ),
                  DropdownMenuItem(
                    value: 'Atomic Radius',
                    child: Text('Atomic Radius'),
                  ),
                  DropdownMenuItem(
                    value: 'Ionization Energy',
                    child: Text('Ionization Energy'),
                  ),
                ],
                onChanged: (value) {
                  if (value != null) {
                    setState(() {
                      _selectedProperty = value;
                    });
                  }
                },
              ),
            ),
          ],
        ),
      ],
    );
  }

  // Build view mode selector
  Widget _buildViewModeSelector() {
    return Row(
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        // Standard view button
        ChoiceChip(
          label: const Text('Standard'),
          selected: _viewMode == 'Standard',
          onSelected: (selected) {
            if (selected) {
              setState(() {
                _viewMode = 'Standard';
              });
            }
          },
          backgroundColor: Colors.grey.withAlpha(50),
          selectedColor: _primaryColor.withAlpha(100),
        ),
        const SizedBox(width: 12),

        // List view button
        ChoiceChip(
          label: const Text('List'),
          selected: _viewMode == 'List',
          onSelected: (selected) {
            if (selected) {
              setState(() {
                _viewMode = 'List';
              });
            }
          },
          backgroundColor: Colors.grey.withAlpha(50),
          selectedColor: _primaryColor.withAlpha(100),
        ),
        const SizedBox(width: 12),

        // Detailed view button
        ChoiceChip(
          label: const Text('Detailed'),
          selected: _viewMode == 'Detailed',
          onSelected: (selected) {
            if (selected) {
              setState(() {
                _viewMode = 'Detailed';
              });
            }
          },
          backgroundColor: Colors.grey.withAlpha(50),
          selectedColor: _primaryColor.withAlpha(100),
        ),
        const SizedBox(width: 24),

        // Legend toggle
        OutlinedButton.icon(
          onPressed: () {
            setState(() {
              _showLegend = !_showLegend;
            });
          },
          icon: Icon(_showLegend ? Icons.visibility_off : Icons.visibility),
          label: const Text('Legend'),
          style: OutlinedButton.styleFrom(
            foregroundColor: _primaryColor,
          ),
        ),
      ],
    );
  }

  // Build periodic table
  Widget _buildPeriodicTable() {
    // Filter elements based on search query and category
    final filteredElements = _elements.where((element) {
      final matchesSearch = _searchQuery.isEmpty ||
          element.name.toLowerCase().contains(_searchQuery.toLowerCase()) ||
          element.symbol.toLowerCase().contains(_searchQuery.toLowerCase()) ||
          element.atomicNumber.toString().contains(_searchQuery);

      final matchesCategory = _selectedCategory == 'All' ||
          element.category == _selectedCategory;

      return matchesSearch && matchesCategory;
    }).toList();

    // Build the appropriate view based on view mode
    switch (_viewMode) {
      case 'Standard':
        return _buildStandardPeriodicTable(filteredElements);
      case 'List':
        return _buildListView(filteredElements);
      case 'Detailed':
        return _buildDetailedView(filteredElements);
      default:
        return _buildStandardPeriodicTable(filteredElements);
    }
  }

  // Build standard periodic table view
  Widget _buildStandardPeriodicTable(List<Element> elements) {
    // For simplicity, we'll just show a grid of elements
    // A full periodic table would require a more complex layout
    return Container(
      height: 300,
      decoration: BoxDecoration(
        color: _backgroundColor,
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: Colors.grey.withAlpha(100)),
      ),
      child: GridView.builder(
        padding: const EdgeInsets.all(8),
        gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
          crossAxisCount: 5,
          childAspectRatio: 1,
          crossAxisSpacing: 8,
          mainAxisSpacing: 8,
        ),
        itemCount: elements.length,
        itemBuilder: (context, index) {
          final element = elements[index];
          return _buildElementTile(element);
        },
      ),
    );
  }

  // Build list view of elements
  Widget _buildListView(List<Element> elements) {
    return Container(
      height: 300,
      decoration: BoxDecoration(
        color: _backgroundColor,
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: Colors.grey.withAlpha(100)),
      ),
      child: ListView.builder(
        padding: const EdgeInsets.all(8),
        itemCount: elements.length,
        itemBuilder: (context, index) {
          final element = elements[index];
          return ListTile(
            leading: CircleAvatar(
              backgroundColor: _getCategoryColor(element.category),
              child: Text(
                element.symbol,
                style: const TextStyle(
                  color: Colors.white,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ),
            title: Text(element.name),
            subtitle: Text('Atomic Number: ${element.atomicNumber}'),
            trailing: Text('${element.atomicWeight}'),
            onTap: () {
              setState(() {
                _selectedElement = element;
              });
            },
            selected: _selectedElement == element,
          );
        },
      ),
    );
  }

  // Build detailed view of elements
  Widget _buildDetailedView(List<Element> elements) {
    return Container(
      height: 300,
      decoration: BoxDecoration(
        color: _backgroundColor,
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: Colors.grey.withAlpha(100)),
      ),
      child: ListView.builder(
        padding: const EdgeInsets.all(8),
        itemCount: elements.length,
        itemBuilder: (context, index) {
          final element = elements[index];
          return Card(
            margin: const EdgeInsets.only(bottom: 8),
            child: Padding(
              padding: const EdgeInsets.all(12),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    children: [
                      Container(
                        width: 60,
                        height: 60,
                        decoration: BoxDecoration(
                          color: _getCategoryColor(element.category),
                          borderRadius: BorderRadius.circular(8),
                        ),
                        child: Center(
                          child: Column(
                            mainAxisAlignment: MainAxisAlignment.center,
                            children: [
                              Text(
                                element.symbol,
                                style: const TextStyle(
                                  color: Colors.white,
                                  fontWeight: FontWeight.bold,
                                  fontSize: 20,
                                ),
                              ),
                              Text(
                                element.atomicNumber.toString(),
                                style: const TextStyle(
                                  color: Colors.white,
                                  fontSize: 12,
                                ),
                              ),
                            ],
                          ),
                        ),
                      ),
                      const SizedBox(width: 16),
                      Expanded(
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              element.name,
                              style: const TextStyle(
                                fontWeight: FontWeight.bold,
                                fontSize: 18,
                              ),
                            ),
                            Text(
                              'Category: ${element.category}',
                              style: TextStyle(
                                color: _textColor.withAlpha(179),
                              ),
                            ),
                            Text(
                              'Atomic Weight: ${element.atomicWeight}',
                              style: TextStyle(
                                color: _textColor.withAlpha(179),
                              ),
                            ),
                          ],
                        ),
                      ),
                      IconButton(
                        icon: const Icon(Icons.info_outline),
                        onPressed: () {
                          setState(() {
                            _selectedElement = element;
                          });
                        },
                      ),
                    ],
                  ),
                ],
              ),
            ),
          );
        },
      ),
    );
  }

  // Build element tile for standard view
  Widget _buildElementTile(Element element) {
    return InkWell(
      onTap: () {
        setState(() {
          _selectedElement = element;
        });
      },
      borderRadius: BorderRadius.circular(4),
      child: Container(
        decoration: BoxDecoration(
          color: _getElementColor(element),
          borderRadius: BorderRadius.circular(4),
          border: Border.all(
            color: _selectedElement == element
                ? _primaryColor
                : Colors.grey.withAlpha(100),
            width: _selectedElement == element ? 2 : 1,
          ),
        ),
        child: Padding(
          padding: const EdgeInsets.all(4),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Text(
                element.atomicNumber.toString(),
                style: TextStyle(
                  fontSize: 10,
                  color: _getContrastColor(_getElementColor(element)),
                ),
              ),
              Text(
                element.symbol,
                style: TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.bold,
                  color: _getContrastColor(_getElementColor(element)),
                ),
              ),
              Text(
                element.name.length > 8
                    ? '${element.name.substring(0, 6)}...'
                    : element.name,
                style: TextStyle(
                  fontSize: 10,
                  color: _getContrastColor(_getElementColor(element)),
                ),
                overflow: TextOverflow.ellipsis,
              ),
            ],
          ),
        ),
      ),
    );
  }

  // Build element details
  Widget _buildElementDetails() {
    final element = _selectedElement!;

    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: _getElementColor(element).withAlpha(50),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: _getElementColor(element)),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Element symbol and basic info
              Container(
                width: 80,
                height: 80,
                decoration: BoxDecoration(
                  color: _getElementColor(element),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Center(
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Text(
                        element.symbol,
                        style: TextStyle(
                          color: _getContrastColor(_getElementColor(element)),
                          fontWeight: FontWeight.bold,
                          fontSize: 28,
                        ),
                      ),
                      Text(
                        element.atomicNumber.toString(),
                        style: TextStyle(
                          color: _getContrastColor(_getElementColor(element)),
                          fontSize: 14,
                        ),
                      ),
                    ],
                  ),
                ),
              ),
              const SizedBox(width: 16),

              // Element details
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      element.name,
                      style: const TextStyle(
                        fontWeight: FontWeight.bold,
                        fontSize: 24,
                      ),
                    ),
                    Text(
                      'Category: ${element.category}',
                      style: TextStyle(
                        color: _textColor,
                        fontSize: 16,
                      ),
                    ),
                    Text(
                      'Atomic Weight: ${element.atomicWeight}',
                      style: TextStyle(
                        color: _textColor,
                        fontSize: 16,
                      ),
                    ),
                    Text(
                      'Group: ${element.group}, Period: ${element.period}',
                      style: TextStyle(
                        color: _textColor,
                        fontSize: 16,
                      ),
                    ),
                  ],
                ),
              ),

              // Close button
              IconButton(
                icon: const Icon(Icons.close),
                onPressed: () {
                  setState(() {
                    _selectedElement = null;
                  });
                },
              ),
            ],
          ),
          const SizedBox(height: 16),

          // Additional properties
          Text(
            'Properties',
            style: TextStyle(
              fontWeight: FontWeight.bold,
              fontSize: 18,
              color: _textColor,
            ),
          ),
          const SizedBox(height: 8),
          Row(
            children: [
              Expanded(
                child: _buildPropertyItem(
                  'Electron Configuration',
                  element.electronConfiguration,
                ),
              ),
              Expanded(
                child: _buildPropertyItem(
                  'Electronegativity',
                  element.electronegativity.toString(),
                ),
              ),
              Expanded(
                child: _buildPropertyItem(
                  'Atomic Radius (pm)',
                  element.atomicRadius.toString(),
                ),
              ),
              Expanded(
                child: _buildPropertyItem(
                  'Ionization Energy (eV)',
                  element.ionizationEnergy.toString(),
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),

          // Description
          if (_showDescription)
            Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'Description',
                  style: TextStyle(
                    fontWeight: FontWeight.bold,
                    fontSize: 18,
                    color: _textColor,
                  ),
                ),
                const SizedBox(height: 8),
                Text(
                  element.description,
                  style: TextStyle(
                    color: _textColor,
                    fontSize: 14,
                  ),
                ),
              ],
            ),

          const SizedBox(height: 16),
          Align(
            alignment: Alignment.centerRight,
            child: TextButton(
              onPressed: () {
                widget.onStateChanged?.call(true);
              },
              child: const Text('Mark as Completed'),
            ),
          ),
        ],
      ),
    );
  }

  // Build property item
  Widget _buildPropertyItem(String label, String value) {
    return Padding(
      padding: const EdgeInsets.all(4),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            label,
            style: TextStyle(
              fontWeight: FontWeight.bold,
              fontSize: 12,
              color: _textColor,
            ),
          ),
          const SizedBox(height: 4),
          Container(
            padding: const EdgeInsets.all(8),
            decoration: BoxDecoration(
              color: Colors.grey.withAlpha(30),
              borderRadius: BorderRadius.circular(4),
            ),
            child: Text(
              value,
              style: TextStyle(
                color: _textColor,
                fontSize: 12,
              ),
            ),
          ),
        ],
      ),
    );
  }

  // Build legend
  Widget _buildLegend() {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: Colors.grey.withAlpha(20),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: Colors.grey.withAlpha(100)),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Element Categories',
            style: TextStyle(
              fontWeight: FontWeight.bold,
              fontSize: 16,
              color: _textColor,
            ),
          ),
          const SizedBox(height: 8),
          Wrap(
            spacing: 8,
            runSpacing: 8,
            children: _categories.map((category) {
              return Container(
                padding: const EdgeInsets.symmetric(
                  horizontal: 8,
                  vertical: 4,
                ),
                decoration: BoxDecoration(
                  color: category.color.withAlpha(50),
                  borderRadius: BorderRadius.circular(4),
                  border: Border.all(color: category.color),
                ),
                child: Text(
                  category.name,
                  style: TextStyle(
                    color: category.color,
                    fontSize: 12,
                  ),
                ),
              );
            }).toList(),
          ),
        ],
      ),
    );
  }

  // Get color for element based on selected property
  Color _getElementColor(Element element) {
    switch (_selectedProperty) {
      case 'Category':
        return _getCategoryColor(element.category);
      case 'Electronegativity':
        return _getGradientColor(
          element.electronegativity,
          0,
          4,
          Colors.blue,
          Colors.red,
        );
      case 'Atomic Radius':
        return _getGradientColor(
          element.atomicRadius,
          30,
          200,
          Colors.red,
          Colors.blue,
        );
      case 'Ionization Energy':
        return _getGradientColor(
          element.ionizationEnergy,
          5,
          25,
          Colors.blue,
          Colors.red,
        );
      case 'Atomic Number':
      default:
        return _getGradientColor(
          element.atomicNumber.toDouble(),
          1,
          118,
          Colors.blue,
          Colors.red,
        );
    }
  }

  // Get color for element category
  Color _getCategoryColor(String category) {
    final matchingCategory = _categories.firstWhere(
      (c) => c.name == category,
      orElse: () => ElementCategory(
        name: 'Unknown',
        color: Colors.grey,
        description: '',
      ),
    );
    return matchingCategory.color;
  }

  // Get gradient color based on value
  Color _getGradientColor(
    double value,
    double min,
    double max,
    Color startColor,
    Color endColor,
  ) {
    // Clamp value to min-max range
    final clampedValue = value.clamp(min, max);

    // Calculate position in gradient (0.0 to 1.0)
    final position = (clampedValue - min) / (max - min);

    // Interpolate between colors
    return Color.lerp(startColor, endColor, position) ?? startColor;
  }

  // Get contrast color (black or white) based on background color
  Color _getContrastColor(Color backgroundColor) {
    // Calculate luminance (brightness) of the background color
    final luminance = (0.299 * backgroundColor.red.toDouble() +
            0.587 * backgroundColor.green.toDouble() +
            0.114 * backgroundColor.blue.toDouble()) /
        255;

    // Use white text on dark backgrounds, black text on light backgrounds
    return luminance > 0.5 ? Colors.black : Colors.white;
  }

  @override
  Widget build(BuildContext context) {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
        side: BorderSide(color: _primaryColor.withAlpha(77)),
      ),
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Title and description
            Text(
              widget.data['title'] ?? 'Periodic Table of Elements',
              style: TextStyle(
                fontSize: 20,
                fontWeight: FontWeight.bold,
                color: _textColor,
              ),
            ),
            const SizedBox(height: 8),
            Text(
              widget.data['description'] ??
                  'Explore the periodic table of elements and their properties',
              style: TextStyle(
                fontSize: 14,
                color: _textColor.withAlpha(179),
              ),
            ),
            const SizedBox(height: 16),

            // Search and filter controls
            _buildSearchAndFilterControls(),

            const SizedBox(height: 16),

            // View mode selector
            _buildViewModeSelector(),

            const SizedBox(height: 16),

            // Periodic table
            _buildPeriodicTable(),

            const SizedBox(height: 16),

            // Element details
            if (_selectedElement != null) _buildElementDetails(),

            const SizedBox(height: 16),

            // Legend
            if (_showLegend) _buildLegend(),
          ],
        ),
      ),
    );
  }
}
