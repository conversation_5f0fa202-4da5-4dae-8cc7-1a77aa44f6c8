import 'package:flutter/material.dart';
import '../../../models/interactive_widget_model.dart';

class MultipleChoiceWidget extends StatefulWidget {
  final InteractiveWidgetModel widget;

  const MultipleChoiceWidget({super.key, required this.widget});

  @override
  State<MultipleChoiceWidget> createState() => _MultipleChoiceWidgetState();
}

class _MultipleChoiceWidgetState extends State<MultipleChoiceWidget> {
  String? _selectedOption;
  bool _hasSubmitted = false;

  @override
  Widget build(BuildContext context) {
    final question = widget.widget.data['question'] as String;
    final options = List<String>.from(widget.widget.data['options']);
    final correctAnswer = widget.widget.data['correctAnswer'] as String;
    final explanation = widget.widget.data['explanation'] as String?;

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Question
        Padding(
          padding: const EdgeInsets.only(bottom: 16.0),
          child: Text(
            question,
            style: const TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.bold,
            ),
          ),
        ),
        
        // Options
        ...options.map((option) {
          final isCorrect = option == correctAnswer;
          final isSelected = option == _selectedOption;
          
          // Determine the color based on selection and correctness
          Color? backgroundColor;
          Color? borderColor;
          
          if (_hasSubmitted) {
            if (isSelected && isCorrect) {
              backgroundColor = Colors.green[100];
              borderColor = Colors.green;
            } else if (isSelected && !isCorrect) {
              backgroundColor = Colors.red[100];
              borderColor = Colors.red;
            } else if (isCorrect) {
              backgroundColor = Colors.green[50];
              borderColor = Colors.green[300];
            } else {
              backgroundColor = Colors.grey[100];
              borderColor = Colors.grey[300];
            }
          } else {
            backgroundColor = isSelected ? Colors.blue[100] : Colors.grey[100];
            borderColor = isSelected ? Colors.blue : Colors.grey[300];
          }
          
          return Padding(
            padding: const EdgeInsets.only(bottom: 8.0),
            child: InkWell(
              onTap: _hasSubmitted ? null : () {
                setState(() {
                  _selectedOption = option;
                });
              },
              borderRadius: BorderRadius.circular(8),
              child: Container(
                width: double.infinity,
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: backgroundColor,
                  border: Border.all(color: borderColor!),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Row(
                  children: [
                    Expanded(
                      child: Text(
                        option,
                        style: TextStyle(
                          fontWeight: isSelected ? FontWeight.bold : FontWeight.normal,
                        ),
                      ),
                    ),
                    if (_hasSubmitted && isCorrect)
                      const Icon(Icons.check_circle, color: Colors.green),
                    if (_hasSubmitted && isSelected && !isCorrect)
                      const Icon(Icons.cancel, color: Colors.red),
                  ],
                ),
              ),
            ),
          );
        }).toList(),
        
        // Submit button
        if (!_hasSubmitted)
          Padding(
            padding: const EdgeInsets.only(top: 16.0),
            child: ElevatedButton(
              onPressed: _selectedOption == null ? null : () {
                setState(() {
                  _hasSubmitted = true;
                });
              },
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.blue,
                foregroundColor: Colors.white,
                padding: const EdgeInsets.symmetric(vertical: 12, horizontal: 24),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(8),
                ),
              ),
              child: const Text('Submit Answer'),
            ),
          ),
          
        // Explanation (shown after submission)
        if (_hasSubmitted && explanation != null)
          Padding(
            padding: const EdgeInsets.only(top: 16.0),
            child: Container(
              width: double.infinity,
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: Colors.blue[50],
                borderRadius: BorderRadius.circular(8),
                border: Border.all(color: Colors.blue[200]!),
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  const Text(
                    'Explanation:',
                    style: TextStyle(
                      fontWeight: FontWeight.bold,
                      fontSize: 14,
                    ),
                  ),
                  const SizedBox(height: 4),
                  Text(
                    explanation,
                    style: const TextStyle(fontSize: 14),
                  ),
                ],
              ),
            ),
          ),
          
        // Reset button (shown after submission)
        if (_hasSubmitted)
          Padding(
            padding: const EdgeInsets.only(top: 16.0),
            child: TextButton.icon(
              onPressed: () {
                setState(() {
                  _hasSubmitted = false;
                  _selectedOption = null;
                });
              },
              icon: const Icon(Icons.refresh),
              label: const Text('Try Again'),
            ),
          ),
      ],
    );
  }
}
