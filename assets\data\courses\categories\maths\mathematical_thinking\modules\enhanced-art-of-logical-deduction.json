{"id": "art-of-logical-deduction", "title": "THE ART OF LOGICAL DEDUCTION", "description": "Unravel the power of reasoning, proving statements, and building solid arguments.", "order": 1, "lessons": [{"id": "spotting-patterns", "title": "Spotting Patterns", "description": "Discover hidden sequences and predict what comes next using inductive reasoning.", "order": 1, "type": "interactive_lesson", "estimatedTimeMinutes": 12, "contentBlocks": [{"id": "sp-screen1-intro", "type": "lesson_screen", "order": 1, "estimatedTimeSeconds": 20, "content": {"headline": "Spotting Patterns: The Mathematician's Superpower!", "body_md": "Ever noticed how your favorite song has a catchy repeating chorus? Or how the <PERSON><PERSON><PERSON><PERSON> sequence appears in flower petals? Our world is full of fascinating patterns waiting to be discovered!", "visual": {"type": "interactive_pattern_animation", "pattern_type": "<PERSON><PERSON><PERSON><PERSON>_spiral", "auto_play": true, "loop": true, "height": 250}, "hook": "Learning to spot patterns is like gaining a superpower that helps you predict the future and solve complex puzzles. Ready to become a pattern detective?", "interactive_element": {"type": "button", "text": "Let's unlock this superpower!", "action": "next_screen"}}}, {"id": "sp-screen1b-patterns-everywhere", "type": "lesson_screen", "order": 2, "estimatedTimeSeconds": 30, "content": {"headline": "Patterns Are Everywhere!", "body_md": "Patterns appear all around us:\n\n* In **nature**: Honeycomb hexagons, spiral seashells, symmetrical snowflakes\n* In **music**: Repeating choruses, rhythmic beats\n* In **art**: Geometric designs, repeating motifs\n* In **technology**: Computer algorithms, data structures\n* In **daily life**: Calendar cycles, traffic light sequences", "visual": {"type": "interactive_pattern_gallery", "patterns": [{"name": "Honeycomb", "image_path": "assets/images/patterns/honeycomb.svg", "description": "Hexagonal pattern in beehives"}, {"name": "Seashell Spiral", "image_path": "assets/images/patterns/seashell.svg", "description": "Logarithmic spiral in shells"}, {"name": "Snowflake", "image_path": "assets/images/patterns/snowflake.svg", "description": "Sixfold symmetry in ice crystals"}]}, "interactive_element": {"type": "button", "text": "So what exactly is a pattern?", "action": "next_screen"}}}, {"id": "sp-screen2-what-is-pattern", "type": "lesson_screen", "order": 3, "estimatedTimeSeconds": 45, "content": {"headline": "So, what's a pattern?", "body_md": "A pattern is a sequence where things repeat or change in a predictable way. Once you understand the rule, you can predict what comes next!\n\nLook at the fruits below. Can you predict what comes next in the sequence?", "visual": {"type": "interactive_sequence_widget", "sequence_items": ["🍎", "🍌", "🍎", "🍌", "🍎", "🍌"], "show_controls": true, "animation_speed": "medium"}, "interactive_element": {"type": "multiple_choice_text", "question_text": "What comes next in the pattern?", "options": [{"id": "s2opt1", "text": "🍎", "is_correct": true, "feedback_correct": "Excellent! You spotted the alternating pattern. After 🍌 comes 🍎, then 🍌 again, and so on.", "feedback_incorrect": ""}, {"id": "s2opt2", "text": "🍌", "is_correct": false, "feedback_correct": "", "feedback_incorrect": "Not quite. Look at how the fruits alternate. We just had 🍌, so what should come next?"}, {"id": "s2opt3", "text": "🍇", "is_correct": false, "feedback_correct": "", "feedback_incorrect": "Not quite. Look closely at how the fruits alternate: 🍎 then 🍌, then it repeats. So after 🍌 comes...?"}], "action_button_text": "Continue"}}}, {"id": "sp-screen3-numerical-patterns", "type": "lesson_screen", "order": 4, "estimatedTimeSeconds": 60, "content": {"headline": "Patterns with Numbers", "body_md": "Numbers can follow patterns too! These are especially important in mathematics.\n\nCan you guess the next number in this sequence?", "visual": {"type": "interactive_number_sequence", "sequence": [2, 4, 6, 8], "show_animation": true, "highlight_differences": true}, "interactive_element": {"type": "text_input", "question_text": "What's the next number?", "placeholder": "Enter the next number", "correct_answer_regex": "^10$", "feedback_correct": "Perfect! You recognized that we're adding 2 each time (2+2=4, 4+2=6, 6+2=8, so 8+2=10).", "feedback_incorrect": "Look at how the numbers are changing. What's the difference between each consecutive number?", "action_button_text": "Let's try another!"}}}, {"id": "sp-screen4-arithmetic-sequence", "type": "lesson_screen", "order": 5, "estimatedTimeSeconds": 60, "content": {"headline": "Arithmetic Sequences: Adding the Same Amount", "body_md": "When we add (or subtract) the same number each time, we get an **arithmetic sequence**.\n\nExample: 3, 7, 11, 15, 19, ...\n\nHere, we add 4 each time. This constant change (4) is called the **common difference**.", "visual": {"type": "interactive_arithmetic_sequence", "start_value": 3, "common_difference": 4, "num_terms": 5, "show_differences": true}, "interactive_element": {"type": "multiple_choice_text", "question_text": "What's the common difference in the sequence 5, 9, 13, 17, 21?", "options": [{"id": "s4opt1", "text": "4", "is_correct": true, "feedback_correct": "Correct! Each term increases by 4.", "feedback_incorrect": ""}, {"id": "s4opt2", "text": "5", "is_correct": false, "feedback_correct": "", "feedback_incorrect": "Not quite. The common difference is how much we add each time. Calculate 9-5, 13-9, etc."}, {"id": "s4opt3", "text": "2", "is_correct": false, "feedback_correct": "", "feedback_incorrect": "Not quite. The common difference is how much we add each time. Calculate 9-5, 13-9, etc."}], "action_button_text": "I've got it!"}}}, {"id": "sp-screen5-geometric-sequence", "type": "lesson_screen", "order": 6, "estimatedTimeSeconds": 60, "content": {"headline": "Geometric Sequences: Multiplying by the Same Amount", "body_md": "When we multiply by the same number each time, we get a **geometric sequence**.\n\nExample: 2, 6, 18, 54, 162, ...\n\nHere, we multiply by 3 each time. This multiplier is called the **common ratio**.", "visual": {"type": "interactive_geometric_sequence", "start_value": 2, "common_ratio": 3, "num_terms": 5, "show_ratios": true}, "interactive_element": {"type": "text_input", "question_text": "In the sequence 3, 6, 12, 24, 48, what's the common ratio?", "placeholder": "Enter the common ratio", "correct_answer_regex": "^2$", "feedback_correct": "Excellent! Each term is multiplied by 2 to get the next term.", "feedback_incorrect": "To find the common ratio, divide each term by the previous term (e.g., 6÷3, 12÷6, etc.)", "action_button_text": "Next pattern type!"}}}, {"id": "sp-screen6-fibonacci-sequence", "type": "lesson_screen", "order": 7, "estimatedTimeSeconds": 60, "content": {"headline": "The Famous Fibon<PERSON>ci Sequence", "body_md": "Some patterns are more complex! In the **<PERSON><PERSON><PERSON><PERSON> sequence**, each number is the sum of the two before it:\n\n1, 1, 2, 3, 5, 8, 13, 21, 34, ...\n\nThis special sequence appears throughout nature, from flower petals to pinecones to seashells!", "visual": {"type": "interactive_fibonacci_widget", "num_terms": 8, "show_sum_animation": true, "show_nature_examples": true}, "interactive_element": {"type": "text_input", "question_text": "What's the next number in the <PERSON><PERSON><PERSON><PERSON> sequence after 34?", "placeholder": "Enter the next <PERSON><PERSON><PERSON><PERSON> number", "correct_answer_regex": "^55$", "feedback_correct": "Perfect! 21 + 34 = 55. You're getting good at this!", "feedback_incorrect": "Remember, each <PERSON><PERSON><PERSON><PERSON> number is the sum of the two previous numbers. So add 21 + 34.", "action_button_text": "Fascinating!"}}}, {"id": "sp-screen7-inductive-reasoning", "type": "lesson_screen", "order": 8, "estimatedTimeSeconds": 60, "content": {"headline": "Inductive Reasoning: From Examples to Rules", "body_md": "When we spot patterns and make predictions, we're using **inductive reasoning**:\n\n1. Observe specific examples\n2. Find a general rule or pattern\n3. Make predictions based on that pattern\n\nThis powerful thinking tool helps us understand the world and make predictions!", "visual": {"type": "interactive_inductive_reasoning", "steps": [{"title": "Observe", "description": "Notice specific examples", "icon": "search"}, {"title": "Pattern", "description": "Find the general rule", "icon": "lightbulb"}, {"title": "Predict", "description": "Apply the rule to new cases", "icon": "trending_up"}]}, "interactive_element": {"type": "multiple_choice_text", "question_text": "Which is an example of inductive reasoning?", "options": [{"id": "s7opt1", "text": "Every swan I've seen is white, so all swans must be white.", "is_correct": true, "feedback_correct": "Correct! This is inductive reasoning - making a general rule from specific observations. (Though in this case, the conclusion is actually false - there are black swans in Australia!)", "feedback_incorrect": ""}, {"id": "s7opt2", "text": "All mammals have fur. Dolphins are mammals. Therefore, dolphins have fur.", "is_correct": false, "feedback_correct": "", "feedback_incorrect": "This is deductive reasoning - applying a general rule to a specific case. Inductive reasoning goes from specific examples to a general rule."}, {"id": "s7opt3", "text": "If it rains, the ground gets wet. The ground is wet. Therefore, it rained.", "is_correct": false, "feedback_correct": "", "feedback_incorrect": "This is actually a logical fallacy called 'affirming the consequent.' Inductive reasoning involves finding patterns in specific examples."}], "action_button_text": "I understand!"}}}, {"id": "sp-screen8-ambiguity-of-patterns", "type": "lesson_screen", "order": 9, "estimatedTimeSeconds": 70, "content": {"headline": "A Curious Case: The Pattern Puzzle", "body_md": "Consider this sequence: **1, 2, 4, ?**\n\nWhat do you think the next number is? (No judgment, just your first instinct!)", "interactive_element": {"type": "text_input_reveal", "placeholder": "Enter your guess", "reveal_text": "Interesting! Common answers include:\n\n* **8** (doubling: 1×2=2, 2×2=4, 4×2=8)\n* **7** (adding consecutive numbers: 1+1=2, 2+2=4, 3+4=7)\n* **6** (square numbers minus 0, 1, 2...: 1²-0=1, 2²-1=3, 3²-2=7)\n\nThe point is: patterns can be ambiguous! Without more information, multiple patterns could fit the same data. This is a limitation of inductive reasoning.", "action_button_text": "Reveal possible answers"}, "visual": {"type": "interactive_multiple_patterns", "sequence": [1, 2, 4], "possible_continuations": [{"next_value": 8, "rule": "<PERSON><PERSON>ling", "formula": "a_n = a_{n-1} × 2"}, {"next_value": 7, "rule": "Adding consecutive numbers", "formula": "a_n = a_{n-1} + n"}, {"next_value": 6, "rule": "Square minus position", "formula": "a_n = n² - (n-1)"}]}}}, {"id": "sp-screen9-mini-game-intro", "type": "lesson_screen", "order": 10, "estimatedTimeSeconds": 15, "content": {"headline": "Pattern Predictor Challenge: Test Your Skills!", "body_md": "Now it's time to put your pattern recognition superpowers to the test with a series of quick challenges! These skills are used by mathematicians, computer scientists, data analysts, and even musicians and artists.\n\nAre you ready to show off your pattern detective skills?", "visual": {"type": "interactive_game_preview", "game_type": "pattern_challenge", "animation_speed": "fast"}, "interactive_element": {"type": "button", "text": "Start the Pattern Challenge!", "action": "next_screen"}}}, {"id": "sp-screen9-game1", "type": "lesson_screen", "order": 11, "estimatedTimeSeconds": 20, "content": {"headline": "Challenge 1: Alphabetical Pattern", "body_md": "Letters can form patterns too! This is important in cryptography and code-breaking.\n\nWhat comes next in this sequence?", "visual": {"type": "interactive_letter_sequence", "sequence": ["A", "B", "C", "D", "E", "F", "G"], "highlight_animation": true}, "interactive_element": {"type": "text_input_quick", "correct_answer_regex": "^[Hh]$", "feedback_correct": "Correct! H is next in the alphabet sequence.", "action_on_correct": "next_screen_auto"}}}, {"id": "sp-screen9-game2", "type": "lesson_screen", "order": 12, "estimatedTimeSeconds": 20, "content": {"headline": "Challenge 2: <PERSON><PERSON><PERSON>", "body_md": "Visual patterns are everywhere in art and design.\n\nWhat shape comes next in this pattern?", "visual": {"type": "interactive_shape_sequence", "sequence": ["circle", "square", "triangle", "circle", "square"], "animation_speed": "medium"}, "interactive_element": {"type": "multiple_choice_icon", "options": [{"id": "g2opt1", "icon": "triangle", "is_correct": true, "feedback_correct": "Correct! The pattern is circle, square, triangle, repeating."}, {"id": "g2opt2", "icon": "circle", "is_correct": false, "feedback_incorrect": "Look at the full pattern. It's repeating every 3 shapes."}, {"id": "g2opt3", "icon": "square", "is_correct": false, "feedback_incorrect": "Look at the full pattern. It's repeating every 3 shapes."}], "action_on_correct": "next_screen_auto"}}}, {"id": "sp-screen9-game3", "type": "lesson_screen", "order": 13, "estimatedTimeSeconds": 30, "content": {"headline": "Challenge 3: Number Pattern", "body_md": "This one's a bit trickier! Look carefully at how the numbers change.\n\nWhat's the next number in this sequence?", "visual": {"type": "interactive_number_sequence", "sequence": [3, 6, 12, 24, 48], "show_animation": true}, "interactive_element": {"type": "text_input_quick", "correct_answer_regex": "^96$", "feedback_correct": "Excellent! Each number is multiplied by 2 to get the next number (3×2=6, 6×2=12, etc.)", "action_on_correct": "next_screen_auto"}}}, {"id": "sp-screen9-game4", "type": "lesson_screen", "order": 14, "estimatedTimeSeconds": 40, "content": {"headline": "Challenge 4: Advanced Number Pattern", "body_md": "Now for a real challenge! This pattern requires careful observation.\n\nWhat's the next number?", "visual": {"type": "interactive_number_sequence", "sequence": [2, 5, 10, 17, 26], "show_animation": true, "highlight_differences": true}, "interactive_element": {"type": "text_input", "placeholder": "Enter the next number", "correct_answer_regex": "^37$", "feedback_correct": "Impressive! The differences between consecutive terms form their own pattern: +3, +5, +7, +9, so the next difference is +11, making 26+11=37.", "feedback_incorrect": "Hint: Look at the differences between consecutive numbers. Do they form a pattern?", "action_button_text": "Final Challenge!"}}}, {"id": "sp-screen9-game5", "type": "lesson_screen", "order": 15, "estimatedTimeSeconds": 40, "content": {"headline": "Challenge 5: <PERSON><PERSON> Master", "body_md": "For your final challenge, identify the rule and find the missing number.\n\nSequence: 1, 4, 9, 16, 25, 36, ?, 64", "visual": {"type": "interactive_number_sequence", "sequence": [1, 4, 9, 16, 25, 36, "?", 64], "show_animation": true, "highlight_special": true}, "interactive_element": {"type": "text_input", "placeholder": "Enter the missing number", "correct_answer_regex": "^49$", "feedback_correct": "Outstanding! These are perfect squares: 1², 2², 3², 4², 5², 6², 7², 8². So the missing number is 7² = 49.", "feedback_incorrect": "Hint: Think about square numbers. What's special about 1, 4, 9, 16...?", "action_button_text": "Complete Challenge!"}}}, {"id": "sp-screen10-recap", "type": "lesson_screen", "order": 16, "estimatedTimeSeconds": 40, "content": {"headline": "Well Done, <PERSON><PERSON> Detective!", "body_md": "Key Takeaways:\n\n*   Patterns are sequences that repeat or change predictably and appear throughout nature, technology, art, and daily life.\n*   **Inductive Reasoning** is a powerful thinking tool where we:\n    *   Observe specific examples\n    *   Find a general rule or pattern\n    *   Make predictions based on that pattern\n*   Pattern recognition is used by mathematicians, scientists, musicians, artists, and even in artificial intelligence!\n*   This skill helps us solve problems, make predictions, and understand the world around us.", "visual": {"type": "interactive_concept_map", "central_concept": "Pattern Recognition", "related_concepts": [{"name": "Inductive Reasoning", "connection": "thinking method"}, {"name": "Sequences", "connection": "mathematical form"}, {"name": "Prediction", "connection": "practical application"}, {"name": "Problem Solving", "connection": "practical application"}]}, "interactive_element": {"type": "button", "text": "Next Lesson: The Power of \"If...Then...\"", "action": "next_lesson"}}}]}, {"id": "power-of-if-then", "title": "The Power of \"If...Then...\"", "description": "Explore conditional statements and the logic behind implications.", "order": 2, "type": "interactive_lesson", "estimatedTimeMinutes": 10, "contentBlocks": [{"id": "pit-screen1-intro", "type": "lesson_screen", "order": 1, "estimatedTimeSeconds": 30, "content": {"headline": "The Mighty \"If...Then...\": Logic's Building Block", "body_md": "Ever made a deal like, \"*If* you clean your room, *then* you can have ice cream\"? That's a conditional statement! These powerful logical connections are the foundation of mathematics, computer programming, and everyday reasoning.", "visual": {"type": "interactive_conditional_flow", "condition": "Clean Room", "result": "Get Ice Cream", "animation_speed": "medium"}, "hook": "Conditional statements are like the 'DNA' of logical thinking. Master them, and you'll strengthen your reasoning powers in every area of life!", "interactive_element": {"type": "button", "text": "Show Me How They Work!", "action": "next_screen"}}}]}]}