{"id": "molecular-structure-and-bonding", "title": "MOLECULAR STRUCTURE AND BONDING", "description": "Investigate how atoms combine to form molecules and the forces that hold them together.", "order": 2, "lessons": [{"id": "covalent-bonds", "title": "Covalent Bonds: Sharing Electrons", "description": "Visualize how atoms share electrons to form molecules.", "order": 1, "type": "interactive_lesson", "estimatedTimeMinutes": 10, "contentBlocks": [{"id": "cb-screen1-intro", "type": "lesson_screen", "order": 1, "estimatedTimeSeconds": 30, "content": {"headline": "The Art of Sharing: Covalent Bonds", "body_md": "Atoms don't always exist alone. They often join together to form **molecules** through chemical bonds. One of the most common types is the **covalent bond**, where atoms share electrons to achieve stability.", "visual": {"type": "giphy_search", "value": "molecule model"}, "hook": "Sharing isn't just good manners - it's how molecules are formed!", "interactive_element": {"type": "button", "text": "How Does Sharing Work?", "action": "next_screen"}}}, {"id": "cb-screen2-octet-rule", "type": "lesson_screen", "order": 2, "estimatedTimeSeconds": 60, "content": {"headline": "The Octet Rule: Atoms Want to Be Like Noble <PERSON>es", "body_md": "Most atoms aim to have 8 electrons in their outer shell (like noble gases), which makes them stable. This is called the **octet rule**.\n\nAtoms can achieve this by:\n- Gaining electrons\n- Losing electrons\n- Sharing electrons\n\nCovalent bonding is all about sharing electrons to reach that magic number 8!", "visual": {"type": "unsplash_search", "value": "noble gases periodic table"}, "hook": "Noble gases are the 'cool kids' of the periodic table - everyone wants to be like them!", "interactive_element": {"type": "button", "text": "Show Me Some Examples", "action": "next_screen"}}}, {"id": "cb-screen3-examples", "type": "lesson_screen", "order": 3, "estimatedTimeSeconds": 90, "content": {"headline": "Covalent Bonding in Action", "body_md": "Let's look at some common examples of covalent bonds:\n\n**Hydrogen (H₂)**: Each H atom has 1 electron and needs 1 more for stability (<PERSON> wants 2, not 8). By sharing, each gets access to 2 electrons.\n\n**Water (H₂O)**: Oxygen (6 valence electrons) shares with two hydrogen atoms. Each H shares its 1 electron, and O shares 1 electron with each H. Result: O gets 8 electrons, each H gets 2.\n\n**Methane (CH₄)**: Carbon (4 valence electrons) shares with four hydrogen atoms, giving carbon access to 8 electrons total.", "visual": {"type": "static_text", "value": "Interactive: Covalent Bond Formation"}, "interactive_element": {"type": "interactive_covalent_bond_visualizer", "molecules": [{"name": "Hydrogen (H₂)", "atoms": [{"element": "H", "valence_electrons": 1}, {"element": "H", "valence_electrons": 1}], "shared_pairs": 1, "description": "Single bond: H-H"}, {"name": "Water (H₂O)", "atoms": [{"element": "O", "valence_electrons": 6}, {"element": "H", "valence_electrons": 1}, {"element": "H", "valence_electrons": 1}], "shared_pairs": 2, "description": "Two single bonds: H-O-H"}], "action_button_text": "Let's Talk About Bond Types"}}}, {"id": "cb-screen4-bond-types", "type": "lesson_screen", "order": 4, "estimatedTimeSeconds": 90, "content": {"headline": "Single, Double, and Triple Bonds", "body_md": "Atoms can share more than one pair of electrons:\n\n- **Single bond**: 1 pair of shared electrons (H-H in H₂)\n- **Double bond**: 2 pairs of shared electrons (O=O in O₂)\n- **Triple bond**: 3 pairs of shared electrons (N≡N in N₂)\n\nMore shared electrons = stronger bond!", "visual": {"type": "giphy_search", "value": "chemical bonds"}, "hook": "Triple bonds are like super glue - they're really hard to break!", "interactive_element": {"type": "button", "text": "Test My Knowledge", "action": "next_screen"}}}, {"id": "cb-screen5-quiz", "type": "lesson_screen", "order": 5, "estimatedTimeSeconds": 60, "content": {"headline": "Quick Check: Covalent Bonds", "body_md": "Let's see what you've learned about covalent bonding!", "interactive_element": {"type": "multiple_choice_text", "question_text": "In a covalent bond, what do atoms share?", "options": [{"id": "opt1", "text": "Electrons", "is_correct": true, "feedback_correct": "Correct! Atoms share electrons in covalent bonds to achieve stability."}, {"id": "opt2", "text": "Protons", "is_correct": false, "feedback_incorrect": "Protons stay in the nucleus and aren't shared between atoms."}, {"id": "opt3", "text": "Neutrons", "is_correct": false, "feedback_incorrect": "Neutrons remain in the nucleus and aren't involved in bonding."}, {"id": "opt4", "text": "Energy", "is_correct": false, "feedback_incorrect": "While energy is involved in bonding, atoms specifically share electrons."}], "action_button_text": "Continue to Next Lesson"}}}]}, {"id": "lewis-structures", "title": "Lewis Structures: Representing Molecular Bonding", "description": "Learn to draw electron dot diagrams to visualize molecular bonding.", "order": 2, "type": "interactive_lesson", "estimatedTimeMinutes": 10, "contentBlocks": [{"id": "ls-screen1-intro", "type": "lesson_screen", "order": 1, "estimatedTimeSeconds": 30, "content": {"headline": "Drawing Molecules: Lewis Structures", "body_md": "How do we represent molecules on paper? **Lewis structures** (also called electron dot diagrams) are a simple way to show how atoms share electrons in molecules.", "visual": {"type": "unsplash_search", "value": "molecular model"}, "hook": "Lewis structures are like the blueprints of molecules!", "interactive_element": {"type": "button", "text": "Show Me How", "action": "next_screen"}}}, {"id": "ls-screen2-basics", "type": "lesson_screen", "order": 2, "estimatedTimeSeconds": 60, "content": {"headline": "The Basics of Lewis Structures", "body_md": "In Lewis structures:\n\n1. Element symbols represent atoms\n2. Dots represent valence electrons\n3. Lines represent shared electron pairs (bonds)\n4. Unshared pairs (lone pairs) are shown as dots\n\nFor example, hydrogen (H) has 1 valence electron, so it's written as H·\nOxygen (O) has 6 valence electrons, so it's written as ·Ö·\n                                                                 ··", "visual": {"type": "static_text", "value": "Interactive: Lewis Structure Basics"}, "interactive_element": {"type": "interactive_lewis_structure_builder", "elements": [{"symbol": "H", "valence_electrons": 1}, {"symbol": "O", "valence_electrons": 6}, {"symbol": "N", "valence_electrons": 5}, {"symbol": "C", "valence_electrons": 4}], "instructions": "Click on each element to see its Lewis dot structure.", "action_button_text": "Let's Draw Some Molecules"}}}, {"id": "ls-screen3-drawing", "type": "lesson_screen", "order": 3, "estimatedTimeSeconds": 120, "content": {"headline": "Drawing Lewis Structures: Step by Step", "body_md": "To draw a Lewis structure:\n\n1. Count total valence electrons for all atoms\n2. Connect atoms with single bonds\n3. Place remaining electrons to satisfy octets\n4. If octets aren't satisfied, form multiple bonds\n\nLet's try drawing water (H₂O):\n- Total valence electrons: 8 (6 from O + 1 from each H)\n- Connect O to each H with single bonds (uses 4 electrons)\n- Place remaining 4 electrons as lone pairs on O", "visual": {"type": "static_text", "value": "Interactive: Draw Water Molecule"}, "interactive_element": {"type": "interactive_lewis_structure_drawer", "molecule": "H₂O", "atoms": [{"symbol": "O", "valence_electrons": 6, "position": "center"}, {"symbol": "H", "valence_electrons": 1, "position": "left"}, {"symbol": "H", "valence_electrons": 1, "position": "right"}], "total_valence_electrons": 8, "correct_structure": {"bonds": [{"from": "O", "to": "H1", "type": "single"}, {"from": "O", "to": "H2", "type": "single"}], "lone_pairs": [{"on": "O", "count": 2}]}, "feedback_correct": "Perfect! You've correctly drawn the Lewis structure for water.", "feedback_incorrect": "Not quite right. Remember, oxygen needs 8 electrons total.", "action_button_text": "Let's Try Another One"}}}, {"id": "ls-screen4-practice", "type": "lesson_screen", "order": 4, "estimatedTimeSeconds": 120, "content": {"headline": "Practice: Draw Carbon Dioxide (CO₂)", "body_md": "Now let's try drawing carbon dioxide (CO₂):\n\n- Total valence electrons: 16 (4 from C + 6 from each O)\n- Connect C to each O with single bonds (uses 4 electrons)\n- Place remaining 12 electrons\n- Check if octets are satisfied (they won't be for C)\n- Form double bonds to satisfy octets", "visual": {"type": "static_text", "value": "Interactive: Draw CO₂ Molecule"}, "interactive_element": {"type": "multiple_choice_image", "question_text": "Which is the correct Lewis structure for CO₂?", "options": [{"id": "opt1", "image_path": "assets/images/chemistry/co2_double_bonds.svg", "alt_text": "O=C=O with all atoms having octets", "is_correct": true, "feedback_correct": "Correct! CO₂ has two double bonds (O=C=O), giving each atom 8 electrons."}, {"id": "opt2", "image_path": "assets/images/chemistry/co2_single_bonds.svg", "alt_text": "O-C-O with oxygen having lone pairs but carbon only having 4 electrons", "is_correct": false, "feedback_incorrect": "This structure doesn't give carbon an octet (8 electrons)."}, {"id": "opt3", "image_path": "assets/images/chemistry/co2_triple_bonds.svg", "alt_text": "O≡C≡O with no lone pairs", "is_correct": false, "feedback_incorrect": "Triple bonds would use too many electrons and leave none for lone pairs."}, {"id": "opt4", "image_path": "assets/images/chemistry/co2_incorrect_charges.svg", "alt_text": "O-C-O with charges", "is_correct": false, "feedback_incorrect": "While formal charges can exist, this isn't the most stable arrangement for CO₂."}], "action_button_text": "Continue"}}}, {"id": "ls-screen5-quiz", "type": "lesson_screen", "order": 5, "estimatedTimeSeconds": 60, "content": {"headline": "Quick Check: Lewis Structures", "body_md": "Let's test your understanding of Lewis structures.", "interactive_element": {"type": "multiple_choice_text", "question_text": "What do the lines between atoms represent in a Lewis structure?", "options": [{"id": "opt1", "text": "Shared pairs of electrons (bonds)", "is_correct": true, "feedback_correct": "Correct! Each line represents a pair of shared electrons forming a bond."}, {"id": "opt2", "text": "Proton connections", "is_correct": false, "feedback_incorrect": "Lines represent electron sharing, not proton connections."}, {"id": "opt3", "text": "Atomic radii", "is_correct": false, "feedback_incorrect": "Lines show bonds, not the size of atoms."}, {"id": "opt4", "text": "Magnetic attractions", "is_correct": false, "feedback_incorrect": "While some bonds have magnetic properties, the lines specifically represent shared electrons."}], "action_button_text": "Continue to Next Lesson"}}}]}], "moduleTest": {"id": "msb-mt1-molecular-modeler", "title": "Module Test: The Molecular Modeler", "description": "Predict molecular shapes and understand different types of chemical bonds and intermolecular forces.", "order": 6, "type": "module_test_interactive", "estimatedTimeMinutes": 10, "contentBlocks": [{"id": "msb-mt1-s0-intro", "type": "test_screen_intro", "order": 1, "estimatedTimeSeconds": 20, "content": {"headline": "Module Test: The Molecular Modeler", "body_md": "Time to test your understanding of molecular structure and bonding! Show what you've learned about how atoms join together to form molecules.", "visual": {"type": "giphy_search", "value": "molecule model building"}, "interactive_element": {"type": "button", "text": "Start the Test!", "action": "next_screen"}}}, {"id": "msb-mt1-s1-q1", "type": "test_screen_intro", "order": 2, "estimatedTimeSeconds": 60, "content": {"headline": "Question 1: Covalent Bonding", "body_md": "What is the main driving force behind covalent bond formation?", "interactive_element": {"type": "multiple_choice_text", "options": [{"id": "opt1", "text": "Atoms achieving a stable electron configuration", "is_correct": true, "feedback_correct": "Correct! Atoms form covalent bonds to achieve stable electron configurations, typically octets."}, {"id": "opt2", "text": "Oppositely charged ions attracting each other", "is_correct": false}, {"id": "opt3", "text": "Magnetic forces between atoms", "is_correct": false}, {"id": "opt4", "text": "Nuclear fusion of atoms", "is_correct": false}], "action_button_text": "Next Question"}}}, {"id": "msb-mt1-s2-q2", "type": "test_screen_intro", "order": 3, "estimatedTimeSeconds": 60, "content": {"headline": "Question 2: Bond Types", "body_md": "Which type of bond is the strongest?", "interactive_element": {"type": "multiple_choice_text", "options": [{"id": "opt1", "text": "Triple bond", "is_correct": true, "feedback_correct": "Correct! Triple bonds, with three shared pairs of electrons, are the strongest type of covalent bond."}, {"id": "opt2", "text": "Double bond", "is_correct": false}, {"id": "opt3", "text": "Single bond", "is_correct": false}, {"id": "opt4", "text": "Ionic bond", "is_correct": false}], "action_button_text": "Next Question"}}}, {"id": "msb-mt1-s3-q3", "type": "test_screen_intro", "order": 4, "estimatedTimeSeconds": 70, "content": {"headline": "Question 3: <PERSON>", "body_md": "How many valence electrons does nitrogen (N) have in its Lewis dot structure?", "interactive_element": {"type": "multiple_choice_text", "options": [{"id": "opt1", "text": "5", "is_correct": true, "feedback_correct": "Correct! Nitrogen has 5 valence electrons, which is why it often forms triple bonds to achieve an octet."}, {"id": "opt2", "text": "3", "is_correct": false}, {"id": "opt3", "text": "7", "is_correct": false}, {"id": "opt4", "text": "8", "is_correct": false}], "action_button_text": "Next Question"}}}, {"id": "msb-mt1-s4-q4", "type": "test_screen_intro", "order": 5, "estimatedTimeSeconds": 70, "content": {"headline": "Question 4: Molecular Geometry", "body_md": "What is the molecular geometry of methane (CH₄)?", "interactive_element": {"type": "multiple_choice_text", "options": [{"id": "opt1", "text": "Tetrahedral", "is_correct": true, "feedback_correct": "Correct! Methane has a tetrahedral geometry with bond angles of approximately 109.5°."}, {"id": "opt2", "text": "Linear", "is_correct": false}, {"id": "opt3", "text": "Trigonal planar", "is_correct": false}, {"id": "opt4", "text": "<PERSON><PERSON>", "is_correct": false}], "action_button_text": "Next Question"}}}, {"id": "msb-mt1-s5-q5", "type": "test_screen_intro", "order": 6, "estimatedTimeSeconds": 70, "content": {"headline": "Question 5: Intermolecular Forces", "body_md": "Which intermolecular force is responsible for the high boiling point of water?", "interactive_element": {"type": "multiple_choice_text", "options": [{"id": "opt1", "text": "Hydrogen bonding", "is_correct": true, "feedback_correct": "Correct! Hydrogen bonding between water molecules gives water its unusually high boiling point."}, {"id": "opt2", "text": "London dispersion forces", "is_correct": false}, {"id": "opt3", "text": "Covalent bonds", "is_correct": false}, {"id": "opt4", "text": "Ionic interactions", "is_correct": false}], "action_button_text": "Finish Test"}}}, {"id": "msb-mt1-s6-conclusion", "type": "lesson_screen", "order": 7, "estimatedTimeSeconds": 30, "content": {"headline": "Molecular Modeler: Complete!", "body_md": "Great job! You've demonstrated your understanding of molecular structure and bonding. You're now ready to explore chemical reactions and how molecules transform!", "visual": {"type": "giphy_search", "value": "chemistry molecules success"}, "interactive_element": {"type": "button", "text": "Back to Course", "action": "module_complete"}}}]}}