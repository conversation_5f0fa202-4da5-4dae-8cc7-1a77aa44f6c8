import 'package:flutter/material.dart';
import 'dart:math' as math;

/// A widget that allows users to generate predictions based on scientific theories
class InteractivePredictionGeneratorWidget extends StatefulWidget {
  /// The data for this widget
  final Map<String, dynamic> data;

  /// Optional callback when the widget state changes
  final Function(bool isCompleted)? onStateChanged;

  const InteractivePredictionGeneratorWidget({
    super.key,
    required this.data,
    this.onStateChanged,
  });

  /// Factory constructor to create the widget from JSON data
  factory InteractivePredictionGeneratorWidget.fromData(Map<String, dynamic> data) {
    return InteractivePredictionGeneratorWidget(
      data: data,
    );
  }

  @override
  State<InteractivePredictionGeneratorWidget> createState() => _InteractivePredictionGeneratorWidgetState();
}

class _InteractivePredictionGeneratorWidgetState extends State<InteractivePredictionGeneratorWidget> {
  // Theories and variables
  late List<Theory> _theories;
  late int _currentTheoryIndex;
  late List<TheoryVariable> _variables;
  late Map<String, double> _variableValues;

  // Predictions
  late List<Prediction> _predictions;
  late List<bool> _selectedPredictions;
  late bool _predictionsGenerated;
  late String _feedback;

  // UI state
  late bool _isCompleted;
  late bool _showExplanation;
  late bool _hasSubmitted;

  // Colors
  late Color _primaryColor;
  late Color _secondaryColor;
  late Color _accentColor;
  late Color _backgroundColor;
  late Color _textColor;

  @override
  void initState() {
    super.initState();
    _initializeWidget();
  }

  void _initializeWidget() {
    // Initialize colors
    _primaryColor = _parseColor(widget.data['primaryColor'] ?? '#2196F3');
    _secondaryColor = _parseColor(widget.data['secondaryColor'] ?? '#FF9800');
    _accentColor = _parseColor(widget.data['accentColor'] ?? '#4CAF50');
    _backgroundColor = _parseColor(widget.data['backgroundColor'] ?? '#FFFFFF');
    _textColor = _parseColor(widget.data['textColor'] ?? '#212121');

    // Initialize theories
    final List<dynamic> theoriesData = widget.data['theories'] ?? [];
    _theories = theoriesData.map((theoryData) => Theory.fromJson(theoryData)).toList();
    _currentTheoryIndex = 0;

    // Initialize variables
    _variables = [];
    _variableValues = {};
    _loadTheoryVariables();

    // Initialize predictions
    _predictions = [];
    _selectedPredictions = [];
    _predictionsGenerated = false;
    _feedback = '';

    // Initialize UI state
    _isCompleted = false;
    _showExplanation = false;
    _hasSubmitted = false;
  }

  void _loadTheoryVariables() {
    if (_theories.isEmpty) return;
    
    Theory theory = _theories[_currentTheoryIndex];
    final List<dynamic> variablesData = theory.variables;
    _variables = variablesData.map((variableData) => TheoryVariable.fromJson(variableData)).toList();
    
    // Initialize variable values to their default values
    _variableValues = {};
    for (var variable in _variables) {
      _variableValues[variable.id] = variable.defaultValue;
    }
  }

  Color _parseColor(String colorString) {
    if (colorString.startsWith('#')) {
      return Color(int.parse('FF${colorString.substring(1)}', radix: 16));
    }
    return Colors.blue;
  }

  void _updateVariableValue(String variableId, double value) {
    setState(() {
      _variableValues[variableId] = value;
    });
  }

  void _generatePredictions() {
    if (_theories.isEmpty) return;
    
    Theory theory = _theories[_currentTheoryIndex];
    
    // Get predictions for the current theory
    _predictions = List<Prediction>.from(theory.predictions);
    
    // Initialize selected predictions
    _selectedPredictions = List<bool>.filled(_predictions.length, false);
    
    setState(() {
      _predictionsGenerated = true;
      _hasSubmitted = false;
      _feedback = '';
    });
  }

  void _togglePrediction(int index) {
    setState(() {
      _selectedPredictions[index] = !_selectedPredictions[index];
    });
  }

  void _submitPredictions() {
    if (_theories.isEmpty) return;
    
    Theory theory = _theories[_currentTheoryIndex];
    
    // Check if the correct predictions are selected based on variable values
    bool allCorrect = true;
    List<String> correctPredictionIds = [];
    
    // Determine which predictions should be correct based on variable values
    for (var prediction in _predictions) {
      bool shouldBeSelected = true;
      
      // Check if all conditions for this prediction are met
      for (var condition in prediction.conditions) {
        double? value = _variableValues[condition.variableId];
        if (value == null) continue;
        
        bool conditionMet = false;
        switch (condition.operator) {
          case '>':
            conditionMet = value > condition.value;
            break;
          case '<':
            conditionMet = value < condition.value;
            break;
          case '>=':
            conditionMet = value >= condition.value;
            break;
          case '<=':
            conditionMet = value <= condition.value;
            break;
          case '==':
            conditionMet = (value - condition.value).abs() < 0.001; // Float comparison
            break;
          default:
            conditionMet = false;
        }
        
        if (!conditionMet) {
          shouldBeSelected = false;
          break;
        }
      }
      
      if (shouldBeSelected) {
        correctPredictionIds.add(prediction.id);
      }
    }
    
    // Check if user selected all the correct predictions and only those
    for (int i = 0; i < _predictions.length; i++) {
      bool isCorrect = correctPredictionIds.contains(_predictions[i].id);
      if (_selectedPredictions[i] != isCorrect) {
        allCorrect = false;
        break;
      }
    }
    
    // Generate feedback
    if (allCorrect) {
      _feedback = theory.correctFeedback;
    } else {
      _feedback = theory.incorrectFeedback;
    }
    
    setState(() {
      _hasSubmitted = true;
    });
  }

  void _resetPredictions() {
    setState(() {
      _predictionsGenerated = false;
      _hasSubmitted = false;
      _feedback = '';
      _selectedPredictions = List<bool>.filled(_predictions.length, false);
    });
  }

  void _nextTheory() {
    if (_currentTheoryIndex < _theories.length - 1) {
      setState(() {
        _currentTheoryIndex++;
        _loadTheoryVariables();
        _resetPredictions();
        _showExplanation = false;
      });
    } else if (!_isCompleted) {
      setState(() {
        _isCompleted = true;
      });
      widget.onStateChanged?.call(true);
    }
  }

  void _toggleExplanation() {
    setState(() {
      _showExplanation = !_showExplanation;
    });
  }

  @override
  Widget build(BuildContext context) {
    if (_theories.isEmpty) {
      return const Center(child: Text('No theories available'));
    }

    Theory theory = _theories[_currentTheoryIndex];

    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
        side: BorderSide(color: _primaryColor.withOpacity(0.5), width: 1),
      ),
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Title
            Text(
              widget.data['title'] ?? 'Prediction Generator',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
                color: _textColor,
              ),
            ),

            const SizedBox(height: 8),

            // Theory navigation
            Row(
              children: [
                Text(
                  'Theory ${_currentTheoryIndex + 1} of ${_theories.length}: ${theory.name}',
                  style: TextStyle(
                    fontSize: 14,
                    fontWeight: FontWeight.bold,
                    color: _primaryColor,
                  ),
                ),
                const Spacer(),
                IconButton(
                  icon: Icon(_showExplanation ? Icons.info_outline : Icons.info),
                  onPressed: _hasSubmitted ? _toggleExplanation : null,
                  tooltip: _showExplanation ? 'Hide Explanation' : 'Show Explanation',
                  color: _secondaryColor,
                ),
              ],
            ),

            const SizedBox(height: 8),

            // Theory description
            Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: _backgroundColor,
                borderRadius: BorderRadius.circular(8),
                border: Border.all(color: _primaryColor.withOpacity(0.3)),
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'Description:',
                    style: TextStyle(
                      fontWeight: FontWeight.bold,
                      color: _textColor,
                    ),
                  ),
                  const SizedBox(height: 4),
                  Text(
                    theory.description,
                    style: TextStyle(color: _textColor.withOpacity(0.8)),
                  ),
                ],
              ),
            ),

            const SizedBox(height: 16),

            // Variables section
            Text(
              'Adjust Variables:',
              style: TextStyle(
                fontWeight: FontWeight.bold,
                color: _textColor,
              ),
            ),

            const SizedBox(height: 8),

            // Variable sliders
            ..._variables.map((variable) => _buildVariableSlider(variable)),

            const SizedBox(height: 16),

            // Generate predictions button
            if (!_predictionsGenerated)
              Center(
                child: ElevatedButton(
                  onPressed: _generatePredictions,
                  style: ElevatedButton.styleFrom(
                    backgroundColor: _primaryColor,
                    foregroundColor: Colors.white,
                    padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(8),
                    ),
                  ),
                  child: const Text('Generate Predictions'),
                ),
              ),

            // Predictions section
            if (_predictionsGenerated) ...[
              Text(
                'Select the predictions that would be true based on the variables:',
                style: TextStyle(
                  fontWeight: FontWeight.bold,
                  color: _textColor,
                ),
              ),

              const SizedBox(height: 8),

              // Prediction checkboxes
              ..._predictions.asMap().entries.map((entry) {
                int index = entry.key;
                Prediction prediction = entry.value;
                return CheckboxListTile(
                  title: Text(prediction.text),
                  value: _selectedPredictions[index],
                  onChanged: _hasSubmitted ? null : (value) => _togglePrediction(index),
                  activeColor: _primaryColor,
                  checkColor: Colors.white,
                  controlAffinity: ListTileControlAffinity.leading,
                  dense: true,
                );
              }).toList(),

              const SizedBox(height: 16),

              // Submit button
              if (!_hasSubmitted)
                Center(
                  child: ElevatedButton(
                    onPressed: _submitPredictions,
                    style: ElevatedButton.styleFrom(
                      backgroundColor: _primaryColor,
                      foregroundColor: Colors.white,
                      padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(8),
                      ),
                    ),
                    child: const Text('Submit Predictions'),
                  ),
                ),
            ],

            // Feedback
            if (_hasSubmitted) ...[
              Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: _backgroundColor,
                  borderRadius: BorderRadius.circular(8),
                  border: Border.all(color: _accentColor),
                ),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Feedback:',
                      style: TextStyle(
                        fontWeight: FontWeight.bold,
                        color: _textColor,
                      ),
                    ),
                    const SizedBox(height: 4),
                    Text(
                      _feedback,
                      style: TextStyle(color: _textColor.withOpacity(0.8)),
                    ),
                  ],
                ),
              ),

              // Explanation (if shown)
              if (_showExplanation) ...[
                const SizedBox(height: 16),
                Container(
                  padding: const EdgeInsets.all(12),
                  decoration: BoxDecoration(
                    color: _secondaryColor.withOpacity(0.1),
                    borderRadius: BorderRadius.circular(8),
                    border: Border.all(color: _secondaryColor.withOpacity(0.3)),
                  ),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'Scientific Explanation:',
                        style: TextStyle(
                          fontWeight: FontWeight.bold,
                          color: _secondaryColor,
                        ),
                      ),
                      const SizedBox(height: 4),
                      Text(
                        theory.explanation,
                        style: TextStyle(color: _textColor.withOpacity(0.8)),
                      ),
                    ],
                  ),
                ),
              ],

              const SizedBox(height: 16),

              // Navigation buttons
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                children: [
                  OutlinedButton(
                    onPressed: _resetPredictions,
                    style: OutlinedButton.styleFrom(
                      foregroundColor: _primaryColor,
                      side: BorderSide(color: _primaryColor),
                      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                    ),
                    child: const Text('Reset'),
                  ),
                  ElevatedButton(
                    onPressed: _nextTheory,
                    style: ElevatedButton.styleFrom(
                      backgroundColor: _primaryColor,
                      foregroundColor: Colors.white,
                      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                    ),
                    child: Text(_currentTheoryIndex < _theories.length - 1
                        ? 'Next Theory'
                        : 'Finish'),
                  ),
                ],
              ),
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildVariableSlider(TheoryVariable variable) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 8.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Expanded(
                child: Text(
                  variable.name,
                  style: TextStyle(
                    fontWeight: FontWeight.w500,
                    color: _textColor,
                  ),
                ),
              ),
              Text(
                '${_variableValues[variable.id]?.toStringAsFixed(1) ?? variable.defaultValue.toStringAsFixed(1)} ${variable.unit}',
                style: TextStyle(
                  fontWeight: FontWeight.bold,
                  color: _primaryColor,
                ),
              ),
            ],
          ),
          const SizedBox(height: 4),
          Text(
            variable.description,
            style: TextStyle(
              fontSize: 12,
              color: _textColor.withOpacity(0.7),
            ),
          ),
          Slider(
            value: _variableValues[variable.id] ?? variable.defaultValue,
            min: variable.minValue,
            max: variable.maxValue,
            divisions: ((variable.maxValue - variable.minValue) * 10).round(),
            label: '${(_variableValues[variable.id] ?? variable.defaultValue).toStringAsFixed(1)} ${variable.unit}',
            onChanged: _predictionsGenerated ? null : (value) => _updateVariableValue(variable.id, value),
            activeColor: _primaryColor,
            inactiveColor: _primaryColor.withOpacity(0.2),
          ),
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                '${variable.minValue} ${variable.unit}',
                style: TextStyle(
                  fontSize: 10,
                  color: _textColor.withOpacity(0.6),
                ),
              ),
              Text(
                '${variable.maxValue} ${variable.unit}',
                style: TextStyle(
                  fontSize: 10,
                  color: _textColor.withOpacity(0.6),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }
}

/// Represents a scientific theory with variables and predictions
class Theory {
  final String id;
  final String name;
  final String description;
  final String explanation;
  final String correctFeedback;
  final String incorrectFeedback;
  final List<dynamic> variables;
  final List<Prediction> predictions;

  Theory({
    required this.id,
    required this.name,
    required this.description,
    required this.explanation,
    required this.correctFeedback,
    required this.incorrectFeedback,
    required this.variables,
    required this.predictions,
  });

  factory Theory.fromJson(Map<String, dynamic> json) {
    List<Prediction> predictions = [];
    if (json.containsKey('predictions') && json['predictions'] is List) {
      predictions = (json['predictions'] as List)
          .map((predictionJson) => Prediction.fromJson(predictionJson))
          .toList();
    }

    return Theory(
      id: json['id'] as String,
      name: json['name'] as String,
      description: json['description'] as String,
      explanation: json['explanation'] as String,
      correctFeedback: json['correctFeedback'] as String,
      incorrectFeedback: json['incorrectFeedback'] as String,
      variables: json['variables'] as List<dynamic>,
      predictions: predictions,
    );
  }
}

/// Represents a variable in a scientific theory
class TheoryVariable {
  final String id;
  final String name;
  final String description;
  final String unit;
  final double minValue;
  final double maxValue;
  final double defaultValue;

  TheoryVariable({
    required this.id,
    required this.name,
    required this.description,
    required this.unit,
    required this.minValue,
    required this.maxValue,
    required this.defaultValue,
  });

  factory TheoryVariable.fromJson(Map<String, dynamic> json) {
    return TheoryVariable(
      id: json['id'] as String,
      name: json['name'] as String,
      description: json['description'] as String,
      unit: json['unit'] as String,
      minValue: (json['minValue'] as num).toDouble(),
      maxValue: (json['maxValue'] as num).toDouble(),
      defaultValue: (json['defaultValue'] as num).toDouble(),
    );
  }
}

/// Represents a prediction based on a scientific theory
class Prediction {
  final String id;
  final String text;
  final List<PredictionCondition> conditions;

  Prediction({
    required this.id,
    required this.text,
    required this.conditions,
  });

  factory Prediction.fromJson(Map<String, dynamic> json) {
    List<PredictionCondition> conditions = [];
    if (json.containsKey('conditions') && json['conditions'] is List) {
      conditions = (json['conditions'] as List)
          .map((conditionJson) => PredictionCondition.fromJson(conditionJson))
          .toList();
    }

    return Prediction(
      id: json['id'] as String,
      text: json['text'] as String,
      conditions: conditions,
    );
  }
}

/// Represents a condition for a prediction
class PredictionCondition {
  final String variableId;
  final String operator;
  final double value;

  PredictionCondition({
    required this.variableId,
    required this.operator,
    required this.value,
  });

  factory PredictionCondition.fromJson(Map<String, dynamic> json) {
    return PredictionCondition(
      variableId: json['variableId'] as String,
      operator: json['operator'] as String,
      value: (json['value'] as num).toDouble(),
    );
  }
}
