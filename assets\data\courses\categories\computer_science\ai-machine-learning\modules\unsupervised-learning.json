{"id": "unsupervised-learning", "title": "Unsupervised Learning", "description": "Explore methods for finding hidden patterns in unlabeled data.", "order": 4, "lessons": [{"id": "what-is-unsupervised-learning", "title": "What is Unsupervised Learning?", "description": "Explore the world of unsupervised learning where models find patterns in unlabeled data.", "order": 1, "type": "interactive_lesson", "estimatedTimeMinutes": 10, "contentBlocks": [{"id": "ul_l1_s1_intro", "type": "lesson_screen", "order": 1, "estimatedTimeSeconds": 75, "content": {"headline": "Learning Without Labels: Unsupervised Learning", "body_md": "So far, we've seen supervised learning, where we provide labeled data (inputs with known outputs).\n\n**Unsupervised Learning** is different. Here, the algorithm is given **unlabeled data** and tries to find hidden patterns, structures, or relationships on its own, without explicit guidance on what the 'correct' output is.", "visual": {"type": "giphy_search", "value": "detective magnifying glass"}, "interactive_element": {"type": "button", "button_text": "How does it work?"}, "audio_narration_url": null}}, {"id": "ul_l1_s2_goal_of_ul", "type": "lesson_screen", "order": 2, "estimatedTimeSeconds": 90, "content": {"headline": "The Goal: Discovering Insights", "body_md": "The main goal of unsupervised learning is to **explore the data** and discover interesting structures or patterns.\n\nCommon tasks include:\n*   **Clustering:** Grouping similar data points together.\n*   **Dimensionality Reduction:** Reducing the number of variables while preserving important information.\n*   **Association Rule Mining:** Finding relationships between items in large datasets (e.g., 'customers who bought X also bought Y').", "visual": {"type": "unsplash_search", "value": "puzzle pieces connecting"}, "interactive_element": {"type": "multiple_choice_text", "question_text": "Which task is NOT typically unsupervised learning?", "options": [{"text": "Grouping customers by purchasing behavior.", "is_correct": false, "feedback": "This is clustering, a common unsupervised task."}, {"text": "Predicting house prices based on features.", "is_correct": true, "feedback": "Correct! Predicting a specific value (price) from labeled data (features and known prices) is supervised learning (regression)."}, {"text": "Finding common topics in a set of news articles.", "is_correct": false, "feedback": "This can be done with unsupervised topic modeling."}]}, "audio_narration_url": null}}, {"id": "ul_l1_s3_clustering_intro", "type": "lesson_screen", "order": 3, "estimatedTimeSeconds": 75, "content": {"headline": "Focus: Clustering", "body_md": "One of the most well-known unsupervised learning tasks is **clustering**.\n\nThe algorithm tries to group data points such that points in the same group (cluster) are more similar to each other than to those in other groups.\n\nThink of sorting a mixed bag of fruits into piles of apples, bananas, and oranges without knowing their names beforehand.", "visual": {"type": "giphy_search", "value": "grouping sorting"}, "interactive_element": {"type": "button", "button_text": "Next: Clustering Algorithms"}, "audio_narration_url": null}}, {"id": "ul_l1_s4_when_to_use", "type": "lesson_screen", "order": 4, "estimatedTimeSeconds": 90, "content": {"headline": "When is Unsupervised Learning Useful?", "body_md": "Unsupervised learning shines when:\n\n*   You don't have labeled data (labeling can be expensive and time-consuming!).\n*   You want to explore your data to find unknown patterns or anomalies.\n*   You need to reduce the complexity of your data (dimensionality reduction).\n*   You want to segment your customers or data points into natural groups.\n\nIt's a powerful tool for data exploration and preprocessing.", "visual": {"type": "unsplash_search", "value": "exploring map"}, "interactive_element": {"type": "multiple_choice_text", "question_text": "If you have a dataset of customer reviews and want to automatically group them by common themes without pre-defining the themes, you would use:", "options": [{"text": "Supervised Classification", "is_correct": false, "feedback": "Supervised classification requires pre-defined themes (labels)."}, {"text": "Unsupervised Clustering", "is_correct": true, "feedback": "Correct! Clustering can find natural groupings (themes) in unlabeled text data."}, {"text": "Supervised Regression", "is_correct": false, "feedback": "Regression predicts continuous values, not themes."}]}, "audio_narration_url": null}}, {"id": "ul_l1_s5_summary", "type": "lesson_screen", "order": 5, "estimatedTimeSeconds": 60, "content": {"headline": "Unsupervised Learning: Recap", "body_md": "Key takeaways:\n\n*   Learns from **unlabeled data**.\n*   Aims to discover **hidden patterns and structures**.\n*   Common tasks include **clustering**, dimensionality reduction, and association rule mining.\n\nReady to explore some clustering algorithms?", "visual": {"type": "giphy_search", "value": "discovery binoculars"}, "interactive_element": {"type": "button", "button_text": "Let's Cluster!"}, "audio_narration_url": null}}]}, {"id": "k-means-clustering", "title": "Clustering Algorithms: K-Means", "description": "Dive into K-Means, a popular algorithm for grouping data points into clusters.", "order": 2, "type": "interactive_lesson", "estimatedTimeMinutes": 12, "contentBlocks": [{"id": "ul_l2_s1_intro_kmeans", "type": "lesson_screen", "order": 1, "estimatedTimeSeconds": 75, "content": {"headline": "K-Means: Finding Cluster Centers", "body_md": "**K-Means Clustering** is one of the simplest and most popular unsupervised learning algorithms.\n\nIts goal is to partition `n` observations into `k` clusters in which each observation belongs to the cluster with the nearest mean (cluster centroid).\n\nImagine trying to group a scatter of points on a map into `k` distinct regions, each with a central point.", "visual": {"type": "giphy_search", "value": "grouping points"}, "interactive_element": {"type": "button", "button_text": "How does it work?"}, "audio_narration_url": null}}, {"id": "ul_l2_s2_kmeans_steps", "type": "lesson_screen", "order": 2, "estimatedTimeSeconds": 120, "content": {"headline": "The K-Means Algorithm Steps", "body_md": "K-Means works iteratively:\n\n1.  **Initialization:** Randomly select `k` data points as initial cluster centroids.\n2.  **Assignment Step:** Assign each data point to the cluster whose centroid is closest (e.g., using Euclidean distance).\n3.  **Update Step:** Recalculate the centroids of the new clusters (as the mean of all points assigned to that cluster).\n4.  **Repeat:** Repeat steps 2 and 3 until the cluster assignments no longer change significantly or a maximum number of iterations is reached.", "visual": {"type": "unsplash_search", "value": "iterative process steps"}, "interactive_element": {"type": "multiple_choice_text", "question_text": "In K-Means, what does '<PERSON>' represent?", "options": [{"text": "The number of data points.", "is_correct": false, "feedback": "'K' is not the total number of data points."}, {"text": "The number of clusters to create.", "is_correct": true, "feedback": "Correct! 'K' is a user-defined parameter specifying the desired number of clusters."}, {"text": "The maximum number of iterations.", "is_correct": false, "feedback": "The maximum iterations is a stopping criterion, not 'K'."}]}, "audio_narration_url": null}}, {"id": "ul_l2_s3_choosing_k", "type": "lesson_screen", "order": 3, "estimatedTimeSeconds": 90, "content": {"headline": "Choosing the Right 'K'", "body_md": "Choosing the optimal number of clusters (`k`) is crucial and often not straightforward.\n\nCommon methods include:\n*   **The Elbow Method:** Plot the sum of squared errors (SSE) for different values of `k`. Look for an 'elbow' point where adding more clusters doesn't significantly reduce SSE.\n*   **Silhouette Analysis:** Measures how similar a point is to its own cluster compared to other clusters.\n\nDomain knowledge about the data can also guide the choice of `k`.", "visual": {"type": "giphy_search", "value": "graph elbow curve"}, "interactive_element": {"type": "button", "button_text": "Pros & Cons?"}, "audio_narration_url": null}}, {"id": "ul_l2_s4_kmeans_pros_cons", "type": "lesson_screen", "order": 4, "estimatedTimeSeconds": 100, "content": {"headline": "K-Means: Pros and Cons", "body_md": "**Pros:**\n*   Simple to understand and implement.\n*   Efficient for large datasets.\n*   Often a good starting point for clustering tasks.\n\n**Cons:**\n*   Need to specify `k` (number of clusters) beforehand.\n*   Sensitive to the initial placement of centroids; can get stuck in local optima.\n*   Assumes clusters are spherical and roughly equal in size.\n*   Can be affected by outliers.", "visual": {"type": "unsplash_search", "value": "plus minus signs"}, "interactive_element": {"type": "multiple_choice_text", "question_text": "A major drawback of K-Means is:", "options": [{"text": "It's too complex to implement.", "is_correct": false, "feedback": "K-Means is actually known for its simplicity."}, {"text": "It automatically determines the best number of clusters.", "is_correct": false, "feedback": "This is a challenge; 'k' must be specified by the user."}, {"text": "Its performance can depend on the initial random choice of centroids.", "is_correct": true, "feedback": "Correct! Different initializations can lead to different final clusters."}]}, "audio_narration_url": null}}, {"id": "ul_l2_s5_kmeans_summary", "type": "lesson_screen", "order": 5, "estimatedTimeSeconds": 60, "content": {"headline": "K-Means Clustering: Recap", "body_md": "Key points about K-Means:\n\n*   Partitions data into `k` clusters.\n*   Iteratively assigns points and updates centroids.\n*   Choosing `k` is important.\n*   Simple, efficient, but has limitations.\n\nIt's a foundational algorithm in unsupervised learning!", "visual": {"type": "giphy_search", "value": "cluster grouping"}, "interactive_element": {"type": "button", "button_text": "Next Lesson"}, "audio_narration_url": null}}]}, {"id": "dimensionality-reduction-pca", "title": "Dimensionality Reduction: PCA", "description": "Learn about Principal Component Analysis (PCA) for reducing data complexity.", "order": 3, "type": "interactive_lesson", "estimatedTimeMinutes": 12, "contentBlocks": [{"id": "ul_l3_s1_intro_pca", "type": "lesson_screen", "order": 1, "estimatedTimeSeconds": 75, "content": {"headline": "Too Many Features? Dimensionality Reduction!", "body_md": "Sometimes, datasets have a very large number of features (dimensions). This can make them hard to work with, visualize, and can even hurt model performance (the 'curse of dimensionality').\n\n**Dimensionality Reduction** techniques aim to reduce the number of features while retaining as much important information as possible.\n\nOne popular method is **Principal Component Analysis (PCA)**.", "visual": {"type": "giphy_search", "value": "shrinking data"}, "interactive_element": {"type": "button", "button_text": "What is PCA?"}, "audio_narration_url": null}}, {"id": "ul_l3_s2_what_is_pca", "type": "lesson_screen", "order": 2, "estimatedTimeSeconds": 100, "content": {"headline": "Principal Component Analysis (PCA)", "body_md": "PCA finds new, artificial features called **principal components**. These components are linear combinations of the original features and are chosen to capture the maximum possible variance in the data.\n\nThe first principal component captures the most variance, the second captures the next most (and is uncorrelated with the first), and so on.\n\nBy keeping only the first few principal components, we can reduce dimensionality while losing minimal information.", "visual": {"type": "unsplash_search", "value": "data projection dimensions"}, "interactive_element": {"type": "multiple_choice_text", "question_text": "The first principal component in PCA is chosen to:", "options": [{"text": "Minimize the variance in the data.", "is_correct": false, "feedback": "PCA aims to maximize variance captured by components."}, {"text": "Capture the maximum possible variance in the data.", "is_correct": true, "feedback": "Correct! This ensures it represents the most significant direction of spread in the data."}, {"text": "Be perfectly correlated with all original features.", "is_correct": false, "feedback": "It's a combination, but not necessarily perfectly correlated with all."}]}, "audio_narration_url": null}}, {"id": "ul_l3_s3_pca_benefits", "type": "lesson_screen", "order": 3, "estimatedTimeSeconds": 90, "content": {"headline": "Benefits of PCA", "body_md": "Why use PCA?\n\n*   **Reduces Overfitting:** Fewer features can lead to simpler models that generalize better.\n*   **Improves Performance:** Some algorithms run faster with fewer features.\n*   **Data Visualization:** Can reduce data to 2 or 3 dimensions for easier plotting and visual exploration.\n*   **Noise Reduction:** Can help remove irrelevant noise by focusing on components with high variance.", "visual": {"type": "giphy_search", "value": "improvement graph"}, "interactive_element": {"type": "button", "button_text": "Any Downsides?"}, "audio_narration_url": null}}, {"id": "ul_l3_s4_pca_downsides", "type": "lesson_screen", "order": 4, "estimatedTimeSeconds": 90, "content": {"headline": "Downsides of PCA", "body_md": "While useful, PCA has some drawbacks:\n\n*   **Interpretability:** Principal components are combinations of original features, making them harder to interpret directly.\n*   **Information Loss:** Some information is inevitably lost when reducing dimensions, though PCA tries to minimize this.\n*   **Assumption of Linearity:** PCA assumes linear relationships between features.\n*   **Sensitive to Data Scaling:** Features with larger scales can dominate PCA, so it's important to scale your data (e.g., standardize) before applying PCA.", "visual": {"type": "unsplash_search", "value": "broken puzzle piece"}, "interactive_element": {"type": "multiple_choice_text", "question_text": "Why is data scaling important before PCA?", "options": [{"text": "It makes the principal components easier to name.", "is_correct": false, "feedback": "Scaling doesn't directly help with naming components."}, {"text": "It prevents features with larger values from disproportionately influencing the components.", "is_correct": true, "feedback": "Correct! Scaling ensures all features contribute more fairly to the variance calculation."}, {"text": "It guarantees that no information will be lost.", "is_correct": false, "feedback": "Some information loss is usually expected in dimensionality reduction."}]}, "audio_narration_url": null}}, {"id": "ul_l3_s5_pca_summary", "type": "lesson_screen", "order": 5, "estimatedTimeSeconds": 60, "content": {"headline": "PCA: Key Takeaways", "body_md": "Principal Component Analysis (PCA):\n\n*   Reduces dimensionality by creating new features (principal components) that capture maximum variance.\n*   Helps with overfitting, performance, visualization, and noise reduction.\n*   Components can be less interpretable, and data scaling is important.\n\nPCA is a valuable tool for simplifying complex datasets!", "visual": {"type": "giphy_search", "value": "data simplify"}, "interactive_element": {"type": "button", "button_text": "Finish Lesson"}, "audio_narration_url": null}}]}], "moduleTest": {"id": "unsupervised-learning-test", "title": "Module Test: Unsupervised Learning", "description": "Test your understanding of unsupervised learning techniques.", "type": "module_test_interactive", "estimatedTimeMinutes": 15, "contentBlocks": [{"id": "ul_test_q1_unlabeled_data", "type": "question_screen", "order": 1, "estimatedTimeSeconds": 60, "content": {"headline": "Question 1: Core of Unsupervised Learning", "body_md": "What is the defining characteristic of the data used in unsupervised learning?", "visual": {"type": "giphy_search", "value": "mystery box"}, "interactive_element": {"type": "multiple_choice_text", "question_text": "Defining characteristic of data in unsupervised learning?", "options": [{"text": "The data is perfectly clean and error-free.", "is_correct": false, "feedback": "Data cleanliness is important for all ML, but not the defining trait of unsupervised learning data."}, {"text": "The data has no predefined output labels.", "is_correct": true, "feedback": "Correct! Unsupervised learning algorithms must find patterns in data without explicit output labels."}, {"text": "The data must be numerical.", "is_correct": false, "feedback": "Unsupervised learning can work with various data types, though preprocessing might be needed."}]}, "audio_narration_url": null}}, {"id": "ul_test_q2_clustering_goal", "type": "question_screen", "order": 2, "estimatedTimeSeconds": 75, "content": {"headline": "Question 2: Goal of Clustering", "body_md": "What is the primary objective of clustering algorithms like K-Means?", "visual": {"type": "unsplash_search", "value": "group of similar items"}, "interactive_element": {"type": "multiple_choice_text", "question_text": "Primary objective of clustering?", "options": [{"text": "To predict a continuous value for each data point.", "is_correct": false, "feedback": "This describes regression, a supervised learning task."}, {"text": "To assign data points to predefined categories.", "is_correct": false, "feedback": "Assigning to predefined categories is classification, a supervised task."}, {"text": "To group similar data points together based on their features.", "is_correct": true, "feedback": "Correct! Clustering aims to find natural groupings in the data."}]}, "audio_narration_url": null}}, {"id": "ul_test_q3_k_in_kmeans", "type": "question_screen", "order": 3, "estimatedTimeSeconds": 75, "content": {"headline": "Question 3: The 'K' in K-Means", "body_md": "In the K-Means clustering algorithm, what does the parameter 'K' represent?", "visual": {"type": "giphy_search", "value": "number k"}, "interactive_element": {"type": "multiple_choice_text", "question_text": "What does '<PERSON>' represent in K-Means?", "options": [{"text": "The number of features in the dataset.", "is_correct": false, "feedback": "The number of features is an inherent property of the dataset, not 'K'."}, {"text": "The desired number of clusters to be formed.", "is_correct": true, "feedback": "Correct! 'K' is specified by the user to tell the algorithm how many groups to find."}, {"text": "The number of iterations the algorithm will run.", "is_correct": false, "feedback": "The number of iterations is a separate parameter or stopping condition."}]}, "audio_narration_url": null}}, {"id": "ul_test_q4_pca_main_purpose", "type": "question_screen", "order": 4, "estimatedTimeSeconds": 90, "content": {"headline": "Question 4: Purpose of PCA", "body_md": "What is the primary purpose of Principal Component Analysis (PCA)?", "visual": {"type": "unsplash_search", "value": "data compression"}, "interactive_element": {"type": "multiple_choice_text", "question_text": "Primary purpose of PCA?", "options": [{"text": "To increase the number of features to make models more complex.", "is_correct": false, "feedback": "PCA is used for dimensionality reduction, i.e., reducing features."}, {"text": "To label unlabeled data for supervised learning.", "is_correct": false, "feedback": "PCA is an unsupervised technique; it doesn't create labels."}, {"text": "To reduce the number of features (dimensions) in a dataset while retaining important information.", "is_correct": true, "feedback": "Correct! PCA aims to simplify data by finding principal components that capture the most variance."}]}, "audio_narration_url": null}}, {"id": "ul_test_q5_pca_interpretability", "type": "question_screen", "order": 5, "estimatedTimeSeconds": 75, "content": {"headline": "Question 5: PCA Interpretability", "body_md": "What is a common challenge when using PCA regarding the new features it creates?", "visual": {"type": "giphy_search", "value": "thinking hard"}, "interactive_element": {"type": "multiple_choice_text", "question_text": "Common challenge with PCA's new features?", "options": [{"text": "They are always perfectly correlated with each other.", "is_correct": false, "feedback": "Principal components are designed to be uncorrelated."}, {"text": "They can be difficult to interpret in terms of the original features.", "is_correct": true, "feedback": "Correct! Since principal components are linear combinations of original features, their direct meaning can be less intuitive."}, {"text": "They always result in a loss of all important information.", "is_correct": false, "feedback": "PCA aims to retain as much important information (variance) as possible."}]}, "audio_narration_url": null}}]}}