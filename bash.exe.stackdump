Stack trace:
Frame         Function      Args
0007FFFFBF80  00021005FE8E (000210285F68, 00021026AB6E, 000000000000, 0007FFFFAE80) msys-2.0.dll+0x1FE8E
0007FFFFBF80  0002100467F9 (000000000000, 000000000000, 000000000000, 0007FFFFC258) msys-2.0.dll+0x67F9
0007FFFFBF80  000210046832 (000210286019, 0007FFFFBE38, 000000000000, 000000000000) msys-2.0.dll+0x6832
0007FFFFBF80  000210068CF6 (000000000000, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x28CF6
0007FFFFBF80  000210068E24 (0007FFFFBF90, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x28E24
0007FFFFC260  00021006A225 (0007FFFFBF90, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x2A225
End of stack trace
Loaded modules:
000100400000 bash.exe
7FFC6EA20000 ntdll.dll
7FFC6D010000 KERNEL32.DLL
7FFC6C150000 KERNELBASE.dll
7FFC68CD0000 apphelp.dll
7FFC6D920000 USER32.dll
7FFC6C680000 win32u.dll
7FFC6CEA0000 GDI32.dll
7FFC6BE00000 gdi32full.dll
7FFC6BD50000 msvcp_win.dll
7FFC6BC00000 ucrtbase.dll
000210040000 msys-2.0.dll
7FFC6C8E0000 advapi32.dll
7FFC6DAF0000 msvcrt.dll
7FFC6D490000 sechost.dll
7FFC6C9A0000 RPCRT4.dll
7FFC6B2F0000 CRYPTBASE.DLL
7FFC6C520000 bcryptPrimitives.dll
7FFC6E460000 IMM32.DLL
