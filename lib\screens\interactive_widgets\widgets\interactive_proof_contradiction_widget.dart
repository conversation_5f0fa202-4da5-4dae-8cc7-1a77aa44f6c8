import 'package:flutter/material.dart';

/// A widget that guides users through proof by contradiction exercises
class InteractiveProofContradictionWidget extends StatefulWidget {
  /// The data for this widget
  final Map<String, dynamic> data;

  /// Optional callback when the widget state changes
  final Function(bool isCompleted)? onStateChanged;

  const InteractiveProofContradictionWidget({
    Key? key,
    required this.data,
    this.onStateChanged,
  }) : super(key: key);

  /// Factory constructor to create the widget from JSON data
  factory InteractiveProofContradictionWidget.fromData(Map<String, dynamic> data) {
    return InteractiveProofContradictionWidget(
      data: data,
    );
  }

  @override
  State<InteractiveProofContradictionWidget> createState() => _InteractiveProofContradictionWidgetState();
}

class _InteractiveProofContradictionWidgetState extends State<InteractiveProofContradictionWidget> {
  // State variables
  bool _isCompleted = false;
  late String _statement;
  late String _negatedStatement;
  late List<ProofStep> _proofSteps;
  late int _currentStepIndex = 0;
  late List<int?> _userAnswers;
  bool _showFeedback = false;
  bool _allCorrect = false;

  @override
  void initState() {
    super.initState();
    
    // Initialize from data
    _statement = widget.data['statement'] ?? 'Statement to prove';
    _negatedStatement = widget.data['negated_statement'] ?? 'Negated statement';
    
    // Load proof steps
    _proofSteps = [];
    final steps = widget.data['proof_steps'] as List<dynamic>? ?? [];
    for (final step in steps) {
      _proofSteps.add(ProofStep.fromJson(step as Map<String, dynamic>));
    }
    
    // Initialize user answers
    _userAnswers = List<int?>.filled(_proofSteps.length, null);
  }

  /// Checks if all user answers are correct
  void _checkAnswers() {
    // Check if all questions are answered
    if (_userAnswers.contains(null)) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('Please select an option for each step'),
          backgroundColor: Colors.orange,
        ),
      );
      return;
    }
    
    // Check if all answers are correct
    _allCorrect = true;
    for (int i = 0; i < _proofSteps.length; i++) {
      if (_userAnswers[i] != _proofSteps[i].correctOptionIndex) {
        _allCorrect = false;
        break;
      }
    }
    
    setState(() {
      _showFeedback = true;
      _isCompleted = _allCorrect;
    });
    
    if (widget.onStateChanged != null) {
      widget.onStateChanged!(_isCompleted);
    }
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withOpacity(0.2),
            spreadRadius: 1,
            blurRadius: 3,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Title and statement
          Text(
            'Proof by Contradiction',
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
              color: Colors.purple[800],
            ),
          ),
          const SizedBox(height: 8),
          Container(
            padding: const EdgeInsets.all(12),
            decoration: BoxDecoration(
              color: Colors.purple[50],
              borderRadius: BorderRadius.circular(8),
              border: Border.all(color: Colors.purple[200]!),
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'Statement to prove: $_statement',
                  style: const TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const SizedBox(height: 8),
                Text(
                  'Step 1: Assume the negation: $_negatedStatement',
                  style: const TextStyle(
                    fontSize: 14,
                    fontStyle: FontStyle.italic,
                  ),
                ),
              ],
            ),
          ),
          const SizedBox(height: 16),
          
          // Proof steps
          Text(
            'Step 2: Follow the logical chain to find a contradiction',
            style: TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.bold,
              color: Colors.purple[700],
            ),
          ),
          const SizedBox(height: 8),
          
          // Steps list
          ListView.builder(
            shrinkWrap: true,
            physics: const NeverScrollableScrollPhysics(),
            itemCount: _proofSteps.length,
            itemBuilder: (context, index) {
              return _buildProofStepCard(index);
            },
          ),
          
          const SizedBox(height: 16),
          
          // Action buttons
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              // Reset button
              if (_showFeedback)
                ElevatedButton.icon(
                  onPressed: () {
                    setState(() {
                      _userAnswers = List<int?>.filled(_proofSteps.length, null);
                      _showFeedback = false;
                    });
                  },
                  icon: const Icon(Icons.refresh),
                  label: const Text('Try Again'),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Colors.grey[300],
                    foregroundColor: Colors.black87,
                  ),
                ),
              
              // Check answers button
              if (!_showFeedback)
                ElevatedButton.icon(
                  onPressed: _checkAnswers,
                  icon: const Icon(Icons.check),
                  label: const Text('Check Answers'),
                ),
            ],
          ),
          
          // Feedback
          if (_showFeedback) ...[
            const SizedBox(height: 16),
            Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: _allCorrect ? Colors.green[50] : Colors.red[50],
                borderRadius: BorderRadius.circular(8),
                border: Border.all(
                  color: _allCorrect ? Colors.green[300]! : Colors.red[300]!,
                ),
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    _allCorrect ? 'Correct!' : 'Not quite right',
                    style: TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.bold,
                      color: _allCorrect ? Colors.green[700] : Colors.red[700],
                    ),
                  ),
                  const SizedBox(height: 8),
                  Text(
                    _allCorrect
                        ? 'Great job! You\'ve correctly worked through the proof by contradiction.'
                        : 'Some of your steps are incorrect. Try again!',
                  ),
                  if (_allCorrect) ...[
                    const SizedBox(height: 8),
                    const Text(
                      'Conclusion:',
                      style: TextStyle(
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    Text(
                      'Since assuming $_negatedStatement leads to a contradiction, the original statement $_statement must be true.',
                    ),
                  ],
                ],
              ),
            ),
          ],
          
          // Widget name tag at the bottom
          if (widget.data['showNameTag'] ?? true)
            Padding(
              padding: const EdgeInsets.only(top: 8),
              child: Text(
                'InteractiveProofContradictionWidget',
                style: TextStyle(
                  color: Colors.grey[600],
                  fontSize: 12,
                  fontStyle: FontStyle.italic,
                ),
              ),
            ),
        ],
      ),
    );
  }

  /// Builds a card for a single proof step
  Widget _buildProofStepCard(int index) {
    final step = _proofSteps[index];
    final userAnswer = _userAnswers[index];
    final isCorrect = _showFeedback && userAnswer == step.correctOptionIndex;
    final isIncorrect = _showFeedback && userAnswer != null && userAnswer != step.correctOptionIndex;
    
    return Card(
      margin: const EdgeInsets.only(bottom: 12),
      color: isCorrect
          ? Colors.green[50]
          : isIncorrect
              ? Colors.red[50]
              : Colors.white,
      child: Padding(
        padding: const EdgeInsets.all(12),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Step ${index + 1}: ${step.prompt}',
              style: const TextStyle(
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 8),
            ...step.options.asMap().entries.map((entry) {
              final optionIndex = entry.key;
              final option = entry.value;
              final isSelected = userAnswer == optionIndex;
              
              return RadioListTile<int>(
                title: Text(option),
                value: optionIndex,
                groupValue: userAnswer,
                onChanged: _showFeedback
                    ? null
                    : (value) {
                        setState(() {
                          _userAnswers[index] = value;
                        });
                      },
                activeColor: _showFeedback
                    ? (optionIndex == step.correctOptionIndex
                        ? Colors.green
                        : Colors.red)
                    : Colors.purple,
                selected: isSelected,
              );
            }).toList(),
            
            // Feedback for this step
            if (_showFeedback) ...[
              const SizedBox(height: 8),
              Container(
                padding: const EdgeInsets.all(8),
                decoration: BoxDecoration(
                  color: isCorrect
                      ? Colors.green.withOpacity(0.1)
                      : Colors.red.withOpacity(0.1),
                  borderRadius: BorderRadius.circular(4),
                ),
                child: Text(
                  isCorrect
                      ? step.correctFeedback
                      : step.incorrectFeedback,
                  style: TextStyle(
                    color: isCorrect ? Colors.green[800] : Colors.red[800],
                    fontStyle: FontStyle.italic,
                  ),
                ),
              ),
            ],
          ],
        ),
      ),
    );
  }
}

/// Represents a single step in the proof by contradiction
class ProofStep {
  final String prompt;
  final List<String> options;
  final int correctOptionIndex;
  final String correctFeedback;
  final String incorrectFeedback;

  ProofStep({
    required this.prompt,
    required this.options,
    required this.correctOptionIndex,
    required this.correctFeedback,
    required this.incorrectFeedback,
  });

  factory ProofStep.fromJson(Map<String, dynamic> json) {
    return ProofStep(
      prompt: json['prompt'] ?? '',
      options: List<String>.from(json['options'] ?? []),
      correctOptionIndex: json['correct_option_index'] ?? 0,
      correctFeedback: json['correct_feedback'] ?? 'Correct!',
      incorrectFeedback: json['incorrect_feedback'] ?? 'Not quite right. Try again!',
    );
  }
}
