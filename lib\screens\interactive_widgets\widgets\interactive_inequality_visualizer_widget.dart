import 'package:flutter/material.dart';
import 'dart:math' as math;

/// A widget that visualizes inequalities on a coordinate plane
class InteractiveInequalityVisualizerWidget extends StatefulWidget {
  /// The data for this widget
  final Map<String, dynamic> data;

  /// Optional callback when the widget state changes
  final Function(bool isCompleted)? onStateChanged;

  const InteractiveInequalityVisualizerWidget({
    Key? key,
    required this.data,
    this.onStateChanged,
  }) : super(key: key);

  /// Factory constructor to create the widget from JSON data
  factory InteractiveInequalityVisualizerWidget.fromData(Map<String, dynamic> data) {
    return InteractiveInequalityVisualizerWidget(
      data: data,
    );
  }

  @override
  State<InteractiveInequalityVisualizerWidget> createState() => _InteractiveInequalityVisualizerWidgetState();
}

class _InteractiveInequalityVisualizerWidgetState extends State<InteractiveInequalityVisualizerWidget> {
  // State variables
  bool _isCompleted = false;
  double _slope = 1.0;
  double _yIntercept = 0.0;
  String _inequalityType = '>';
  bool _isShaded = true;
  int _currentChallengeIndex = 0;
  List<Map<String, dynamic>> _challenges = [];
  
  // Colors
  late Color _primaryColor;
  late Color _secondaryColor;
  late Color _accentColor;
  late Color _textColor;
  late Color _successColor;
  late Color _errorColor;
  late Color _shadingColor;

  @override
  void initState() {
    super.initState();
    _initializeFromData();
  }

  void _initializeFromData() {
    // Initialize colors
    _primaryColor = _parseColor(widget.data['primaryColor'] ?? '#2196F3');
    _secondaryColor = _parseColor(widget.data['secondaryColor'] ?? '#4CAF50');
    _accentColor = _parseColor(widget.data['accentColor'] ?? '#FF9800');
    _textColor = _parseColor(widget.data['textColor'] ?? '#212121');
    _successColor = _parseColor(widget.data['successColor'] ?? '#4CAF50');
    _errorColor = _parseColor(widget.data['errorColor'] ?? '#F44336');
    _shadingColor = _parseColor(widget.data['shadingColor'] ?? '#2196F3').withOpacity(0.2);
    
    // Initialize challenges
    _challenges = List<Map<String, dynamic>>.from(widget.data['challenges'] ?? []);
    
    if (_challenges.isNotEmpty) {
      _loadChallenge(0);
    }
  }

  void _loadChallenge(int index) {
    if (index < 0 || index >= _challenges.length) return;
    
    final challenge = _challenges[index];
    
    setState(() {
      _currentChallengeIndex = index;
      _slope = challenge['slope']?.toDouble() ?? 1.0;
      _yIntercept = challenge['yIntercept']?.toDouble() ?? 0.0;
      _inequalityType = challenge['inequalityType'] ?? '>';
      _isShaded = challenge['isShaded'] ?? true;
      _isCompleted = false;
    });
  }

  Color _parseColor(String hexColor) {
    hexColor = hexColor.replaceAll('#', '');
    if (hexColor.length == 6) {
      hexColor = 'FF$hexColor';
    }
    return Color(int.parse(hexColor, radix: 16));
  }

  void _updateSlope(double value) {
    setState(() {
      _slope = value;
      _checkCompletion();
    });
  }

  void _updateYIntercept(double value) {
    setState(() {
      _yIntercept = value;
      _checkCompletion();
    });
  }

  void _toggleInequalityType() {
    setState(() {
      if (_inequalityType == '>') {
        _inequalityType = '>=';
      } else if (_inequalityType == '>=') {
        _inequalityType = '<';
      } else if (_inequalityType == '<') {
        _inequalityType = '<=';
      } else {
        _inequalityType = '>';
      }
      _checkCompletion();
    });
  }

  void _toggleShading() {
    setState(() {
      _isShaded = !_isShaded;
      _checkCompletion();
    });
  }

  void _checkCompletion() {
    final challenge = _challenges[_currentChallengeIndex];
    
    final targetSlope = challenge['targetSlope']?.toDouble() ?? 0;
    final targetYIntercept = challenge['targetYIntercept']?.toDouble() ?? 0;
    final targetInequalityType = challenge['targetInequalityType'] ?? '>';
    final targetIsShaded = challenge['targetIsShaded'] ?? true;
    final tolerance = challenge['tolerance']?.toDouble() ?? 0.1;
    
    final slopeMatch = (targetSlope - _slope).abs() <= tolerance;
    final yInterceptMatch = (targetYIntercept - _yIntercept).abs() <= tolerance;
    final inequalityTypeMatch = targetInequalityType == _inequalityType;
    final shadingMatch = targetIsShaded == _isShaded;
    
    final isCorrect = slopeMatch && yInterceptMatch && inequalityTypeMatch && shadingMatch;
    
    if (isCorrect != _isCompleted) {
      setState(() {
        _isCompleted = isCorrect;
      });
      widget.onStateChanged?.call(isCorrect);
    }
  }

  void _resetChallenge() {
    _loadChallenge(_currentChallengeIndex);
  }

  void _nextChallenge() {
    if (_currentChallengeIndex < _challenges.length - 1) {
      _loadChallenge(_currentChallengeIndex + 1);
    }
  }

  void _previousChallenge() {
    if (_currentChallengeIndex > 0) {
      _loadChallenge(_currentChallengeIndex - 1);
    }
  }

  String _getInequalityEquation() {
    final sign = _inequalityType;
    final yIntercept = _yIntercept >= 0 ? '+ ${_yIntercept.toStringAsFixed(1)}' : '- ${(_yIntercept * -1).toStringAsFixed(1)}';
    return 'y $sign ${_slope.toStringAsFixed(1)}x $yIntercept';
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withOpacity(0.2),
            spreadRadius: 1,
            blurRadius: 3,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Title
          Text(
            widget.data['title'] ?? 'Inequality Visualizer',
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
              color: _textColor,
            ),
          ),
          
          const SizedBox(height: 12),
          
          // Challenge description
          if (_challenges.isNotEmpty)
            Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: _primaryColor.withOpacity(0.1),
                borderRadius: BorderRadius.circular(8),
                border: Border.all(color: _primaryColor.withOpacity(0.3)),
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'Challenge ${_currentChallengeIndex + 1}/${_challenges.length}',
                    style: TextStyle(
                      fontWeight: FontWeight.bold,
                      color: _primaryColor,
                    ),
                  ),
                  const SizedBox(height: 4),
                  Text(
                    _challenges[_currentChallengeIndex]['description'] ?? '',
                    style: TextStyle(
                      fontSize: 14,
                      color: _textColor,
                    ),
                  ),
                ],
              ),
            ),
          
          const SizedBox(height: 16),
          
          // Current inequality equation
          Container(
            padding: const EdgeInsets.all(12),
            decoration: BoxDecoration(
              color: Colors.grey.shade50,
              borderRadius: BorderRadius.circular(8),
              border: Border.all(
                color: _isCompleted ? _successColor : Colors.grey.shade300,
                width: _isCompleted ? 2 : 1,
              ),
            ),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Text(
                  _getInequalityEquation(),
                  style: TextStyle(
                    fontSize: 20,
                    fontWeight: FontWeight.bold,
                    color: _textColor,
                  ),
                ),
              ],
            ),
          ),
          
          const SizedBox(height: 16),
          
          // Inequality visualization
          Container(
            height: 200,
            width: double.infinity,
            decoration: BoxDecoration(
              color: Colors.grey.shade50,
              borderRadius: BorderRadius.circular(8),
              border: Border.all(
                color: _isCompleted ? _successColor : Colors.grey.shade300,
                width: _isCompleted ? 2 : 1,
              ),
            ),
            child: CustomPaint(
              painter: InequalityPainter(
                slope: _slope,
                yIntercept: _yIntercept,
                inequalityType: _inequalityType,
                isShaded: _isShaded,
                primaryColor: _primaryColor,
                shadingColor: _shadingColor,
              ),
            ),
          ),
          
          const SizedBox(height: 16),
          
          // Controls
          Row(
            children: [
              // Slope and y-intercept controls
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Slope',
                      style: TextStyle(
                        fontWeight: FontWeight.bold,
                        color: _textColor,
                      ),
                    ),
                    Row(
                      children: [
                        Text(
                          _slope.toStringAsFixed(1),
                          style: TextStyle(
                            color: _primaryColor,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                      ],
                    ),
                    Slider(
                      value: _slope,
                      min: -5,
                      max: 5,
                      divisions: 100,
                      label: _slope.toStringAsFixed(1),
                      activeColor: _primaryColor,
                      onChanged: _updateSlope,
                    ),
                    
                    Text(
                      'Y-Intercept',
                      style: TextStyle(
                        fontWeight: FontWeight.bold,
                        color: _textColor,
                      ),
                    ),
                    Row(
                      children: [
                        Text(
                          _yIntercept.toStringAsFixed(1),
                          style: TextStyle(
                            color: _accentColor,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                      ],
                    ),
                    Slider(
                      value: _yIntercept,
                      min: -5,
                      max: 5,
                      divisions: 100,
                      label: _yIntercept.toStringAsFixed(1),
                      activeColor: _accentColor,
                      onChanged: _updateYIntercept,
                    ),
                  ],
                ),
              ),
              
              const SizedBox(width: 16),
              
              // Inequality type and shading controls
              Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'Inequality Type',
                    style: TextStyle(
                      fontWeight: FontWeight.bold,
                      color: _textColor,
                    ),
                  ),
                  const SizedBox(height: 8),
                  ElevatedButton(
                    onPressed: _toggleInequalityType,
                    style: ElevatedButton.styleFrom(
                      backgroundColor: _primaryColor,
                      foregroundColor: Colors.white,
                      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
                    ),
                    child: Text(_inequalityType),
                  ),
                  
                  const SizedBox(height: 16),
                  
                  Text(
                    'Shading',
                    style: TextStyle(
                      fontWeight: FontWeight.bold,
                      color: _textColor,
                    ),
                  ),
                  const SizedBox(height: 8),
                  ElevatedButton(
                    onPressed: _toggleShading,
                    style: ElevatedButton.styleFrom(
                      backgroundColor: _isShaded ? _accentColor : Colors.grey.shade300,
                      foregroundColor: _isShaded ? Colors.white : Colors.black87,
                      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
                    ),
                    child: Text(_isShaded ? 'Shaded' : 'Not Shaded'),
                  ),
                ],
              ),
            ],
          ),
          
          const SizedBox(height: 16),
          
          // Feedback
          if (_isCompleted)
            Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: _successColor.withOpacity(0.1),
                borderRadius: BorderRadius.circular(8),
                border: Border.all(color: _successColor),
              ),
              child: Row(
                children: [
                  Icon(Icons.check_circle, color: _successColor),
                  const SizedBox(width: 8),
                  Expanded(
                    child: Text(
                      _challenges[_currentChallengeIndex]['successMessage'] ?? 'Correct! You\'ve represented the inequality correctly.',
                      style: TextStyle(
                        color: _successColor,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                  ),
                ],
              ),
            ),
          
          const SizedBox(height: 16),
          
          // Navigation buttons
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              // Previous/Reset buttons
              Row(
                children: [
                  // Previous challenge button
                  if (_currentChallengeIndex > 0)
                    ElevatedButton.icon(
                      onPressed: _previousChallenge,
                      icon: const Icon(Icons.arrow_back),
                      label: const Text('Previous'),
                      style: ElevatedButton.styleFrom(
                        backgroundColor: Colors.grey.shade200,
                        foregroundColor: Colors.black87,
                      ),
                    ),
                  
                  const SizedBox(width: 8),
                  
                  // Reset button
                  ElevatedButton.icon(
                    onPressed: _resetChallenge,
                    icon: const Icon(Icons.refresh),
                    label: const Text('Reset'),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.grey.shade200,
                      foregroundColor: Colors.black87,
                    ),
                  ),
                ],
              ),
              
              // Next challenge button
              if (_isCompleted && _currentChallengeIndex < _challenges.length - 1)
                ElevatedButton.icon(
                  onPressed: _nextChallenge,
                  icon: const Icon(Icons.arrow_forward),
                  label: const Text('Next Challenge'),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: _primaryColor,
                    foregroundColor: Colors.white,
                  ),
                ),
            ],
          ),
          
          // Widget name tag at the bottom
          if (widget.data['showNameTag'] ?? true)
            Padding(
              padding: const EdgeInsets.only(top: 8),
              child: Text(
                'InteractiveInequalityVisualizer',
                style: TextStyle(
                  color: Colors.grey[600],
                  fontSize: 12,
                  fontStyle: FontStyle.italic,
                ),
              ),
            ),
        ],
      ),
    );
  }
}

/// Custom painter for drawing the inequality
class InequalityPainter extends CustomPainter {
  final double slope;
  final double yIntercept;
  final String inequalityType;
  final bool isShaded;
  final Color primaryColor;
  final Color shadingColor;
  
  InequalityPainter({
    required this.slope,
    required this.yIntercept,
    required this.inequalityType,
    required this.isShaded,
    required this.primaryColor,
    required this.shadingColor,
  });
  
  @override
  void paint(Canvas canvas, Size size) {
    final paint = Paint()
      ..color = primaryColor
      ..strokeWidth = 2
      ..style = PaintingStyle.stroke;
    
    final shadePaint = Paint()
      ..color = shadingColor
      ..style = PaintingStyle.fill;
    
    final dashPaint = Paint()
      ..color = primaryColor
      ..strokeWidth = 2
      ..style = PaintingStyle.stroke;
    
    final gridPaint = Paint()
      ..color = Colors.grey.shade300
      ..strokeWidth = 1
      ..style = PaintingStyle.stroke;
    
    final axesPaint = Paint()
      ..color = Colors.black87
      ..strokeWidth = 2
      ..style = PaintingStyle.stroke;
    
    final textPainter = TextPainter(
      textDirection: TextDirection.ltr,
      textAlign: TextAlign.center,
    );
    
    // Draw grid
    final gridSize = 20.0;
    for (double x = 0; x <= size.width; x += gridSize) {
      canvas.drawLine(
        Offset(x, 0),
        Offset(x, size.height),
        gridPaint,
      );
    }
    
    for (double y = 0; y <= size.height; y += gridSize) {
      canvas.drawLine(
        Offset(0, y),
        Offset(size.width, y),
        gridPaint,
      );
    }
    
    // Draw axes
    final centerX = size.width / 2;
    final centerY = size.height / 2;
    
    canvas.drawLine(
      Offset(0, centerY),
      Offset(size.width, centerY),
      axesPaint,
    );
    
    canvas.drawLine(
      Offset(centerX, 0),
      Offset(centerX, size.height),
      axesPaint,
    );
    
    // Draw axis labels
    for (int i = -5; i <= 5; i++) {
      if (i == 0) continue;
      
      // X-axis labels
      final x = centerX + i * gridSize;
      textPainter.text = TextSpan(
        text: i.toString(),
        style: TextStyle(
          color: Colors.black87,
          fontSize: 10,
        ),
      );
      textPainter.layout();
      textPainter.paint(
        canvas,
        Offset(x - textPainter.width / 2, centerY + 5),
      );
      
      // Y-axis labels
      final y = centerY - i * gridSize;
      textPainter.text = TextSpan(
        text: i.toString(),
        style: TextStyle(
          color: Colors.black87,
          fontSize: 10,
        ),
      );
      textPainter.layout();
      textPainter.paint(
        canvas,
        Offset(centerX + 5, y - textPainter.height / 2),
      );
    }
    
    // Calculate line points
    final startX = 0.0;
    final startY = centerY - (slope * (-centerX) + yIntercept * gridSize);
    final endX = size.width;
    final endY = centerY - (slope * (size.width - centerX) + yIntercept * gridSize);
    
    // Draw inequality line
    if (inequalityType == '>=' || inequalityType == '<=') {
      canvas.drawLine(
        Offset(startX, startY),
        Offset(endX, endY),
        paint,
      );
    } else {
      // Draw dashed line for strict inequalities
      final dashWidth = 5.0;
      final dashSpace = 3.0;
      final dx = endX - startX;
      final dy = endY - startY;
      final distance = math.sqrt(dx * dx + dy * dy);
      final dashCount = distance / (dashWidth + dashSpace);
      
      final dashDx = dx / dashCount;
      final dashDy = dy / dashCount;
      
      for (int i = 0; i < dashCount.floor(); i++) {
        final startDashX = startX + i * (dashDx + dashSpace);
        final startDashY = startY + i * (dashDy + dashSpace);
        final endDashX = startDashX + dashDx;
        final endDashY = startDashY + dashDy;
        
        canvas.drawLine(
          Offset(startDashX, startDashY),
          Offset(endDashX, endDashY),
          dashPaint,
        );
      }
    }
    
    // Shade the appropriate region
    if (isShaded) {
      final path = Path();
      
      // Determine which side to shade based on inequality type
      final shouldShadeBelow = inequalityType == '<' || inequalityType == '<=';
      
      if (shouldShadeBelow) {
        path.moveTo(startX, startY);
        path.lineTo(endX, endY);
        path.lineTo(endX, size.height);
        path.lineTo(startX, size.height);
      } else {
        path.moveTo(startX, startY);
        path.lineTo(endX, endY);
        path.lineTo(endX, 0);
        path.lineTo(startX, 0);
      }
      
      path.close();
      canvas.drawPath(path, shadePaint);
    }
  }
  
  @override
  bool shouldRepaint(InequalityPainter oldDelegate) {
    return oldDelegate.slope != slope ||
           oldDelegate.yIntercept != yIntercept ||
           oldDelegate.inequalityType != inequalityType ||
           oldDelegate.isShaded != isShaded;
  }
}
