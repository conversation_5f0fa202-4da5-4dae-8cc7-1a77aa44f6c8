import 'package:flutter/material.dart';
import 'dart:math' as math;

/// A widget that allows users to analyze the strength of scientific arguments
class InteractiveArgumentStrengthAnalyzerWidget extends StatefulWidget {
  /// The data for this widget
  final Map<String, dynamic> data;

  /// Optional callback when the widget state changes
  final Function(bool isCompleted)? onStateChanged;

  const InteractiveArgumentStrengthAnalyzerWidget({
    super.key,
    required this.data,
    this.onStateChanged,
  });

  /// Factory constructor to create the widget from JSON data
  factory InteractiveArgumentStrengthAnalyzerWidget.fromData(Map<String, dynamic> data) {
    return InteractiveArgumentStrengthAnalyzerWidget(
      data: data,
    );
  }

  @override
  State<InteractiveArgumentStrengthAnalyzerWidget> createState() => _InteractiveArgumentStrengthAnalyzerWidgetState();
}

class _InteractiveArgumentStrengthAnalyzerWidgetState extends State<InteractiveArgumentStrengthAnalyzerWidget> {
  // Arguments and scenarios
  late List<ArgumentScenario> _scenarios;
  late int _currentScenarioIndex;
  
  // Analysis state
  late Map<String, double> _criteriaRatings;
  late Map<String, String> _criteriaFeedback;
  late double _overallScore;
  late String _overallFeedback;
  late bool _hasSubmitted;
  
  // UI state
  late bool _isCompleted;
  late bool _showExplanation;
  
  // Colors
  late Color _primaryColor;
  late Color _secondaryColor;
  late Color _accentColor;
  late Color _backgroundColor;
  late Color _textColor;

  @override
  void initState() {
    super.initState();
    _initializeWidget();
  }

  void _initializeWidget() {
    // Initialize colors
    _primaryColor = _parseColor(widget.data['primaryColor'] ?? '#2196F3');
    _secondaryColor = _parseColor(widget.data['secondaryColor'] ?? '#FF9800');
    _accentColor = _parseColor(widget.data['accentColor'] ?? '#4CAF50');
    _backgroundColor = _parseColor(widget.data['backgroundColor'] ?? '#FFFFFF');
    _textColor = _parseColor(widget.data['textColor'] ?? '#212121');

    // Initialize scenarios
    final List<dynamic> scenariosData = widget.data['scenarios'] ?? [];
    _scenarios = scenariosData.map((scenarioData) => ArgumentScenario.fromJson(scenarioData)).toList();
    _currentScenarioIndex = 0;
    
    // Initialize analysis state
    _resetAnalysis();
  }

  void _resetAnalysis() {
    if (_scenarios.isEmpty) return;
    
    ArgumentScenario scenario = _scenarios[_currentScenarioIndex];
    _criteriaRatings = {};
    _criteriaFeedback = {};
    
    for (var criterion in scenario.criteria) {
      _criteriaRatings[criterion.id] = 0.0;
      _criteriaFeedback[criterion.id] = '';
    }
    
    _overallScore = 0.0;
    _overallFeedback = '';
    _hasSubmitted = false;
    _isCompleted = false;
    _showExplanation = false;
  }

  Color _parseColor(String colorString) {
    if (colorString.startsWith('#')) {
      return Color(int.parse('FF${colorString.substring(1)}', radix: 16));
    }
    return Colors.blue;
  }

  void _updateCriterionRating(String criterionId, double value) {
    setState(() {
      _criteriaRatings[criterionId] = value;
    });
  }

  void _submitAnalysis() {
    if (_scenarios.isEmpty) return;
    
    ArgumentScenario scenario = _scenarios[_currentScenarioIndex];
    
    // Calculate overall score (weighted average)
    double totalScore = 0.0;
    int totalWeight = 0;
    
    for (var criterion in scenario.criteria) {
      double rating = _criteriaRatings[criterion.id] ?? 0.0;
      totalScore += rating * criterion.weight;
      totalWeight += criterion.weight;
      
      // Determine feedback for each criterion
      if (rating >= 4.0) {
        _criteriaFeedback[criterion.id] = criterion.highScoreFeedback;
      } else if (rating >= 2.0) {
        _criteriaFeedback[criterion.id] = criterion.mediumScoreFeedback;
      } else {
        _criteriaFeedback[criterion.id] = criterion.lowScoreFeedback;
      }
    }
    
    _overallScore = totalWeight > 0 ? totalScore / totalWeight : 0.0;
    
    // Determine overall feedback
    if (_overallScore >= 4.0) {
      _overallFeedback = scenario.highScoreFeedback;
    } else if (_overallScore >= 2.5) {
      _overallFeedback = scenario.mediumScoreFeedback;
    } else {
      _overallFeedback = scenario.lowScoreFeedback;
    }
    
    setState(() {
      _hasSubmitted = true;
    });
  }

  void _nextScenario() {
    if (_currentScenarioIndex < _scenarios.length - 1) {
      setState(() {
        _currentScenarioIndex++;
        _resetAnalysis();
      });
    } else if (!_isCompleted) {
      setState(() {
        _isCompleted = true;
      });
      widget.onStateChanged?.call(true);
    }
  }

  void _toggleExplanation() {
    setState(() {
      _showExplanation = !_showExplanation;
    });
  }

  @override
  Widget build(BuildContext context) {
    if (_scenarios.isEmpty) {
      return const Center(child: Text('No scenarios available'));
    }

    ArgumentScenario scenario = _scenarios[_currentScenarioIndex];

    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
        side: BorderSide(color: _primaryColor.withOpacity(0.5), width: 1),
      ),
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Title
            Text(
              widget.data['title'] ?? 'Argument Strength Analyzer',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
                color: _textColor,
              ),
            ),

            const SizedBox(height: 8),

            // Scenario navigation
            Row(
              children: [
                Text(
                  'Argument ${_currentScenarioIndex + 1} of ${_scenarios.length}',
                  style: TextStyle(
                    fontSize: 14,
                    fontWeight: FontWeight.bold,
                    color: _primaryColor,
                  ),
                ),
                const Spacer(),
                IconButton(
                  icon: Icon(_showExplanation ? Icons.info_outline : Icons.info),
                  onPressed: _hasSubmitted ? _toggleExplanation : null,
                  tooltip: _showExplanation ? 'Hide Explanation' : 'Show Explanation',
                  color: _secondaryColor,
                ),
              ],
            ),

            const SizedBox(height: 8),

            // Argument description
            Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: _backgroundColor,
                borderRadius: BorderRadius.circular(8),
                border: Border.all(color: _primaryColor.withOpacity(0.3)),
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'Claim: ${scenario.claim}',
                    style: TextStyle(
                      fontWeight: FontWeight.bold,
                      color: _textColor,
                    ),
                  ),
                  const SizedBox(height: 8),
                  Text(
                    'Argument:',
                    style: TextStyle(
                      fontWeight: FontWeight.bold,
                      color: _textColor,
                    ),
                  ),
                  const SizedBox(height: 4),
                  Text(
                    scenario.argument,
                    style: TextStyle(color: _textColor.withOpacity(0.8)),
                  ),
                ],
              ),
            ),

            const SizedBox(height: 16),

            // Instructions
            Text(
              'Rate the strength of this argument on the following criteria:',
              style: TextStyle(
                fontWeight: FontWeight.bold,
                color: _textColor,
              ),
            ),

            const SizedBox(height: 16),

            // Criteria sliders
            ...scenario.criteria.map((criterion) => _buildCriterionSlider(criterion)),

            const SizedBox(height: 16),

            // Submit button
            if (!_hasSubmitted)
              Center(
                child: ElevatedButton(
                  onPressed: _submitAnalysis,
                  style: ElevatedButton.styleFrom(
                    backgroundColor: _primaryColor,
                    foregroundColor: Colors.white,
                    padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(8),
                    ),
                  ),
                  child: const Text('Submit Analysis'),
                ),
              ),

            // Results
            if (_hasSubmitted) ...[
              // Overall score
              Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: _getScoreColor(_overallScore).withOpacity(0.1),
                  borderRadius: BorderRadius.circular(8),
                  border: Border.all(color: _getScoreColor(_overallScore)),
                ),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        Text(
                          'Overall Strength:',
                          style: TextStyle(
                            fontWeight: FontWeight.bold,
                            color: _textColor,
                          ),
                        ),
                        Text(
                          '${_overallScore.toStringAsFixed(1)}/5.0',
                          style: TextStyle(
                            fontWeight: FontWeight.bold,
                            color: _getScoreColor(_overallScore),
                          ),
                        ),
                      ],
                    ),
                    const SizedBox(height: 8),
                    Text(
                      'Feedback:',
                      style: TextStyle(
                        fontWeight: FontWeight.bold,
                        color: _textColor,
                      ),
                    ),
                    const SizedBox(height: 4),
                    Text(
                      _overallFeedback,
                      style: TextStyle(color: _textColor.withOpacity(0.8)),
                    ),
                  ],
                ),
              ),

              const SizedBox(height: 16),

              // Criteria feedback
              Text(
                'Criteria Feedback:',
                style: TextStyle(
                  fontWeight: FontWeight.bold,
                  color: _textColor,
                ),
              ),

              const SizedBox(height: 8),

              // Feedback for each criterion
              ...scenario.criteria.map((criterion) => _buildCriterionFeedback(criterion)),

              // Expert explanation (if shown)
              if (_showExplanation) ...[
                const SizedBox(height: 16),
                Container(
                  padding: const EdgeInsets.all(12),
                  decoration: BoxDecoration(
                    color: _secondaryColor.withOpacity(0.1),
                    borderRadius: BorderRadius.circular(8),
                    border: Border.all(color: _secondaryColor.withOpacity(0.3)),
                  ),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'Expert Analysis:',
                        style: TextStyle(
                          fontWeight: FontWeight.bold,
                          color: _secondaryColor,
                        ),
                      ),
                      const SizedBox(height: 4),
                      Text(
                        scenario.expertAnalysis,
                        style: TextStyle(color: _textColor.withOpacity(0.8)),
                      ),
                    ],
                  ),
                ),
              ],

              const SizedBox(height: 16),

              // Navigation buttons
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                children: [
                  OutlinedButton(
                    onPressed: () => setState(() => _resetAnalysis()),
                    style: OutlinedButton.styleFrom(
                      foregroundColor: _primaryColor,
                      side: BorderSide(color: _primaryColor),
                      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                    ),
                    child: const Text('Try Again'),
                  ),
                  ElevatedButton(
                    onPressed: _nextScenario,
                    style: ElevatedButton.styleFrom(
                      backgroundColor: _primaryColor,
                      foregroundColor: Colors.white,
                      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                    ),
                    child: Text(_currentScenarioIndex < _scenarios.length - 1
                        ? 'Next Argument'
                        : 'Finish'),
                  ),
                ],
              ),
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildCriterionSlider(ArgumentCriterion criterion) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 8.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Expanded(
                child: Text(
                  criterion.name,
                  style: TextStyle(
                    fontWeight: FontWeight.w500,
                    color: _textColor,
                  ),
                ),
              ),
              Text(
                '${(_criteriaRatings[criterion.id] ?? 0.0).toStringAsFixed(1)}/5.0',
                style: TextStyle(
                  fontWeight: FontWeight.bold,
                  color: _getScoreColor(_criteriaRatings[criterion.id] ?? 0.0),
                ),
              ),
            ],
          ),
          const SizedBox(height: 4),
          Text(
            criterion.description,
            style: TextStyle(
              fontSize: 12,
              color: _textColor.withOpacity(0.7),
            ),
          ),
          Slider(
            value: _criteriaRatings[criterion.id] ?? 0.0,
            min: 0.0,
            max: 5.0,
            divisions: 10,
            label: (_criteriaRatings[criterion.id] ?? 0.0).toStringAsFixed(1),
            onChanged: _hasSubmitted ? null : (value) => _updateCriterionRating(criterion.id, value),
            activeColor: _primaryColor,
            inactiveColor: _primaryColor.withOpacity(0.2),
          ),
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                'Weak',
                style: TextStyle(
                  fontSize: 10,
                  color: _textColor.withOpacity(0.6),
                ),
              ),
              Text(
                'Strong',
                style: TextStyle(
                  fontSize: 10,
                  color: _textColor.withOpacity(0.6),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildCriterionFeedback(ArgumentCriterion criterion) {
    final String feedback = _criteriaFeedback[criterion.id] ?? '';
    final double rating = _criteriaRatings[criterion.id] ?? 0.0;
    
    return Container(
      margin: const EdgeInsets.only(bottom: 8),
      padding: const EdgeInsets.all(8),
      decoration: BoxDecoration(
        color: _backgroundColor,
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: _getScoreColor(rating).withOpacity(0.5)),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                criterion.name,
                style: TextStyle(
                  fontWeight: FontWeight.bold,
                  color: _textColor,
                ),
              ),
              Text(
                '${rating.toStringAsFixed(1)}/5.0',
                style: TextStyle(
                  fontWeight: FontWeight.bold,
                  color: _getScoreColor(rating),
                ),
              ),
            ],
          ),
          const SizedBox(height: 4),
          Text(
            feedback,
            style: TextStyle(
              fontSize: 12,
              color: _textColor.withOpacity(0.8),
            ),
          ),
        ],
      ),
    );
  }

  Color _getScoreColor(double score) {
    if (score >= 4.0) {
      return Colors.green;
    } else if (score >= 2.5) {
      return Colors.orange;
    } else {
      return Colors.red;
    }
  }
}

/// Represents a scenario with an argument to analyze
class ArgumentScenario {
  final String id;
  final String claim;
  final String argument;
  final List<ArgumentCriterion> criteria;
  final String highScoreFeedback;
  final String mediumScoreFeedback;
  final String lowScoreFeedback;
  final String expertAnalysis;

  ArgumentScenario({
    required this.id,
    required this.claim,
    required this.argument,
    required this.criteria,
    required this.highScoreFeedback,
    required this.mediumScoreFeedback,
    required this.lowScoreFeedback,
    required this.expertAnalysis,
  });

  factory ArgumentScenario.fromJson(Map<String, dynamic> json) {
    final List<dynamic> criteriaData = json['criteria'] ?? [];
    final List<ArgumentCriterion> criteria = criteriaData
        .map((item) => ArgumentCriterion.fromJson(item))
        .toList();

    return ArgumentScenario(
      id: json['id'] as String,
      claim: json['claim'] as String,
      argument: json['argument'] as String,
      criteria: criteria,
      highScoreFeedback: json['highScoreFeedback'] as String,
      mediumScoreFeedback: json['mediumScoreFeedback'] as String,
      lowScoreFeedback: json['lowScoreFeedback'] as String,
      expertAnalysis: json['expertAnalysis'] as String,
    );
  }
}

/// Represents a criterion for evaluating an argument
class ArgumentCriterion {
  final String id;
  final String name;
  final String description;
  final int weight;
  final String highScoreFeedback;
  final String mediumScoreFeedback;
  final String lowScoreFeedback;

  ArgumentCriterion({
    required this.id,
    required this.name,
    required this.description,
    required this.weight,
    required this.highScoreFeedback,
    required this.mediumScoreFeedback,
    required this.lowScoreFeedback,
  });

  factory ArgumentCriterion.fromJson(Map<String, dynamic> json) {
    return ArgumentCriterion(
      id: json['id'] as String,
      name: json['name'] as String,
      description: json['description'] as String,
      weight: json['weight'] as int,
      highScoreFeedback: json['highScoreFeedback'] as String,
      mediumScoreFeedback: json['mediumScoreFeedback'] as String,
      lowScoreFeedback: json['lowScoreFeedback'] as String,
    );
  }
}
