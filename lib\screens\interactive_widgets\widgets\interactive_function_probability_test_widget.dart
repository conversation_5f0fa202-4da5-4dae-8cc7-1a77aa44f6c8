import 'package:flutter/material.dart';
import 'dart:math' as math;

/// A comprehensive test widget that combines function and probability concepts
class InteractiveFunctionProbabilityTestWidget extends StatefulWidget {
  /// The data for this widget
  final Map<String, dynamic> data;

  /// Optional callback when the widget state changes
  final Function(bool isCompleted)? onStateChanged;

  const InteractiveFunctionProbabilityTestWidget({
    Key? key,
    required this.data,
    this.onStateChanged,
  }) : super(key: key);

  /// Factory constructor to create the widget from JSON data
  factory InteractiveFunctionProbabilityTestWidget.fromData(Map<String, dynamic> data) {
    return InteractiveFunctionProbabilityTestWidget(
      data: data,
    );
  }

  @override
  _InteractiveFunctionProbabilityTestWidgetState createState() =>
      _InteractiveFunctionProbabilityTestWidgetState();
}

class _InteractiveFunctionProbabilityTestWidgetState extends State<InteractiveFunctionProbabilityTestWidget> with SingleTickerProviderStateMixin {
  // Questions and answers
  late List<Map<String, dynamic>> _questions;
  late List<int?> _userAnswers;
  int _currentQuestionIndex = 0;
  bool _showExplanation = false;
  bool _showResults = false;
  int _score = 0;
  late int _maxScore;
  bool _isCompleted = false;

  // Animation controller for transitions
  late AnimationController _animationController;
  late Animation<double> _animation;

  // Colors
  late Color _primaryColor;
  late Color _secondaryColor;
  late Color _accentColor;
  late Color _backgroundColor;
  late Color _textColor;

  @override
  void initState() {
    super.initState();

    // Initialize colors
    _primaryColor = widget.data['primaryColor'] != null
        ? Color(int.parse(widget.data['primaryColor'], radix: 16))
        : Colors.blue;
    _secondaryColor = widget.data['secondaryColor'] != null
        ? Color(int.parse(widget.data['secondaryColor'], radix: 16))
        : Colors.lightBlue;
    _accentColor = widget.data['accentColor'] != null
        ? Color(int.parse(widget.data['accentColor'], radix: 16))
        : Colors.orange;
    _backgroundColor = widget.data['backgroundColor'] != null
        ? Color(int.parse(widget.data['backgroundColor'], radix: 16))
        : Colors.white;
    _textColor = widget.data['textColor'] != null
        ? Color(int.parse(widget.data['textColor'], radix: 16))
        : Colors.black87;

    // Initialize questions
    _questions = widget.data['questions'] != null
        ? List<Map<String, dynamic>>.from(widget.data['questions'])
        : _getDefaultQuestions();

    _maxScore = _questions.length;
    _userAnswers = List<int?>.filled(_questions.length, null);

    // Initialize animation controller
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 300),
      vsync: this,
    );

    _animation = CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeInOut,
    );
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  // Get default questions if none provided
  List<Map<String, dynamic>> _getDefaultQuestions() {
    return [
      {
        'type': 'multiple_choice',
        'question': 'Which of the following represents a linear function?',
        'options': [
          'y = 2x + 3',
          'y = x²',
          'y = 2^x',
          'y = 1/x',
        ],
        'correctAnswer': 0,
        'explanation': 'A linear function has the form y = mx + b, where m is the slope and b is the y-intercept. The option y = 2x + 3 is a linear function with slope 2 and y-intercept 3.',
        'image': 'assets/images/function_test/linear_function.png',
      },
      {
        'type': 'multiple_choice',
        'question': 'What is the vertex of the quadratic function f(x) = 2(x - 3)² + 4?',
        'options': [
          '(3, 4)',
          '(3, 0)',
          '(0, 4)',
          '(4, 3)',
        ],
        'correctAnswer': 0,
        'explanation': 'When a quadratic function is in the form f(x) = a(x - h)² + k, the vertex is at (h, k). For f(x) = 2(x - 3)² + 4, the vertex is at (3, 4).',
        'image': 'assets/images/function_test/quadratic_vertex.png',
      },
      {
        'type': 'multiple_choice',
        'question': 'If f(x) = 2^x, what happens to the function value as x increases?',
        'options': [
          'It increases at an increasing rate',
          'It increases at a constant rate',
          'It increases at a decreasing rate',
          'It decreases at a decreasing rate',
        ],
        'correctAnswer': 0,
        'explanation': 'Exponential functions of the form f(x) = b^x where b > 1 (like 2^x) increase at an increasing rate as x increases. This means the growth becomes faster and faster.',
        'image': 'assets/images/function_test/exponential_growth.png',
      },
      {
        'type': 'multiple_choice',
        'question': 'What is the probability of rolling a sum of 7 with two fair dice?',
        'options': [
          '1/6',
          '1/12',
          '1/36',
          '1/3',
        ],
        'correctAnswer': 0,
        'explanation': 'When rolling two dice, there are 36 possible outcomes (6 × 6). The sum of 7 can be achieved in 6 ways: (1,6), (2,5), (3,4), (4,3), (5,2), (6,1). Therefore, the probability is 6/36 = 1/6.',
        'image': 'assets/images/function_test/dice_probability.png',
      },
      {
        'type': 'multiple_choice',
        'question': 'In a standard deck of 52 cards, what is the probability of drawing a face card (Jack, Queen, or King)?',
        'options': [
          '3/13',
          '1/4',
          '1/13',
          '3/26',
        ],
        'correctAnswer': 1,
        'explanation': 'There are 12 face cards in a standard deck (3 face cards in each of the 4 suits). Therefore, the probability is 12/52 = 3/13 = 1/4.',
        'image': 'assets/images/function_test/card_probability.png',
      },
      {
        'type': 'multiple_choice',
        'question': 'Which of the following is NOT a characteristic of a linear function?',
        'options': [
          'The graph is a parabola',
          'The rate of change is constant',
          'The graph is a straight line',
          'It can be written in the form f(x) = mx + b',
        ],
        'correctAnswer': 0,
        'explanation': 'A parabola is the graph of a quadratic function, not a linear function. Linear functions have a constant rate of change, their graphs are straight lines, and they can be written in the form f(x) = mx + b.',
        'image': 'assets/images/function_test/linear_vs_quadratic.png',
      },
      {
        'type': 'multiple_choice',
        'question': 'If you flip a fair coin 3 times, what is the probability of getting exactly 2 heads?',
        'options': [
          '3/8',
          '1/2',
          '1/4',
          '1/8',
        ],
        'correctAnswer': 0,
        'explanation': 'When flipping a coin 3 times, there are 2^3 = 8 possible outcomes. There are 3 ways to get exactly 2 heads: HHT, HTH, THH. Therefore, the probability is 3/8.',
        'image': 'assets/images/function_test/coin_probability.png',
      },
      {
        'type': 'multiple_choice',
        'question': 'Which function has a horizontal asymptote at y = 0?',
        'options': [
          'f(x) = 1/x',
          'f(x) = x²',
          'f(x) = x + 2',
          'f(x) = √x',
        ],
        'correctAnswer': 0,
        'explanation': 'The function f(x) = 1/x has a horizontal asymptote at y = 0 because as x approaches infinity (or negative infinity), 1/x approaches 0.',
        'image': 'assets/images/function_test/horizontal_asymptote.png',
      },
    ];
  }

  // Select an answer
  void _selectAnswer(int answerIndex) {
    setState(() {
      _userAnswers[_currentQuestionIndex] = answerIndex;
    });
  }

  // Move to the next question
  void _nextQuestion() {
    if (_currentQuestionIndex < _questions.length - 1) {
      // Start animation
      _animationController.forward().then((_) {
        setState(() {
          _currentQuestionIndex++;
          _showExplanation = false;
        });
        _animationController.reverse();
      });
    } else {
      // Show results if all questions have been answered
      _calculateScore();
      setState(() {
        _showResults = true;
      });
    }
  }

  // Move to the previous question
  void _previousQuestion() {
    if (_currentQuestionIndex > 0) {
      // Start animation
      _animationController.forward().then((_) {
        setState(() {
          _currentQuestionIndex--;
          _showExplanation = false;
        });
        _animationController.reverse();
      });
    }
  }

  // Calculate the score
  void _calculateScore() {
    _score = 0;
    for (int i = 0; i < _questions.length; i++) {
      if (_userAnswers[i] == _questions[i]['correctAnswer']) {
        _score++;
      }
    }

    // Mark as completed if score is at least 70%
    if (_score >= (_maxScore * 0.7).round()) {
      _isCompleted = true;
      widget.onStateChanged?.call(true);
    }
  }

  // Restart the test
  void _restartTest() {
    setState(() {
      _currentQuestionIndex = 0;
      _userAnswers.clear();
      _userAnswers.addAll(List<int?>.filled(_questions.length, null));
      _showResults = false;
      _isCompleted = false;
      _score = 0;
      _showExplanation = false;
    });
  }

  // Toggle explanation visibility
  void _toggleExplanation() {
    setState(() {
      _showExplanation = !_showExplanation;
    });
  }

  @override
  Widget build(BuildContext context) {
    return Card(
      elevation: 4,
      margin: const EdgeInsets.all(8),
      color: _backgroundColor,
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: _showResults ? _buildResultsScreen() : _buildQuestionScreen(),
      ),
    );
  }

  // Build option button
  Widget _buildOptionButton(int index, String optionText) {
    final isSelected = _userAnswers[_currentQuestionIndex] == index;
    final isCorrect = _questions[_currentQuestionIndex]['correctAnswer'] == index;
    final hasAnswered = _userAnswers[_currentQuestionIndex] != null;

    return Padding(
      padding: const EdgeInsets.only(bottom: 8),
      child: InkWell(
        onTap: hasAnswered ? null : () => _selectAnswer(index),
        borderRadius: BorderRadius.circular(8),
        child: Container(
          padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
          decoration: BoxDecoration(
            color: isSelected
                ? (hasAnswered && _showExplanation
                    ? (isCorrect ? Colors.green.withOpacity(0.2) : Colors.red.withOpacity(0.2))
                    : _primaryColor.withOpacity(0.1))
                : Colors.grey.withOpacity(0.05),
            borderRadius: BorderRadius.circular(8),
            border: Border.all(
              color: isSelected
                  ? (hasAnswered && _showExplanation
                      ? (isCorrect ? Colors.green : Colors.red)
                      : _primaryColor)
                  : Colors.grey.withOpacity(0.3),
              width: isSelected ? 2 : 1,
            ),
          ),
          child: Row(
            children: [
              Container(
                width: 24,
                height: 24,
                decoration: BoxDecoration(
                  shape: BoxShape.circle,
                  color: isSelected
                      ? (hasAnswered && _showExplanation
                          ? (isCorrect ? Colors.green : Colors.red)
                          : _primaryColor)
                      : Colors.white,
                  border: Border.all(
                    color: isSelected
                        ? (hasAnswered && _showExplanation
                            ? (isCorrect ? Colors.green : Colors.red)
                            : _primaryColor)
                        : Colors.grey,
                    width: 1,
                  ),
                ),
                child: isSelected
                    ? Icon(
                        hasAnswered && _showExplanation
                            ? (isCorrect ? Icons.check : Icons.close)
                            : Icons.check,
                        size: 16,
                        color: Colors.white,
                      )
                    : null,
              ),
              const SizedBox(width: 12),
              Expanded(
                child: Text(
                  optionText,
                  style: TextStyle(
                    fontSize: 14,
                    color: _textColor,
                    fontWeight: isSelected ? FontWeight.bold : FontWeight.normal,
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  // Build the question screen
  Widget _buildQuestionScreen() {
    final question = _questions[_currentQuestionIndex];

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Title
        Text(
          widget.data['title'] ?? 'Function and Probability Test',
          style: TextStyle(
            fontSize: 18,
            fontWeight: FontWeight.bold,
            color: _textColor,
          ),
        ),

        const SizedBox(height: 16),

        // Progress indicator
        LinearProgressIndicator(
          value: (_currentQuestionIndex + 1) / _questions.length,
          backgroundColor: _secondaryColor.withOpacity(0.3),
          valueColor: AlwaysStoppedAnimation<Color>(_primaryColor),
        ),

        const SizedBox(height: 8),

        // Question number
        Text(
          'Question ${_currentQuestionIndex + 1} of ${_questions.length}',
          style: TextStyle(
            fontSize: 14,
            color: _textColor.withOpacity(0.7),
          ),
        ),

        const SizedBox(height: 16),

        // Question text
        Text(
          question['question'],
          style: TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.bold,
            color: _textColor,
          ),
        ),

        const SizedBox(height: 16),

        // Question image if available
        if (question['image'] != null)
          Container(
            height: 150,
            width: double.infinity,
            decoration: BoxDecoration(
              color: Colors.grey[200],
              borderRadius: BorderRadius.circular(8),
            ),
            child: Center(
              child: Text(
                'Image: ${question['image']}',
                style: TextStyle(
                  color: _textColor.withOpacity(0.7),
                ),
              ),
            ),
          ),

        if (question['image'] != null)
          const SizedBox(height: 16),

        // Options
        ...List.generate(
          question['options'].length,
          (index) => _buildOptionButton(index, question['options'][index]),
        ),

        const SizedBox(height: 16),

        // Explanation (if shown)
        if (_showExplanation)
          Container(
            padding: const EdgeInsets.all(12),
            decoration: BoxDecoration(
              color: _secondaryColor.withOpacity(0.1),
              borderRadius: BorderRadius.circular(8),
              border: Border.all(color: _secondaryColor.withOpacity(0.3)),
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'Explanation:',
                  style: TextStyle(
                    fontSize: 14,
                    fontWeight: FontWeight.bold,
                    color: _textColor,
                  ),
                ),
                const SizedBox(height: 4),
                Text(
                  question['explanation'],
                  style: TextStyle(
                    fontSize: 14,
                    color: _textColor.withOpacity(0.8),
                  ),
                ),
              ],
            ),
          ),

        if (_showExplanation)
          const SizedBox(height: 16),

        // Navigation buttons
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            // Previous button
            ElevatedButton(
              onPressed: _currentQuestionIndex > 0 ? _previousQuestion : null,
              style: ElevatedButton.styleFrom(
                backgroundColor: _secondaryColor,
                foregroundColor: Colors.white,
              ),
              child: const Text('Previous'),
            ),

            // Explanation button
            OutlinedButton(
              onPressed: _userAnswers[_currentQuestionIndex] != null ? _toggleExplanation : null,
              style: OutlinedButton.styleFrom(
                foregroundColor: _primaryColor,
              ),
              child: Text(_showExplanation ? 'Hide Explanation' : 'Show Explanation'),
            ),

            // Next button
            ElevatedButton(
              onPressed: _userAnswers[_currentQuestionIndex] != null ? _nextQuestion : null,
              style: ElevatedButton.styleFrom(
                backgroundColor: _primaryColor,
                foregroundColor: Colors.white,
              ),
              child: Text(_currentQuestionIndex < _questions.length - 1 ? 'Next' : 'Finish'),
            ),
          ],
        ),
      ],
    );
  }

  // Build a summary point for the results screen
  Widget _buildSummaryPoint(String category, String score) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 8),
      child: Row(
        children: [
          Icon(
            Icons.check_circle,
            color: _primaryColor,
            size: 18,
          ),
          const SizedBox(width: 8),
          Expanded(
            child: Text(
              category,
              style: TextStyle(
                fontSize: 14,
                color: _textColor,
              ),
            ),
          ),
          Text(
            score,
            style: TextStyle(
              fontSize: 14,
              fontWeight: FontWeight.bold,
              color: _primaryColor,
            ),
          ),
        ],
      ),
    );
  }

  // Calculate category score based on question type
  String _calculateCategoryScore(String category) {
    int total = 0;
    int correct = 0;

    for (int i = 0; i < _questions.length; i++) {
      final question = _questions[i];

      // Check if question belongs to this category
      bool isInCategory = false;
      if (category == 'function' &&
          (question['question'].toString().contains('function') ||
           question['question'].toString().contains('linear') ||
           question['question'].toString().contains('quadratic') ||
           question['question'].toString().contains('exponential'))) {
        isInCategory = true;
      } else if (category == 'probability' &&
                (question['question'].toString().contains('probability') ||
                 question['question'].toString().contains('dice') ||
                 question['question'].toString().contains('coin') ||
                 question['question'].toString().contains('card'))) {
        isInCategory = true;
      } else if (category == 'problem') {
        // All questions involve problem solving
        isInCategory = true;
      }

      if (isInCategory) {
        total++;
        if (_userAnswers[i] == question['correctAnswer']) {
          correct++;
        }
      }
    }

    if (total == 0) return 'N/A';
    return '$correct/$total';
  }

  // Build the results screen
  Widget _buildResultsScreen() {
    // Calculate percentage score
    final percentage = (_score / _maxScore) * 100;
    final isPassing = percentage >= 70;

    return Column(
      crossAxisAlignment: CrossAxisAlignment.center,
      children: [
        // Title
        Text(
          widget.data['title'] ?? 'Function and Probability Test',
          style: TextStyle(
            fontSize: 18,
            fontWeight: FontWeight.bold,
            color: _textColor,
          ),
        ),

        const SizedBox(height: 24),

        // Score display
        Container(
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            color: isPassing ? Colors.green.withOpacity(0.1) : Colors.red.withOpacity(0.1),
            borderRadius: BorderRadius.circular(8),
            border: Border.all(
              color: isPassing ? Colors.green.withOpacity(0.3) : Colors.red.withOpacity(0.3),
            ),
          ),
          child: Column(
            children: [
              Text(
                isPassing ? 'Congratulations!' : 'Keep Practicing!',
                style: TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                  color: isPassing ? Colors.green : Colors.red,
                ),
              ),

              const SizedBox(height: 8),

              Text(
                'Your Score:',
                style: TextStyle(
                  fontSize: 16,
                  color: _textColor,
                ),
              ),

              const SizedBox(height: 4),

              Text(
                '$_score / $_maxScore (${percentage.toStringAsFixed(0)}%)',
                style: TextStyle(
                  fontSize: 24,
                  fontWeight: FontWeight.bold,
                  color: isPassing ? Colors.green : Colors.red,
                ),
              ),

              const SizedBox(height: 16),

              Text(
                isPassing
                    ? 'You have a good understanding of functions and probability!'
                    : 'Review the concepts and try again to improve your score.',
                textAlign: TextAlign.center,
                style: TextStyle(
                  fontSize: 14,
                  color: _textColor.withOpacity(0.8),
                ),
              ),
            ],
          ),
        ),

        const SizedBox(height: 24),

        // Performance summary
        Text(
          'Performance Summary:',
          style: TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.bold,
            color: _textColor,
          ),
        ),

        const SizedBox(height: 8),

        // Summary points
        _buildSummaryPoint('Function Concepts', _calculateCategoryScore('function')),
        _buildSummaryPoint('Probability Concepts', _calculateCategoryScore('probability')),
        _buildSummaryPoint('Problem Solving', _calculateCategoryScore('problem')),

        const SizedBox(height: 24),

        // Action buttons
        Row(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            // Restart button
            ElevatedButton(
              onPressed: _restartTest,
              style: ElevatedButton.styleFrom(
                backgroundColor: _secondaryColor,
                foregroundColor: Colors.white,
              ),
              child: const Text('Restart Test'),
            ),

            const SizedBox(width: 16),

            // Complete button (only if passing)
            if (isPassing)
              ElevatedButton(
                onPressed: () {
                  setState(() {
                    _isCompleted = true;
                  });
                  widget.onStateChanged?.call(true);
                },
                style: ElevatedButton.styleFrom(
                  backgroundColor: _primaryColor,
                  foregroundColor: Colors.white,
                ),
                child: const Text('Complete'),
              ),
          ],
        ),
      ],
    );
  }
}
