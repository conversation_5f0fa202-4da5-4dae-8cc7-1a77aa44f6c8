{"id": "tackling-two-step-equations", "title": "Tackling Two-Step Equations", "description": "Extend your solving skills to equations requiring multiple operations.", "order": 3, "lessons": [{"id": "ttse-l1-order-of-operations-reverse", "title": "Two Steps to Success: Order of Operations in Reverse", "description": "Master solving two-step equations by strategically 'undoing' operations in the reverse order of PEMDAS/BODMAS.", "order": 1, "type": "interactive_lesson", "estimatedTimeMinutes": 9, "contentBlocks": [{"id": "ttse-l1-s1", "type": "lesson_screen", "order": 1, "estimatedTimeSeconds": 60, "content": {"headline": "Beyond One Step", "body_md": "You're now a pro at one-step equations! Ready for a new challenge? **Two-step equations** involve, you guessed it, two operations. For example, `2x + 3 = 11`. Here, 'x' is first multiplied by 2, and then 3 is added to the result.\n\nTo solve these, we undo operations in the **reverse order** of PEMDAS/BODMAS – think **SADMEP** (Subtraction/Addition, Division/Multiplication, Exponents, Parentheses).", "visual": {"type": "giphy_search", "value": "level up challenge"}, "hook": "You've mastered one step, now let's double the fun (and the steps)!", "interactive_element": {"type": "button", "text": "SADMEP? Tell me more!", "action": "next_screen"}}}, {"id": "ttse-l1-s2", "type": "lesson_screen", "order": 2, "estimatedTimeSeconds": 90, "content": {"headline": "Solving 2x + 3 = 11", "body_md": "Let's break down `2x + 3 = 11` using SADMEP:\n\n1.  **Undo Addition/Subtraction first:** The `+ 3` is undone by subtracting 3 from both sides:\n    `2x + 3 - 3 = 11 - 3`\n    `2x = 8`\n\n2.  **Undo Multiplication/Division next:** Now `2x = 8`. Undo multiplication by 2 by dividing both sides by 2:\n    `2x / 2 = 8 / 2`\n    `x = 4`\n\nSolution: `x = 4`. Check: `2(4) + 3 = 8 + 3 = 11`. Perfect!", "visual": {"type": "static_text", "value": "2x + 3 = 11  =>  2x = 8  =>  x = 4"}, "hook": "It's like a two-part puzzle. Solve one, then the other.", "interactive_element": {"type": "button", "text": "Let's try one with guidance!", "action": "next_screen"}}}, {"id": "ttse-l1-s3", "type": "lesson_screen", "order": 3, "estimatedTimeSeconds": 180, "content": {"headline": "Guided: Solve 3y - 4 = 14", "body_md": "Let's solve `3y - 4 = 14` using reverse order of operations (SADMEP).", "visual": {"type": "static_text", "value": "Interactive: Solve 3y - 4 = 14"}, "interactive_element": {"type": "guided_proof_steps", "proof_title": "Solving `3y - 4 = 14` Step-by-Step", "introduction_text": "Equation: `3y - 4 = 14`.", "steps": [{"id": "ttse_s1_ask_add_sub", "prompt_text": "According to SADMEP, what operation undoes the '- 4' first?", "options": [{"id": "opt1", "text": "Add 4 to both sides", "is_correct": true, "feedback": "Correct! Adding 4 is the inverse of subtracting 4."}, {"id": "opt2", "text": "Subtract 4 from both sides", "is_correct": false, "feedback": "Not quite. We want to cancel out the '- 4'."}, {"id": "opt3", "text": "Divide by 3 on both sides", "is_correct": false, "feedback": "Hold on! Address multiplication/division *after* addition/subtraction."}]}, {"id": "ttse_s2_show_add_sub_result", "prompt_text": "Excellent! After adding 4 to both sides: `3y - 4 + 4 = 14 + 4`, which simplifies to `3y = 18`.", "depends_on_correct": "ttse_s1_ask_add_sub"}, {"id": "ttse_s3_ask_mul_div", "prompt_text": "Now we have `3y = 18`. What's the next operation to isolate 'y'?", "options": [{"id": "optA", "text": "Divide both sides by 3", "is_correct": true, "feedback": "Exactly! Dividing by 3 will undo the multiplication."}, {"id": "optB", "text": "Multiply both sides by 3", "is_correct": false, "feedback": "That would give '9y', not isolate 'y'."}], "depends_on_correct": "ttse_s2_show_add_sub_result"}, {"id": "ttse_s4_show_mul_div_result", "prompt_text": "Perfect! After dividing both sides by 3: `3y / 3 = 18 / 3`, which simplifies to `y = 6`.", "depends_on_correct": "ttse_s3_ask_mul_div"}], "conclusion_text_on_complete": "Well done! `y = 6`. Check: `3(6) - 4 = 18 - 4 = 14`. It works!", "action_button_text": "What if things get messy with like terms?"}, "hook": "Follow these steps, and you'll be a two-step master!"}}]}, {"id": "ttse-l2-combining-like-terms-before-solving", "title": "Clean Up Crew: Combining Like Terms First", "description": "Learn to simplify equations by combining like terms on one or both sides *before* applying inverse operations to solve.", "order": 2, "type": "interactive_lesson", "estimatedTimeMinutes": 9, "contentBlocks": [{"id": "ttse-l2-s1", "type": "lesson_screen", "order": 1, "estimatedTimeSeconds": 60, "content": {"headline": "Cluttered Equations", "body_md": "Sometimes, two-step equations might look a bit more cluttered, like `5a + 2a - 3 = 11`.\n\nBefore you jump into undoing operations (SADMEP), it's often essential to **simplify each side of the equation first by combining like terms**.", "visual": {"type": "giphy_search", "value": "messy room clean up"}, "hook": "Sometimes equations need a little tidying before we can solve them.", "interactive_element": {"type": "button", "text": "How do we clean this up?", "action": "next_screen"}}}, {"id": "ttse-l2-s2", "type": "lesson_screen", "order": 2, "estimatedTimeSeconds": 90, "content": {"headline": "Simplifying 5a + 2a - 3 = 11", "body_md": "1.  **Simplify (Combine Like Terms):** On the left side of `5a + 2a - 3 = 11`, `5a` and `+2a` are like terms. Combine them: `5a + 2a = 7a`.\n    The equation now becomes: `7a - 3 = 11`.\n\n2.  **Solve the Two-Step Equation:** Now it's familiar!\n    -   Add 3 to both sides: `7a - 3 + 3 = 11 + 3`  =>  `7a = 14`.\n    -   Divide by 7: `7a / 7 = 14 / 7`  =>  `a = 2`.\n\nSo, `a = 2`. Check: `5(2) + 2(2) - 3 = 10 + 4 - 3 = 11`. Correct!", "visual": {"type": "static_text", "value": "5a + 2a - 3 = 11  =>  7a - 3 = 11  =>  a = 2"}, "hook": "See? Much neater and easier to handle!", "interactive_element": {"type": "button", "text": "Let me try simplifying then solving!", "action": "next_screen"}}}, {"id": "ttse-l2-s3", "type": "lesson_screen", "order": 3, "estimatedTimeSeconds": 150, "content": {"headline": "Interactive: Simplify Then Solve", "body_md": "Let's practice with `4k - k + 5 = 20`.", "visual": {"type": "static_text", "value": "Interactive: Simplify and Solve 4k - k + 5 = 20"}, "interactive_element": {"type": "interactive_simplify_then_solve", "equation_string_unsimplified": "4k - k + 5 = 20", "introduction_text": "Equation: `4k - k + 5 = 20`.", "prompt_simplify": "First, combine like terms on the left side. Write the new, simplified equation.", "simplified_equation_placeholder": "e.g., 3k + 5 = 20", "correct_simplified_form_regex": "^\\s*3k\\s*\\+\\s*5\\s*=\\s*20\\s*$|^\\s*5\\s*\\+\\s*3k\\s*=\\s*20\\s*$", "feedback_correct_simplify": "Great! `3k + 5 = 20` is correct.", "feedback_incorrect_simplify": "Remember `-k` is `-1k`. Combine `4k - 1k`.", "prompt_solve_simplified": "Now solve `3k + 5 = 20` for 'k'.", "solution_input_placeholder": "Enter value of k", "correct_solution_value": 5, "solution_steps_for_display": ["Simplified: `3k + 5 = 20`", "1. Subtract 5: `3k = 15`", "2. <PERSON><PERSON> by 3: `k = 5`"], "feedback_correct_solve": "Excellent! `k = 5`. (Check: 4(5) - 5 + 5 = 20)", "feedback_incorrect_solve": "From `3k + 5 = 20`, subtract 5, then divide by 3.", "action_button_text_simplify": "Check Simplified Equation", "action_button_text_solve": "Why is this 'cleanup' step so useful?"}, "hook": "Your turn to be the equation cleanup crew!"}}, {"id": "ttse-l2-s4", "type": "lesson_screen", "order": 4, "estimatedTimeSeconds": 40, "content": {"headline": "Clean Up First!", "body_md": "Always scan both sides of an equation for like terms to combine before applying inverse operations. This 'cleanup' step makes solving much smoother.", "visual": {"type": "unsplash_search", "value": "organizing tools"}, "hook": "A little organization goes a long way in algebra.", "interactive_element": {"type": "button", "text": "How does this apply to visual problems?", "action": "next_lesson"}}}]}, {"id": "ttse-l3-visualizing-the-steps", "title": "Visualizing Two-Step Solutions: The Balance Act", "description": "Reinforce understanding of the two-step solving process by visualizing each operation on a balance scale.", "order": 3, "type": "interactive_lesson", "estimatedTimeMinutes": 8, "contentBlocks": [{"id": "ttse-l3-s1", "type": "lesson_screen", "order": 1, "estimatedTimeSeconds": 90, "content": {"headline": "Visualizing (p/3) + 2 = 7", "body_md": "Consider `(p/3) + 2 = 7`.\n\n**Step 1: Undo Addition.** Remove 2 from both sides.\n`p/3 = 5`\n\n**Step 2: Undo Division.** Multiply both sides by 3.\n`p = 15`\n\nCheck: `(15/3) + 2 = 5 + 2 = 7`. Works!", "visual": {"type": "local_asset", "value": "assets/images/algebra/balance_scale_p_over_3_plus_2_equals_7_combined.svg", "alt_text": "Balance scale showing (p/3) + 2 = 7, then p/3 = 5, then p = 15."}, "hook": "Seeing is believing, especially with two-step equations!", "interactive_element": {"type": "button", "text": "Let's visualize another one!", "action": "next_screen"}}}, {"id": "ttse-l3-s2", "type": "lesson_screen", "order": 2, "estimatedTimeSeconds": 180, "content": {"headline": "Guided: Visualizing 2m + 1 = 9", "body_md": "Let's walk through solving `2m + 1 = 9` using the balance scale idea. Imagine 'm' is a mystery box.", "visual": {"type": "static_text", "value": "Interactive: Balance Scale for 2m + 1 = 9"}, "interactive_element": {"type": "guided_proof_steps", "proof_title": "Visualizing `2m + 1 = 9` with a Balance Scale", "introduction_text": "Initial: Left pan has `2m + 1`. Right pan has `9`.", "steps": [{"id": "ttse_l3_s1_initial_ask", "prompt_text": "What's our first goal in isolating 'm'?", "options": [{"id": "optA", "text": "Get '2m' by itself.", "is_correct": true, "feedback": "Exactly! Isolate the term with 'm'."}, {"id": "optB", "text": "Find 'm' immediately.", "is_correct": false, "feedback": "Ultimate goal, but undo operations one by one."}]}, {"id": "ttse_l3_s2_ask_step1_op", "prompt_text": "To get '2m' by itself (from `2m + 1`), what operation do we do to both sides?", "options": [{"id": "opt1", "text": "Remove one '1' unit from both sides.", "is_correct": true, "feedback": "Correct! This undoes the '+ 1'."}, {"id": "opt2", "text": "Add one '1' unit to both sides.", "is_correct": false, "feedback": "That would make it `2m + 2`."}], "depends_on_correct": "ttse_l3_s1_initial_ask"}, {"id": "ttse_l3_s3_show_step1_res", "prompt_text": "After removing one '1' from both sides: Left: `2m`. Right: `8`. Equation: `2m = 8`.", "depends_on_correct": "ttse_l3_s2_ask_step1_op"}, {"id": "ttse_l3_s4_ask_step2_op", "prompt_text": "Now `2m = 8`. To find 'm', what do we do to both sides?", "options": [{"id": "optX", "text": "Divide both sides by 2.", "is_correct": true, "feedback": "Exactly! This finds the weight of one 'm'."}, {"id": "optY", "text": "Multiply both sides by 2.", "is_correct": false, "feedback": "That would give `4m`."}], "depends_on_correct": "ttse_l3_s3_show_step1_res"}, {"id": "ttse_l3_s5_show_step2_res", "prompt_text": "After dividing by 2: Left: `m`. Right: `4`. Equation: `m = 4`.", "depends_on_correct": "ttse_l3_s4_ask_step2_op"}], "conclusion_text_on_complete": "Perfect! `m = 4`. Visualizing helps see *why* we reverse operations.", "action_button_text": "What about equations with fractions?"}, "hook": "Watch how each step keeps the balance!"}}]}, {"id": "ttse-l4-equations-with-fractions", "title": "Fraction Action: Solving Two-Step Equations with Fractions", "description": "Confidently apply the principles of solving two-step equations to those involving fractions, including multiplying by reciprocals.", "order": 4, "type": "interactive_lesson", "estimatedTimeMinutes": 10, "contentBlocks": [{"id": "ttse-l4-s1", "type": "lesson_screen", "order": 1, "estimatedTimeSeconds": 90, "content": {"headline": "Two-Steps with Fractions", "body_md": "The same SADMEP principles apply with fractions!\n\n**Scenario 1: `(x/4) - 1 = 2`**\n1.  Add 1 to both sides: `x/4 = 3`\n2.  Multiply by 4: `x = 12`\n\n**Scenario 2: `(2/3)y = 6`**\n1.  Multiply by reciprocal (3/2): `(3/2)*(2/3)y = 6*(3/2)` => `y = 18/2 = 9`\n\nRemember, dividing by a fraction is multiplying by its reciprocal.", "visual": {"type": "giphy_search", "value": "fractions math"}, "hook": "Don't let fractions scare you; the same rules apply!", "interactive_element": {"type": "button", "text": "Let's walk through a fraction problem!", "action": "next_screen"}}}, {"id": "ttse-l4-s2", "type": "lesson_screen", "order": 2, "estimatedTimeSeconds": 180, "content": {"headline": "Guided: <PERSON><PERSON> (3/5)z + 2 = 8", "body_md": "Let's tackle `(3/5)z + 2 = 8`.", "visual": {"type": "static_text", "value": "Interactive: <PERSON>ve (3/5)z + 2 = 8"}, "interactive_element": {"type": "guided_proof_steps", "proof_title": "Solving `(3/5)z + 2 = 8` (Fraction Edition!)", "introduction_text": "Equation: `(3/5)z + 2 = 8`.", "steps": [{"id": "ttse_l4_s1_ask_step1_op", "prompt_text": "First operation to undo the '+ 2'?", "options": [{"id": "optA", "text": "Subtract 2 from both sides.", "is_correct": true, "feedback": "Correct!"}, {"id": "optB", "text": "Multiply by 5/3.", "is_correct": false, "feedback": "Not yet, handle addition/subtraction first."}]}, {"id": "ttse_l4_s2_show_step1_res", "prompt_text": "After subtracting 2: `(3/5)z = 6`.", "depends_on_correct": "ttse_l4_s1_ask_step1_op"}, {"id": "ttse_l4_s3_ask_step2_op", "prompt_text": "Now `(3/5)z = 6`. How to isolate 'z'?", "options": [{"id": "opX", "text": "Multiply by reciprocal 5/3.", "is_correct": true, "feedback": "Exactly!"}, {"id": "opY", "text": "Divide by 3/5.", "is_correct": true, "feedback": "Correct! Same as multiplying by 5/3."}], "depends_on_correct": "ttse_l4_s2_show_step1_res"}, {"id": "ttse_l4_s4_show_step2_res", "prompt_text": "Multiplying by 5/3: `(5/3)*(3/5)z = 6*(5/3)` => `z = 30/3`.", "depends_on_correct": "ttse_l4_s3_ask_step2_op"}, {"id": "ttse_l4_s5_final_sol", "prompt_text": "Simplify `30/3`. Value of 'z'?", "user_input_details": {"type": "number_input", "placeholder": "Enter z", "correct_answer_value": 10}, "depends_on_correct": "ttse_l4_s4_show_step2_res"}], "conclusion_text_on_complete": "Fantastic! `z = 10`. Check: `(3/5)(10) + 2 = 6 + 2 = 8`. True!", "action_button_text": "How can we use this in the real world?"}, "hook": "Fractions are just numbers, too. Let's solve this together."}}]}, {"id": "ttse-l5-real-world-equations", "title": "Algebra in Action: Two-Step Equations in the Real World", "description": "Learn to translate everyday scenarios and word problems into two-step algebraic equations and then solve them.", "order": 5, "type": "interactive_lesson", "estimatedTimeMinutes": 10, "contentBlocks": [{"id": "ttse-l5-s1", "type": "lesson_screen", "order": 1, "estimatedTimeSeconds": 90, "content": {"headline": "Real-World Problem Solving", "body_md": "Algebra is all around us! Many real-world problems can be modeled using two-step equations.\n\n**Steps:**\n1.  **Understand** the Problem.\n2.  **Define a Variable** for the unknown.\n3.  **Write the Equation** translating words to math.\n4.  **Solve** the Equation (SADMEP).\n5.  **Check** Your Answer in context.\n\n*Example:* Magazine ($5) + 3 candy bars = $11 total. Cost per candy bar (c)?\nEquation: `3c + 5 = 11`. Solve: `3c = 6` => `c = 2`. Each candy bar is $2.", "visual": {"type": "giphy_search", "value": "real world application"}, "hook": "Time to see how algebra helps us understand the world around us.", "interactive_element": {"type": "button", "text": "Let's solve a real problem!", "action": "next_screen"}}}, {"id": "ttse-l5-s2", "type": "lesson_screen", "order": 2, "estimatedTimeSeconds": 180, "content": {"headline": "Interactive Word Problem: <PERSON><PERSON>", "body_md": "A taxi charges a **$3.00 flat fee** plus **$2.00 per mile**. Total ride cost: **$15.00**. How many miles (m) was the ride?", "visual": {"type": "static_text", "value": "Interactive: Solve the Taxi Fare Problem"}, "interactive_element": {"type": "interactive_word_problem_solver", "problem_statement_md": "Taxi: $3 flat fee + $2/mile. Total: $15. Miles (m)?", "variable_suggestion_prompt": "Let 'm' be miles.", "prompt_equation": "Write the equation for this problem using 'm'.", "equation_placeholder": "e.g., 2m + 3 = 15", "correct_equation_regex": "^\\s*2m\\s*\\+\\s*3\\s*=\\s*15\\s*$|^\\s*3\\s*\\+\\s*2m\\s*=\\s*15\\s*$", "feedback_correct_equation": "Great! `2m + 3 = 15` models this.", "feedback_incorrect_equation": "Total cost ($15) = flat fee ($3) + (cost per mile ($2) * miles (m)).", "prompt_solve": "Now, solve your equation for 'm'.", "solution_input_placeholder": "Enter miles", "correct_solution_value": 6, "solution_unit": "miles", "solution_steps_for_display": ["Eq: `2m + 3 = 15`", "1. Subtract 3: `2m = 12`", "2. <PERSON><PERSON> by 2: `m = 6`"], "feedback_correct_solution": "Excellent! 6 miles. (Check: 2*6 + 3 = 15)", "feedback_incorrect_solution": "From `2m + 3 = 15`, subtract 3, then divide by 2.", "action_button_text_equation": "Check Equation", "action_button_text_solve": "Why is this useful?"}, "hook": "Put on your problem-solver hat!"}}, {"id": "ttse-l5-s3", "type": "lesson_screen", "order": 3, "estimatedTimeSeconds": 40, "content": {"headline": "Algebra is Everywhere!", "body_md": "Being able to translate real-world situations into mathematical equations is a powerful skill that opens up problem-solving in many areas of life, from budgeting to science!", "visual": {"type": "unsplash_search", "value": "city life everyday problem solving"}, "hook": "You're now using algebra like a pro to model and solve real situations!", "interactive_element": {"type": "button", "text": "Ready for the Two-Step Challenge Test!", "action": "next_lesson"}}}]}], "moduleTest": {"id": "ttse-mt1-two-step-challenge", "title": "Module Test: Two-Step Challenge", "description": "Solve more complex equations involving two operations.", "order": 6, "type": "module_test_interactive", "estimatedTimeMinutes": 7, "contentBlocks": [{"id": "ttse-mt1-s0-intro", "type": "test_screen_intro", "order": 1, "estimatedTimeSeconds": 20, "content": {"headline": "Module Test: Two-Step Challenge", "body_md": "Test your skills with two-step equations!", "visual": {"type": "giphy_search", "value": "math quiz test"}, "interactive_element": {"type": "button", "text": "Begin!", "action": "next_screen"}}}, {"id": "ttse-mt1-s1-q1", "type": "test_screen_intro", "order": 2, "estimatedTimeSeconds": 70, "content": {"headline": "Question 1", "body_md": "Solve for x: `4x - 7 = 13`", "interactive_element": {"type": "text_input", "placeholder": "Enter value of x", "correct_answer_regex": "^5$", "feedback_correct": "Correct! x = 5.", "feedback_incorrect": "1. Add 7 to both sides. 2. <PERSON><PERSON> by 4.", "action_button_text": "Next Question"}}}, {"id": "ttse-mt1-s2-q2", "type": "test_screen_intro", "order": 3, "estimatedTimeSeconds": 70, "content": {"headline": "Question 2", "body_md": "Solve for y: `(y/3) + 5 = 9`", "interactive_element": {"type": "text_input", "placeholder": "Enter value of y", "correct_answer_regex": "^12$", "feedback_correct": "Correct! y = 12.", "feedback_incorrect": "1. Subtract 5 from both sides. 2. Multiply by 3.", "action_button_text": "Next Question"}}}, {"id": "ttse-mt1-s3-q3", "type": "test_screen_intro", "order": 4, "estimatedTimeSeconds": 70, "content": {"headline": "Question 3", "body_md": "Solve for a: `10 - 2a = 4`", "interactive_element": {"type": "text_input", "placeholder": "Enter value of a", "correct_answer_regex": "^3$", "feedback_correct": "Correct! a = 3.", "feedback_incorrect": "1. Subtract 10 from both sides (gives -2a = -6). 2. Divide by -2.", "action_button_text": "Next Question"}}}, {"id": "ttse-mt1-s4-q4", "type": "test_screen_intro", "order": 5, "estimatedTimeSeconds": 90, "content": {"headline": "Question 4: Word Problem", "body_md": "<PERSON> bought 3 books that each cost the same amount and a magazine for $4. He spent $25 in total. If 'b' is the cost of one book, what is 'b'?", "interactive_element": {"type": "text_input", "placeholder": "Enter cost of one book (b)", "correct_answer_regex": "^7(\\.00)?$", "feedback_correct": "Correct! Each book cost $7. (Equation: 3b + 4 = 25)", "feedback_incorrect": "Set up the equation: 3b + 4 = 25. Then solve for b.", "action_button_text": "Finish Test"}}}, {"id": "ttse-mt1-s5-conclusion", "type": "lesson_screen", "order": 6, "estimatedTimeSeconds": 30, "content": {"headline": "Test Complete!", "body_md": "Excellent work on the Two-Step Challenge!", "visual": {"type": "giphy_search", "value": "success high five"}, "interactive_element": {"type": "button", "text": "Back to Course", "action": "module_complete"}}}]}}