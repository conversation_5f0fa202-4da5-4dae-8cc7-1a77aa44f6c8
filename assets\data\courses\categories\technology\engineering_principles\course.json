{"id": "engineering-principles", "title": "Engineering Principles", "description": "Explore core engineering concepts including design, mechanics, materials, thermodynamics, and systems engineering.", "categoryId": "technology", "thumbnailPath": "assets/images/technology_banner.svg", "difficulty": "Intermediate", "modules": [{"id": "engineering-design-process", "title": "The Engineering Design Process in Detail", "description": "Deep dive into each stage of the engineering design process with practical examples.", "order": 1}, {"id": "fundamental-principles-of-mechanics", "title": "Fundamental Principles of Mechanics", "description": "Explore the core concepts of forces, motion, and equilibrium that underpin many engineering disciplines.", "order": 2}, {"id": "materials-science-engineering", "title": "Materials Science and Engineering", "description": "Explore the properties, behavior, and selection of materials for engineering applications.", "order": 3}, {"id": "thermodynamics-fluid-mechanics-intro", "title": "Thermodynamics and Fluid Mechanics (Introduction)", "description": "Introduce the principles of energy transfer and the behavior of fluids.", "order": 4}, {"id": "systems-engineering-project-management", "title": "Systems Engineering and Project Management", "description": "Explore the principles of designing and managing complex engineering projects.", "order": 5}]}