import 'package:flutter/material.dart';
import 'dart:math' as math;

/// A widget that helps users construct logical chains of reasoning
class InteractiveLogicalChainConstructorWidget extends StatefulWidget {
  /// The data for this widget
  final Map<String, dynamic> data;

  /// Optional callback when the widget state changes
  final Function(bool isCompleted)? onStateChanged;

  const InteractiveLogicalChainConstructorWidget({
    Key? key,
    required this.data,
    this.onStateChanged,
  }) : super(key: key);

  /// Factory constructor to create the widget from JSON data
  factory InteractiveLogicalChainConstructorWidget.fromData(Map<String, dynamic> data) {
    return InteractiveLogicalChainConstructorWidget(
      data: data,
    );
  }

  @override
  State<InteractiveLogicalChainConstructorWidget> createState() => _InteractiveLogicalChainConstructorWidgetState();
}

class _InteractiveLogicalChainConstructorWidgetState extends State<InteractiveLogicalChainConstructorWidget> {
  // Logical chain data
  late List<LogicalChainProblem> _problems;
  
  // Current state
  int _currentProblemIndex = 0;
  bool _isCompleted = false;
  bool _showHint = false;
  bool _showSolution = false;
  String? _errorMessage;
  
  // User input - the ordered list of statements selected by the user
  List<int> _selectedStatementIndices = [];
  
  // UI customization
  late Color _primaryColor;
  late Color _successColor;
  late Color _errorColor;
  late Color _hintColor;
  late Color _neutralColor;

  @override
  void initState() {
    super.initState();
    
    // Parse problems
    _problems = [];
    final problemsData = widget.data['problems'] as List<dynamic>? ?? [];
    for (final problem in problemsData) {
      if (problem is Map<String, dynamic>) {
        final statements = <String>[];
        final statementsData = problem['statements'] as List<dynamic>? ?? [];
        for (final statement in statementsData) {
          if (statement is String) {
            statements.add(statement);
          }
        }
        
        final correctOrder = <int>[];
        final correctOrderData = problem['correct_order'] as List<dynamic>? ?? [];
        for (final index in correctOrderData) {
          if (index is int) {
            correctOrder.add(index);
          }
        }
        
        _problems.add(LogicalChainProblem(
          title: problem['title'] ?? '',
          description: problem['description'] ?? '',
          statements: statements,
          correctOrder: correctOrder,
          hint: problem['hint'] ?? '',
          explanation: problem['explanation'] ?? '',
        ));
      }
    }
    
    // If no problems provided, create default problems
    if (_problems.isEmpty) {
      _problems = [
        LogicalChainProblem(
          title: 'Socrates is Mortal',
          description: 'Arrange the statements to form a valid logical chain of reasoning.',
          statements: [
            'All men are mortal.',
            'Socrates is a man.',
            'Therefore, Socrates is mortal.',
          ],
          correctOrder: [0, 1, 2],
          hint: 'Start with the general premise about all men, then apply it to the specific case of Socrates.',
          explanation: 'This is a classic syllogism. We start with the major premise "All men are mortal," followed by the minor premise "Socrates is a man." From these two premises, we can logically conclude that "Socrates is mortal."',
        ),
        LogicalChainProblem(
          title: 'Divisibility by 6',
          description: 'Arrange the statements to form a valid logical chain of reasoning about divisibility by 6.',
          statements: [
            'If a number is divisible by 6, then it is divisible by both 2 and 3.',
            'The number 18 is divisible by both 2 and 3.',
            'The number 18 is divisible by 2 because it is even.',
            'The number 18 is divisible by 3 because the sum of its digits (1+8=9) is divisible by 3.',
            'Therefore, 18 is divisible by 6.',
          ],
          correctOrder: [0, 2, 3, 1, 4],
          hint: 'Start with the general rule about divisibility by 6, then verify the conditions for the number 18.',
          explanation: 'We begin with the rule that a number is divisible by 6 if and only if it is divisible by both 2 and 3. Then we verify that 18 is divisible by 2 (it\'s even) and that 18 is divisible by 3 (sum of digits is divisible by 3). Since both conditions are met, we can conclude that 18 is divisible by 6.',
        ),
        LogicalChainProblem(
          title: 'Pythagorean Theorem Application',
          description: 'Arrange the statements to form a valid logical chain of reasoning to find the length of the hypotenuse.',
          statements: [
            'In a right triangle, the square of the hypotenuse equals the sum of the squares of the other two sides (Pythagorean Theorem).',
            'We have a right triangle with sides of length 3 and 4.',
            'The square of the hypotenuse = 3² + 4² = 9 + 16 = 25.',
            'The hypotenuse = √25 = 5.',
          ],
          correctOrder: [0, 1, 2, 3],
          hint: 'Start with the theorem, then apply it to the specific triangle.',
          explanation: 'We start with the Pythagorean Theorem (a² + b² = c²), then identify our specific triangle with sides 3 and 4. We calculate the square of the hypotenuse as 3² + 4² = 25, and finally take the square root to find the hypotenuse length of 5.',
        ),
      ];
    }
    
    // Initialize colors
    _primaryColor = _parseColor(widget.data['primary_color'], Colors.blue);
    _successColor = _parseColor(widget.data['success_color'], Colors.green);
    _errorColor = _parseColor(widget.data['error_color'], Colors.red);
    _hintColor = _parseColor(widget.data['hint_color'], Colors.orange);
    _neutralColor = _parseColor(widget.data['neutral_color'], Colors.grey.shade200);
  }

  // Helper method to parse color from string
  Color _parseColor(dynamic colorValue, Color defaultColor) {
    if (colorValue == null) return defaultColor;
    if (colorValue is String) {
      try {
        return Color(int.parse(colorValue.replaceAll('#', '0xFF')));
      } catch (e) {
        return defaultColor;
      }
    }
    return defaultColor;
  }
  
  // Add a statement to the selected list
  void _addStatement(int index) {
    if (!_selectedStatementIndices.contains(index)) {
      setState(() {
        _selectedStatementIndices.add(index);
        _errorMessage = null;
      });
    }
  }
  
  // Remove a statement from the selected list
  void _removeStatement(int index) {
    setState(() {
      _selectedStatementIndices.remove(index);
      _errorMessage = null;
    });
  }
  
  // Move a statement up in the selected list
  void _moveStatementUp(int index) {
    if (index > 0) {
      setState(() {
        final temp = _selectedStatementIndices[index];
        _selectedStatementIndices[index] = _selectedStatementIndices[index - 1];
        _selectedStatementIndices[index - 1] = temp;
      });
    }
  }
  
  // Move a statement down in the selected list
  void _moveStatementDown(int index) {
    if (index < _selectedStatementIndices.length - 1) {
      setState(() {
        final temp = _selectedStatementIndices[index];
        _selectedStatementIndices[index] = _selectedStatementIndices[index + 1];
        _selectedStatementIndices[index + 1] = temp;
      });
    }
  }
  
  // Check if the selected statements are in the correct order
  void _checkOrder() {
    final currentProblem = _problems[_currentProblemIndex];
    
    // Check if all statements are selected
    if (_selectedStatementIndices.length != currentProblem.statements.length) {
      setState(() {
        _errorMessage = 'Please use all statements in your logical chain.';
      });
      return;
    }
    
    // Check if the order is correct
    final isCorrect = _areListsEqual(_selectedStatementIndices, currentProblem.correctOrder);
    
    setState(() {
      if (isCorrect) {
        _showSolution = true;
        _errorMessage = null;
      } else {
        _errorMessage = 'The order of statements is not logically valid. Try again or check the hint.';
      }
    });
  }
  
  // Helper method to check if two lists are equal
  bool _areListsEqual(List<int> list1, List<int> list2) {
    if (list1.length != list2.length) return false;
    for (int i = 0; i < list1.length; i++) {
      if (list1[i] != list2[i]) return false;
    }
    return true;
  }
  
  // Move to the next problem
  void _nextProblem() {
    if (_currentProblemIndex < _problems.length - 1) {
      setState(() {
        _currentProblemIndex++;
        _resetProblemState();
      });
    } else {
      setState(() {
        _isCompleted = true;
      });
      
      // Notify parent about completion
      widget.onStateChanged?.call(true);
    }
  }
  
  // Reset the state for the current problem
  void _resetProblemState() {
    _selectedStatementIndices = [];
    _showHint = false;
    _showSolution = false;
    _errorMessage = null;
  }
  
  // Toggle hint visibility
  void _toggleHint() {
    setState(() {
      _showHint = !_showHint;
    });
  }
  
  // Reset the widget
  void _reset() {
    setState(() {
      _currentProblemIndex = 0;
      _isCompleted = false;
      _resetProblemState();
    });
    
    widget.onStateChanged?.call(false);
  }

  @override
  Widget build(BuildContext context) {
    final currentProblem = _problems[_currentProblemIndex];
    
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withOpacity(0.2),
            spreadRadius: 1,
            blurRadius: 3,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Title
          Text(
            'Logical Chain Constructor',
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
              color: _primaryColor,
            ),
          ),
          
          const SizedBox(height: 12),
          
          // Problem title and description
          Container(
            padding: const EdgeInsets.all(12),
            decoration: BoxDecoration(
              color: Colors.grey.shade50,
              borderRadius: BorderRadius.circular(8),
              border: Border.all(color: Colors.grey.shade300),
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'Problem ${_currentProblemIndex + 1} of ${_problems.length}: ${currentProblem.title}',
                  style: TextStyle(
                    fontWeight: FontWeight.bold,
                    color: _primaryColor,
                  ),
                ),
                const SizedBox(height: 8),
                Text(
                  currentProblem.description,
                  style: const TextStyle(
                    fontSize: 16,
                  ),
                ),
              ],
            ),
          ),
          
          const SizedBox(height: 16),
          
          if (_isCompleted) ...[
            // Completion message
            Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: _successColor.withOpacity(0.1),
                borderRadius: BorderRadius.circular(8),
                border: Border.all(color: _successColor),
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    children: [
                      Icon(Icons.check_circle, color: _successColor),
                      const SizedBox(width: 8),
                      Text(
                        'All Problems Completed!',
                        style: TextStyle(
                          fontWeight: FontWeight.bold,
                          color: _successColor,
                          fontSize: 16,
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 8),
                  const Text(
                    'You have successfully constructed all the logical chains of reasoning.',
                    style: TextStyle(fontSize: 14),
                  ),
                  const SizedBox(height: 12),
                  ElevatedButton(
                    onPressed: _reset,
                    style: ElevatedButton.styleFrom(
                      backgroundColor: _successColor,
                      foregroundColor: Colors.white,
                      padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(8),
                      ),
                    ),
                    child: const Text('Start Over'),
                  ),
                ],
              ),
            ),
          ] else ...[
            // Available statements
            Text(
              'Available Statements:',
              style: const TextStyle(fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 8),
            Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: _neutralColor.withOpacity(0.5),
                borderRadius: BorderRadius.circular(8),
                border: Border.all(color: Colors.grey.shade300),
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: List.generate(currentProblem.statements.length, (index) {
                  final isSelected = _selectedStatementIndices.contains(index);
                  return Opacity(
                    opacity: isSelected ? 0.5 : 1.0,
                    child: Container(
                      margin: const EdgeInsets.only(bottom: 8),
                      child: InkWell(
                        onTap: isSelected ? null : () => _addStatement(index),
                        borderRadius: BorderRadius.circular(8),
                        child: Container(
                          padding: const EdgeInsets.all(12),
                          decoration: BoxDecoration(
                            color: Colors.white,
                            borderRadius: BorderRadius.circular(8),
                            border: Border.all(color: Colors.grey.shade300),
                          ),
                          child: Row(
                            children: [
                              Expanded(
                                child: Text(
                                  currentProblem.statements[index],
                                  style: TextStyle(
                                    color: isSelected ? Colors.grey : Colors.black,
                                  ),
                                ),
                              ),
                              if (!isSelected)
                                Icon(Icons.add_circle, color: _primaryColor),
                            ],
                          ),
                        ),
                      ),
                    ),
                  );
                }),
              ),
            ),
            
            const SizedBox(height: 16),
            
            // Selected statements (logical chain)
            Text(
              'Your Logical Chain:',
              style: const TextStyle(fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 8),
            Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: _primaryColor.withOpacity(0.1),
                borderRadius: BorderRadius.circular(8),
                border: Border.all(color: _primaryColor.withOpacity(0.5)),
              ),
              child: _selectedStatementIndices.isEmpty
                  ? const Center(
                      child: Padding(
                        padding: EdgeInsets.all(16.0),
                        child: Text(
                          'Tap statements above to add them to your logical chain',
                          style: TextStyle(fontStyle: FontStyle.italic),
                          textAlign: TextAlign.center,
                        ),
                      ),
                    )
                  : Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: List.generate(_selectedStatementIndices.length, (index) {
                        final statementIndex = _selectedStatementIndices[index];
                        return Container(
                          margin: const EdgeInsets.only(bottom: 8),
                          child: Row(
                            children: [
                              Text(
                                '${index + 1}. ',
                                style: const TextStyle(fontWeight: FontWeight.bold),
                              ),
                              Expanded(
                                child: Container(
                                  padding: const EdgeInsets.all(12),
                                  decoration: BoxDecoration(
                                    color: Colors.white,
                                    borderRadius: BorderRadius.circular(8),
                                    border: Border.all(color: _primaryColor),
                                  ),
                                  child: Text(
                                    currentProblem.statements[statementIndex],
                                  ),
                                ),
                              ),
                              Column(
                                children: [
                                  IconButton(
                                    icon: const Icon(Icons.arrow_upward),
                                    onPressed: index > 0 ? () => _moveStatementUp(index) : null,
                                    color: _primaryColor,
                                    iconSize: 20,
                                  ),
                                  IconButton(
                                    icon: const Icon(Icons.arrow_downward),
                                    onPressed: index < _selectedStatementIndices.length - 1
                                        ? () => _moveStatementDown(index)
                                        : null,
                                    color: _primaryColor,
                                    iconSize: 20,
                                  ),
                                ],
                              ),
                              IconButton(
                                icon: const Icon(Icons.remove_circle),
                                onPressed: () => _removeStatement(statementIndex),
                                color: _errorColor,
                                iconSize: 20,
                              ),
                            ],
                          ),
                        );
                      }),
                    ),
            ),
            
            if (_errorMessage != null) ...[
              const SizedBox(height: 8),
              Text(
                _errorMessage!,
                style: TextStyle(color: _errorColor, fontStyle: FontStyle.italic),
              ),
            ],
            
            const SizedBox(height: 16),
            
            // Action buttons
            Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                // Check button
                if (!_showSolution)
                  ElevatedButton(
                    onPressed: _checkOrder,
                    style: ElevatedButton.styleFrom(
                      backgroundColor: _primaryColor,
                      foregroundColor: Colors.white,
                      padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(8),
                      ),
                    ),
                    child: const Text('Check'),
                  ),
                
                // Next button
                if (_showSolution)
                  ElevatedButton(
                    onPressed: _nextProblem,
                    style: ElevatedButton.styleFrom(
                      backgroundColor: _primaryColor,
                      foregroundColor: Colors.white,
                      padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(8),
                      ),
                    ),
                    child: const Text('Next Problem'),
                  ),
                
                const SizedBox(width: 16),
                
                // Hint button
                OutlinedButton(
                  onPressed: _toggleHint,
                  style: OutlinedButton.styleFrom(
                    foregroundColor: _hintColor,
                    side: BorderSide(color: _hintColor),
                    padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(8),
                    ),
                  ),
                  child: Text(_showHint ? 'Hide Hint' : 'Show Hint'),
                ),
              ],
            ),
            
            if (_showHint) ...[
              const SizedBox(height: 16),
              Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: _hintColor.withOpacity(0.1),
                  borderRadius: BorderRadius.circular(8),
                  border: Border.all(color: _hintColor),
                ),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      children: [
                        Icon(Icons.lightbulb, color: _hintColor),
                        const SizedBox(width: 8),
                        Text(
                          'Hint',
                          style: TextStyle(
                            fontWeight: FontWeight.bold,
                            color: _hintColor,
                            fontSize: 16,
                          ),
                        ),
                      ],
                    ),
                    const SizedBox(height: 8),
                    Text(
                      currentProblem.hint,
                      style: const TextStyle(fontSize: 14),
                    ),
                  ],
                ),
              ),
            ],
            
            if (_showSolution) ...[
              const SizedBox(height: 16),
              Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: _successColor.withOpacity(0.1),
                  borderRadius: BorderRadius.circular(8),
                  border: Border.all(color: _successColor),
                ),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      children: [
                        Icon(Icons.check_circle, color: _successColor),
                        const SizedBox(width: 8),
                        Text(
                          'Correct!',
                          style: TextStyle(
                            fontWeight: FontWeight.bold,
                            color: _successColor,
                            fontSize: 16,
                          ),
                        ),
                      ],
                    ),
                    const SizedBox(height: 8),
                    Text(
                      'Explanation: ${currentProblem.explanation}',
                      style: const TextStyle(fontSize: 14),
                    ),
                  ],
                ),
              ),
            ],
          ],
          
          // Widget name tag
          if (widget.data['showNameTag'] ?? true)
            Padding(
              padding: const EdgeInsets.only(top: 8),
              child: Text(
                'InteractiveLogicalChainConstructorWidget',
                style: TextStyle(
                  color: Colors.grey[600],
                  fontSize: 12,
                  fontStyle: FontStyle.italic,
                ),
              ),
            ),
        ],
      ),
    );
  }
}

/// Represents a logical chain problem
class LogicalChainProblem {
  final String title;
  final String description;
  final List<String> statements;
  final List<int> correctOrder;
  final String hint;
  final String explanation;
  
  LogicalChainProblem({
    required this.title,
    required this.description,
    required this.statements,
    required this.correctOrder,
    required this.hint,
    required this.explanation,
  });
}
