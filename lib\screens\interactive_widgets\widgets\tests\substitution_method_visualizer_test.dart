import 'package:flutter/material.dart';
import '../interactive_substitution_method_visualizer_widget.dart';

/// A simple test app for the Interactive Substitution Method Visualizer widget
void main() {
  runApp(const SubstitutionMethodVisualizerTestApp());
}

class SubstitutionMethodVisualizerTestApp extends StatelessWidget {
  const SubstitutionMethodVisualizerTestApp({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return MaterialApp(
      title: 'Substitution Method Visualizer Test',
      theme: ThemeData(
        primarySwatch: Colors.blue,
        visualDensity: VisualDensity.adaptivePlatformDensity,
      ),
      home: const SubstitutionMethodVisualizerTestScreen(),
    );
  }
}

class SubstitutionMethodVisualizerTestScreen extends StatelessWidget {
  const SubstitutionMethodVisualizerTestScreen({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    // Sample data for testing
    final testData = {
      'title': 'Substitution Method Visualizer Test',
      'description': 'Test the substitution method visualizer widget with step-by-step visualization.',
      'primaryColor': '#2196F3',
      'secondaryColor': '#FF9800',
      'accentColor': '#4CAF50',
      'textColor': '#212121',
      'highlightColor': '#FFEB3B',
      'initialEquations': [
        {
          'a': 3.0,
          'b': 2.0,
          'c': 7.0,
        },
        {
          'a': 1.0,
          'b': -1.0,
          'c': 0.0,
        },
      ],
      'showNameTag': true,
    };

    return Scaffold(
      appBar: AppBar(
        title: const Text('Substitution Method Visualizer Test'),
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'Interactive Substitution Method Visualizer Widget',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 16),
            const Text(
              'This is a test for the Interactive Substitution Method Visualizer widget. '
              'You can visualize the step-by-step process of solving a system of linear equations using the substitution method.',
              style: TextStyle(fontSize: 14),
            ),
            const SizedBox(height: 24),
            InteractiveSubstitutionMethodVisualizerWidget(
              data: testData,
              onStateChanged: (isCompleted) {
                print('Widget state changed: $isCompleted');
              },
            ),
            const SizedBox(height: 24),
            const Text(
              'Test Instructions:',
              style: TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 8),
            const Text(
              '1. Enter your own system of linear equations\n'
              '2. Click "Solve System" to see the step-by-step solution\n'
              '3. Use the navigation arrows to move through the steps\n'
              '4. Click "Animate Solution" to see the steps automatically\n'
              '5. Verify that the highlighting and explanations are clear',
              style: TextStyle(fontSize: 14),
            ),
          ],
        ),
      ),
    );
  }
}
