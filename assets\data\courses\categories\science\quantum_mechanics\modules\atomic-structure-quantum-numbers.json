{"id": "atomic-structure-quantum-numbers", "title": "ATOMIC STRUCTURE AND QU<PERSON>TUM NUMBERS", "description": "Apply quantum mechanics to understand the structure and behavior of atoms.", "order": 4, "lessons": [{"id": "quantum-numbers", "title": "Quantum Numbers: Specifying Electron States", "description": "Understand the labels for electron orbitals.", "order": 1, "type": "interactive_lesson", "estimatedTimeMinutes": 10, "contentBlocks": [{"id": "qn-screen1-intro", "type": "lesson_screen", "order": 1, "estimatedTimeSeconds": 30, "content": {"headline": "The Quantum Address System", "body_md": "Just as your home has an address that specifies its exact location, electrons in atoms have a set of \"quantum numbers\" that completely specify their quantum state.", "visual": {"type": "giphy_search", "value": "electron orbital"}, "hook": "These quantum numbers arise naturally from solving the <PERSON><PERSON><PERSON><PERSON><PERSON> equation for the hydrogen atom and reveal the beautiful structure of the periodic table.", "interactive_element": {"type": "button", "text": "Let's explore quantum numbers!", "action": "next_screen"}}}, {"id": "qn-screen2-hydrogen", "type": "lesson_screen", "order": 2, "estimatedTimeSeconds": 50, "content": {"headline": "The Hydrogen Atom: Quantum Mechanics' First Triumph", "body_md": "The hydrogen atom—with one proton and one electron—is the simplest atom and the first to be solved using quantum mechanics.\n\nIn 1926, <PERSON> applied his equation to the hydrogen atom and found that the electron's state is described by three quantum numbers:\n\n• Principal quantum number (n)\n• Angular momentum quantum number (l)\n• Magnetic quantum number (mₗ)\n\nLater, a fourth quantum number was added to account for electron spin.", "visual": {"type": "unsplash_search", "value": "hydrogen atom model"}, "interactive_element": {"type": "button", "text": "Tell me about the principal quantum number", "action": "next_screen"}}}, {"id": "qn-screen3-principal", "type": "lesson_screen", "order": 3, "estimatedTimeSeconds": 60, "content": {"headline": "Principal Quantum Number (n)", "body_md": "The **principal quantum number (n)** determines the electron's energy level and the overall size of the orbital:\n\n• Takes positive integer values: n = 1, 2, 3, ...\n• Larger n means higher energy and larger orbital size\n• The electron's energy in hydrogen is Eₙ = -13.6 eV/n²\n• Corresponds to the \"shell\" in the Bohr model\n• Historically labeled as K (n=1), L (n=2), M (n=3), etc.", "interactive_element": {"type": "multiple_choice_text", "question_text": "What happens to an electron's energy as the principal quantum number (n) increases?", "options": [{"id": "qn3opt1", "text": "Energy decreases", "is_correct": false, "feedback_incorrect": "Energy actually increases with higher n values."}, {"id": "qn3opt2", "text": "Energy increases", "is_correct": true, "feedback_correct": "Correct! As n increases, the electron's energy increases (becomes less negative), and it's found farther from the nucleus on average.", "feedback_incorrect": "Think about how the energy formula Eₙ = -13.6 eV/n² changes as n gets larger."}, {"id": "qn3opt3", "text": "Energy remains constant", "is_correct": false, "feedback_incorrect": "The energy definitely changes with different n values."}], "action_button_text": "Continue"}}}, {"id": "qn-screen4-angular", "type": "lesson_screen", "order": 4, "estimatedTimeSeconds": 70, "content": {"headline": "Angular Momentum Quantum Number (l)", "body_md": "The **angular momentum quantum number (l)** determines the shape of the orbital and the electron's angular momentum:\n\n• Takes values from 0 to n-1: l = 0, 1, 2, ..., (n-1)\n• Different l values correspond to different orbital shapes\n• Traditionally labeled with letters: s (l=0), p (l=1), d (l=2), f (l=3), g (l=4), ...\n• The angular momentum of the electron is √(l(l+1))ℏ\n• Corresponds to the \"subshell\" in atomic structure", "visual": {"type": "giphy_search", "value": "atomic orbital shapes"}, "interactive_element": {"type": "button", "text": "What about the magnetic quantum number?", "action": "next_screen"}}}, {"id": "qn-screen5-magnetic", "type": "lesson_screen", "order": 5, "estimatedTimeSeconds": 60, "content": {"headline": "Magnetic Quantum Number (mₗ)", "body_md": "The **magnetic quantum number (mₗ)** determines the orbital's orientation in space:\n\n• Takes values from -l to +l: mₗ = -l, -l+1, ..., 0, ..., l-1, l\n• For a given l value, there are 2l+1 possible mₗ values\n• Different mₗ values correspond to different orientations\n• In a magnetic field, different mₗ states have different energies (<PERSON><PERSON><PERSON> effect)\n• For example, p orbitals (l=1) have three orientations: pₓ, pᵧ, and pᵣ (corresponding to mₗ = -1, 0, +1)", "interactive_element": {"type": "interactive", "interactiveType": "<PERSON><PERSON><PERSON><PERSON>", "data": {"title": "Orbital Types and Quantum Numbers", "instruction": "Match each orbital type with its correct quantum numbers:", "conditions": ["1s orbital", "2p orbital", "3d orbital", "4f orbital"], "outcomes": ["n=1, l=0, mₗ=0", "n=2, l=1, mₗ=-1,0,+1", "n=3, l=2, mₗ=-2,-1,0,+1,+2", "n=4, l=3, mₗ=-3,-2,-1,0,+1,+2,+3"], "correctMatches": [[0, 0], [1, 1], [2, 2], [3, 3]], "explanation": "The orbital notation combines the principal quantum number (n) with a letter representing the angular momentum quantum number (l). Each l value allows for 2l+1 different mₗ values."}}}}, {"id": "qn-screen6-spin", "type": "lesson_screen", "order": 6, "estimatedTimeSeconds": 60, "content": {"headline": "Spin Quantum Number (mₛ)", "body_md": "The **spin quantum number (mₛ)** describes the electron's intrinsic angular momentum or \"spin\":\n\n• Takes only two values: mₛ = +½ (\"spin up\") or -½ (\"spin down\")\n• Spin is an intrinsic property of electrons with no classical analog\n• Discovered experimentally in the <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> experiment\n• Creates a magnetic moment, making electrons act like tiny magnets\n• The total spin angular momentum is always √(3/4)ℏ\n\nSpin completes our quantum description of the electron state.", "visual": {"type": "giphy_search", "value": "electron spin"}, "interactive_element": {"type": "multiple_choice_text", "question_text": "How many possible spin states does an electron have?", "options": [{"id": "qn6opt1", "text": "One", "is_correct": false, "feedback_incorrect": "Electrons have two possible spin states."}, {"id": "qn6opt2", "text": "Two", "is_correct": true, "feedback_correct": "Correct! An electron can have either spin up (mₛ = +½) or spin down (mₛ = -½).", "feedback_incorrect": "Think about the possible values of the spin quantum number mₛ."}, {"id": "qn6opt3", "text": "Four", "is_correct": false, "feedback_incorrect": "Electrons have only two possible spin states, not four."}], "action_button_text": "Continue"}}}, {"id": "qn-screen7-pauli", "type": "lesson_screen", "order": 7, "estimatedTimeSeconds": 50, "content": {"headline": "The Pauli Exclusion Principle", "body_md": "In 1925, <PERSON> discovered a fundamental principle that governs how electrons occupy atomic states:\n\n**The Pauli Exclusion Principle**: No two electrons in an atom can have the same set of four quantum numbers (n, l, mₗ, mₛ).\n\nThis means:\n• Each orbital (defined by n, l, and mₗ) can hold at most two electrons\n• Those two electrons must have opposite spins\n• This principle explains the structure of the periodic table\n• It's why matter is stable and doesn't collapse\n\nWithout this principle, all electrons would occupy the lowest energy state, and chemistry as we know it wouldn't exist!", "visual": {"type": "unsplash_search", "value": "periodic table elements"}, "interactive_element": {"type": "button", "text": "Let's review what we've learned", "action": "next_screen"}}}, {"id": "qn-screen8-recap", "type": "lesson_screen", "order": 8, "estimatedTimeSeconds": 40, "content": {"headline": "Key Takeaways", "body_md": "• Electrons in atoms are described by **four quantum numbers**\n• The **principal quantum number (n)** determines energy level and orbital size\n• The **angular momentum quantum number (l)** determines orbital shape\n• The **magnetic quantum number (mₗ)** determines orbital orientation\n• The **spin quantum number (mₛ)** describes the electron's intrinsic spin\n• The **Pauli Exclusion Principle** states that no two electrons can have identical quantum numbers", "interactive_element": {"type": "button", "text": "Next Lesson: Atomic Orbitals", "action": "next_lesson"}}}]}, {"id": "atomic-orbitals", "title": "Atomic Orbitals: Shapes and Energies", "description": "Visualize the probability distributions of electrons.", "order": 2, "type": "interactive_lesson", "estimatedTimeMinutes": 10, "contentBlocks": [{"id": "ao-screen1-intro", "type": "lesson_screen", "order": 1, "estimatedTimeSeconds": 30, "content": {"headline": "The Quantum Landscape of Atoms", "body_md": "Forget the simple planetary model of atoms you may have learned in school. Quantum mechanics reveals that electrons don't orbit like planets but exist as three-dimensional standing waves called **atomic orbitals**.", "visual": {"type": "giphy_search", "value": "3D atomic orbital"}, "hook": "These beautiful and complex shapes represent probability clouds where electrons are likely to be found, and they're the foundation of chemical bonding and the periodic table.", "interactive_element": {"type": "button", "text": "Show me these orbitals!", "action": "next_screen"}}}, {"id": "ao-screen2-what-are", "type": "lesson_screen", "order": 2, "estimatedTimeSeconds": 50, "content": {"headline": "What Are Atomic Orbitals?", "body_md": "**Atomic orbitals** are the quantum mechanical description of where electrons are likely to be found in an atom:\n\n• They are solutions to the <PERSON><PERSON><PERSON><PERSON><PERSON> equation for electrons in atoms\n• Each orbital is described by three quantum numbers: n, l, and mₗ\n• The orbital's wave function ψ(r,θ,φ) gives the probability amplitude at each point\n• |ψ|² gives the probability density of finding the electron\n• Orbitals are often visualized as surfaces containing a high percentage (e.g., 90%) of the electron probability", "visual": {"type": "unsplash_search", "value": "quantum probability cloud"}, "interactive_element": {"type": "button", "text": "Tell me about s orbitals", "action": "next_screen"}}}, {"id": "ao-screen3-s-orbitals", "type": "lesson_screen", "order": 3, "estimatedTimeSeconds": 60, "content": {"headline": "s Orbitals (l=0)", "body_md": "**s orbitals** are the simplest atomic orbitals:\n\n• Spherically symmetric (same in all directions)\n• Described by quantum numbers l=0, mₗ=0\n• The probability density is highest at the nucleus for 1s\n• Higher n values (2s, 3s, etc.) have spherical nodes (shells where probability is zero)\n• The number of nodes is n-1\n• s orbitals can hold a maximum of 2 electrons (with opposite spins)\n• Examples: 1s, 2s, 3s, etc. (the number indicates the n value)", "interactive_element": {"type": "interactive", "interactiveType": "graph-plotter", "data": {"title": "Radial Probability Density for s Orbitals", "xLabel": "Distance from Nucleus (Bohr radii)", "yLabel": "Probability Density", "xRange": [0, 15], "yRange": [0, 0.6], "functions": [{"expression": "4*x^2*exp(-2*x)", "label": "1s", "color": "blue"}, {"expression": "x^2*(1-x/2)^2*exp(-x)", "label": "2s", "color": "red"}, {"expression": "x^2*(1-2*x/3+2*x^2/27)^2*exp(-2*x/3)", "label": "3s", "color": "green"}], "interactive": "Notice how higher n values have peaks farther from the nucleus, and the number of peaks equals n."}}}}, {"id": "ao-screen4-p-orbitals", "type": "lesson_screen", "order": 4, "estimatedTimeSeconds": 70, "content": {"headline": "p Orbitals (l=1)", "body_md": "**p orbitals** have a distinctive dumbbell shape:\n\n• Described by quantum numbers l=1, mₗ=-1, 0, +1\n• Come in sets of three, oriented along x, y, and z axes (pₓ, pᵧ, pᵣ)\n• Have a node at the nucleus (zero probability there)\n• The three p orbitals in a set are perpendicular to each other\n• Each p orbital can hold 2 electrons, so a complete p subshell holds 6 electrons\n• Examples: 2p, 3p, 4p, etc. (p orbitals start at n=2 since l must be less than n)", "visual": {"type": "giphy_search", "value": "p orbital"}, "interactive_element": {"type": "multiple_choice_text", "question_text": "How many electrons can a complete set of p orbitals (e.g., 2p) hold?", "options": [{"id": "ao4opt1", "text": "2 electrons", "is_correct": false, "feedback_incorrect": "A single p orbital can hold 2 electrons, but there are three p orbitals in a complete set."}, {"id": "ao4opt2", "text": "6 electrons", "is_correct": true, "feedback_correct": "Correct! A complete p subshell has three orbitals (pₓ, pᵧ, pᵣ), each holding 2 electrons, for a total of 6 electrons.", "feedback_incorrect": "Remember that there are three p orbitals in a set, each holding 2 electrons."}, {"id": "ao4opt3", "text": "10 electrons", "is_correct": false, "feedback_incorrect": "10 electrons would be in a complete d subshell, not a p subshell."}], "action_button_text": "Continue"}}}, {"id": "ao-screen5-d-f-orbitals", "type": "lesson_screen", "order": 5, "estimatedTimeSeconds": 70, "content": {"headline": "d and f Orbitals (l=2, l=3)", "body_md": "**d orbitals** (l=2) have more complex shapes:\n• Five orbitals per subshell (mₗ = -2, -1, 0, +1, +2)\n• More complex shapes with multiple lobes\n• Can hold a total of 10 electrons\n• Examples: 3d, 4d, 5d (start at n=3)\n\n**f orbitals** (l=3) are even more complex:\n• Seven orbitals per subshell (mₗ = -3, -2, -1, 0, +1, +2, +3)\n• Very intricate shapes with multiple lobes and nodes\n• Can hold a total of 14 electrons\n• Examples: 4f, 5f, 6f (start at n=4)\n\nThese orbitals are responsible for the properties of transition metals and rare earth elements.", "interactive_element": {"type": "interactive", "interactiveType": "<PERSON><PERSON><PERSON><PERSON>", "data": {"title": "Orbital Types", "instruction": "Match each orbital type with its correct properties:", "conditions": ["s orbital (l=0)", "p orbital (l=1)", "d orbital (l=2)", "f orbital (l=3)"], "outcomes": ["Spherical shape, 1 orbital per subshell, holds 2 electrons", "Dumbbell shape, 3 orbitals per subshell, holds 6 electrons", "Complex lobed shape, 5 orbitals per subshell, holds 10 electrons", "Very complex shape, 7 orbitals per subshell, holds 14 electrons"], "correctMatches": [[0, 0], [1, 1], [2, 2], [3, 3]], "explanation": "The number of orbitals in a subshell is given by 2l+1, and each orbital can hold 2 electrons (with opposite spins)."}}}}, {"id": "ao-screen6-energy-levels", "type": "lesson_screen", "order": 6, "estimatedTimeSeconds": 60, "content": {"headline": "Orbital Energies and Filling Order", "body_md": "In the hydrogen atom, orbitals with the same n value have the same energy, regardless of l. But in multi-electron atoms, the energy depends on both n and l due to electron-electron repulsion.\n\nThe general order of increasing energy is given by the **Aufbau principle**:\n\n1s < 2s < 2p < 3s < 3p < 4s < 3d < 4p < 5s < 4d < 5p < 6s < 4f < 5d < 6p < 7s < 5f < 6d\n\nThis ordering explains the structure of the periodic table and the electron configurations of elements.", "visual": {"type": "unsplash_search", "value": "energy level diagram"}, "interactive_element": {"type": "button", "text": "How do we visualize orbitals?", "action": "next_screen"}}}, {"id": "ao-screen7-visualization", "type": "lesson_screen", "order": 7, "estimatedTimeSeconds": 50, "content": {"headline": "Visualizing Atomic Orbitals", "body_md": "Atomic orbitals are typically visualized in several ways:\n\n• **Boundary surface plots**: 3D surfaces showing where there's a high probability (e.g., 90%) of finding the electron\n\n• **Probability density plots**: 2D or 3D representations of |ψ|², with brighter regions indicating higher probability\n\n• **Radial distribution functions**: Plots showing the probability of finding the electron at a given distance from the nucleus\n\n• **Cross-sectional plots**: 2D slices through 3D probability distributions\n\nThese visualizations help us understand the complex quantum nature of electrons in atoms.", "visual": {"type": "giphy_search", "value": "atomic orbital visualization"}, "interactive_element": {"type": "button", "text": "Let's review what we've learned", "action": "next_screen"}}}, {"id": "ao-screen8-recap", "type": "lesson_screen", "order": 8, "estimatedTimeSeconds": 40, "content": {"headline": "Key Takeaways", "body_md": "• **Atomic orbitals** are three-dimensional probability distributions for electrons\n• **s orbitals** (l=0) are spherically symmetric and can hold 2 electrons\n• **p orbitals** (l=1) have dumbbell shapes, come in sets of three, and hold 6 electrons total\n• **d orbitals** (l=2) and **f orbitals** (l=3) have more complex shapes\n• Orbital **energy levels** determine the filling order and explain the periodic table\n• Orbitals can be **visualized** in various ways to represent electron probability distributions", "interactive_element": {"type": "button", "text": "Next Lesson: The Pauli Exclusion Principle", "action": "next_lesson"}}}]}, {"id": "pauli-exclusion-principle", "title": "The Pauli Exclusion Principle", "description": "Understand why electrons in an atom have unique quantum numbers.", "order": 3, "type": "interactive_lesson", "estimatedTimeMinutes": 10, "contentBlocks": [{"id": "pep-screen1-intro", "type": "lesson_screen", "order": 1, "estimatedTimeSeconds": 30, "content": {"headline": "The Rule That Makes Chemistry Possible", "body_md": "The Pauli Exclusion Principle is one of the most important rules in quantum mechanics. Without it, all electrons would collapse into the lowest energy state, atoms wouldn't have their distinct structures, and chemistry as we know it wouldn't exist.", "visual": {"type": "giphy_search", "value": "electron configuration"}, "hook": "This fundamental principle explains the structure of the periodic table, the stability of matter, and even why stars don't collapse into black holes more quickly.", "interactive_element": {"type": "button", "text": "Tell me about this principle!", "action": "next_screen"}}}, {"id": "pep-screen2-statement", "type": "lesson_screen", "order": 2, "estimatedTimeSeconds": 50, "content": {"headline": "The Pauli Exclusion Principle Stated", "body_md": "In 1925, Austrian physicist <PERSON> formulated his exclusion principle:\n\n**No two electrons in an atom can have the same set of four quantum numbers (n, l, mₗ, mₛ).**\n\nIn other words, no two electrons can occupy exactly the same quantum state. Each electron must differ from every other electron in at least one quantum number.\n\nThis principle applies not just to electrons but to all fermions (particles with half-integer spin).", "visual": {"type": "unsplash_search", "value": "<PERSON> physicist"}, "interactive_element": {"type": "button", "text": "What are the implications?", "action": "next_screen"}}}, {"id": "pep-screen3-implications", "type": "lesson_screen", "order": 3, "estimatedTimeSeconds": 60, "content": {"headline": "Implications for Atomic Structure", "body_md": "The Pauli Exclusion Principle has profound implications for atomic structure:\n\n• Each orbital (defined by n, l, mₗ) can hold at most two electrons\n• Those two electrons must have opposite spins (mₛ = +½ and mₛ = -½)\n• Electrons must fill higher energy orbitals once lower ones are filled\n• This creates the shell structure of atoms and the periodic table\n• Elements in the same column of the periodic table have similar outer electron configurations", "interactive_element": {"type": "multiple_choice_text", "question_text": "According to the Pauli Exclusion Principle, how many electrons can occupy a single orbital?", "options": [{"id": "pep3opt1", "text": "One electron", "is_correct": false, "feedback_incorrect": "A single orbital can actually hold two electrons."}, {"id": "pep3opt2", "text": "Two electrons", "is_correct": true, "feedback_correct": "Correct! A single orbital (defined by quantum numbers n, l, mₗ) can hold two electrons, but they must have opposite spins (mₛ = +½ and mₛ = -½).", "feedback_incorrect": "Think about how many electrons can share the same n, l, and mₗ values but differ in their spin quantum number."}, {"id": "pep3opt3", "text": "Four electrons", "is_correct": false, "feedback_incorrect": "The Pauli Exclusion Principle limits a single orbital to two electrons with opposite spins."}], "action_button_text": "Continue"}}}, {"id": "pep-screen4-electron-config", "type": "lesson_screen", "order": 4, "estimatedTimeSeconds": 70, "content": {"headline": "Electron Configurations", "body_md": "The Pauli Exclusion Principle, combined with the <PERSON><PERSON><PERSON>u principle (electrons fill lower-energy orbitals first) and <PERSON><PERSON>'s rule (electrons maximize their parallel spins in degenerate orbitals), determines the **electron configurations** of atoms.\n\nFor example, the electron configuration of oxygen (Z=8) is 1s² 2s² 2p⁴, meaning:\n• 2 electrons in the 1s orbital\n• 2 electrons in the 2s orbital\n• 4 electrons distributed among the three 2p orbitals\n\nThese configurations explain the chemical properties and reactivity of elements.", "interactive_element": {"type": "interactive", "interactiveType": "sequenceChallenge", "data": {"title": "Electron Configuration Order", "instruction": "Arrange these orbitals in the order they are filled according to the Aufbau principle:", "sequenceType": "text", "correctSequence": ["1s", "2s", "2p", "3s", "3p", "4s", "3d", "4p", "5s", "4d"], "explanation": "The Aufbau principle states that electrons fill orbitals in order of increasing energy. The general pattern follows n+l values (lower values first), and for equal n+l values, lower n comes first."}}}}, {"id": "pep-screen5-spin-statistics", "type": "lesson_screen", "order": 5, "estimatedTimeSeconds": 60, "content": {"headline": "The Spin-Statistics Theorem", "body_md": "The Pauli Exclusion Principle is a special case of the more general **spin-statistics theorem** in quantum field theory:\n\n• **Fermions** (particles with half-integer spin like electrons, protons, neutrons) obey the Pauli Exclusion Principle\n\n• **Bosons** (particles with integer spin like photons, gluons, Higgs bosons) do not obey the exclusion principle and can occupy the same quantum state\n\nThis fundamental distinction leads to very different behaviors: fermions make up matter, while bosons typically carry forces.", "visual": {"type": "giphy_search", "value": "fermions bosons"}, "interactive_element": {"type": "button", "text": "What about quantum statistics?", "action": "next_screen"}}}, {"id": "pep-screen6-quantum-statistics", "type": "lesson_screen", "order": 6, "estimatedTimeSeconds": 60, "content": {"headline": "Quantum Statistics", "body_md": "The Pauli Exclusion Principle leads to different statistical behaviors for collections of particles:\n\n• **Fermi-Dirac statistics** apply to fermions (like electrons)\n  - Particles avoid occupying the same state\n  - At low temperatures, they fill energy levels from bottom up\n  - Creates a \"Fermi sea\" with a sharp energy cutoff (Fermi energy)\n\n• **Bose-Einstein statistics** apply to bosons (like photons)\n  - Particles tend to occupy the same state\n  - At low temperatures, they can form Bose-Einstein condensates\n  - Leads to phenomena like superconductivity and superfluidity", "interactive_element": {"type": "multiple_choice_text", "question_text": "Which particles obey the Pauli Exclusion Principle?", "options": [{"id": "pep6opt1", "text": "Bosons (particles with integer spin)", "is_correct": false, "feedback_incorrect": "Bosons do not obey the Pauli Exclusion Principle—they can occupy the same quantum state."}, {"id": "pep6opt2", "text": "Fermions (particles with half-integer spin)", "is_correct": true, "feedback_correct": "Correct! Fermions, which include electrons, protons, and neutrons, obey the Pauli Exclusion Principle and cannot share the same quantum state.", "feedback_incorrect": "Think about which type of particles (based on their spin) must have different quantum states."}, {"id": "pep6opt3", "text": "Both fermions and bosons", "is_correct": false, "feedback_incorrect": "Only fermions obey the Pauli Exclusion Principle; bosons can occupy the same quantum state."}], "action_button_text": "Continue"}}}, {"id": "pep-screen7-applications", "type": "lesson_screen", "order": 7, "estimatedTimeSeconds": 50, "content": {"headline": "Applications and Consequences", "body_md": "The Pauli Exclusion Principle has far-reaching consequences:\n\n• **Chemical bonding**: Explains why atoms form bonds and how electrons are shared or transferred\n\n• **White dwarf stars**: Electron degeneracy pressure (from the exclusion principle) prevents these stars from collapsing\n\n• **Neutron stars**: Neutron degeneracy pressure supports these incredibly dense objects\n\n• **Electrical conductivity**: Explains the behavior of electrons in metals and semiconductors\n\n• **Quantum computing**: Influences how quantum bits (qubits) can be implemented and manipulated", "visual": {"type": "unsplash_search", "value": "white dwarf star"}, "interactive_element": {"type": "button", "text": "Let's review what we've learned", "action": "next_screen"}}}, {"id": "pep-screen8-recap", "type": "lesson_screen", "order": 8, "estimatedTimeSeconds": 40, "content": {"headline": "Key Takeaways", "body_md": "• The **Pauli Exclusion Principle** states that no two electrons in an atom can have the same set of four quantum numbers\n• This principle applies to all **fermions** (particles with half-integer spin)\n• It explains the **shell structure** of atoms and the organization of the **periodic table**\n• Each orbital can hold a maximum of **two electrons** with opposite spins\n• The principle leads to **Fermi-Dirac statistics** for collections of fermions\n• It has important applications in **chemistry, astrophysics, and materials science**", "interactive_element": {"type": "button", "text": "Next Lesson: Electron Configuration and the Periodic Table", "action": "next_lesson"}}}]}, {"id": "electron-configuration", "title": "Electron Configuration and the Periodic Table (Quantum View)", "description": "See the quantum basis for periodic trends.", "order": 4, "type": "interactive_lesson", "estimatedTimeMinutes": 10, "contentBlocks": [{"id": "ec-screen1-intro", "type": "lesson_screen", "order": 1, "estimatedTimeSeconds": 30, "content": {"headline": "The Quantum Blueprint of Elements", "body_md": "The periodic table, one of science's greatest organizational achievements, is actually a manifestation of quantum mechanics. The arrangement of elements and their chemical properties emerge directly from the quantum rules governing electron configurations.", "visual": {"type": "giphy_search", "value": "periodic table quantum"}, "hook": "Let's explore how quantum mechanics explains the structure of the periodic table and the patterns of chemical behavior across elements.", "interactive_element": {"type": "button", "text": "Let's begin!", "action": "next_screen"}}}, {"id": "ec-screen2-electron-config", "type": "lesson_screen", "order": 2, "estimatedTimeSeconds": 50, "content": {"headline": "Electron Configurations", "body_md": "The **electron configuration** of an atom describes how electrons are distributed among the available orbitals.\n\nElectron configurations are written using a shorthand notation:\n• The principal quantum number (n)\n• The orbital type (s, p, d, f) corresponding to l = 0, 1, 2, 3\n• A superscript indicating the number of electrons in that subshell\n\nFor example, carbon (Z=6) has the configuration 1s² 2s² 2p²\n\nThis tells us carbon has:\n• 2 electrons in the 1s orbital\n• 2 electrons in the 2s orbital\n• 2 electrons distributed among the three 2p orbitals", "visual": {"type": "unsplash_search", "value": "carbon atom model"}, "interactive_element": {"type": "button", "text": "How do we determine configurations?", "action": "next_screen"}}}, {"id": "ec-screen3-aufbau", "type": "lesson_screen", "order": 3, "estimatedTimeSeconds": 60, "content": {"headline": "The Aufbau Principle", "body_md": "The **Aufbau principle** (German for \"building up\") states that electrons fill orbitals in order of increasing energy.\n\nThe general order of orbital energies is:\n1s < 2s < 2p < 3s < 3p < 4s < 3d < 4p < 5s < 4d < 5p < 6s < 4f < 5d < 6p < 7s < 5f < 6d\n\nThis order can be remembered using the diagonal rule in the orbital diagram, following the arrows from top to bottom.", "interactive_element": {"type": "interactive", "interactiveType": "<PERSON><PERSON><PERSON><PERSON>", "data": {"title": "Electron Configurations", "instruction": "Match each element with its correct electron configuration:", "conditions": ["Hydrogen (H, Z=1)", "Helium (<PERSON>, <PERSON>=2)", "Lithium (Li, Z=3)", "Carbon (C, Z=6)", "Neon (<PERSON><PERSON>, Z=10)"], "outcomes": ["1s¹", "1s²", "1s² 2s¹", "1s² 2s² 2p²", "1s² 2s² 2p⁶"], "correctMatches": [[0, 0], [1, 1], [2, 2], [3, 3], [4, 4]], "explanation": "Electron configurations are built by adding electrons one at a time to the lowest-energy available orbital, following the Aufbau principle."}}}}, {"id": "ec-screen4-hunds-rule", "type": "lesson_screen", "order": 4, "estimatedTimeSeconds": 70, "content": {"headline": "<PERSON><PERSON>'s Rule", "body_md": "**<PERSON><PERSON>'s rule** states that for orbitals of equal energy (such as the three 2p orbitals), electrons will occupy different orbitals with parallel spins before pairing up.\n\nFor example, nitrogen (Z=7) with configuration 1s² 2s² 2p³ has its three 2p electrons in separate 2p orbitals, all with the same spin.\n\nThis rule arises from:\n• The Pauli Exclusion Principle\n• Electron-electron repulsion (electrons stay farther apart in different orbitals)\n• Exchange energy (parallel spins have lower energy due to quantum effects)", "visual": {"type": "giphy_search", "value": "electron spin parallel"}, "interactive_element": {"type": "multiple_choice_text", "question_text": "According to <PERSON><PERSON>'s rule, how are the 2p electrons arranged in a carbon atom (Z=6)?", "options": [{"id": "ec4opt1", "text": "Two electrons paired in one 2p orbital, other 2p orbitals empty", "is_correct": false, "feedback_incorrect": "<PERSON><PERSON>'s rule states that electrons will occupy different orbitals before pairing up."}, {"id": "ec4opt2", "text": "One electron in each of two 2p orbitals with parallel spins", "is_correct": true, "feedback_correct": "Correct! Carbon has two 2p electrons, and according to <PERSON><PERSON>'s rule, they occupy different 2p orbitals with parallel spins to minimize energy.", "feedback_incorrect": "Think about how electrons distribute themselves among orbitals of equal energy."}, {"id": "ec4opt3", "text": "One electron in each of two 2p orbitals with opposite spins", "is_correct": false, "feedback_incorrect": "<PERSON><PERSON>'s rule states that electrons in different orbitals of the same subshell will have parallel spins."}], "action_button_text": "Continue"}}}, {"id": "ec-screen5-periodic-table", "type": "lesson_screen", "order": 5, "estimatedTimeSeconds": 60, "content": {"headline": "The Quantum Basis of the Periodic Table", "body_md": "The structure of the periodic table directly reflects electron configurations:\n\n• **Groups (columns)** contain elements with similar outer electron configurations (valence electrons)\n\n• **Periods (rows)** represent the principal quantum number of the outermost electrons\n\n• **Blocks** correspond to the subshell being filled:\n  - s-block (Groups 1-2): s subshell filling\n  - p-block (Groups 13-18): p subshell filling\n  - d-block (transition metals): d subshell filling\n  - f-block (lanthanides, actinides): f subshell filling", "interactive_element": {"type": "interactive", "interactiveType": "graph-plotter", "data": {"title": "Periodic Table Blocks", "xLabel": "Group", "yLabel": "Period", "xRange": [0, 18], "yRange": [0, 7], "regions": [{"x1": 0, "y1": 0, "x2": 2, "y2": 7, "label": "s-block", "color": "red"}, {"x1": 12, "y1": 0, "x2": 18, "y2": 7, "label": "p-block", "color": "blue"}, {"x1": 2, "y1": 3, "x2": 12, "y2": 7, "label": "d-block", "color": "green"}, {"x1": 2, "y1": 5, "x2": 16, "y2": 7, "label": "f-block", "color": "purple"}], "interactive": "The periodic table is organized by electron configuration, with each block corresponding to a different type of orbital being filled."}}}}, {"id": "ec-screen6-periodic-trends", "type": "lesson_screen", "order": 6, "estimatedTimeSeconds": 60, "content": {"headline": "Quantum Explanation of Periodic Trends", "body_md": "Quantum mechanics explains the periodic trends in element properties:\n\n• **Atomic radius**: Decreases across a period (increasing nuclear charge pulls electrons closer) and increases down a group (new shells are added farther from nucleus)\n\n• **Ionization energy**: Increases across a period (electrons are held more tightly) and decreases down a group (valence electrons are farther from nucleus)\n\n• **Electronegativity**: Increases across a period and decreases down a group (for similar reasons)\n\n• **Metallic character**: Decreases across a period and increases down a group (related to ease of losing electrons)", "visual": {"type": "unsplash_search", "value": "periodic trend graph"}, "interactive_element": {"type": "multiple_choice_text", "question_text": "Why does atomic radius generally decrease from left to right across a period?", "options": [{"id": "ec6opt1", "text": "Electrons are added to new shells farther from the nucleus", "is_correct": false, "feedback_incorrect": "Across a period, electrons are added to the same shell, not new ones."}, {"id": "ec6opt2", "text": "Increasing nuclear charge pulls electrons closer to the nucleus", "is_correct": true, "feedback_correct": "Correct! As we move across a period, protons are added to the nucleus, increasing its positive charge. This stronger attraction pulls the electron cloud closer, reducing the atomic radius.", "feedback_incorrect": "Think about what happens to the nuclear charge as you move across a period."}, {"id": "ec6opt3", "text": "Electron-electron repulsion pushes electrons closer to the nucleus", "is_correct": false, "feedback_incorrect": "Electron-electron repulsion would actually tend to increase atomic radius, not decrease it."}], "action_button_text": "Continue"}}}, {"id": "ec-screen7-exceptions", "type": "lesson_screen", "order": 7, "estimatedTimeSeconds": 50, "content": {"headline": "Exceptions and Special Cases", "body_md": "While the Aufbau principle works for most elements, there are some exceptions:\n\n• **Chromium (Cr, Z=24)**: Expected 1s² 2s² 2p⁶ 3s² 3p⁶ 4s² 3d⁴, but actually has 1s² 2s² 2p⁶ 3s² 3p⁶ 4s¹ 3d⁵\n\n• **Copper (C<PERSON>, Z=29)**: Expected 1s² 2s² 2p⁶ 3s² 3p⁶ 4s² 3d⁹, but actually has 1s² 2s² 2p⁶ 3s² 3p⁶ 4s¹ 3d¹⁰\n\nThese exceptions occur because half-filled and completely filled subshells have extra stability due to exchange energy and electron-electron interactions.\n\nSimilar exceptions occur in heavier elements, especially with d and f orbitals.", "visual": {"type": "giphy_search", "value": "copper atom"}, "interactive_element": {"type": "button", "text": "Let's review what we've learned", "action": "next_screen"}}}, {"id": "ec-screen8-recap", "type": "lesson_screen", "order": 8, "estimatedTimeSeconds": 40, "content": {"headline": "Key Takeaways", "body_md": "• **Electron configurations** describe how electrons are distributed in atomic orbitals\n• The **Aufbau principle** states that electrons fill orbitals in order of increasing energy\n• **<PERSON><PERSON>'s rule** states that electrons occupy orbitals of equal energy singly with parallel spins before pairing\n• The **periodic table structure** directly reflects electron configurations\n• **Periodic trends** in properties like atomic radius and ionization energy have quantum explanations\n• Some elements have **exceptions** to the expected configurations due to stability of half-filled or filled subshells", "interactive_element": {"type": "button", "text": "Next Lesson: Spectroscopy", "action": "next_lesson"}}}]}, {"id": "spectroscopy", "title": "Spectroscopy: Probing Atomic Energy Levels", "description": "Understand how light interacts with atoms.", "order": 5, "type": "interactive_lesson", "estimatedTimeMinutes": 10, "contentBlocks": [{"id": "spec-screen1-intro", "type": "lesson_screen", "order": 1, "estimatedTimeSeconds": 30, "content": {"headline": "Reading the Atomic Fingerprints", "body_md": "Spectroscopy—the study of how matter interacts with electromagnetic radiation—is one of our most powerful tools for understanding atoms. Each element has a unique spectral \"fingerprint\" that reveals its quantum structure.", "visual": {"type": "giphy_search", "value": "emission spectrum"}, "hook": "This technique allows us to identify elements in distant stars, understand chemical bonds, and probe the quantum energy levels of atoms with remarkable precision.", "interactive_element": {"type": "button", "text": "Let's explore spectroscopy!", "action": "next_screen"}}}, {"id": "spec-screen2-atomic-spectra", "type": "lesson_screen", "order": 2, "estimatedTimeSeconds": 50, "content": {"headline": "Atomic Spectra: Quantum Signatures", "body_md": "When atoms interact with light, they can absorb or emit photons, but only at specific frequencies that correspond to energy differences between quantum states.\n\nThis leads to two types of atomic spectra:\n\n• **Emission spectra**: Bright lines on a dark background, produced when excited atoms emit photons\n\n• **Absorption spectra**: Dark lines on a continuous background, produced when atoms absorb specific frequencies from white light\n\nThese spectra are unique for each element—like atomic fingerprints.", "visual": {"type": "unsplash_search", "value": "spectral lines"}, "interactive_element": {"type": "button", "text": "How do these spectra form?", "action": "next_screen"}}}, {"id": "spec-screen3-energy-transitions", "type": "lesson_screen", "order": 3, "estimatedTimeSeconds": 60, "content": {"headline": "Quantum Transitions and Photons", "body_md": "Atomic spectra arise from electrons transitioning between energy levels:\n\n• When an electron jumps from a higher energy level (E₂) to a lower one (E₁), it emits a photon with energy E = E₂ - E₁\n\n• When an electron absorbs a photon with energy E = E₂ - E₁, it jumps from energy level E₁ to E₂\n\n• The energy of a photon is related to its frequency by E = hf, where h is <PERSON><PERSON>'s constant\n\n• The wavelength of the photon is λ = c/f, where c is the speed of light\n\nThese relationships connect the observed spectral lines to the atom's quantum energy levels.", "interactive_element": {"type": "multiple_choice_text", "question_text": "What happens when an electron transitions from a higher energy level to a lower one?", "options": [{"id": "spec3opt1", "text": "It absorbs a photon", "is_correct": false, "feedback_incorrect": "Electrons absorb photons when moving to higher energy levels, not lower ones."}, {"id": "spec3opt2", "text": "It emits a photon", "is_correct": true, "feedback_correct": "Correct! When an electron drops from a higher energy level to a lower one, it releases energy in the form of a photon with energy equal to the difference between the levels.", "feedback_incorrect": "Think about energy conservation—where does the energy difference between the levels go?"}, {"id": "spec3opt3", "text": "Nothing happens", "is_correct": false, "feedback_incorrect": "Energy must be conserved, so something must happen when an electron changes energy levels."}], "action_button_text": "Continue"}}}, {"id": "spec-screen4-hydrogen", "type": "lesson_screen", "order": 4, "estimatedTimeSeconds": 70, "content": {"headline": "The Hydrogen Spectrum", "body_md": "The hydrogen atom has the simplest spectrum, with several distinct series of spectral lines:\n\n• **<PERSON>yman series** (ultraviolet): Transitions to the ground state (n=1)\n• **Balmer series** (visible): Transitions to the n=2 state\n• **Paschen series** (infrared): Transitions to the n=3 state\n• **Brackett series** (infrared): Transitions to the n=4 state\n\nThe wavelengths in each series follow a pattern described by the Rydberg formula:\n\n1/λ = R(1/n₁² - 1/n₂²)\n\nwhere R is the Rydberg constant, n₁ is the lower energy level, and n₂ is the higher energy level.", "interactive_element": {"type": "interactive", "interactiveType": "graph-plotter", "data": {"title": "Hydrogen Spectral Series", "xLabel": "Wavelength (nm)", "yLabel": "Relative Intensity", "xRange": [0, 700], "yRange": [0, 1], "points": [{"x": 121.6, "y": 0.8, "label": "Lyman-α"}, {"x": 102.6, "y": 0.3, "label": "Lyman-β"}, {"x": 97.3, "y": 0.15, "label": "Lyman-γ"}, {"x": 656.3, "y": 1.0, "label": "Balmer-α (Hα)"}, {"x": 486.1, "y": 0.7, "label": "Balmer-β (Hβ)"}, {"x": 434.0, "y": 0.4, "label": "Balmer-γ (Hγ)"}, {"x": 410.2, "y": 0.2, "label": "Balmer-δ (Hδ)"}], "interactive": "The Balmer series falls in the visible range, with Hα appearing red, Hβ blue-green, and Hγ and Hδ violet."}}}}, {"id": "spec-screen5-selection-rules", "type": "lesson_screen", "order": 5, "estimatedTimeSeconds": 60, "content": {"headline": "Quantum Selection Rules", "body_md": "Not all transitions between energy levels are allowed. **Quantum selection rules** determine which transitions can occur:\n\n• For the principal quantum number: Δn can be any value\n• For the angular momentum quantum number: Δl = ±1\n• For the magnetic quantum number: Δmₗ = 0, ±1\n\nThese rules arise from conservation of angular momentum and the properties of electromagnetic radiation (photons have spin 1).\n\nTransitions that violate these rules are called \"forbidden transitions.\" They can still occur but are much less likely.", "visual": {"type": "giphy_search", "value": "quantum transition"}, "interactive_element": {"type": "multiple_choice_text", "question_text": "According to selection rules, which of these transitions is allowed?", "options": [{"id": "spec5opt1", "text": "2s → 1s (Δn = -1, Δl = 0)", "is_correct": false, "feedback_incorrect": "This transition violates the selection rule Δl = ±1."}, {"id": "spec5opt2", "text": "2p → 1s (Δn = -1, Δl = -1)", "is_correct": true, "feedback_correct": "Correct! This transition satisfies the selection rules: Δn can be any value, and Δl = -1 is allowed.", "feedback_incorrect": "Check the selection rule for the angular momentum quantum number (l)."}, {"id": "spec5opt3", "text": "3d → 1s (Δn = -2, Δl = -2)", "is_correct": false, "feedback_incorrect": "This transition violates the selection rule Δl = ±1."}], "action_button_text": "Continue"}}}, {"id": "spec-screen6-spectroscopic-techniques", "type": "lesson_screen", "order": 6, "estimatedTimeSeconds": 60, "content": {"headline": "Modern Spectroscopic Techniques", "body_md": "Modern spectroscopy goes far beyond visible light and includes many sophisticated techniques:\n\n• **Laser spectroscopy**: Uses monochromatic laser light for high precision\n\n• **X-ray spectroscopy**: Probes inner electron transitions and core electrons\n\n• **Photoelectron spectroscopy**: Measures the energy of electrons ejected by photons\n\n• **Nuclear magnetic resonance (NMR)**: Examines transitions between nuclear spin states\n\n• **<PERSON><PERSON><PERSON><PERSON><PERSON> spectroscopy**: Detects tiny energy shifts due to the environment of atomic nuclei\n\nThese techniques provide detailed information about atomic and molecular structure.", "interactive_element": {"type": "interactive", "interactiveType": "<PERSON><PERSON><PERSON><PERSON>", "data": {"title": "Spectroscopic Techniques", "instruction": "Match each spectroscopic technique with what it primarily measures:", "conditions": ["Absorption spectroscopy", "Emission spectroscopy", "Photoelectron spectroscopy", "Nuclear magnetic resonance", "X-ray fluorescence"], "outcomes": ["Which wavelengths are absorbed by a sample", "Which wavelengths are emitted by excited atoms", "Energies of electrons ejected from atoms", "Transitions between nuclear spin states", "Inner shell electron transitions"], "correctMatches": [[0, 0], [1, 1], [2, 2], [3, 3], [4, 4]], "explanation": "Different spectroscopic techniques probe different aspects of atomic and molecular structure, from outer electron configurations to nuclear properties."}}}}, {"id": "spec-screen7-applications", "type": "lesson_screen", "order": 7, "estimatedTimeSeconds": 50, "content": {"headline": "Applications of Spectroscopy", "body_md": "Spectroscopy has countless applications across science and technology:\n\n• **Astronomy**: Identifying elements in stars and determining stellar properties\n\n• **Chemistry**: Analyzing chemical composition and molecular structure\n\n• **Medicine**: Medical imaging techniques like MRI (based on NMR)\n\n• **Environmental science**: Monitoring pollutants and analyzing soil composition\n\n• **Forensics**: Identifying unknown substances at crime scenes\n\n• **Quality control**: Ensuring product purity in manufacturing\n\nSpectroscopy is one of the most versatile and powerful analytical tools in science.", "visual": {"type": "unsplash_search", "value": "astronomical spectroscopy"}, "interactive_element": {"type": "button", "text": "Let's review what we've learned", "action": "next_screen"}}}, {"id": "spec-screen8-recap", "type": "lesson_screen", "order": 8, "estimatedTimeSeconds": 40, "content": {"headline": "Key Takeaways", "body_md": "• **Spectroscopy** studies how matter interacts with electromagnetic radiation\n• **Atomic spectra** arise from electrons transitioning between quantum energy levels\n• **Emission spectra** occur when excited atoms emit photons\n• **Absorption spectra** occur when atoms absorb specific frequencies from white light\n• **Selection rules** determine which transitions are allowed\n• **Modern spectroscopic techniques** probe different aspects of atomic and molecular structure\n• Spectroscopy has numerous **applications** across science and technology", "interactive_element": {"type": "button", "text": "Take the Module Test", "action": "next_lesson"}}}]}, {"id": "quantum-chemist-test", "title": "The Quantum Chemist", "description": "Use quantum numbers to describe electron states and understand the quantum basis of atomic structure.", "order": 6, "type": "module_test_interactive", "estimatedTimeMinutes": 10, "contentBlocks": [{"id": "qct-intro", "type": "question_screen", "order": 1, "estimatedTimeSeconds": 30, "content": {"headline": "Test Your Understanding of Atomic Structure", "body_md": "Congratulations on completing the fourth module of Quantum Mechanics! Let's see how well you understand quantum numbers, atomic orbitals, and the quantum basis of atomic structure.", "visual": {"type": "giphy_search", "value": "quantum atom"}, "interactive_element": {"type": "button", "text": "Begin the Test", "action": "next_screen"}}}, {"id": "qct-q1", "type": "question_screen", "order": 2, "estimatedTimeSeconds": 60, "content": {"headline": "Question 1: Quantum Numbers", "body_md": "Which set of quantum numbers is NOT allowed for an electron in an atom?", "interactive_element": {"type": "multiple_choice_text", "question_text": "Select the invalid set of quantum numbers:", "options": [{"id": "qct1opt1", "text": "n=2, l=0, mₗ=0, mₛ=+½", "is_correct": false, "feedback_incorrect": "This is a valid set of quantum numbers describing an electron in the 2s orbital with spin up."}, {"id": "qct1opt2", "text": "n=3, l=2, mₗ=0, mₛ=-½", "is_correct": false, "feedback_incorrect": "This is a valid set of quantum numbers describing an electron in one of the 3d orbitals with spin down."}, {"id": "qct1opt3", "text": "n=2, l=2, mₗ=1, mₛ=+½", "is_correct": true, "feedback_correct": "Correct! This set is invalid because l cannot exceed n-1. For n=2, the maximum value of l is 1, so l=2 is not allowed.", "feedback_incorrect": "Remember the constraints on quantum numbers: l must be less than n."}, {"id": "qct1opt4", "text": "n=4, l=1, mₗ=-1, mₛ=-½", "is_correct": false, "feedback_incorrect": "This is a valid set of quantum numbers describing an electron in one of the 4p orbitals with spin down."}], "action_button_text": "Next Question"}}}, {"id": "qct-q2", "type": "question_screen", "order": 3, "estimatedTimeSeconds": 60, "content": {"headline": "Question 2: Atomic Orbitals", "body_md": "How many electrons can be accommodated in the 3d subshell?", "interactive_element": {"type": "multiple_choice_text", "question_text": "Select the correct number:", "options": [{"id": "qct2opt1", "text": "2 electrons", "is_correct": false, "feedback_incorrect": "A single orbital can hold 2 electrons, but the 3d subshell contains multiple orbitals."}, {"id": "qct2opt2", "text": "6 electrons", "is_correct": false, "feedback_incorrect": "6 electrons would be in a p subshell, not a d subshell."}, {"id": "qct2opt3", "text": "10 electrons", "is_correct": true, "feedback_correct": "Correct! The d subshell (l=2) has 5 orbitals (mₗ = -2, -1, 0, +1, +2), and each orbital can hold 2 electrons, for a total of 10 electrons.", "feedback_incorrect": "Calculate the number of orbitals in the d subshell (2l+1) and multiply by 2 electrons per orbital."}, {"id": "qct2opt4", "text": "14 electrons", "is_correct": false, "feedback_incorrect": "14 electrons would be in an f subshell, not a d subshell."}], "action_button_text": "Next Question"}}}, {"id": "qct-q3", "type": "question_screen", "order": 4, "estimatedTimeSeconds": 70, "content": {"headline": "Question 3: The Pauli Exclusion Principle", "body_md": "According to the Pauli Exclusion Principle, which of the following statements is true?", "interactive_element": {"type": "multiple_choice_text", "question_text": "Select the correct statement:", "options": [{"id": "qct3opt1", "text": "No two electrons in an atom can have the same set of four quantum numbers", "is_correct": true, "feedback_correct": "Correct! The Pauli Exclusion Principle states that no two electrons in an atom can have identical values for all four quantum numbers (n, l, mₗ, mₛ).", "feedback_incorrect": "This is the fundamental statement of the Pauli Exclusion Principle."}, {"id": "qct3opt2", "text": "No two electrons in an atom can have the same principal quantum number", "is_correct": false, "feedback_incorrect": "Many electrons can share the same principal quantum number (n). For example, all electrons in the second shell have n=2."}, {"id": "qct3opt3", "text": "No two electrons in an atom can have the same spin", "is_correct": false, "feedback_incorrect": "Many electrons in an atom can have the same spin quantum number."}, {"id": "qct3opt4", "text": "No two electrons in an atom can occupy the same orbital", "is_correct": false, "feedback_incorrect": "Two electrons can occupy the same orbital if they have opposite spins."}], "action_button_text": "Next Question"}}}, {"id": "qct-q4", "type": "question_screen", "order": 5, "estimatedTimeSeconds": 70, "content": {"headline": "Question 4: Electron Configuration", "body_md": "What is the electron configuration of nitrogen (Z=7)?", "interactive_element": {"type": "multiple_choice_text", "question_text": "Select the correct electron configuration:", "options": [{"id": "qct4opt1", "text": "1s² 2s² 2p³", "is_correct": true, "feedback_correct": "Correct! Nitrogen has 7 electrons: 2 in the 1s orbital, 2 in the 2s orbital, and 3 in the 2p orbitals.", "feedback_incorrect": "Count the total number of electrons (7) and distribute them according to the Aufbau principle."}, {"id": "qct4opt2", "text": "1s² 2s² 2p²", "is_correct": false, "feedback_incorrect": "This configuration has only 6 electrons, but nitrogen has 7."}, {"id": "qct4opt3", "text": "1s² 2s¹ 2p⁴", "is_correct": false, "feedback_incorrect": "This doesn't follow the Aufbau principle, which states that the 2s orbital fills before the 2p orbitals."}, {"id": "qct4opt4", "text": "1s² 2s² 2p² 3s¹", "is_correct": false, "feedback_incorrect": "This doesn't follow the Aufbau principle. The 2p orbitals would fill before the 3s orbital."}], "action_button_text": "Next Question"}}}, {"id": "qct-q5", "type": "question_screen", "order": 6, "estimatedTimeSeconds": 70, "content": {"headline": "Question 5: Spectroscopy", "body_md": "When an electron in a hydrogen atom transitions from the n=3 state to the n=2 state, what happens?", "interactive_element": {"type": "multiple_choice_text", "question_text": "Select the correct outcome:", "options": [{"id": "qct5opt1", "text": "A photon is absorbed", "is_correct": false, "feedback_incorrect": "Photons are absorbed when electrons move to higher energy states, not lower ones."}, {"id": "qct5opt2", "text": "A photon is emitted with a wavelength in the Balmer series", "is_correct": true, "feedback_correct": "Correct! When an electron drops from n=3 to n=2, it emits a photon. This transition is part of the Balmer series, which includes transitions to the n=2 level. Specifically, this produces the Hα line with a wavelength of 656.3 nm (red light).", "feedback_incorrect": "Think about energy conservation and which spectral series involves transitions to the n=2 level."}, {"id": "qct5opt3", "text": "A photon is emitted with a wavelength in the Lyman series", "is_correct": false, "feedback_incorrect": "The Lyman series involves transitions to the ground state (n=1), not to n=2."}, {"id": "qct5opt4", "text": "No photon is emitted or absorbed", "is_correct": false, "feedback_incorrect": "Energy must be conserved, so a photon must be emitted when an electron drops to a lower energy level."}], "action_button_text": "Final Question"}}}, {"id": "qct-q6", "type": "question_screen", "order": 7, "estimatedTimeSeconds": 70, "content": {"headline": "Question 6: Atomic Structure", "body_md": "Match each quantum concept with its correct description.", "interactive_element": {"type": "interactive", "interactiveType": "<PERSON><PERSON><PERSON><PERSON>", "data": {"title": "Atomic Structure Concepts", "instruction": "Match each concept with its correct description:", "conditions": ["Principal quantum number (n)", "Angular momentum quantum number (l)", "Magnetic quantum number (mₗ)", "Spin quantum number (mₛ)", "<PERSON><PERSON>'s rule"], "outcomes": ["Determines the energy level and overall size of the orbital", "Determines the shape of the orbital (s, p, d, f)", "Determines the orientation of the orbital in space", "Describes the electron's intrinsic angular momentum (up or down)", "States that electrons occupy orbitals of equal energy singly with parallel spins before pairing"], "correctMatches": [[0, 0], [1, 1], [2, 2], [3, 3], [4, 4]], "explanation": "These quantum concepts form the foundation of our understanding of atomic structure and electron configurations."}}}}, {"id": "qct-results", "type": "lesson_screen", "order": 8, "estimatedTimeSeconds": 40, "content": {"headline": "Module Complete!", "body_md": "Congratulations! You've completed the fourth module of Quantum Mechanics: Atomic Structure and Quantum Numbers.\n\nYou now understand how quantum mechanics explains the structure of atoms, including:\n\n• The four quantum numbers that specify electron states\n• The shapes and properties of atomic orbitals\n• The Pauli Exclusion Principle and its implications\n• Electron configurations and the quantum basis of the periodic table\n• How spectroscopy reveals the quantum structure of atoms\n\nReady to explore the practical applications of quantum mechanics in modern technology?", "visual": {"type": "giphy_search", "value": "quantum atom celebration"}, "interactive_element": {"type": "button", "text": "Continue to Module 5", "action": "module_complete"}}}]}]}