import 'package:flutter/material.dart';
import 'widget_colors.dart';

/// Standardized text styles for interactive widgets
/// Ensures consistent typography across all widget types
class WidgetTextStyles {
  // Private constructor to prevent instantiation
  WidgetTextStyles._();

  // ============================================================================
  // FONT FAMILY
  // ============================================================================
  
  static const String fontFamily = 'WorkSans';

  // ============================================================================
  // HEADING STYLES
  // ============================================================================
  
  /// Large heading for widget titles (24px, bold)
  static const TextStyle headingLarge = TextStyle(
    fontSize: 24,
    fontWeight: FontWeight.bold,
    color: WidgetColors.textPrimary,
    fontFamily: fontFamily,
    height: 1.2,
  );
  
  /// Medium heading for section titles (20px, semi-bold)
  static const TextStyle headingMedium = TextStyle(
    fontSize: 20,
    fontWeight: FontWeight.w600,
    color: WidgetColors.textPrimary,
    fontFamily: fontFamily,
    height: 1.3,
  );
  
  /// Small heading for subsections (18px, semi-bold)
  static const TextStyle headingSmall = TextStyle(
    fontSize: 18,
    fontWeight: FontWeight.w600,
    color: WidgetColors.textPrimary,
    fontFamily: fontFamily,
    height: 1.3,
  );

  // ============================================================================
  // BODY TEXT STYLES
  // ============================================================================
  
  /// Large body text for main content (16px, regular)
  static const TextStyle bodyLarge = TextStyle(
    fontSize: 16,
    fontWeight: FontWeight.w400,
    color: WidgetColors.textPrimary,
    fontFamily: fontFamily,
    height: 1.5,
  );
  
  /// Medium body text for secondary content (14px, regular)
  static const TextStyle bodyMedium = TextStyle(
    fontSize: 14,
    fontWeight: FontWeight.w400,
    color: WidgetColors.textPrimary,
    fontFamily: fontFamily,
    height: 1.4,
  );
  
  /// Small body text for captions and labels (12px, regular)
  static const TextStyle bodySmall = TextStyle(
    fontSize: 12,
    fontWeight: FontWeight.w400,
    color: WidgetColors.textSecondary,
    fontFamily: fontFamily,
    height: 1.4,
  );

  // ============================================================================
  // BUTTON TEXT STYLES
  // ============================================================================
  
  /// Large button text (16px, semi-bold)
  static const TextStyle buttonLarge = TextStyle(
    fontSize: 16,
    fontWeight: FontWeight.w600,
    color: Colors.white,
    fontFamily: fontFamily,
    height: 1.2,
  );
  
  /// Medium button text (14px, semi-bold)
  static const TextStyle buttonMedium = TextStyle(
    fontSize: 14,
    fontWeight: FontWeight.w600,
    color: Colors.white,
    fontFamily: fontFamily,
    height: 1.2,
  );
  
  /// Small button text (12px, semi-bold)
  static const TextStyle buttonSmall = TextStyle(
    fontSize: 12,
    fontWeight: FontWeight.w600,
    color: Colors.white,
    fontFamily: fontFamily,
    height: 1.2,
  );

  // ============================================================================
  // INPUT TEXT STYLES
  // ============================================================================
  
  /// Input field text (16px, regular)
  static const TextStyle input = TextStyle(
    fontSize: 16,
    fontWeight: FontWeight.w400,
    color: WidgetColors.textPrimary,
    fontFamily: fontFamily,
    height: 1.4,
  );
  
  /// Input field placeholder text (16px, regular, secondary color)
  static const TextStyle inputPlaceholder = TextStyle(
    fontSize: 16,
    fontWeight: FontWeight.w400,
    color: WidgetColors.textSecondary,
    fontFamily: fontFamily,
    height: 1.4,
  );
  
  /// Input field label text (14px, medium)
  static const TextStyle inputLabel = TextStyle(
    fontSize: 14,
    fontWeight: FontWeight.w500,
    color: WidgetColors.textPrimary,
    fontFamily: fontFamily,
    height: 1.3,
  );

  // ============================================================================
  // FEEDBACK TEXT STYLES
  // ============================================================================
  
  /// Success message text (14px, medium, success color)
  static const TextStyle success = TextStyle(
    fontSize: 14,
    fontWeight: FontWeight.w500,
    color: WidgetColors.success,
    fontFamily: fontFamily,
    height: 1.4,
  );
  
  /// Error message text (14px, medium, error color)
  static const TextStyle error = TextStyle(
    fontSize: 14,
    fontWeight: FontWeight.w500,
    color: WidgetColors.error,
    fontFamily: fontFamily,
    height: 1.4,
  );
  
  /// Warning message text (14px, medium, warning color)
  static const TextStyle warning = TextStyle(
    fontSize: 14,
    fontWeight: FontWeight.w500,
    color: WidgetColors.warning,
    fontFamily: fontFamily,
    height: 1.4,
  );
  
  /// Info message text (14px, medium, info color)
  static const TextStyle info = TextStyle(
    fontSize: 14,
    fontWeight: FontWeight.w500,
    color: WidgetColors.info,
    fontFamily: fontFamily,
    height: 1.4,
  );

  // ============================================================================
  // SPECIAL TEXT STYLES
  // ============================================================================
  
  /// Caption text for small labels (10px, regular, secondary color)
  static const TextStyle caption = TextStyle(
    fontSize: 10,
    fontWeight: FontWeight.w400,
    color: WidgetColors.textSecondary,
    fontFamily: fontFamily,
    height: 1.3,
  );
  
  /// Overline text for categories and tags (10px, bold, uppercase)
  static const TextStyle overline = TextStyle(
    fontSize: 10,
    fontWeight: FontWeight.bold,
    color: WidgetColors.textSecondary,
    fontFamily: fontFamily,
    height: 1.3,
    letterSpacing: 1.5,
  );
  
  /// Code text for mathematical expressions (14px, monospace)
  static const TextStyle code = TextStyle(
    fontSize: 14,
    fontWeight: FontWeight.w400,
    color: WidgetColors.textPrimary,
    fontFamily: 'monospace',
    height: 1.4,
  );

  // ============================================================================
  // HELPER METHODS
  // ============================================================================
  
  /// Create a text style with a specific color
  static TextStyle withColor(TextStyle style, Color color) {
    return style.copyWith(color: color);
  }
  
  /// Create a text style with a specific font size
  static TextStyle withSize(TextStyle style, double fontSize) {
    return style.copyWith(fontSize: fontSize);
  }
  
  /// Create a text style with a specific font weight
  static TextStyle withWeight(TextStyle style, FontWeight fontWeight) {
    return style.copyWith(fontWeight: fontWeight);
  }
  
  /// Create a text style with category color
  static TextStyle withCategoryColor(TextStyle style, String categoryId) {
    return style.copyWith(color: WidgetColors.getCategoryColor(categoryId));
  }
  
  /// Create a text style with opacity
  static TextStyle withOpacity(TextStyle style, double opacity) {
    return style.copyWith(color: style.color?.withOpacity(opacity));
  }

  // ============================================================================
  // WIDGET-SPECIFIC STYLES
  // ============================================================================
  
  /// Question text in interactive widgets (18px, medium)
  static const TextStyle question = TextStyle(
    fontSize: 18,
    fontWeight: FontWeight.w500,
    color: WidgetColors.textPrimary,
    fontFamily: fontFamily,
    height: 1.4,
  );
  
  /// Answer option text (16px, regular)
  static const TextStyle answerOption = TextStyle(
    fontSize: 16,
    fontWeight: FontWeight.w400,
    color: WidgetColors.textPrimary,
    fontFamily: fontFamily,
    height: 1.4,
  );
  
  /// Feedback text for answers (14px, medium)
  static const TextStyle feedback = TextStyle(
    fontSize: 14,
    fontWeight: FontWeight.w500,
    color: WidgetColors.textPrimary,
    fontFamily: fontFamily,
    height: 1.4,
  );
  
  /// Progress text (12px, medium)
  static const TextStyle progress = TextStyle(
    fontSize: 12,
    fontWeight: FontWeight.w500,
    color: WidgetColors.textSecondary,
    fontFamily: fontFamily,
    height: 1.3,
  );
  
  /// Hint text (14px, italic, secondary color)
  static const TextStyle hint = TextStyle(
    fontSize: 14,
    fontWeight: FontWeight.w400,
    fontStyle: FontStyle.italic,
    color: WidgetColors.textSecondary,
    fontFamily: fontFamily,
    height: 1.4,
  );
}
