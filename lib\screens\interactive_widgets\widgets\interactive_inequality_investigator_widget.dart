import 'package:flutter/material.dart';
import 'dart:math' as math;

/// A widget that tests students' understanding of inequalities through a series of challenges.
class InteractiveInequalityInvestigatorWidget extends StatefulWidget {
  final Map<String, dynamic> data;
  final Function(bool)? onStateChanged;
  final Color primaryColor;
  final Color secondaryColor;
  final Color backgroundColor;
  final Color textColor;
  final Color successColor;
  final Color errorColor;

  const InteractiveInequalityInvestigatorWidget({
    super.key,
    this.data = const {},
    this.onStateChanged,
    this.primaryColor = Colors.blue,
    this.secondaryColor = Colors.orange,
    this.backgroundColor = Colors.white,
    this.textColor = Colors.black87,
    this.successColor = Colors.green,
    this.errorColor = Colors.red,
  });

  @override
  State<InteractiveInequalityInvestigatorWidget> createState() =>
      _InteractiveInequalityInvestigatorWidgetState();
}

class _InteractiveInequalityInvestigatorWidgetState
    extends State<InteractiveInequalityInvestigatorWidget> with SingleTickerProviderStateMixin {
  // State variables
  bool _isCompleted = false;
  int _currentQuestionIndex = 0;
  List<InequalityQuestion> _questions = [];
  late InequalityQuestion _currentQuestion;

  // User input
  String _userAnswer = '';
  TextEditingController _answerController = TextEditingController();
  String? _feedbackMessage;
  bool _isCorrect = false;

  // Score tracking
  int _score = 0;
  int _totalQuestions = 0;

  // Animation controller for feedback
  late AnimationController _animationController;
  late Animation<double> _animation;

  @override
  void initState() {
    super.initState();
    _initializeQuestions();
    _currentQuestion = _questions[_currentQuestionIndex];
    _totalQuestions = _questions.length;

    // Initialize animation controller
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 300),
      vsync: this,
    );

    _animation = CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeInOut,
    );
  }

  @override
  void dispose() {
    _answerController.dispose();
    _animationController.dispose();
    super.dispose();
  }

  void _initializeQuestions() {
    // Check if questions are provided in the data
    if (widget.data.containsKey('questions') &&
        widget.data['questions'] is List &&
        widget.data['questions'].isNotEmpty) {

      final questionsData = widget.data['questions'] as List;
      for (final questionData in questionsData) {
        if (questionData is Map<String, dynamic>) {
          final question = InequalityQuestion.fromJson(questionData);
          _questions.add(question);
        }
      }
    }

    // If no questions were provided, create default ones
    if (_questions.isEmpty) {
      _questions = [
        InequalityQuestion(
          questionText: "Solve for x: x - 7 > 3",
          answerType: "text_input",
          correctAnswer: "x > 10",
          options: [],
          explanation: "Add 7 to both sides: x > 10",
          hint: "Add 7 to both sides.",
        ),
        InequalityQuestion(
          questionText: "Which symbol means 'less than or equal to'?",
          answerType: "multiple_choice",
          correctAnswer: "≤",
          options: ["<", ">", "≤", "≥"],
          explanation: "≤ means less than or equal to.",
          hint: "Think about which symbol includes equality.",
        ),
        InequalityQuestion(
          questionText: "Solve for y: -5y ≤ 20",
          answerType: "text_input",
          correctAnswer: "y ≥ -4",
          options: [],
          explanation: "Divide by -5 and FLIP the inequality sign: y ≥ -4",
          hint: "Remember to flip the sign when dividing by a negative number.",
        ),
        InequalityQuestion(
          questionText: "Which graph represents x < 1?",
          answerType: "multiple_choice",
          correctAnswer: "Open circle at 1, arrow to the left",
          options: [
            "Open circle at 1, arrow to the right",
            "Closed circle at 1, arrow to the right",
            "Open circle at 1, arrow to the left",
            "Closed circle at 1, arrow to the left"
          ],
          explanation: "For x < 1, we use an open circle at 1 (not included) and an arrow pointing left (less than).",
          hint: "Think about whether 1 is included in the solution and which direction 'less than' points.",
        ),
        InequalityQuestion(
          questionText: "Solve for z: (z/2) + 3 ≥ 5",
          answerType: "text_input",
          correctAnswer: "z ≥ 4",
          options: [],
          explanation: "Subtract 3 from both sides: z/2 ≥ 2. Then multiply by 2: z ≥ 4",
          hint: "First isolate the term with z, then multiply to isolate z.",
        ),
      ];
    }
  }

  void _checkAnswer() {
    if (_userAnswer.isEmpty) return;

    bool isCorrect = false;

    if (_currentQuestion.answerType == "text_input") {
      // Normalize answers for comparison (remove spaces)
      String normalizedUserAnswer = _userAnswer.replaceAll(' ', '').toLowerCase();
      String normalizedCorrectAnswer = _currentQuestion.correctAnswer.replaceAll(' ', '').toLowerCase();

      isCorrect = normalizedUserAnswer == normalizedCorrectAnswer;
    } else if (_currentQuestion.answerType == "multiple_choice") {
      isCorrect = _userAnswer == _currentQuestion.correctAnswer;
    }

    setState(() {
      _isCorrect = isCorrect;

      if (isCorrect) {
        _score++;
        _feedbackMessage = "Correct! ${_currentQuestion.explanation}";
      } else {
        _feedbackMessage = "Not quite. ${_currentQuestion.hint}";
      }

      // Clear the input field
      _userAnswer = '';
      _answerController.clear();
    });

    // Start the animation
    _animationController.forward(from: 0.0);
  }

  void _nextQuestion() {
    if (_currentQuestionIndex < _questions.length - 1) {
      setState(() {
        _currentQuestionIndex++;
        _currentQuestion = _questions[_currentQuestionIndex];
        _feedbackMessage = null;
        _isCorrect = false;
      });
    } else {
      // All questions completed
      setState(() {
        _isCompleted = true;
      });
      widget.onStateChanged?.call(true);
    }
  }

  void _resetWidget() {
    setState(() {
      _currentQuestionIndex = 0;
      _currentQuestion = _questions[_currentQuestionIndex];
      _feedbackMessage = null;
      _isCorrect = false;
      _score = 0;
      _isCompleted = false;
    });
    widget.onStateChanged?.call(false);
  }

  @override
  Widget build(BuildContext context) {
    if (_isCompleted) {
      return _buildCompletionScreen();
    }

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Title and progress
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Text(
              'Inequality Investigator',
              style: TextStyle(
                fontSize: 20,
                fontWeight: FontWeight.bold,
                color: widget.textColor,
              ),
            ),
            Text(
              'Question ${_currentQuestionIndex + 1} of $_totalQuestions',
              style: TextStyle(
                fontSize: 16,
                color: widget.textColor,
              ),
            ),
          ],
        ),

        const SizedBox(height: 16),

        // Progress bar
        LinearProgressIndicator(
          value: (_currentQuestionIndex + 1) / _totalQuestions,
          backgroundColor: Colors.grey[300],
          valueColor: AlwaysStoppedAnimation<Color>(widget.primaryColor),
        ),

        const SizedBox(height: 24),

        // Question
        Container(
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            color: widget.backgroundColor,
            borderRadius: BorderRadius.circular(8),
            border: Border.all(color: widget.primaryColor),
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                _currentQuestion.questionText,
                style: TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.w500,
                  color: widget.textColor,
                ),
              ),
            ],
          ),
        ),

        const SizedBox(height: 24),

        // Answer input
        if (_currentQuestion.answerType == "text_input") ...[
          TextField(
            controller: _answerController,
            decoration: InputDecoration(
              labelText: 'Your answer',
              hintText: 'e.g., x > 5',
              border: OutlineInputBorder(
                borderRadius: BorderRadius.circular(8),
              ),
              suffixIcon: IconButton(
                icon: const Icon(Icons.check),
                onPressed: () {
                  _userAnswer = _answerController.text;
                  _checkAnswer();
                },
              ),
            ),
            onChanged: (value) {
              _userAnswer = value;
            },
            onSubmitted: (value) {
              _userAnswer = value;
              _checkAnswer();
            },
          ),
        ] else if (_currentQuestion.answerType == "multiple_choice") ...[
          ...List.generate(
            _currentQuestion.options.length,
            (index) => Padding(
              padding: const EdgeInsets.only(bottom: 8),
              child: RadioListTile<String>(
                title: Text(_currentQuestion.options[index]),
                value: _currentQuestion.options[index],
                groupValue: _userAnswer,
                onChanged: (value) {
                  setState(() {
                    _userAnswer = value!;
                  });
                },
                activeColor: widget.primaryColor,
              ),
            ),
          ),
          const SizedBox(height: 16),
          ElevatedButton(
            onPressed: _checkAnswer,
            style: ElevatedButton.styleFrom(
              backgroundColor: widget.primaryColor,
              foregroundColor: Colors.white,
              padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
            ),
            child: const Text('Check Answer'),
          ),
        ],

        const SizedBox(height: 24),

        // Feedback message
        if (_feedbackMessage != null) ...[
          AnimatedBuilder(
            animation: _animation,
            builder: (context, child) {
              return Container(
                padding: const EdgeInsets.all(16),
                decoration: BoxDecoration(
                  color: _isCorrect
                      ? widget.successColor.withOpacity(0.1)
                      : widget.errorColor.withOpacity(0.1),
                  borderRadius: BorderRadius.circular(8),
                  border: Border.all(
                    color: _isCorrect ? widget.successColor : widget.errorColor,
                  ),
                ),
                child: Row(
                  children: [
                    Icon(
                      _isCorrect ? Icons.check_circle : Icons.error,
                      color: _isCorrect ? widget.successColor : widget.errorColor,
                    ),
                    const SizedBox(width: 8),
                    Expanded(
                      child: Text(
                        _feedbackMessage!,
                        style: TextStyle(
                          color: _isCorrect ? widget.successColor : widget.errorColor,
                        ),
                      ),
                    ),
                  ],
                ),
              );
            },
          ),
          const SizedBox(height: 24),
        ],

        // Next button
        Row(
          mainAxisAlignment: MainAxisAlignment.end,
          children: [
            ElevatedButton.icon(
              icon: const Icon(Icons.arrow_forward),
              label: Text(_currentQuestionIndex < _questions.length - 1 ? 'Next Question' : 'Finish'),
              onPressed: _isCorrect ? _nextQuestion : null,
              style: ElevatedButton.styleFrom(
                backgroundColor: widget.primaryColor,
                foregroundColor: Colors.white,
                disabledBackgroundColor: Colors.grey.shade300,
                padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
              ),
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildCompletionScreen() {
    final percentage = (_score / _totalQuestions) * 100;
    String message;

    if (percentage >= 90) {
      message = 'Excellent! You\'re an inequality master!';
    } else if (percentage >= 70) {
      message = 'Great job! You have a solid understanding of inequalities.';
    } else if (percentage >= 50) {
      message = 'Good effort! Keep practicing to improve your skills.';
    } else {
      message = 'Keep practicing! Inequalities take time to master.';
    }

    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.emoji_events,
            color: widget.successColor,
            size: 64,
          ),
          const SizedBox(height: 16),
          Text(
            'Investigation Complete!',
            style: TextStyle(
              fontSize: 24,
              fontWeight: FontWeight.bold,
              color: widget.textColor,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            'Your Score: $_score/$_totalQuestions (${percentage.toInt()}%)',
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
              color: widget.primaryColor,
            ),
          ),
          const SizedBox(height: 16),
          Text(
            message,
            textAlign: TextAlign.center,
            style: TextStyle(
              fontSize: 16,
              color: widget.textColor,
            ),
          ),
          const SizedBox(height: 24),
          ElevatedButton.icon(
            icon: const Icon(Icons.refresh),
            label: const Text('Try Again'),
            onPressed: _resetWidget,
            style: ElevatedButton.styleFrom(
              backgroundColor: widget.primaryColor,
              foregroundColor: Colors.white,
              padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
            ),
          ),
        ],
      ),
    );
  }
}

/// Data class for inequality questions
class InequalityQuestion {
  final String questionText;
  final String answerType; // "text_input" or "multiple_choice"
  final String correctAnswer;
  final List<String> options; // For multiple choice questions
  final String explanation;
  final String hint;

  InequalityQuestion({
    required this.questionText,
    required this.answerType,
    required this.correctAnswer,
    required this.options,
    required this.explanation,
    required this.hint,
  });

  factory InequalityQuestion.fromJson(Map<String, dynamic> json) {
    return InequalityQuestion(
      questionText: (json['questionText'] as String?) ?? '',
      answerType: (json['answerType'] as String?) ?? 'text_input',
      correctAnswer: (json['correctAnswer'] as String?) ?? '',
      options: (json['options'] as List<dynamic>?)?.map((e) => e.toString()).toList() ?? <String>[],
      explanation: (json['explanation'] as String?) ?? '',
      hint: (json['hint'] as String?) ?? '',
    );
  }
}
