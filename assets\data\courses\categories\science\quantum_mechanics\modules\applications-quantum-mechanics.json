{
  "id": "applications-quantum-mechanics",
  "title": "APPLICATIONS OF QUANTUM MECHANICS",
  "description": "Explore the diverse and rapidly evolving technological applications of quantum mechanical principles.",
  "order": 5,
  "lessons": [
    {
      "id": "lasers",
      "title": "Lasers: Coherent Light Amplification",
      "description": "Understand the quantum basis of laser operation.",
      "order": 1,
      "type": "interactive_lesson",
      "estimatedTimeMinutes": 10,
      "contentBlocks": [
        {
          "id": "laser-screen1-intro",
          "type": "lesson_screen",
          "order": 1,
          "estimatedTimeSeconds": 30,
          "content": {
            "headline": "From Quantum Theory to Everyday Technology",
            "body_md": "Lasers are one of the most successful applications of quantum mechanics, transforming from a laboratory curiosity to a technology that touches nearly every aspect of modern life—from barcode scanners to eye surgery, from fiber-optic communications to cutting-edge scientific research.",
            "visual": {
              "type": "giphy_search",
              "value": "laser beam"
            },
            "hook": "Let's explore how quantum mechanics makes these remarkable devices possible!",
            "interactive_element": {
              "type": "button",
              "text": "What is a laser?",
              "action": "next_screen"
            }
          }
        },
        {
          "id": "laser-screen2-what-is",
          "type": "lesson_screen",
          "order": 2,
          "estimatedTimeSeconds": 50,
          "content": {
            "headline": "What Makes Laser Light Special?",
            "body_md": "The word \"laser\" is an acronym for **Light Amplification by Stimulated Emission of Radiation**.\n\nLaser light has several unique properties that distinguish it from ordinary light:\n\n• **Monochromatic**: Contains a single wavelength or color\n• **Coherent**: All light waves are in phase with each other\n• **Directional**: Travels in a tight, focused beam\n• **High intensity**: Can be extremely powerful when focused\n\nThese properties arise directly from quantum mechanical principles.",
            "visual": {
              "type": "unsplash_search",
              "value": "laser beam comparison"
            },
            "interactive_element": {
              "type": "button",
              "text": "How do lasers work?",
              "action": "next_screen"
            }
          }
        },
        {
          "id": "laser-screen3-quantum-basis",
          "type": "lesson_screen",
          "order": 3,
          "estimatedTimeSeconds": 70,
          "content": {
            "headline": "The Quantum Basis of Lasers",
            "body_md": "Lasers operate based on three key quantum processes:\n\n1. **Absorption**: An electron absorbs a photon and jumps to a higher energy level\n\n2. **Spontaneous emission**: An excited electron randomly drops to a lower energy level, emitting a photon in a random direction with random phase\n\n3. **Stimulated emission**: An excited electron is stimulated by an incoming photon to emit a second photon with identical properties (same wavelength, direction, and phase)\n\nStimulated emission, predicted by Einstein in 1917, is the crucial quantum process that makes lasers possible.",
            "interactive_element": {
              "type": "multiple_choice_text",
              "question_text": "Which quantum process is most essential for laser operation?",
              "options": [
                {"id": "laser3opt1", "text": "Absorption", "is_correct": false, "feedback_incorrect": "While absorption is involved in laser operation, it's not the defining process that makes lasers unique."},
                {"id": "laser3opt2", "text": "Spontaneous emission", "is_correct": false, "feedback_incorrect": "Spontaneous emission actually works against laser operation by producing random, incoherent photons."},
                {"id": "laser3opt3", "text": "Stimulated emission", "is_correct": true, "feedback_correct": "Correct! Stimulated emission is the key quantum process that produces the coherent, in-phase photons that make laser light special.", "feedback_incorrect": "Think about which process produces photons with identical properties (wavelength, direction, and phase)."}
              ],
              "action_button_text": "Continue"
            }
          }
        },
        {
          "id": "laser-screen4-components",
          "type": "lesson_screen",
          "order": 4,
          "estimatedTimeSeconds": 60,
          "content": {
            "headline": "Basic Components of a Laser",
            "body_md": "All lasers share three essential components:\n\n1. **Gain medium**: Material (gas, liquid, solid, or semiconductor) containing atoms that can be excited to produce stimulated emission\n\n2. **Pump source**: Energy source (electrical current, flash lamp, or another laser) that excites atoms in the gain medium\n\n3. **Optical resonator**: Typically two mirrors (one fully reflective, one partially reflective) that bounce photons back and forth through the gain medium, amplifying the light\n\nThese components work together to produce a coherent beam of laser light.",
            "visual": {
              "type": "giphy_search",
              "value": "laser components animation"
            },
            "interactive_element": {
              "type": "button",
              "text": "How does it all work together?",
              "action": "next_screen"
            }
          }
        },
        {
          "id": "laser-screen5-operation",
          "type": "lesson_screen",
          "order": 5,
          "estimatedTimeSeconds": 70,
          "content": {
            "headline": "Laser Operation: Step by Step",
            "body_md": "Here's how a laser generates its beam:\n\n1. The pump source provides energy to the gain medium, exciting electrons to higher energy states (creating a **population inversion** where more atoms are in excited states than ground states)\n\n2. Some excited atoms spontaneously emit photons in random directions\n\n3. Photons traveling along the axis of the resonator bounce between mirrors, passing through the gain medium repeatedly\n\n4. These photons stimulate the emission of more identical photons from excited atoms (same wavelength, direction, and phase)\n\n5. The process cascades, creating an amplified beam of coherent light\n\n6. Some light escapes through the partially reflective mirror as the laser beam",
            "interactive_element": {
              "type": "interactive",
              "interactiveType": "sequenceChallenge",
              "data": {
                "title": "Laser Operation Sequence",
                "instruction": "Arrange these steps in the correct order of laser operation:",
                "sequenceType": "text",
                "correctSequence": [
                  "Energy is supplied to the gain medium by the pump source",
                  "Electrons in the gain medium are excited to higher energy states",
                  "Population inversion is achieved (more atoms in excited states than ground states)",
                  "Some excited atoms spontaneously emit photons",
                  "Photons traveling along the resonator axis stimulate emission of identical photons",
                  "Stimulated emission cascades, creating an amplified coherent beam",
                  "Some light escapes through the partially reflective mirror as the laser beam"
                ],
                "explanation": "This sequence shows how energy from the pump source is converted into a coherent laser beam through the quantum process of stimulated emission."
              }
            }
          }
        },
        {
          "id": "laser-screen6-types",
          "type": "lesson_screen",
          "order": 6,
          "estimatedTimeSeconds": 60,
          "content": {
            "headline": "Types of Lasers",
            "body_md": "Lasers are classified by their gain medium, which determines many of their properties:\n\n• **Gas lasers** (He-Ne, CO₂, excimer): Use gases as the gain medium\n\n• **Solid-state lasers** (ruby, Nd:YAG): Use crystals or glasses doped with ions\n\n• **Semiconductor lasers** (diode lasers): Use p-n junctions in semiconductor materials\n\n• **Dye lasers**: Use organic dyes in liquid solution\n\n• **Fiber lasers**: Use optical fibers doped with rare-earth elements\n\n• **Free-electron lasers**: Use relativistic electrons moving through magnetic fields\n\nEach type has unique characteristics suited for different applications.",
            "interactive_element": {
              "type": "interactive",
              "interactiveType": "conditionalMatcher",
              "data": {
                "title": "Laser Types and Applications",
                "instruction": "Match each laser type with its common application:",
                "conditions": [
                  "CO₂ laser",
                  "Excimer laser",
                  "Semiconductor diode laser",
                  "Nd:YAG laser",
                  "Ti:Sapphire laser"
                ],
                "outcomes": [
                  "Industrial cutting and welding of materials",
                  "LASIK eye surgery",
                  "Barcode scanners and optical disk readers",
                  "Laser pointers and range finders",
                  "Ultrafast spectroscopy and research"
                ],
                "correctMatches": [
                  [0, 0],
                  [1, 1],
                  [2, 2],
                  [3, 3],
                  [4, 4]
                ],
                "explanation": "Different laser types produce light with different wavelengths, power levels, and pulse characteristics, making them suitable for specific applications."
              }
            }
          }
        },
        {
          "id": "laser-screen7-applications",
          "type": "lesson_screen",
          "order": 7,
          "estimatedTimeSeconds": 50,
          "content": {
            "headline": "Applications of Lasers",
            "body_md": "Lasers have revolutionized countless fields:\n\n• **Medicine**: Surgery, dermatology, dentistry, cancer treatment\n\n• **Communications**: Fiber-optic networks, satellite communications\n\n• **Manufacturing**: Cutting, welding, 3D printing, precision measurement\n\n• **Consumer electronics**: Barcode scanners, printers, DVD/Blu-ray players\n\n• **Scientific research**: Spectroscopy, atomic clocks, gravitational wave detection\n\n• **Military**: Guidance systems, range finding, directed energy weapons\n\n• **Entertainment**: Light shows, holograms, laser pointers\n\nFew technologies based on quantum mechanics have had such widespread impact.",
            "visual": {
              "type": "unsplash_search",
              "value": "laser surgery"
            },
            "interactive_element": {
              "type": "button",
              "text": "Let's review what we've learned",
              "action": "next_screen"
            }
          }
        },
        {
          "id": "laser-screen8-recap",
          "type": "lesson_screen",
          "order": 8,
          "estimatedTimeSeconds": 40,
          "content": {
            "headline": "Key Takeaways",
            "body_md": "• Lasers produce light that is **monochromatic**, **coherent**, **directional**, and can be very **intense**\n• They operate based on the quantum process of **stimulated emission**\n• All lasers have a **gain medium**, **pump source**, and **optical resonator**\n• Laser operation requires a **population inversion** (more atoms in excited states than ground states)\n• Different **types of lasers** use different gain media and have different properties\n• Lasers have countless **applications** across medicine, communications, manufacturing, and research",
            "interactive_element": {
              "type": "button",
              "text": "Next Lesson: Semiconductors and Transistors",
              "action": "next_lesson"
            }
          }
        }
      ]
    },
    {
      "id": "semiconductors-transistors",
      "title": "Semiconductors and Transistors",
      "description": "Explore the quantum behavior of electrons in materials.",
      "order": 2,
      "type": "interactive_lesson",
      "estimatedTimeMinutes": 10,
      "contentBlocks": [
        {
          "id": "semi-screen1-intro",
          "type": "lesson_screen",
          "order": 1,
          "estimatedTimeSeconds": 30,
          "content": {
            "headline": "The Quantum Revolution in Computing",
            "body_md": "The modern digital age—computers, smartphones, the internet—rests on a foundation of semiconductor devices whose operation depends entirely on quantum mechanics. Without an understanding of quantum behavior, these technologies would be impossible.",
            "visual": {
              "type": "giphy_search",
              "value": "computer chip transistor"
            },
            "hook": "Let's explore how quantum physics enables the microchips that power our digital world!",
            "interactive_element": {
              "type": "button",
              "text": "What are semiconductors?",
              "action": "next_screen"
            }
          }
        },
        {
          "id": "semi-screen2-what-are",
          "type": "lesson_screen",
          "order": 2,
          "estimatedTimeSeconds": 50,
          "content": {
            "headline": "What Are Semiconductors?",
            "body_md": "**Semiconductors** are materials with electrical conductivity between that of conductors (like metals) and insulators (like glass).\n\nCommon semiconductor materials include:\n• Silicon (Si) - most widely used\n• Germanium (Ge) - used in early transistors\n• Gallium arsenide (GaAs) - used in high-speed devices\n• Silicon carbide (SiC) - used in high-power applications\n\nTheir unique properties arise from their crystal structure and quantum mechanical behavior of electrons within them.",
            "visual": {
              "type": "unsplash_search",
              "value": "silicon wafer"
            },
            "interactive_element": {
              "type": "button",
              "text": "How do they work?",
              "action": "next_screen"
            }
          }
        },
        {
          "id": "semi-screen3-band-theory",
          "type": "lesson_screen",
          "order": 3,
          "estimatedTimeSeconds": 70,
          "content": {
            "headline": "Quantum Band Theory",
            "body_md": "The behavior of semiconductors is explained by quantum **band theory**:\n\n• When atoms form a solid, their discrete energy levels merge into continuous **energy bands**\n\n• The **valence band** contains electrons bound to atoms\n\n• The **conduction band** contains electrons free to move and conduct electricity\n\n• The **band gap** is the energy difference between these bands\n\n• In conductors: bands overlap (easy conduction)\n• In insulators: large band gap (difficult conduction)\n• In semiconductors: small band gap (controllable conduction)",
            "interactive_element": {
              "type": "interactive",
              "interactiveType": "graph-plotter",
              "data": {
                "title": "Energy Band Structures",
                "xLabel": "Material Type",
                "yLabel": "Energy",
                "xRange": [0, 3],
                "yRange": [0, 6],
                "regions": [
                  {"x1": 0, "y1": 0, "x2": 1, "y2": 3, "label": "Valence Band", "color": "blue"},
                  {"x1": 0, "y1": 2.5, "x2": 1, "y2": 6, "label": "Conduction Band", "color": "red"},
                  {"x1": 1, "y1": 0, "x2": 2, "y2": 2, "label": "Valence Band", "color": "blue"},
                  {"x1": 1, "y1": 3, "x2": 2, "y2": 5, "label": "Conduction Band", "color": "red"},
                  {"x1": 2, "y1": 0, "x2": 3, "y2": 1, "label": "Valence Band", "color": "blue"},
                  {"x1": 2, "y1": 5, "x2": 3, "y2": 6, "label": "Conduction Band", "color": "red"}
                ],
                "labels": [
                  {"x": 0.5, "y": 3.5, "text": "Conductor"},
                  {"x": 1.5, "y": 2.5, "text": "Semiconductor"},
                  {"x": 2.5, "y": 3, "text": "Insulator"}
                ],
                "interactive": "Note how the band gap increases from conductors (overlapping bands) to semiconductors (small gap) to insulators (large gap)."
              }
            }
          }
        },
        {
          "id": "semi-screen4-doping",
          "type": "lesson_screen",
          "order": 4,
          "estimatedTimeSeconds": 60,
          "content": {
            "headline": "Semiconductor Doping",
            "body_md": "Pure semiconductors have limited conductivity, but their properties can be dramatically altered through **doping**—intentionally adding impurities to the crystal structure:\n\n• **n-type doping**: Adding elements with extra valence electrons (like phosphorus to silicon)\n  - Creates excess electrons in the conduction band\n  - Electrons are the majority charge carriers\n\n• **p-type doping**: Adding elements with fewer valence electrons (like boron to silicon)\n  - Creates \"holes\" (electron vacancies) in the valence band\n  - Holes behave like positive charge carriers\n\nDoping is the key to creating useful semiconductor devices.",
            "visual": {
              "type": "giphy_search",
              "value": "semiconductor doping animation"
            },
            "interactive_element": {
              "type": "multiple_choice_text",
              "question_text": "What happens when silicon is doped with phosphorus?",
              "options": [
                {"id": "semi4opt1", "text": "It becomes p-type with holes as majority carriers", "is_correct": false, "feedback_incorrect": "Phosphorus has 5 valence electrons while silicon has 4, so it adds electrons, not holes."},
                {"id": "semi4opt2", "text": "It becomes n-type with electrons as majority carriers", "is_correct": true, "feedback_correct": "Correct! Phosphorus has 5 valence electrons compared to silicon's 4, so it contributes extra electrons to the crystal, creating n-type semiconductor material.", "feedback_incorrect": "Think about the number of valence electrons in phosphorus compared to silicon."},
                {"id": "semi4opt3", "text": "It becomes an insulator", "is_correct": false, "feedback_incorrect": "Doping actually increases conductivity rather than making the material an insulator."}
              ],
              "action_button_text": "Continue"
            }
          }
        },
        {
          "id": "semi-screen5-pn-junction",
          "type": "lesson_screen",
          "order": 5,
          "estimatedTimeSeconds": 70,
          "content": {
            "headline": "The p-n Junction: Building Block of Electronics",
            "body_md": "When p-type and n-type semiconductors are joined, they form a **p-n junction**—the fundamental building block of semiconductor devices.\n\nAt the junction:\n• Electrons from the n-side diffuse to the p-side\n• Holes from the p-side diffuse to the n-side\n• This creates a **depletion region** with no free charge carriers\n• An electric field forms across the depletion region\n\nThe p-n junction acts as a one-way valve for electric current:\n• Forward bias (p connected to +, n to -): Current flows easily\n• Reverse bias (p connected to -, n to +): Current is blocked",
            "interactive_element": {
              "type": "interactive",
              "interactiveType": "conditionalMatcher",
              "data": {
                "title": "p-n Junction Behavior",
                "instruction": "Match each condition with the correct behavior of a p-n junction:",
                "conditions": [
                  "Forward bias (positive voltage to p-side)",
                  "Reverse bias (negative voltage to p-side)",
                  "Zero bias (no voltage applied)",
                  "Reverse bias beyond breakdown voltage",
                  "Forward bias with increasing voltage"
                ],
                "outcomes": [
                  "Current flows easily as depletion region narrows",
                  "Almost no current flows as depletion region widens",
                  "Small depletion region forms, no current flows",
                  "Sudden large current flow (avalanche breakdown)",
                  "Current increases exponentially with voltage"
                ],
                "correctMatches": [
                  [0, 0],
                  [1, 1],
                  [2, 2],
                  [3, 3],
                  [4, 4]
                ],
                "explanation": "The behavior of a p-n junction under different bias conditions is the basis for diodes, transistors, and other semiconductor devices."
              }
            }
          }
        },
        {
          "id": "semi-screen6-transistors",
          "type": "lesson_screen",
          "order": 6,
          "estimatedTimeSeconds": 60,
          "content": {
            "headline": "Transistors: The Quantum Switches",
            "body_md": "**Transistors** are semiconductor devices that can amplify or switch electronic signals. The most common type today is the **MOSFET** (Metal-Oxide-Semiconductor Field-Effect Transistor).\n\nA MOSFET has three terminals:\n• **Source**: Where charge carriers enter\n• **Drain**: Where charge carriers exit\n• **Gate**: Controls the flow between source and drain\n\nBy applying voltage to the gate, we can control whether the transistor conducts (ON) or not (OFF). This binary switching is the basis of digital computing.\n\nModern microprocessors contain billions of transistors, each just nanometers in size.",
            "visual": {
              "type": "giphy_search",
              "value": "transistor animation"
            },
            "interactive_element": {
              "type": "button",
              "text": "What are the applications?",
              "action": "next_screen"
            }
          }
        },
        {
          "id": "semi-screen7-applications",
          "type": "lesson_screen",
          "order": 7,
          "estimatedTimeSeconds": 50,
          "content": {
            "headline": "Applications of Semiconductor Devices",
            "body_md": "Semiconductor devices are ubiquitous in modern technology:\n\n• **Computing**: CPUs, memory chips, solid-state drives\n\n• **Communications**: Smartphones, wireless transmitters, fiber optic systems\n\n• **Power electronics**: Voltage regulators, motor controllers, power converters\n\n• **Sensors**: Digital cameras, temperature sensors, touch screens\n\n• **Optoelectronics**: LEDs, solar cells, laser diodes\n\n• **Medical devices**: Pacemakers, hearing aids, diagnostic equipment\n\nThe semiconductor industry has enabled exponential growth in computing power (Moore's Law) for decades.",
            "visual": {
              "type": "unsplash_search",
              "value": "computer processor chip"
            },
            "interactive_element": {
              "type": "button",
              "text": "Let's review what we've learned",
              "action": "next_screen"
            }
          }
        },
        {
          "id": "semi-screen8-recap",
          "type": "lesson_screen",
          "order": 8,
          "estimatedTimeSeconds": 40,
          "content": {
            "headline": "Key Takeaways",
            "body_md": "• **Semiconductors** have conductivity between conductors and insulators\n• Their behavior is explained by quantum **band theory** with valence and conduction bands\n• **Doping** with impurities creates n-type (excess electrons) or p-type (excess holes) semiconductors\n• The **p-n junction** forms a one-way valve for electric current\n• **Transistors** use quantum effects to switch or amplify electronic signals\n• Semiconductor devices are the foundation of modern **computing, communications, and electronics**\n• Understanding quantum mechanics was essential for developing these technologies",
            "interactive_element": {
              "type": "button",
              "text": "Next Lesson: Quantum Computing",
              "action": "next_lesson"
            }
          }
        }
      ]
    },
    {
      "id": "quantum-computing",
      "title": "Quantum Computing: Beyond Classical Limits",
      "description": "Understand how quantum computers work and their potential.",
      "order": 3,
      "type": "interactive_lesson",
      "estimatedTimeMinutes": 10,
      "contentBlocks": [
        {
          "id": "qc-screen1-intro",
          "type": "lesson_screen",
          "order": 1,
          "estimatedTimeSeconds": 30,
          "content": {
            "headline": "Computing with Quantum Mechanics",
            "body_md": "Quantum computing represents one of the most exciting frontiers in technology—a fundamentally new paradigm that harnesses the strange properties of quantum mechanics to solve problems that would be practically impossible for classical computers.",
            "visual": {
              "type": "giphy_search",
              "value": "quantum computer"
            },
            "hook": "These machines don't just offer faster computing—they promise to transform fields from cryptography to drug discovery, materials science to artificial intelligence.",
            "interactive_element": {
              "type": "button",
              "text": "How are quantum computers different?",
              "action": "next_screen"
            }
          }
        },
        {
          "id": "qc-screen2-classical-vs-quantum",
          "type": "lesson_screen",
          "order": 2,
          "estimatedTimeSeconds": 50,
          "content": {
            "headline": "Classical vs. Quantum Computing",
            "body_md": "Classical and quantum computers differ fundamentally in how they process information:\n\n**Classical computers:**\n• Use bits (0 or 1) as the basic unit of information\n• Process information sequentially or with limited parallelism\n• Follow deterministic algorithms\n• Scale linearly or polynomially with problem size\n\n**Quantum computers:**\n• Use quantum bits or **qubits** that can exist in superpositions of 0 and 1\n• Process information in parallel across many quantum states\n• Utilize quantum effects like interference and entanglement\n• Can achieve exponential speedup for certain problems",
            "visual": {
              "type": "unsplash_search",
              "value": "quantum vs classical computing"
            },
            "interactive_element": {
              "type": "button",
              "text": "Tell me about qubits",
              "action": "next_screen"
            }
          }
        },
        {
          "id": "qc-screen3-qubits",
          "type": "lesson_screen",
          "order": 3,
          "estimatedTimeSeconds": 60,
          "content": {
            "headline": "Qubits: The Building Blocks",
            "body_md": "The **qubit** (quantum bit) is the fundamental unit of quantum information:\n\n• Unlike classical bits (which are either 0 or 1), a qubit can exist in a **superposition** of both states simultaneously\n\n• Mathematically, a qubit's state is represented as: |ψ⟩ = α|0⟩ + β|1⟩, where α and β are complex numbers with |α|² + |β|² = 1\n\n• When measured, a qubit \"collapses\" to either 0 or 1 with probabilities determined by α and β\n\n• Multiple qubits can be **entangled**, creating correlations that have no classical equivalent\n\n• n qubits can represent 2^n states simultaneously, giving quantum computers their power",
            "interactive_element": {
              "type": "multiple_choice_text",
              "question_text": "What happens when you measure a qubit in superposition?",
              "options": [
                {"id": "qc3opt1", "text": "You get both 0 and 1 simultaneously", "is_correct": false, "feedback_incorrect": "Measurement causes the superposition to collapse to a single definite state."},
                {"id": "qc3opt2", "text": "You get either 0 or 1 with probabilities determined by the superposition", "is_correct": true, "feedback_correct": "Correct! When measured, a qubit in superposition collapses to either 0 or 1, with probabilities determined by the amplitudes of the superposition state.", "feedback_incorrect": "Think about the measurement postulate in quantum mechanics."},
                {"id": "qc3opt3", "text": "You get a value between 0 and 1", "is_correct": false, "feedback_incorrect": "Qubits always measure as either 0 or 1, never as intermediate values."}
              ],
              "action_button_text": "Continue"
            }
          }
        },
        {
          "id": "qc-screen4-physical-implementation",
          "type": "lesson_screen",
          "order": 4,
          "estimatedTimeSeconds": 70,
          "content": {
            "headline": "Physical Implementations of Qubits",
            "body_md": "Qubits can be physically realized in various ways:\n\n• **Superconducting qubits**: Use Josephson junctions cooled to near absolute zero (used by IBM, Google)\n\n• **Trapped ion qubits**: Use electromagnetic fields to trap ions (used by IonQ, Honeywell)\n\n• **Photonic qubits**: Use polarization states of photons (used by Xanadu, PsiQuantum)\n\n• **Topological qubits**: Use exotic quasiparticles called anyons (researched by Microsoft)\n\n• **Spin qubits**: Use electron or nuclear spins (researched by Intel, various labs)\n\nEach approach has different advantages and challenges regarding coherence time, error rates, and scalability.",
            "visual": {
              "type": "giphy_search",
              "value": "superconducting qubit"
            },
            "interactive_element": {
              "type": "button",
              "text": "How do quantum algorithms work?",
              "action": "next_screen"
            }
          }
        },
        {
          "id": "qc-screen5-algorithms",
          "type": "lesson_screen",
          "order": 5,
          "estimatedTimeSeconds": 70,
          "content": {
            "headline": "Quantum Algorithms",
            "body_md": "Quantum algorithms exploit superposition, entanglement, and interference to solve specific problems more efficiently than classical algorithms:\n\n• **Shor's algorithm**: Exponentially faster factoring of large numbers, threatening current cryptography\n\n• **Grover's algorithm**: Quadratic speedup for searching unsorted databases\n\n• **Quantum Fourier Transform**: Efficiently performs Fourier transforms, a building block for many algorithms\n\n• **Quantum simulation**: Efficiently simulates quantum systems for chemistry and materials science\n\n• **Quantum machine learning**: Potential speedups for certain machine learning tasks\n\nMany of these algorithms require large-scale, error-corrected quantum computers that don't yet exist.",
            "interactive_element": {
              "type": "interactive",
              "interactiveType": "conditionalMatcher",
              "data": {
                "title": "Quantum Algorithms",
                "instruction": "Match each quantum algorithm with its primary application:",
                "conditions": [
                  "Shor's Algorithm",
                  "Grover's Algorithm",
                  "Quantum Phase Estimation",
                  "VQE (Variational Quantum Eigensolver)",
                  "QAOA (Quantum Approximate Optimization Algorithm)"
                ],
                "outcomes": [
                  "Factoring large numbers and breaking RSA encryption",
                  "Searching unsorted databases with quadratic speedup",
                  "Finding eigenvalues of quantum systems",
                  "Finding ground state energies of molecules",
                  "Solving combinatorial optimization problems"
                ],
                "correctMatches": [
                  [0, 0],
                  [1, 1],
                  [2, 2],
                  [3, 3],
                  [4, 4]
                ],
                "explanation": "Different quantum algorithms provide advantages for specific types of problems, often with no classical equivalent for efficient solution."
              }
            }
          }
        },
        {
          "id": "qc-screen6-challenges",
          "type": "lesson_screen",
          "order": 6,
          "estimatedTimeSeconds": 60,
          "content": {
            "headline": "Challenges in Quantum Computing",
            "body_md": "Despite rapid progress, quantum computing faces significant challenges:\n\n• **Decoherence**: Quantum states are extremely fragile and easily disturbed by environmental interactions\n\n• **Quantum error correction**: Requires many physical qubits to create one logical error-corrected qubit\n\n• **Scalability**: Building systems with many high-quality qubits is technically challenging\n\n• **Noise and errors**: Current quantum computers have high error rates limiting their capabilities\n\n• **Algorithm development**: Finding problems where quantum computers offer meaningful advantage\n\n• **Engineering challenges**: Requires extreme cooling, precise control systems, and specialized electronics",
            "visual": {
              "type": "unsplash_search",
              "value": "quantum computer cooling"
            },
            "interactive_element": {
              "type": "multiple_choice_text",
              "question_text": "What is one of the biggest challenges facing quantum computing today?",
              "options": [
                {"id": "qc6opt1", "text": "Making the computers smaller and more portable", "is_correct": false, "feedback_incorrect": "Size and portability are not primary concerns at this stage of development."},
                {"id": "qc6opt2", "text": "Quantum decoherence and error correction", "is_correct": true, "feedback_correct": "Correct! Quantum states are extremely fragile and easily disturbed by environmental interactions (decoherence). Developing effective error correction methods is one of the biggest challenges in building practical quantum computers.", "feedback_incorrect": "Think about what makes quantum computing fundamentally difficult from a physical perspective."},
                {"id": "qc6opt3", "text": "Finding enough programmers who understand quantum mechanics", "is_correct": false, "feedback_incorrect": "While quantum education is important, the fundamental technical challenges are more significant barriers."}
              ],
              "action_button_text": "Continue"
            }
          }
        },
        {
          "id": "qc-screen7-applications",
          "type": "lesson_screen",
          "order": 7,
          "estimatedTimeSeconds": 50,
          "content": {
            "headline": "Applications and Impact",
            "body_md": "Quantum computing has potential to transform many fields:\n\n• **Cryptography**: Breaking current encryption but also enabling quantum-secure cryptography\n\n• **Drug discovery**: Simulating molecular interactions to design new medicines\n\n• **Materials science**: Designing new materials with specific properties\n\n• **Optimization**: Solving complex logistics, financial, and resource allocation problems\n\n• **Artificial intelligence**: Potential speedups for specific machine learning tasks\n\n• **Climate modeling**: More accurate simulations of complex climate systems\n\n• **Financial modeling**: Better risk assessment and portfolio optimization\n\nMany of these applications are still theoretical and await more powerful quantum computers.",
            "visual": {
              "type": "giphy_search",
              "value": "molecular simulation"
            },
            "interactive_element": {
              "type": "button",
              "text": "Let's review what we've learned",
              "action": "next_screen"
            }
          }
        },
        {
          "id": "qc-screen8-recap",
          "type": "lesson_screen",
          "order": 8,
          "estimatedTimeSeconds": 40,
          "content": {
            "headline": "Key Takeaways",
            "body_md": "• **Quantum computers** use qubits that can exist in superpositions of states\n• They exploit quantum effects like **superposition**, **entanglement**, and **interference**\n• n qubits can represent **2^n states** simultaneously, giving exponential power\n• **Quantum algorithms** can solve certain problems exponentially faster than classical computers\n• Major **challenges** include decoherence, error correction, and scalability\n• Potential **applications** span cryptography, drug discovery, materials science, and optimization\n• Quantum computing is still in its early stages but advancing rapidly",
            "interactive_element": {
              "type": "button",
              "text": "Next Lesson: Quantum Cryptography",
              "action": "next_lesson"
            }
          }
        }
      ]
    },
    {
      "id": "quantum-cryptography",
      "title": "Quantum Cryptography: Unbreakable Codes",
      "description": "Learn how quantum mechanics enables secure communication.",
      "order": 4,
      "type": "interactive_lesson",
      "estimatedTimeMinutes": 10,
      "contentBlocks": [
        {
          "id": "qcrypto-screen1-intro",
          "type": "lesson_screen",
          "order": 1,
          "estimatedTimeSeconds": 30,
          "content": {
            "headline": "Securing Communications with Quantum Physics",
            "body_md": "In an age of increasing cyber threats, quantum cryptography offers something remarkable: communication security guaranteed by the laws of physics rather than mathematical complexity.",
            "visual": {
              "type": "giphy_search",
              "value": "secure encryption"
            },
            "hook": "Unlike conventional encryption, which can potentially be broken with enough computing power, quantum cryptography can create theoretically unbreakable encryption—and detect any attempt at eavesdropping.",
            "interactive_element": {
              "type": "button",
              "text": "How does it work?",
              "action": "next_screen"
            }
          }
        },
        {
          "id": "qcrypto-screen2-classical-crypto",
          "type": "lesson_screen",
          "order": 2,
          "estimatedTimeSeconds": 50,
          "content": {
            "headline": "Classical vs. Quantum Cryptography",
            "body_md": "**Classical cryptography** relies on mathematical problems that are computationally difficult to solve:\n\n• **Symmetric encryption**: Same key for encryption and decryption (e.g., AES)\n• **Asymmetric encryption**: Public key for encryption, private key for decryption (e.g., RSA)\n• Security depends on computational difficulty of problems like factoring large numbers\n• Potentially vulnerable to quantum computers (Shor's algorithm)\n\n**Quantum cryptography** uses the principles of quantum mechanics:\n\n• Security based on fundamental laws of physics, not computational complexity\n• Can detect any eavesdropping attempt due to quantum measurement effects\n• Immune to computational advances, including quantum computing",
            "visual": {
              "type": "unsplash_search",
              "value": "encryption key"
            },
            "interactive_element": {
              "type": "button",
              "text": "Tell me about quantum key distribution",
              "action": "next_screen"
            }
          }
        },
        {
          "id": "qcrypto-screen3-qkd",
          "type": "lesson_screen",
          "order": 3,
          "estimatedTimeSeconds": 70,
          "content": {
            "headline": "Quantum Key Distribution (QKD)",
            "body_md": "**Quantum Key Distribution (QKD)** is the most developed form of quantum cryptography. It allows two parties (traditionally called Alice and Bob) to generate a shared secret key with guaranteed security.\n\nKey principles of QKD:\n\n• Uses quantum states (like photon polarization) to transmit information\n• The **no-cloning theorem** prevents perfect copying of unknown quantum states\n• Any measurement by an eavesdropper (Eve) disturbs the quantum states\n• This disturbance can be detected by Alice and Bob\n• If no disturbance is detected, the key is guaranteed to be secure\n\nThe generated key can then be used with conventional encryption algorithms.",
            "interactive_element": {
              "type": "multiple_choice_text",
              "question_text": "Why is quantum key distribution secure against eavesdropping?",
              "options": [
                {"id": "qcrypto3opt1", "text": "It uses complex mathematical algorithms that are impossible to break", "is_correct": false, "feedback_incorrect": "QKD relies on physics, not mathematical complexity."},
                {"id": "qcrypto3opt2", "text": "Any attempt to measure the quantum states will inevitably disturb them in a detectable way", "is_correct": true, "feedback_correct": "Correct! According to quantum mechanics, measuring an unknown quantum state disturbs it. This means any eavesdropper will inevitably leave evidence of their intrusion, which can be detected by the legitimate users.", "feedback_incorrect": "Think about what happens when you measure a quantum system."},
                {"id": "qcrypto3opt3", "text": "The keys are transmitted faster than they can be intercepted", "is_correct": false, "feedback_incorrect": "Speed is not the security mechanism in QKD."}
              ],
              "action_button_text": "Continue"
            }
          }
        },
        {
          "id": "qcrypto-screen4-bb84",
          "type": "lesson_screen",
          "order": 4,
          "estimatedTimeSeconds": 70,
          "content": {
            "headline": "The BB84 Protocol",
            "body_md": "The first and most famous QKD protocol is **BB84**, developed by Bennett and Brassard in 1984. Here's how it works:\n\n1. **Quantum transmission**: Alice sends photons to Bob, randomly choosing between two bases (rectilinear or diagonal) and two states (0 or 1) for each photon\n\n2. **Measurement**: Bob measures each photon, randomly choosing between the two bases\n\n3. **Basis reconciliation**: Alice and Bob publicly compare which bases they used (but not the results)\n\n4. **Key sifting**: They keep only the results where they happened to use the same basis\n\n5. **Error estimation**: They check a sample of their results to detect any eavesdropping\n\n6. **Privacy amplification**: They apply techniques to eliminate any partial information an eavesdropper might have gained",
            "visual": {
              "type": "giphy_search",
              "value": "quantum key distribution"
            },
            "interactive_element": {
              "type": "interactive",
              "interactiveType": "sequenceChallenge",
              "data": {
                "title": "BB84 Protocol Steps",
                "instruction": "Arrange these steps of the BB84 protocol in the correct order:",
                "sequenceType": "text",
                "correctSequence": [
                  "Alice sends photons in randomly chosen bases and states",
                  "Bob measures each photon using randomly chosen bases",
                  "Alice and Bob publicly compare which bases they used",
                  "They keep only results where they used the same basis",
                  "They check a sample of results to detect eavesdropping",
                  "They perform privacy amplification on the remaining bits",
                  "The resulting shared bits form their secret key"
                ],
                "explanation": "This sequence ensures that Alice and Bob end up with a shared secret key, and can detect if anyone has attempted to eavesdrop on their communication."
              }
            }
          }
        },
        {
          "id": "qcrypto-screen5-implementations",
          "type": "lesson_screen",
          "order": 5,
          "estimatedTimeSeconds": 60,
          "content": {
            "headline": "Practical Implementations",
            "body_md": "QKD has moved from theory to practical implementation:\n\n• **Fiber optic QKD**: Uses optical fibers to transmit photons\n  - Current record: ~500 km distance\n  - Limited by photon loss in fibers\n\n• **Free-space QKD**: Transmits photons through the air\n  - Demonstrated between ground stations and satellites\n  - Enables global-scale quantum networks\n\n• **Continuous-variable QKD**: Uses quantum properties of light waves rather than single photons\n  - Compatible with standard telecom equipment\n  - Potentially more practical for widespread deployment\n\n• **Device-independent QKD**: Security guaranteed even if devices are not trusted\n  - Based on Bell's inequality violations\n  - Highest level of security but technically challenging",
            "visual": {
              "type": "unsplash_search",
              "value": "quantum satellite communication"
            },
            "interactive_element": {
              "type": "button",
              "text": "What about quantum-resistant cryptography?",
              "action": "next_screen"
            }
          }
        },
        {
          "id": "qcrypto-screen6-post-quantum",
          "type": "lesson_screen",
          "order": 6,
          "estimatedTimeSeconds": 60,
          "content": {
            "headline": "Post-Quantum Cryptography",
            "body_md": "While QKD offers physics-based security, it requires specialized hardware. **Post-quantum cryptography** is a complementary approach:\n\n• Conventional cryptographic algorithms that resist quantum computer attacks\n• Software-based solution that works on existing infrastructure\n• Based on mathematical problems believed to be hard even for quantum computers\n\nMain types of post-quantum cryptography:\n\n• **Lattice-based cryptography**: Based on finding shortest vectors in high-dimensional lattices\n• **Hash-based cryptography**: Based on properties of cryptographic hash functions\n• **Code-based cryptography**: Based on error-correcting codes\n• **Multivariate cryptography**: Based on solving systems of multivariate polynomial equations\n• **Isogeny-based cryptography**: Based on finding paths between elliptic curves",
            "interactive_element": {
              "type": "multiple_choice_text",
              "question_text": "What is the main difference between quantum key distribution and post-quantum cryptography?",
              "options": [
                {"id": "qcrypto6opt1", "text": "QKD uses quantum mechanics to secure communications, while post-quantum cryptography uses mathematical algorithms resistant to quantum attacks", "is_correct": true, "feedback_correct": "Correct! QKD uses quantum mechanical principles and requires specialized hardware, while post-quantum cryptography uses mathematical algorithms designed to resist quantum computer attacks but can run on conventional computers.", "feedback_incorrect": "Think about the fundamental approach each method takes to security."},
                {"id": "qcrypto6opt2", "text": "QKD is theoretical while post-quantum cryptography is practical", "is_correct": false, "feedback_incorrect": "Both approaches have practical implementations and theoretical foundations."},
                {"id": "qcrypto6opt3", "text": "QKD is vulnerable to quantum computers while post-quantum cryptography isn't", "is_correct": false, "feedback_incorrect": "Actually, QKD is secure even against quantum computers, as its security is based on physics, not computational complexity."}
              ],
              "action_button_text": "Continue"
            }
          }
        },
        {
          "id": "qcrypto-screen7-applications",
          "type": "lesson_screen",
          "order": 7,
          "estimatedTimeSeconds": 50,
          "content": {
            "headline": "Applications and Future Directions",
            "body_md": "Quantum cryptography is finding applications in high-security environments:\n\n• **Government and military**: Securing classified communications\n\n• **Financial sector**: Protecting high-value transactions and sensitive financial data\n\n• **Critical infrastructure**: Securing power grids, water systems, and other vital systems\n\n• **Healthcare**: Protecting sensitive patient data\n\n• **Quantum internet**: Foundation for a future quantum internet with unprecedented security\n\nFuture developments include:\n• **Quantum repeaters** to extend QKD range\n• **Chip-scale QKD** for miniaturization\n• **Quantum random number generators** for improved security\n• **Quantum digital signatures** for document authentication",
            "visual": {
              "type": "giphy_search",
              "value": "quantum network"
            },
            "interactive_element": {
              "type": "button",
              "text": "Let's review what we've learned",
              "action": "next_screen"
            }
          }
        },
        {
          "id": "qcrypto-screen8-recap",
          "type": "lesson_screen",
          "order": 8,
          "estimatedTimeSeconds": 40,
          "content": {
            "headline": "Key Takeaways",
            "body_md": "• **Quantum cryptography** uses quantum mechanics to achieve unprecedented security\n• **Quantum Key Distribution (QKD)** allows secure key sharing with detection of eavesdropping\n• Security is based on the **no-cloning theorem** and the fact that measurement disturbs quantum states\n• The **BB84 protocol** is the most well-known QKD method\n• **Practical implementations** include fiber-optic, free-space, and satellite-based systems\n• **Post-quantum cryptography** provides mathematical algorithms resistant to quantum attacks\n• Applications span **government, finance, infrastructure, and healthcare** sectors",
            "interactive_element": {
              "type": "button",
              "text": "Next Lesson: Quantum Sensing",
              "action": "next_lesson"
            }
          }
        }
      ]
    },
    {
      "id": "quantum-sensing",
      "title": "Quantum Sensing: Ultimate Precision",
      "description": "Discover how quantum effects enable unprecedented measurement precision.",
      "order": 5,
      "type": "interactive_lesson",
      "estimatedTimeMinutes": 10,
      "contentBlocks": [
        {
          "id": "qsense-screen1-intro",
          "type": "lesson_screen",
          "order": 1,
          "estimatedTimeSeconds": 30,
          "content": {
            "headline": "Pushing the Limits of Measurement",
            "body_md": "Quantum sensing harnesses the unique properties of quantum systems to achieve measurements with unprecedented precision, sensitivity, and accuracy—often approaching or even reaching the fundamental limits imposed by the laws of physics.",
            "visual": {
              "type": "giphy_search",
              "value": "quantum sensor"
            },
            "hook": "These technologies are revolutionizing fields from medicine to navigation, enabling detection of the tiniest signals that would be impossible to measure with classical techniques.",
            "interactive_element": {
              "type": "button",
              "text": "What is quantum sensing?",
              "action": "next_screen"
            }
          }
        },
        {
          "id": "qsense-screen2-what-is",
          "type": "lesson_screen",
          "order": 2,
          "estimatedTimeSeconds": 50,
          "content": {
            "headline": "What Makes a Sensor "Quantum"?",
            "body_md": "**Quantum sensors** use quantum systems as measuring devices, exploiting quantum effects to achieve exceptional performance.\n\nA sensor is considered \"quantum\" when it uses:\n\n• **Quantum superposition**: Placing the sensor in multiple states simultaneously\n\n• **Quantum entanglement**: Creating correlations that enhance sensitivity\n\n• **Quantum coherence**: Maintaining delicate quantum states during measurement\n\n• **Quantum squeezing**: Redistributing uncertainty to improve precision in one variable\n\nThese quantum resources allow measurements at or near the fundamental limits set by quantum mechanics.",
            "visual": {
              "type": "unsplash_search",
              "value": "quantum precision measurement"
            },
            "interactive_element": {
              "type": "button",
              "text": "What can we measure?",
              "action": "next_screen"
            }
          }
        },
        {
          "id": "qsense-screen3-types",
          "type": "lesson_screen",
          "order": 3,
          "estimatedTimeSeconds": 60,
          "content": {
            "headline": "Types of Quantum Sensors",
            "body_md": "Quantum sensors can measure a wide range of physical quantities with extraordinary precision:\n\n• **Magnetic field sensors**: Detect incredibly weak magnetic fields using SQUIDs, NV centers, or atomic magnetometers\n\n• **Electric field sensors**: Measure tiny electric fields using Rydberg atoms or single-electron transistors\n\n• **Time and frequency sensors**: Atomic clocks that lose less than a second in billions of years\n\n• **Gravitational sensors**: Detect minute gravitational variations using atom interferometry\n\n• **Acceleration and rotation sensors**: Quantum gyroscopes and accelerometers for navigation\n\n• **Temperature sensors**: Quantum thermometers with micro-Kelvin sensitivity",
            "interactive_element": {
              "type": "multiple_choice_text",
              "question_text": "Which of these is NOT a type of quantum sensor?",
              "options": [
                {"id": "qsense3opt1", "text": "SQUID magnetometer", "is_correct": false, "feedback_incorrect": "SQUIDs (Superconducting QUantum Interference Devices) are quantum sensors that measure magnetic fields with extreme sensitivity."},
                {"id": "qsense3opt2", "text": "Quantum radar detector", "is_correct": true, "feedback_correct": "Correct! While quantum radar is a theoretical concept, 'quantum radar detectors' are not established quantum sensors. The other options are all real quantum sensing technologies.", "feedback_incorrect": "Consider which option might be fictional or not yet developed as a quantum technology."},
                {"id": "qsense3opt3", "text": "Atomic clock", "is_correct": false, "feedback_incorrect": "Atomic clocks are quantum sensors that measure time with extraordinary precision using quantum properties of atoms."}
              ],
              "action_button_text": "Continue"
            }
          }
        },
        {
          "id": "qsense-screen4-atomic-clocks",
          "type": "lesson_screen",
          "order": 4,
          "estimatedTimeSeconds": 70,
          "content": {
            "headline": "Atomic Clocks: Quantum Timekeepers",
            "body_md": "**Atomic clocks** are among the most successful quantum sensors, using the quantum properties of atoms to measure time with astonishing precision:\n\n• They measure the frequency of electromagnetic radiation needed to cause atoms to transition between energy levels\n\n• Modern atomic clocks are accurate to within 1 second in 30 billion years\n\n• They use various atoms (cesium, rubidium, strontium, ytterbium) depending on the application\n\n• **Optical lattice clocks** and **single-ion clocks** represent the current state of the art\n\n• They enable GPS navigation, telecommunications synchronization, and fundamental physics research\n\n• Next-generation atomic clocks may use quantum entanglement to surpass the standard quantum limit",
            "visual": {
              "type": "giphy_search",
              "value": "atomic clock"
            },
            "interactive_element": {
              "type": "button",
              "text": "Tell me about quantum magnetometers",
              "action": "next_screen"
            }
          }
        },
        {
          "id": "qsense-screen5-magnetometers",
          "type": "lesson_screen",
          "order": 5,
          "estimatedTimeSeconds": 60,
          "content": {
            "headline": "Quantum Magnetometers",
            "body_md": "**Quantum magnetometers** detect magnetic fields with extraordinary sensitivity using quantum effects:\n\n• **SQUIDs (Superconducting QUantum Interference Devices)**:\n  - Use superconducting loops with Josephson junctions\n  - Can detect fields as weak as a few femtotesla (10^-15 T)\n  - Used in medical imaging (magnetoencephalography) and geophysical surveys\n\n• **NV center magnetometers**:\n  - Use nitrogen-vacancy defects in diamond\n  - Operate at room temperature with nanoscale spatial resolution\n  - Applications in biology, materials science, and navigation\n\n• **Atomic magnetometers**:\n  - Use spin-polarized atoms (often alkali metals)\n  - Can reach sensitivities comparable to SQUIDs without cryogenic cooling\n  - Used in medical imaging, fundamental physics, and security screening",
            "interactive_element": {
              "type": "interactive",
              "interactiveType": "conditionalMatcher",
              "data": {
                "title": "Quantum Magnetometers",
                "instruction": "Match each quantum magnetometer with its key characteristic:",
                "conditions": [
                  "SQUID magnetometer",
                  "NV center magnetometer",
                  "Atomic magnetometer",
                  "Fluxgate magnetometer",
                  "SERF magnetometer"
                ],
                "outcomes": [
                  "Uses superconducting loops and requires cryogenic cooling",
                  "Uses defects in diamond and works at room temperature",
                  "Uses spin-polarized atoms in vapor cells",
                  "Classical (non-quantum) sensor using ferromagnetic cores",
                  "Special type of atomic magnetometer with highest sensitivity"
                ],
                "correctMatches": [
                  [0, 0],
                  [1, 1],
                  [2, 2],
                  [3, 3],
                  [4, 4]
                ],
                "explanation": "Different quantum magnetometers use different quantum systems and have different advantages in terms of sensitivity, operating conditions, and spatial resolution."
              }
            }
          }
        },
        {
          "id": "qsense-screen6-gravimeters",
          "type": "lesson_screen",
          "order": 6,
          "estimatedTimeSeconds": 60,
          "content": {
            "headline": "Quantum Gravimeters and Accelerometers",
            "body_md": "**Quantum gravimeters** and **accelerometers** use quantum interference to measure gravity and acceleration with unprecedented precision:\n\n• They typically use **atom interferometry**, where atoms are placed in superposition of different paths\n\n• The phase difference between paths depends on the gravitational field or acceleration\n\n• They can detect changes in gravity as small as a few parts in 10^9 of Earth's gravitational field\n\n• Applications include:\n  - Mineral and oil exploration\n  - Underground structure detection\n  - Navigation without GPS\n  - Fundamental physics tests\n  - Early warning for volcanic activity and earthquakes\n\n• Compact versions are being developed for field use and even space applications",
            "visual": {
              "type": "unsplash_search",
              "value": "gravity measurement"
            },
            "interactive_element": {
              "type": "multiple_choice_text",
              "question_text": "What quantum effect is primarily used in atom interferometry gravimeters?",
              "options": [
                {"id": "qsense6opt1", "text": "Quantum tunneling", "is_correct": false, "feedback_incorrect": "While tunneling is an important quantum effect, it's not the primary mechanism used in atom interferometry."},
                {"id": "qsense6opt2", "text": "Quantum superposition", "is_correct": true, "feedback_correct": "Correct! Atom interferometers place atoms in a superposition of different paths, and then recombine them to create an interference pattern that depends on the gravitational field.", "feedback_incorrect": "Think about which quantum effect allows particles to be in multiple places simultaneously."},
                {"id": "qsense6opt3", "text": "Quantum entanglement", "is_correct": false, "feedback_incorrect": "While entanglement can enhance sensitivity in some quantum sensors, basic atom interferometers primarily rely on superposition."}
              ],
              "action_button_text": "Continue"
            }
          }
        },
        {
          "id": "qsense-screen7-applications",
          "type": "lesson_screen",
          "order": 7,
          "estimatedTimeSeconds": 50,
          "content": {
            "headline": "Applications and Impact",
            "body_md": "Quantum sensors are finding applications across numerous fields:\n\n• **Medicine**: Brain imaging (magnetoencephalography), early disease detection, drug discovery\n\n• **Navigation**: GPS-free navigation systems for aircraft, ships, and submarines\n\n• **Geology and resource exploration**: Underground mapping, mineral detection, water resource management\n\n• **Civil engineering**: Infrastructure monitoring, detecting underground voids or structures\n\n• **Fundamental physics**: Testing general relativity, searching for dark matter, gravitational wave detection\n\n• **Defense and security**: Submarine detection, underground facility detection, secure navigation\n\n• **Telecommunications**: Network synchronization, secure time distribution\n\nAs quantum sensors become smaller, cheaper, and more robust, their applications will continue to expand.",
            "visual": {
              "type": "giphy_search",
              "value": "brain imaging scan"
            },
            "interactive_element": {
              "type": "button",
              "text": "Let's review what we've learned",
              "action": "next_screen"
            }
          }
        },
        {
          "id": "qsense-screen8-recap",
          "type": "lesson_screen",
          "order": 8,
          "estimatedTimeSeconds": 40,
          "content": {
            "headline": "Key Takeaways",
            "body_md": "• **Quantum sensors** use quantum effects to achieve unprecedented measurement precision\n• They exploit **quantum superposition**, **entanglement**, **coherence**, and **squeezing**\n• **Atomic clocks** measure time with extraordinary accuracy (1 second in billions of years)\n• **Quantum magnetometers** (SQUIDs, NV centers, atomic) detect extremely weak magnetic fields\n• **Quantum gravimeters** use atom interferometry to measure tiny gravitational variations\n• Applications span **medicine**, **navigation**, **geology**, **physics**, and **security**\n• Quantum sensing is one of the most mature and practical quantum technologies",
            "interactive_element": {
              "type": "button",
              "text": "Take the Module Test",
              "action": "next_lesson"
            }
          }
        }
      ]
    },
    {
      "id": "quantum-technologist-test",
      "title": "The Quantum Technologist",
      "description": "Test your understanding of quantum technologies and their applications.",
      "order": 6,
      "type": "module_test_interactive",
      "estimatedTimeMinutes": 10,
      "contentBlocks": [
        {
          "id": "qtt-intro",
          "type": "question_screen",
          "order": 1,
          "estimatedTimeSeconds": 30,
          "content": {
            "headline": "Test Your Understanding of Quantum Technologies",
            "body_md": "Congratulations on completing the fifth module of Quantum Mechanics! Let's see how well you understand the various applications of quantum mechanics in modern technology.",
            "visual": {
              "type": "giphy_search",
              "value": "quantum technology"
            },
            "interactive_element": {
              "type": "button",
              "text": "Begin the Test",
              "action": "next_screen"
            }
          }
        },
        {
          "id": "qtt-q1",
          "type": "question_screen",
          "order": 2,
          "estimatedTimeSeconds": 60,
          "content": {
            "headline": "Question 1: Lasers",
            "body_md": "Which quantum process is essential for laser operation?",
            "interactive_element": {
              "type": "multiple_choice_text",
              "question_text": "Select the correct answer:",
              "options": [
                {"id": "qtt1opt1", "text": "Spontaneous emission", "is_correct": false, "feedback_incorrect": "While spontaneous emission occurs in lasers, it's not the defining process that makes laser light special."},
                {"id": "qtt1opt2", "text": "Stimulated emission", "is_correct": true, "feedback_correct": "Correct! Stimulated emission is the key quantum process that produces the coherent, in-phase photons that make laser light special. This process was predicted by Einstein in 1917 and is the foundation of all laser operation.", "feedback_incorrect": "Think about which process produces photons with identical properties (wavelength, direction, and phase)."},
                {"id": "qtt1opt3", "text": "Quantum tunneling", "is_correct": false, "feedback_incorrect": "Quantum tunneling is not directly involved in the basic operation of lasers."},
                {"id": "qtt1opt4", "text": "Quantum entanglement", "is_correct": false, "feedback_incorrect": "Quantum entanglement is not necessary for conventional laser operation."}
              ],
              "action_button_text": "Next Question"
            }
          }
        },
        {
          "id": "qtt-q2",
          "type": "question_screen",
          "order": 3,
          "estimatedTimeSeconds": 60,
          "content": {
            "headline": "Question 2: Semiconductors",
            "body_md": "What happens when silicon (which has 4 valence electrons) is doped with boron (which has 3 valence electrons)?",
            "interactive_element": {
              "type": "multiple_choice_text",
              "question_text": "Select the correct outcome:",
              "options": [
                {"id": "qtt2opt1", "text": "It becomes n-type with electrons as majority carriers", "is_correct": false, "feedback_incorrect": "Boron has fewer valence electrons than silicon, so it creates holes, not excess electrons."},
                {"id": "qtt2opt2", "text": "It becomes p-type with holes as majority carriers", "is_correct": true, "feedback_correct": "Correct! Boron has 3 valence electrons compared to silicon's 4, so it creates 'holes' (electron vacancies) in the crystal structure. These holes act as positive charge carriers, making the semiconductor p-type.", "feedback_incorrect": "Think about what happens when you add an atom with fewer valence electrons than silicon."},
                {"id": "qtt2opt3", "text": "It becomes an insulator", "is_correct": false, "feedback_incorrect": "Doping increases conductivity rather than making the material an insulator."},
                {"id": "qtt2opt4", "text": "It becomes a superconductor", "is_correct": false, "feedback_incorrect": "Doping silicon with boron does not create a superconductor at normal temperatures."}
              ],
              "action_button_text": "Next Question"
            }
          }
        },
        {
          "id": "qtt-q3",
          "type": "question_screen",
          "order": 4,
          "estimatedTimeSeconds": 70,
          "content": {
            "headline": "Question 3: Quantum Computing",
            "body_md": "What gives quantum computers their potential advantage over classical computers for certain problems?",
            "interactive_element": {
              "type": "multiple_choice_text",
              "question_text": "Select the best explanation:",
              "options": [
                {"id": "qtt3opt1", "text": "They simply run at much higher clock speeds than classical computers", "is_correct": false, "feedback_incorrect": "Quantum computers don't necessarily operate at higher clock speeds; their advantage comes from their fundamentally different way of processing information."},
                {"id": "qtt3opt2", "text": "They can represent and process many states simultaneously through quantum superposition and entanglement", "is_correct": true, "feedback_correct": "Correct! Quantum computers use qubits that can exist in superpositions of states, and through entanglement, n qubits can represent 2^n states simultaneously. This allows them to explore many possible solutions in parallel, giving an exponential advantage for certain problems.", "feedback_incorrect": "Think about the fundamental quantum properties that distinguish qubits from classical bits."},
                {"id": "qtt3opt3", "text": "They use more powerful materials in their processors", "is_correct": false, "feedback_incorrect": "The advantage isn't about materials but about the quantum mechanical principles used for computation."},
                {"id": "qtt3opt4", "text": "They can store more data in the same physical space", "is_correct": false, "feedback_incorrect": "While quantum systems can in principle encode more information, the primary advantage is in processing power for specific algorithms, not data storage density."}
              ],
              "action_button_text": "Next Question"
            }
          }
        },
        {
          "id": "qtt-q4",
          "type": "question_screen",
          "order": 5,
          "estimatedTimeSeconds": 70,
          "content": {
            "headline": "Question 4: Quantum Cryptography",
            "body_md": "Why is quantum key distribution (QKD) secure against eavesdropping?",
            "interactive_element": {
              "type": "multiple_choice_text",
              "question_text": "Select the best explanation:",
              "options": [
                {"id": "qtt4opt1", "text": "It uses encryption keys that are too long to be broken", "is_correct": false, "feedback_incorrect": "The security of QKD doesn't rely on key length but on fundamental physics."},
                {"id": "qtt4opt2", "text": "Any measurement of the quantum states by an eavesdropper inevitably disturbs them in a detectable way", "is_correct": true, "feedback_correct": "Correct! According to quantum mechanics, measuring an unknown quantum state disturbs it. This means any eavesdropper will inevitably leave evidence of their intrusion, which can be detected by the legitimate users. This is a consequence of the no-cloning theorem and measurement postulate in quantum mechanics.", "feedback_incorrect": "Think about what happens when someone tries to measure a quantum system without the authorized users knowing."},
                {"id": "qtt4opt3", "text": "It uses mathematical algorithms that are impossible to solve", "is_correct": false, "feedback_incorrect": "QKD relies on physics, not mathematical complexity, for its security."},
                {"id": "qtt4opt4", "text": "The keys are transmitted faster than they can be intercepted", "is_correct": false, "feedback_incorrect": "Speed is not the security mechanism in QKD."}
              ],
              "action_button_text": "Next Question"
            }
          }
        },
        {
          "id": "qtt-q5",
          "type": "question_screen",
          "order": 6,
          "estimatedTimeSeconds": 70,
          "content": {
            "headline": "Question 5: Quantum Sensing",
            "body_md": "Which of the following is NOT a type of quantum sensor?",
            "interactive_element": {
              "type": "multiple_choice_text",
              "question_text": "Select the non-quantum sensor:",
              "options": [
                {"id": "qtt5opt1", "text": "SQUID magnetometer", "is_correct": false, "feedback_incorrect": "SQUIDs (Superconducting QUantum Interference Devices) are quantum sensors that use quantum effects to measure magnetic fields."},
                {"id": "qtt5opt2", "text": "Atomic clock", "is_correct": false, "feedback_incorrect": "Atomic clocks use quantum properties of atoms to measure time with extraordinary precision."},
                {"id": "qtt5opt3", "text": "Atom interferometer", "is_correct": false, "feedback_incorrect": "Atom interferometers use quantum superposition and interference to measure gravity and acceleration."},
                {"id": "qtt5opt4", "text": "Thermocouple", "is_correct": true, "feedback_correct": "Correct! A thermocouple is a classical sensor that measures temperature using the Seebeck effect (voltage generated at the junction of two different metals). It doesn't rely on quantum effects for its operation, unlike the other options which are all quantum sensors.", "feedback_incorrect": "Consider which option works based on classical physics principles rather than quantum effects."}
              ],
              "action_button_text": "Final Question"
            }
          }
        },
        {
          "id": "qtt-q6",
          "type": "question_screen",
          "order": 7,
          "estimatedTimeSeconds": 70,
          "content": {
            "headline": "Question 6: Quantum Technologies",
            "body_md": "Match each quantum technology with its correct description.",
            "interactive_element": {
              "type": "interactive",
              "interactiveType": "conditionalMatcher",
              "data": {
                "title": "Quantum Technologies",
                "instruction": "Match each quantum technology with its correct description:",
                "conditions": [
                  "Laser",
                  "Transistor",
                  "Quantum Computer",
                  "Quantum Key Distribution",
                  "Atomic Clock"
                ],
                "outcomes": [
                  "Uses stimulated emission to produce coherent light",
                  "Uses quantum tunneling and band structure to switch or amplify signals",
                  "Uses qubits in superposition to perform parallel computations",
                  "Uses quantum measurement properties to detect eavesdropping",
                  "Uses atomic energy transitions to measure time with extreme precision"
                ],
                "correctMatches": [
                  [0, 0],
                  [1, 1],
                  [2, 2],
                  [3, 3],
                  [4, 4]
                ],
                "explanation": "Each of these technologies relies on different quantum mechanical principles to achieve its function."
              }
            }
          }
        },
        {
          "id": "qtt-results",
          "type": "lesson_screen",
          "order": 8,
          "estimatedTimeSeconds": 40,
          "content": {
            "headline": "Course Complete!",
            "body_md": "Congratulations! You've completed the fifth and final module of Quantum Mechanics: Applications of Quantum Mechanics.\n\nYou now understand how quantum mechanics has revolutionized technology:\n\n• **Lasers** and their applications in medicine, communications, and manufacturing\n• **Semiconductors and transistors** that power our digital world\n• **Quantum computing** and its potential to solve previously intractable problems\n• **Quantum cryptography** for unbreakable encryption\n• **Quantum sensing** for measurements at the limits of physical possibility\n\nYou've completed the entire Quantum Mechanics course! You now have a solid foundation in this fascinating field that continues to transform our understanding of nature and our technological capabilities.",
            "visual": {
              "type": "giphy_search",
              "value": "quantum celebration"
            },
            "interactive_element": {
              "type": "button",
              "text": "Complete Course",
              "action": "module_complete"
            }
          }
        }
      ]
    }
  ]
}
