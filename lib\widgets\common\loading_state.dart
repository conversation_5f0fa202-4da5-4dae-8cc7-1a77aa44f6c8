import 'package:flutter/material.dart';
import '../../theme/widget_colors.dart';
import '../../theme/text_styles.dart';

/// A standardized loading state widget for interactive widgets
/// Provides consistent loading experience across all widget types
class LoadingState extends StatefulWidget {
  /// The height of the loading container
  final double? height;
  
  /// The width of the loading container
  final double? width;
  
  /// Custom loading message
  final String? message;
  
  /// Whether to show the loading message
  final bool showMessage;
  
  /// Whether to show a shimmer effect
  final bool showShimmer;
  
  /// Category ID for color theming
  final String? categoryId;

  const LoadingState({
    super.key,
    this.height,
    this.width,
    this.message,
    this.showMessage = true,
    this.showShimmer = false,
    this.categoryId,
  });

  @override
  State<LoadingState> createState() => _LoadingStateState();
}

class _LoadingStateState extends State<LoadingState>
    with SingleTickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _fadeAnimation;
  late Animation<double> _scaleAnimation;

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 1500),
      vsync: this,
    );
    
    _fadeAnimation = Tween<double>(
      begin: 0.3,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeInOut,
    ));
    
    _scaleAnimation = Tween<double>(
      begin: 0.8,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.elasticOut,
    ));
    
    _animationController.repeat(reverse: true);
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final categoryColor = widget.categoryId != null
        ? WidgetColors.getCategoryColor(widget.categoryId!)
        : WidgetColors.buttonPrimary;

    return Container(
      height: widget.height ?? 200,
      width: widget.width ?? double.infinity,
      decoration: BoxDecoration(
        color: WidgetColors.backgroundSecondary,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: WidgetColors.borderLight,
          width: 1,
        ),
      ),
      child: widget.showShimmer ? _buildShimmerLoading() : _buildStandardLoading(categoryColor),
    );
  }

  Widget _buildStandardLoading(Color categoryColor) {
    return AnimatedBuilder(
      animation: _animationController,
      builder: (context, child) {
        return FadeTransition(
          opacity: _fadeAnimation,
          child: ScaleTransition(
            scale: _scaleAnimation,
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                // Loading indicator
                SizedBox(
                  width: 40,
                  height: 40,
                  child: CircularProgressIndicator(
                    strokeWidth: 3,
                    valueColor: AlwaysStoppedAnimation<Color>(categoryColor),
                    backgroundColor: categoryColor.withOpacity(0.2),
                  ),
                ),
                
                if (widget.showMessage) ...[
                  const SizedBox(height: 16),
                  Text(
                    widget.message ?? 'Loading interactive content...',
                    style: WidgetTextStyles.bodyMedium.copyWith(
                      color: WidgetColors.textSecondary,
                    ),
                    textAlign: TextAlign.center,
                  ),
                ],
              ],
            ),
          ),
        );
      },
    );
  }

  Widget _buildShimmerLoading() {
    return AnimatedBuilder(
      animation: _animationController,
      builder: (context, child) {
        return Container(
          decoration: BoxDecoration(
            gradient: LinearGradient(
              begin: Alignment.topLeft,
              end: Alignment.bottomRight,
              colors: [
                WidgetColors.backgroundSecondary,
                WidgetColors.backgroundTertiary.withOpacity(_fadeAnimation.value),
                WidgetColors.backgroundSecondary,
              ],
            ),
            borderRadius: BorderRadius.circular(12),
          ),
          child: Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                // Shimmer placeholder bars
                _buildShimmerBar(width: 120, height: 20),
                const SizedBox(height: 12),
                _buildShimmerBar(width: 80, height: 16),
                const SizedBox(height: 12),
                _buildShimmerBar(width: 100, height: 16),
              ],
            ),
          ),
        );
      },
    );
  }

  Widget _buildShimmerBar({required double width, required double height}) {
    return Container(
      width: width,
      height: height,
      decoration: BoxDecoration(
        color: WidgetColors.borderLight.withOpacity(0.6),
        borderRadius: BorderRadius.circular(4),
      ),
    );
  }
}

/// A compact loading indicator for inline use
class CompactLoadingIndicator extends StatelessWidget {
  final String? categoryId;
  final double size;

  const CompactLoadingIndicator({
    super.key,
    this.categoryId,
    this.size = 20,
  });

  @override
  Widget build(BuildContext context) {
    final categoryColor = categoryId != null
        ? WidgetColors.getCategoryColor(categoryId!)
        : WidgetColors.buttonPrimary;

    return SizedBox(
      width: size,
      height: size,
      child: CircularProgressIndicator(
        strokeWidth: 2,
        valueColor: AlwaysStoppedAnimation<Color>(categoryColor),
        backgroundColor: categoryColor.withOpacity(0.2),
      ),
    );
  }
}

/// A loading overlay that can be placed over existing content
class LoadingOverlay extends StatelessWidget {
  final Widget child;
  final bool isLoading;
  final String? message;
  final String? categoryId;

  const LoadingOverlay({
    super.key,
    required this.child,
    required this.isLoading,
    this.message,
    this.categoryId,
  });

  @override
  Widget build(BuildContext context) {
    return Stack(
      children: [
        child,
        if (isLoading)
          Positioned.fill(
            child: Container(
              color: WidgetColors.backgroundPrimary.withOpacity(0.8),
              child: LoadingState(
                message: message,
                categoryId: categoryId,
              ),
            ),
          ),
      ],
    );
  }
}

/// Loading state specifically for mathematical content
class MathLoadingState extends StatelessWidget {
  final double? height;
  final String? equation;

  const MathLoadingState({
    super.key,
    this.height,
    this.equation,
  });

  @override
  Widget build(BuildContext context) {
    return LoadingState(
      height: height,
      message: equation != null 
          ? 'Calculating $equation...'
          : 'Loading mathematical content...',
      categoryId: 'mathematics',
    );
  }
}

/// Loading state specifically for science simulations
class ScienceLoadingState extends StatelessWidget {
  final double? height;
  final String? simulationType;

  const ScienceLoadingState({
    super.key,
    this.height,
    this.simulationType,
  });

  @override
  Widget build(BuildContext context) {
    return LoadingState(
      height: height,
      message: simulationType != null 
          ? 'Initializing $simulationType simulation...'
          : 'Loading scientific simulation...',
      categoryId: 'science',
      showShimmer: true,
    );
  }
}
