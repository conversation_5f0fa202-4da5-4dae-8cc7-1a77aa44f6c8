import 'package:flutter/material.dart';
import 'dart:math' as math;
import 'dart:async';

class InteractiveAlgorithmPerformanceEvaluatorWidget extends StatefulWidget {
  final Map<String, dynamic>? data;

  const InteractiveAlgorithmPerformanceEvaluatorWidget({
    super.key,
    this.data,
  });

  factory InteractiveAlgorithmPerformanceEvaluatorWidget.fromData(
      Map<String, dynamic> data) {
    return InteractiveAlgorithmPerformanceEvaluatorWidget(
      data: data,
    );
  }

  @override
  State<InteractiveAlgorithmPerformanceEvaluatorWidget> createState() =>
      _InteractiveAlgorithmPerformanceEvaluatorWidgetState();
}

class _InteractiveAlgorithmPerformanceEvaluatorWidgetState
    extends State<InteractiveAlgorithmPerformanceEvaluatorWidget>
    with SingleTickerProviderStateMixin {
  // UI parameters
  late Color _primaryColor;
  late Color _secondaryColor;
  late Color _tertiaryColor;
  late Color _textColor;
  late Color _backgroundColor;

  // Algorithm parameters
  final List<String> _algorithmTypes = [
    'Sorting',
    'Searching',
    'Graph',
    'Mathematical'
  ];
  String _selectedAlgorithmType = 'Sorting';

  // Sorting algorithms
  final Map<String, String> _sortingAlgorithms = {
    'bubble': 'Bubble Sort - O(n²)',
    'selection': 'Selection Sort - O(n²)',
    'insertion': 'Insertion Sort - O(n²)',
    'merge': 'Merge Sort - O(n log n)',
    'quick': 'Quick Sort - O(n log n)',
    'heap': 'Heap Sort - O(n log n)',
  };

  // Searching algorithms
  final Map<String, String> _searchingAlgorithms = {
    'linear': 'Linear Search - O(n)',
    'binary': 'Binary Search - O(log n)',
    'jump': 'Jump Search - O(√n)',
    'interpolation': 'Interpolation Search - O(log log n)',
  };

  // Graph algorithms
  final Map<String, String> _graphAlgorithms = {
    'bfs': 'Breadth-First Search - O(V+E)',
    'dfs': 'Depth-First Search - O(V+E)',
    'dijkstra': 'Dijkstra\'s Algorithm - O(V² + E)',
    'bellman_ford': 'Bellman-Ford - O(V×E)',
  };

  // Mathematical algorithms
  final Map<String, String> _mathematicalAlgorithms = {
    'fibonacci_recursive': 'Fibonacci (Recursive) - O(2ⁿ)',
    'fibonacci_iterative': 'Fibonacci (Iterative) - O(n)',
    'factorial_recursive': 'Factorial (Recursive) - O(n)',
    'factorial_iterative': 'Factorial (Iterative) - O(n)',
    'prime_check_naive': 'Prime Check (Naive) - O(n)',
    'prime_check_optimized': 'Prime Check (Optimized) - O(√n)',
  };

  // Currently selected algorithms for comparison
  List<String> _selectedAlgorithms = ['bubble', 'quick'];

  // Input size parameters
  double _inputSize = 50;
  final double _minInputSize = 10;
  final double _maxInputSize = 1000;

  // Performance metrics
  Map<String, int> _operationCounts = {};
  Map<String, double> _executionTimes = {};
  bool _isRunning = false;

  // Animation controller
  late AnimationController _animationController;
  late Animation<double> _animation;

  // Complexity classes for visualization
  final Map<String, Function> _complexityFunctions = {
    'O(1)': (n) => 1,
    'O(log n)': (n) => math.log(n) / math.log(2),
    'O(√n)': (n) => math.sqrt(n),
    'O(n)': (n) => n,
    'O(n log n)': (n) => n * math.log(n) / math.log(2),
    'O(n²)': (n) => n * n,
    'O(2ⁿ)': (n) => math.pow(2, n),
  };

  @override
  void initState() {
    super.initState();
    _initializeFromData();
    _animationController = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 1000),
    );
    _animation = CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeInOut,
    );
    _animationController.forward();
    _runAlgorithms();
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  void _initializeFromData() {
    final data = widget.data;
    if (data != null) {
      _primaryColor = Color(data['primary_color'] ?? 0xFF2196F3);
      _secondaryColor = Color(data['secondary_color'] ?? 0xFFFFA000);
      _tertiaryColor = Color(data['tertiary_color'] ?? 0xFF4CAF50);
      _textColor = Color(data['text_color'] ?? 0xFF333333);
      _backgroundColor = Color(data['background_color'] ?? 0xFFF5F5F5);

      if (data['algorithm_type'] != null) {
        _selectedAlgorithmType = data['algorithm_type'];
      }

      if (data['selected_algorithms'] != null) {
        _selectedAlgorithms = List<String>.from(data['selected_algorithms']);
      } else {
        // Set default algorithms based on type
        _setDefaultAlgorithms();
      }

      if (data['input_size'] != null) {
        _inputSize = data['input_size'].toDouble();
      }
    } else {
      _primaryColor = Colors.blue;
      _secondaryColor = Colors.orange;
      _tertiaryColor = Colors.green;
      _textColor = Colors.black87;
      _backgroundColor = Colors.grey.shade100;
      _setDefaultAlgorithms();
    }
  }

  void _setDefaultAlgorithms() {
    switch (_selectedAlgorithmType) {
      case 'Sorting':
        _selectedAlgorithms = ['bubble', 'quick'];
        break;
      case 'Searching':
        _selectedAlgorithms = ['linear', 'binary'];
        break;
      case 'Graph':
        _selectedAlgorithms = ['bfs', 'dijkstra'];
        break;
      case 'Mathematical':
        _selectedAlgorithms = ['fibonacci_recursive', 'fibonacci_iterative'];
        break;
    }
  }

  Map<String, String> _getCurrentAlgorithms() {
    switch (_selectedAlgorithmType) {
      case 'Sorting':
        return _sortingAlgorithms;
      case 'Searching':
        return _searchingAlgorithms;
      case 'Graph':
        return _graphAlgorithms;
      case 'Mathematical':
        return _mathematicalAlgorithms;
      default:
        return _sortingAlgorithms;
    }
  }

  void _runAlgorithms() {
    if (_isRunning) return;

    setState(() {
      _isRunning = true;
      _operationCounts = {};
      _executionTimes = {};
    });

    // Run algorithms on a separate isolate or future to avoid blocking UI
    Future.microtask(() {
      for (var algo in _selectedAlgorithms) {
        final startTime = DateTime.now();
        int operations = _runAlgorithm(algo, _inputSize.toInt());
        final endTime = DateTime.now();
        final executionTime = endTime.difference(startTime).inMicroseconds / 1000.0;

        _operationCounts[algo] = operations;
        _executionTimes[algo] = executionTime;
      }

      if (mounted) {
        setState(() {
          _isRunning = false;
        });
      }
    });
  }

  int _runAlgorithm(String algorithm, int size) {
    int operations = 0;

    // Generate test data
    List<int> data = List.generate(size, (i) => math.Random().nextInt(1000));

    // Run the selected algorithm
    switch (algorithm) {
      // Sorting algorithms
      case 'bubble':
        operations = _bubbleSort(data.toList());
        break;
      case 'selection':
        operations = _selectionSort(data.toList());
        break;
      case 'insertion':
        operations = _insertionSort(data.toList());
        break;
      case 'merge':
        operations = _mergeSort(data.toList());
        break;
      case 'quick':
        operations = _quickSort(data.toList());
        break;
      case 'heap':
        operations = _heapSort(data.toList());
        break;

      // Searching algorithms
      case 'linear':
        operations = _linearSearch(data, 500);
        break;
      case 'binary':
        data.sort();
        operations = _binarySearch(data, 500);
        break;
      case 'jump':
        data.sort();
        operations = _jumpSearch(data, 500);
        break;
      case 'interpolation':
        data.sort();
        operations = _interpolationSearch(data, 500);
        break;

      // Mathematical algorithms
      case 'fibonacci_recursive':
        operations = _fibonacciRecursive(math.min(30, size));
        break;
      case 'fibonacci_iterative':
        operations = _fibonacciIterative(size);
        break;
      case 'factorial_recursive':
        operations = _factorialRecursive(math.min(20, size));
        break;
      case 'factorial_iterative':
        operations = _factorialIterative(math.min(20, size));
        break;
      case 'prime_check_naive':
        operations = _primeCheckNaive(size);
        break;
      case 'prime_check_optimized':
        operations = _primeCheckOptimized(size);
        break;

      // Graph algorithms - simplified versions
      case 'bfs':
        operations = _simulateBFS(size);
        break;
      case 'dfs':
        operations = _simulateDFS(size);
        break;
      case 'dijkstra':
        operations = _simulateDijkstra(size);
        break;
      case 'bellman_ford':
        operations = _simulateBellmanFord(size);
        break;
    }

    return operations;
  }

  // Algorithm implementations
  // These are simplified implementations that count operations

  // Sorting Algorithms
  int _bubbleSort(List<int> arr) {
    int operations = 0;
    int n = arr.length;
    for (int i = 0; i < n; i++) {
      for (int j = 0; j < n - i - 1; j++) {
        operations++; // Comparison operation
        if (arr[j] > arr[j + 1]) {
          // Swap
          int temp = arr[j];
          arr[j] = arr[j + 1];
          arr[j + 1] = temp;
          operations++; // Swap operation
        }
      }
    }
    return operations;
  }

  int _selectionSort(List<int> arr) {
    int operations = 0;
    int n = arr.length;
    for (int i = 0; i < n; i++) {
      int minIdx = i;
      for (int j = i + 1; j < n; j++) {
        operations++; // Comparison operation
        if (arr[j] < arr[minIdx]) {
          minIdx = j;
        }
      }
      // Swap
      int temp = arr[minIdx];
      arr[minIdx] = arr[i];
      arr[i] = temp;
      operations++; // Swap operation
    }
    return operations;
  }

  int _insertionSort(List<int> arr) {
    int operations = 0;
    int n = arr.length;
    for (int i = 1; i < n; i++) {
      int key = arr[i];
      int j = i - 1;
      operations++; // Assignment operation

      while (j >= 0 && arr[j] > key) {
        operations++; // Comparison operation
        arr[j + 1] = arr[j];
        j--;
        operations++; // Assignment operation
      }
      arr[j + 1] = key;
      operations++; // Assignment operation
    }
    return operations;
  }

  int _mergeSort(List<int> arr) {
    int operations = 0;
    operations = _mergeSortHelper(arr, 0, arr.length - 1);
    return operations;
  }

  int _mergeSortHelper(List<int> arr, int left, int right) {
    int operations = 0;
    if (left < right) {
      int mid = (left + right) ~/ 2;
      operations++; // Calculation operation

      operations += _mergeSortHelper(arr, left, mid);
      operations += _mergeSortHelper(arr, mid + 1, right);
      operations += _merge(arr, left, mid, right);
    }
    return operations;
  }

  int _merge(List<int> arr, int left, int mid, int right) {
    int operations = 0;
    int n1 = mid - left + 1;
    int n2 = right - mid;
    operations += 2; // Calculation operations

    List<int> L = List<int>.filled(n1, 0);
    List<int> R = List<int>.filled(n2, 0);

    for (int i = 0; i < n1; i++) {
      L[i] = arr[left + i];
      operations++; // Assignment operation
    }
    for (int j = 0; j < n2; j++) {
      R[j] = arr[mid + 1 + j];
      operations++; // Assignment operation
    }

    int i = 0, j = 0;
    int k = left;

    while (i < n1 && j < n2) {
      operations++; // Comparison operation
      if (L[i] <= R[j]) {
        arr[k] = L[i];
        i++;
      } else {
        arr[k] = R[j];
        j++;
      }
      k++;
      operations++; // Assignment operation
    }

    while (i < n1) {
      arr[k] = L[i];
      i++;
      k++;
      operations++; // Assignment operation
    }

    while (j < n2) {
      arr[k] = R[j];
      j++;
      k++;
      operations++; // Assignment operation
    }

    return operations;
  }

  int _quickSort(List<int> arr) {
    int operations = 0;
    operations = _quickSortHelper(arr, 0, arr.length - 1);
    return operations;
  }

  int _quickSortHelper(List<int> arr, int low, int high) {
    int operations = 0;
    if (low < high) {
      int pi = _partition(arr, low, high, operations);
      operations += pi & 0x7FFFFFFF; // Extract operations count
      pi = pi >> 32; // Extract pivot index

      operations += _quickSortHelper(arr, low, pi - 1);
      operations += _quickSortHelper(arr, pi + 1, high);
    }
    return operations;
  }

  int _partition(List<int> arr, int low, int high, int operations) {
    int pivot = arr[high];
    int i = low - 1;
    operations++; // Assignment operation

    for (int j = low; j < high; j++) {
      operations++; // Comparison operation
      if (arr[j] < pivot) {
        i++;
        // Swap
        int temp = arr[i];
        arr[i] = arr[j];
        arr[j] = temp;
        operations++; // Swap operation
      }
    }

    // Swap
    int temp = arr[i + 1];
    arr[i + 1] = arr[high];
    arr[high] = temp;
    operations++; // Swap operation

    // Return both pivot index and operations count
    return ((i + 1) << 32) | operations;
  }

  int _heapSort(List<int> arr) {
    int operations = 0;
    int n = arr.length;

    // Build heap
    for (int i = n ~/ 2 - 1; i >= 0; i--) {
      operations += _heapify(arr, n, i);
    }

    // Extract elements from heap
    for (int i = n - 1; i > 0; i--) {
      // Swap
      int temp = arr[0];
      arr[0] = arr[i];
      arr[i] = temp;
      operations++; // Swap operation

      operations += _heapify(arr, i, 0);
    }

    return operations;
  }

  int _heapify(List<int> arr, int n, int i) {
    int operations = 0;
    int largest = i;
    int left = 2 * i + 1;
    int right = 2 * i + 2;

    operations += 2; // Calculation operations

    if (left < n && arr[left] > arr[largest]) {
      largest = left;
      operations++; // Comparison operation
    }

    if (right < n && arr[right] > arr[largest]) {
      largest = right;
      operations++; // Comparison operation
    }

    if (largest != i) {
      // Swap
      int temp = arr[i];
      arr[i] = arr[largest];
      arr[largest] = temp;
      operations++; // Swap operation

      operations += _heapify(arr, n, largest);
    }

    return operations;
  }

  // Searching Algorithms
  int _linearSearch(List<int> arr, int x) {
    int operations = 0;
    for (int i = 0; i < arr.length; i++) {
      operations++; // Comparison operation
      if (arr[i] == x) {
        return operations;
      }
    }
    return operations;
  }

  int _binarySearch(List<int> arr, int x) {
    int operations = 0;
    int left = 0;
    int right = arr.length - 1;

    while (left <= right) {
      operations++; // Comparison operation
      int mid = left + (right - left) ~/ 2;
      operations++; // Calculation operation

      if (arr[mid] == x) {
        return operations;
      }

      if (arr[mid] < x) {
        left = mid + 1;
      } else {
        right = mid - 1;
      }
      operations++; // Comparison operation
    }

    return operations;
  }

  int _jumpSearch(List<int> arr, int x) {
    int operations = 0;
    int n = arr.length;
    int step = math.sqrt(n).floor();
    operations++; // Calculation operation

    int prev = 0;
    while (arr[math.min(step, n) - 1] < x) {
      operations++; // Comparison operation
      prev = step;
      step += math.sqrt(n).floor();
      operations++; // Calculation operation
      if (prev >= n) {
        return operations;
      }
    }

    while (arr[prev] < x) {
      operations++; // Comparison operation
      prev++;
      if (prev == math.min(step, n)) {
        return operations;
      }
    }

    if (arr[prev] == x) {
      operations++; // Comparison operation
      return operations;
    }

    return operations;
  }

  int _interpolationSearch(List<int> arr, int x) {
    int operations = 0;
    int low = 0;
    int high = arr.length - 1;

    while (low <= high && x >= arr[low] && x <= arr[high]) {
      operations++; // Comparison operation

      if (low == high) {
        if (arr[low] == x) {
          operations++; // Comparison operation
          return operations;
        }
        return operations;
      }

      // Calculate probe position
      int pos = low + ((high - low) * (x - arr[low]) ~/ (arr[high] - arr[low]));
      operations += 3; // Calculation operations

      if (arr[pos] == x) {
        operations++; // Comparison operation
        return operations;
      }

      if (arr[pos] < x) {
        low = pos + 1;
      } else {
        high = pos - 1;
      }
      operations++; // Comparison operation
    }

    return operations;
  }

  // Mathematical Algorithms
  int _fibonacciRecursive(int n) {
    if (n <= 1) return 1; // Base case, 1 operation
    return 1 + _fibonacciRecursive(n - 1) + _fibonacciRecursive(n - 2);
  }

  int _fibonacciIterative(int n) {
    int operations = 0;
    if (n <= 1) return 1;

    int a = 0, b = 1, c;
    operations += 2; // Assignment operations

    for (int i = 2; i <= n; i++) {
      c = a + b;
      a = b;
      b = c;
      operations += 3; // Assignment operations
    }

    return operations;
  }

  int _factorialRecursive(int n) {
    if (n <= 1) return 1; // Base case, 1 operation
    return 1 + _factorialRecursive(n - 1);
  }

  int _factorialIterative(int n) {
    int operations = 0;
    int result = 1;
    operations++; // Assignment operation

    for (int i = 2; i <= n; i++) {
      result *= i;
      operations++; // Multiplication operation
    }

    return operations;
  }

  int _primeCheckNaive(int n) {
    int operations = 0;
    if (n <= 1) return 1;
    if (n <= 3) return 2;

    if (n % 2 == 0 || n % 3 == 0) {
      operations += 2; // Comparison operations
      return operations;
    }

    for (int i = 5; i <= n; i += 6) {
      operations += 2; // Comparison operations
      if (n % i == 0 || n % (i + 2) == 0) {
        return operations;
      }
    }

    return operations;
  }

  int _primeCheckOptimized(int n) {
    int operations = 0;
    if (n <= 1) return 1;
    if (n <= 3) return 2;

    if (n % 2 == 0 || n % 3 == 0) {
      operations += 2; // Comparison operations
      return operations;
    }

    for (int i = 5; i * i <= n; i += 6) {
      operations += 2; // Comparison operations
      if (n % i == 0 || n % (i + 2) == 0) {
        return operations;
      }
    }

    return operations;
  }

  // Graph Algorithms (simplified simulations)
  int _simulateBFS(int n) {
    // Simulate BFS operations on a graph with n vertices
    int operations = 0;
    int edges = n * 2; // Assume average of 2 edges per vertex

    // BFS is O(V+E)
    operations = n + edges;

    return operations;
  }

  int _simulateDFS(int n) {
    // Simulate DFS operations on a graph with n vertices
    int operations = 0;
    int edges = n * 2; // Assume average of 2 edges per vertex

    // DFS is O(V+E)
    operations = n + edges;

    return operations;
  }

  int _simulateDijkstra(int n) {
    // Simulate Dijkstra's algorithm operations on a graph with n vertices
    int operations = 0;

    // Dijkstra's is O(V² + E)
    operations = n * n + (n * 2);

    return operations;
  }

  int _simulateBellmanFord(int n) {
    // Simulate Bellman-Ford algorithm operations on a graph with n vertices
    int operations = 0;
    int edges = n * 2; // Assume average of 2 edges per vertex

    // Bellman-Ford is O(V×E)
    operations = n * edges;

    return operations;
  }

  @override
  Widget build(BuildContext context) {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
      ),
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Title and description
            Text(
              'Algorithm Performance Evaluator',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
                color: _textColor,
              ),
            ),
            const SizedBox(height: 8),
            Text(
              'Compare the performance of different algorithms and understand Big-O notation',
              style: TextStyle(
                fontSize: 14,
                color: _textColor.withOpacity(0.7),
              ),
            ),
            const SizedBox(height: 16),

            // Algorithm type selector
            Row(
              children: [
                Text(
                  'Algorithm Type:',
                  style: TextStyle(
                    fontSize: 14,
                    fontWeight: FontWeight.bold,
                    color: _textColor,
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: DropdownButton<String>(
                    value: _selectedAlgorithmType,
                    isExpanded: true,
                    onChanged: (String? newValue) {
                      if (newValue != null) {
                        setState(() {
                          _selectedAlgorithmType = newValue;
                          _setDefaultAlgorithms();
                          _runAlgorithms();
                        });
                      }
                    },
                    items: _algorithmTypes
                        .map<DropdownMenuItem<String>>((String value) {
                      return DropdownMenuItem<String>(
                        value: value,
                        child: Text(value),
                      );
                    }).toList(),
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),

            // Algorithm selection
            Text(
              'Select Algorithms to Compare:',
              style: TextStyle(
                fontSize: 14,
                fontWeight: FontWeight.bold,
                color: _textColor,
              ),
            ),
            const SizedBox(height: 8),
            Wrap(
              spacing: 8,
              runSpacing: 8,
              children: _getCurrentAlgorithms().entries.map((entry) {
                final isSelected = _selectedAlgorithms.contains(entry.key);
                return FilterChip(
                  label: Text(entry.value),
                  selected: isSelected,
                  onSelected: (bool selected) {
                    setState(() {
                      if (selected) {
                        if (_selectedAlgorithms.length < 3) {
                          _selectedAlgorithms.add(entry.key);
                          _runAlgorithms();
                        }
                      } else {
                        if (_selectedAlgorithms.length > 1) {
                          _selectedAlgorithms.remove(entry.key);
                          _runAlgorithms();
                        }
                      }
                    });
                  },
                  selectedColor: _primaryColor.withOpacity(0.3),
                  checkmarkColor: _primaryColor,
                );
              }).toList(),
            ),
            const SizedBox(height: 16),

            // Input size slider
            Row(
              children: [
                Text(
                  'Input Size:',
                  style: TextStyle(
                    fontSize: 14,
                    fontWeight: FontWeight.bold,
                    color: _textColor,
                  ),
                ),
                const SizedBox(width: 8),
                Text(
                  _inputSize.toInt().toString(),
                  style: TextStyle(
                    fontSize: 14,
                    color: _primaryColor,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
            Slider(
              value: _inputSize,
              min: _minInputSize,
              max: _maxInputSize,
              divisions: 99,
              label: _inputSize.toInt().toString(),
              onChanged: (double value) {
                setState(() {
                  _inputSize = value;
                });
              },
              onChangeEnd: (double value) {
                _runAlgorithms();
              },
              activeColor: _primaryColor,
              inactiveColor: _primaryColor.withOpacity(0.2),
            ),
            const SizedBox(height: 16),

            // Performance visualization
            Container(
              height: 200,
              width: double.infinity,
              decoration: BoxDecoration(
                color: Colors.grey.withOpacity(0.1),
                borderRadius: BorderRadius.circular(8),
              ),
              child: _isRunning
                  ? const Center(child: CircularProgressIndicator())
                  : CustomPaint(
                      painter: AlgorithmPerformancePainter(
                        algorithms: _selectedAlgorithms,
                        operationCounts: _operationCounts,
                        executionTimes: _executionTimes,
                        primaryColor: _primaryColor,
                        secondaryColor: _secondaryColor,
                        tertiaryColor: _tertiaryColor,
                        textColor: _textColor,
                        animationValue: _animation.value,
                        algorithmNames: _getCurrentAlgorithms(),
                      ),
                    ),
            ),
            const SizedBox(height: 16),

            // Performance metrics
            Text(
              'Performance Metrics:',
              style: TextStyle(
                fontSize: 14,
                fontWeight: FontWeight.bold,
                color: _textColor,
              ),
            ),
            const SizedBox(height: 8),
            _isRunning
                ? const Center(child: CircularProgressIndicator())
                : Column(
                    children: _selectedAlgorithms.map((algo) {
                      final name = _getCurrentAlgorithms()[algo] ?? algo;
                      final operations = _operationCounts[algo] ?? 0;
                      final time = _executionTimes[algo] ?? 0.0;
                      return Padding(
                        padding: const EdgeInsets.only(bottom: 8.0),
                        child: Row(
                          children: [
                            Container(
                              width: 12,
                              height: 12,
                              decoration: BoxDecoration(
                                color: _getAlgorithmColor(algo),
                                shape: BoxShape.circle,
                              ),
                            ),
                            const SizedBox(width: 8),
                            Expanded(
                              child: Text(
                                name,
                                style: TextStyle(
                                  fontSize: 14,
                                  color: _textColor,
                                ),
                              ),
                            ),
                            Text(
                              'Operations: $operations',
                              style: TextStyle(
                                fontSize: 14,
                                color: _textColor,
                              ),
                            ),
                            const SizedBox(width: 16),
                            Text(
                              'Time: ${time.toStringAsFixed(2)} ms',
                              style: TextStyle(
                                fontSize: 14,
                                color: _textColor,
                              ),
                            ),
                          ],
                        ),
                      );
                    }).toList(),
                  ),
            const SizedBox(height: 16),

            // Big-O notation explanation
            ExpansionTile(
              title: Text(
                'Understanding Big-O Notation',
                style: TextStyle(
                  fontSize: 14,
                  fontWeight: FontWeight.bold,
                  color: _textColor,
                ),
              ),
              children: [
                Padding(
                  padding: const EdgeInsets.all(16.0),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'Big-O notation describes how an algorithm\'s performance scales with input size:',
                        style: TextStyle(
                          fontSize: 14,
                          color: _textColor,
                        ),
                      ),
                      const SizedBox(height: 8),
                      _buildBigOExplanationItem('O(1)', 'Constant time - Performance doesn\'t depend on input size'),
                      _buildBigOExplanationItem('O(log n)', 'Logarithmic time - Performance increases logarithmically with input size'),
                      _buildBigOExplanationItem('O(n)', 'Linear time - Performance scales linearly with input size'),
                      _buildBigOExplanationItem('O(n log n)', 'Linearithmic time - Common in efficient sorting algorithms'),
                      _buildBigOExplanationItem('O(n²)', 'Quadratic time - Performance scales with the square of input size'),
                      _buildBigOExplanationItem('O(2ⁿ)', 'Exponential time - Performance doubles with each additional input element'),
                    ],
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildBigOExplanationItem(String notation, String explanation) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 4.0),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            '$notation: ',
            style: TextStyle(
              fontSize: 14,
              fontWeight: FontWeight.bold,
              color: _primaryColor,
            ),
          ),
          Expanded(
            child: Text(
              explanation,
              style: TextStyle(
                fontSize: 14,
                color: _textColor,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Color _getAlgorithmColor(String algorithm) {
    final index = _selectedAlgorithms.indexOf(algorithm);
    switch (index) {
      case 0:
        return _primaryColor;
      case 1:
        return _secondaryColor;
      case 2:
        return _tertiaryColor;
      default:
        return Colors.grey;
    }
  }
}

class AlgorithmPerformancePainter extends CustomPainter {
  final List<String> algorithms;
  final Map<String, int> operationCounts;
  final Map<String, double> executionTimes;
  final Color primaryColor;
  final Color secondaryColor;
  final Color tertiaryColor;
  final Color textColor;
  final double animationValue;
  final Map<String, String> algorithmNames;

  AlgorithmPerformancePainter({
    required this.algorithms,
    required this.operationCounts,
    required this.executionTimes,
    required this.primaryColor,
    required this.secondaryColor,
    required this.tertiaryColor,
    required this.textColor,
    required this.animationValue,
    required this.algorithmNames,
  });

  @override
  void paint(Canvas canvas, Size size) {
    // Draw axes
    final axisPaint = Paint()
      ..color = textColor.withOpacity(0.5)
      ..style = PaintingStyle.stroke
      ..strokeWidth = 1.0;

    // X-axis
    canvas.drawLine(
      Offset(40, size.height - 40),
      Offset(size.width - 20, size.height - 40),
      axisPaint,
    );

    // Y-axis
    canvas.drawLine(
      Offset(40, 20),
      Offset(40, size.height - 40),
      axisPaint,
    );

    // Draw axis labels
    final textStyle = TextStyle(
      color: textColor.withOpacity(0.7),
      fontSize: 10,
    );

    // X-axis label
    final xLabelSpan = TextSpan(
      text: 'Input Size',
      style: textStyle,
    );
    final xLabelPainter = TextPainter(
      text: xLabelSpan,
      textDirection: TextDirection.ltr,
    );
    xLabelPainter.layout();
    xLabelPainter.paint(
      canvas,
      Offset(size.width / 2 - xLabelPainter.width / 2, size.height - 20),
    );

    // Y-axis label
    final yLabelSpan = TextSpan(
      text: 'Operations',
      style: textStyle,
    );
    final yLabelPainter = TextPainter(
      text: yLabelSpan,
      textDirection: TextDirection.ltr,
    );
    yLabelPainter.layout();

    // Rotate canvas to draw vertical text
    canvas.save();
    canvas.translate(15, size.height / 2 + yLabelPainter.width / 2);
    canvas.rotate(-math.pi / 2);
    yLabelPainter.paint(canvas, Offset.zero);
    canvas.restore();

    // Find max operation count for scaling
    int maxOperations = 1;
    for (var algo in algorithms) {
      final count = operationCounts[algo] ?? 0;
      if (count > maxOperations) {
        maxOperations = count;
      }
    }

    // Draw grid lines
    final gridPaint = Paint()
      ..color = textColor.withOpacity(0.1)
      ..style = PaintingStyle.stroke
      ..strokeWidth = 0.5;

    // Horizontal grid lines
    for (int i = 1; i <= 5; i++) {
      final y = size.height - 40 - (i * (size.height - 60) / 5);
      canvas.drawLine(
        Offset(40, y),
        Offset(size.width - 20, y),
        gridPaint,
      );

      // Draw y-axis values
      final value = (maxOperations * i / 5).toInt();
      final valueSpan = TextSpan(
        text: value.toString(),
        style: textStyle,
      );
      final valuePainter = TextPainter(
        text: valueSpan,
        textDirection: TextDirection.ltr,
      );
      valuePainter.layout();
      valuePainter.paint(
        canvas,
        Offset(35 - valuePainter.width, y - valuePainter.height / 2),
      );
    }

    // Draw complexity curves
    final graphWidth = size.width - 60;
    final graphHeight = size.height - 60;
    final origin = Offset(40, size.height - 40);

    // Draw theoretical complexity curves
    _drawComplexityCurve(
      canvas,
      size,
      origin,
      graphWidth,
      graphHeight,
      maxOperations,
      'O(1)',
      Colors.grey.withOpacity(0.3),
      1.0,
    );

    _drawComplexityCurve(
      canvas,
      size,
      origin,
      graphWidth,
      graphHeight,
      maxOperations,
      'O(log n)',
      Colors.grey.withOpacity(0.3),
      1.0,
    );

    _drawComplexityCurve(
      canvas,
      size,
      origin,
      graphWidth,
      graphHeight,
      maxOperations,
      'O(n)',
      Colors.grey.withOpacity(0.3),
      1.0,
    );

    _drawComplexityCurve(
      canvas,
      size,
      origin,
      graphWidth,
      graphHeight,
      maxOperations,
      'O(n log n)',
      Colors.grey.withOpacity(0.3),
      1.0,
    );

    _drawComplexityCurve(
      canvas,
      size,
      origin,
      graphWidth,
      graphHeight,
      maxOperations,
      'O(n²)',
      Colors.grey.withOpacity(0.3),
      1.0,
    );

    // Draw algorithm performance points
    for (int i = 0; i < algorithms.length; i++) {
      final algo = algorithms[i];
      final operations = operationCounts[algo] ?? 0;

      Color color;
      switch (i) {
        case 0:
          color = primaryColor;
          break;
        case 1:
          color = secondaryColor;
          break;
        case 2:
          color = tertiaryColor;
          break;
        default:
          color = Colors.grey;
      }

      // Draw point
      final pointPaint = Paint()
        ..color = color
        ..style = PaintingStyle.fill;

      final x = origin.dx + graphWidth * 0.8; // Fixed x position for current input size
      final y = origin.dy - (operations / maxOperations) * graphHeight * animationValue;

      canvas.drawCircle(Offset(x, y), 5, pointPaint);

      // Draw algorithm name
      final nameSpan = TextSpan(
        text: algorithmNames[algo]?.split(' - ')[0] ?? algo,
        style: TextStyle(
          color: color,
          fontSize: 10,
          fontWeight: FontWeight.bold,
        ),
      );
      final namePainter = TextPainter(
        text: nameSpan,
        textDirection: TextDirection.ltr,
      );
      namePainter.layout();
      namePainter.paint(
        canvas,
        Offset(x + 10, y - namePainter.height / 2),
      );
    }
  }

  void _drawComplexityCurve(
    Canvas canvas,
    Size size,
    Offset origin,
    double graphWidth,
    double graphHeight,
    int maxOperations,
    String complexityClass,
    Color color,
    double strokeWidth,
  ) {
    final curvePaint = Paint()
      ..color = color
      ..style = PaintingStyle.stroke
      ..strokeWidth = strokeWidth;

    final path = Path();
    path.moveTo(origin.dx, origin.dy);

    final complexityFunction = _getComplexityFunction(complexityClass);

    for (int i = 1; i <= 100; i++) {
      final x = origin.dx + (i / 100) * graphWidth;
      final inputSize = i * 10; // Scale input size
      final operations = complexityFunction(inputSize);

      // Scale operations to fit in graph
      final scaledOperations = operations / maxOperations * graphHeight;
      final y = origin.dy - scaledOperations * animationValue;

      path.lineTo(x, y);
    }

    canvas.drawPath(path, curvePaint);

    // Draw complexity class label
    final labelSpan = TextSpan(
      text: complexityClass,
      style: TextStyle(
        color: color,
        fontSize: 10,
      ),
    );
    final labelPainter = TextPainter(
      text: labelSpan,
      textDirection: TextDirection.ltr,
    );
    labelPainter.layout();

    // Position label at the end of the curve
    final inputSize = 1000.0;
    final operations = complexityFunction(inputSize);
    final scaledOperations = operations / maxOperations * graphHeight;
    final y = origin.dy - scaledOperations * animationValue;

    if (y > 20 && y < size.height - 40) {
      labelPainter.paint(
        canvas,
        Offset(origin.dx + graphWidth + 5, y - labelPainter.height / 2),
      );
    }
  }

  Function _getComplexityFunction(String complexityClass) {
    switch (complexityClass) {
      case 'O(1)':
        return (n) => 1;
      case 'O(log n)':
        return (n) => math.log(n) / math.log(2);
      case 'O(√n)':
        return (n) => math.sqrt(n);
      case 'O(n)':
        return (n) => n;
      case 'O(n log n)':
        return (n) => n * math.log(n) / math.log(2);
      case 'O(n²)':
        return (n) => n * n;
      case 'O(2ⁿ)':
        return (n) => math.pow(2, n);
      default:
        return (n) => n;
    }
  }

  @override
  bool shouldRepaint(covariant AlgorithmPerformancePainter oldDelegate) {
    return oldDelegate.animationValue != animationValue ||
        oldDelegate.algorithms != algorithms ||
        oldDelegate.operationCounts != operationCounts ||
        oldDelegate.executionTimes != executionTimes;
  }
}