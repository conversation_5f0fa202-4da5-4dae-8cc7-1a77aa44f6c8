import 'package:flutter/material.dart';
import 'dart:math' as math;

/// A widget that helps users build counterexamples to disprove mathematical statements
class InteractiveCounterexampleBuilderWidget extends StatefulWidget {
  /// The data for this widget
  final Map<String, dynamic> data;

  /// Optional callback when the widget state changes
  final Function(bool isCompleted)? onStateChanged;

  const InteractiveCounterexampleBuilderWidget({
    Key? key,
    required this.data,
    this.onStateChanged,
  }) : super(key: key);

  /// Factory constructor to create the widget from JSON data
  factory InteractiveCounterexampleBuilderWidget.fromData(Map<String, dynamic> data) {
    return InteractiveCounterexampleBuilderWidget(
      data: data,
    );
  }

  @override
  State<InteractiveCounterexampleBuilderWidget> createState() => _InteractiveCounterexampleBuilderWidgetState();
}

class _InteractiveCounterexampleBuilderWidgetState extends State<InteractiveCounterexampleBuilderWidget> {
  // Statement data
  late List<Statement> _statements;
  
  // Current state
  int _currentStatementIndex = 0;
  bool _isCompleted = false;
  bool _showHint = false;
  bool _showSolution = false;
  String? _errorMessage;
  
  // User input
  final TextEditingController _counterexampleController = TextEditingController();
  
  // UI customization
  late Color _primaryColor;
  late Color _successColor;
  late Color _errorColor;
  late Color _hintColor;
  late Color _neutralColor;

  @override
  void initState() {
    super.initState();
    
    // Parse statements
    _statements = [];
    final statementsData = widget.data['statements'] as List<dynamic>? ?? [];
    for (final statement in statementsData) {
      if (statement is Map<String, dynamic>) {
        _statements.add(Statement(
          statementText: statement['statement_text'] ?? '',
          isTrue: statement['is_true'] ?? false,
          hint: statement['hint'] ?? '',
          sampleCounterexample: statement['sample_counterexample'] ?? '',
          explanation: statement['explanation'] ?? '',
        ));
      }
    }
    
    // If no statements provided, create default statements
    if (_statements.isEmpty) {
      _statements = [
        Statement(
          statementText: 'All even numbers are divisible by 4.',
          isTrue: false,
          hint: 'Think about even numbers that are not multiples of 4.',
          sampleCounterexample: '6 is an even number, but it is not divisible by 4.',
          explanation: 'This statement is false. While all numbers divisible by 4 are even, not all even numbers are divisible by 4. For example, 2, 6, 10, and 14 are even numbers that are not divisible by 4.',
        ),
        Statement(
          statementText: 'If a quadrilateral has four equal sides, then it is a square.',
          isTrue: false,
          hint: 'Consider shapes with equal sides but different angles.',
          sampleCounterexample: 'A rhombus has four equal sides, but it is not a square if its angles are not 90 degrees.',
          explanation: 'This statement is false. A quadrilateral with four equal sides is called a rhombus. A square is a special case of a rhombus where all angles are 90 degrees. A rhombus with angles that are not 90 degrees is a counterexample.',
        ),
        Statement(
          statementText: 'If x² = 4, then x = 2.',
          isTrue: false,
          hint: 'Remember that a squared number can have two possible square roots.',
          sampleCounterexample: 'If x = -2, then x² = 4, but x ≠ 2.',
          explanation: 'This statement is false. The equation x² = 4 has two solutions: x = 2 and x = -2. So the statement "If x² = 4, then x = 2" is not always true, as x could also equal -2.',
        ),
        Statement(
          statementText: 'For all real numbers a and b, if a < b, then a² < b².',
          isTrue: false,
          hint: 'Consider what happens when both numbers are negative.',
          sampleCounterexample: 'If a = -3 and b = -2, then a < b, but a² = 9 and b² = 4, so a² > b².',
          explanation: 'This statement is false. When both a and b are negative and a < b, then a² > b². For example, if a = -3 and b = -2, then a < b, but a² = 9 and b² = 4, so a² > b².',
        ),
        Statement(
          statementText: 'The sum of two prime numbers is always odd.',
          isTrue: false,
          hint: 'Consider the smallest prime number and its properties.',
          sampleCounterexample: '3 + 2 = 5, which is odd, but 2 + 2 = 4, which is even.',
          explanation: 'This statement is false. While many pairs of prime numbers do sum to an odd number, there are counterexamples. The number 2 is the only even prime number. If we add 2 to any other prime number (which must be odd), we get an odd sum. But if we add 2 to itself, we get 2 + 2 = 4, which is even.',
        ),
      ];
    }
    
    // Initialize colors
    _primaryColor = _parseColor(widget.data['primary_color'], Colors.blue);
    _successColor = _parseColor(widget.data['success_color'], Colors.green);
    _errorColor = _parseColor(widget.data['error_color'], Colors.red);
    _hintColor = _parseColor(widget.data['hint_color'], Colors.orange);
    _neutralColor = _parseColor(widget.data['neutral_color'], Colors.grey.shade200);
  }

  @override
  void dispose() {
    _counterexampleController.dispose();
    super.dispose();
  }

  // Helper method to parse color from string
  Color _parseColor(dynamic colorValue, Color defaultColor) {
    if (colorValue == null) return defaultColor;
    if (colorValue is String) {
      try {
        return Color(int.parse(colorValue.replaceAll('#', '0xFF')));
      } catch (e) {
        return defaultColor;
      }
    }
    return defaultColor;
  }
  
  // Check the user's counterexample
  void _checkCounterexample() {
    final currentStatement = _statements[_currentStatementIndex];
    
    // Check if the statement is true (no counterexample possible)
    if (currentStatement.isTrue) {
      setState(() {
        _errorMessage = 'This statement is actually true! No counterexample exists.';
      });
      return;
    }
    
    // Check if the user provided a counterexample
    final counterexample = _counterexampleController.text.trim();
    if (counterexample.isEmpty) {
      setState(() {
        _errorMessage = 'Please enter a counterexample';
      });
      return;
    }
    
    // For a real app, you would implement a more sophisticated check
    // to validate if the user's counterexample is correct
    // For now, we'll just accept any non-empty input as a potential counterexample
    // for false statements
    
    setState(() {
      _showSolution = true;
      _errorMessage = null;
    });
  }
  
  // Move to the next statement
  void _nextStatement() {
    if (_currentStatementIndex < _statements.length - 1) {
      setState(() {
        _currentStatementIndex++;
        _resetStatementState();
      });
    } else {
      setState(() {
        _isCompleted = true;
      });
      
      // Notify parent about completion
      widget.onStateChanged?.call(true);
    }
  }
  
  // Reset the state for the current statement
  void _resetStatementState() {
    _counterexampleController.clear();
    _showHint = false;
    _showSolution = false;
    _errorMessage = null;
  }
  
  // Toggle hint visibility
  void _toggleHint() {
    setState(() {
      _showHint = !_showHint;
    });
  }
  
  // Reset the widget
  void _reset() {
    setState(() {
      _currentStatementIndex = 0;
      _isCompleted = false;
      _resetStatementState();
    });
    
    widget.onStateChanged?.call(false);
  }

  @override
  Widget build(BuildContext context) {
    final currentStatement = _statements[_currentStatementIndex];
    
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withOpacity(0.2),
            spreadRadius: 1,
            blurRadius: 3,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Title
          Text(
            'Counterexample Builder',
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
              color: _primaryColor,
            ),
          ),
          
          const SizedBox(height: 12),
          
          // Statement text
          Container(
            padding: const EdgeInsets.all(12),
            decoration: BoxDecoration(
              color: Colors.grey.shade50,
              borderRadius: BorderRadius.circular(8),
              border: Border.all(color: Colors.grey.shade300),
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'Statement ${_currentStatementIndex + 1} of ${_statements.length}:',
                  style: TextStyle(
                    fontWeight: FontWeight.bold,
                    color: _primaryColor,
                  ),
                ),
                const SizedBox(height: 8),
                Text(
                  currentStatement.statementText,
                  style: const TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
          ),
          
          const SizedBox(height: 16),
          
          if (_isCompleted) ...[
            // Completion message
            Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: _successColor.withOpacity(0.1),
                borderRadius: BorderRadius.circular(8),
                border: Border.all(color: _successColor),
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    children: [
                      Icon(Icons.check_circle, color: _successColor),
                      const SizedBox(width: 8),
                      Text(
                        'All Statements Completed!',
                        style: TextStyle(
                          fontWeight: FontWeight.bold,
                          color: _successColor,
                          fontSize: 16,
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 8),
                  const Text(
                    'You have successfully worked through all the statements and identified counterexamples for the false ones.',
                    style: TextStyle(fontSize: 14),
                  ),
                  const SizedBox(height: 12),
                  ElevatedButton(
                    onPressed: _reset,
                    style: ElevatedButton.styleFrom(
                      backgroundColor: _successColor,
                      foregroundColor: Colors.white,
                      padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(8),
                      ),
                    ),
                    child: const Text('Start Over'),
                  ),
                ],
              ),
            ),
          ] else ...[
            // Counterexample input
            Text(
              'Enter a counterexample (if one exists):',
              style: const TextStyle(fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 8),
            TextField(
              controller: _counterexampleController,
              decoration: InputDecoration(
                hintText: 'e.g., 6 is even but not divisible by 4',
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(8),
                ),
                focusedBorder: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(8),
                  borderSide: BorderSide(color: _primaryColor, width: 2),
                ),
              ),
              maxLines: 3,
            ),
            
            if (_errorMessage != null) ...[
              const SizedBox(height: 8),
              Text(
                _errorMessage!,
                style: TextStyle(color: _errorColor, fontStyle: FontStyle.italic),
              ),
            ],
            
            const SizedBox(height: 16),
            
            // Action buttons
            Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                // Check button
                if (!_showSolution)
                  ElevatedButton(
                    onPressed: _checkCounterexample,
                    style: ElevatedButton.styleFrom(
                      backgroundColor: _primaryColor,
                      foregroundColor: Colors.white,
                      padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(8),
                      ),
                    ),
                    child: const Text('Check'),
                  ),
                
                // Next button
                if (_showSolution)
                  ElevatedButton(
                    onPressed: _nextStatement,
                    style: ElevatedButton.styleFrom(
                      backgroundColor: _primaryColor,
                      foregroundColor: Colors.white,
                      padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(8),
                      ),
                    ),
                    child: const Text('Next Statement'),
                  ),
                
                const SizedBox(width: 16),
                
                // Hint button
                OutlinedButton(
                  onPressed: _toggleHint,
                  style: OutlinedButton.styleFrom(
                    foregroundColor: _hintColor,
                    side: BorderSide(color: _hintColor),
                    padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(8),
                    ),
                  ),
                  child: Text(_showHint ? 'Hide Hint' : 'Show Hint'),
                ),
              ],
            ),
            
            if (_showHint) ...[
              const SizedBox(height: 16),
              Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: _hintColor.withOpacity(0.1),
                  borderRadius: BorderRadius.circular(8),
                  border: Border.all(color: _hintColor),
                ),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      children: [
                        Icon(Icons.lightbulb, color: _hintColor),
                        const SizedBox(width: 8),
                        Text(
                          'Hint',
                          style: TextStyle(
                            fontWeight: FontWeight.bold,
                            color: _hintColor,
                            fontSize: 16,
                          ),
                        ),
                      ],
                    ),
                    const SizedBox(height: 8),
                    Text(
                      currentStatement.hint,
                      style: const TextStyle(fontSize: 14),
                    ),
                  ],
                ),
              ),
            ],
            
            if (_showSolution) ...[
              const SizedBox(height: 16),
              Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: _successColor.withOpacity(0.1),
                  borderRadius: BorderRadius.circular(8),
                  border: Border.all(color: _successColor),
                ),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      children: [
                        Icon(Icons.check_circle, color: _successColor),
                        const SizedBox(width: 8),
                        Text(
                          currentStatement.isTrue ? 'Correct! No counterexample exists.' : 'Good job!',
                          style: TextStyle(
                            fontWeight: FontWeight.bold,
                            color: _successColor,
                            fontSize: 16,
                          ),
                        ),
                      ],
                    ),
                    const SizedBox(height: 8),
                    if (!currentStatement.isTrue) ...[
                      Text(
                        'Sample counterexample: ${currentStatement.sampleCounterexample}',
                        style: const TextStyle(
                          fontSize: 14,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      const SizedBox(height: 8),
                    ],
                    Text(
                      'Explanation: ${currentStatement.explanation}',
                      style: const TextStyle(fontSize: 14),
                    ),
                  ],
                ),
              ),
            ],
          ],
          
          // Widget name tag
          if (widget.data['showNameTag'] ?? true)
            Padding(
              padding: const EdgeInsets.only(top: 8),
              child: Text(
                'InteractiveCounterexampleBuilderWidget',
                style: TextStyle(
                  color: Colors.grey[600],
                  fontSize: 12,
                  fontStyle: FontStyle.italic,
                ),
              ),
            ),
        ],
      ),
    );
  }
}

/// Represents a mathematical statement
class Statement {
  final String statementText;
  final bool isTrue;
  final String hint;
  final String sampleCounterexample;
  final String explanation;
  
  Statement({
    required this.statementText,
    required this.isTrue,
    required this.hint,
    required this.sampleCounterexample,
    required this.explanation,
  });
}
