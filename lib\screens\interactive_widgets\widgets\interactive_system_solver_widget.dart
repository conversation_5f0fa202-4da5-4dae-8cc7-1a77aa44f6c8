import 'package:flutter/material.dart';
import 'dart:math' as math;

/// A widget that allows users to solve systems of linear equations
class InteractiveSystemSolverWidget extends StatefulWidget {
  /// The data for this widget
  final Map<String, dynamic> data;

  /// Optional callback when the widget state changes
  final Function(bool isCompleted)? onStateChanged;

  const InteractiveSystemSolverWidget({
    Key? key,
    required this.data,
    this.onStateChanged,
  }) : super(key: key);

  /// Factory constructor to create the widget from JSON data
  factory InteractiveSystemSolverWidget.fromData(Map<String, dynamic> data) {
    return InteractiveSystemSolverWidget(
      data: data,
    );
  }

  @override
  State<InteractiveSystemSolverWidget> createState() => _InteractiveSystemSolverWidgetState();
}

class _InteractiveSystemSolverWidgetState extends State<InteractiveSystemSolverWidget> {
  // State variables
  bool _isCompleted = false;
  String _currentMethod = 'substitution'; // 'substitution', 'elimination', 'graphical'
  
  // System of equations
  List<Equation> _equations = [];
  
  // Solution
  double? _solutionX;
  double? _solutionY;
  String _solutionMessage = '';
  bool _hasSolution = false;
  
  // Step-by-step solution
  List<String> _solutionSteps = [];
  int _currentStep = 0;
  bool _showingSolution = false;
  
  // Controllers for equation inputs
  final List<TextEditingController> _aControllers = [];
  final List<TextEditingController> _bControllers = [];
  final List<TextEditingController> _cControllers = [];
  
  // Colors
  late Color _primaryColor;
  late Color _secondaryColor;
  late Color _accentColor;
  late Color _textColor;
  
  @override
  void initState() {
    super.initState();
    _initializeFromData();
  }
  
  @override
  void dispose() {
    // Dispose all controllers
    for (var controller in _aControllers) {
      controller.dispose();
    }
    for (var controller in _bControllers) {
      controller.dispose();
    }
    for (var controller in _cControllers) {
      controller.dispose();
    }
    super.dispose();
  }
  
  void _initializeFromData() {
    // Initialize colors
    _primaryColor = _parseColor(widget.data['primaryColor'] ?? '#2196F3');
    _secondaryColor = _parseColor(widget.data['secondaryColor'] ?? '#FF9800');
    _accentColor = _parseColor(widget.data['accentColor'] ?? '#4CAF50');
    _textColor = _parseColor(widget.data['textColor'] ?? '#212121');
    
    // Initialize method
    _currentMethod = widget.data['initialMethod'] ?? 'substitution';
    
    // Initialize equations
    final initialEquations = widget.data['initialEquations'] as List<dynamic>? ?? [];
    
    if (initialEquations.isNotEmpty) {
      for (var eqData in initialEquations) {
        final a = (eqData['a'] as num).toDouble();
        final b = (eqData['b'] as num).toDouble();
        final c = (eqData['c'] as num).toDouble();
        
        _equations.add(Equation(a: a, b: b, c: c));
        
        // Create controllers for this equation
        _aControllers.add(TextEditingController(text: a.toString()));
        _bControllers.add(TextEditingController(text: b.toString()));
        _cControllers.add(TextEditingController(text: c.toString()));
      }
    } else {
      // Create default equations if none provided
      _equations = [
        Equation(a: 1, b: 1, c: 10),
        Equation(a: 2, b: -1, c: 5),
      ];
      
      // Create controllers for default equations
      _aControllers.add(TextEditingController(text: '1'));
      _bControllers.add(TextEditingController(text: '1'));
      _cControllers.add(TextEditingController(text: '10'));
      
      _aControllers.add(TextEditingController(text: '2'));
      _bControllers.add(TextEditingController(text: '-1'));
      _cControllers.add(TextEditingController(text: '5'));
    }
  }
  
  Color _parseColor(String colorString) {
    if (colorString.startsWith('#')) {
      return Color(int.parse('FF${colorString.substring(1)}', radix: 16));
    }
    return Colors.black;
  }
  
  void _updateEquationFromControllers(int index) {
    try {
      final a = double.parse(_aControllers[index].text);
      final b = double.parse(_bControllers[index].text);
      final c = double.parse(_cControllers[index].text);
      
      setState(() {
        _equations[index] = Equation(a: a, b: b, c: c);
        _resetSolution();
      });
    } catch (e) {
      // Handle parsing errors
      print('Error parsing equation values: $e');
    }
  }
  
  void _resetSolution() {
    setState(() {
      _solutionX = null;
      _solutionY = null;
      _solutionMessage = '';
      _hasSolution = false;
      _solutionSteps = [];
      _currentStep = 0;
      _showingSolution = false;
      _isCompleted = false;
    });
    
    // Notify parent of completion status
    widget.onStateChanged?.call(_isCompleted);
  }
  
  void _solveSystem() {
    if (_equations.length < 2) {
      setState(() {
        _solutionMessage = 'Need at least 2 equations to solve a system.';
        _hasSolution = false;
      });
      return;
    }
    
    setState(() {
      _solutionSteps = [];
      _currentStep = 0;
      _showingSolution = true;
    });
    
    switch (_currentMethod) {
      case 'substitution':
        _solveBySubstitution();
        break;
      case 'elimination':
        _solveByElimination();
        break;
      case 'graphical':
        _solveGraphically();
        break;
    }
    
    // Mark as completed if we have a solution
    setState(() {
      _isCompleted = _hasSolution;
    });
    
    // Notify parent of completion status
    widget.onStateChanged?.call(_isCompleted);
  }
  
  void _solveBySubstitution() {
    // Get the two equations
    final eq1 = _equations[0];
    final eq2 = _equations[1];
    
    // Step 1: Solve for y in terms of x from the first equation
    _solutionSteps.add('Step 1: Solve for y in terms of x from the first equation: ${eq1.toString()}');
    
    if (eq1.b == 0) {
      setState(() {
        _solutionMessage = 'Cannot solve using substitution method with these equations.';
        _hasSolution = false;
      });
      return;
    }
    
    final yCoefficient = eq1.b;
    final yConstant = eq1.c - eq1.a;
    
    _solutionSteps.add('$yCoefficient y = $yConstant x + ${eq1.c}');
    _solutionSteps.add('y = (${yConstant}x + ${eq1.c}) / $yCoefficient');
    
    // Step 2: Substitute this expression into the second equation
    _solutionSteps.add('Step 2: Substitute this expression for y into the second equation: ${eq2.toString()}');
    
    final substitutedEq = '${eq2.a}x + ${eq2.b} * ((${yConstant}x + ${eq1.c}) / $yCoefficient) = ${eq2.c}';
    _solutionSteps.add(substitutedEq);
    
    // Step 3: Solve for x
    _solutionSteps.add('Step 3: Solve for x');
    
    // Calculate x
    // (a2 * b1 - a1 * b2) * x = (c2 * b1 - c1 * b2)
    final numerator = eq2.c * eq1.b - eq1.c * eq2.b;
    final denominator = eq2.a * eq1.b - eq1.a * eq2.b;
    
    if (denominator == 0) {
      if (numerator == 0) {
        setState(() {
          _solutionMessage = 'The system has infinitely many solutions.';
          _hasSolution = false;
        });
      } else {
        setState(() {
          _solutionMessage = 'The system has no solution.';
          _hasSolution = false;
        });
      }
      return;
    }
    
    final x = numerator / denominator;
    _solutionSteps.add('x = $x');
    
    // Step 4: Substitute x back to find y
    _solutionSteps.add('Step 4: Substitute x = $x back into y = (${yConstant}x + ${eq1.c}) / $yCoefficient to find y');
    
    final y = (yConstant * x + eq1.c) / yCoefficient;
    _solutionSteps.add('y = $y');
    
    // Set the solution
    setState(() {
      _solutionX = x;
      _solutionY = y;
      _solutionMessage = 'Solution: x = $x, y = $y';
      _hasSolution = true;
    });
  }
  
  void _solveByElimination() {
    // Get the two equations
    final eq1 = _equations[0];
    final eq2 = _equations[1];
    
    // Step 1: Prepare equations for elimination
    _solutionSteps.add('Step 1: Prepare equations for elimination');
    _solutionSteps.add('Equation 1: ${eq1.toString()}');
    _solutionSteps.add('Equation 2: ${eq2.toString()}');
    
    // Step 2: Multiply equations to eliminate one variable
    _solutionSteps.add('Step 2: Multiply equations to eliminate one variable');
    
    // Try to eliminate y first
    final lcm = _lcm(eq1.b.abs().toInt(), eq2.b.abs().toInt());
    final factor1 = lcm ~/ eq1.b.abs().toInt() * (eq1.b > 0 ? 1 : -1);
    final factor2 = lcm ~/ eq2.b.abs().toInt() * (eq2.b > 0 ? -1 : 1);
    
    final newEq1 = Equation(
      a: eq1.a * factor1,
      b: eq1.b * factor1,
      c: eq1.c * factor1,
    );
    
    final newEq2 = Equation(
      a: eq2.a * factor2,
      b: eq2.b * factor2,
      c: eq2.c * factor2,
    );
    
    _solutionSteps.add('Multiply Equation 1 by $factor1: ${newEq1.toString()}');
    _solutionSteps.add('Multiply Equation 2 by $factor2: ${newEq2.toString()}');
    
    // Step 3: Add the equations to eliminate y
    _solutionSteps.add('Step 3: Add the equations to eliminate y');
    
    final combinedA = newEq1.a + newEq2.a;
    final combinedB = newEq1.b + newEq2.b; // Should be 0
    final combinedC = newEq1.c + newEq2.c;
    
    _solutionSteps.add('${newEq1.a}x + ${newEq1.b}y + ${newEq2.a}x + ${newEq2.b}y = ${newEq1.c} + ${newEq2.c}');
    _solutionSteps.add('${combinedA}x + ${combinedB}y = $combinedC');
    
    if (combinedA == 0) {
      if (combinedC == 0) {
        setState(() {
          _solutionMessage = 'The system has infinitely many solutions.';
          _hasSolution = false;
        });
      } else {
        setState(() {
          _solutionMessage = 'The system has no solution.';
          _hasSolution = false;
        });
      }
      return;
    }
    
    // Step 4: Solve for x
    _solutionSteps.add('Step 4: Solve for x');
    
    final x = combinedC / combinedA;
    _solutionSteps.add('x = $x');
    
    // Step 5: Substitute x back into one of the original equations to find y
    _solutionSteps.add('Step 5: Substitute x = $x back into the first equation to find y');
    
    if (eq1.b == 0) {
      // Use the second equation if the first has no y term
      final y = (eq2.c - eq2.a * x) / eq2.b;
      _solutionSteps.add('y = (${eq2.c} - ${eq2.a} * $x) / ${eq2.b} = $y');
      
      setState(() {
        _solutionX = x;
        _solutionY = y;
        _solutionMessage = 'Solution: x = $x, y = $y';
        _hasSolution = true;
      });
    } else {
      final y = (eq1.c - eq1.a * x) / eq1.b;
      _solutionSteps.add('y = (${eq1.c} - ${eq1.a} * $x) / ${eq1.b} = $y');
      
      setState(() {
        _solutionX = x;
        _solutionY = y;
        _solutionMessage = 'Solution: x = $x, y = $y';
        _hasSolution = true;
      });
    }
  }
  
  void _solveGraphically() {
    // Get the two equations
    final eq1 = _equations[0];
    final eq2 = _equations[1];
    
    // Step 1: Convert to slope-intercept form (y = mx + b)
    _solutionSteps.add('Step 1: Convert equations to slope-intercept form (y = mx + b)');
    
    if (eq1.b == 0 || eq2.b == 0) {
      setState(() {
        _solutionMessage = 'Cannot convert to slope-intercept form with vertical lines.';
        _hasSolution = false;
      });
      return;
    }
    
    final slope1 = -eq1.a / eq1.b;
    final intercept1 = eq1.c / eq1.b;
    
    final slope2 = -eq2.a / eq2.b;
    final intercept2 = eq2.c / eq2.b;
    
    _solutionSteps.add('Line 1: y = ${slope1}x + $intercept1');
    _solutionSteps.add('Line 2: y = ${slope2}x + $intercept2');
    
    // Step 2: Find the intersection point
    _solutionSteps.add('Step 2: Find the intersection point by setting the equations equal');
    _solutionSteps.add('${slope1}x + $intercept1 = ${slope2}x + $intercept2');
    
    // Step 3: Solve for x
    _solutionSteps.add('Step 3: Solve for x');
    _solutionSteps.add('${slope1 - slope2}x = ${intercept2 - intercept1}');
    
    if (slope1 == slope2) {
      if (intercept1 == intercept2) {
        setState(() {
          _solutionMessage = 'The lines are identical. The system has infinitely many solutions.';
          _hasSolution = false;
        });
      } else {
        setState(() {
          _solutionMessage = 'The lines are parallel. The system has no solution.';
          _hasSolution = false;
        });
      }
      return;
    }
    
    final x = (intercept2 - intercept1) / (slope1 - slope2);
    _solutionSteps.add('x = $x');
    
    // Step 4: Substitute x to find y
    _solutionSteps.add('Step 4: Substitute x = $x into either equation to find y');
    
    final y = slope1 * x + intercept1;
    _solutionSteps.add('y = $slope1 * $x + $intercept1 = $y');
    
    // Set the solution
    setState(() {
      _solutionX = x;
      _solutionY = y;
      _solutionMessage = 'Solution: x = $x, y = $y';
      _hasSolution = true;
    });
  }
  
  int _gcd(int a, int b) {
    while (b != 0) {
      final t = b;
      b = a % b;
      a = t;
    }
    return a;
  }
  
  int _lcm(int a, int b) {
    return (a * b) ~/ _gcd(a, b);
  }
  
  void _nextStep() {
    if (_currentStep < _solutionSteps.length - 1) {
      setState(() {
        _currentStep++;
      });
    }
  }
  
  void _previousStep() {
    if (_currentStep > 0) {
      setState(() {
        _currentStep--;
      });
    }
  }
  
  void _setMethod(String method) {
    setState(() {
      _currentMethod = method;
      _resetSolution();
    });
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withOpacity(0.2),
            spreadRadius: 1,
            blurRadius: 3,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Title
          Text(
            widget.data['title'] ?? 'System of Equations Solver',
            style: const TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
            ),
          ),
          
          const SizedBox(height: 8),
          
          // Description
          if (widget.data['description'] != null)
            Text(
              widget.data['description'],
              style: const TextStyle(
                fontSize: 14,
                color: Colors.grey,
              ),
            ),
          
          const SizedBox(height: 16),
          
          // Method selection
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceEvenly,
            children: [
              _buildMethodButton('Substitution', 'substitution', Icons.swap_horiz),
              _buildMethodButton('Elimination', 'elimination', Icons.remove_circle_outline),
              _buildMethodButton('Graphical', 'graphical', Icons.timeline),
            ],
          ),
          
          const SizedBox(height: 24),
          
          // Equation inputs
          ..._buildEquationInputs(),
          
          const SizedBox(height: 24),
          
          // Solve button
          Center(
            child: ElevatedButton.icon(
              icon: const Icon(Icons.calculate),
              label: const Text('Solve System'),
              style: ElevatedButton.styleFrom(
                backgroundColor: _primaryColor,
                foregroundColor: Colors.white,
                padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
              ),
              onPressed: _solveSystem,
            ),
          ),
          
          const SizedBox(height: 24),
          
          // Solution display
          if (_showingSolution) ...[
            const Divider(),
            
            // Step navigation
            if (_solutionSteps.isNotEmpty) ...[
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  IconButton(
                    icon: const Icon(Icons.arrow_back),
                    onPressed: _currentStep > 0 ? _previousStep : null,
                    tooltip: 'Previous Step',
                  ),
                  Text(
                    'Step ${_currentStep + 1} of ${_solutionSteps.length}',
                    style: const TextStyle(fontWeight: FontWeight.bold),
                  ),
                  IconButton(
                    icon: const Icon(Icons.arrow_forward),
                    onPressed: _currentStep < _solutionSteps.length - 1 ? _nextStep : null,
                    tooltip: 'Next Step',
                  ),
                ],
              ),
              
              // Current step
              Container(
                padding: const EdgeInsets.all(16),
                decoration: BoxDecoration(
                  color: Colors.grey[100],
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Text(_solutionSteps[_currentStep]),
              ),
            ],
            
            const SizedBox(height: 16),
            
            // Final solution
            if (_hasSolution)
              Container(
                padding: const EdgeInsets.all(16),
                decoration: BoxDecoration(
                  color: _accentColor.withOpacity(0.1),
                  borderRadius: BorderRadius.circular(8),
                  border: Border.all(color: _accentColor),
                ),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Solution:',
                      style: TextStyle(
                        fontWeight: FontWeight.bold,
                        color: _accentColor,
                      ),
                    ),
                    const SizedBox(height: 8),
                    Text('x = ${_solutionX?.toStringAsFixed(4)}'),
                    Text('y = ${_solutionY?.toStringAsFixed(4)}'),
                  ],
                ),
              )
            else
              Container(
                padding: const EdgeInsets.all(16),
                decoration: BoxDecoration(
                  color: Colors.red[50],
                  borderRadius: BorderRadius.circular(8),
                  border: Border.all(color: Colors.red),
                ),
                child: Text(
                  _solutionMessage,
                  style: const TextStyle(color: Colors.red),
                ),
              ),
          ],
          
          // Widget name tag
          if (widget.data['showNameTag'] ?? true)
            Padding(
              padding: const EdgeInsets.only(top: 8),
              child: Text(
                'InteractiveSystemSolver',
                style: TextStyle(
                  color: Colors.grey[600],
                  fontSize: 12,
                  fontStyle: FontStyle.italic,
                ),
              ),
            ),
        ],
      ),
    );
  }
  
  Widget _buildMethodButton(String label, String method, IconData icon) {
    final isSelected = _currentMethod == method;
    
    return ElevatedButton.icon(
      icon: Icon(icon, size: 16),
      label: Text(label),
      style: ElevatedButton.styleFrom(
        backgroundColor: isSelected ? _primaryColor : Colors.grey[200],
        foregroundColor: isSelected ? Colors.white : Colors.black87,
        padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
      ),
      onPressed: () => _setMethod(method),
    );
  }
  
  List<Widget> _buildEquationInputs() {
    final widgets = <Widget>[];
    
    for (var i = 0; i < _equations.length; i++) {
      widgets.add(
        Padding(
          padding: const EdgeInsets.only(bottom: 16),
          child: Row(
            children: [
              Expanded(
                child: TextField(
                  controller: _aControllers[i],
                  keyboardType: const TextInputType.numberWithOptions(decimal: true, signed: true),
                  decoration: InputDecoration(
                    labelText: 'a${i+1}',
                    border: const OutlineInputBorder(),
                  ),
                  onChanged: (_) => _updateEquationFromControllers(i),
                ),
              ),
              const SizedBox(width: 8),
              const Text('x +'),
              const SizedBox(width: 8),
              Expanded(
                child: TextField(
                  controller: _bControllers[i],
                  keyboardType: const TextInputType.numberWithOptions(decimal: true, signed: true),
                  decoration: InputDecoration(
                    labelText: 'b${i+1}',
                    border: const OutlineInputBorder(),
                  ),
                  onChanged: (_) => _updateEquationFromControllers(i),
                ),
              ),
              const SizedBox(width: 8),
              const Text('y ='),
              const SizedBox(width: 8),
              Expanded(
                child: TextField(
                  controller: _cControllers[i],
                  keyboardType: const TextInputType.numberWithOptions(decimal: true, signed: true),
                  decoration: InputDecoration(
                    labelText: 'c${i+1}',
                    border: const OutlineInputBorder(),
                  ),
                  onChanged: (_) => _updateEquationFromControllers(i),
                ),
              ),
            ],
          ),
        ),
      );
    }
    
    return widgets;
  }
}

class Equation {
  final double a; // coefficient of x
  final double b; // coefficient of y
  final double c; // constant term
  
  Equation({
    required this.a,
    required this.b,
    required this.c,
  });
  
  @override
  String toString() {
    return '${a}x + ${b}y = $c';
  }
}
