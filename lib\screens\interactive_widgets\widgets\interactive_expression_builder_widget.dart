import 'package:flutter/material.dart';

/// A widget that allows users to build algebraic expressions by dragging and dropping terms
class InteractiveExpressionBuilderWidget extends StatefulWidget {
  /// The data for this widget
  final Map<String, dynamic> data;

  /// Optional callback when the widget state changes
  final Function(bool isCompleted)? onStateChanged;

  const InteractiveExpressionBuilderWidget({
    Key? key,
    required this.data,
    this.onStateChanged,
  }) : super(key: key);

  /// Factory constructor to create the widget from JSON data
  factory InteractiveExpressionBuilderWidget.fromData(Map<String, dynamic> data) {
    return InteractiveExpressionBuilderWidget(
      data: data,
    );
  }

  @override
  State<InteractiveExpressionBuilderWidget> createState() => _InteractiveExpressionBuilderWidgetState();
}

class _InteractiveExpressionBuilderWidgetState extends State<InteractiveExpressionBuilderWidget> {
  // State variables
  bool _isCompleted = false;
  List<String> _availableTerms = [];
  List<String> _builtExpression = [];
  String _targetExpression = '';
  String _currentChallenge = '';
  int _currentChallengeIndex = 0;
  List<Map<String, dynamic>> _challenges = [];
  
  // Colors
  late Color _primaryColor;
  late Color _secondaryColor;
  late Color _accentColor;
  late Color _textColor;
  late Color _successColor;
  late Color _errorColor;

  @override
  void initState() {
    super.initState();
    _initializeFromData();
  }

  void _initializeFromData() {
    // Initialize colors
    _primaryColor = _parseColor(widget.data['primaryColor'] ?? '#2196F3');
    _secondaryColor = _parseColor(widget.data['secondaryColor'] ?? '#4CAF50');
    _accentColor = _parseColor(widget.data['accentColor'] ?? '#FF9800');
    _textColor = _parseColor(widget.data['textColor'] ?? '#212121');
    _successColor = _parseColor(widget.data['successColor'] ?? '#4CAF50');
    _errorColor = _parseColor(widget.data['errorColor'] ?? '#F44336');
    
    // Initialize challenges
    _challenges = List<Map<String, dynamic>>.from(widget.data['challenges'] ?? []);
    
    if (_challenges.isNotEmpty) {
      _loadChallenge(0);
    }
  }

  void _loadChallenge(int index) {
    if (index < 0 || index >= _challenges.length) return;
    
    final challenge = _challenges[index];
    
    setState(() {
      _currentChallengeIndex = index;
      _currentChallenge = challenge['description'] ?? '';
      _targetExpression = challenge['targetExpression'] ?? '';
      _availableTerms = List<String>.from(challenge['availableTerms'] ?? []);
      _builtExpression = [];
      _isCompleted = false;
    });
  }

  Color _parseColor(String hexColor) {
    hexColor = hexColor.replaceAll('#', '');
    if (hexColor.length == 6) {
      hexColor = 'FF$hexColor';
    }
    return Color(int.parse(hexColor, radix: 16));
  }

  void _addTerm(String term) {
    setState(() {
      _builtExpression.add(term);
      _availableTerms.remove(term);
      _checkCompletion();
    });
  }

  void _removeTerm(int index) {
    if (index < 0 || index >= _builtExpression.length) return;
    
    setState(() {
      final term = _builtExpression[index];
      _builtExpression.removeAt(index);
      _availableTerms.add(term);
      _isCompleted = false;
    });
  }

  void _checkCompletion() {
    final currentExpression = _builtExpression.join(' ');
    final isCorrect = currentExpression == _targetExpression;
    
    if (isCorrect && !_isCompleted) {
      setState(() {
        _isCompleted = true;
      });
      widget.onStateChanged?.call(true);
    }
  }

  void _resetChallenge() {
    final challenge = _challenges[_currentChallengeIndex];
    
    setState(() {
      _availableTerms = List<String>.from(challenge['availableTerms'] ?? []);
      _builtExpression = [];
      _isCompleted = false;
    });
  }

  void _nextChallenge() {
    if (_currentChallengeIndex < _challenges.length - 1) {
      _loadChallenge(_currentChallengeIndex + 1);
    }
  }

  void _previousChallenge() {
    if (_currentChallengeIndex > 0) {
      _loadChallenge(_currentChallengeIndex - 1);
    }
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withOpacity(0.2),
            spreadRadius: 1,
            blurRadius: 3,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Title
          Text(
            widget.data['title'] ?? 'Expression Builder',
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
              color: _textColor,
            ),
          ),
          
          const SizedBox(height: 12),
          
          // Challenge description
          Container(
            padding: const EdgeInsets.all(12),
            decoration: BoxDecoration(
              color: _primaryColor.withOpacity(0.1),
              borderRadius: BorderRadius.circular(8),
              border: Border.all(color: _primaryColor.withOpacity(0.3)),
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'Challenge ${_currentChallengeIndex + 1}/${_challenges.length}',
                  style: TextStyle(
                    fontWeight: FontWeight.bold,
                    color: _primaryColor,
                  ),
                ),
                const SizedBox(height: 4),
                Text(
                  _currentChallenge,
                  style: TextStyle(
                    fontSize: 14,
                    color: _textColor,
                  ),
                ),
              ],
            ),
          ),
          
          const SizedBox(height: 16),
          
          // Built expression
          Container(
            padding: const EdgeInsets.all(12),
            decoration: BoxDecoration(
              color: Colors.grey.shade50,
              borderRadius: BorderRadius.circular(8),
              border: Border.all(
                color: _isCompleted ? _successColor : Colors.grey.shade300,
                width: _isCompleted ? 2 : 1,
              ),
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'Your Expression:',
                  style: TextStyle(
                    fontWeight: FontWeight.bold,
                    color: _textColor,
                  ),
                ),
                const SizedBox(height: 8),
                if (_builtExpression.isEmpty)
                  Container(
                    height: 40,
                    alignment: Alignment.center,
                    child: Text(
                      'Drag terms here to build your expression',
                      style: TextStyle(
                        color: Colors.grey.shade500,
                        fontStyle: FontStyle.italic,
                      ),
                    ),
                  )
                else
                  Wrap(
                    spacing: 8,
                    runSpacing: 8,
                    children: List.generate(_builtExpression.length, (index) {
                      return _buildDraggableTerm(
                        _builtExpression[index],
                        isInExpression: true,
                        index: index,
                      );
                    }),
                  ),
                if (_isCompleted)
                  Padding(
                    padding: const EdgeInsets.only(top: 8),
                    child: Row(
                      children: [
                        Icon(Icons.check_circle, color: _successColor, size: 16),
                        const SizedBox(width: 4),
                        Text(
                          'Correct!',
                          style: TextStyle(
                            color: _successColor,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                      ],
                    ),
                  ),
              ],
            ),
          ),
          
          const SizedBox(height: 16),
          
          // Available terms
          Container(
            padding: const EdgeInsets.all(12),
            decoration: BoxDecoration(
              color: Colors.grey.shade100,
              borderRadius: BorderRadius.circular(8),
              border: Border.all(color: Colors.grey.shade300),
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'Available Terms:',
                  style: TextStyle(
                    fontWeight: FontWeight.bold,
                    color: _textColor,
                  ),
                ),
                const SizedBox(height: 8),
                if (_availableTerms.isEmpty)
                  Container(
                    height: 40,
                    alignment: Alignment.center,
                    child: Text(
                      'No more terms available',
                      style: TextStyle(
                        color: Colors.grey.shade500,
                        fontStyle: FontStyle.italic,
                      ),
                    ),
                  )
                else
                  Wrap(
                    spacing: 8,
                    runSpacing: 8,
                    children: _availableTerms.map((term) {
                      return _buildDraggableTerm(term);
                    }).toList(),
                  ),
              ],
            ),
          ),
          
          const SizedBox(height: 16),
          
          // Navigation buttons
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              // Previous/Reset buttons
              Row(
                children: [
                  // Previous challenge button
                  if (_currentChallengeIndex > 0)
                    ElevatedButton.icon(
                      onPressed: _previousChallenge,
                      icon: const Icon(Icons.arrow_back),
                      label: const Text('Previous'),
                      style: ElevatedButton.styleFrom(
                        backgroundColor: Colors.grey.shade200,
                        foregroundColor: Colors.black87,
                      ),
                    ),
                  
                  const SizedBox(width: 8),
                  
                  // Reset button
                  ElevatedButton.icon(
                    onPressed: _resetChallenge,
                    icon: const Icon(Icons.refresh),
                    label: const Text('Reset'),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.grey.shade200,
                      foregroundColor: Colors.black87,
                    ),
                  ),
                ],
              ),
              
              // Next challenge button
              if (_isCompleted && _currentChallengeIndex < _challenges.length - 1)
                ElevatedButton.icon(
                  onPressed: _nextChallenge,
                  icon: const Icon(Icons.arrow_forward),
                  label: const Text('Next Challenge'),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: _primaryColor,
                    foregroundColor: Colors.white,
                  ),
                ),
            ],
          ),
          
          // Widget name tag at the bottom
          if (widget.data['showNameTag'] ?? true)
            Padding(
              padding: const EdgeInsets.only(top: 8),
              child: Text(
                'InteractiveExpressionBuilder',
                style: TextStyle(
                  color: Colors.grey[600],
                  fontSize: 12,
                  fontStyle: FontStyle.italic,
                ),
              ),
            ),
        ],
      ),
    );
  }
  
  Widget _buildDraggableTerm(String term, {bool isInExpression = false, int index = -1}) {
    final termWidget = Container(
      padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
      decoration: BoxDecoration(
        color: isInExpression ? _primaryColor : _accentColor,
        borderRadius: BorderRadius.circular(6),
      ),
      child: Text(
        term,
        style: const TextStyle(
          color: Colors.white,
          fontWeight: FontWeight.bold,
        ),
      ),
    );
    
    if (isInExpression) {
      return GestureDetector(
        onTap: () => _removeTerm(index),
        child: termWidget,
      );
    } else {
      return GestureDetector(
        onTap: () => _addTerm(term),
        child: termWidget,
      );
    }
  }
}
