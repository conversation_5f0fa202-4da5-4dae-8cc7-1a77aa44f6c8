# Brilliant.org-Style Content Development Plan

## Overview

This document outlines our plan to develop engaging, interactive educational content for our app, inspired by Brilliant.org's approach. The content will be organized hierarchically as follows:

1. **Categories**: Broad subject areas (e.g., Maths, Science, Logic)
2. **Courses**: Specific learning paths within a Category (e.g., "Mathematical Thinking" within Maths)
3. **Modules (Levels)**: Thematic sections within Courses (5-10 per course)
4. **Lessons**: Core building blocks where active learning occurs (3-7 per module)

## Content Design Principles

### User Experience
- **Single Scrollable Page**: Lessons will appear on a single scrollable page instead of requiring navigation back to the roadmap between lessons
- **Smooth Transitions**: Large horizontal continue buttons with smooth automatic scrolling to new content
- **Interactive Elements**: Auto-expanding widgets when continuing to new content
- **Skip/Continue Button Logic**: Continue button changes to 'skip' when interactive content is incomplete, with skip showing the solution automatically before allowing continuation

### Content Quality
- **Rich Media**: Engaging content including text, GIFs, animations, and videos
- **Interactive Widgets**: High-quality, well-designed interactive elements focused on mathematical thinking
- **Widget Design**: Not buggy or too large, with appropriate sizing and responsive design
- **Visual Appeal**: Clean, modern design with consistent styling

## Available Content Block Types

Our app supports the following content block types, each designed to provide a different type of learning experience:

1. **TextBlock**: Text content with optional Markdown support
   - Properties: `id`, `order`, `content`, `isMarkdown`
   - Supports rich text formatting when `isMarkdown` is true
   - Used for explanations, introductions, and conceptual content

2. **ImageBlock**: Static images with optional captions
   - Properties: `id`, `order`, `imagePath`, `caption`, `width`, `height`
   - Supports various image formats (PNG, JPG, etc.)
   - Used for diagrams, illustrations, and visual examples

3. **VideoBlock**: Video content with thumbnails and duration
   - Properties: `id`, `order`, `videoPath`, `thumbnailPath`, `duration`
   - Shows a thumbnail with play button before video starts
   - Used for demonstrations, explanations, and visual learning

4. **AnimationBlock**: Animated content including GIFs, Lottie animations, and WebP
   - Properties: `id`, `order`, `animationPath`, `caption`, `width`, `height`, `autoPlay`, `duration`
   - Supports multiple animation formats:
     - GIF: Standard animated GIFs
     - WebP: Animated WebP images (more efficient than GIFs)
     - Lottie: JSON-based animations with controller support
   - Can be set to auto-play or include play/pause controls
   - Used for demonstrating processes, visualizing concepts, and engaging learners

5. **QuizBlock**: Interactive quiz questions with feedback
   - Properties: `id`, `order`, `question`, `options`, `correctOptionIndex`, `explanation`
   - Shows immediate feedback on answer selection
   - Includes optional explanation that can be expanded
   - Used for knowledge checks and reinforcing learning

6. **InteractiveBlock**: Custom interactive widgets for hands-on learning
   - Properties: `id`, `order`, `interactiveType`, `data`
   - The `interactiveType` determines which widget is displayed
   - The `data` object contains widget-specific configuration
   - Used for experiential learning and concept exploration

## Interactive Widget Types

Based on the codebase analysis, we have the following interactive widget types implemented:

1. **Fraction Visualizer** (`fraction-visualizer`)
   - Visualize fractions and operations
   - Icon: `pie_chart`
   - Useful for: Basic math, fractions, proportions

2. **Equation Solver** (`equation-solver`)
   - Interactive equation solving with step-by-step guidance
   - Icon: `functions`
   - Data properties: `equation`, `variable`, `correctAnswer`, `solutionSteps`
   - Useful for: Algebra, equations, step-by-step problem solving

3. **Force Simulator** (`force-simulator`)
   - Simulate forces and motion
   - Icon: `speed`
   - Data properties: `initialForce`
   - Useful for: Physics, mechanics, forces

4. **Graph Plotter** (`graph-plotter`)
   - Visualize mathematical functions
   - Icon: `show_chart`
   - Data properties: `function`, `xMin`, `xMax`, `yMin`, `yMax`, `gridLines`
   - Supports functions: `x^2`, `sin(x)`, `cos(x)`, `x`, `1/x`
   - Useful for: Functions, calculus, data visualization

5. **Geometry Explorer** (`geometry-explorer`)
   - Interactive geometry demonstrations
   - Icon: `category`
   - Useful for: Geometry, spatial reasoning

6. **Number Line** (`number-line`)
   - Explore number concepts visually
   - Icon: `linear_scale`
   - Useful for: Number theory, integers, real numbers

7. **Probability Simulator** (`probability-simulator`)
   - Visualize probability concepts
   - Icon: `casino`
   - Useful for: Probability, statistics, randomness

8. **Problem Sorter** (`problemSorter`)
   - Categorize problems by type or solution approach
   - Icon: `drag_indicator`
   - Useful for: Problem-solving strategies, classification

9. **Sequence Challenge** (`sequenceChallenge`)
   - Pattern recognition exercises
   - Icon: `format_list_numbered`
   - Data properties: `title`, `instruction`, `sequenceType`, `displayItems`, `answerOptions`, `correctAnswerIndex`
   - Supports sequence types: `numeric`, `visual`
   - Useful for: Pattern recognition, sequences, inductive reasoning

10. **Conditional Matcher** (`conditionalMatcher`)
    - Match conditions with outcomes
    - Icon: `compare_arrows`
    - Data properties: `title`, `instruction`, `originalStatement`, `statementsToMatch`
    - Useful for: Logic, conditional statements, reasoning

11. **Deduction Puzzle** (`deductionPuzzle`)
    - Logic puzzles requiring deductive reasoning
    - Icon: `grid_on`
    - Useful for: Logic, deduction, critical thinking

12. **Problem Decomposer** (`problemDecomposer`)
    - Break down complex problems into simpler parts
    - Icon: `account_tree`
    - Useful for: Problem-solving strategies, decomposition

13. **Estimation Challenge** (`estimationChallenge`)
    - Make reasonable estimates with acceptable ranges
    - Icon: `calculate`
    - Useful for: Estimation, numerical reasoning

14. **Fermi Problem Solver** (`fermiProblemSolver`)
    - Order-of-magnitude estimation problems
    - Icon: `science`
    - Useful for: Estimation, scale, approximation

15. **Assumption Spotter** (`assumptionSpotter`)
    - Identify hidden assumptions in statements
    - Icon: `psychology`
    - Data properties: `title`, `statement`, `instruction`, `options`, `correctOptionIndex`
    - Useful for: Critical thinking, assumptions, logical fallacies

## Sample Course: Mathematical Thinking

We'll start by developing content for the "Mathematical Thinking" course, which already exists in the codebase but has empty modules.

### Module 1: Introduction to Mathematical Reasoning

**Lesson 1: What is Mathematical Thinking?**
- TextBlock: Introduction to mathematical thinking and its importance
- VideoBlock: Short video explaining mathematical thinking in everyday life
- ImageBlock: Visual representation of mathematical vs. everyday thinking
- InteractiveBlock: "Assumption Spotter" - Identify assumptions in everyday statements
- QuizBlock: Simple quiz to test understanding of key concepts

**Lesson 2: Pattern Recognition**
- TextBlock: Explanation of pattern recognition in mathematics
- AnimationBlock: GIF showing pattern development
- InteractiveBlock: "Sequence Challenge" - Find the next element in various sequences
- TextBlock: Explanation of how pattern recognition helps in problem-solving
- QuizBlock: Pattern recognition quiz

**Lesson 3: Logical Reasoning**
- TextBlock: Introduction to logical reasoning
- ImageBlock: Visual representation of logical structures
- InteractiveBlock: "Conditional Matcher" - Match if-then statements
- TextBlock: Real-world applications of logical reasoning
- QuizBlock: Test on logical reasoning concepts

### Module 2: Problem-Solving Strategies

**Lesson 1: Breaking Down Problems**
- TextBlock: Introduction to problem decomposition
- VideoBlock: Demonstration of breaking down a complex problem
- InteractiveBlock: "Problem Decomposer" - Practice breaking down problems
- TextBlock: Benefits of problem decomposition
- QuizBlock: Apply decomposition to sample problems

**Lesson 2: Estimation and Approximation**
- TextBlock: The power of estimation in mathematics
- AnimationBlock: Visualization of estimation process
- InteractiveBlock: "Estimation Challenge" - Make reasonable estimates
- TextBlock: When and how to use approximation
- QuizBlock: Estimation practice problems

**Lesson 3: Working Backwards**
- TextBlock: Introduction to working backwards as a strategy
- ImageBlock: Visual example of working backwards
- InteractiveBlock: "Equation Solver" - Solve equations by working backwards
- TextBlock: Applications in different types of problems
- QuizBlock: Practice problems using working backwards

### Module 3: Mathematical Modeling

**Lesson 1: Representing Real-World Situations**
- TextBlock: Introduction to mathematical modeling
- VideoBlock: Examples of mathematical models in the real world
- InteractiveBlock: "Graph Plotter" - Create models of simple scenarios
- TextBlock: Steps in creating a mathematical model
- QuizBlock: Identify appropriate models for different situations

**Lesson 2: Testing and Refining Models**
- TextBlock: How to test mathematical models
- AnimationBlock: Visualization of model refinement process
- InteractiveBlock: "Fermi Problem Solver" - Estimate quantities using models
- TextBlock: Common pitfalls in modeling
- QuizBlock: Evaluate and improve sample models

## Content Creation Guidelines

### Best Practices for Content Blocks

1. **TextBlocks**
   - Keep paragraphs short and focused (3-5 sentences)
   - Use Markdown for emphasis, lists, and headers
   - Break complex concepts into multiple blocks
   - Use a conversational, engaging tone

2. **ImageBlocks**
   - Use high-quality, clear images
   - Include descriptive captions
   - Ensure images are relevant to the content
   - Optimize image sizes for mobile devices

3. **VideoBlocks**
   - Keep videos short (1-3 minutes)
   - Include engaging thumbnails
   - Focus on visual demonstrations
   - Ensure videos have clear audio

4. **AnimationBlocks**
   - Use GIFs for simple animations
   - Use Lottie for complex, interactive animations
   - Keep file sizes reasonable
   - Ensure animations illustrate concepts clearly

5. **QuizBlocks**
   - Ask clear, focused questions
   - Provide 3-5 answer options
   - Include detailed explanations for both correct and incorrect answers
   - Use quizzes to reinforce key concepts

6. **InteractiveBlocks**
   - Choose the appropriate widget type for the concept
   - Provide clear instructions
   - Include enough data for meaningful interaction
   - Ensure the widget is intuitive to use

### Interactive Widget Implementation

When implementing interactive widgets:

1. **Data Structure**
   - Provide all required properties in the `data` object
   - Include default values for optional properties
   - Use consistent naming conventions

2. **User Experience**
   - Include clear instructions
   - Provide immediate feedback
   - Allow for exploration and experimentation
   - Include a "Complete" or "Skip" option

3. **Visual Design**
   - Use consistent colors and styling
   - Ensure text is readable
   - Make interactive elements obvious
   - Size widgets appropriately for the content

4. **Accessibility**
   - Include alternative text for visual elements
   - Ensure color contrast meets accessibility standards
   - Support keyboard navigation where possible
   - Provide clear error messages

## Implementation Plan

1. **Phase 1: Content Structure**
   - Define detailed module and lesson structure for Mathematical Thinking course
   - Create JSON templates for each lesson type
   - Implement content blocks for first module

2. **Phase 2: Interactive Widgets**
   - Develop and test interactive widgets needed for Mathematical Thinking
   - Ensure widgets are responsive and user-friendly
   - Implement auto-expand and skip/continue functionality

3. **Phase 3: Content Expansion**
   - Complete all modules for Mathematical Thinking course
   - Begin development of additional courses
   - Gather user feedback and iterate on content design

4. **Phase 4: Polish and Refinement**
   - Enhance visual design and animations
   - Optimize performance of interactive elements
   - Add additional media (videos, animations) to enrich content

## Next Steps

1. Create a sample JSON structure for the first lesson
2. Implement the continuous scrolling lesson view
3. Develop the first set of interactive widgets
4. Test user experience with the new content format

## Technical Implementation Notes

### Adding New Interactive Widgets

To add a new interactive widget:

1. Define a new widget type in `interactive_block_widget.dart`
2. Add the type to the `_buildInteractiveContent()` switch statement
3. Implement the widget builder method (e.g., `_buildNewWidgetType()`)
4. Add the widget type to `_getInteractiveTitle()` and `_getInteractiveIcon()`
5. Create a sample implementation in a course

### Content Block JSON Structure

All content blocks follow this basic structure:

```json
{
  "id": "unique-block-id",
  "type": "text|image|video|quiz|interactive|animation",
  "order": 1,
  // Type-specific properties
}
```

For interactive blocks, the structure is:

```json
{
  "id": "interactive-block-id",
  "type": "interactive",
  "order": 3,
  "interactiveType": "sequenceChallenge",
  "data": {
    // Widget-specific properties
    "title": "Find the Pattern",
    "instruction": "What comes next in this sequence?",
    // Other properties...
  }
}
```
