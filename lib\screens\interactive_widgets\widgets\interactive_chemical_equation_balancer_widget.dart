import 'package:flutter/material.dart';
import 'dart:math' as math;

/// A widget that provides an interactive chemical equation balancer
class InteractiveChemicalEquationBalancerWidget extends StatefulWidget {
  /// The data for this widget
  final Map<String, dynamic> data;

  /// Optional callback when the widget state changes
  final Function(bool isCompleted)? onStateChanged;

  const InteractiveChemicalEquationBalancerWidget({
    super.key,
    required this.data,
    this.onStateChanged,
  });

  /// Factory constructor to create the widget from JSON data
  factory InteractiveChemicalEquationBalancerWidget.fromData(
      Map<String, dynamic> data) {
    return InteractiveChemicalEquationBalancerWidget(
      data: data,
    );
  }

  @override
  State<InteractiveChemicalEquationBalancerWidget> createState() =>
      _InteractiveChemicalEquationBalancerWidgetState();
}

/// Chemical element model
class ChemicalElement {
  final String symbol;
  final String name;
  final int atomicNumber;
  final double atomicWeight;
  final Color color;

  ChemicalElement({
    required this.symbol,
    required this.name,
    required this.atomicNumber,
    required this.atomicWeight,
    required this.color,
  });
}

/// Chemical compound model
class Compound {
  final String formula;
  final String name;
  final List<ElementCount> elements;

  Compound({
    required this.formula,
    required this.name,
    required this.elements,
  });
}

/// Element count in a compound
class ElementCount {
  final String symbol;
  final int count;

  ElementCount({
    required this.symbol,
    required this.count,
  });
}

/// Chemical equation model
class ChemicalEquation {
  final String name;
  final String description;
  final List<Compound> reactants;
  final List<Compound> products;
  final List<int> reactantCoefficients;
  final List<int> productCoefficients;
  final bool isBalanced;
  final String type; // combustion, synthesis, decomposition, etc.
  final String difficulty; // easy, medium, hard

  ChemicalEquation({
    required this.name,
    required this.description,
    required this.reactants,
    required this.products,
    required this.reactantCoefficients,
    required this.productCoefficients,
    required this.isBalanced,
    required this.type,
    required this.difficulty,
  });

  /// Create a copy of the equation with new coefficients
  ChemicalEquation copyWith({
    List<int>? reactantCoefficients,
    List<int>? productCoefficients,
    bool? isBalanced,
  }) {
    return ChemicalEquation(
      name: name,
      description: description,
      reactants: reactants,
      products: products,
      reactantCoefficients: reactantCoefficients ?? this.reactantCoefficients,
      productCoefficients: productCoefficients ?? this.productCoefficients,
      isBalanced: isBalanced ?? this.isBalanced,
      type: type,
      difficulty: difficulty,
    );
  }
}

class _InteractiveChemicalEquationBalancerWidgetState
    extends State<InteractiveChemicalEquationBalancerWidget> {
  // Colors
  late Color _primaryColor;
  late Color _textColor;
  late Color _backgroundColor;
  late Color _successColor;
  late Color _errorColor;

  // Chemical elements
  late List<ChemicalElement> _elements;

  // Chemical equations
  late List<ChemicalEquation> _equations;
  late int _currentEquationIndex;

  // UI state
  bool _showDescription = true;
  bool _showHints = false;
  bool _showSolution = false;
  bool _practiceMode = false;
  bool _isEditing = false;
  bool _showElementCounts = false;

  // User input
  late List<int> _userReactantCoefficients;
  late List<int> _userProductCoefficients;

  @override
  void initState() {
    super.initState();

    // Initialize colors
    _primaryColor = _getColorFromHex(
        widget.data['primaryColor'] ?? '#2196F3'); // Blue
    _textColor =
        _getColorFromHex(widget.data['textColor'] ?? '#212121'); // Dark Grey
    _backgroundColor = _getColorFromHex(
        widget.data['backgroundColor'] ?? '#FFFFFF'); // White
    _successColor =
        _getColorFromHex(widget.data['successColor'] ?? '#4CAF50'); // Green
    _errorColor =
        _getColorFromHex(widget.data['errorColor'] ?? '#F44336'); // Red

    // Initialize chemical elements
    _elements = _createChemicalElements();

    // Initialize chemical equations
    _equations = _createChemicalEquations();
    _currentEquationIndex = 0;

    // Initialize user input
    _resetUserInput();
  }

  // Convert hex color string to Color
  Color _getColorFromHex(String hexColor) {
    hexColor = hexColor.replaceAll('#', '');
    if (hexColor.length == 6) {
      hexColor = 'FF$hexColor';
    }
    return Color(int.parse(hexColor, radix: 16));
  }

  // Create chemical elements
  List<ChemicalElement> _createChemicalElements() {
    return [
      ChemicalElement(
        symbol: 'H',
        name: 'Hydrogen',
        atomicNumber: 1,
        atomicWeight: 1.008,
        color: Colors.blue.shade200,
      ),
      ChemicalElement(
        symbol: 'C',
        name: 'Carbon',
        atomicNumber: 6,
        atomicWeight: 12.011,
        color: Colors.grey.shade700,
      ),
      ChemicalElement(
        symbol: 'N',
        name: 'Nitrogen',
        atomicNumber: 7,
        atomicWeight: 14.007,
        color: Colors.blue.shade700,
      ),
      ChemicalElement(
        symbol: 'O',
        name: 'Oxygen',
        atomicNumber: 8,
        atomicWeight: 15.999,
        color: Colors.red.shade500,
      ),
      ChemicalElement(
        symbol: 'Na',
        name: 'Sodium',
        atomicNumber: 11,
        atomicWeight: 22.990,
        color: Colors.purple.shade300,
      ),
      ChemicalElement(
        symbol: 'Cl',
        name: 'Chlorine',
        atomicNumber: 17,
        atomicWeight: 35.453,
        color: Colors.green.shade500,
      ),
      ChemicalElement(
        symbol: 'Fe',
        name: 'Iron',
        atomicNumber: 26,
        atomicWeight: 55.845,
        color: Colors.orange.shade700,
      ),
      ChemicalElement(
        symbol: 'Cu',
        name: 'Copper',
        atomicNumber: 29,
        atomicWeight: 63.546,
        color: Colors.brown.shade500,
      ),
    ];
  }

  // Create chemical equations
  List<ChemicalEquation> _createChemicalEquations() {
    return [
      // Hydrogen + Oxygen -> Water
      ChemicalEquation(
        name: 'Formation of Water',
        description: 'Hydrogen gas reacts with oxygen gas to form water.',
        reactants: [
          Compound(
            formula: 'H₂',
            name: 'Hydrogen',
            elements: [ElementCount(symbol: 'H', count: 2)],
          ),
          Compound(
            formula: 'O₂',
            name: 'Oxygen',
            elements: [ElementCount(symbol: 'O', count: 2)],
          ),
        ],
        products: [
          Compound(
            formula: 'H₂O',
            name: 'Water',
            elements: [
              ElementCount(symbol: 'H', count: 2),
              ElementCount(symbol: 'O', count: 1),
            ],
          ),
        ],
        reactantCoefficients: [2, 1],
        productCoefficients: [2],
        isBalanced: true,
        type: 'Synthesis',
        difficulty: 'Easy',
      ),
      
      // Methane combustion
      ChemicalEquation(
        name: 'Combustion of Methane',
        description: 'Methane burns in oxygen to produce carbon dioxide and water.',
        reactants: [
          Compound(
            formula: 'CH₄',
            name: 'Methane',
            elements: [
              ElementCount(symbol: 'C', count: 1),
              ElementCount(symbol: 'H', count: 4),
            ],
          ),
          Compound(
            formula: 'O₂',
            name: 'Oxygen',
            elements: [ElementCount(symbol: 'O', count: 2)],
          ),
        ],
        products: [
          Compound(
            formula: 'CO₂',
            name: 'Carbon Dioxide',
            elements: [
              ElementCount(symbol: 'C', count: 1),
              ElementCount(symbol: 'O', count: 2),
            ],
          ),
          Compound(
            formula: 'H₂O',
            name: 'Water',
            elements: [
              ElementCount(symbol: 'H', count: 2),
              ElementCount(symbol: 'O', count: 1),
            ],
          ),
        ],
        reactantCoefficients: [1, 2],
        productCoefficients: [1, 2],
        isBalanced: true,
        type: 'Combustion',
        difficulty: 'Medium',
      ),
      
      // Iron + Oxygen -> Iron(III) Oxide
      ChemicalEquation(
        name: 'Rusting of Iron',
        description: 'Iron reacts with oxygen to form iron(III) oxide, commonly known as rust.',
        reactants: [
          Compound(
            formula: 'Fe',
            name: 'Iron',
            elements: [ElementCount(symbol: 'Fe', count: 1)],
          ),
          Compound(
            formula: 'O₂',
            name: 'Oxygen',
            elements: [ElementCount(symbol: 'O', count: 2)],
          ),
        ],
        products: [
          Compound(
            formula: 'Fe₂O₃',
            name: 'Iron(III) Oxide',
            elements: [
              ElementCount(symbol: 'Fe', count: 2),
              ElementCount(symbol: 'O', count: 3),
            ],
          ),
        ],
        reactantCoefficients: [4, 3],
        productCoefficients: [2],
        isBalanced: true,
        type: 'Synthesis',
        difficulty: 'Medium',
      ),
      
      // Sodium + Chlorine -> Sodium Chloride
      ChemicalEquation(
        name: 'Formation of Table Salt',
        description: 'Sodium metal reacts with chlorine gas to form sodium chloride (table salt).',
        reactants: [
          Compound(
            formula: 'Na',
            name: 'Sodium',
            elements: [ElementCount(symbol: 'Na', count: 1)],
          ),
          Compound(
            formula: 'Cl₂',
            name: 'Chlorine',
            elements: [ElementCount(symbol: 'Cl', count: 2)],
          ),
        ],
        products: [
          Compound(
            formula: 'NaCl',
            name: 'Sodium Chloride',
            elements: [
              ElementCount(symbol: 'Na', count: 1),
              ElementCount(symbol: 'Cl', count: 1),
            ],
          ),
        ],
        reactantCoefficients: [2, 1],
        productCoefficients: [2],
        isBalanced: true,
        type: 'Synthesis',
        difficulty: 'Easy',
      ),
    ];
  }

  // Reset user input
  void _resetUserInput() {
    final equation = _equations[_currentEquationIndex];
    _userReactantCoefficients = List.filled(equation.reactants.length, 1);
    _userProductCoefficients = List.filled(equation.products.length, 1);
    _showSolution = false;
  }

  @override
  Widget build(BuildContext context) {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
        side: BorderSide(color: _primaryColor.withAlpha(77)),
      ),
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Title and description
            Text(
              widget.data['title'] ?? 'Chemical Equation Balancer',
              style: TextStyle(
                fontSize: 20,
                fontWeight: FontWeight.bold,
                color: _textColor,
              ),
            ),
            const SizedBox(height: 8),
            Text(
              widget.data['description'] ??
                  'Balance chemical equations and learn about chemical reactions',
              style: TextStyle(
                fontSize: 14,
                color: _textColor.withAlpha(179),
              ),
            ),
            const SizedBox(height: 16),

            // Equation selector
            _buildEquationSelector(),

            const SizedBox(height: 16),

            // Chemical equation
            _buildChemicalEquation(),

            const SizedBox(height: 16),

            // Controls
            _buildControls(),

            const SizedBox(height: 16),

            // Element counts
            if (_showElementCounts) _buildElementCounts(),

            const SizedBox(height: 16),

            // Equation description
            if (_showDescription) _buildEquationDescription(),
          ],
        ),
      ),
    );
  }
}
