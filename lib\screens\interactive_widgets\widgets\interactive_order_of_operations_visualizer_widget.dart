import 'package:flutter/material.dart';
import 'dart:math' as math;

/// A widget that visualizes the order of operations (PEMDAS) for solving equations.
class InteractiveOrderOfOperationsVisualizerWidget extends StatefulWidget {
  final Map<String, dynamic> data;
  final Function(bool)? onStateChanged;
  final Color primaryColor;
  final Color secondaryColor;
  final Color backgroundColor;
  final Color textColor;

  const InteractiveOrderOfOperationsVisualizerWidget({
    super.key,
    this.data = const {},
    this.onStateChanged,
    this.primaryColor = Colors.blue,
    this.secondaryColor = Colors.orange,
    this.backgroundColor = Colors.white,
    this.textColor = Colors.black87,
  });

  @override
  State<InteractiveOrderOfOperationsVisualizerWidget> createState() =>
      _InteractiveOrderOfOperationsVisualizerWidgetState();
}

class _InteractiveOrderOfOperationsVisualizerWidgetState
    extends State<InteractiveOrderOfOperationsVisualizerWidget>
    with SingleTickerProviderStateMixin {
  // Animation controller
  late AnimationController _animationController;
  late Animation<double> _animation;

  // State variables
  bool _isCompleted = false;
  bool _isAnimating = false;
  int _currentExpressionIndex = 0;
  int _currentStep = 0;
  List<ExpressionData> _expressions = [];
  late ExpressionData _currentExpression;
  bool _showQuiz = false;
  String? _selectedAnswer;
  bool _isCorrect = false;
  String? _feedbackMessage;

  @override
  void initState() {
    super.initState();

    // Initialize animation controller
    _animationController = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 800),
    );

    _animation = CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeInOut,
    );

    // Add listener to animation controller
    _animationController.addStatusListener((status) {
      if (status == AnimationStatus.completed) {
        setState(() {
          _currentStep++;
          if (_currentStep < _currentExpression.steps.length) {
            _animationController.reset();
            _animationController.forward();
          } else {
            _isAnimating = false;
          }
        });
      }
    });

    // Initialize expressions
    _initializeExpressions();
    _currentExpression = _expressions[_currentExpressionIndex];
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  void _initializeExpressions() {
    // Check if expressions are provided in the data
    if (widget.data.containsKey('expressions') &&
        widget.data['expressions'] is List &&
        widget.data['expressions'].isNotEmpty) {

      final expressionsData = widget.data['expressions'] as List;
      for (final expData in expressionsData) {
        if (expData is Map<String, dynamic>) {
          final expression = ExpressionData.fromJson(expData);
          _expressions.add(expression);
        }
      }
    }

    // If no expressions were provided, create default ones
    if (_expressions.isEmpty) {
      _expressions = [
        ExpressionData(
          expression: '2 + 3 × 4',
          result: '14',
          steps: [
            ExpressionStep(
              expression: '2 + 3 × 4',
              explanation: 'Start with the original expression.',
              highlightRanges: [
                HighlightRange(start: 0, end: 8, color: Colors.grey.withOpacity(0.3)),
              ],
              operation: 'Original expression',
            ),
            ExpressionStep(
              expression: '2 + 3 × 4',
              explanation: 'According to PEMDAS, multiplication comes before addition.',
              highlightRanges: [
                HighlightRange(start: 4, end: 8, color: Colors.orange.withOpacity(0.3)),
              ],
              operation: 'Identify multiplication',
            ),
            ExpressionStep(
              expression: '2 + 12',
              explanation: 'Calculate 3 × 4 = 12.',
              highlightRanges: [
                HighlightRange(start: 4, end: 6, color: Colors.green.withOpacity(0.3)),
              ],
              operation: 'Perform multiplication',
            ),
            ExpressionStep(
              expression: '14',
              explanation: 'Calculate 2 + 12 = 14.',
              highlightRanges: [
                HighlightRange(start: 0, end: 2, color: Colors.green.withOpacity(0.3)),
              ],
              operation: 'Perform addition',
            ),
          ],
          quiz: {
            'question': 'What is the value of 5 + 2 × 3?',
            'options': ['21', '11', '17', '9'],
            'correctAnswer': '11',
            'explanation': 'Following PEMDAS, we first calculate 2 × 3 = 6, then 5 + 6 = 11.'
          },
        ),
        ExpressionData(
          expression: '(4 + 2) × 3',
          result: '18',
          steps: [
            ExpressionStep(
              expression: '(4 + 2) × 3',
              explanation: 'Start with the original expression.',
              highlightRanges: [
                HighlightRange(start: 0, end: 10, color: Colors.grey.withOpacity(0.3)),
              ],
              operation: 'Original expression',
            ),
            ExpressionStep(
              expression: '(4 + 2) × 3',
              explanation: 'According to PEMDAS, operations inside parentheses come first.',
              highlightRanges: [
                HighlightRange(start: 0, end: 7, color: Colors.orange.withOpacity(0.3)),
              ],
              operation: 'Identify parentheses',
            ),
            ExpressionStep(
              expression: '6 × 3',
              explanation: 'Calculate 4 + 2 = 6.',
              highlightRanges: [
                HighlightRange(start: 0, end: 1, color: Colors.green.withOpacity(0.3)),
              ],
              operation: 'Perform addition inside parentheses',
            ),
            ExpressionStep(
              expression: '18',
              explanation: 'Calculate 6 × 3 = 18.',
              highlightRanges: [
                HighlightRange(start: 0, end: 2, color: Colors.green.withOpacity(0.3)),
              ],
              operation: 'Perform multiplication',
            ),
          ],
          quiz: {
            'question': 'What is the value of (8 - 3) × 4?',
            'options': ['20', '5', '32', '17'],
            'correctAnswer': '20',
            'explanation': 'Following PEMDAS, we first calculate (8 - 3) = 5, then 5 × 4 = 20.'
          },
        ),
        ExpressionData(
          expression: '12 ÷ 4 + 2 × 3',
          result: '9',
          steps: [
            ExpressionStep(
              expression: '12 ÷ 4 + 2 × 3',
              explanation: 'Start with the original expression.',
              highlightRanges: [
                HighlightRange(start: 0, end: 14, color: Colors.grey.withOpacity(0.3)),
              ],
              operation: 'Original expression',
            ),
            ExpressionStep(
              expression: '12 ÷ 4 + 2 × 3',
              explanation: 'According to PEMDAS, division and multiplication have the same precedence and are evaluated from left to right.',
              highlightRanges: [
                HighlightRange(start: 0, end: 6, color: Colors.orange.withOpacity(0.3)),
                HighlightRange(start: 9, end: 14, color: Colors.orange.withOpacity(0.3)),
              ],
              operation: 'Identify division and multiplication',
            ),
            ExpressionStep(
              expression: '3 + 2 × 3',
              explanation: 'Calculate 12 ÷ 4 = 3.',
              highlightRanges: [
                HighlightRange(start: 0, end: 1, color: Colors.green.withOpacity(0.3)),
              ],
              operation: 'Perform division',
            ),
            ExpressionStep(
              expression: '3 + 6',
              explanation: 'Calculate 2 × 3 = 6.',
              highlightRanges: [
                HighlightRange(start: 4, end: 5, color: Colors.green.withOpacity(0.3)),
              ],
              operation: 'Perform multiplication',
            ),
            ExpressionStep(
              expression: '9',
              explanation: 'Calculate 3 + 6 = 9.',
              highlightRanges: [
                HighlightRange(start: 0, end: 1, color: Colors.green.withOpacity(0.3)),
              ],
              operation: 'Perform addition',
            ),
          ],
          quiz: {
            'question': 'What is the value of 20 ÷ 5 + 3 × 2?',
            'options': ['10', '16', '8', '26'],
            'correctAnswer': '10',
            'explanation': 'Following PEMDAS, we first calculate 20 ÷ 5 = 4 and 3 × 2 = 6, then 4 + 6 = 10.'
          },
        ),
        ExpressionData(
          expression: '36 ÷ (3 + 3) - 2',
          result: '4',
          steps: [
            ExpressionStep(
              expression: '36 ÷ (3 + 3) - 2',
              explanation: 'Start with the original expression.',
              highlightRanges: [
                HighlightRange(start: 0, end: 16, color: Colors.grey.withOpacity(0.3)),
              ],
              operation: 'Original expression',
            ),
            ExpressionStep(
              expression: '36 ÷ (3 + 3) - 2',
              explanation: 'According to PEMDAS, operations inside parentheses come first.',
              highlightRanges: [
                HighlightRange(start: 5, end: 10, color: Colors.orange.withOpacity(0.3)),
              ],
              operation: 'Identify parentheses',
            ),
            ExpressionStep(
              expression: '36 ÷ 6 - 2',
              explanation: 'Calculate 3 + 3 = 6.',
              highlightRanges: [
                HighlightRange(start: 5, end: 6, color: Colors.green.withOpacity(0.3)),
              ],
              operation: 'Perform addition inside parentheses',
            ),
            ExpressionStep(
              expression: '6 - 2',
              explanation: 'Calculate 36 ÷ 6 = 6.',
              highlightRanges: [
                HighlightRange(start: 0, end: 1, color: Colors.green.withOpacity(0.3)),
              ],
              operation: 'Perform division',
            ),
            ExpressionStep(
              expression: '4',
              explanation: 'Calculate 6 - 2 = 4.',
              highlightRanges: [
                HighlightRange(start: 0, end: 1, color: Colors.green.withOpacity(0.3)),
              ],
              operation: 'Perform subtraction',
            ),
          ],
          quiz: {
            'question': 'What is the value of 24 ÷ (2 + 2) × 3?',
            'options': ['18', '6', '4', '36'],
            'correctAnswer': '18',
            'explanation': 'Following PEMDAS, we first calculate (2 + 2) = 4, then 24 ÷ 4 = 6, and finally 6 × 3 = 18.'
          },
        ),
      ];
    }
  }

  void _startAnimation() {
    if (_currentExpression.steps.isEmpty) return;

    setState(() {
      _currentStep = 0;
      _isAnimating = true;
      _showQuiz = false;
      _selectedAnswer = null;
      _feedbackMessage = null;
    });

    _animationController.reset();
    _animationController.forward();
  }

  void _stopAnimation() {
    setState(() {
      _isAnimating = false;
    });
    _animationController.stop();
  }

  void _resetAnimation() {
    setState(() {
      _currentStep = 0;
      _isAnimating = false;
      _showQuiz = false;
      _selectedAnswer = null;
      _feedbackMessage = null;
    });
    _animationController.reset();
  }

  void _showQuizQuestion() {
    setState(() {
      _showQuiz = true;
      _selectedAnswer = null;
      _feedbackMessage = null;
    });
  }

  void _checkAnswer(String answer) {
    final isCorrect = answer == _currentExpression.quiz['correctAnswer'];

    setState(() {
      _selectedAnswer = answer;
      _isCorrect = isCorrect;

      if (isCorrect) {
        _feedbackMessage = 'Correct! ${_currentExpression.quiz['explanation']}';
      } else {
        _feedbackMessage = 'Not quite. ${_currentExpression.quiz['explanation']}';
      }
    });
  }

  void _nextExpression() {
    if (_currentExpressionIndex < _expressions.length - 1) {
      setState(() {
        _currentExpressionIndex++;
        _currentExpression = _expressions[_currentExpressionIndex];
        _resetAnimation();
      });
    } else {
      // All expressions completed
      setState(() {
        _isCompleted = true;
      });
      widget.onStateChanged?.call(true);
    }
  }

  void _previousExpression() {
    if (_currentExpressionIndex > 0) {
      setState(() {
        _currentExpressionIndex--;
        _currentExpression = _expressions[_currentExpressionIndex];
        _resetAnimation();
      });
    }
  }

  void _resetWidget() {
    setState(() {
      _currentExpressionIndex = 0;
      _currentExpression = _expressions[_currentExpressionIndex];
      _resetAnimation();
      _isCompleted = false;
    });
    widget.onStateChanged?.call(false);
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: widget.backgroundColor,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withOpacity(0.2),
            spreadRadius: 1,
            blurRadius: 3,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: _isCompleted ? _buildCompletionScreen() : _buildMainContent(),
    );
  }

  Widget _buildMainContent() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Title and progress indicator
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Text(
              'Order of Operations',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
                color: widget.primaryColor,
              ),
            ),
            Text(
              'Example ${_currentExpressionIndex + 1}/${_expressions.length}',
              style: TextStyle(
                fontSize: 14,
                color: widget.textColor.withOpacity(0.7),
              ),
            ),
          ],
        ),

        const SizedBox(height: 16),

        // PEMDAS reminder
        Container(
          width: double.infinity,
          padding: const EdgeInsets.all(12),
          decoration: BoxDecoration(
            color: widget.primaryColor.withOpacity(0.1),
            borderRadius: BorderRadius.circular(8),
            border: Border.all(color: widget.primaryColor.withOpacity(0.3)),
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                'PEMDAS: Order of Operations',
                style: TextStyle(
                  fontWeight: FontWeight.bold,
                  color: widget.primaryColor,
                ),
              ),
              const SizedBox(height: 8),
              Row(
                children: [
                  _buildPemdasItem('P', 'Parentheses', 1),
                  _buildPemdasItem('E', 'Exponents', 2),
                  _buildPemdasItem('M', 'Multiplication', 3),
                  _buildPemdasItem('D', 'Division', 3),
                  _buildPemdasItem('A', 'Addition', 4),
                  _buildPemdasItem('S', 'Subtraction', 4),
                ],
              ),
              const SizedBox(height: 4),
              Text(
                'Note: M/D have the same precedence (left to right), and A/S have the same precedence (left to right).',
                style: TextStyle(
                  fontSize: 12,
                  fontStyle: FontStyle.italic,
                  color: widget.textColor.withOpacity(0.7),
                ),
              ),
            ],
          ),
        ),

        const SizedBox(height: 24),

        // Current expression
        if (!_showQuiz) ...[
          Text(
            'Expression:',
            style: TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.w500,
              color: widget.textColor,
            ),
          ),
          const SizedBox(height: 8),
          Container(
            width: double.infinity,
            padding: const EdgeInsets.symmetric(vertical: 16, horizontal: 16),
            decoration: BoxDecoration(
              color: Colors.grey.shade50,
              borderRadius: BorderRadius.circular(8),
              border: Border.all(color: Colors.grey.shade300),
            ),
            child: _isAnimating && _currentStep < _currentExpression.steps.length
                ? _buildHighlightedExpression(_currentExpression.steps[_currentStep])
                : Text(
                    _currentExpression.expression,
                    style: TextStyle(
                      fontSize: 24,
                      fontWeight: FontWeight.bold,
                      color: widget.textColor,
                    ),
                    textAlign: TextAlign.center,
                  ),
          ),

          const SizedBox(height: 16),

          // Current step explanation
          if (_isAnimating && _currentStep < _currentExpression.steps.length) ...[
            Container(
              width: double.infinity,
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: widget.secondaryColor.withOpacity(0.1),
                borderRadius: BorderRadius.circular(8),
                border: Border.all(color: widget.secondaryColor.withOpacity(0.3)),
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'Step ${_currentStep + 1}: ${_currentExpression.steps[_currentStep].operation}',
                    style: TextStyle(
                      fontWeight: FontWeight.bold,
                      color: widget.secondaryColor,
                    ),
                  ),
                  const SizedBox(height: 4),
                  Text(
                    _currentExpression.steps[_currentStep].explanation,
                    style: TextStyle(
                      color: widget.textColor,
                    ),
                  ),
                ],
              ),
            ),
          ],

          const Spacer(),

          // Animation controls
          Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              if (!_isAnimating) ...[
                ElevatedButton.icon(
                  onPressed: _startAnimation,
                  icon: const Icon(Icons.play_arrow),
                  label: const Text('Start Animation'),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: widget.primaryColor,
                  ),
                ),
                const SizedBox(width: 16),
                ElevatedButton.icon(
                  onPressed: _showQuizQuestion,
                  icon: const Icon(Icons.quiz),
                  label: const Text('Try a Quiz'),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: widget.secondaryColor,
                  ),
                ),
              ] else ...[
                ElevatedButton.icon(
                  onPressed: _stopAnimation,
                  icon: const Icon(Icons.pause),
                  label: const Text('Pause'),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Colors.red,
                  ),
                ),
              ],
            ],
          ),
        ],

        // Quiz section
        if (_showQuiz) ...[
          Text(
            'Quiz:',
            style: TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.w500,
              color: widget.textColor,
            ),
          ),
          const SizedBox(height: 8),
          Container(
            width: double.infinity,
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: widget.primaryColor.withOpacity(0.1),
              borderRadius: BorderRadius.circular(8),
              border: Border.all(color: widget.primaryColor.withOpacity(0.3)),
            ),
            child: Text(
              _currentExpression.quiz['question'],
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
                color: widget.textColor,
              ),
            ),
          ),

          const SizedBox(height: 16),

          // Answer options
          ...(_currentExpression.quiz['options'] as List<String>).map((option) {
            final bool isSelected = _selectedAnswer == option;
            final bool isCorrect = option == _currentExpression.quiz['correctAnswer'];

            return Padding(
              padding: const EdgeInsets.only(bottom: 8.0),
              child: InkWell(
                onTap: _selectedAnswer == null ? () => _checkAnswer(option) : null,
                child: Container(
                  width: double.infinity,
                  padding: const EdgeInsets.all(12),
                  decoration: BoxDecoration(
                    color: isSelected
                        ? (isCorrect ? Colors.green.withOpacity(0.2) : Colors.red.withOpacity(0.2))
                        : Colors.grey.shade50,
                    borderRadius: BorderRadius.circular(8),
                    border: Border.all(
                      color: isSelected
                          ? (isCorrect ? Colors.green : Colors.red)
                          : Colors.grey.shade300,
                    ),
                  ),
                  child: Row(
                    children: [
                      Expanded(
                        child: Text(
                          option,
                          style: TextStyle(
                            fontWeight: isSelected ? FontWeight.bold : FontWeight.normal,
                            color: widget.textColor,
                          ),
                        ),
                      ),
                      if (isSelected)
                        Icon(
                          isCorrect ? Icons.check_circle : Icons.cancel,
                          color: isCorrect ? Colors.green : Colors.red,
                        ),
                    ],
                  ),
                ),
              ),
            );
          }).toList(),

          if (_feedbackMessage != null)
            Container(
              width: double.infinity,
              padding: const EdgeInsets.all(12),
              margin: const EdgeInsets.only(top: 16),
              decoration: BoxDecoration(
                color: _isCorrect
                    ? Colors.green.withOpacity(0.1)
                    : Colors.red.withOpacity(0.1),
                borderRadius: BorderRadius.circular(8),
                border: Border.all(
                  color: _isCorrect ? Colors.green : Colors.red,
                ),
              ),
              child: Text(
                _feedbackMessage!,
                style: TextStyle(
                  color: _isCorrect ? Colors.green : Colors.red,
                ),
              ),
            ),

          const Spacer(),

          // Back to animation button
          ElevatedButton.icon(
            onPressed: _startAnimation,
            icon: const Icon(Icons.arrow_back),
            label: const Text('Back to Animation'),
            style: ElevatedButton.styleFrom(
              backgroundColor: widget.primaryColor,
            ),
          ),
        ],

        const SizedBox(height: 16),

        // Navigation buttons
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            // Previous button
            ElevatedButton(
              onPressed: _currentExpressionIndex > 0 ? _previousExpression : null,
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.grey.shade200,
                foregroundColor: Colors.black87,
              ),
              child: const Text('Previous'),
            ),

            // Next button
            ElevatedButton(
              onPressed: _nextExpression,
              style: ElevatedButton.styleFrom(
                backgroundColor: widget.primaryColor,
                foregroundColor: Colors.white,
              ),
              child: Text(_currentExpressionIndex < _expressions.length - 1 ? 'Next' : 'Finish'),
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildPemdasItem(String letter, String description, int priority) {
    return Expanded(
      child: Column(
        children: [
          Container(
            width: 30,
            height: 30,
            decoration: BoxDecoration(
              color: widget.primaryColor,
              shape: BoxShape.circle,
            ),
            child: Center(
              child: Text(
                letter,
                style: const TextStyle(
                  color: Colors.white,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ),
          ),
          const SizedBox(height: 4),
          Text(
            description,
            style: TextStyle(
              fontSize: 10,
              color: widget.textColor,
            ),
            textAlign: TextAlign.center,
          ),
          Text(
            'Priority: $priority',
            style: TextStyle(
              fontSize: 9,
              color: widget.textColor.withOpacity(0.7),
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Widget _buildHighlightedExpression(ExpressionStep step) {
    final text = step.expression;
    final List<HighlightRange> highlights = step.highlightRanges;

    if (highlights.isEmpty) {
      return Text(
        text,
        style: TextStyle(
          fontSize: 24,
          fontWeight: FontWeight.bold,
          color: widget.textColor,
        ),
        textAlign: TextAlign.center,
      );
    }

    // Sort highlights by start index
    highlights.sort((a, b) => a.start.compareTo(b.start));

    final List<TextSpan> spans = [];
    int currentIndex = 0;

    for (final highlight in highlights) {
      if (highlight.start > currentIndex) {
        // Add non-highlighted text before this highlight
        spans.add(
          TextSpan(
            text: text.substring(currentIndex, highlight.start),
            style: TextStyle(
              fontSize: 24,
              fontWeight: FontWeight.bold,
              color: widget.textColor,
            ),
          ),
        );
      }

      // Add highlighted text
      spans.add(
        TextSpan(
          text: text.substring(highlight.start, highlight.end),
          style: TextStyle(
            fontSize: 24,
            fontWeight: FontWeight.bold,
            color: widget.textColor,
            backgroundColor: highlight.color,
          ),
        ),
      );

      currentIndex = highlight.end;
    }

    // Add any remaining text after the last highlight
    if (currentIndex < text.length) {
      spans.add(
        TextSpan(
          text: text.substring(currentIndex),
          style: TextStyle(
            fontSize: 24,
            fontWeight: FontWeight.bold,
            color: widget.textColor,
          ),
        ),
      );
    }

    return RichText(
      text: TextSpan(children: spans),
      textAlign: TextAlign.center,
    );
  }

  Widget _buildCompletionScreen() {
    return Column(
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        Icon(
          Icons.check_circle,
          size: 80,
          color: Colors.green,
        ),
        const SizedBox(height: 24),
        Text(
          'Congratulations!',
          style: TextStyle(
            fontSize: 24,
            fontWeight: FontWeight.bold,
            color: widget.primaryColor,
          ),
        ),
        const SizedBox(height: 16),
        Text(
          'You\'ve completed all the order of operations examples!',
          textAlign: TextAlign.center,
          style: TextStyle(
            fontSize: 16,
            color: widget.textColor,
          ),
        ),
        const SizedBox(height: 32),
        ElevatedButton.icon(
          onPressed: _resetWidget,
          icon: const Icon(Icons.refresh),
          label: const Text('Start Again'),
          style: ElevatedButton.styleFrom(
            backgroundColor: widget.secondaryColor,
            padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
          ),
        ),
      ],
    );
  }
}

/// Data class for expression information
class ExpressionData {
  final String expression;
  final String result;
  final List<ExpressionStep> steps;
  final Map<String, dynamic> quiz;

  ExpressionData({
    required this.expression,
    required this.result,
    required this.steps,
    required this.quiz,
  });

  factory ExpressionData.fromJson(Map<String, dynamic> json) {
    final steps = <ExpressionStep>[];
    if (json.containsKey('steps') && json['steps'] is List) {
      for (final stepData in json['steps']) {
        if (stepData is Map<String, dynamic>) {
          steps.add(ExpressionStep.fromJson(stepData));
        }
      }
    }

    return ExpressionData(
      expression: json['expression'] ?? '2 + 3 × 4',
      result: json['result'] ?? '14',
      steps: steps,
      quiz: json['quiz'] ?? {
        'question': 'What is the value of 5 + 2 × 3?',
        'options': ['21', '11', '17', '9'],
        'correctAnswer': '11',
        'explanation': 'Following PEMDAS, we first calculate 2 × 3 = 6, then 5 + 6 = 11.'
      },
    );
  }
}

/// Data class for expression solution steps
class ExpressionStep {
  final String expression;
  final String explanation;
  final String operation;
  final List<HighlightRange> highlightRanges;

  ExpressionStep({
    required this.expression,
    required this.explanation,
    required this.operation,
    required this.highlightRanges,
  });

  factory ExpressionStep.fromJson(Map<String, dynamic> json) {
    final highlightRanges = <HighlightRange>[];
    if (json.containsKey('highlightRanges') && json['highlightRanges'] is List) {
      for (final rangeData in json['highlightRanges']) {
        if (rangeData is Map<String, dynamic>) {
          highlightRanges.add(HighlightRange.fromJson(rangeData));
        }
      }
    }

    return ExpressionStep(
      expression: json['expression'] ?? '',
      explanation: json['explanation'] ?? '',
      operation: json['operation'] ?? '',
      highlightRanges: highlightRanges,
    );
  }
}

/// Data class for highlighting parts of expressions
class HighlightRange {
  final int start;
  final int end;
  final Color color;

  HighlightRange({
    required this.start,
    required this.end,
    required this.color,
  });

  factory HighlightRange.fromJson(Map<String, dynamic> json) {
    return HighlightRange(
      start: json['start'] ?? 0,
      end: json['end'] ?? 0,
      color: Color(json['color'] ?? 0x4D9E9E9E), // Default to grey with 30% opacity
    );
  }
}
