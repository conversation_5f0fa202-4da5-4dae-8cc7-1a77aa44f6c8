import 'package:flutter/material.dart';
import 'dart:async';

class InteractiveLetterSequenceWidget extends StatefulWidget {
  final List<String> sequence;
  final bool highlightAnimation;
  final Duration animationDuration;
  final double letterSize;
  final Color primaryColor;
  final Color secondaryColor;
  final Color backgroundColor;

  const InteractiveLetterSequenceWidget({
    Key? key,
    required this.sequence,
    this.highlightAnimation = true,
    this.animationDuration = const Duration(milliseconds: 600),
    this.letterSize = 40.0,
    this.primaryColor = Colors.blue,
    this.secondaryColor = Colors.orange,
    this.backgroundColor = Colors.white,
  }) : super(key: key);

  factory InteractiveLetterSequenceWidget.fromData(Map<String, dynamic> data) {
    return InteractiveLetterSequenceWidget(
      sequence: List<String>.from(data['sequence'] ?? []),
      highlightAnimation: data['highlight_animation'] ?? true,
      animationDuration: Duration(
        milliseconds: data['animation_duration_ms'] ?? 600,
      ),
      letterSize: data['letter_size']?.toDouble() ?? 40.0,
      primaryColor: _parseColor(data['primary_color'], Colors.blue),
      secondaryColor: _parseColor(data['secondary_color'], Colors.orange),
      backgroundColor: _parseColor(data['background_color'], Colors.white),
    );
  }

  static Color _parseColor(String? colorString, Color defaultColor) {
    if (colorString == null) return defaultColor;
    try {
      return Color(int.parse(colorString.replaceAll('#', '0xFF')));
    } catch (e) {
      return defaultColor;
    }
  }

  @override
  State<InteractiveLetterSequenceWidget> createState() =>
      _InteractiveLetterSequenceWidgetState();
}

class _InteractiveLetterSequenceWidgetState
    extends State<InteractiveLetterSequenceWidget>
    with SingleTickerProviderStateMixin {
  late AnimationController _controller;
  int _highlightIndex = 0;
  Timer? _animationTimer;
  bool _isAnimating = false;

  @override
  void initState() {
    super.initState();
    _controller = AnimationController(
      vsync: this,
      duration: widget.animationDuration,
    );

    _controller.addStatusListener((status) {
      if (status == AnimationStatus.completed) {
        _controller.reverse();
      } else if (status == AnimationStatus.dismissed) {
        if (_isAnimating) {
          _moveToNextLetter();
        }
      }
    });

    if (widget.highlightAnimation) {
      _startAnimation();
    }
  }

  void _startAnimation() {
    _isAnimating = true;
    _highlightIndex = 0;
    _controller.forward();
  }

  void _moveToNextLetter() {
    if (!mounted) return;

    setState(() {
      _highlightIndex = (_highlightIndex + 1) % widget.sequence.length;
    });

    _animationTimer = Timer(
      const Duration(milliseconds: 100),
      () {
        if (mounted && _isAnimating) {
          _controller.forward(from: 0.0);
        }
      },
    );
  }

  void _stopAnimation() {
    _isAnimating = false;
    _animationTimer?.cancel();
    _controller.reset();
  }

  void _toggleAnimation() {
    if (_isAnimating) {
      _stopAnimation();
    } else {
      _startAnimation();
    }
  }

  @override
  void dispose() {
    _controller.dispose();
    _animationTimer?.cancel();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      mainAxisSize: MainAxisSize.min,
      children: [
        // Letter sequence
        Container(
          height: widget.letterSize * 1.5,
          padding: const EdgeInsets.symmetric(vertical: 8, horizontal: 16),
          decoration: BoxDecoration(
            color: widget.backgroundColor,
            borderRadius: BorderRadius.circular(12),
            border: Border.all(color: Colors.grey[300]!),
          ),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: List.generate(
              widget.sequence.length,
              (index) => _buildLetterBox(index),
            ),
          ),
        ),

        // Controls
        Padding(
          padding: const EdgeInsets.only(top: 8.0),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              IconButton(
                icon: Icon(_isAnimating ? Icons.pause : Icons.play_arrow),
                onPressed: _toggleAnimation,
                color: widget.primaryColor,
              ),
              IconButton(
                icon: const Icon(Icons.replay),
                onPressed: () {
                  _stopAnimation();
                  _startAnimation();
                },
                color: widget.primaryColor,
              ),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildLetterBox(int index) {
    final isHighlighted = index == _highlightIndex && _isAnimating;
    
    return AnimatedBuilder(
      animation: _controller,
      builder: (context, child) {
        final scale = isHighlighted
            ? 1.0 + (_controller.value * 0.3)
            : 1.0;
        
        return Container(
          margin: const EdgeInsets.symmetric(horizontal: 4),
          child: Transform.scale(
            scale: scale,
            child: Container(
              width: widget.letterSize,
              height: widget.letterSize,
              decoration: BoxDecoration(
                color: isHighlighted
                    ? widget.primaryColor.withOpacity(0.2)
                    : Colors.transparent,
                borderRadius: BorderRadius.circular(8),
                border: Border.all(
                  color: isHighlighted
                      ? widget.primaryColor
                      : Colors.grey[300]!,
                  width: isHighlighted ? 2 : 1,
                ),
              ),
              child: Center(
                child: Text(
                  widget.sequence[index],
                  style: TextStyle(
                    fontSize: widget.letterSize * 0.6,
                    fontWeight: isHighlighted
                        ? FontWeight.bold
                        : FontWeight.normal,
                    color: isHighlighted
                        ? widget.primaryColor
                        : Colors.black87,
                  ),
                ),
              ),
            ),
          ),
        );
      },
    );
  }
}
