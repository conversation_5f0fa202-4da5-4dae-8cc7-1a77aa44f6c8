{"id": "introduction-to-ac-circuits", "title": "Introduction to AC Circuits", "description": "Explore the basics of alternating current and its behavior in circuits.", "order": 3, "lessons": [{"id": "what-is-ac", "title": "What is Alternating Current (AC)?", "description": "Visualize sinusoidal waveforms and frequency.", "order": 1, "type": "interactive_lesson", "estimatedTimeMinutes": 20, "contentBlocks": [{"id": "what-is-ac-screen-1", "type": "lesson_screen", "order": 1, "estimatedTimeSeconds": 60, "content": {"headline": "Beyond DC: Alternating Current!", "body_md": "So far, we've mostly talked about Direct Current (DC), where current flows steadily in one direction. But much of the world runs on **Alternating Current (AC)**!\n\nIn AC, the current periodically **reverses direction**.", "visual": {"type": "unsplash_search", "value": "power lines alternating current"}, "interactive_element": {"type": "button", "button_text": "Why does it alternate?", "action": "next_screen"}}}, {"id": "what-is-ac-screen-2", "type": "lesson_screen", "order": 2, "estimatedTimeSeconds": 120, "content": {"headline": "The Sinusoidal Waveform", "body_md": "AC voltage and current are typically represented by a **sine wave** (or sinusoidal waveform).\n\n- The voltage/current smoothly rises to a positive peak, falls through zero to a negative peak, and then rises back to zero.\n- This completes one **cycle**.", "visual": {"type": "local_asset", "value": "assets/images/course_specific/sine_wave_ac.gif"}, "interactive_element": {"type": "multiple_choice_text", "question_text": "What does one complete 'round trip' of an AC waveform (e.g., positive peak to negative peak and back to zero) represent?", "options": [{"id": "opt1", "text": "Amplitude"}, {"id": "opt2", "text": "Frequency"}, {"id": "opt3", "text": "One cycle"}, {"id": "opt4", "text": "RMS value"}], "correct_option_id": "opt3", "feedback_correct": "Correct! That's one full cycle of the AC waveform.", "feedback_incorrect": "Think about the repeating nature of the wave. What do we call one full repetition?"}}}, {"id": "what-is-ac-screen-3", "type": "lesson_screen", "order": 3, "estimatedTimeSeconds": 90, "content": {"headline": "Frequency: Cycles Per Second", "body_md": "The **frequency (f)** of an AC waveform is the number of cycles that occur in one second. It's measured in **Hertz (Hz)**.\n\n- Household power in North America is typically 60Hz.\n- In Europe and many other parts of the world, it's 50Hz.", "visual": {"type": "static_text", "value": "60 Hz / 50 Hz"}, "interactive_element": {"type": "button", "button_text": "What about the 'strength' of AC?", "action": "next_screen"}}}, {"id": "what-is-ac-screen-4", "type": "lesson_screen", "order": 4, "estimatedTimeSeconds": 100, "content": {"headline": "Amplitude and Peak Voltage", "body_md": "The **amplitude** of an AC waveform is its maximum displacement from zero. For voltage, this is called the **peak voltage (V_peak)**.\n\nIt represents the highest positive or negative voltage the waveform reaches in a cycle.", "visual": {"type": "local_asset", "value": "assets/images/course_specific/ac_waveform_peak.png"}, "interactive_element": {"type": "multiple_choice_text", "question_text": "If an AC voltage has a V_peak of 10V, what is its maximum negative voltage?", "options": [{"id": "opt1", "text": "0V"}, {"id": "opt2", "text": "-5V"}, {"id": "opt3", "text": "-10V"}, {"id": "opt4", "text": "-20V"}], "correct_option_id": "opt3", "feedback_correct": "Correct! The negative peak is equal in magnitude to the positive peak, just in the opposite direction.", "feedback_incorrect": "The sine wave is symmetrical around zero. The negative peak is the same magnitude as the positive peak."}}}, {"id": "what-is-ac-screen-5", "type": "lesson_screen", "order": 5, "estimatedTimeSeconds": 90, "content": {"headline": "Why Use AC?", "body_md": "AC has major advantages for power generation and distribution:\n\n- **Easy to transform:** Voltages can be easily stepped up (for efficient long-distance transmission) or stepped down (for safe use) using transformers.\n- **Simpler generator design:** AC generators (alternators) are generally simpler and more robust than DC generators.", "visual": {"type": "unsplash_search", "value": "electrical transformer station"}, "interactive_element": {"type": "button", "button_text": "Got it! Let's recap.", "action": "next_screen"}}}, {"id": "what-is-ac-screen-6", "type": "lesson_screen", "order": 6, "estimatedTimeSeconds": 60, "content": {"headline": "AC Fundamentals Summary", "body_md": "You're alternating with knowledge!\n\n- AC current periodically reverses direction, typically as a sine wave.\n- **Frequency (Hz):** Cycles per second.\n- **Peak Voltage (V_peak):** Maximum voltage in a cycle.\n- AC is easily transformed, making it ideal for power distribution.", "visual": {"type": "giphy_search", "value": "wave electricity success"}, "interactive_element": {"type": "button", "button_text": "Finish Lesson", "action": "complete_lesson"}}}]}, {"id": "voltage-current-ac", "title": "Voltage and Current in AC Circuits", "description": "Understand RMS and peak values.", "order": 2, "type": "interactive_lesson", "estimatedTimeMinutes": 20, "contentBlocks": [{"id": "voltage-current-ac-screen-1", "type": "lesson_screen", "order": 1, "estimatedTimeSeconds": 75, "content": {"headline": "Beyond Peaks: Effective Values", "body_md": "We know AC voltage has a peak value (V_peak). But since it's always changing, how do we talk about its 'effective' strength, especially when comparing to DC?\n\nThis is where **RMS (Root Mean Square)** values come in!", "visual": {"type": "unsplash_search", "value": "measuring tape comparison"}, "interactive_element": {"type": "button", "button_text": "What is RMS?", "action": "next_screen"}}}, {"id": "voltage-current-ac-screen-2", "type": "lesson_screen", "order": 2, "estimatedTimeSeconds": 120, "content": {"headline": "RMS: The DC Equivalent", "body_md": "The **RMS value** of an AC voltage (V_rms) is the DC voltage that would produce the **same amount of heat (power)** in a resistor as the AC voltage does.\n\nFor a sinusoidal waveform:\n`V_rms = V_peak / √2 ≈ V_peak * 0.707`\n\nSimilarly for current:\n`I_rms = I_peak / √2 ≈ I_peak * 0.707`\n\nWhen you see an AC voltage like '120V' for household power, that's usually the RMS value!", "visual": {"type": "local_asset", "value": "assets/images/course_specific/vrms_formula.png"}, "interactive_element": {"type": "multiple_choice_text", "question_text": "If an AC voltage has a V_peak of 170V, what is its approximate V_rms?", "options": [{"id": "opt1", "text": "85V"}, {"id": "opt2", "text": "120V"}, {"id": "opt3", "text": "170V"}, {"id": "opt4", "text": "240V"}], "correct_option_id": "opt2", "feedback_correct": "Correct! V_rms ≈ 170V * 0.707 ≈ 120V. This is typical for North American household power.", "feedback_incorrect": "Use the formula: V_rms = V_peak * 0.707 (or V_peak / √2)."}}}, {"id": "voltage-current-ac-screen-3", "type": "lesson_screen", "order": 3, "estimatedTimeSeconds": 90, "content": {"headline": "Peak-to-Peak Voltage (V_pp)", "body_md": "Another way to describe AC voltage is its **peak-to-peak voltage (V_pp)**.\n\nThis is simply the total voltage swing from the positive peak to the negative peak.\n\n`V_pp = 2 * V_peak`", "visual": {"type": "local_asset", "value": "assets/images/course_specific/ac_waveform_vpp.png"}, "interactive_element": {"type": "button", "button_text": "Why so many measures?", "action": "next_screen"}}}, {"id": "voltage-current-ac-screen-4", "type": "lesson_screen", "order": 4, "estimatedTimeSeconds": 100, "content": {"headline": "Context Matters!", "body_md": "Different measures are useful in different contexts:\n\n- **V_peak:** Important for component ratings (e.g., ensuring a capacitor doesn't break down).\n- **V_rms:** Most common for power calculations and general AC specifications (like your wall outlet voltage).\n- **V_pp:** Often used when looking at signals on an oscilloscope.", "visual": {"type": "giphy_search", "value": "oscilloscope waveform"}, "interactive_element": {"type": "multiple_choice_text", "question_text": "If you're calculating the power dissipated by a resistor in an AC circuit, which voltage value is most directly useful?", "options": [{"id": "opt1", "text": "V_peak"}, {"id": "opt2", "text": "V_rms"}, {"id": "opt3", "text": "V_pp"}, {"id": "opt4", "text": "Average Voltage (which is 0 for pure AC!)"}], "correct_option_id": "opt2", "feedback_correct": "Correct! V_rms is the DC equivalent for power calculations (P = V_rms² / R or P = I_rms² * R).", "feedback_incorrect": "Think about which AC value is defined by its equivalent heating effect in DC."}}}, {"id": "voltage-current-ac-screen-5", "type": "lesson_screen", "order": 5, "estimatedTimeSeconds": 75, "content": {"headline": "Average Value (Careful!)", "body_md": "The **average value** of a symmetrical AC sine wave over a full cycle is **zero**! The positive half cancels out the negative half.\n\nHowever, the average of the *rectified* AC (e.g., after a full-wave rectifier) is non-zero and is sometimes used: `V_avg_rectified = (2 * V_peak) / π ≈ 0.637 * V_peak`.", "visual": {"type": "local_asset", "value": "assets/images/course_specific/full_wave_rectified_avg.png"}, "interactive_element": {"type": "button", "button_text": "Time to summarize!", "action": "next_screen"}}}, {"id": "voltage-current-ac-screen-6", "type": "lesson_screen", "order": 6, "estimatedTimeSeconds": 60, "content": {"headline": "AC Voltage/Current Measures Recap", "body_md": "You've got the measures down!\n\n- **V_peak (I_peak):** Maximum value.\n- **V_rms (I_rms):** Effective (DC equivalent) value, `V_peak / √2`.\n- **V_pp (I_pp):** Peak-to-peak, `2 * V_peak`.\n- Average of pure AC is zero; average of rectified AC is useful.", "visual": {"type": "giphy_search", "value": "measuring success"}, "interactive_element": {"type": "button", "button_text": "Finish Lesson", "action": "complete_lesson"}}}]}, {"id": "components-in-ac", "title": "Resistors, Capacitors, and Inductors in AC Circuits", "description": "Explore their frequency-dependent behavior (impedance - conceptual).", "order": 3, "type": "interactive_lesson", "estimatedTimeMinutes": 25, "contentBlocks": [{"id": "components-in-ac-screen-1", "type": "lesson_screen", "order": 1, "estimatedTimeSeconds": 60, "content": {"headline": "Components in a New Light: AC!", "body_md": "How do our familiar friends - Resistors, Capacitors, and Inductors - behave when the current is constantly changing direction and magnitude? Their opposition to current flow becomes more dynamic!", "visual": {"type": "unsplash_search", "value": "electronic components variety"}, "interactive_element": {"type": "button", "button_text": "Resistors First!", "action": "next_screen"}}}, {"id": "components-in-ac-screen-2", "type": "lesson_screen", "order": 2, "estimatedTimeSeconds": 100, "content": {"headline": "Resistors in AC: Still Resisting!", "body_md": "Good news! Resistors behave much the same in AC as in DC. They still resist current flow according to <PERSON><PERSON>'s Law (`V = IR`, using RMS values).\n\nCrucially, in a purely resistive AC circuit, the current waveform is **in phase** with the voltage waveform. They peak and cross zero at the same time.", "visual": {"type": "local_asset", "value": "assets/images/course_specific/resistor_ac_in_phase.gif"}, "interactive_element": {"type": "multiple_choice_text", "question_text": "In a purely resistive AC circuit, the current is:", "options": [{"id": "opt1", "text": "In phase with the voltage"}, {"id": "opt2", "text": "Leading the voltage by 90°"}, {"id": "opt3", "text": "Lagging the voltage by 90°"}], "correct_option_id": "opt1", "feedback_correct": "Correct! For resistors in AC, voltage and current are in step.", "feedback_incorrect": "Think about a simple resistor. Does it inherently cause a time delay between voltage and current?"}}}, {"id": "components-in-ac-screen-3", "type": "lesson_screen", "order": 3, "estimatedTimeSeconds": 120, "content": {"headline": "Capacitors in AC: Capacitive Reactance (Xc)", "body_md": "Capacitors in AC constantly charge and discharge. This creates an opposition to current flow called **Capacitive Reactance (Xc)**, measured in Ohms.\n\n`Xc = 1 / (2πfC)`\n\n- **Higher frequency (f) or Higher capacitance (C) → Lower Xc** (easier for AC to pass).\n- **Lower frequency (f) or Lower capacitance (C) → Higher Xc** (harder for AC to pass).\n\nAt 0Hz (DC), Xc is infinite – a capacitor blocks DC once charged!", "visual": {"type": "static_text", "value": "Xc = 1 / (2πfC)"}, "interactive_element": {"type": "multiple_choice_text", "question_text": "If the AC frequency decreases, what happens to Capacitive Reactance (Xc)?", "options": [{"id": "opt1", "text": "Increases"}, {"id": "opt2", "text": "Decreases"}, {"id": "opt3", "text": "Stays the same"}], "correct_option_id": "opt1", "feedback_correct": "Correct! Lower frequency means more time for the capacitor to charge/discharge, thus more opposition (higher Xc).", "feedback_incorrect": "Look at the formula: Xc = 1 / (2πfC). If 'f' in the denominator gets smaller, what happens to Xc?"}}}, {"id": "components-in-ac-screen-4", "type": "lesson_screen", "order": 4, "estimatedTimeSeconds": 120, "content": {"headline": "Inductors in AC: Inductive Reactance (XL)", "body_md": "Inductors also oppose AC current due to their changing magnetic fields. This opposition is **Inductive Reactance (XL)**, also in Ohms.\n\n`XL = 2πfL`\n\n- **Higher frequency (f) or Higher inductance (L) → Higher XL** (harder for AC to pass).\n- **Lower frequency (f) or Lower inductance (L) → Lower XL** (easier for AC to pass).\n\nAt 0Hz (DC), XL is zero – an ideal inductor is like a short circuit to steady DC.", "visual": {"type": "static_text", "value": "XL = 2πfL"}, "interactive_element": {"type": "multiple_choice_text", "question_text": "If the AC frequency increases, what happens to Inductive Reactance (XL)?", "options": [{"id": "opt1", "text": "Increases"}, {"id": "opt2", "text": "Decreases"}, {"id": "opt3", "text": "Stays the same"}], "correct_option_id": "opt1", "feedback_correct": "Correct! Higher frequency means faster current changes, which inductors oppose more strongly (higher XL).", "feedback_incorrect": "Look at the formula: XL = 2πfL. If 'f' gets larger, what happens to XL?"}}}, {"id": "components-in-ac-screen-5", "type": "lesson_screen", "order": 5, "estimatedTimeSeconds": 90, "content": {"headline": "Impedance (Z): Total AC Opposition", "body_md": "In AC circuits with combinations of R, C, and L, their total opposition to current flow is called **Impedance (Z)**. It's also measured in Ohms.\n\nImpedance is a complex quantity that considers both resistance (R) and reactance (Xc and XL). It's like the 'AC version' of resistance.\n\n`Z = √(R² + (XL - Xc)²)` (This is for series RLC, we'll explore it more later!)", "visual": {"type": "giphy_search", "value": "complex maze puzzle"}, "interactive_element": {"type": "button", "button_text": "Let's summarize!", "action": "next_screen"}}}, {"id": "components-in-ac-screen-6", "type": "lesson_screen", "order": 6, "estimatedTimeSeconds": 75, "content": {"headline": "R, C, L in AC: Recap", "body_md": "Key takeaways:\n\n- **Resistors:** Current and voltage are in phase.\n- **Capacitors:** Offer Capacitive Reactance (Xc), inversely proportional to frequency. Current *leads* voltage (ICE).\n- **Inductors:** Offer Inductive Reactance (XL), directly proportional to frequency. Voltage *leads* current (ELI).\n- **Impedance (Z):** Total opposition in AC circuits.", "visual": {"type": "giphy_search", "value": "electronics components working"}, "interactive_element": {"type": "button", "button_text": "Finish Lesson", "action": "complete_lesson"}}}]}, {"id": "phase-relationships-ac", "title": "Phase Relationships in AC Circuits", "description": "Understand how voltage and current waveforms align.", "order": 4, "type": "interactive_lesson", "estimatedTimeMinutes": 20, "contentBlocks": [{"id": "phase-relationships-screen-1", "type": "lesson_screen", "order": 1, "estimatedTimeSeconds": 60, "content": {"headline": "Timing is Everything: Phase!", "body_md": "In AC circuits, voltage and current are both sine waves. But they don't always peak and cross zero at the exact same time! The **phase relationship** describes this timing difference.\n\nThink of two runners: are they in step, or is one ahead of the other?", "visual": {"type": "unsplash_search", "value": "two runners on a track"}, "interactive_element": {"type": "button", "button_text": "How does it work for resistors?", "action": "next_screen"}}}, {"id": "phase-relationships-screen-2", "type": "lesson_screen", "order": 2, "estimatedTimeSeconds": 90, "content": {"headline": "Resistors: In Phase", "body_md": "As we mentioned, for a purely **resistive** AC circuit, the current (I) and voltage (V) waveforms are **in phase**.\n\nThis means they reach their peaks, troughs, and zero-crossings simultaneously. There's no time lag between them.", "visual": {"type": "local_asset", "value": "assets/images/course_specific/resistor_ac_in_phase.gif"}, "interactive_element": {"type": "button", "button_text": "What about capacitors? (ICE)", "action": "next_screen"}}}, {"id": "phase-relationships-screen-3", "type": "lesson_screen", "order": 3, "estimatedTimeSeconds": 120, "content": {"headline": "Capacitors: Current LEADS Voltage (ICE)", "body_md": "In a purely **capacitive** AC circuit, the current (I) **leads** the voltage (V) by 90 degrees (or π/2 radians).\n\nThink **ICE**: In a **C**apacitor, **I** (current) comes before **E** (electromotive force/voltage).\n\nWhy? A capacitor opposes changes in voltage. Current must flow first to build up charge (and thus voltage) on the plates.", "visual": {"type": "local_asset", "value": "assets/images/course_specific/capacitor_ac_phase.gif"}, "interactive_element": {"type": "multiple_choice_text", "question_text": "In a capacitor, does current peak before or after voltage?", "options": [{"id": "opt1", "text": "After"}, {"id": "opt2", "text": "Before"}, {"id": "opt3", "text": "At the same time"}], "correct_option_id": "opt2", "feedback_correct": "Correct! Current leads (peaks before) voltage in a capacitor.", "feedback_incorrect": "Remember ICE: Current (I) comes before Voltage (E) in a Capacitor (C)."}}}, {"id": "phase-relationships-screen-4", "type": "lesson_screen", "order": 4, "estimatedTimeSeconds": 120, "content": {"headline": "Inductors: Voltage LEADS Current (ELI)", "body_md": "In a purely **inductive** AC circuit, the voltage (V) **leads** the current (I) by 90 degrees (or π/2 radians).\n\nThink **ELI**: In an **L** (inductor), **E** (electromotive force/voltage) comes before **I** (current).\n\nWhy? An inductor opposes changes in current. Voltage (back EMF) must be present first to resist the change in current.", "visual": {"type": "local_asset", "value": "assets/images/course_specific/inductor_ac_phase.gif"}, "interactive_element": {"type": "multiple_choice_text", "question_text": "In an inductor, does voltage peak before or after current?", "options": [{"id": "opt1", "text": "After"}, {"id": "opt2", "text": "Before"}, {"id": "opt3", "text": "At the same time"}], "correct_option_id": "opt2", "feedback_correct": "Correct! Voltage leads (peaks before) current in an inductor.", "feedback_incorrect": "Remember ELI: Voltage (E) comes before Current (I) in an Inductor (L)."}}}, {"id": "phase-relationships-screen-5", "type": "lesson_screen", "order": 5, "estimatedTimeSeconds": 90, "content": {"headline": "Why Phase Matters", "body_md": "Phase relationships are crucial for:\n\n- Understanding power in AC circuits (True Power vs. Reactive Power).\n- Designing resonant circuits (like in radios) where Xc and XL cancel out.\n- Analyzing complex AC circuits with R, L, and C components.", "visual": {"type": "giphy_search", "value": "gears turning together"}, "interactive_element": {"type": "button", "button_text": "Let's recap these phases!", "action": "next_screen"}}}, {"id": "phase-relationships-screen-6", "type": "lesson_screen", "order": 6, "estimatedTimeSeconds": 75, "content": {"headline": "Phase Relationships Summary", "body_md": "You've phased it in perfectly!\n\n- **Resistor:** Voltage and Current are IN PHASE.\n- **Capacitor (ICE):** Current LEADS Voltage by 90°.\n- **Inductor (ELI):** Voltage LEADS Current by 90°.\n\nThese relationships are key to understanding AC circuit behavior!", "visual": {"type": "giphy_search", "value": "synchronized dance"}, "interactive_element": {"type": "button", "button_text": "Finish Lesson", "action": "complete_lesson"}}}]}, {"id": "basic-ac-analysis", "title": "Basic AC Circuit Analysis (Qualitative)", "description": "Understand the behavior of simple RLC circuits.", "order": 5, "type": "interactive_lesson", "estimatedTimeMinutes": 25, "contentBlocks": [{"id": "basic-ac-analysis-screen-1", "type": "lesson_screen", "order": 1, "estimatedTimeSeconds": 75, "content": {"headline": "Putting It Together: RLC Circuits", "body_md": "Now that we know how R, L, and C behave individually in AC, what happens when they're combined? We get RLC circuits!\n\nAnalyzing these fully involves complex numbers (phasors), but we can understand their behavior qualitatively.", "visual": {"type": "unsplash_search", "value": "team working together puzzle"}, "interactive_element": {"type": "button", "button_text": "How does frequency play a role?", "action": "next_screen"}}}, {"id": "basic-ac-analysis-screen-2", "type": "lesson_screen", "order": 2, "estimatedTimeSeconds": 120, "content": {"headline": "Frequency Response: The Key", "body_md": "The behavior of RLC circuits is highly **frequency-dependent** because Xc and XL change with frequency.\n\n- At **low frequencies:** Xc is high (capacitor acts like an open), XL is low (inductor acts like a short).\n- At **high frequencies:** Xc is low (capacitor acts like a short), XL is high (inductor acts like an open).\n- At a specific **resonant frequency:** Xc = XL, and they cancel each other out!", "visual": {"type": "local_asset", "value": "assets/images/course_specific/rlc_frequency_response.png"}, "interactive_element": {"type": "multiple_choice_text", "question_text": "At very high frequencies, which component tends to block AC current the most?", "options": [{"id": "opt1", "text": "Resistor"}, {"id": "opt2", "text": "Capacitor (low Xc)"}, {"id": "opt3", "text": "Inductor (high XL)"}], "correct_option_id": "opt3", "feedback_correct": "Correct! XL = 2πfL, so high 'f' means high XL (more opposition).", "feedback_incorrect": "Recall how Xc and XL change with frequency. Xc = 1/(2πfC), XL = 2πfL."}}}, {"id": "basic-ac-analysis-screen-3", "type": "lesson_screen", "order": 3, "estimatedTimeSeconds": 100, "content": {"headline": "Series RLC Circuit: A Quick Look", "body_md": "In a series RLC circuit:\n\n- **Impedance Z = √(R² + (XL - Xc)²)**\n- At resonance (XL = Xc), Z = R (minimum impedance!). Current is maximum.\n- This is how radio tuners work – they adjust C or L to resonate at the desired station's frequency, maximizing current for that signal.", "visual": {"type": "giphy_search", "value": "radio tuning dial"}, "interactive_element": {"type": "button", "button_text": "What about parallel RLC?", "action": "next_screen"}}}, {"id": "basic-ac-analysis-screen-4", "type": "lesson_screen", "order": 4, "estimatedTimeSeconds": 100, "content": {"headline": "Parallel RLC Circuit: A Glimpse", "body_md": "In a parallel RLC circuit:\n\n- Analysis is more complex, but at resonance (XL = Xc), the impedance is **maximum** (ideally infinite for perfect components!). Current is minimum.\n- This is useful for creating **filter circuits** that block specific frequencies.", "visual": {"type": "unsplash_search", "value": "sound wave filter"}, "interactive_element": {"type": "multiple_choice_text", "question_text": "At resonance in a series RLC circuit, impedance is:", "options": [{"id": "opt1", "text": "Minimum"}, {"id": "opt2", "text": "Maximum"}, {"id": "opt3", "text": "Zero"}], "correct_option_id": "opt1", "feedback_correct": "Correct! In series RLC, Z is minimum (equal to R) at resonance.", "feedback_incorrect": "Think about XL and Xc cancelling each other out in the impedance formula for series RLC."}}}, {"id": "basic-ac-analysis-screen-5", "type": "lesson_screen", "order": 5, "estimatedTimeSeconds": 75, "content": {"headline": "Qualitative AC Analysis: You Did It!", "body_md": "Great job grasping the basics!\n\n- RLC circuit behavior depends heavily on frequency.\n- Reactances (Xc, XL) change with frequency.\n- Resonance occurs when Xc = XL.\n- Series RLC: Minimum Z at resonance.\n- Parallel RLC: Maximum Z at resonance (conceptually).\n\nFull AC analysis uses phasors, which you'll see in more advanced topics!", "visual": {"type": "giphy_search", "value": "brain wave electronics"}, "interactive_element": {"type": "button", "button_text": "Finish Lesson", "action": "complete_lesson"}}}]}, {"id": "module-test-ac-circuit-apprentice", "title": "Module Test: AC Circuit Apprentice", "description": "Understand the fundamentals of alternating current and the behavior of basic components in AC circuits.", "order": 6, "type": "module_test_interactive", "estimatedTimeMinutes": 30, "passingScorePercentage": 70, "contentBlocks": [{"id": "test-ac-apprentice-q1", "type": "question_screen", "order": 1, "estimatedTimeSeconds": 75, "content": {"headline": "Question 1: AC Waveform", "body_md": "If an AC voltage completes 50 full cycles in one second, what is its frequency?", "visual": {"type": "local_asset", "value": "assets/images/course_specific/sine_wave_cycles.png"}, "interactive_element": {"type": "multiple_choice_text", "question_text": "Determine the frequency.", "options": [{"id": "opt1", "text": "0.02 Hz"}, {"id": "opt2", "text": "25 Hz"}, {"id": "opt3", "text": "50 Hz"}, {"id": "opt4", "text": "100 Hz"}], "correct_option_id": "opt3", "feedback_correct": "Correct! Frequency is cycles per second, so 50 cycles/second = 50Hz.", "feedback_incorrect": "Frequency is defined as the number of cycles per second."}}}, {"id": "test-ac-apprentice-q2", "type": "question_screen", "order": 2, "estimatedTimeSeconds": 90, "content": {"headline": "Question 2: RMS Value", "body_md": "An AC current has a peak value (I_peak) of 10 Amperes. What is its approximate RMS value (I_rms)?", "visual": {"type": "static_text", "value": "I_rms = I_peak / √2"}, "interactive_element": {"type": "multiple_choice_text", "question_text": "Calculate I_rms.", "options": [{"id": "opt1", "text": "5A"}, {"id": "opt2", "text": "7.07A"}, {"id": "opt3", "text": "10A"}, {"id": "opt4", "text": "14.14A"}], "correct_option_id": "opt2", "feedback_correct": "Correct! I_rms ≈ 10A * 0.707 ≈ 7.07A.", "feedback_incorrect": "Remember, I_rms = I_peak / √2, and 1/√2 is approximately 0.707."}}}, {"id": "test-ac-apprentice-q3", "type": "question_screen", "order": 3, "estimatedTimeSeconds": 75, "content": {"headline": "Question 3: Capacitive Reactance", "body_md": "How does the capacitive reactance (Xc) of a capacitor change if the frequency of the AC signal increases?", "visual": {"type": "static_text", "value": "Xc = 1 / (2πfC)"}, "interactive_element": {"type": "multiple_choice_text", "question_text": "Select the correct change.", "options": [{"id": "opt1", "text": "Xc Increases"}, {"id": "opt2", "text": "Xc Decreases"}, {"id": "opt3", "text": "Xc Stays the same"}, {"id": "opt4", "text": "Xc becomes zero"}], "correct_option_id": "opt2", "feedback_correct": "Correct! Since frequency (f) is in the denominator of Xc = 1/(2πfC), increasing f decreases Xc.", "feedback_incorrect": "Review the formula for capacitive reactance: Xc = 1 / (2πfC)."}}}, {"id": "test-ac-apprentice-q4", "type": "question_screen", "order": 4, "estimatedTimeSeconds": 90, "content": {"headline": "Question 4: Inductive Reactance", "body_md": "What happens to the inductive reactance (XL) of an inductor if the inductance (L) is doubled, while frequency remains constant?", "visual": {"type": "static_text", "value": "XL = 2πfL"}, "interactive_element": {"type": "multiple_choice_text", "question_text": "How does XL change?", "options": [{"id": "opt1", "text": "XL is halved"}, {"id": "opt2", "text": "XL is doubled"}, {"id": "opt3", "text": "XL stays the same"}, {"id": "opt4", "text": "XL becomes zero"}], "correct_option_id": "opt2", "feedback_correct": "Correct! XL is directly proportional to L (XL = 2πfL), so doubling L doubles XL.", "feedback_incorrect": "Examine the direct relationship between XL and L in the formula XL = 2πfL."}}}, {"id": "test-ac-apprentice-q5", "type": "question_screen", "order": 5, "estimatedTimeSeconds": 75, "content": {"headline": "Question 5: Phase Relationship", "body_md": "In a purely inductive AC circuit, what is the phase relationship between voltage (V) and current (I)?", "visual": {"type": "giphy_search", "value": "ELI the ICE man"}, "interactive_element": {"type": "multiple_choice_text", "question_text": "Describe the phase.", "options": [{"id": "opt1", "text": "V and I are in phase"}, {"id": "opt2", "text": "I leads V by 90°"}, {"id": "opt3", "text": "<PERSON> leads I by 90°"}, {"id": "opt4", "text": "V lags I by 180°"}], "correct_option_id": "opt3", "feedback_correct": "Correct! Remember ELI: In an Inductor (L), Voltage (E) leads Current (I).", "feedback_incorrect": "Think of the mnemonic ELI the ICE man. For inductors (L), E (voltage) comes before I (current)."}}}]}]}