import 'package:flutter/material.dart';
import 'dart:math' as math;

/// A widget that helps users calculate and understand various probability concepts
class InteractiveProbabilityCalculatorWidget extends StatefulWidget {
  /// The data for this widget
  final Map<String, dynamic> data;

  /// Optional callback when the widget state changes
  final Function(bool isCompleted)? onStateChanged;

  const InteractiveProbabilityCalculatorWidget({
    Key? key,
    required this.data,
    this.onStateChanged,
  }) : super(key: key);

  /// Factory constructor to create the widget from JSON data
  factory InteractiveProbabilityCalculatorWidget.fromData(Map<String, dynamic> data) {
    return InteractiveProbabilityCalculatorWidget(
      data: data,
    );
  }

  @override
  State<InteractiveProbabilityCalculatorWidget> createState() => _InteractiveProbabilityCalculatorWidgetState();
}

class _InteractiveProbabilityCalculatorWidgetState extends State<InteractiveProbabilityCalculatorWidget> {
  // Current calculator mode
  String _currentMode = 'simple';

  // Whether the widget is completed
  bool _isCompleted = false;

  // Current example
  int _currentExampleIndex = 0;

  // Colors
  late Color _primaryColor;
  late Color _secondaryColor;
  late Color _accentColor;
  late Color _backgroundColor;
  late Color _textColor;

  // List of examples
  late List<Map<String, dynamic>> _examples;

  // Whether to show the example
  bool _showExample = false;

  // Simple probability parameters
  int _favorableOutcomes = 1;
  int _totalOutcomes = 2;

  // Compound probability parameters (AND)
  double _probabilityA = 0.5;
  double _probabilityB = 0.5;
  bool _eventsIndependent = true;
  double _conditionalProbability = 0.5; // P(B|A)

  // Conditional probability parameters
  double _jointProbability = 0.25; // P(A and B)
  double _marginalProbability = 0.5; // P(A)

  // Binomial probability parameters
  int _trials = 5;
  int _successes = 3;
  double _successProbability = 0.5;

  // Whether to show step-by-step explanation
  bool _showExplanation = false;

  // Text editing controllers
  final TextEditingController _favorableOutcomesController = TextEditingController();
  final TextEditingController _totalOutcomesController = TextEditingController();
  final TextEditingController _probabilityAController = TextEditingController();
  final TextEditingController _probabilityBController = TextEditingController();
  final TextEditingController _conditionalProbabilityController = TextEditingController();
  final TextEditingController _jointProbabilityController = TextEditingController();
  final TextEditingController _marginalProbabilityController = TextEditingController();
  final TextEditingController _trialsController = TextEditingController();
  final TextEditingController _successesController = TextEditingController();
  final TextEditingController _successProbabilityController = TextEditingController();

  @override
  void initState() {
    super.initState();

    // Initialize colors
    _primaryColor = _parseColor(widget.data['primary_color']) ?? Colors.blue;
    _secondaryColor = _parseColor(widget.data['secondary_color']) ?? Colors.orange;
    _accentColor = _parseColor(widget.data['accent_color']) ?? Colors.green;
    _backgroundColor = _parseColor(widget.data['background_color']) ?? Colors.white;
    _textColor = _parseColor(widget.data['text_color']) ?? Colors.black87;

    // Initialize examples
    _examples = widget.data['examples'] != null
        ? List<Map<String, dynamic>>.from(widget.data['examples'])
        : _getDefaultExamples();

    // Initialize text controllers
    _favorableOutcomesController.text = _favorableOutcomes.toString();
    _totalOutcomesController.text = _totalOutcomes.toString();
    _probabilityAController.text = _probabilityA.toString();
    _probabilityBController.text = _probabilityB.toString();
    _conditionalProbabilityController.text = _conditionalProbability.toString();
    _jointProbabilityController.text = _jointProbability.toString();
    _marginalProbabilityController.text = _marginalProbability.toString();
    _trialsController.text = _trials.toString();
    _successesController.text = _successes.toString();
    _successProbabilityController.text = _successProbability.toString();

    // Add listeners to text controllers
    _favorableOutcomesController.addListener(_updateSimpleProbability);
    _totalOutcomesController.addListener(_updateSimpleProbability);
    _probabilityAController.addListener(_updateCompoundProbability);
    _probabilityBController.addListener(_updateCompoundProbability);
    _conditionalProbabilityController.addListener(_updateCompoundProbability);
    _jointProbabilityController.addListener(_updateConditionalProbability);
    _marginalProbabilityController.addListener(_updateConditionalProbability);
    _trialsController.addListener(_updateBinomialProbability);
    _successesController.addListener(_updateBinomialProbability);
    _successProbabilityController.addListener(_updateBinomialProbability);
  }

  @override
  void dispose() {
    // Dispose text controllers
    _favorableOutcomesController.dispose();
    _totalOutcomesController.dispose();
    _probabilityAController.dispose();
    _probabilityBController.dispose();
    _conditionalProbabilityController.dispose();
    _jointProbabilityController.dispose();
    _marginalProbabilityController.dispose();
    _trialsController.dispose();
    _successesController.dispose();
    _successProbabilityController.dispose();

    super.dispose();
  }

  // Parse color from hex string
  Color? _parseColor(String? hexString) {
    if (hexString == null) return null;

    hexString = hexString.replaceAll('#', '');
    if (hexString.length == 6) {
      hexString = 'FF$hexString';
    }

    return Color(int.parse(hexString, radix: 16));
  }

  // Get default examples if none provided
  List<Map<String, dynamic>> _getDefaultExamples() {
    return [
      {
        'title': 'Drawing a Card',
        'description': 'What is the probability of drawing an ace from a standard deck of 52 cards?',
        'mode': 'simple',
        'favorable_outcomes': 4,
        'total_outcomes': 52,
        'explanation': 'There are 4 aces in a standard deck of 52 cards. The probability is 4/52 = 1/13 ≈ 0.077 or about 7.7%.',
      },
      {
        'title': 'Rolling Dice',
        'description': 'What is the probability of rolling a sum of 7 with two fair dice?',
        'mode': 'simple',
        'favorable_outcomes': 6,
        'total_outcomes': 36,
        'explanation': 'When rolling two dice, there are 36 possible outcomes (6 × 6). The sum of 7 can be achieved in 6 ways: (1,6), (2,5), (3,4), (4,3), (5,2), (6,1). So the probability is 6/36 = 1/6 ≈ 0.167 or about 16.7%.',
      },
      {
        'title': 'Independent Events',
        'description': 'What is the probability of flipping a coin twice and getting heads both times?',
        'mode': 'compound',
        'probability_a': 0.5,
        'probability_b': 0.5,
        'events_independent': true,
        'explanation': 'The probability of getting heads on a single flip is 0.5. Since the flips are independent, the probability of getting heads on both flips is 0.5 × 0.5 = 0.25 or 25%.',
      },
      {
        'title': 'Dependent Events',
        'description': 'What is the probability of drawing two aces in a row from a standard deck of cards without replacement?',
        'mode': 'compound',
        'probability_a': 4/52,
        'probability_b': 3/51,
        'events_independent': false,
        'conditional_probability': 3/51,
        'explanation': 'The probability of drawing an ace on the first draw is 4/52. After drawing an ace, there are 3 aces left out of 51 cards, so the probability of drawing another ace is 3/51. The probability of both events is (4/52) × (3/51) = 12/2652 = 1/221 ≈ 0.0045 or about 0.45%.',
      },
      {
        'title': 'Medical Test',
        'description': 'If a disease affects 1% of the population and a test for the disease has a 95% accuracy rate, what is the probability that a person has the disease given a positive test result?',
        'mode': 'conditional',
        'joint_probability': 0.0095,
        'marginal_probability': 0.0590,
        'explanation': 'Using Bayes\' theorem, we need P(Disease|Positive) = P(Positive|Disease) × P(Disease) / P(Positive). P(Positive|Disease) = 0.95 (test accuracy), P(Disease) = 0.01 (prevalence), P(Positive) = P(Positive|Disease) × P(Disease) + P(Positive|No Disease) × P(No Disease) = 0.95 × 0.01 + 0.05 × 0.99 = 0.0095 + 0.0495 = 0.059. Therefore, P(Disease|Positive) = 0.95 × 0.01 / 0.059 = 0.0095 / 0.059 ≈ 0.161 or about 16.1%.',
      },
      {
        'title': 'Binomial Probability',
        'description': 'What is the probability of getting exactly 3 heads in 5 coin flips?',
        'mode': 'binomial',
        'trials': 5,
        'successes': 3,
        'success_probability': 0.5,
        'explanation': 'Using the binomial probability formula: P(X = k) = C(n,k) × p^k × (1-p)^(n-k), where n is the number of trials, k is the number of successes, and p is the probability of success in a single trial. P(X = 3) = C(5,3) × 0.5^3 × 0.5^2 = 10 × 0.125 × 0.25 = 0.3125 or 31.25%.',
      },
    ];
  }

  // Update simple probability parameters
  void _updateSimpleProbability() {
    try {
      setState(() {
        _favorableOutcomes = int.parse(_favorableOutcomesController.text);
        _totalOutcomes = int.parse(_totalOutcomesController.text);
      });
    } catch (e) {
      // Handle parsing errors
    }
  }

  // Update compound probability parameters
  void _updateCompoundProbability() {
    try {
      setState(() {
        _probabilityA = double.parse(_probabilityAController.text);
        _probabilityB = double.parse(_probabilityBController.text);
        if (!_eventsIndependent) {
          _conditionalProbability = double.parse(_conditionalProbabilityController.text);
        }
      });
    } catch (e) {
      // Handle parsing errors
    }
  }

  // Update conditional probability parameters
  void _updateConditionalProbability() {
    try {
      setState(() {
        _jointProbability = double.parse(_jointProbabilityController.text);
        _marginalProbability = double.parse(_marginalProbabilityController.text);
      });
    } catch (e) {
      // Handle parsing errors
    }
  }

  // Update binomial probability parameters
  void _updateBinomialProbability() {
    try {
      setState(() {
        _trials = int.parse(_trialsController.text);
        _successes = int.parse(_successesController.text);
        _successProbability = double.parse(_successProbabilityController.text);
      });
    } catch (e) {
      // Handle parsing errors
    }
  }

  // Calculate simple probability
  double _calculateSimpleProbability() {
    if (_totalOutcomes <= 0) return 0.0;
    return _favorableOutcomes / _totalOutcomes;
  }

  // Calculate compound probability (AND)
  double _calculateCompoundProbability() {
    if (_eventsIndependent) {
      return _probabilityA * _probabilityB;
    } else {
      return _probabilityA * _conditionalProbability;
    }
  }

  // Calculate conditional probability
  double _calculateConditionalProbability() {
    if (_marginalProbability <= 0) return 0.0;
    return _jointProbability / _marginalProbability;
  }

  // Calculate binomial probability
  double _calculateBinomialProbability() {
    if (_trials < 0 || _successes < 0 || _successes > _trials) return 0.0;

    // Calculate binomial coefficient C(n,k)
    int coefficient = _binomialCoefficient(_trials, _successes);

    // Calculate probability
    double successProb = math.pow(_successProbability, _successes).toDouble();
    double failureProb = math.pow(1 - _successProbability, _trials - _successes).toDouble();

    return coefficient * successProb * failureProb;
  }

  // Calculate binomial coefficient C(n,k)
  int _binomialCoefficient(int n, int k) {
    if (k < 0 || k > n) return 0;
    if (k == 0 || k == n) return 1;

    // Use the symmetry of binomial coefficients
    if (k > n - k) k = n - k;

    int result = 1;
    for (int i = 0; i < k; i++) {
      result = result * (n - i) ~/ (i + 1);
    }

    return result;
  }

  // Load an example
  void _loadExample(int index) {
    if (index >= 0 && index < _examples.length) {
      final example = _examples[index];

      setState(() {
        _currentExampleIndex = index;
        _currentMode = example['mode'];
        _showExample = true;

        // Set parameters based on the example mode
        switch (_currentMode) {
          case 'simple':
            _favorableOutcomes = example['favorable_outcomes'];
            _totalOutcomes = example['total_outcomes'];
            _favorableOutcomesController.text = _favorableOutcomes.toString();
            _totalOutcomesController.text = _totalOutcomes.toString();
            break;
          case 'compound':
            _probabilityA = example['probability_a'];
            _probabilityB = example['probability_b'];
            _eventsIndependent = example['events_independent'];
            if (!_eventsIndependent && example['conditional_probability'] != null) {
              _conditionalProbability = example['conditional_probability'];
            }
            _probabilityAController.text = _probabilityA.toString();
            _probabilityBController.text = _probabilityB.toString();
            _conditionalProbabilityController.text = _conditionalProbability.toString();
            break;
          case 'conditional':
            _jointProbability = example['joint_probability'];
            _marginalProbability = example['marginal_probability'];
            _jointProbabilityController.text = _jointProbability.toString();
            _marginalProbabilityController.text = _marginalProbability.toString();
            break;
          case 'binomial':
            _trials = example['trials'];
            _successes = example['successes'];
            _successProbability = example['success_probability'];
            _trialsController.text = _trials.toString();
            _successesController.text = _successes.toString();
            _successProbabilityController.text = _successProbability.toString();
            break;
        }
      });
    }
  }

  // Reset to default values
  void _resetToDefault() {
    setState(() {
      switch (_currentMode) {
        case 'simple':
          _favorableOutcomes = 1;
          _totalOutcomes = 2;
          _favorableOutcomesController.text = _favorableOutcomes.toString();
          _totalOutcomesController.text = _totalOutcomes.toString();
          break;
        case 'compound':
          _probabilityA = 0.5;
          _probabilityB = 0.5;
          _eventsIndependent = true;
          _conditionalProbability = 0.5;
          _probabilityAController.text = _probabilityA.toString();
          _probabilityBController.text = _probabilityB.toString();
          _conditionalProbabilityController.text = _conditionalProbability.toString();
          break;
        case 'conditional':
          _jointProbability = 0.25;
          _marginalProbability = 0.5;
          _jointProbabilityController.text = _jointProbability.toString();
          _marginalProbabilityController.text = _marginalProbability.toString();
          break;
        case 'binomial':
          _trials = 5;
          _successes = 3;
          _successProbability = 0.5;
          _trialsController.text = _trials.toString();
          _successesController.text = _successes.toString();
          _successProbabilityController.text = _successProbability.toString();
          break;
      }

      _showExample = false;
      _showExplanation = false;
    });
  }

  // Toggle explanation visibility
  void _toggleExplanation() {
    setState(() {
      _showExplanation = !_showExplanation;
    });
  }

  // Change calculator mode
  void _changeMode(String mode) {
    setState(() {
      _currentMode = mode;
      _showExample = false;
      _showExplanation = false;
    });
  }

  // Mark the widget as completed
  void _markAsCompleted() {
    if (!_isCompleted) {
      setState(() {
        _isCompleted = true;
      });

      // Notify parent of state change
      widget.onStateChanged?.call(true);
    }
  }

  // Build the calculator and controls section
  Widget _buildCalculatorAndControls() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Mode selector
        Container(
          padding: const EdgeInsets.all(8),
          decoration: BoxDecoration(
            color: Colors.grey[100],
            borderRadius: BorderRadius.circular(8),
            border: Border.all(color: Colors.grey[300]!),
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                'Probability Type:',
                style: TextStyle(
                  fontSize: 14,
                  fontWeight: FontWeight.bold,
                  color: _textColor,
                ),
              ),
              const SizedBox(height: 8),
              SingleChildScrollView(
                scrollDirection: Axis.horizontal,
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                  children: [
                    _buildModeButton('simple', 'Simple'),
                    const SizedBox(width: 8),
                    _buildModeButton('compound', 'Compound'),
                    const SizedBox(width: 8),
                    _buildModeButton('conditional', 'Conditional'),
                    const SizedBox(width: 8),
                    _buildModeButton('binomial', 'Binomial'),
                  ],
                ),
              ),
            ],
          ),
        ),

        const SizedBox(height: 16),

        // Calculator based on mode
        _buildCalculator(),

        const SizedBox(height: 16),

        // Result display
        Container(
          width: double.infinity,
          padding: const EdgeInsets.all(12),
          decoration: BoxDecoration(
            color: _primaryColor.withOpacity(0.1),
            borderRadius: BorderRadius.circular(8),
            border: Border.all(color: _primaryColor),
          ),
          child: Column(
            children: [
              Text(
                'Probability:',
                style: TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.bold,
                  color: _primaryColor,
                ),
              ),
              const SizedBox(height: 8),
              Text(
                _formatProbability(_calculateProbability()),
                style: TextStyle(
                  fontSize: 24,
                  fontWeight: FontWeight.bold,
                  color: _primaryColor,
                ),
              ),
              if (_showExplanation && _showExample)
                Padding(
                  padding: const EdgeInsets.only(top: 12),
                  child: Container(
                    width: double.infinity,
                    padding: const EdgeInsets.all(8),
                    decoration: BoxDecoration(
                      color: _accentColor.withOpacity(0.1),
                      borderRadius: BorderRadius.circular(8),
                      border: Border.all(color: _accentColor),
                    ),
                    child: Text(
                      _examples[_currentExampleIndex]['explanation'],
                      style: TextStyle(
                        fontSize: 14,
                        color: _textColor,
                      ),
                    ),
                  ),
                ),
            ],
          ),
        ),

        const SizedBox(height: 16),

        // Examples and reset buttons
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            ElevatedButton(
              onPressed: _resetToDefault,
              style: ElevatedButton.styleFrom(
                backgroundColor: _secondaryColor,
                foregroundColor: Colors.white,
              ),
              child: const Text('Reset'),
            ),
            if (_showExample)
              ElevatedButton(
                onPressed: _toggleExplanation,
                style: ElevatedButton.styleFrom(
                  backgroundColor: _accentColor,
                  foregroundColor: Colors.white,
                ),
                child: Text(_showExplanation ? 'Hide Explanation' : 'Show Explanation'),
              ),
            ElevatedButton(
              onPressed: () => _showExamplesDialog(context),
              style: ElevatedButton.styleFrom(
                backgroundColor: _primaryColor,
                foregroundColor: Colors.white,
              ),
              child: const Text('Examples'),
            ),
            ElevatedButton(
              onPressed: _markAsCompleted,
              style: ElevatedButton.styleFrom(
                backgroundColor: _accentColor,
                foregroundColor: Colors.white,
              ),
              child: const Text('Complete'),
            ),
          ],
        ),
      ],
    );
  }

  // Build the calculator based on the current mode
  Widget _buildCalculator() {
    switch (_currentMode) {
      case 'simple':
        return _buildSimpleProbabilityCalculator();
      case 'compound':
        return _buildCompoundProbabilityCalculator();
      case 'conditional':
        return _buildConditionalProbabilityCalculator();
      case 'binomial':
        return _buildBinomialProbabilityCalculator();
      default:
        return _buildSimpleProbabilityCalculator();
    }
  }

  // Build simple probability calculator
  Widget _buildSimpleProbabilityCalculator() {
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: Colors.grey[100],
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: Colors.grey[300]!),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Simple Probability: P(A) = Favorable Outcomes / Total Outcomes',
            style: TextStyle(
              fontSize: 14,
              fontWeight: FontWeight.bold,
              color: _textColor,
            ),
          ),
          const SizedBox(height: 12),
          Row(
            children: [
              Expanded(
                child: TextField(
                  controller: _favorableOutcomesController,
                  keyboardType: TextInputType.number,
                  decoration: InputDecoration(
                    labelText: 'Favorable Outcomes',
                    border: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(8),
                    ),
                  ),
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: TextField(
                  controller: _totalOutcomesController,
                  keyboardType: TextInputType.number,
                  decoration: InputDecoration(
                    labelText: 'Total Outcomes',
                    border: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(8),
                    ),
                  ),
                ),
              ),
            ],
          ),
          const SizedBox(height: 12),
          Text(
            'Examples: Drawing a specific card, rolling a specific number on a die',
            style: TextStyle(
              fontSize: 12,
              fontStyle: FontStyle.italic,
              color: _textColor.withOpacity(0.7),
            ),
          ),
        ],
      ),
    );
  }

  // Build compound probability calculator
  Widget _buildCompoundProbabilityCalculator() {
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: Colors.grey[100],
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: Colors.grey[300]!),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Compound Probability (AND): P(A and B)',
            style: TextStyle(
              fontSize: 14,
              fontWeight: FontWeight.bold,
              color: _textColor,
            ),
          ),
          const SizedBox(height: 12),
          Row(
            children: [
              Expanded(
                child: TextField(
                  controller: _probabilityAController,
                  keyboardType: const TextInputType.numberWithOptions(decimal: true),
                  decoration: InputDecoration(
                    labelText: 'P(A)',
                    border: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(8),
                    ),
                  ),
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: TextField(
                  controller: _probabilityBController,
                  keyboardType: const TextInputType.numberWithOptions(decimal: true),
                  decoration: InputDecoration(
                    labelText: 'P(B)',
                    border: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(8),
                    ),
                  ),
                ),
              ),
            ],
          ),
          const SizedBox(height: 12),
          Row(
            children: [
              Checkbox(
                value: _eventsIndependent,
                activeColor: _primaryColor,
                onChanged: (value) {
                  setState(() {
                    _eventsIndependent = value ?? true;
                  });
                },
              ),
              Text(
                'Events are independent',
                style: TextStyle(
                  fontSize: 14,
                  color: _textColor,
                ),
              ),
            ],
          ),
          if (!_eventsIndependent)
            Padding(
              padding: const EdgeInsets.only(top: 8),
              child: TextField(
                controller: _conditionalProbabilityController,
                keyboardType: const TextInputType.numberWithOptions(decimal: true),
                decoration: InputDecoration(
                  labelText: 'P(B|A) - Probability of B given A',
                  border: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(8),
                  ),
                ),
              ),
            ),
          const SizedBox(height: 12),
          Text(
            _eventsIndependent
                ? 'For independent events: P(A and B) = P(A) × P(B)'
                : 'For dependent events: P(A and B) = P(A) × P(B|A)',
            style: TextStyle(
              fontSize: 12,
              fontStyle: FontStyle.italic,
              color: _textColor.withOpacity(0.7),
            ),
          ),
        ],
      ),
    );
  }

  // Build conditional probability calculator
  Widget _buildConditionalProbabilityCalculator() {
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: Colors.grey[100],
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: Colors.grey[300]!),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Conditional Probability: P(A|B) = P(A and B) / P(B)',
            style: TextStyle(
              fontSize: 14,
              fontWeight: FontWeight.bold,
              color: _textColor,
            ),
          ),
          const SizedBox(height: 12),
          Row(
            children: [
              Expanded(
                child: TextField(
                  controller: _jointProbabilityController,
                  keyboardType: const TextInputType.numberWithOptions(decimal: true),
                  decoration: InputDecoration(
                    labelText: 'P(A and B)',
                    border: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(8),
                    ),
                  ),
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: TextField(
                  controller: _marginalProbabilityController,
                  keyboardType: const TextInputType.numberWithOptions(decimal: true),
                  decoration: InputDecoration(
                    labelText: 'P(B)',
                    border: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(8),
                    ),
                  ),
                ),
              ),
            ],
          ),
          const SizedBox(height: 12),
          Text(
            'Example: Probability of having a disease given a positive test result',
            style: TextStyle(
              fontSize: 12,
              fontStyle: FontStyle.italic,
              color: _textColor.withOpacity(0.7),
            ),
          ),
        ],
      ),
    );
  }

  // Build binomial probability calculator
  Widget _buildBinomialProbabilityCalculator() {
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: Colors.grey[100],
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: Colors.grey[300]!),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Binomial Probability: P(X = k) = C(n,k) × p^k × (1-p)^(n-k)',
            style: TextStyle(
              fontSize: 14,
              fontWeight: FontWeight.bold,
              color: _textColor,
            ),
          ),
          const SizedBox(height: 12),
          Row(
            children: [
              Expanded(
                child: TextField(
                  controller: _trialsController,
                  keyboardType: TextInputType.number,
                  decoration: InputDecoration(
                    labelText: 'Number of Trials (n)',
                    border: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(8),
                    ),
                  ),
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: TextField(
                  controller: _successesController,
                  keyboardType: TextInputType.number,
                  decoration: InputDecoration(
                    labelText: 'Number of Successes (k)',
                    border: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(8),
                    ),
                  ),
                ),
              ),
            ],
          ),
          const SizedBox(height: 12),
          TextField(
            controller: _successProbabilityController,
            keyboardType: const TextInputType.numberWithOptions(decimal: true),
            decoration: InputDecoration(
              labelText: 'Probability of Success in a Single Trial (p)',
              border: OutlineInputBorder(
                borderRadius: BorderRadius.circular(8),
              ),
            ),
          ),
          const SizedBox(height: 12),
          Text(
            'Example: Probability of getting exactly k heads in n coin flips',
            style: TextStyle(
              fontSize: 12,
              fontStyle: FontStyle.italic,
              color: _textColor.withOpacity(0.7),
            ),
          ),
        ],
      ),
    );
  }

  // Build a mode selection button
  Widget _buildModeButton(String mode, String label) {
    final isSelected = _currentMode == mode;

    return ElevatedButton(
      onPressed: () => _changeMode(mode),
      style: ElevatedButton.styleFrom(
        backgroundColor: isSelected ? _primaryColor : Colors.grey[300],
        foregroundColor: isSelected ? Colors.white : _textColor,
        padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
      ),
      child: Text(label),
    );
  }

  // Build the example section
  Widget _buildExampleSection() {
    final example = _examples[_currentExampleIndex];

    return Container(
      margin: const EdgeInsets.only(top: 16),
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: _accentColor.withOpacity(0.1),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: _accentColor),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Example: ${example['title']}',
            style: TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.bold,
              color: _accentColor,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            example['description'],
            style: TextStyle(
              fontSize: 14,
              color: _textColor,
            ),
          ),
        ],
      ),
    );
  }

  // Show examples dialog
  void _showExamplesDialog(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(
          'Probability Examples',
          style: TextStyle(
            color: _primaryColor,
            fontWeight: FontWeight.bold,
          ),
        ),
        content: Container(
          width: double.maxFinite,
          child: ListView.builder(
            shrinkWrap: true,
            itemCount: _examples.length,
            itemBuilder: (context, index) {
              final example = _examples[index];
              return ListTile(
                title: Text(example['title']),
                subtitle: Text(
                  example['description'],
                  maxLines: 2,
                  overflow: TextOverflow.ellipsis,
                ),
                trailing: Container(
                  padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                  decoration: BoxDecoration(
                    color: _getProbabilityTypeColor(example['mode']).withOpacity(0.2),
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: Text(
                    _getProbabilityTypeLabel(example['mode']),
                    style: TextStyle(
                      fontSize: 12,
                      color: _getProbabilityTypeColor(example['mode']),
                    ),
                  ),
                ),
                onTap: () {
                  Navigator.of(context).pop();
                  _loadExample(index);
                },
              );
            },
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Cancel'),
          ),
        ],
      ),
    );
  }

  // Get color for probability type
  Color _getProbabilityTypeColor(String mode) {
    switch (mode) {
      case 'simple':
        return Colors.blue;
      case 'compound':
        return Colors.green;
      case 'conditional':
        return Colors.orange;
      case 'binomial':
        return Colors.purple;
      default:
        return Colors.blue;
    }
  }

  // Get label for probability type
  String _getProbabilityTypeLabel(String mode) {
    switch (mode) {
      case 'simple':
        return 'Simple';
      case 'compound':
        return 'Compound';
      case 'conditional':
        return 'Conditional';
      case 'binomial':
        return 'Binomial';
      default:
        return 'Simple';
    }
  }

  // Calculate probability based on current mode
  double _calculateProbability() {
    switch (_currentMode) {
      case 'simple':
        return _calculateSimpleProbability();
      case 'compound':
        return _calculateCompoundProbability();
      case 'conditional':
        return _calculateConditionalProbability();
      case 'binomial':
        return _calculateBinomialProbability();
      default:
        return _calculateSimpleProbability();
    }
  }

  // Format probability as percentage and fraction
  String _formatProbability(double probability) {
    // Format as percentage
    String percentage = '${(probability * 100).toStringAsFixed(2)}%';

    // Format as fraction for simple probability
    String fraction = '';
    if (_currentMode == 'simple') {
      int gcd = _findGCD(_favorableOutcomes, _totalOutcomes);
      int numerator = _favorableOutcomes ~/ gcd;
      int denominator = _totalOutcomes ~/ gcd;
      fraction = ' ($numerator/$denominator)';
    }

    return '$percentage$fraction';
  }

  // Find greatest common divisor (GCD) using Euclidean algorithm
  int _findGCD(int a, int b) {
    while (b != 0) {
      int temp = b;
      b = a % b;
      a = temp;
    }
    return a;
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: _backgroundColor,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withOpacity(0.2),
            spreadRadius: 1,
            blurRadius: 3,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Title
          Text(
            widget.data['title'] ?? 'Probability Calculator',
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
              color: _textColor,
            ),
          ),

          const SizedBox(height: 8),

          // Description
          if (widget.data['description'] != null)
            Text(
              widget.data['description'],
              style: TextStyle(
                fontSize: 14,
                color: _textColor.withOpacity(0.8),
              ),
            ),

          const SizedBox(height: 16),

          // Calculator and controls
          _buildCalculatorAndControls(),

          // Example section
          if (_showExample)
            _buildExampleSection(),

          // Widget name tag
          if (widget.data['showNameTag'] ?? true)
            Padding(
              padding: const EdgeInsets.only(top: 8),
              child: Text(
                'InteractiveProbabilityCalculator',
                style: TextStyle(
                  color: Colors.grey[600],
                  fontSize: 12,
                  fontStyle: FontStyle.italic,
                ),
              ),
            ),
        ],
      ),
    );
  }
}
