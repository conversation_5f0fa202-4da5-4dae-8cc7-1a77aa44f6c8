import 'package:flutter/material.dart';
import 'dart:math' as math;

/// Custom painter for distribution visualization
class DistributionPainter extends CustomPainter {
  final Distribution distribution;
  final bool showPDF;
  final bool showCDF;
  final double minX;
  final double maxX;
  final double minY;
  final double maxY;
  final int numPoints;
  final Color gridColor;
  final Color axisColor;
  final Color pdfColor;
  final Color cdfColor;
  final Color textColor;

  DistributionPainter({
    required this.distribution,
    required this.showPDF,
    required this.showCDF,
    required this.minX,
    required this.maxX,
    required this.minY,
    required this.maxY,
    required this.numPoints,
    required this.gridColor,
    required this.axisColor,
    required this.pdfColor,
    required this.cdfColor,
    required this.textColor,
  });

  @override
  void paint(Canvas canvas, Size size) {
    final width = size.width;
    final height = size.height;

    // Calculate scale factors
    final xScale = width / (maxX - minX);
    final yScale = height / (maxY - minY);

    // Draw grid
    _drawGrid(canvas, size, xScale, yScale);

    // Draw axes
    _drawAxes(canvas, size, xScale, yScale);

    // Draw PDF
    if (showPDF) {
      _drawPDF(canvas, size, xScale, yScale);
    }

    // Draw CDF
    if (showCDF) {
      _drawCDF(canvas, size, xScale, yScale);
    }

    // Draw labels
    _drawLabels(canvas, size, xScale, yScale);
  }

  // Draw grid lines
  void _drawGrid(Canvas canvas, Size size, double xScale, double yScale) {
    final width = size.width;
    final height = size.height;

    final gridPaint = Paint()
      ..color = gridColor
      ..style = PaintingStyle.stroke
      ..strokeWidth = 0.5;

    // Draw vertical grid lines
    final xStep = _calculateStep(maxX - minX);
    for (double x = minX - (minX % xStep); x <= maxX; x += xStep) {
      final xPos = (x - minX) * xScale;
      canvas.drawLine(
        Offset(xPos, 0),
        Offset(xPos, height),
        gridPaint,
      );
    }

    // Draw horizontal grid lines
    final yStep = _calculateStep(maxY - minY);
    for (double y = minY - (minY % yStep); y <= maxY; y += yStep) {
      final yPos = height - (y - minY) * yScale;
      canvas.drawLine(
        Offset(0, yPos),
        Offset(width, yPos),
        gridPaint,
      );
    }
  }

  // Draw axes
  void _drawAxes(Canvas canvas, Size size, double xScale, double yScale) {
    final width = size.width;
    final height = size.height;

    final axisPaint = Paint()
      ..color = axisColor
      ..style = PaintingStyle.stroke
      ..strokeWidth = 1.5;

    // X-axis
    final yZero = height - (0 - minY) * yScale;
    if (yZero >= 0 && yZero <= height) {
      canvas.drawLine(
        Offset(0, yZero),
        Offset(width, yZero),
        axisPaint,
      );
    }

    // Y-axis
    final xZero = (0 - minX) * xScale;
    if (xZero >= 0 && xZero <= width) {
      canvas.drawLine(
        Offset(xZero, 0),
        Offset(xZero, height),
        axisPaint,
      );
    }
  }

  // Draw PDF curve
  void _drawPDF(Canvas canvas, Size size, double xScale, double yScale) {
    final height = size.height;

    final pdfPaint = Paint()
      ..color = pdfColor
      ..style = PaintingStyle.stroke
      ..strokeWidth = 2.5;

    final path = Path();
    bool isFirstPoint = true;

    // For discrete distributions, we'll draw bars instead of a curve
    final isDiscrete = distribution.type == 'binomial' || distribution.type == 'poisson';

    if (isDiscrete) {
      // Draw bars for discrete distributions
      final barPaint = Paint()
        ..color = pdfColor.withAlpha(150)
        ..style = PaintingStyle.fill;

      final barBorderPaint = Paint()
        ..color = pdfColor
        ..style = PaintingStyle.stroke
        ..strokeWidth = 1.0;

      final domainRange = distribution.getDomainRange();
      final start = domainRange[0].ceil();
      final end = domainRange[1].floor();

      for (int k = start; k <= end; k++) {
        final x = k.toDouble();
        final y = distribution.calculatePDF(x);

        final xPos = (x - minX) * xScale;
        final yPos = height - (y - minY) * yScale;
        final barWidth = xScale * 0.8;

        // Draw bar
        final barRect = Rect.fromCenter(
          center: Offset(xPos, (height + yPos) / 2),
          width: barWidth,
          height: height - yPos,
        );

        canvas.drawRect(barRect, barPaint);
        canvas.drawRect(barRect, barBorderPaint);
      }
    } else {
      // Draw curve for continuous distributions
      final step = (maxX - minX) / numPoints;

      for (int i = 0; i <= numPoints; i++) {
        final x = minX + i * step;
        final y = distribution.calculatePDF(x);

        final xPos = (x - minX) * xScale;
        final yPos = height - (y - minY) * yScale;

        if (isFirstPoint) {
          path.moveTo(xPos, yPos);
          isFirstPoint = false;
        } else {
          path.lineTo(xPos, yPos);
        }
      }

      canvas.drawPath(path, pdfPaint);
    }
  }

  // Draw CDF curve
  void _drawCDF(Canvas canvas, Size size, double xScale, double yScale) {
    final height = size.height;

    final cdfPaint = Paint()
      ..color = cdfColor
      ..style = PaintingStyle.stroke
      ..strokeWidth = 2.5;

    final path = Path();
    bool isFirstPoint = true;

    // For discrete distributions, we'll draw a step function
    final isDiscrete = distribution.type == 'binomial' || distribution.type == 'poisson';

    if (isDiscrete) {
      // Draw step function for discrete distributions
      final domainRange = distribution.getDomainRange();
      final start = domainRange[0].floor();
      final end = domainRange[1].ceil();

      double prevY = 0;

      for (int k = start; k <= end; k++) {
        final x = k.toDouble();
        final y = distribution.calculateCDF(x);

        final xPos = (x - minX) * xScale;
        final yPos = height - (y - minY) * yScale;

        if (isFirstPoint) {
          path.moveTo(xPos, yPos);
          isFirstPoint = false;
        } else {
          // Draw horizontal line from previous point
          path.lineTo(xPos, prevY);
          // Draw vertical line to current point
          path.lineTo(xPos, yPos);
        }

        prevY = yPos;
      }
    } else {
      // Draw curve for continuous distributions
      final step = (maxX - minX) / numPoints;

      for (int i = 0; i <= numPoints; i++) {
        final x = minX + i * step;
        final y = distribution.calculateCDF(x);

        final xPos = (x - minX) * xScale;
        final yPos = height - (y - minY) * yScale;

        if (isFirstPoint) {
          path.moveTo(xPos, yPos);
          isFirstPoint = false;
        } else {
          path.lineTo(xPos, yPos);
        }
      }
    }

    canvas.drawPath(path, cdfPaint);
  }

  // Draw axis labels
  void _drawLabels(Canvas canvas, Size size, double xScale, double yScale) {
    final width = size.width;
    final height = size.height;

    final textStyle = TextStyle(
      color: textColor,
      fontSize: 10,
    );

    // Draw x-axis labels
    final xStep = _calculateStep(maxX - minX);
    for (double x = minX - (minX % xStep); x <= maxX; x += xStep) {
      final xPos = (x - minX) * xScale;

      // Skip if too close to edges
      if (xPos < 20 || xPos > width - 20) continue;

      final textSpan = TextSpan(
        text: x.toStringAsFixed(1),
        style: textStyle,
      );
      final textPainter = TextPainter(
        text: textSpan,
        textDirection: TextDirection.ltr,
      );
      textPainter.layout();
      textPainter.paint(
        canvas,
        Offset(
          xPos - textPainter.width / 2,
          height - textPainter.height,
        ),
      );
    }

    // Draw y-axis labels
    final yStep = _calculateStep(maxY - minY);
    for (double y = minY - (minY % yStep); y <= maxY; y += yStep) {
      final yPos = height - (y - minY) * yScale;

      // Skip if too close to edges
      if (yPos < 20 || yPos > height - 20) continue;

      final textSpan = TextSpan(
        text: y.toStringAsFixed(1),
        style: textStyle,
      );
      final textPainter = TextPainter(
        text: textSpan,
        textDirection: TextDirection.ltr,
      );
      textPainter.layout();
      textPainter.paint(
        canvas,
        Offset(
          5,
          yPos - textPainter.height / 2,
        ),
      );
    }

    // Draw legend
    if (showPDF) {
      final textSpan = TextSpan(
        text: 'PDF',
        style: TextStyle(
          color: pdfColor,
          fontSize: 12,
          fontWeight: FontWeight.bold,
        ),
      );
      final textPainter = TextPainter(
        text: textSpan,
        textDirection: TextDirection.ltr,
      );
      textPainter.layout();
      textPainter.paint(
        canvas,
        Offset(
          width - textPainter.width - 10,
          10,
        ),
      );
    }

    if (showCDF) {
      final textSpan = TextSpan(
        text: 'CDF',
        style: TextStyle(
          color: cdfColor,
          fontSize: 12,
          fontWeight: FontWeight.bold,
        ),
      );
      final textPainter = TextPainter(
        text: textSpan,
        textDirection: TextDirection.ltr,
      );
      textPainter.layout();
      textPainter.paint(
        canvas,
        Offset(
          width - textPainter.width - 10,
          30,
        ),
      );
    }
  }

  // Calculate appropriate step size for grid lines
  double _calculateStep(double range) {
    // Use log base 10 to determine magnitude
    final magnitude = math.pow(10, math.log(range) / math.ln10).floor().toDouble();
    final normalized = range / magnitude;

    if (normalized < 2) {
      return magnitude / 5;
    } else if (normalized < 5) {
      return magnitude / 2;
    } else {
      return magnitude;
    }
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => true;
}

/// A widget that provides an interactive statistical distribution explorer
class InteractiveStatisticalDistributionExplorerWidget extends StatefulWidget {
  /// The data for this widget
  final Map<String, dynamic> data;

  /// Optional callback when the widget state changes
  final Function(bool isCompleted)? onStateChanged;

  const InteractiveStatisticalDistributionExplorerWidget({
    super.key,
    required this.data,
    this.onStateChanged,
  });

  /// Factory constructor to create the widget from JSON data
  factory InteractiveStatisticalDistributionExplorerWidget.fromData(
      Map<String, dynamic> data) {
    return InteractiveStatisticalDistributionExplorerWidget(
      data: data,
    );
  }

  @override
  State<InteractiveStatisticalDistributionExplorerWidget> createState() =>
      _InteractiveStatisticalDistributionExplorerWidgetState();
}

/// Statistical distribution model
class Distribution {
  final String type;
  final String name;
  final String formula;
  final String description;
  final List<double> parameters;
  final List<String> parameterNames;
  final List<double> parameterMinValues;
  final List<double> parameterMaxValues;
  final List<double> parameterSteps;
  final Color color;

  Distribution({
    required this.type,
    required this.name,
    required this.formula,
    required this.description,
    required this.parameters,
    required this.parameterNames,
    required this.parameterMinValues,
    required this.parameterMaxValues,
    required this.parameterSteps,
    required this.color,
  });

  /// Create a copy of the distribution with new parameters
  Distribution copyWith({List<double>? parameters}) {
    return Distribution(
      type: type,
      name: name,
      formula: formula,
      description: description,
      parameters: parameters ?? this.parameters,
      parameterNames: parameterNames,
      parameterMinValues: parameterMinValues,
      parameterMaxValues: parameterMaxValues,
      parameterSteps: parameterSteps,
      color: color,
    );
  }

  /// Calculate the probability density function (PDF) value at x
  double calculatePDF(double x) {
    switch (type) {
      case 'normal':
        final mean = parameters[0];
        final stdDev = parameters[1];
        final exponent = -0.5 * math.pow((x - mean) / stdDev, 2);
        return (1 / (stdDev * math.sqrt(2 * math.pi))) * math.exp(exponent);

      case 'uniform':
        final a = parameters[0];
        final b = parameters[1];
        if (x >= a && x <= b) {
          return 1 / (b - a);
        }
        return 0;

      case 'exponential':
        final lambda = parameters[0];
        if (x >= 0) {
          return lambda * math.exp(-lambda * x);
        }
        return 0;

      case 'binomial':
        final n = parameters[0].toInt();
        final p = parameters[1];
        // For binomial, we'll show PMF (Probability Mass Function) instead of PDF
        if (x.toInt() == x && x >= 0 && x <= n) {
          final k = x.toInt();
          return _binomialPMF(n, k, p);
        }
        return 0;

      case 'poisson':
        final lambda = parameters[0];
        // For poisson, we'll show PMF (Probability Mass Function) instead of PDF
        if (x.toInt() == x && x >= 0) {
          final k = x.toInt();
          return _poissonPMF(lambda, k);
        }
        return 0;

      default:
        return 0;
    }
  }

  /// Calculate the cumulative distribution function (CDF) value at x
  double calculateCDF(double x) {
    switch (type) {
      case 'normal':
        final mean = parameters[0];
        final stdDev = parameters[1];
        return 0.5 * (1 + _erf((x - mean) / (stdDev * math.sqrt(2))));

      case 'uniform':
        final a = parameters[0];
        final b = parameters[1];
        if (x < a) return 0;
        if (x > b) return 1;
        return (x - a) / (b - a);

      case 'exponential':
        final lambda = parameters[0];
        if (x < 0) return 0;
        return 1 - math.exp(-lambda * x);

      case 'binomial':
        final n = parameters[0].toInt();
        final p = parameters[1];
        // For binomial, we'll calculate the sum of PMF up to x
        if (x < 0) return 0;
        if (x >= n) return 1;

        double sum = 0;
        for (int k = 0; k <= x.toInt(); k++) {
          sum += _binomialPMF(n, k, p);
        }
        return sum;

      case 'poisson':
        final lambda = parameters[0];
        // For poisson, we'll calculate the sum of PMF up to x
        if (x < 0) return 0;

        double sum = 0;
        for (int k = 0; k <= x.toInt(); k++) {
          sum += _poissonPMF(lambda, k);
        }
        return sum;

      default:
        return 0;
    }
  }

  /// Calculate binomial PMF: P(X = k) = (n choose k) * p^k * (1-p)^(n-k)
  double _binomialPMF(int n, int k, double p) {
    if (k < 0 || k > n) return 0;
    return _combinations(n, k) * math.pow(p, k) * math.pow(1 - p, n - k);
  }

  /// Calculate poisson PMF: P(X = k) = (lambda^k * e^(-lambda)) / k!
  double _poissonPMF(double lambda, int k) {
    if (k < 0) return 0;
    return (math.pow(lambda, k) * math.exp(-lambda)) / _factorial(k);
  }

  /// Calculate n choose k: n! / (k! * (n-k)!)
  double _combinations(int n, int k) {
    return _factorial(n) / (_factorial(k) * _factorial(n - k));
  }

  /// Calculate factorial
  double _factorial(int n) {
    if (n <= 1) return 1;
    double result = 1;
    for (int i = 2; i <= n; i++) {
      result *= i;
    }
    return result;
  }

  /// Error function approximation
  double _erf(double x) {
    // Constants
    const a1 = 0.254829592;
    const a2 = -0.284496736;
    const a3 = 1.421413741;
    const a4 = -1.453152027;
    const a5 = 1.061405429;
    const p = 0.3275911;

    // Save the sign of x
    int sign = (x < 0) ? -1 : 1;
    x = x.abs();

    // A&S formula 7.1.26
    final t = 1.0 / (1.0 + p * x);
    final y = 1.0 - (((((a5 * t + a4) * t) + a3) * t + a2) * t + a1) * t * math.exp(-x * x);

    return sign * y;
  }

  /// Get the domain range for plotting
  List<double> getDomainRange() {
    switch (type) {
      case 'normal':
        final mean = parameters[0];
        final stdDev = parameters[1];
        return [mean - 4 * stdDev, mean + 4 * stdDev];

      case 'uniform':
        final a = parameters[0];
        final b = parameters[1];
        final padding = (b - a) * 0.2;
        return [a - padding, b + padding];

      case 'exponential':
        final lambda = parameters[0];
        return [0, 5 / lambda];

      case 'binomial':
        final n = parameters[0].toInt();
        return [0, n.toDouble()];

      case 'poisson':
        final lambda = parameters[0];
        return [0, lambda * 3];

      default:
        return [0, 10];
    }
  }

  /// Get the range for plotting
  List<double> getRangeForPDF() {
    switch (type) {
      case 'normal':
        final stdDev = parameters[1];
        return [0, 1 / (stdDev * math.sqrt(2 * math.pi)) * 1.2];

      case 'uniform':
        final a = parameters[0];
        final b = parameters[1];
        return [0, 1 / (b - a) * 1.5];

      case 'exponential':
        final lambda = parameters[0];
        return [0, lambda * 1.5];

      case 'binomial':
        final n = parameters[0].toInt();
        final p = parameters[1];
        // Approximate max PMF value
        final mode = ((n + 1) * p).floor();
        final maxPMF = _binomialPMF(n, mode, p) * 1.5;
        return [0, maxPMF];

      case 'poisson':
        final lambda = parameters[0];
        // Approximate max PMF value
        final mode = lambda.floor();
        final maxPMF = _poissonPMF(lambda, mode) * 1.5;
        return [0, maxPMF];

      default:
        return [0, 1];
    }
  }

  /// Get the range for plotting CDF
  List<double> getRangeForCDF() {
    return [0, 1.1]; // CDF ranges from 0 to 1
  }
}

class _InteractiveStatisticalDistributionExplorerWidgetState
    extends State<InteractiveStatisticalDistributionExplorerWidget> {
  // Colors
  late Color _primaryColor;
  late Color _textColor;
  late Color _backgroundColor;

  // Distributions
  late List<Distribution> _distributions;
  late int _currentDistributionIndex;

  // UI state
  bool _showPDF = true;
  bool _showCDF = true;
  bool _showDescription = true;
  bool _showFormula = true;

  // Plot state
  late double _minX;
  late double _maxX;
  late double _minY;
  late double _maxY;
  late int _numPoints;

  @override
  void initState() {
    super.initState();

    // Initialize colors
    _primaryColor = _getColorFromHex(
        widget.data['primaryColor'] ?? '#2196F3'); // Blue
    _textColor =
        _getColorFromHex(widget.data['textColor'] ?? '#212121'); // Dark Grey
    _backgroundColor = _getColorFromHex(
        widget.data['backgroundColor'] ?? '#FFFFFF'); // White

    // Initialize distributions
    _distributions = _createDistributions();
    _currentDistributionIndex = 0;

    // Initialize plot state
    _numPoints = 100;
    _updatePlotRange();
  }

  // Convert hex color string to Color
  Color _getColorFromHex(String hexColor) {
    hexColor = hexColor.replaceAll('#', '');
    if (hexColor.length == 6) {
      hexColor = 'FF$hexColor';
    }
    return Color(int.parse(hexColor, radix: 16));
  }

  // Create predefined distributions
  List<Distribution> _createDistributions() {
    return [
      Distribution(
        type: 'normal',
        name: 'Normal Distribution',
        formula: 'f(x) = \\frac{1}{\\sigma\\sqrt{2\\pi}} e^{-\\frac{1}{2}(\\frac{x-\\mu}{\\sigma})^2}',
        description:
            'The normal distribution is a continuous probability distribution that is symmetric about the mean, '
            'showing that data near the mean are more frequent in occurrence than data far from the mean. '
            'It is characterized by its mean (μ) and standard deviation (σ).',
        parameters: [0, 1], // mean, stdDev
        parameterNames: ['Mean (μ)', 'Standard Deviation (σ)'],
        parameterMinValues: [-10, 0.1],
        parameterMaxValues: [10, 5],
        parameterSteps: [0.1, 0.1],
        color: Colors.blue,
      ),
      Distribution(
        type: 'uniform',
        name: 'Uniform Distribution',
        formula: 'f(x) = \\frac{1}{b-a} \\text{ for } a \\leq x \\leq b',
        description:
            'The uniform distribution is a continuous probability distribution where all outcomes in the range are equally likely. '
            'It is characterized by its minimum (a) and maximum (b) values.',
        parameters: [0, 1], // a, b
        parameterNames: ['Minimum (a)', 'Maximum (b)'],
        parameterMinValues: [-10, -9],
        parameterMaxValues: [9, 10],
        parameterSteps: [0.1, 0.1],
        color: Colors.green,
      ),
      Distribution(
        type: 'exponential',
        name: 'Exponential Distribution',
        formula: 'f(x) = \\lambda e^{-\\lambda x} \\text{ for } x \\geq 0',
        description:
            'The exponential distribution is a continuous probability distribution that describes the time between events in a Poisson process. '
            'It is characterized by its rate parameter (λ).',
        parameters: [1], // lambda
        parameterNames: ['Rate (λ)'],
        parameterMinValues: [0.1],
        parameterMaxValues: [5],
        parameterSteps: [0.1],
        color: Colors.red,
      ),
      Distribution(
        type: 'binomial',
        name: 'Binomial Distribution',
        formula: 'P(X = k) = {n \\choose k} p^k (1-p)^{n-k}',
        description:
            'The binomial distribution is a discrete probability distribution that models the number of successes in a fixed number of independent trials. '
            'It is characterized by the number of trials (n) and the probability of success (p) in each trial.',
        parameters: [10, 0.5], // n, p
        parameterNames: ['Number of Trials (n)', 'Probability of Success (p)'],
        parameterMinValues: [1, 0],
        parameterMaxValues: [30, 1],
        parameterSteps: [1, 0.01],
        color: Colors.purple,
      ),
      Distribution(
        type: 'poisson',
        name: 'Poisson Distribution',
        formula: 'P(X = k) = \\frac{\\lambda^k e^{-\\lambda}}{k!}',
        description:
            'The Poisson distribution is a discrete probability distribution that expresses the probability of a given number of events occurring in a fixed interval of time or space. '
            'It is characterized by its rate parameter (λ), which is the expected number of occurrences.',
        parameters: [5], // lambda
        parameterNames: ['Rate (λ)'],
        parameterMinValues: [0.1],
        parameterMaxValues: [20],
        parameterSteps: [0.1],
        color: Colors.orange,
      ),
    ];
  }

  // Update parameter value
  void _updateParameter(int paramIndex, double value) {
    setState(() {
      final currentDist = _distributions[_currentDistributionIndex];
      final newParams = List<double>.from(currentDist.parameters);
      newParams[paramIndex] = value;

      // Ensure a < b for uniform distribution
      if (currentDist.type == 'uniform' && paramIndex == 0 && value >= newParams[1]) {
        newParams[1] = value + 1;
      } else if (currentDist.type == 'uniform' && paramIndex == 1 && value <= newParams[0]) {
        newParams[0] = value - 1;
      }

      _distributions[_currentDistributionIndex] = currentDist.copyWith(parameters: newParams);
      _updatePlotRange();
    });
  }

  // Update plot range based on current distribution
  void _updatePlotRange() {
    final currentDist = _distributions[_currentDistributionIndex];
    final domainRange = currentDist.getDomainRange();
    _minX = domainRange[0];
    _maxX = domainRange[1];

    if (_showPDF && !_showCDF) {
      final pdfRange = currentDist.getRangeForPDF();
      _minY = pdfRange[0];
      _maxY = pdfRange[1];
    } else if (!_showPDF && _showCDF) {
      final cdfRange = currentDist.getRangeForCDF();
      _minY = cdfRange[0];
      _maxY = cdfRange[1];
    } else {
      // Show both or none
      final pdfRange = currentDist.getRangeForPDF();
      final cdfRange = currentDist.getRangeForCDF();
      _minY = 0;
      _maxY = math.max(pdfRange[1], cdfRange[1]);
    }
  }

  // Build distribution selector
  Widget _buildDistributionSelector() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Select Distribution',
          style: TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.bold,
            color: _textColor,
          ),
        ),
        const SizedBox(height: 8),
        SingleChildScrollView(
          scrollDirection: Axis.horizontal,
          child: Row(
            children: List.generate(_distributions.length, (index) {
              final isSelected = index == _currentDistributionIndex;
              return Padding(
                padding: const EdgeInsets.only(right: 8),
                child: ChoiceChip(
                  label: Text(_distributions[index].name),
                  selected: isSelected,
                  onSelected: (selected) {
                    if (selected) {
                      setState(() {
                        _currentDistributionIndex = index;
                        _updatePlotRange();
                      });
                    }
                  },
                  backgroundColor: Colors.grey.withAlpha(50),
                  selectedColor: _distributions[index].color.withAlpha(100),
                ),
              );
            }),
          ),
        ),
      ],
    );
  }

  // Build parameter controls
  Widget _buildParameterControls(Distribution dist) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Parameters',
          style: TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.bold,
            color: _textColor,
          ),
        ),
        const SizedBox(height: 8),
        ...List.generate(dist.parameters.length, (index) {
          return Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                children: [
                  Text(
                    '${dist.parameterNames[index]}: ',
                    style: TextStyle(
                      color: _textColor,
                    ),
                  ),
                  Text(
                    dist.parameters[index].toStringAsFixed(
                      dist.parameterSteps[index] < 1 ? 2 : 0,
                    ),
                    style: TextStyle(
                      fontWeight: FontWeight.bold,
                      color: dist.color,
                    ),
                  ),
                ],
              ),
              Slider(
                value: dist.parameters[index],
                min: dist.parameterMinValues[index],
                max: dist.parameterMaxValues[index],
                divisions: ((dist.parameterMaxValues[index] - dist.parameterMinValues[index]) /
                           dist.parameterSteps[index]).round(),
                label: dist.parameters[index].toStringAsFixed(
                  dist.parameterSteps[index] < 1 ? 2 : 0,
                ),
                onChanged: (value) => _updateParameter(index, value),
                activeColor: dist.color,
              ),
              const SizedBox(height: 8),
            ],
          );
        }),
      ],
    );
  }

  // Build plot controls
  Widget _buildPlotControls() {
    return Row(
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        // Toggle PDF button
        OutlinedButton.icon(
          onPressed: () {
            setState(() {
              _showPDF = !_showPDF;
              _updatePlotRange();
            });
          },
          icon: Icon(_showPDF ? Icons.visibility_off : Icons.visibility),
          label: const Text('PDF'),
          style: OutlinedButton.styleFrom(
            foregroundColor: _showPDF ? _primaryColor : Colors.grey,
            side: BorderSide(
              color: _showPDF ? _primaryColor : Colors.grey,
            ),
          ),
        ),
        const SizedBox(width: 16),
        // Toggle CDF button
        OutlinedButton.icon(
          onPressed: () {
            setState(() {
              _showCDF = !_showCDF;
              _updatePlotRange();
            });
          },
          icon: Icon(_showCDF ? Icons.visibility_off : Icons.visibility),
          label: const Text('CDF'),
          style: OutlinedButton.styleFrom(
            foregroundColor: _showCDF ? _primaryColor : Colors.grey,
            side: BorderSide(
              color: _showCDF ? _primaryColor : Colors.grey,
            ),
          ),
        ),
        const SizedBox(width: 16),
        // Toggle formula button
        OutlinedButton.icon(
          onPressed: () {
            setState(() {
              _showFormula = !_showFormula;
            });
          },
          icon: Icon(_showFormula ? Icons.visibility_off : Icons.visibility),
          label: const Text('Formula'),
          style: OutlinedButton.styleFrom(
            foregroundColor: _showFormula ? _primaryColor : Colors.grey,
            side: BorderSide(
              color: _showFormula ? _primaryColor : Colors.grey,
            ),
          ),
        ),
        const SizedBox(width: 16),
        // Toggle description button
        OutlinedButton.icon(
          onPressed: () {
            setState(() {
              _showDescription = !_showDescription;
            });
          },
          icon: Icon(_showDescription ? Icons.visibility_off : Icons.visibility),
          label: const Text('Info'),
          style: OutlinedButton.styleFrom(
            foregroundColor: _showDescription ? _primaryColor : Colors.grey,
            side: BorderSide(
              color: _showDescription ? _primaryColor : Colors.grey,
            ),
          ),
        ),
      ],
    );
  }

  // Build distribution plot
  Widget _buildDistributionPlot(Distribution dist) {
    return Container(
      height: 250,
      width: double.infinity,
      decoration: BoxDecoration(
        color: _backgroundColor,
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: Colors.grey.withAlpha(100)),
      ),
      child: ClipRRect(
        borderRadius: BorderRadius.circular(8),
        child: CustomPaint(
          painter: DistributionPainter(
            distribution: dist,
            showPDF: _showPDF,
            showCDF: _showCDF,
            minX: _minX,
            maxX: _maxX,
            minY: _minY,
            maxY: _maxY,
            numPoints: _numPoints,
            gridColor: Colors.grey.withAlpha(50),
            axisColor: Colors.grey,
            pdfColor: dist.color,
            cdfColor: dist.color.withAlpha(150),
            textColor: _textColor,
          ),
          size: const Size(double.infinity, 250),
        ),
      ),
    );
  }

  // Build formula display
  Widget _buildFormula(Distribution dist) {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: Colors.grey.withAlpha(20),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: Colors.grey.withAlpha(100)),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Formula',
            style: TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.bold,
              color: _textColor,
            ),
          ),
          const SizedBox(height: 8),
          // Display formula (in a real app, use a LaTeX renderer)
          Text(
            dist.formula,
            style: TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.bold,
              color: dist.color,
              fontFamily: 'monospace',
            ),
          ),
        ],
      ),
    );
  }

  // Build distribution information
  Widget _buildDistributionInfo(Distribution dist) {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: dist.color.withAlpha(20),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: dist.color.withAlpha(100)),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            dist.name,
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
              color: _textColor,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            dist.description,
            style: TextStyle(
              color: _textColor,
              fontSize: 14,
            ),
          ),
          const SizedBox(height: 8),
          Align(
            alignment: Alignment.centerRight,
            child: TextButton(
              onPressed: () {
                widget.onStateChanged?.call(true);
              },
              child: const Text('Mark as Completed'),
            ),
          ),
        ],
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    final currentDist = _distributions[_currentDistributionIndex];

    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
        side: BorderSide(color: _primaryColor.withAlpha(77)),
      ),
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Title and description
            Text(
              widget.data['title'] ?? 'Statistical Distribution Explorer',
              style: TextStyle(
                fontSize: 20,
                fontWeight: FontWeight.bold,
                color: _textColor,
              ),
            ),
            const SizedBox(height: 8),
            Text(
              widget.data['description'] ??
                  'Explore statistical distributions and their properties',
              style: TextStyle(
                fontSize: 14,
                color: _textColor.withAlpha(179),
              ),
            ),
            const SizedBox(height: 16),

            // Distribution selector
            _buildDistributionSelector(),

            const SizedBox(height: 16),

            // Parameter controls
            _buildParameterControls(currentDist),

            const SizedBox(height: 16),

            // Plot controls
            _buildPlotControls(),

            const SizedBox(height: 16),

            // Distribution plot
            _buildDistributionPlot(currentDist),

            const SizedBox(height: 16),

            // Formula
            if (_showFormula) _buildFormula(currentDist),

            if (_showFormula) const SizedBox(height: 16),

            // Distribution information
            if (_showDescription) _buildDistributionInfo(currentDist),
          ],
        ),
      ),
    );
  }
}
