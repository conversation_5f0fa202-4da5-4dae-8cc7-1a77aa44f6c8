import 'package:flutter/material.dart';
import 'dart:math' as math;

/// A widget that presents pattern prediction puzzles for testing pattern recognition skills
class InteractivePatternPredictionPuzzleWidget extends StatefulWidget {
  /// The data for this widget
  final Map<String, dynamic> data;

  /// Optional callback when the widget state changes
  final Function(bool isCompleted)? onStateChanged;

  const InteractivePatternPredictionPuzzleWidget({
    Key? key,
    required this.data,
    this.onStateChanged,
  }) : super(key: key);

  /// Factory constructor to create the widget from JSON data
  factory InteractivePatternPredictionPuzzleWidget.fromData(Map<String, dynamic> data) {
    return InteractivePatternPredictionPuzzleWidget(
      data: data,
    );
  }

  @override
  State<InteractivePatternPredictionPuzzleWidget> createState() => _InteractivePatternPredictionPuzzleWidgetState();
}

class _InteractivePatternPredictionPuzzleWidgetState extends State<InteractivePatternPredictionPuzzleWidget> with SingleTickerProviderStateMixin {
  // State variables
  late String _title;
  late String _description;
  late String _puzzleType;
  late List<dynamic> _patternElements;
  late String _correctAnswer;
  late String _correctFeedback;
  late String _incorrectFeedback;
  late Color _primaryColor;
  late Color _secondaryColor;
  late Color _accentColor;
  late Color _textColor;
  late bool _showNameTag;
  
  String? _userAnswer;
  bool _hasSubmitted = false;
  bool _isCorrect = false;
  String _feedback = '';
  int _attempts = 0;
  int _maxAttempts = 3;
  
  // Animation controller for visual effects
  late AnimationController _animationController;
  late Animation<double> _animation;
  
  @override
  void initState() {
    super.initState();
    
    // Initialize from data
    _title = widget.data['title'] ?? 'Pattern Prediction Puzzle';
    _description = widget.data['description'] ?? 'Predict the next element in the pattern.';
    _puzzleType = widget.data['puzzleType'] ?? 'number_sequence';
    _patternElements = widget.data['patternElements'] ?? [];
    _correctAnswer = widget.data['correctAnswer'] ?? '';
    _correctFeedback = widget.data['correctFeedback'] ?? 'Correct! You found the pattern.';
    _incorrectFeedback = widget.data['incorrectFeedback'] ?? 'Not quite right. Try again!';
    _maxAttempts = widget.data['maxAttempts'] ?? 3;
    _showNameTag = widget.data['showNameTag'] ?? true;
    
    // Parse colors
    _primaryColor = _parseColor(widget.data['primaryColor'] ?? '#2196F3');
    _secondaryColor = _parseColor(widget.data['secondaryColor'] ?? '#4CAF50');
    _accentColor = _parseColor(widget.data['accentColor'] ?? '#FF9800');
    _textColor = _parseColor(widget.data['textColor'] ?? '#212121');
    
    // Initialize animation controller
    _animationController = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 800),
    );
    
    _animation = CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeInOut,
    );
  }
  
  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }
  
  // Helper method to parse color from hex string
  Color _parseColor(String colorString) {
    if (colorString.startsWith('#')) {
      return Color(int.parse(colorString.substring(1, 7), radix: 16) + 0xFF000000);
    }
    return Colors.blue;
  }
  
  // Submit the user's answer
  void _submitAnswer() {
    if (_userAnswer == null || _userAnswer!.isEmpty) return;
    
    setState(() {
      _hasSubmitted = true;
      _attempts++;
      
      // Check if the answer is correct
      _isCorrect = _userAnswer!.trim() == _correctAnswer.trim();
      _feedback = _isCorrect ? _correctFeedback : _incorrectFeedback;
      
      if (_isCorrect) {
        _animationController.forward();
        if (widget.onStateChanged != null) {
          widget.onStateChanged!(true);
        }
      } else if (_attempts >= _maxAttempts) {
        _feedback = '$_incorrectFeedback\nThe correct answer is: $_correctAnswer';
      }
    });
  }
  
  // Reset the puzzle for another attempt
  void _resetPuzzle() {
    setState(() {
      _hasSubmitted = false;
      _userAnswer = null;
      _feedback = '';
    });
  }
  
  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withOpacity(0.2),
            spreadRadius: 1,
            blurRadius: 3,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Title
          Text(
            _title,
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
              color: _textColor,
            ),
          ),
          const SizedBox(height: 12),
          
          // Description
          Text(
            _description,
            style: TextStyle(
              fontSize: 16,
              color: _textColor,
            ),
          ),
          const SizedBox(height: 16),
          
          // Pattern visualization
          _buildPatternVisualization(),
          const SizedBox(height: 16),
          
          // Answer input
          if (!_hasSubmitted || (_hasSubmitted && !_isCorrect && _attempts < _maxAttempts))
            TextField(
              decoration: InputDecoration(
                hintText: 'Enter your prediction',
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(8),
                ),
                enabled: !_hasSubmitted || (_hasSubmitted && !_isCorrect && _attempts < _maxAttempts),
              ),
              onChanged: (value) {
                setState(() {
                  _userAnswer = value;
                });
              },
            ),
          
          // Feedback
          if (_hasSubmitted)
            Padding(
              padding: const EdgeInsets.symmetric(vertical: 12),
              child: Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: _isCorrect ? _secondaryColor.withOpacity(0.2) : Colors.red.withOpacity(0.2),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Text(
                  _feedback,
                  style: TextStyle(
                    color: _isCorrect ? _secondaryColor : Colors.red,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ),
            ),
          
          const SizedBox(height: 16),
          
          // Buttons
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              if (_hasSubmitted && !_isCorrect && _attempts < _maxAttempts)
                ElevatedButton(
                  onPressed: _resetPuzzle,
                  style: ElevatedButton.styleFrom(
                    backgroundColor: _primaryColor,
                  ),
                  child: const Text('Try Again'),
                ),
              const Spacer(),
              ElevatedButton(
                onPressed: (_userAnswer == null || _userAnswer!.isEmpty) || _hasSubmitted
                    ? null
                    : _submitAnswer,
                style: ElevatedButton.styleFrom(
                  backgroundColor: _accentColor,
                ),
                child: Text(_hasSubmitted ? 'Submitted' : 'Submit Answer'),
              ),
            ],
          ),
          
          // Widget name tag
          if (_showNameTag)
            Padding(
              padding: const EdgeInsets.only(top: 8),
              child: Text(
                'InteractivePatternPredictionPuzzleWidget',
                style: TextStyle(
                  color: Colors.grey[600],
                  fontSize: 12,
                  fontStyle: FontStyle.italic,
                ),
              ),
            ),
        ],
      ),
    );
  }
  
  // Build the pattern visualization based on the puzzle type
  Widget _buildPatternVisualization() {
    switch (_puzzleType) {
      case 'number_sequence':
        return _buildNumberSequence();
      case 'shape_sequence':
        return _buildShapeSequence();
      case 'visual_pattern':
        return _buildVisualPattern();
      default:
        return _buildNumberSequence();
    }
  }
  
  // Build a number sequence visualization
  Widget _buildNumberSequence() {
    return Container(
      height: 80,
      decoration: BoxDecoration(
        color: Colors.grey.withOpacity(0.1),
        borderRadius: BorderRadius.circular(8),
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceEvenly,
        children: [
          ..._patternElements.map((element) => _buildNumberElement(element.toString())),
          _buildQuestionMark(),
        ],
      ),
    );
  }
  
  // Build a shape sequence visualization
  Widget _buildShapeSequence() {
    return Container(
      height: 100,
      decoration: BoxDecoration(
        color: Colors.grey.withOpacity(0.1),
        borderRadius: BorderRadius.circular(8),
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceEvenly,
        children: [
          ..._patternElements.map((element) => _buildShapeElement(element.toString())),
          _buildQuestionMark(),
        ],
      ),
    );
  }
  
  // Build a visual pattern (grid-based) visualization
  Widget _buildVisualPattern() {
    return Container(
      height: 150,
      decoration: BoxDecoration(
        color: Colors.grey.withOpacity(0.1),
        borderRadius: BorderRadius.circular(8),
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceEvenly,
        children: [
          ..._patternElements.map((element) => _buildVisualPatternElement(element)),
          _buildQuestionMark(),
        ],
      ),
    );
  }
  
  // Build a number element for the sequence
  Widget _buildNumberElement(String number) {
    return Container(
      width: 50,
      height: 50,
      decoration: BoxDecoration(
        color: _primaryColor,
        shape: BoxShape.circle,
      ),
      child: Center(
        child: Text(
          number,
          style: const TextStyle(
            color: Colors.white,
            fontWeight: FontWeight.bold,
            fontSize: 18,
          ),
        ),
      ),
    );
  }
  
  // Build a shape element for the sequence
  Widget _buildShapeElement(String shape) {
    IconData iconData;
    switch (shape) {
      case 'circle':
        iconData = Icons.circle;
        break;
      case 'square':
        iconData = Icons.square;
        break;
      case 'triangle':
        iconData = Icons.change_history;
        break;
      case 'star':
        iconData = Icons.star;
        break;
      default:
        iconData = Icons.help;
    }
    
    return Icon(
      iconData,
      size: 50,
      color: _primaryColor,
    );
  }
  
  // Build a visual pattern element (grid of dots)
  Widget _buildVisualPatternElement(dynamic pattern) {
    if (pattern is! List) return const SizedBox(width: 60, height: 60);
    
    return Container(
      width: 60,
      height: 60,
      padding: const EdgeInsets.all(4),
      decoration: BoxDecoration(
        border: Border.all(color: Colors.grey),
        borderRadius: BorderRadius.circular(4),
      ),
      child: CustomPaint(
        painter: DotPatternPainter(
          pattern: List<List<bool>>.from(pattern.map((row) => 
            List<bool>.from(row.map((cell) => cell == 1))
          )),
          dotColor: _primaryColor,
        ),
      ),
    );
  }
  
  // Build the question mark for the missing element
  Widget _buildQuestionMark() {
    return Container(
      width: 50,
      height: 50,
      decoration: BoxDecoration(
        color: _accentColor,
        shape: BoxShape.circle,
      ),
      child: const Center(
        child: Text(
          '?',
          style: TextStyle(
            color: Colors.white,
            fontWeight: FontWeight.bold,
            fontSize: 24,
          ),
        ),
      ),
    );
  }
}

// Custom painter for dot patterns
class DotPatternPainter extends CustomPainter {
  final List<List<bool>> pattern;
  final Color dotColor;
  
  DotPatternPainter({
    required this.pattern,
    required this.dotColor,
  });
  
  @override
  void paint(Canvas canvas, Size size) {
    final paint = Paint()
      ..color = dotColor
      ..style = PaintingStyle.fill;
    
    final rows = pattern.length;
    final cols = pattern.isNotEmpty ? pattern[0].length : 0;
    
    if (rows == 0 || cols == 0) return;
    
    final dotSize = math.min(size.width / cols, size.height / rows) * 0.7;
    final xSpacing = size.width / cols;
    final ySpacing = size.height / rows;
    
    for (int row = 0; row < rows; row++) {
      for (int col = 0; col < cols; col++) {
        if (pattern[row][col]) {
          final x = col * xSpacing + xSpacing / 2;
          final y = row * ySpacing + ySpacing / 2;
          canvas.drawCircle(Offset(x, y), dotSize / 2, paint);
        }
      }
    }
  }
  
  @override
  bool shouldRepaint(covariant DotPatternPainter oldDelegate) {
    return oldDelegate.pattern != pattern || oldDelegate.dotColor != dotColor;
  }
}
