{"buildFiles": ["C:\\flutter\\packages\\flutter_tools\\gradle\\src\\main\\groovy\\CMakeLists.txt"], "cleanCommandsComponents": [["D:\\AndroidSDK\\cmake\\3.22.1\\bin\\ninja.exe", "-C", "D:\\AResonance\\rn\\android\\app\\.cxx\\Debug\\5e5t3o49\\x86_64", "clean"]], "buildTargetsCommandComponents": ["D:\\AndroidSDK\\cmake\\3.22.1\\bin\\ninja.exe", "-C", "D:\\AResonance\\rn\\android\\app\\.cxx\\Debug\\5e5t3o49\\x86_64", "{LIST_OF_TARGETS_TO_BUILD}"], "libraries": {}}