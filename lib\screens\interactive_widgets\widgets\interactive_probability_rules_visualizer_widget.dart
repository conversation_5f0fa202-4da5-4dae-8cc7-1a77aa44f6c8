import 'package:flutter/material.dart';
import 'dart:math' as math;

/// A widget that visualizes probability rules (addition and multiplication)
class InteractiveProbabilityRulesVisualizerWidget extends StatefulWidget {
  /// The data for this widget
  final Map<String, dynamic> data;

  /// Optional callback when the widget state changes
  final Function(bool isCompleted)? onStateChanged;

  const InteractiveProbabilityRulesVisualizerWidget({
    Key? key,
    required this.data,
    this.onStateChanged,
  }) : super(key: key);

  /// Factory constructor to create the widget from JSON data
  factory InteractiveProbabilityRulesVisualizerWidget.fromData(Map<String, dynamic> data) {
    return InteractiveProbabilityRulesVisualizerWidget(
      data: data,
    );
  }

  @override
  _InteractiveProbabilityRulesVisualizerWidgetState createState() =>
      _InteractiveProbabilityRulesVisualizerWidgetState();
}

class _InteractiveProbabilityRulesVisualizerWidgetState extends State<InteractiveProbabilityRulesVisualizerWidget> {
  // Rule selection
  late String _selectedRule;
  late List<String> _availableRules;

  // Event parameters
  double _probabilityA = 0.3;
  double _probabilityB = 0.4;
  double _probabilityAandB = 0.1;
  bool _mutuallyExclusive = false;
  bool _independent = false;

  // Calculation results
  double _probabilityAorB = 0.0;
  double _probabilityBgivenA = 0.0;

  // Colors
  late Color _primaryColor;
  late Color _secondaryColor;
  late Color _accentColor;
  late Color _backgroundColor;
  late Color _textColor;
  late Color _eventAColor;
  late Color _eventBColor;
  late Color _intersectionColor;

  // Predefined examples
  late List<Map<String, dynamic>> _examples;

  // Completion tracking
  bool _isCompleted = false;

  @override
  void initState() {
    super.initState();

    // Initialize colors
    _primaryColor = widget.data['primaryColor'] != null
        ? Color(int.parse(widget.data['primaryColor'], radix: 16))
        : Colors.blue;
    _secondaryColor = widget.data['secondaryColor'] != null
        ? Color(int.parse(widget.data['secondaryColor'], radix: 16))
        : Colors.lightBlue;
    _accentColor = widget.data['accentColor'] != null
        ? Color(int.parse(widget.data['accentColor'], radix: 16))
        : Colors.orange;
    _backgroundColor = widget.data['backgroundColor'] != null
        ? Color(int.parse(widget.data['backgroundColor'], radix: 16))
        : Colors.white;
    _textColor = widget.data['textColor'] != null
        ? Color(int.parse(widget.data['textColor'], radix: 16))
        : Colors.black87;

    // Set event colors
    _eventAColor = Colors.red.withOpacity(0.5);
    _eventBColor = Colors.blue.withOpacity(0.5);
    _intersectionColor = Colors.purple.withOpacity(0.7);

    // Initialize rule types
    _availableRules = widget.data['ruleTypes'] != null
        ? List<String>.from(widget.data['ruleTypes'])
        : ['addition', 'multiplication'];
    _selectedRule = _availableRules.first;

    // Initialize examples
    _examples = widget.data['examples'] != null
        ? List<Map<String, dynamic>>.from(widget.data['examples'])
        : _getDefaultExamples();

    // Calculate initial probabilities
    _calculateProbabilities();
  }

  // Get default examples if none provided
  List<Map<String, dynamic>> _getDefaultExamples() {
    return [
      {
        'title': 'Mutually Exclusive Events',
        'description': 'Events that cannot occur together (e.g., rolling an odd or even number)',
        'rule': 'addition',
        'probabilityA': 0.5,
        'probabilityB': 0.5,
        'probabilityAandB': 0.0,
        'mutuallyExclusive': true,
        'independent': false,
      },
      {
        'title': 'Independent Events',
        'description': 'Events where one does not affect the other (e.g., flipping two coins)',
        'rule': 'multiplication',
        'probabilityA': 0.5,
        'probabilityB': 0.5,
        'probabilityAandB': 0.25,
        'mutuallyExclusive': false,
        'independent': true,
      },
      {
        'title': 'Dependent Events',
        'description': 'Events where one affects the other (e.g., drawing cards without replacement)',
        'rule': 'multiplication',
        'probabilityA': 0.25,
        'probabilityB': 0.25,
        'probabilityAandB': 0.05,
        'mutuallyExclusive': false,
        'independent': false,
      },
      {
        'title': 'Overlapping Events',
        'description': 'Events that can occur together (e.g., drawing a heart or a face card)',
        'rule': 'addition',
        'probabilityA': 0.25,
        'probabilityB': 0.25,
        'probabilityAandB': 0.0625,
        'mutuallyExclusive': false,
        'independent': false,
      },
    ];
  }

  // Load an example
  void _loadExample(Map<String, dynamic> example) {
    setState(() {
      _selectedRule = example['rule'];
      _probabilityA = example['probabilityA'].toDouble();
      _probabilityB = example['probabilityB'].toDouble();
      _probabilityAandB = example['probabilityAandB'].toDouble();
      _mutuallyExclusive = example['mutuallyExclusive'];
      _independent = example['independent'];

      // Calculate probabilities
      _calculateProbabilities();

      // Mark as completed when user interacts with examples
      if (!_isCompleted) {
        _isCompleted = true;
        widget.onStateChanged?.call(true);
      }
    });
  }

  // Calculate probabilities based on rule
  void _calculateProbabilities() {
    // Ensure probabilities are valid
    _validateProbabilities();

    // Calculate P(A or B) using addition rule
    _probabilityAorB = _probabilityA + _probabilityB - _probabilityAandB;

    // Calculate P(B|A) using conditional probability
    _probabilityBgivenA = _probabilityA > 0 ? _probabilityAandB / _probabilityA : 0.0;

    // Update mutual exclusivity and independence
    if (_probabilityAandB == 0) {
      _mutuallyExclusive = true;
    } else {
      _mutuallyExclusive = false;
    }

    if ((_probabilityAandB - _probabilityA * _probabilityB).abs() < 0.0001) {
      _independent = true;
    } else {
      _independent = false;
    }
  }

  // Validate probabilities to ensure they are consistent
  void _validateProbabilities() {
    // Ensure P(A) and P(B) are between 0 and 1
    _probabilityA = _probabilityA.clamp(0.0, 1.0);
    _probabilityB = _probabilityB.clamp(0.0, 1.0);

    // Ensure P(A and B) is not greater than min(P(A), P(B))
    double maxIntersection = math.min(_probabilityA, _probabilityB);
    _probabilityAandB = _probabilityAandB.clamp(0.0, maxIntersection);

    // If mutually exclusive, P(A and B) must be 0
    if (_mutuallyExclusive) {
      _probabilityAandB = 0.0;
    }

    // If independent, P(A and B) must be P(A) * P(B)
    if (_independent) {
      _probabilityAandB = _probabilityA * _probabilityB;
    }
  }

  @override
  Widget build(BuildContext context) {
    return Card(
      elevation: 4,
      margin: const EdgeInsets.all(8),
      color: _backgroundColor,
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Title
            Text(
              widget.data['title'] ?? 'Probability Rules Visualizer',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
                color: _textColor,
              ),
            ),

            const SizedBox(height: 8),

            // Description
            Text(
              widget.data['description'] ?? 'Visualize addition and multiplication rules of probability.',
              style: TextStyle(
                fontSize: 14,
                color: _textColor.withOpacity(0.7),
              ),
            ),

            const SizedBox(height: 16),

            // Rule selection
            Row(
              children: [
                Text(
                  'Probability Rule:',
                  style: TextStyle(
                    fontSize: 14,
                    fontWeight: FontWeight.bold,
                    color: _textColor,
                  ),
                ),
                const SizedBox(width: 16),
                DropdownButton<String>(
                  value: _selectedRule,
                  onChanged: (String? newValue) {
                    if (newValue != null) {
                      setState(() {
                        _selectedRule = newValue;
                      });
                    }
                  },
                  items: _availableRules
                      .map<DropdownMenuItem<String>>((String value) {
                    return DropdownMenuItem<String>(
                      value: value,
                      child: Text(value.capitalize()),
                    );
                  }).toList(),
                ),
              ],
            ),

            const SizedBox(height: 16),

            // Venn diagram visualization
            Container(
              height: 200,
              width: double.infinity,
              decoration: BoxDecoration(
                color: Colors.grey[100],
                borderRadius: BorderRadius.circular(8),
              ),
              child: CustomPaint(
                painter: VennDiagramPainter(
                  probabilityA: _probabilityA,
                  probabilityB: _probabilityB,
                  probabilityAandB: _probabilityAandB,
                  eventAColor: _eventAColor,
                  eventBColor: _eventBColor,
                  intersectionColor: _intersectionColor,
                  textColor: _textColor,
                ),
              ),
            ),

            const SizedBox(height: 16),

            // Probability sliders
            Text(
              'Adjust Probabilities:',
              style: TextStyle(
                fontSize: 14,
                fontWeight: FontWeight.bold,
                color: _textColor,
              ),
            ),

            const SizedBox(height: 8),

            // P(A) slider
            Row(
              children: [
                SizedBox(
                  width: 120,
                  child: Text(
                    'P(A):',
                    style: TextStyle(
                      fontSize: 12,
                      color: _textColor,
                    ),
                  ),
                ),
                Expanded(
                  child: Slider(
                    value: _probabilityA,
                    min: 0.0,
                    max: 1.0,
                    divisions: 20,
                    label: _probabilityA.toStringAsFixed(2),
                    onChanged: (double value) {
                      setState(() {
                        _probabilityA = value;
                        _calculateProbabilities();
                      });
                    },
                    activeColor: _eventAColor,
                    inactiveColor: _eventAColor.withOpacity(0.3),
                  ),
                ),
                SizedBox(
                  width: 40,
                  child: Text(
                    _probabilityA.toStringAsFixed(2),
                    style: TextStyle(
                      fontSize: 12,
                      color: _textColor,
                    ),
                  ),
                ),
              ],
            ),

            // P(B) slider
            Row(
              children: [
                SizedBox(
                  width: 120,
                  child: Text(
                    'P(B):',
                    style: TextStyle(
                      fontSize: 12,
                      color: _textColor,
                    ),
                  ),
                ),
                Expanded(
                  child: Slider(
                    value: _probabilityB,
                    min: 0.0,
                    max: 1.0,
                    divisions: 20,
                    label: _probabilityB.toStringAsFixed(2),
                    onChanged: (double value) {
                      setState(() {
                        _probabilityB = value;
                        _calculateProbabilities();
                      });
                    },
                    activeColor: _eventBColor,
                    inactiveColor: _eventBColor.withOpacity(0.3),
                  ),
                ),
                SizedBox(
                  width: 40,
                  child: Text(
                    _probabilityB.toStringAsFixed(2),
                    style: TextStyle(
                      fontSize: 12,
                      color: _textColor,
                    ),
                  ),
                ),
              ],
            ),

            // P(A and B) slider
            Row(
              children: [
                SizedBox(
                  width: 120,
                  child: Text(
                    'P(A and B):',
                    style: TextStyle(
                      fontSize: 12,
                      color: _textColor,
                    ),
                  ),
                ),
                Expanded(
                  child: Slider(
                    value: _probabilityAandB,
                    min: 0.0,
                    max: math.min(_probabilityA, _probabilityB),
                    divisions: 20,
                    label: _probabilityAandB.toStringAsFixed(2),
                    onChanged: _mutuallyExclusive || _independent
                        ? null
                        : (double value) {
                            setState(() {
                              _probabilityAandB = value;
                              _calculateProbabilities();
                            });
                          },
                    activeColor: _intersectionColor,
                    inactiveColor: _intersectionColor.withOpacity(0.3),
                  ),
                ),
                SizedBox(
                  width: 40,
                  child: Text(
                    _probabilityAandB.toStringAsFixed(2),
                    style: TextStyle(
                      fontSize: 12,
                      color: _textColor,
                    ),
                  ),
                ),
              ],
            ),

            const SizedBox(height: 16),

            // Event relationship checkboxes
            Row(
              children: [
                Checkbox(
                  value: _mutuallyExclusive,
                  onChanged: (bool? value) {
                    setState(() {
                      _mutuallyExclusive = value ?? false;
                      if (_mutuallyExclusive) {
                        _independent = false;
                        _probabilityAandB = 0.0;
                      }
                      _calculateProbabilities();
                    });
                  },
                  activeColor: _primaryColor,
                ),
                Text(
                  'Mutually Exclusive',
                  style: TextStyle(
                    fontSize: 12,
                    color: _textColor,
                  ),
                ),
                const SizedBox(width: 16),
                Checkbox(
                  value: _independent,
                  onChanged: (bool? value) {
                    setState(() {
                      _independent = value ?? false;
                      if (_independent) {
                        _mutuallyExclusive = false;
                        _probabilityAandB = _probabilityA * _probabilityB;
                      }
                      _calculateProbabilities();
                    });
                  },
                  activeColor: _primaryColor,
                ),
                Text(
                  'Independent',
                  style: TextStyle(
                    fontSize: 12,
                    color: _textColor,
                  ),
                ),
              ],
            ),

            const SizedBox(height: 16),

            // Results section
            Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: _secondaryColor.withOpacity(0.1),
                borderRadius: BorderRadius.circular(8),
                border: Border.all(color: _secondaryColor.withOpacity(0.3)),
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    _selectedRule == 'addition'
                        ? 'Addition Rule: P(A or B) = P(A) + P(B) - P(A and B)'
                        : 'Multiplication Rule: P(A and B) = P(A) × P(B|A)',
                    style: TextStyle(
                      fontSize: 12,
                      fontWeight: FontWeight.bold,
                      color: _textColor,
                    ),
                  ),
                  const SizedBox(height: 8),
                  _selectedRule == 'addition'
                      ? Text(
                          'P(A or B) = ${_probabilityA.toStringAsFixed(2)} + ${_probabilityB.toStringAsFixed(2)} - ${_probabilityAandB.toStringAsFixed(2)} = ${_probabilityAorB.toStringAsFixed(2)}',
                          style: TextStyle(
                            fontSize: 12,
                            color: _textColor,
                          ),
                        )
                      : Text(
                          'P(A and B) = ${_probabilityA.toStringAsFixed(2)} × ${_probabilityBgivenA.toStringAsFixed(2)} = ${_probabilityAandB.toStringAsFixed(2)}',
                          style: TextStyle(
                            fontSize: 12,
                            color: _textColor,
                          ),
                        ),
                  const SizedBox(height: 8),
                  Text(
                    _mutuallyExclusive
                        ? 'These events are mutually exclusive (cannot occur together)'
                        : _independent
                            ? 'These events are independent (one does not affect the other)'
                            : 'These events are dependent (one affects the other)',
                    style: TextStyle(
                      fontSize: 12,
                      fontStyle: FontStyle.italic,
                      color: _textColor.withOpacity(0.7),
                    ),
                  ),
                ],
              ),
            ),

            const SizedBox(height: 16),

            // Examples section
            Text(
              'Example Scenarios:',
              style: TextStyle(
                fontSize: 14,
                fontWeight: FontWeight.bold,
                color: _textColor,
              ),
            ),

            const SizedBox(height: 8),

            // Example cards
            SizedBox(
              height: 120,
              child: ListView.builder(
                scrollDirection: Axis.horizontal,
                itemCount: _examples.length,
                itemBuilder: (context, index) {
                  final example = _examples[index];
                  return Card(
                    margin: const EdgeInsets.only(right: 8),
                    color: _secondaryColor.withOpacity(0.1),
                    child: InkWell(
                      onTap: () => _loadExample(example),
                      child: Container(
                        width: 200,
                        padding: const EdgeInsets.all(8),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              example['title'],
                              style: TextStyle(
                                fontSize: 14,
                                fontWeight: FontWeight.bold,
                                color: _textColor,
                              ),
                            ),
                            const SizedBox(height: 4),
                            Text(
                              example['description'],
                              style: TextStyle(
                                fontSize: 12,
                                color: _textColor.withOpacity(0.7),
                              ),
                            ),
                          ],
                        ),
                      ),
                    ),
                  );
                },
              ),
            ),
          ],
        ),
      ),
    );
  }
}

// Extension to capitalize strings
extension StringExtension on String {
  String capitalize() {
    return "${this[0].toUpperCase()}${this.substring(1)}";
  }
}

// Custom painter for drawing the Venn diagram
class VennDiagramPainter extends CustomPainter {
  final double probabilityA;
  final double probabilityB;
  final double probabilityAandB;
  final Color eventAColor;
  final Color eventBColor;
  final Color intersectionColor;
  final Color textColor;

  VennDiagramPainter({
    required this.probabilityA,
    required this.probabilityB,
    required this.probabilityAandB,
    required this.eventAColor,
    required this.eventBColor,
    required this.intersectionColor,
    required this.textColor,
  });

  @override
  void paint(Canvas canvas, Size size) {
    final Paint paintA = Paint()
      ..color = eventAColor
      ..style = PaintingStyle.fill;

    final Paint paintB = Paint()
      ..color = eventBColor
      ..style = PaintingStyle.fill;

    final Paint paintIntersection = Paint()
      ..color = intersectionColor
      ..style = PaintingStyle.fill;

    final Paint outlinePaint = Paint()
      ..color = Colors.grey
      ..style = PaintingStyle.stroke
      ..strokeWidth = 1.0;

    // Draw sample space (rectangle)
    final Rect sampleSpace = Rect.fromLTWH(10, 10, size.width - 20, size.height - 20);
    canvas.drawRect(sampleSpace, Paint()..color = Colors.white);
    canvas.drawRect(sampleSpace, outlinePaint);

    // Calculate circle sizes based on probabilities
    double maxRadius = math.min(size.width, size.height) * 0.3;
    double radiusA = maxRadius * math.sqrt(probabilityA);
    double radiusB = maxRadius * math.sqrt(probabilityB);

    // Calculate circle positions
    double centerX = size.width / 2;
    double centerY = size.height / 2;

    // Calculate overlap distance based on intersection probability
    double maxOverlap = math.min(radiusA, radiusB) * 2;
    double overlap = maxOverlap * (1 - probabilityAandB / math.min(probabilityA, probabilityB));

    // Adjust for mutually exclusive events
    if (probabilityAandB == 0) {
      overlap = radiusA + radiusB + 10; // Ensure no overlap
    }

    // Calculate circle centers
    Offset centerA = Offset(centerX - overlap / 2, centerY);
    Offset centerB = Offset(centerX + overlap / 2, centerY);

    // Draw circles
    canvas.drawCircle(centerA, radiusA, paintA);
    canvas.drawCircle(centerB, radiusB, paintB);

    // Draw labels
    final TextPainter textPainterA = TextPainter(
      text: TextSpan(
        text: 'A',
        style: TextStyle(
          color: textColor,
          fontSize: 16,
          fontWeight: FontWeight.bold,
        ),
      ),
      textDirection: TextDirection.ltr,
    );

    final TextPainter textPainterB = TextPainter(
      text: TextSpan(
        text: 'B',
        style: TextStyle(
          color: textColor,
          fontSize: 16,
          fontWeight: FontWeight.bold,
        ),
      ),
      textDirection: TextDirection.ltr,
    );

    textPainterA.layout();
    textPainterB.layout();

    textPainterA.paint(canvas, Offset(centerA.dx - textPainterA.width / 2, centerA.dy - textPainterA.height / 2));
    textPainterB.paint(canvas, Offset(centerB.dx - textPainterB.width / 2, centerB.dy - textPainterB.height / 2));
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) {
    return true;
  }
}
