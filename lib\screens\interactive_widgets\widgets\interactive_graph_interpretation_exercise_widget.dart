import 'package:flutter/material.dart';
import 'dart:math' as math;

/// Model class for a data point
class DataPoint {
  final double x;
  final double y;
  final String label;

  DataPoint({
    required this.x,
    required this.y,
    required this.label,
  });
}

/// Model class for a question
class Question {
  final String text;
  final List<String> options;
  final int correctAnswerIndex;
  final String explanation;

  Question({
    required this.text,
    required this.options,
    required this.correctAnswerIndex,
    required this.explanation,
  });
}

/// Custom painter for line charts
class LineChartPainter extends CustomPainter {
  final List<DataPoint> dataPoints;
  final double minX;
  final double maxX;
  final double minY;
  final double maxY;
  final String xAxisLabel;
  final String yAxisLabel;
  final Function(DataPoint) pointToOffset;
  final Color primaryColor;
  final Color textColor;

  LineChartPainter({
    required this.dataPoints,
    required this.minX,
    required this.maxX,
    required this.minY,
    required this.maxY,
    required this.xAxisLabel,
    required this.yAxisLabel,
    required this.pointToOffset,
    required this.primaryColor,
    required this.textColor,
  });

  @override
  void paint(Canvas canvas, Size size) {
    final width = size.width;
    final height = size.height;

    // Draw axes
    final axesPaint = Paint()
      ..color = textColor.withAlpha(150)
      ..strokeWidth = 1;

    // X axis
    canvas.drawLine(
      Offset(0, height),
      Offset(width, height),
      axesPaint,
    );

    // Y axis
    canvas.drawLine(
      Offset(0, 0),
      Offset(0, height),
      axesPaint,
    );

    // Draw grid lines
    final gridPaint = Paint()
      ..color = textColor.withAlpha(30)
      ..strokeWidth = 0.5;

    // Horizontal grid lines
    for (int i = 0; i < 5; i++) {
      final y = i * height / 4;
      canvas.drawLine(
        Offset(0, y),
        Offset(width, y),
        gridPaint,
      );

      // Y axis labels
      final yValue = maxY - (i * (maxY - minY) / 4);
      final textPainter = TextPainter(
        text: TextSpan(
          text: yValue.toStringAsFixed(1),
          style: TextStyle(
            color: textColor.withAlpha(150),
            fontSize: 10,
          ),
        ),
        textDirection: TextDirection.ltr,
      );
      textPainter.layout();
      textPainter.paint(canvas, Offset(5, y - 12));
    }

    // Vertical grid lines
    for (int i = 0; i < 5; i++) {
      final x = i * width / 4;
      canvas.drawLine(
        Offset(x, 0),
        Offset(x, height),
        gridPaint,
      );

      // X axis labels
      final xValue = minX + (i * (maxX - minX) / 4);
      final textPainter = TextPainter(
        text: TextSpan(
          text: xValue.toStringAsFixed(1),
          style: TextStyle(
            color: textColor.withAlpha(150),
            fontSize: 10,
          ),
        ),
        textDirection: TextDirection.ltr,
      );
      textPainter.layout();
      textPainter.paint(canvas, Offset(x - textPainter.width / 2, height + 5));
    }

    // Draw axis labels
    final xLabelPainter = TextPainter(
      text: TextSpan(
        text: xAxisLabel,
        style: TextStyle(
          color: textColor.withAlpha(200),
          fontSize: 12,
          fontWeight: FontWeight.bold,
        ),
      ),
      textDirection: TextDirection.ltr,
    );
    xLabelPainter.layout();
    xLabelPainter.paint(canvas, Offset(width / 2 - xLabelPainter.width / 2, height + 20));

    final yLabelPainter = TextPainter(
      text: TextSpan(
        text: yAxisLabel,
        style: TextStyle(
          color: textColor.withAlpha(200),
          fontSize: 12,
          fontWeight: FontWeight.bold,
        ),
      ),
      textDirection: TextDirection.ltr,
    );
    yLabelPainter.layout();
    canvas.save();
    canvas.translate(0, height / 2);
    canvas.rotate(-math.pi / 2);
    yLabelPainter.paint(canvas, Offset(-yLabelPainter.width / 2, -30));
    canvas.restore();

    // Draw line
    final linePaint = Paint()
      ..color = primaryColor
      ..strokeWidth = 2
      ..style = PaintingStyle.stroke;

    final path = Path();

    if (dataPoints.isNotEmpty) {
      final firstPoint = pointToOffset(dataPoints.first);
      path.moveTo(firstPoint.dx, firstPoint.dy);

      for (int i = 1; i < dataPoints.length; i++) {
        final point = pointToOffset(dataPoints[i]);
        path.lineTo(point.dx, point.dy);
      }
    }

    canvas.drawPath(path, linePaint);

    // Draw points
    final pointPaint = Paint()
      ..color = primaryColor
      ..strokeWidth = 1
      ..style = PaintingStyle.fill;

    for (final dataPoint in dataPoints) {
      final point = pointToOffset(dataPoint);
      canvas.drawCircle(point, 4, pointPaint);
    }
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => true;
}

/// Custom painter for bar charts
class BarChartPainter extends CustomPainter {
  final List<DataPoint> dataPoints;
  final double maxY;
  final double barWidth;
  final String xAxisLabel;
  final String yAxisLabel;
  final Color primaryColor;
  final Color textColor;

  BarChartPainter({
    required this.dataPoints,
    required this.maxY,
    required this.barWidth,
    required this.xAxisLabel,
    required this.yAxisLabel,
    required this.primaryColor,
    required this.textColor,
  });

  @override
  void paint(Canvas canvas, Size size) {
    final width = size.width;
    final height = size.height;

    // Draw axes
    final axesPaint = Paint()
      ..color = textColor.withAlpha(150)
      ..strokeWidth = 1;

    // X axis
    canvas.drawLine(
      Offset(0, height),
      Offset(width, height),
      axesPaint,
    );

    // Y axis
    canvas.drawLine(
      Offset(0, 0),
      Offset(0, height),
      axesPaint,
    );

    // Draw grid lines
    final gridPaint = Paint()
      ..color = textColor.withAlpha(30)
      ..strokeWidth = 0.5;

    // Horizontal grid lines
    for (int i = 0; i < 5; i++) {
      final y = i * height / 4;
      canvas.drawLine(
        Offset(0, y),
        Offset(width, y),
        gridPaint,
      );

      // Y axis labels
      final yValue = maxY - (i * maxY / 4);
      final textPainter = TextPainter(
        text: TextSpan(
          text: yValue.toStringAsFixed(1),
          style: TextStyle(
            color: textColor.withAlpha(150),
            fontSize: 10,
          ),
        ),
        textDirection: TextDirection.ltr,
      );
      textPainter.layout();
      textPainter.paint(canvas, Offset(5, y - 12));
    }

    // Draw axis labels
    final xLabelPainter = TextPainter(
      text: TextSpan(
        text: xAxisLabel,
        style: TextStyle(
          color: textColor.withAlpha(200),
          fontSize: 12,
          fontWeight: FontWeight.bold,
        ),
      ),
      textDirection: TextDirection.ltr,
    );
    xLabelPainter.layout();
    xLabelPainter.paint(canvas, Offset(width / 2 - xLabelPainter.width / 2, height + 20));

    final yLabelPainter = TextPainter(
      text: TextSpan(
        text: yAxisLabel,
        style: TextStyle(
          color: textColor.withAlpha(200),
          fontSize: 12,
          fontWeight: FontWeight.bold,
        ),
      ),
      textDirection: TextDirection.ltr,
    );
    yLabelPainter.layout();
    canvas.save();
    canvas.translate(0, height / 2);
    canvas.rotate(-math.pi / 2);
    yLabelPainter.paint(canvas, Offset(-yLabelPainter.width / 2, -30));
    canvas.restore();

    // Draw bars
    final barPaint = Paint()
      ..color = primaryColor
      ..style = PaintingStyle.fill;

    final spacing = (width - (dataPoints.length * barWidth)) / (dataPoints.length + 1);

    for (int i = 0; i < dataPoints.length; i++) {
      final dataPoint = dataPoints[i];
      final barHeight = (dataPoint.y / maxY) * height;
      final left = spacing + i * (barWidth + spacing);

      canvas.drawRect(
        Rect.fromLTWH(left, height - barHeight, barWidth, barHeight),
        barPaint,
      );

      // X axis labels
      final textPainter = TextPainter(
        text: TextSpan(
          text: dataPoint.label,
          style: TextStyle(
            color: textColor.withAlpha(150),
            fontSize: 10,
          ),
        ),
        textDirection: TextDirection.ltr,
      );
      textPainter.layout();
      textPainter.paint(
        canvas,
        Offset(left + barWidth / 2 - textPainter.width / 2, height + 5),
      );
    }
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => true;
}

/// Custom painter for scatter plots
class ScatterPlotPainter extends CustomPainter {
  final List<DataPoint> dataPoints;
  final double minX;
  final double maxX;
  final double minY;
  final double maxY;
  final String xAxisLabel;
  final String yAxisLabel;
  final Function(DataPoint) pointToOffset;
  final Color primaryColor;
  final Color textColor;

  ScatterPlotPainter({
    required this.dataPoints,
    required this.minX,
    required this.maxX,
    required this.minY,
    required this.maxY,
    required this.xAxisLabel,
    required this.yAxisLabel,
    required this.pointToOffset,
    required this.primaryColor,
    required this.textColor,
  });

  @override
  void paint(Canvas canvas, Size size) {
    final width = size.width;
    final height = size.height;

    // Draw axes
    final axesPaint = Paint()
      ..color = textColor.withAlpha(150)
      ..strokeWidth = 1;

    // X axis
    canvas.drawLine(
      Offset(0, height),
      Offset(width, height),
      axesPaint,
    );

    // Y axis
    canvas.drawLine(
      Offset(0, 0),
      Offset(0, height),
      axesPaint,
    );

    // Draw grid lines
    final gridPaint = Paint()
      ..color = textColor.withAlpha(30)
      ..strokeWidth = 0.5;

    // Horizontal grid lines
    for (int i = 0; i < 5; i++) {
      final y = i * height / 4;
      canvas.drawLine(
        Offset(0, y),
        Offset(width, y),
        gridPaint,
      );

      // Y axis labels
      final yValue = maxY - (i * (maxY - minY) / 4);
      final textPainter = TextPainter(
        text: TextSpan(
          text: yValue.toStringAsFixed(1),
          style: TextStyle(
            color: textColor.withAlpha(150),
            fontSize: 10,
          ),
        ),
        textDirection: TextDirection.ltr,
      );
      textPainter.layout();
      textPainter.paint(canvas, Offset(5, y - 12));
    }

    // Vertical grid lines
    for (int i = 0; i < 5; i++) {
      final x = i * width / 4;
      canvas.drawLine(
        Offset(x, 0),
        Offset(x, height),
        gridPaint,
      );

      // X axis labels
      final xValue = minX + (i * (maxX - minX) / 4);
      final textPainter = TextPainter(
        text: TextSpan(
          text: xValue.toStringAsFixed(1),
          style: TextStyle(
            color: textColor.withAlpha(150),
            fontSize: 10,
          ),
        ),
        textDirection: TextDirection.ltr,
      );
      textPainter.layout();
      textPainter.paint(canvas, Offset(x - textPainter.width / 2, height + 5));
    }

    // Draw axis labels
    final xLabelPainter = TextPainter(
      text: TextSpan(
        text: xAxisLabel,
        style: TextStyle(
          color: textColor.withAlpha(200),
          fontSize: 12,
          fontWeight: FontWeight.bold,
        ),
      ),
      textDirection: TextDirection.ltr,
    );
    xLabelPainter.layout();
    xLabelPainter.paint(canvas, Offset(width / 2 - xLabelPainter.width / 2, height + 20));

    final yLabelPainter = TextPainter(
      text: TextSpan(
        text: yAxisLabel,
        style: TextStyle(
          color: textColor.withAlpha(200),
          fontSize: 12,
          fontWeight: FontWeight.bold,
        ),
      ),
      textDirection: TextDirection.ltr,
    );
    yLabelPainter.layout();
    canvas.save();
    canvas.translate(0, height / 2);
    canvas.rotate(-math.pi / 2);
    yLabelPainter.paint(canvas, Offset(-yLabelPainter.width / 2, -30));
    canvas.restore();

    // Draw points
    final pointPaint = Paint()
      ..color = primaryColor
      ..strokeWidth = 1
      ..style = PaintingStyle.fill;

    final pointStrokePaint = Paint()
      ..color = primaryColor.withAlpha(150)
      ..strokeWidth = 1
      ..style = PaintingStyle.stroke;

    for (final dataPoint in dataPoints) {
      final point = pointToOffset(dataPoint);
      canvas.drawCircle(point, 6, pointStrokePaint);
      canvas.drawCircle(point, 4, pointPaint);
    }
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => true;
}

/// Model class for a graph exercise
class GraphExercise {
  final String title;
  final String description;
  final String graphType;
  final String xAxisLabel;
  final String yAxisLabel;
  final List<DataPoint> dataPoints;
  final List<Question> questions;
  final String explanation;

  GraphExercise({
    required this.title,
    required this.description,
    required this.graphType,
    required this.xAxisLabel,
    required this.yAxisLabel,
    required this.dataPoints,
    required this.questions,
    required this.explanation,
  });
}

/// A widget that presents graph interpretation exercises for scientific data
/// Users can analyze graphs and answer questions about trends, relationships, and conclusions
class InteractiveGraphInterpretationExerciseWidget extends StatefulWidget {
  /// The data for this widget
  final Map<String, dynamic> data;

  /// Optional callback when the widget state changes
  final Function(bool isCompleted)? onStateChanged;

  const InteractiveGraphInterpretationExerciseWidget({
    super.key,
    required this.data,
    this.onStateChanged,
  });

  /// Factory constructor to create the widget from JSON data
  factory InteractiveGraphInterpretationExerciseWidget.fromData(Map<String, dynamic> data) {
    return InteractiveGraphInterpretationExerciseWidget(
      data: data,
    );
  }

  @override
  State<InteractiveGraphInterpretationExerciseWidget> createState() => _InteractiveGraphInterpretationExerciseWidgetState();
}

class _InteractiveGraphInterpretationExerciseWidgetState extends State<InteractiveGraphInterpretationExerciseWidget> {
  // Exercise scenarios
  late List<GraphExercise> _exercises;
  late int _currentExerciseIndex;

  // UI state
  late bool _isCompleted;
  late bool _showExplanation;
  late bool _hasSubmitted;
  late List<int> _selectedAnswers;
  late bool _isCorrect;

  // Colors
  late Color _primaryColor;
  late Color _secondaryColor;
  late Color _accentColor;
  late Color _backgroundColor;
  late Color _textColor;

  @override
  void initState() {
    super.initState();

    // Initialize colors
    _primaryColor = _getColorFromHex(widget.data['primaryColor'] ?? '#2196F3');
    _secondaryColor = _getColorFromHex(widget.data['secondaryColor'] ?? '#FF9800');
    _accentColor = _getColorFromHex(widget.data['accentColor'] ?? '#4CAF50');
    _backgroundColor = _getColorFromHex(widget.data['backgroundColor'] ?? '#FFFFFF');
    _textColor = _getColorFromHex(widget.data['textColor'] ?? '#212121');

    // Initialize exercises
    _initializeExercises();

    // Initialize state
    _currentExerciseIndex = 0;
    _isCompleted = false;
    _showExplanation = false;
    _hasSubmitted = false;
    _selectedAnswers = [];
    _isCorrect = false;
  }

  // Initialize the exercise scenarios
  void _initializeExercises() {
    final List<dynamic> exercisesData = widget.data['exercises'] ?? _getDefaultExercises();

    _exercises = exercisesData.map((exerciseData) {
      return GraphExercise(
        title: exerciseData['title'] ?? '',
        description: exerciseData['description'] ?? '',
        graphType: exerciseData['graphType'] ?? 'line',
        xAxisLabel: exerciseData['xAxisLabel'] ?? '',
        yAxisLabel: exerciseData['yAxisLabel'] ?? '',
        dataPoints: List<DataPoint>.from(
          (exerciseData['dataPoints'] ?? []).map(
            (dataPoint) => DataPoint(
              x: dataPoint['x'] ?? 0,
              y: dataPoint['y'] ?? 0,
              label: dataPoint['label'] ?? '',
            ),
          ),
        ),
        questions: List<Question>.from(
          (exerciseData['questions'] ?? []).map(
            (question) => Question(
              text: question['text'] ?? '',
              options: List<String>.from(question['options'] ?? []),
              correctAnswerIndex: question['correctAnswerIndex'] ?? 0,
              explanation: question['explanation'] ?? '',
            ),
          ),
        ),
        explanation: exerciseData['explanation'] ?? '',
      );
    }).toList();
  }

  // Get default exercises if none are provided
  List<Map<String, dynamic>> _getDefaultExercises() {
    return [
      {
        'title': 'Population Growth Over Time',
        'description': 'The graph shows the population of a bacterial culture over time.',
        'graphType': 'line',
        'xAxisLabel': 'Time (hours)',
        'yAxisLabel': 'Population (thousands)',
        'dataPoints': [
          {'x': 0, 'y': 10, 'label': '0h'},
          {'x': 1, 'y': 15, 'label': '1h'},
          {'x': 2, 'y': 22, 'label': '2h'},
          {'x': 3, 'y': 33, 'label': '3h'},
          {'x': 4, 'y': 50, 'label': '4h'},
          {'x': 5, 'y': 74, 'label': '5h'},
          {'x': 6, 'y': 111, 'label': '6h'},
          {'x': 7, 'y': 166, 'label': '7h'},
          {'x': 8, 'y': 220, 'label': '8h'},
          {'x': 9, 'y': 240, 'label': '9h'},
          {'x': 10, 'y': 245, 'label': '10h'},
        ],
        'questions': [
          {
            'text': 'What type of growth pattern does this graph show?',
            'options': [
              'Linear growth',
              'Exponential growth followed by a plateau',
              'Logarithmic growth',
              'Constant growth rate',
            ],
            'correctAnswerIndex': 1,
            'explanation': 'The graph shows exponential growth (hours 0-7) followed by a plateau (hours 8-10). This is typical of bacterial growth in a limited environment, where resources eventually become scarce.',
          },
          {
            'text': 'During which time period was the growth rate highest?',
            'options': [
              'Between 0-2 hours',
              'Between 3-5 hours',
              'Between 6-8 hours',
              'Between 8-10 hours',
            ],
            'correctAnswerIndex': 2,
            'explanation': 'The growth rate (slope of the curve) is steepest between 6-8 hours, indicating the highest rate of population increase during this period.',
          },
          {
            'text': 'What might explain the plateau after 8 hours?',
            'options': [
              'The bacteria died',
              'The experiment ended',
              'The bacteria reached carrying capacity due to limited resources',
              'The temperature decreased',
            ],
            'correctAnswerIndex': 2,
            'explanation': 'The plateau indicates that the bacterial population reached its carrying capacity, which occurs when limited resources (nutrients, space, etc.) prevent further population growth.',
          },
        ],
        'explanation': 'This graph illustrates a typical bacterial growth curve with distinct phases: lag phase (minimal growth at the beginning), exponential phase (rapid growth in the middle), and stationary phase (plateau at the end). Understanding these phases is crucial for interpreting microbial growth in various environments.',
      },
      {
        'title': 'Temperature vs. Reaction Rate',
        'description': 'The graph shows the relationship between temperature and the rate of a chemical reaction.',
        'graphType': 'line',
        'xAxisLabel': 'Temperature (°C)',
        'yAxisLabel': 'Reaction Rate (mol/L·s)',
        'dataPoints': [
          {'x': 0, 'y': 0.1, 'label': '0°C'},
          {'x': 10, 'y': 0.2, 'label': '10°C'},
          {'x': 20, 'y': 0.4, 'label': '20°C'},
          {'x': 30, 'y': 0.8, 'label': '30°C'},
          {'x': 40, 'y': 1.6, 'label': '40°C'},
          {'x': 50, 'y': 3.2, 'label': '50°C'},
          {'x': 60, 'y': 6.4, 'label': '60°C'},
          {'x': 70, 'y': 12.8, 'label': '70°C'},
          {'x': 80, 'y': 25.6, 'label': '80°C'},
          {'x': 90, 'y': 30.0, 'label': '90°C'},
          {'x': 100, 'y': 15.0, 'label': '100°C'},
        ],
        'questions': [
          {
            'text': 'How does temperature generally affect the reaction rate up to 90°C?',
            'options': [
              'Temperature has no effect on reaction rate',
              'Reaction rate decreases as temperature increases',
              'Reaction rate increases linearly with temperature',
              'Reaction rate increases exponentially with temperature',
            ],
            'correctAnswerIndex': 3,
            'explanation': 'The graph shows an exponential increase in reaction rate as temperature increases up to 90°C. This follows the Arrhenius equation, which describes how reaction rates typically increase exponentially with temperature.',
          },
          {
            'text': 'What happens to the reaction rate above 90°C?',
            'options': [
              'It continues to increase',
              'It plateaus',
              'It decreases',
              'It remains constant',
            ],
            'correctAnswerIndex': 2,
            'explanation': 'The reaction rate decreases above 90°C, as shown by the downward slope of the curve between 90°C and 100°C.',
          },
          {
            'text': 'What might explain the decrease in reaction rate above 90°C?',
            'options': [
              'The reactants are being used up',
              'The enzyme or catalyst is being denatured by high temperature',
              'The reaction is reaching equilibrium',
              'The measurement equipment is malfunctioning',
            ],
            'correctAnswerIndex': 1,
            'explanation': 'The decrease in reaction rate above 90°C is likely due to the denaturation of an enzyme or degradation of a catalyst at high temperatures. This is common in biological reactions or catalyzed chemical reactions.',
          },
        ],
        'explanation': 'This graph demonstrates the relationship between temperature and reaction rate, showing both the expected exponential increase (following the Arrhenius equation) and the effects of high temperatures on biological catalysts or enzymes. The optimal temperature for this reaction appears to be around 90°C, after which the rate decreases due to catalyst degradation.',
      },
      {
        'title': 'Plant Growth Under Different Light Conditions',
        'description': 'The graph shows the growth of plants under different light intensities over a 4-week period.',
        'graphType': 'bar',
        'xAxisLabel': 'Light Intensity (lux)',
        'yAxisLabel': 'Plant Height (cm)',
        'dataPoints': [
          {'x': 1, 'y': 5, 'label': '1,000 lux'},
          {'x': 2, 'y': 12, 'label': '5,000 lux'},
          {'x': 3, 'y': 18, 'label': '10,000 lux'},
          {'x': 4, 'y': 22, 'label': '15,000 lux'},
          {'x': 5, 'y': 20, 'label': '20,000 lux'},
          {'x': 6, 'y': 15, 'label': '25,000 lux'},
        ],
        'questions': [
          {
            'text': 'At which light intensity did plants grow tallest?',
            'options': [
              '5,000 lux',
              '10,000 lux',
              '15,000 lux',
              '20,000 lux',
            ],
            'correctAnswerIndex': 2,
            'explanation': 'The plants grew tallest (22 cm) at 15,000 lux, as shown by the highest bar on the graph.',
          },
          {
            'text': 'What happens to plant growth at very high light intensities (above 15,000 lux)?',
            'options': [
              'Growth continues to increase',
              'Growth remains constant',
              'Growth decreases',
              'Growth stops completely',
            ],
            'correctAnswerIndex': 2,
            'explanation': 'Plant growth decreases at light intensities above 15,000 lux, as shown by the shorter bars for 20,000 and 25,000 lux.',
          },
          {
            'text': 'Based on this graph, what conclusion can you draw about the relationship between light intensity and plant growth?',
            'options': [
              'More light always leads to better growth',
              'Light has no significant effect on plant growth',
              'Plants grow best at moderate light levels, with too little or too much light inhibiting growth',
              'Plants only need minimal light to achieve maximum growth',
            ],
            'correctAnswerIndex': 2,
            'explanation': 'The graph shows that plants grow best at moderate light levels (around 15,000 lux), with both too little light (1,000 lux) and too much light (25,000 lux) resulting in reduced growth. This demonstrates that there is an optimal range of light intensity for plant growth.',
          },
        ],
        'explanation': 'This graph illustrates the concept of optimal environmental conditions for plant growth. While light is essential for photosynthesis, excessive light can cause photoinhibition or heat stress, reducing growth. Similarly, insufficient light limits photosynthesis. Understanding these relationships is important for optimizing plant growth in agricultural and ecological contexts.',
      },
      {
        'title': 'Correlation Between Study Time and Test Scores',
        'description': 'The scatter plot shows the relationship between hours spent studying and test scores for 20 students.',
        'graphType': 'scatter',
        'xAxisLabel': 'Study Time (hours)',
        'yAxisLabel': 'Test Score (%)',
        'dataPoints': [
          {'x': 1, 'y': 65, 'label': 'Student 1'},
          {'x': 2, 'y': 70, 'label': 'Student 2'},
          {'x': 1.5, 'y': 68, 'label': 'Student 3'},
          {'x': 3, 'y': 75, 'label': 'Student 4'},
          {'x': 4, 'y': 80, 'label': 'Student 5'},
          {'x': 2.5, 'y': 72, 'label': 'Student 6'},
          {'x': 5, 'y': 85, 'label': 'Student 7'},
          {'x': 6, 'y': 88, 'label': 'Student 8'},
          {'x': 4.5, 'y': 82, 'label': 'Student 9'},
          {'x': 7, 'y': 90, 'label': 'Student 10'},
          {'x': 8, 'y': 92, 'label': 'Student 11'},
          {'x': 3.5, 'y': 76, 'label': 'Student 12'},
          {'x': 5.5, 'y': 86, 'label': 'Student 13'},
          {'x': 6.5, 'y': 89, 'label': 'Student 14'},
          {'x': 7.5, 'y': 91, 'label': 'Student 15'},
          {'x': 2, 'y': 65, 'label': 'Student 16'},
          {'x': 4, 'y': 75, 'label': 'Student 17'},
          {'x': 6, 'y': 82, 'label': 'Student 18'},
          {'x': 8, 'y': 88, 'label': 'Student 19'},
          {'x': 9, 'y': 95, 'label': 'Student 20'},
        ],
        'questions': [
          {
            'text': 'What type of correlation does this scatter plot show?',
            'options': [
              'No correlation',
              'Weak negative correlation',
              'Strong positive correlation',
              'Perfect correlation',
            ],
            'correctAnswerIndex': 2,
            'explanation': 'The scatter plot shows a strong positive correlation between study time and test scores. As study time increases, test scores tend to increase as well, with points generally following an upward trend.',
          },
          {
            'text': 'Based on this data, what would you predict for a student who studies for 5 hours?',
            'options': [
              'A score below 70%',
              'A score between 70-80%',
              'A score between 80-90%',
              'A score above 90%',
            ],
            'correctAnswerIndex': 2,
            'explanation': 'Based on the trend in the data, a student who studies for 5 hours would likely score between 80-90%. Looking at the data points around x=5, we see scores of 85% and 82%.',
          },
          {
            'text': 'Which statement best describes the relationship shown in this graph?',
            'options': [
              'Study time causes test scores to increase',
              'There is a positive association between study time and test scores',
              'Studying guarantees high test scores',
              'Test scores determine how long students study',
            ],
            'correctAnswerIndex': 1,
            'explanation': 'The graph shows a positive association between study time and test scores, meaning that higher study times tend to be associated with higher test scores. However, correlation does not necessarily imply causation, and other factors may also influence test scores.',
          },
        ],
        'explanation': 'This scatter plot demonstrates correlation between two variables. While there is a clear positive association between study time and test scores, it\'s important to note that correlation does not necessarily imply causation. Other factors not shown in the graph (prior knowledge, study efficiency, etc.) may also influence test scores. Understanding correlational data is crucial for interpreting scientific research and avoiding unwarranted causal conclusions.',
      },
    ];
  }

  // Get color from hex string
  Color _getColorFromHex(String hexColor) {
    hexColor = hexColor.replaceAll('#', '');
    if (hexColor.length == 6) {
      hexColor = 'FF$hexColor';
    }
    return Color(int.parse(hexColor, radix: 16));
  }

  // Select an answer for the current question
  void _selectAnswer(int questionIndex, int answerIndex) {
    setState(() {
      // Ensure the list is large enough
      while (_selectedAnswers.length <= questionIndex) {
        _selectedAnswers.add(-1);
      }
      _selectedAnswers[questionIndex] = answerIndex;
      _hasSubmitted = false;
    });
  }

  // Check if all questions have been answered
  bool _allQuestionsAnswered() {
    final exercise = _exercises[_currentExerciseIndex];
    if (_selectedAnswers.length < exercise.questions.length) {
      return false;
    }

    for (int i = 0; i < exercise.questions.length; i++) {
      if (_selectedAnswers[i] == -1) {
        return false;
      }
    }

    return true;
  }

  // Submit answers for checking
  void _submitAnswers() {
    final exercise = _exercises[_currentExerciseIndex];
    bool allCorrect = true;

    for (int i = 0; i < exercise.questions.length; i++) {
      if (_selectedAnswers[i] != exercise.questions[i].correctAnswerIndex) {
        allCorrect = false;
        break;
      }
    }

    setState(() {
      _isCorrect = allCorrect;
      _hasSubmitted = true;
    });
  }

  // Go to the next exercise
  void _nextExercise() {
    if (_currentExerciseIndex < _exercises.length - 1) {
      setState(() {
        _currentExerciseIndex++;
        _selectedAnswers = [];
        _hasSubmitted = false;
        _showExplanation = false;
      });
    } else if (!_isCompleted) {
      setState(() {
        _isCompleted = true;
      });
      widget.onStateChanged?.call(true);
    }
  }

  // Go to the previous exercise
  void _previousExercise() {
    if (_currentExerciseIndex > 0) {
      setState(() {
        _currentExerciseIndex--;
        _selectedAnswers = [];
        _hasSubmitted = false;
        _showExplanation = false;
      });
    }
  }

  // Toggle explanation visibility
  void _toggleExplanation() {
    setState(() {
      _showExplanation = !_showExplanation;
    });
  }

  // Reset the widget
  void _resetWidget() {
    setState(() {
      _currentExerciseIndex = 0;
      _selectedAnswers = [];
      _hasSubmitted = false;
      _isCompleted = false;
      _showExplanation = false;
    });
  }

  // Build a question card
  Widget _buildQuestionCard(int index, Question question) {
    final isAnswered = _selectedAnswers.length > index && _selectedAnswers[index] != -1;
    final selectedAnswer = isAnswered ? _selectedAnswers[index] : -1;
    final isCorrect = _hasSubmitted && selectedAnswer == question.correctAnswerIndex;
    final isIncorrect = _hasSubmitted && selectedAnswer != -1 && selectedAnswer != question.correctAnswerIndex;

    return Container(
      margin: const EdgeInsets.only(bottom: 16),
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: _backgroundColor,
        borderRadius: BorderRadius.circular(8),
        border: Border.all(
          color: _hasSubmitted
              ? (isCorrect ? _accentColor : (isIncorrect ? Colors.red : _primaryColor.withAlpha(75)))
              : _primaryColor.withAlpha(75),
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Question text
          Text(
            '${index + 1}. ${question.text}',
            style: TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.bold,
              color: _textColor,
            ),
          ),

          const SizedBox(height: 12),

          // Options
          ...question.options.asMap().entries.map((entry) {
            final optionIndex = entry.key;
            final option = entry.value;
            final isSelected = selectedAnswer == optionIndex;
            final isCorrectOption = question.correctAnswerIndex == optionIndex;

            return InkWell(
              onTap: _hasSubmitted ? null : () => _selectAnswer(index, optionIndex),
              child: Container(
                margin: const EdgeInsets.only(bottom: 8),
                padding: const EdgeInsets.symmetric(vertical: 8, horizontal: 12),
                decoration: BoxDecoration(
                  color: _hasSubmitted
                      ? (isCorrectOption
                          ? _accentColor.withAlpha(50)
                          : (isSelected ? Colors.red.withAlpha(50) : _backgroundColor))
                      : (isSelected ? _primaryColor.withAlpha(50) : _backgroundColor),
                  borderRadius: BorderRadius.circular(4),
                  border: Border.all(
                    color: _hasSubmitted
                        ? (isCorrectOption
                            ? _accentColor
                            : (isSelected ? Colors.red : Colors.grey.withAlpha(75)))
                        : (isSelected ? _primaryColor : Colors.grey.withAlpha(75)),
                  ),
                ),
                child: Row(
                  children: [
                    Icon(
                      _hasSubmitted
                          ? (isCorrectOption
                              ? Icons.check_circle
                              : (isSelected ? Icons.cancel : Icons.radio_button_unchecked))
                          : (isSelected ? Icons.radio_button_checked : Icons.radio_button_unchecked),
                      color: _hasSubmitted
                          ? (isCorrectOption
                              ? _accentColor
                              : (isSelected ? Colors.red : Colors.grey))
                          : (isSelected ? _primaryColor : Colors.grey),
                      size: 20,
                    ),
                    const SizedBox(width: 8),
                    Expanded(
                      child: Text(
                        option,
                        style: TextStyle(
                          fontSize: 14,
                          color: _textColor,
                          fontWeight: isSelected || (isCorrectOption && _hasSubmitted) ? FontWeight.bold : FontWeight.normal,
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            );
          }).toList(),

          // Explanation for this question
          if (_hasSubmitted)
            Container(
              margin: const EdgeInsets.only(top: 8),
              padding: const EdgeInsets.all(8),
              decoration: BoxDecoration(
                color: _secondaryColor.withAlpha(25),
                borderRadius: BorderRadius.circular(4),
                border: Border.all(color: _secondaryColor.withAlpha(75)),
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'Explanation:',
                    style: TextStyle(
                      fontSize: 14,
                      fontWeight: FontWeight.bold,
                      color: _secondaryColor,
                    ),
                  ),
                  const SizedBox(height: 4),
                  Text(
                    question.explanation,
                    style: TextStyle(
                      fontSize: 14,
                      color: _textColor,
                    ),
                  ),
                ],
              ),
            ),
        ],
      ),
    );
  }

  // Build the graph visualization
  Widget _buildGraph(GraphExercise exercise) {
    switch (exercise.graphType) {
      case 'bar':
        return _buildBarChart(exercise);
      case 'scatter':
        return _buildScatterPlot(exercise);
      case 'line':
      default:
        return _buildLineChart(exercise);
    }
  }

  // Build a line chart
  Widget _buildLineChart(GraphExercise exercise) {
    // Find min and max values for scaling
    double minX = double.infinity;
    double maxX = double.negativeInfinity;
    double minY = double.infinity;
    double maxY = double.negativeInfinity;

    for (final point in exercise.dataPoints) {
      minX = math.min(minX, point.x);
      maxX = math.max(maxX, point.x);
      minY = math.min(minY, point.y);
      maxY = math.max(maxY, point.y);
    }

    // Add some padding
    final xRange = maxX - minX;
    final yRange = maxY - minY;
    minX -= xRange * 0.05;
    maxX += xRange * 0.05;
    minY -= yRange * 0.1;
    maxY += yRange * 0.1;

    return LayoutBuilder(
      builder: (context, constraints) {
        final width = constraints.maxWidth;
        final height = constraints.maxHeight;

        // Scale factors
        final xScale = width / (maxX - minX);
        final yScale = height / (maxY - minY);

        // Convert data point to canvas position
        Offset pointToOffset(DataPoint point) {
          return Offset(
            (point.x - minX) * xScale,
            height - (point.y - minY) * yScale, // Invert Y axis
          );
        }

        return CustomPaint(
          size: Size(width, height),
          painter: LineChartPainter(
            dataPoints: exercise.dataPoints,
            minX: minX,
            maxX: maxX,
            minY: minY,
            maxY: maxY,
            xAxisLabel: exercise.xAxisLabel,
            yAxisLabel: exercise.yAxisLabel,
            pointToOffset: pointToOffset,
            primaryColor: _primaryColor,
            textColor: _textColor,
          ),
        );
      },
    );
  }

  // Build a bar chart
  Widget _buildBarChart(GraphExercise exercise) {
    // Find max Y value for scaling
    double maxY = 0;
    for (final point in exercise.dataPoints) {
      maxY = math.max(maxY, point.y);
    }

    // Add some padding
    maxY += maxY * 0.1;

    return LayoutBuilder(
      builder: (context, constraints) {
        final width = constraints.maxWidth;
        final height = constraints.maxHeight;

        // Bar width
        final barWidth = width / (exercise.dataPoints.length * 1.5);

        return CustomPaint(
          size: Size(width, height),
          painter: BarChartPainter(
            dataPoints: exercise.dataPoints,
            maxY: maxY,
            barWidth: barWidth,
            xAxisLabel: exercise.xAxisLabel,
            yAxisLabel: exercise.yAxisLabel,
            primaryColor: _primaryColor,
            textColor: _textColor,
          ),
        );
      },
    );
  }

  // Build a scatter plot
  Widget _buildScatterPlot(GraphExercise exercise) {
    // Find min and max values for scaling
    double minX = double.infinity;
    double maxX = double.negativeInfinity;
    double minY = double.infinity;
    double maxY = double.negativeInfinity;

    for (final point in exercise.dataPoints) {
      minX = math.min(minX, point.x);
      maxX = math.max(maxX, point.x);
      minY = math.min(minY, point.y);
      maxY = math.max(maxY, point.y);
    }

    // Add some padding
    final xRange = maxX - minX;
    final yRange = maxY - minY;
    minX -= xRange * 0.05;
    maxX += xRange * 0.05;
    minY -= yRange * 0.1;
    maxY += yRange * 0.1;

    return LayoutBuilder(
      builder: (context, constraints) {
        final width = constraints.maxWidth;
        final height = constraints.maxHeight;

        // Scale factors
        final xScale = width / (maxX - minX);
        final yScale = height / (maxY - minY);

        // Convert data point to canvas position
        Offset pointToOffset(DataPoint point) {
          return Offset(
            (point.x - minX) * xScale,
            height - (point.y - minY) * yScale, // Invert Y axis
          );
        }

        return CustomPaint(
          size: Size(width, height),
          painter: ScatterPlotPainter(
            dataPoints: exercise.dataPoints,
            minX: minX,
            maxX: maxX,
            minY: minY,
            maxY: maxY,
            xAxisLabel: exercise.xAxisLabel,
            yAxisLabel: exercise.yAxisLabel,
            pointToOffset: pointToOffset,
            primaryColor: _primaryColor,
            textColor: _textColor,
          ),
        );
      },
    );
  }

  @override
  Widget build(BuildContext context) {
    final exercise = _exercises[_currentExerciseIndex];

    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: _backgroundColor,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withAlpha(50),
            spreadRadius: 1,
            blurRadius: 3,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Title
          Text(
            widget.data['title'] ?? 'Graph Interpretation Exercise',
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
              color: _textColor,
            ),
          ),

          const SizedBox(height: 8),

          // Exercise navigation
          Row(
            children: [
              Text(
                'Exercise ${_currentExerciseIndex + 1} of ${_exercises.length}: ${exercise.title}',
                style: TextStyle(
                  fontSize: 14,
                  fontWeight: FontWeight.bold,
                  color: _primaryColor,
                ),
              ),
              const Spacer(),
              IconButton(
                icon: Icon(_showExplanation ? Icons.info_outline : Icons.info),
                onPressed: _hasSubmitted ? _toggleExplanation : null,
                tooltip: _showExplanation ? 'Hide Explanation' : 'Show Explanation',
                color: _secondaryColor,
              ),
            ],
          ),

          const SizedBox(height: 8),

          // Exercise description
          Text(
            exercise.description,
            style: TextStyle(
              fontSize: 14,
              fontStyle: FontStyle.italic,
              color: _textColor.withAlpha(180),
            ),
          ),

          const SizedBox(height: 16),

          // Graph visualization
          Container(
            height: 200,
            width: double.infinity,
            padding: const EdgeInsets.all(8),
            decoration: BoxDecoration(
              color: _backgroundColor,
              borderRadius: BorderRadius.circular(8),
              border: Border.all(color: _primaryColor.withAlpha(75)),
            ),
            child: _buildGraph(exercise),
          ),

          const SizedBox(height: 16),

          // Questions
          Expanded(
            child: SingleChildScrollView(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'Questions:',
                    style: TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.bold,
                      color: _primaryColor,
                    ),
                  ),

                  const SizedBox(height: 8),

                  // Build questions
                  ...exercise.questions.asMap().entries.map((entry) {
                    final index = entry.key;
                    final question = entry.value;
                    return _buildQuestionCard(index, question);
                  }).toList(),

                  const SizedBox(height: 16),

                  // Submit button
                  Center(
                    child: ElevatedButton(
                      onPressed: _allQuestionsAnswered() && !_hasSubmitted ? _submitAnswers : null,
                      style: ElevatedButton.styleFrom(
                        backgroundColor: _accentColor,
                        foregroundColor: Colors.white,
                        padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
                      ),
                      child: const Text('Submit Answers'),
                    ),
                  ),

                  const SizedBox(height: 16),

                  // Feedback
                  if (_hasSubmitted)
                    Container(
                      padding: const EdgeInsets.all(12),
                      decoration: BoxDecoration(
                        color: _isCorrect ? _accentColor.withAlpha(30) : _secondaryColor.withAlpha(30),
                        borderRadius: BorderRadius.circular(8),
                        border: Border.all(
                          color: _isCorrect ? _accentColor.withAlpha(75) : _secondaryColor.withAlpha(75),
                        ),
                      ),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            _isCorrect ? 'Correct! Well done!' : 'Some answers are incorrect. Review the explanations and try again.',
                            style: TextStyle(
                              fontSize: 16,
                              fontWeight: FontWeight.bold,
                              color: _isCorrect ? _accentColor : _secondaryColor,
                            ),
                          ),
                        ],
                      ),
                    ),

                  // Explanation
                  if (_showExplanation && _hasSubmitted)
                    Container(
                      padding: const EdgeInsets.all(12),
                      margin: const EdgeInsets.only(top: 16),
                      decoration: BoxDecoration(
                        color: _secondaryColor.withAlpha(25),
                        borderRadius: BorderRadius.circular(8),
                        border: Border.all(color: _secondaryColor.withAlpha(75)),
                      ),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            'Explanation:',
                            style: TextStyle(
                              fontSize: 16,
                              fontWeight: FontWeight.bold,
                              color: _secondaryColor,
                            ),
                          ),
                          const SizedBox(height: 8),
                          Text(
                            exercise.explanation,
                            style: TextStyle(
                              fontSize: 14,
                              color: _textColor,
                            ),
                          ),
                        ],
                      ),
                    ),
                ],
              ),
            ),
          ),

          const SizedBox(height: 16),

          // Navigation buttons
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              ElevatedButton(
                onPressed: _currentExerciseIndex > 0 ? _previousExercise : null,
                style: ElevatedButton.styleFrom(
                  backgroundColor: _secondaryColor,
                  foregroundColor: Colors.white,
                ),
                child: const Text('Previous'),
              ),
              ElevatedButton(
                onPressed: (_hasSubmitted && _isCorrect) ?
                           (_currentExerciseIndex < _exercises.length - 1 ? _nextExercise : (_isCompleted ? _resetWidget : _nextExercise)) :
                           null,
                style: ElevatedButton.styleFrom(
                  backgroundColor: _primaryColor,
                  foregroundColor: Colors.white,
                ),
                child: Text(_currentExerciseIndex < _exercises.length - 1 ? 'Next' : (_isCompleted ? 'Restart' : 'Complete')),
              ),
            ],
          ),

          // Widget name tag at the bottom
          if (widget.data['showNameTag'] ?? true)
            Padding(
              padding: const EdgeInsets.only(top: 8),
              child: Text(
                'InteractiveGraphInterpretationExerciseWidget',
                style: TextStyle(
                  color: Colors.grey[600],
                  fontSize: 12,
                  fontStyle: FontStyle.italic,
                ),
              ),
            ),
        ],
      ),
    );
  }
}
