import 'package:flutter/material.dart';
import 'dart:math' as math;

/// A widget that allows users to rotate shapes around a point
class RotationInteractiveGameWidget extends StatefulWidget {
  final Map<String, dynamic> data;
  final Function(bool)? onStateChanged;

  const RotationInteractiveGameWidget({
    Key? key,
    required this.data,
    this.onStateChanged,
  }) : super(key: key);

  /// Creates the widget from JSON data
  static RotationInteractiveGameWidget fromData(Map<String, dynamic> data) {
    return RotationInteractiveGameWidget(
      data: data,
    );
  }

  @override
  State<RotationInteractiveGameWidget> createState() => _RotationInteractiveGameWidgetState();
}

class _RotationInteractiveGameWidgetState extends State<RotationInteractiveGameWidget> with SingleTickerProviderStateMixin {
  // Rotation properties
  late String _shape;
  late String _centerOfRotation;
  late double _angleDegrees;
  late String _direction;
  
  // Current rotation angle
  double _currentAngle = 0.0;
  
  // Colors
  late Color _shapeColor;
  late Color _rotatedShapeColor;
  late Color _centerColor;
  late Color _textColor;
  
  // Game state
  bool _isRotating = false;
  bool _isCompleted = false;
  
  // Animation controller
  late AnimationController _animationController;
  late Animation<double> _animation;

  @override
  void initState() {
    super.initState();
    _initializeWidget();
    
    // Initialize animation controller
    _animationController = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 1000),
    );
    
    _animation = Tween<double>(
      begin: 0.0,
      end: _getTargetAngleRadians(),
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeInOut,
    ))
      ..addListener(() {
        setState(() {
          _currentAngle = _animation.value;
        });
      })
      ..addStatusListener((status) {
        if (status == AnimationStatus.completed) {
          setState(() {
            _isRotating = false;
            _isCompleted = true;
          });
          
          // Notify parent about completion
          widget.onStateChanged?.call(true);
        }
      });
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  void _initializeWidget() {
    // Set shape
    _shape = widget.data['shape'] ?? 'arrow';
    
    // Set center of rotation
    _centerOfRotation = widget.data['center_of_rotation'] ?? 'origin';
    
    // Set angle
    _angleDegrees = (widget.data['angle_degrees'] ?? 90).toDouble();
    
    // Set direction
    _direction = widget.data['direction'] ?? 'clockwise';
    
    // Set colors
    _shapeColor = _parseColor(widget.data['shapeColor']) ?? Colors.blue;
    _rotatedShapeColor = _parseColor(widget.data['rotatedShapeColor']) ?? Colors.blue.withOpacity(0.5);
    _centerColor = _parseColor(widget.data['centerColor']) ?? Colors.red;
    _textColor = _parseColor(widget.data['textColor']) ?? Colors.black87;
  }

  // Parse color from hex string
  Color? _parseColor(String? hexString) {
    if (hexString == null) return null;
    
    hexString = hexString.replaceAll('#', '');
    if (hexString.length == 6) {
      hexString = 'FF$hexString';
    }
    
    return Color(int.parse(hexString, radix: 16));
  }

  // Get target angle in radians
  double _getTargetAngleRadians() {
    // Convert degrees to radians
    double angleRadians = _angleDegrees * (math.pi / 180);
    
    // Adjust for direction
    if (_direction == 'clockwise') {
      angleRadians = -angleRadians;
    }
    
    return angleRadians;
  }

  // Start rotation animation
  void _startRotation() {
    if (_isRotating || _isCompleted) return;
    
    setState(() {
      _isRotating = true;
    });
    
    _animationController.forward();
  }

  // Reset rotation
  void _resetRotation() {
    setState(() {
      _isRotating = false;
      _isCompleted = false;
      _currentAngle = 0.0;
    });
    
    _animationController.reset();
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withOpacity(0.2),
            spreadRadius: 1,
            blurRadius: 3,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Title
          Text(
            widget.data['title'] ?? 'Rotation Interactive Game',
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
              color: _textColor,
            ),
          ),
          
          const SizedBox(height: 8),
          
          // Description
          if (widget.data['description'] != null)
            Text(
              widget.data['description'],
              style: TextStyle(
                fontSize: 14,
                color: _textColor.withOpacity(0.8),
              ),
            ),
          
          const SizedBox(height: 16),
          
          // Rotation information
          Container(
            padding: const EdgeInsets.all(12),
            decoration: BoxDecoration(
              color: Colors.grey.withOpacity(0.1),
              borderRadius: BorderRadius.circular(8),
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'Rotation Parameters:',
                  style: TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                    color: _textColor,
                  ),
                ),
                const SizedBox(height: 8),
                Text(
                  'Angle: ${_angleDegrees.toInt()}° ${_direction == "clockwise" ? "clockwise" : "counterclockwise"}',
                  style: TextStyle(
                    color: _textColor,
                  ),
                ),
                Text(
                  'Center: ${_centerOfRotation == "origin" ? "Origin (0, 0)" : _centerOfRotation}',
                  style: TextStyle(
                    color: _textColor,
                  ),
                ),
                Text(
                  'Shape: ${_shape.substring(0, 1).toUpperCase()}${_shape.substring(1)}',
                  style: TextStyle(
                    color: _textColor,
                  ),
                ),
              ],
            ),
          ),
          
          const SizedBox(height: 16),
          
          // Rotation visualization
          SizedBox(
            height: 300,
            child: CustomPaint(
              painter: RotationGamePainter(
                shape: _shape,
                centerOfRotation: _centerOfRotation,
                currentAngle: _currentAngle,
                shapeColor: _shapeColor,
                rotatedShapeColor: _rotatedShapeColor,
                centerColor: _centerColor,
                showRotatedShape: _isCompleted || _isRotating,
              ),
              child: Container(),
            ),
          ),
          
          const SizedBox(height: 16),
          
          // Controls
          Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              ElevatedButton.icon(
                onPressed: _isRotating ? null : _startRotation,
                icon: const Icon(Icons.rotate_right),
                label: Text(_isCompleted ? 'Rotated!' : 'Rotate'),
                style: ElevatedButton.styleFrom(
                  backgroundColor: _isCompleted ? Colors.green : Colors.blue,
                  padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
                ),
              ),
              const SizedBox(width: 16),
              ElevatedButton.icon(
                onPressed: _isRotating ? null : _resetRotation,
                icon: const Icon(Icons.refresh),
                label: const Text('Reset'),
                style: ElevatedButton.styleFrom(
                  backgroundColor: Colors.grey,
                  padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
                ),
              ),
            ],
          ),
          
          // Feedback
          if (_isCompleted && widget.data['feedback_correct'] != null)
            Padding(
              padding: const EdgeInsets.only(top: 16),
              child: Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: Colors.green.withOpacity(0.1),
                  borderRadius: BorderRadius.circular(8),
                  border: Border.all(color: Colors.green),
                ),
                child: Row(
                  children: [
                    const Icon(
                      Icons.check_circle,
                      color: Colors.green,
                    ),
                    const SizedBox(width: 8),
                    Expanded(
                      child: Text(
                        widget.data['feedback_correct'],
                        style: const TextStyle(
                          color: Colors.green,
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ),
          
          // Widget name tag
          if (widget.data['showNameTag'] ?? true)
            Padding(
              padding: const EdgeInsets.only(top: 8),
              child: Text(
                'RotationInteractiveGameWidget',
                style: TextStyle(
                  color: Colors.grey[600],
                  fontSize: 12,
                  fontStyle: FontStyle.italic,
                ),
              ),
            ),
        ],
      ),
    );
  }
}

/// Custom painter for the rotation game
class RotationGamePainter extends CustomPainter {
  final String shape;
  final String centerOfRotation;
  final double currentAngle;
  final Color shapeColor;
  final Color rotatedShapeColor;
  final Color centerColor;
  final bool showRotatedShape;
  
  RotationGamePainter({
    required this.shape,
    required this.centerOfRotation,
    required this.currentAngle,
    required this.shapeColor,
    required this.rotatedShapeColor,
    required this.centerColor,
    required this.showRotatedShape,
  });
  
  @override
  void paint(Canvas canvas, Size size) {
    // Draw coordinate system
    _drawCoordinateSystem(canvas, size);
    
    // Get center of rotation
    final center = _getCenterOfRotation(size);
    
    // Draw center of rotation
    final centerPaint = Paint()
      ..color = centerColor
      ..style = PaintingStyle.fill;
    
    canvas.drawCircle(center, 5, centerPaint);
    
    // Draw original shape
    final shapePaint = Paint()
      ..color = shapeColor
      ..style = PaintingStyle.fill;
    
    final shapePath = _getShapePath(size, center);
    canvas.drawPath(shapePath, shapePaint);
    
    // Draw rotated shape if needed
    if (showRotatedShape) {
      final rotatedShapePaint = Paint()
        ..color = rotatedShapeColor
        ..style = PaintingStyle.fill;
      
      // Save canvas state
      canvas.save();
      
      // Translate to center of rotation
      canvas.translate(center.dx, center.dy);
      
      // Rotate canvas
      canvas.rotate(currentAngle);
      
      // Translate back
      canvas.translate(-center.dx, -center.dy);
      
      // Draw rotated shape
      canvas.drawPath(shapePath, rotatedShapePaint);
      
      // Restore canvas state
      canvas.restore();
      
      // Draw rotation arc
      final arcPaint = Paint()
        ..color = centerColor
        ..style = PaintingStyle.stroke
        ..strokeWidth = 2;
      
      final arcRadius = 30.0;
      final arcRect = Rect.fromCircle(center: center, radius: arcRadius);
      
      // Determine start and end angles
      double startAngle = -math.pi / 2; // Start from top
      double sweepAngle = currentAngle;
      
      canvas.drawArc(
        arcRect,
        startAngle,
        sweepAngle,
        false,
        arcPaint,
      );
    }
  }
  
  // Draw coordinate system
  void _drawCoordinateSystem(Canvas canvas, Size size) {
    final axisPaint = Paint()
      ..color = Colors.grey
      ..style = PaintingStyle.stroke
      ..strokeWidth = 1;
    
    final gridPaint = Paint()
      ..color = Colors.grey.withOpacity(0.3)
      ..style = PaintingStyle.stroke
      ..strokeWidth = 0.5;
    
    // Draw grid
    final gridSpacing = 20.0;
    for (double x = 0; x < size.width; x += gridSpacing) {
      canvas.drawLine(
        Offset(x, 0),
        Offset(x, size.height),
        gridPaint,
      );
    }
    
    for (double y = 0; y < size.height; y += gridSpacing) {
      canvas.drawLine(
        Offset(0, y),
        Offset(size.width, y),
        gridPaint,
      );
    }
    
    // Draw axes
    canvas.drawLine(
      Offset(0, size.height / 2),
      Offset(size.width, size.height / 2),
      axisPaint,
    );
    
    canvas.drawLine(
      Offset(size.width / 2, 0),
      Offset(size.width / 2, size.height),
      axisPaint,
    );
  }
  
  // Get center of rotation based on setting
  Offset _getCenterOfRotation(Size size) {
    switch (centerOfRotation) {
      case 'origin':
        return Offset(size.width / 2, size.height / 2);
      case 'top_left':
        return const Offset(50, 50);
      case 'top_right':
        return Offset(size.width - 50, 50);
      case 'bottom_left':
        return Offset(50, size.height - 50);
      case 'bottom_right':
        return Offset(size.width - 50, size.height - 50);
      default:
        return Offset(size.width / 2, size.height / 2);
    }
  }
  
  // Get shape path based on shape type
  Path _getShapePath(Size size, Offset center) {
    switch (shape) {
      case 'arrow':
        return _getArrowPath(center);
      case 'triangle':
        return _getTrianglePath(center);
      case 'rectangle':
        return _getRectanglePath(center);
      case 'letter_F':
        return _getLetterFPath(center);
      default:
        return _getArrowPath(center);
    }
  }
  
  // Create arrow shape
  Path _getArrowPath(Offset center) {
    final path = Path();
    
    // Arrow pointing up by default
    path.moveTo(center.dx, center.dy - 60); // Tip
    path.lineTo(center.dx + 20, center.dy - 30); // Right corner
    path.lineTo(center.dx + 10, center.dy - 30); // Right inner corner
    path.lineTo(center.dx + 10, center.dy + 30); // Bottom right
    path.lineTo(center.dx - 10, center.dy + 30); // Bottom left
    path.lineTo(center.dx - 10, center.dy - 30); // Left inner corner
    path.lineTo(center.dx - 20, center.dy - 30); // Left corner
    path.close();
    
    return path;
  }
  
  // Create triangle shape
  Path _getTrianglePath(Offset center) {
    final path = Path();
    
    path.moveTo(center.dx, center.dy - 40); // Top
    path.lineTo(center.dx + 40, center.dy + 40); // Bottom right
    path.lineTo(center.dx - 40, center.dy + 40); // Bottom left
    path.close();
    
    return path;
  }
  
  // Create rectangle shape
  Path _getRectanglePath(Offset center) {
    final path = Path();
    
    path.addRect(Rect.fromCenter(
      center: center,
      width: 80,
      height: 60,
    ));
    
    return path;
  }
  
  // Create letter F shape
  Path _getLetterFPath(Offset center) {
    final path = Path();
    
    path.moveTo(center.dx - 20, center.dy - 40); // Top left
    path.lineTo(center.dx + 20, center.dy - 40); // Top right
    path.lineTo(center.dx + 20, center.dy - 30); // Top right inner
    path.lineTo(center.dx - 10, center.dy - 30); // Top left inner
    path.lineTo(center.dx - 10, center.dy - 10); // Middle left
    path.lineTo(center.dx + 10, center.dy - 10); // Middle right
    path.lineTo(center.dx + 10, center.dy); // Middle right inner
    path.lineTo(center.dx - 10, center.dy); // Middle left inner
    path.lineTo(center.dx - 10, center.dy + 40); // Bottom
    path.lineTo(center.dx - 20, center.dy + 40); // Bottom left
    path.close();
    
    return path;
  }
  
  @override
  bool shouldRepaint(covariant RotationGamePainter oldDelegate) {
    return oldDelegate.currentAngle != currentAngle ||
           oldDelegate.showRotatedShape != showRotatedShape;
  }
}
