import 'package:flutter/material.dart';
import 'dart:math' as math;

/// A widget that demonstrates solving one-step equations involving multiplication.
class InteractiveMultiplicationEquationSolverWidget extends StatefulWidget {
  final Map<String, dynamic> data;
  final Function(bool)? onStateChanged;
  final Color primaryColor;
  final Color secondaryColor;
  final Color backgroundColor;
  final Color textColor;

  const InteractiveMultiplicationEquationSolverWidget({
    super.key,
    this.data = const {},
    this.onStateChanged,
    this.primaryColor = Colors.blue,
    this.secondaryColor = Colors.orange,
    this.backgroundColor = Colors.white,
    this.textColor = Colors.black87,
  });

  @override
  State<InteractiveMultiplicationEquationSolverWidget> createState() =>
      _InteractiveMultiplicationEquationSolverWidgetState();
}

class _InteractiveMultiplicationEquationSolverWidgetState
    extends State<InteractiveMultiplicationEquationSolverWidget>
    with SingleTickerProviderStateMixin {
  // Animation controller
  late AnimationController _animationController;
  late Animation<double> _animation;

  // State variables
  bool _isCompleted = false;
  bool _isAnimating = false;
  int _currentStep = 0;
  int _currentEquationIndex = 0;
  String? _userAnswer;
  bool _showSolution = false;
  bool _isCorrect = false;
  String? _feedbackMessage;

  // Equation data
  List<EquationData> _equations = [];
  late EquationData _currentEquation;

  @override
  void initState() {
    super.initState();

    // Initialize animation controller
    _animationController = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 800),
    );

    _animation = CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeInOut,
    );

    // Add listener to animation controller
    _animationController.addStatusListener((status) {
      if (status == AnimationStatus.completed) {
        setState(() {
          _currentStep++;
          if (_currentStep < _currentEquation.steps.length) {
            _animationController.reset();
            _animationController.forward();
          } else {
            _isAnimating = false;
          }
        });
      }
    });

    // Initialize equations
    _initializeEquations();
    _currentEquation = _equations[_currentEquationIndex];
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  void _initializeEquations() {
    // Check if equations are provided in the data
    if (widget.data.containsKey('equations') &&
        widget.data['equations'] is List &&
        widget.data['equations'].isNotEmpty) {

      final equationsData = widget.data['equations'] as List;
      for (final eqData in equationsData) {
        if (eqData is Map<String, dynamic>) {
          final equation = EquationData.fromJson(eqData);
          _equations.add(equation);
        }
      }
    }

    // If no equations were provided, create default ones
    if (_equations.isEmpty) {
      _equations = [
        EquationData(
          initialEquation: 'x/4 = 5',
          variableName: 'x',
          solution: '20',
          operation: 'multiplication',
          steps: [
            EquationStep(
              equation: 'x/4 = 5',
              explanation: 'We start with the original equation.',
              operation: 'Original equation',
            ),
            EquationStep(
              equation: 'x/4 × 4 = 5 × 4',
              explanation: 'To isolate the variable, we multiply both sides by 4.',
              operation: 'Multiply both sides by 4',
            ),
            EquationStep(
              equation: 'x = 20',
              explanation: 'Simplify to get the solution.',
              operation: 'Simplify',
            ),
          ],
        ),
        EquationData(
          initialEquation: 'y/3 = 7',
          variableName: 'y',
          solution: '21',
          operation: 'multiplication',
          steps: [
            EquationStep(
              equation: 'y/3 = 7',
              explanation: 'We start with the original equation.',
              operation: 'Original equation',
            ),
            EquationStep(
              equation: 'y/3 × 3 = 7 × 3',
              explanation: 'To isolate the variable, we multiply both sides by 3.',
              operation: 'Multiply both sides by 3',
            ),
            EquationStep(
              equation: 'y = 21',
              explanation: 'Simplify to get the solution.',
              operation: 'Simplify',
            ),
          ],
        ),
        EquationData(
          initialEquation: 'z/5 = 4',
          variableName: 'z',
          solution: '20',
          operation: 'multiplication',
          steps: [
            EquationStep(
              equation: 'z/5 = 4',
              explanation: 'We start with the original equation.',
              operation: 'Original equation',
            ),
            EquationStep(
              equation: 'z/5 × 5 = 4 × 5',
              explanation: 'To isolate the variable, we multiply both sides by 5.',
              operation: 'Multiply both sides by 5',
            ),
            EquationStep(
              equation: 'z = 20',
              explanation: 'Simplify to get the solution.',
              operation: 'Simplify',
            ),
          ],
        ),
        EquationData(
          initialEquation: '6 = a/2',
          variableName: 'a',
          solution: '12',
          operation: 'multiplication',
          steps: [
            EquationStep(
              equation: '6 = a/2',
              explanation: 'We start with the original equation.',
              operation: 'Original equation',
            ),
            EquationStep(
              equation: '6 × 2 = a/2 × 2',
              explanation: 'To isolate the variable, we multiply both sides by 2.',
              operation: 'Multiply both sides by 2',
            ),
            EquationStep(
              equation: '12 = a',
              explanation: 'Simplify to get the solution.',
              operation: 'Simplify',
            ),
          ],
        ),
        EquationData(
          initialEquation: 'b/8 = 3',
          variableName: 'b',
          solution: '24',
          operation: 'multiplication',
          steps: [
            EquationStep(
              equation: 'b/8 = 3',
              explanation: 'We start with the original equation.',
              operation: 'Original equation',
            ),
            EquationStep(
              equation: 'b/8 × 8 = 3 × 8',
              explanation: 'To isolate the variable, we multiply both sides by 8.',
              operation: 'Multiply both sides by 8',
            ),
            EquationStep(
              equation: 'b = 24',
              explanation: 'Simplify to get the solution.',
              operation: 'Simplify',
            ),
          ],
        ),
      ];
    }
  }

  void _startAnimation() {
    if (_currentEquation.steps.isEmpty) return;

    setState(() {
      _currentStep = 0;
      _isAnimating = true;
      _showSolution = true;
    });

    _animationController.reset();
    _animationController.forward();
  }

  void _stopAnimation() {
    setState(() {
      _isAnimating = false;
    });
    _animationController.stop();
  }

  void _resetAnimation() {
    setState(() {
      _currentStep = 0;
      _isAnimating = false;
      _showSolution = false;
    });
    _animationController.reset();
  }

  void _nextEquation() {
    if (_currentEquationIndex < _equations.length - 1) {
      setState(() {
        _currentEquationIndex++;
        _currentEquation = _equations[_currentEquationIndex];
        _resetAnimation();
        _userAnswer = null;
        _isCorrect = false;
        _feedbackMessage = null;
      });
    } else {
      // All equations completed
      setState(() {
        _isCompleted = true;
      });
      widget.onStateChanged?.call(true);
    }
  }

  void _previousEquation() {
    if (_currentEquationIndex > 0) {
      setState(() {
        _currentEquationIndex--;
        _currentEquation = _equations[_currentEquationIndex];
        _resetAnimation();
        _userAnswer = null;
        _isCorrect = false;
        _feedbackMessage = null;
      });
    }
  }

  void _checkAnswer(String answer) {
    final isCorrect = answer.trim() == _currentEquation.solution.trim();

    setState(() {
      _userAnswer = answer;
      _isCorrect = isCorrect;

      if (isCorrect) {
        _feedbackMessage = 'Correct! ${_currentEquation.variableName} = ${_currentEquation.solution} is the solution.';
      } else {
        _feedbackMessage = 'Not quite. Try again or check the solution.';
      }
    });
  }

  void _resetWidget() {
    setState(() {
      _currentEquationIndex = 0;
      _currentEquation = _equations[_currentEquationIndex];
      _resetAnimation();
      _userAnswer = null;
      _isCorrect = false;
      _feedbackMessage = null;
      _isCompleted = false;
    });
    widget.onStateChanged?.call(false);
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: widget.backgroundColor,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withOpacity(0.2),
            spreadRadius: 1,
            blurRadius: 3,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: _isCompleted ? _buildCompletionScreen() : _buildMainContent(),
    );
  }

  Widget _buildMainContent() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Title and progress indicator
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Text(
              'Multiplication Equation Solver',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
                color: widget.primaryColor,
              ),
            ),
            Text(
              'Equation ${_currentEquationIndex + 1}/${_equations.length}',
              style: TextStyle(
                fontSize: 14,
                color: widget.textColor.withOpacity(0.7),
              ),
            ),
          ],
        ),

        const SizedBox(height: 16),

        // Current equation
        Container(
          width: double.infinity,
          padding: const EdgeInsets.symmetric(vertical: 12, horizontal: 16),
          decoration: BoxDecoration(
            color: widget.primaryColor.withOpacity(0.1),
            borderRadius: BorderRadius.circular(8),
            border: Border.all(color: widget.primaryColor.withOpacity(0.3)),
          ),
          child: Text(
            _currentEquation.initialEquation,
            style: TextStyle(
              fontSize: 24,
              fontWeight: FontWeight.bold,
              color: widget.textColor,
            ),
            textAlign: TextAlign.center,
          ),
        ),

        const SizedBox(height: 24),

        // Instruction
        Text(
          'Solve for ${_currentEquation.variableName}:',
          style: TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.w500,
            color: widget.textColor,
          ),
        ),

        const SizedBox(height: 8),

        // User input area
        if (!_showSolution) ...[
          Row(
            children: [
              Expanded(
                child: TextField(
                  decoration: InputDecoration(
                    labelText: 'Your answer',
                    hintText: 'Enter the value of ${_currentEquation.variableName}',
                    border: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(8),
                    ),
                    suffixIcon: IconButton(
                      icon: const Icon(Icons.check_circle),
                      onPressed: () {
                        if (_userAnswer != null && _userAnswer!.isNotEmpty) {
                          _checkAnswer(_userAnswer!);
                        }
                      },
                    ),
                  ),
                  keyboardType: TextInputType.number,
                  onChanged: (value) {
                    setState(() {
                      _userAnswer = value;
                    });
                  },
                  onSubmitted: (value) {
                    if (value.isNotEmpty) {
                      _checkAnswer(value);
                    }
                  },
                ),
              ),
              const SizedBox(width: 8),
              ElevatedButton(
                onPressed: _startAnimation,
                style: ElevatedButton.styleFrom(
                  backgroundColor: widget.secondaryColor,
                  padding: const EdgeInsets.symmetric(vertical: 16),
                ),
                child: const Text('Show Solution'),
              ),
            ],
          ),

          // Feedback message
          if (_feedbackMessage != null)
            Padding(
              padding: const EdgeInsets.only(top: 8.0),
              child: Text(
                _feedbackMessage!,
                style: TextStyle(
                  color: _isCorrect ? Colors.green : Colors.red,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ),
        ],

        // Solution steps
        if (_showSolution) ...[
          const SizedBox(height: 16),
          Text(
            'Solution Steps:',
            style: TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.bold,
              color: widget.primaryColor,
            ),
          ),
          const SizedBox(height: 8),
          ..._buildSolutionSteps(),
        ],

        const Spacer(),

        // Navigation buttons
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            // Previous button
            ElevatedButton(
              onPressed: _currentEquationIndex > 0 ? _previousEquation : null,
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.grey.shade200,
                foregroundColor: Colors.black87,
              ),
              child: const Text('Previous'),
            ),

            // Next button
            ElevatedButton(
              onPressed: (_isCorrect || _showSolution) ? _nextEquation : null,
              style: ElevatedButton.styleFrom(
                backgroundColor: widget.primaryColor,
                foregroundColor: Colors.white,
              ),
              child: Text(_currentEquationIndex < _equations.length - 1 ? 'Next' : 'Finish'),
            ),
          ],
        ),
      ],
    );
  }

  List<Widget> _buildSolutionSteps() {
    final steps = <Widget>[];
    final totalSteps = _currentEquation.steps.length;

    for (int i = 0; i < totalSteps; i++) {
      final step = _currentEquation.steps[i];
      final isCurrentStep = i == _currentStep && _isAnimating;
      final isPastStep = i <= _currentStep;

      if (!isPastStep && !isCurrentStep) continue;

      steps.add(
        AnimatedOpacity(
          opacity: isPastStep ? 1.0 : _animation.value,
          duration: const Duration(milliseconds: 300),
          child: Container(
            margin: const EdgeInsets.only(bottom: 12),
            padding: const EdgeInsets.all(12),
            decoration: BoxDecoration(
              color: isCurrentStep
                  ? widget.secondaryColor.withOpacity(0.2)
                  : Colors.grey.shade50,
              borderRadius: BorderRadius.circular(8),
              border: Border.all(
                color: isCurrentStep
                    ? widget.secondaryColor
                    : Colors.grey.shade300,
              ),
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Step number and operation
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Text(
                      'Step ${i + 1}',
                      style: TextStyle(
                        fontWeight: FontWeight.bold,
                        color: widget.primaryColor,
                      ),
                    ),
                    Text(
                      step.operation,
                      style: TextStyle(
                        fontStyle: FontStyle.italic,
                        color: widget.textColor.withOpacity(0.7),
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 8),

                // Equation
                Text(
                  step.equation,
                  style: const TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.w500,
                  ),
                  textAlign: TextAlign.center,
                ),
                const SizedBox(height: 4),

                // Explanation
                Text(
                  step.explanation,
                  style: TextStyle(
                    color: widget.textColor.withOpacity(0.8),
                  ),
                ),
              ],
            ),
          ),
        ),
      );
    }

    return steps;
  }

  Widget _buildCompletionScreen() {
    return Column(
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        Icon(
          Icons.check_circle,
          size: 80,
          color: Colors.green,
        ),
        const SizedBox(height: 24),
        Text(
          'Congratulations!',
          style: TextStyle(
            fontSize: 24,
            fontWeight: FontWeight.bold,
            color: widget.primaryColor,
          ),
        ),
        const SizedBox(height: 16),
        Text(
          'You\'ve completed all the one-step multiplication equations!',
          textAlign: TextAlign.center,
          style: TextStyle(
            fontSize: 16,
            color: widget.textColor,
          ),
        ),
        const SizedBox(height: 32),
        ElevatedButton.icon(
          onPressed: _resetWidget,
          icon: const Icon(Icons.refresh),
          label: const Text('Practice Again'),
          style: ElevatedButton.styleFrom(
            backgroundColor: widget.secondaryColor,
            padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
          ),
        ),
      ],
    );
  }
}

/// Data class for equation information
class EquationData {
  final String initialEquation;
  final String variableName;
  final String solution;
  final String operation;
  final List<EquationStep> steps;

  EquationData({
    required this.initialEquation,
    required this.variableName,
    required this.solution,
    required this.operation,
    required this.steps,
  });

  factory EquationData.fromJson(Map<String, dynamic> json) {
    final steps = <EquationStep>[];
    if (json.containsKey('steps') && json['steps'] is List) {
      for (final stepData in json['steps']) {
        if (stepData is Map<String, dynamic>) {
          steps.add(EquationStep.fromJson(stepData));
        }
      }
    }

    return EquationData(
      initialEquation: json['initialEquation'] ?? 'x + 5 = 10',
      variableName: json['variableName'] ?? 'x',
      solution: json['solution'] ?? '5',
      operation: json['operation'] ?? 'subtraction',
      steps: steps,
    );
  }
}

/// Data class for equation solution steps
class EquationStep {
  final String equation;
  final String explanation;
  final String operation;

  EquationStep({
    required this.equation,
    required this.explanation,
    required this.operation,
  });

  factory EquationStep.fromJson(Map<String, dynamic> json) {
    return EquationStep(
      equation: json['equation'] ?? '',
      explanation: json['explanation'] ?? '',
      operation: json['operation'] ?? '',
    );
  }
}
