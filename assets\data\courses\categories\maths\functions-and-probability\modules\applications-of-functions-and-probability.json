{"id": "applications-of-functions-and-probability", "title": "APPLICATIONS OF FUNCTIONS AND PROBABILITY", "description": "Explore real-world scenarios where functions model probabilistic events.", "order": 5, "lessons": [{"id": "modeling-random-processes", "title": "Modeling Random Processes with Functions", "description": "Use functions to describe outcomes.", "order": 1, "type": "interactive_lesson", "estimatedTimeMinutes": 10, "contentBlocks": [{"id": "mrp-screen1-intro", "type": "lesson_screen", "order": 1, "estimatedTimeSeconds": 30, "content": {"headline": "Functions Meet Probability: Modeling Randomness", "body_md": "When we combine functions with probability, we gain powerful tools for modeling random processes. These mathematical models help us understand, predict, and make decisions about uncertain events.", "visual": {"type": "giphy_search", "value": "random process model"}, "interactive_element": {"type": "button", "text": "Let's Explore Random Models!", "action": "next_screen"}}}, {"id": "mrp-screen2-definition", "type": "lesson_screen", "order": 2, "estimatedTimeSeconds": 60, "content": {"headline": "What is a Random Process?", "body_md": "A random process is a phenomenon that evolves over time with some element of randomness. Examples include:\n\n- Stock market prices\n- Weather patterns\n- Number of customers entering a store\n- Spread of a disease\n- Radioactive decay\n\nFunctions help us model how these processes behave and evolve.", "visual": {"type": "unsplash_search", "value": "stock market graph"}, "interactive_element": {"type": "multiple_choice_text", "question": "Which of these is NOT typically modeled as a random process?", "options": [{"id": "a", "text": "The number of emails you receive each day", "is_correct": false, "feedback_incorrect": "Incorrect. Email arrivals are random and can be modeled as a stochastic process."}, {"id": "b", "text": "The planetary orbits in our solar system", "is_correct": true, "feedback_correct": "Correct! Planetary orbits follow deterministic physical laws and are highly predictable, not random."}, {"id": "c", "text": "The spread of a virus in a population", "is_correct": false, "feedback_incorrect": "Incorrect. Virus spread involves random interactions between people and is typically modeled as a stochastic process."}, {"id": "d", "text": "The fluctuations in a currency's exchange rate", "is_correct": false, "feedback_incorrect": "Incorrect. Currency exchange rates involve unpredictable market forces and are typically modeled as random processes."}]}}}, {"id": "mrp-screen3-functions", "type": "lesson_screen", "order": 3, "estimatedTimeSeconds": 60, "content": {"headline": "Functions for Random Processes", "body_md": "Several types of functions are commonly used to model random processes:\n\n- **Probability Mass Functions (PMFs)**: For discrete outcomes\n- **Probability Density Functions (PDFs)**: For continuous outcomes\n- **Cumulative Distribution Functions (CDFs)**: For finding probabilities of ranges\n- **Moment Generating Functions**: For calculating moments (mean, variance, etc.)\n- **Characteristic Functions**: For analyzing distributions theoretically", "visual": {"type": "giphy_search", "value": "probability function"}, "interactive_element": {"type": "multiple_choice_text", "question": "Which function would be most appropriate for modeling the exact time until the next earthquake?", "options": [{"id": "a", "text": "Probability Mass Function (PMF)", "is_correct": false, "feedback_incorrect": "Incorrect. PMFs are for discrete random variables, but time is continuous."}, {"id": "b", "text": "Probability Density Function (PDF)", "is_correct": true, "feedback_correct": "Correct! Since time is a continuous variable, a PDF is appropriate for modeling the distribution of possible earthquake times."}, {"id": "c", "text": "Linear function", "is_correct": false, "feedback_incorrect": "Incorrect. Linear functions don't capture the probabilistic nature of earthquake timing."}, {"id": "d", "text": "Quadratic function", "is_correct": false, "feedback_incorrect": "Incorrect. Quadratic functions don't capture the probabilistic nature of earthquake timing."}]}}}, {"id": "mrp-screen4-examples", "type": "lesson_screen", "order": 4, "estimatedTimeSeconds": 60, "content": {"headline": "Real-World Examples", "body_md": "Here are some examples of functions modeling random processes:\n\n- **Poisson Process**: Models random events occurring independently at a constant average rate (e.g., customer arrivals, website hits)\n- **Markov Chains**: Models systems that transition between states with certain probabilities (e.g., weather patterns, game states)\n- **Brownian Motion**: Models continuous random movement (e.g., stock prices, particle movement)\n- **Exponential Decay**: Models time until an event occurs (e.g., radioactive decay, equipment failure)", "visual": {"type": "unsplash_search", "value": "random walk graph"}, "interactive_element": {"type": "multiple_choice_text", "question": "A call center receives an average of 12 calls per hour. Which model would best describe the number of calls received in a given hour?", "options": [{"id": "a", "text": "Normal distribution", "is_correct": false, "feedback_incorrect": "Incorrect. While this could be an approximation, there's a more appropriate model for counting random arrivals."}, {"id": "b", "text": "Exponential distribution", "is_correct": false, "feedback_incorrect": "Incorrect. Exponential distribution models the time between arrivals, not the count of arrivals."}, {"id": "c", "text": "Poisson distribution", "is_correct": true, "feedback_correct": "Correct! The Poisson distribution is ideal for modeling the number of events occurring in a fixed time period at a known average rate."}, {"id": "d", "text": "Uniform distribution", "is_correct": false, "feedback_incorrect": "Incorrect. A uniform distribution would suggest all possible numbers of calls are equally likely, which is unrealistic."}]}}}, {"id": "mrp-screen5-conclusion", "type": "lesson_screen", "order": 5, "estimatedTimeSeconds": 30, "content": {"headline": "Modeling Randomness: The Power of Mathematical Functions", "body_md": "Great job! You now understand how functions can model random processes. This powerful connection between functions and probability allows us to analyze, predict, and make decisions about complex, uncertain systems in the real world.", "visual": {"type": "unsplash_search", "value": "probability model"}, "interactive_element": {"type": "button", "text": "Continue to Next Lesson", "action": "next_lesson"}}}]}, {"id": "linear-functions-in-probability", "title": "Linear Functions in Probability (e.g., Expected Value)", "description": "Apply linear relationships to probabilistic scenarios.", "order": 2, "type": "interactive_lesson", "estimatedTimeMinutes": 10, "contentBlocks": [{"id": "lfp-screen1-intro", "type": "lesson_screen", "order": 1, "estimatedTimeSeconds": 30, "content": {"headline": "Linear Functions and Probability", "body_md": "Linear functions play a special role in probability theory. They help us calculate expected values, understand relationships between random variables, and make predictions about uncertain outcomes.", "visual": {"type": "giphy_search", "value": "linear function probability"}, "interactive_element": {"type": "button", "text": "Let's Explore Linear Probability!", "action": "next_screen"}}}, {"id": "lfp-screen2-expected-value", "type": "lesson_screen", "order": 2, "estimatedTimeSeconds": 60, "content": {"headline": "Expected Value as a Linear Function", "body_md": "The expected value of a random variable X is a linear function of X. This means:\n\n- E[aX + b] = a·E[X] + b\n- E[X + Y] = E[X] + E[Y]\n\nThese properties make expected value calculations straightforward and powerful.", "visual": {"type": "unsplash_search", "value": "expected value probability"}, "interactive_element": {"type": "text_input_quick", "question": "If E[X] = 5 and E[Y] = 3, what is E[2X - 4Y + 7]?", "correct_answer_regex": "^-2$|^-2\\.0$", "placeholder": "Enter your answer", "feedback_correct": "Correct! E[2X - 4Y + 7] = 2·E[X] - 4·E[Y] + 7 = 2(5) - 4(3) + 7 = 10 - 12 + 7 = -2", "feedback_incorrect": "Not quite. Use the linearity property: E[2X - 4Y + 7] = 2·E[X] - 4·E[Y] + 7 = 2(5) - 4(3) + 7 = 10 - 12 + 7 = -2"}}}, {"id": "lfp-screen3-linear-combinations", "type": "lesson_screen", "order": 3, "estimatedTimeSeconds": 60, "content": {"headline": "Linear Combinations of Random Variables", "body_md": "Many practical applications involve linear combinations of random variables:\n\n- **Portfolio returns**: Total return = w₁R₁ + w₂R₂ + ... + wₙRₙ\n- **Sample mean**: X̄ = (X₁ + X₂ + ... + Xₙ)/n\n- **Weighted averages**: Y = a₁X₁ + a₂X₂ + ... + aₙXₙ\n- **Linear transformations**: Y = aX + b\n\nThe linearity of expectation makes these easy to analyze.", "visual": {"type": "giphy_search", "value": "stock portfolio graph"}, "interactive_element": {"type": "multiple_choice_text", "question": "An investment portfolio has 30% in Stock A with expected return 5%, 50% in Stock B with expected return 8%, and 20% in Stock C with expected return 3%. What is the expected return of the portfolio?", "options": [{"id": "a", "text": "5.3%", "is_correct": false, "feedback_incorrect": "Incorrect. Calculate the weighted average: 0.3(5%) + 0.5(8%) + 0.2(3%)"}, {"id": "b", "text": "6.1%", "is_correct": true, "feedback_correct": "Correct! E[Return] = 0.3(5%) + 0.5(8%) + 0.2(3%) = 1.5% + 4% + 0.6% = 6.1%"}, {"id": "c", "text": "5.33%", "is_correct": false, "feedback_incorrect": "Incorrect. This is the simple average (5% + 8% + 3%)/3, not the weighted average."}, {"id": "d", "text": "16%", "is_correct": false, "feedback_incorrect": "Incorrect. This is the sum of the returns, not the weighted average."}]}}}, {"id": "lfp-screen4-variance", "type": "lesson_screen", "order": 4, "estimatedTimeSeconds": 60, "content": {"headline": "Variance and Linear Functions", "body_md": "While expected value is linear, variance follows different rules for linear transformations:\n\n- Var(aX + b) = a²·Var(X)\n- Var(X + Y) = Var(X) + Var(Y) + 2·Cov(X,Y)\n\nFor independent random variables, Cov(X,Y) = 0, so:\n- Var(X + Y) = Var(X) + Var(Y) when X and Y are independent", "visual": {"type": "unsplash_search", "value": "variance statistics"}, "interactive_element": {"type": "text_input_quick", "question": "If Var(X) = 4, what is Var(3X + 2)?", "correct_answer_regex": "^36$", "placeholder": "Enter your answer", "feedback_correct": "Correct! Var(3X + 2) = 3²·Var(X) = 9 × 4 = 36", "feedback_incorrect": "Not quite. Use the formula Var(aX + b) = a²·Var(X). Note that adding a constant (2) doesn't affect the variance."}}}, {"id": "lfp-screen5-conclusion", "type": "lesson_screen", "order": 5, "estimatedTimeSeconds": 30, "content": {"headline": "Linear Functions: Simplifying Probability Calculations", "body_md": "Excellent! You now understand how linear functions interact with probability concepts like expected value and variance. These relationships are fundamental in statistics, finance, engineering, and many other fields.", "visual": {"type": "unsplash_search", "value": "linear function probability"}, "interactive_element": {"type": "button", "text": "Continue to Next Lesson", "action": "next_lesson"}}}]}, {"id": "exponential-functions-in-probability", "title": "Exponential Functions in Probability (e.g., Decay)", "description": "Model probabilistic decay processes.", "order": 3, "type": "interactive_lesson", "estimatedTimeMinutes": 10, "contentBlocks": [{"id": "efp-screen1-intro", "type": "lesson_screen", "order": 1, "estimatedTimeSeconds": 30, "content": {"headline": "Exponential Functions and Probability", "body_md": "Exponential functions are perfect for modeling many probabilistic phenomena, especially those involving growth or decay over time. They appear in scenarios ranging from radioactive decay to population growth to compound interest.", "visual": {"type": "giphy_search", "value": "exponential decay graph"}, "interactive_element": {"type": "button", "text": "Let's Explore Exponential Probability!", "action": "next_screen"}}}, {"id": "efp-screen2-exponential-distribution", "type": "lesson_screen", "order": 2, "estimatedTimeSeconds": 60, "content": {"headline": "The Exponential Distribution", "body_md": "The exponential distribution models the time between events in a Poisson process. Its probability density function is:\n\nf(x) = λe^(-λx) for x ≥ 0\n\nWhere λ (lambda) is the rate parameter. The exponential distribution has several key properties:\n- Mean (expected value): 1/λ\n- Variance: 1/λ²\n- Memoryless property: P(X > s + t | X > s) = P(X > t)", "visual": {"type": "unsplash_search", "value": "exponential distribution"}, "interactive_element": {"type": "multiple_choice_text", "question": "If customers arrive at a store according to a Poisson process with an average of 5 customers per hour, what is the expected time between customer arrivals?", "options": [{"id": "a", "text": "5 hours", "is_correct": false, "feedback_incorrect": "Incorrect. The expected time is 1/λ, where λ is the rate parameter (5 customers per hour)."}, {"id": "b", "text": "1/5 hour (12 minutes)", "is_correct": true, "feedback_correct": "Correct! The expected time between arrivals is 1/λ = 1/5 hour = 12 minutes."}, {"id": "c", "text": "5 minutes", "is_correct": false, "feedback_incorrect": "Incorrect. The expected time is 1/λ = 1/5 hour = 12 minutes."}, {"id": "d", "text": "1 hour", "is_correct": false, "feedback_incorrect": "Incorrect. The expected time is 1/λ, where λ is the rate parameter (5 customers per hour)."}]}}}, {"id": "efp-screen3-memoryless", "type": "lesson_screen", "order": 3, "estimatedTimeSeconds": 60, "content": {"headline": "The Memoryless Property", "body_md": "One of the most fascinating aspects of the exponential distribution is its memoryless property. This means that the probability of waiting an additional time t is the same, regardless of how long you've already waited.\n\nFor example, if you're waiting for a bus that arrives according to an exponential distribution with an average of 10 minutes between buses, and you've already been waiting for 15 minutes, the probability distribution of additional waiting time is exactly the same as if you had just arrived at the bus stop!", "visual": {"type": "giphy_search", "value": "waiting bus stop"}, "interactive_element": {"type": "multiple_choice_text", "question": "Which of these scenarios is best modeled by the memoryless property of the exponential distribution?", "options": [{"id": "a", "text": "The remaining lifetime of a brand new light bulb", "is_correct": false, "feedback_incorrect": "Incorrect. Light bulbs typically wear out over time, so their failure rate increases with age (not memoryless)."}, {"id": "b", "text": "The time until the next earthquake in a seismically active region", "is_correct": true, "feedback_correct": "Correct! Earthquake occurrences are often modeled as memoryless - the time until the next earthquake doesn't depend on how long it's been since the last one."}, {"id": "c", "text": "The time until a machine breaks down after regular use", "is_correct": false, "feedback_incorrect": "Incorrect. Machines typically wear out over time, so their failure rate increases with age (not memoryless)."}, {"id": "d", "text": "The time until a student finishes an exam", "is_correct": false, "feedback_incorrect": "Incorrect. The time to finish an exam depends on how much of the exam remains, which depends on how long the student has been working on it."}]}}}, {"id": "efp-screen4-applications", "type": "lesson_screen", "order": 4, "estimatedTimeSeconds": 60, "content": {"headline": "Applications of Exponential Functions in Probability", "body_md": "Exponential functions appear in many probabilistic models:\n\n- **Radioactive decay**: The amount of radioactive material remaining after time t is N(t) = N₀e^(-λt)\n- **Population growth**: A population growing at a constant rate follows P(t) = P₀e^(rt)\n- **Reliability engineering**: The probability of a component surviving beyond time t is R(t) = e^(-λt)\n- **Queueing theory**: Time between arrivals or service times often follow exponential distributions\n- **Finance**: Continuous compounding follows the formula A = Pe^(rt)", "visual": {"type": "unsplash_search", "value": "radioactive decay"}, "interactive_element": {"type": "text_input_quick", "question": "A radioactive isotope has a decay constant of 0.05 per year. If you start with 100 grams, how much will remain after 10 years?", "correct_answer_regex": "^60\\.65$|^60\\.7$|^61$|^60\\.6$", "placeholder": "Enter your answer in grams", "feedback_correct": "Correct! N(10) = 100e^(-0.05 × 10) = 100e^(-0.5) = 100 × 0.6065 = 60.65 grams", "feedback_incorrect": "Not quite. Use the formula N(t) = N₀e^(-λt) with N₀ = 100, λ = 0.05, and t = 10."}}}, {"id": "efp-screen5-conclusion", "type": "lesson_screen", "order": 5, "estimatedTimeSeconds": 30, "content": {"headline": "Exponential Functions: Modeling Time and Decay", "body_md": "Great job! You now understand how exponential functions model probabilistic phenomena, especially those involving time between events or decay processes. These models are essential in fields ranging from physics and engineering to finance and operations research.", "visual": {"type": "unsplash_search", "value": "exponential model"}, "interactive_element": {"type": "button", "text": "Continue to Next Lesson", "action": "next_lesson"}}}]}, {"id": "analyzing-data", "title": "Analyzing Data with Functions and Probability", "description": "Fit functions to observed frequencies.", "order": 4, "type": "interactive_lesson", "estimatedTimeMinutes": 10, "contentBlocks": [{"id": "ad-screen1-intro", "type": "lesson_screen", "order": 1, "estimatedTimeSeconds": 30, "content": {"headline": "From Data to Functions: Finding Patterns", "body_md": "Real-world data often contains randomness, but underlying patterns can be discovered by fitting mathematical functions to the data. This process helps us understand, predict, and make decisions based on observed frequencies.", "visual": {"type": "giphy_search", "value": "data analysis graph"}, "interactive_element": {"type": "button", "text": "Let's Explore Data Analysis!", "action": "next_screen"}}}, {"id": "ad-screen2-fitting", "type": "lesson_screen", "order": 2, "estimatedTimeSeconds": 60, "content": {"headline": "Fitting Functions to Data", "body_md": "When we have data that appears to follow a pattern, we can fit a function to it using various techniques:\n\n- **Linear regression**: Fits a line to data points\n- **Polynomial regression**: Fits a polynomial curve\n- **Exponential regression**: Fits an exponential function\n- **Maximum likelihood estimation**: Finds the parameters of a probability distribution that make the observed data most likely\n\nThe goal is to find a function that closely matches the observed data.", "visual": {"type": "unsplash_search", "value": "curve fitting data"}, "interactive_element": {"type": "multiple_choice_text", "question": "Which fitting technique would be most appropriate for data that shows constant percentage growth over time?", "options": [{"id": "a", "text": "Linear regression", "is_correct": false, "feedback_incorrect": "Incorrect. Linear regression fits a straight line, which models constant absolute growth, not percentage growth."}, {"id": "b", "text": "Exponential regression", "is_correct": true, "feedback_correct": "Correct! Exponential functions model constant percentage growth, making exponential regression appropriate for this data."}, {"id": "c", "text": "Quadratic regression", "is_correct": false, "feedback_incorrect": "Incorrect. Quadratic functions model accelerating or decelerating growth, not constant percentage growth."}, {"id": "d", "text": "Logarithmic regression", "is_correct": false, "feedback_incorrect": "Incorrect. Logarithmic functions model diminishing returns, not constant percentage growth."}]}}}, {"id": "ad-screen3-distributions", "type": "lesson_screen", "order": 3, "estimatedTimeSeconds": 60, "content": {"headline": "Fitting Probability Distributions", "body_md": "When analyzing random data, we often try to fit a probability distribution to understand the underlying process. Common distributions include:\n\n- **Normal (Gaussian)**: For data clustered around a mean with symmetric variation\n- **Poisson**: For count data (number of events in a fixed time/space)\n- **Exponential**: For time between events\n- **Binomial**: For number of successes in fixed number of trials\n- **Uniform**: For data equally likely across a range\n\nThe choice depends on the nature of the data and the process generating it.", "visual": {"type": "giphy_search", "value": "probability distribution fitting"}, "interactive_element": {"type": "multiple_choice_text", "question": "A quality control engineer counts the number of defects in each batch of products. Which distribution would likely fit this data best?", "options": [{"id": "a", "text": "Normal distribution", "is_correct": false, "feedback_incorrect": "Incorrect. Normal distributions are continuous and symmetric, not ideal for count data that can only be non-negative integers."}, {"id": "b", "text": "Exponential distribution", "is_correct": false, "feedback_incorrect": "Incorrect. Exponential distributions model time between events, not counts of events."}, {"id": "c", "text": "Poisson distribution", "is_correct": true, "feedback_correct": "Correct! The Poisson distribution is ideal for modeling the number of events (defects) occurring in a fixed unit of space or time."}, {"id": "d", "text": "Uniform distribution", "is_correct": false, "feedback_incorrect": "Incorrect. Uniform distributions assume all outcomes are equally likely, which is rarely the case for defect counts."}]}}}, {"id": "ad-screen4-goodness", "type": "lesson_screen", "order": 4, "estimatedTimeSeconds": 60, "content": {"headline": "Evaluating the Fit", "body_md": "After fitting a function or distribution to data, we need to evaluate how well it fits. Common measures include:\n\n- **R-squared (R²)**: Proportion of variance explained by the model (higher is better)\n- **Mean Squared Error (MSE)**: Average of squared differences between predicted and actual values (lower is better)\n- **Chi-squared test**: Tests if observed frequencies match expected frequencies from a distribution\n- **<PERSON><PERSON><PERSON>rov-<PERSON><PERSON><PERSON> test**: Tests if a sample comes from a specific distribution\n- **Visual inspection**: Plotting residuals or comparing histograms", "visual": {"type": "unsplash_search", "value": "data fit residuals"}, "interactive_element": {"type": "multiple_choice_text", "question": "What does an R² value of 0.85 mean in the context of fitting a function to data?", "options": [{"id": "a", "text": "The function is 85% accurate in its predictions", "is_correct": false, "feedback_incorrect": "Incorrect. R² doesn't directly measure prediction accuracy."}, {"id": "b", "text": "85% of the data points lie exactly on the fitted function", "is_correct": false, "feedback_incorrect": "Incorrect. R² doesn't count the number of points that lie exactly on the curve."}, {"id": "c", "text": "The function explains 85% of the variance in the data", "is_correct": true, "feedback_correct": "Correct! R² represents the proportion of variance in the dependent variable that is explained by the independent variable(s) in the model."}, {"id": "d", "text": "The function is 85% better than a random guess", "is_correct": false, "feedback_incorrect": "Incorrect. While R² does compare to a baseline model, it's not a direct measure of improvement over random guessing."}]}}}, {"id": "ad-screen5-conclusion", "type": "lesson_screen", "order": 5, "estimatedTimeSeconds": 30, "content": {"headline": "From Data to Insight: The Power of Functional Analysis", "body_md": "Excellent! You now understand how to analyze data by fitting functions and probability distributions. This powerful approach allows us to extract patterns from noisy data, make predictions, and gain insights into the processes generating the data.", "visual": {"type": "unsplash_search", "value": "data analysis insight"}, "interactive_element": {"type": "button", "text": "Continue to Next Lesson", "action": "next_lesson"}}}]}, {"id": "decision-making", "title": "Decision Making Under Uncertainty (Introduction)", "description": "Use probability and function concepts.", "order": 5, "type": "interactive_lesson", "estimatedTimeMinutes": 10, "contentBlocks": [{"id": "dm-screen1-intro", "type": "lesson_screen", "order": 1, "estimatedTimeSeconds": 30, "content": {"headline": "Making Decisions in an Uncertain World", "body_md": "In real life, we rarely have complete information when making decisions. Functions and probability give us tools to make optimal choices even when outcomes are uncertain.", "visual": {"type": "giphy_search", "value": "decision making uncertainty"}, "interactive_element": {"type": "button", "text": "Let's Explore Decision Making!", "action": "next_screen"}}}, {"id": "dm-screen2-expected-utility", "type": "lesson_screen", "order": 2, "estimatedTimeSeconds": 60, "content": {"headline": "Expected Utility Theory", "body_md": "Expected utility theory provides a framework for decision making under uncertainty:\n\n1. Identify possible actions\n2. Determine possible outcomes for each action\n3. Assign probabilities to each outcome\n4. Assign utilities (values) to each outcome\n5. Calculate expected utility for each action: E[U] = Σ P(outcome) × U(outcome)\n6. Choose the action with the highest expected utility\n\nThis approach uses both probability and functions to quantify the value of different choices.", "visual": {"type": "unsplash_search", "value": "decision tree probability"}, "interactive_element": {"type": "multiple_choice_text", "question": "An investor can choose between two investments: A has a 70% chance of $1000 profit and 30% chance of $200 loss; B has a 30% chance of $3000 profit and 70% chance of $500 loss. Which has the higher expected value?", "options": [{"id": "a", "text": "Investment A: $640", "is_correct": true, "feedback_correct": "Correct! E[A] = 0.7($1000) + 0.3(-$200) = $700 - $60 = $640"}, {"id": "b", "text": "Investment B: $550", "is_correct": false, "feedback_incorrect": "Incorrect. Calculate E[B] = 0.3($3000) + 0.7(-$500) = $900 - $350 = $550"}, {"id": "c", "text": "Both have the same expected value", "is_correct": false, "feedback_incorrect": "Incorrect. Calculate and compare the expected values of both investments."}, {"id": "d", "text": "Cannot be determined without more information", "is_correct": false, "feedback_incorrect": "Incorrect. We have all the information needed to calculate and compare expected values."}]}}}, {"id": "dm-screen3-risk", "type": "lesson_screen", "order": 3, "estimatedTimeSeconds": 60, "content": {"headline": "Risk and Utility Functions", "body_md": "People's attitudes toward risk affect their decisions. This can be modeled using utility functions:\n\n- **Risk-averse**: Utility function is concave (diminishing marginal utility)\n- **Risk-neutral**: Utility function is linear (utility proportional to monetary value)\n- **Risk-seeking**: Utility function is convex (increasing marginal utility)\n\nFor example, a risk-averse person might prefer a guaranteed $50 over a 50% chance of $100 and 50% chance of $0, even though both have the same expected monetary value.", "visual": {"type": "giphy_search", "value": "risk utility function"}, "interactive_element": {"type": "multiple_choice_text", "question": "Which of these choices would a risk-averse person likely prefer?", "options": [{"id": "a", "text": "A 50% chance of winning $1000 and 50% chance of winning nothing", "is_correct": false, "feedback_incorrect": "Incorrect. A risk-averse person would typically prefer a guaranteed amount over a risky option with the same expected value."}, {"id": "b", "text": "A guaranteed $450", "is_correct": true, "feedback_correct": "Correct! A risk-averse person would typically prefer a guaranteed amount (even if it's less than the expected value of a risky option) to avoid uncertainty."}, {"id": "c", "text": "A 10% chance of winning $5000 and 90% chance of winning nothing", "is_correct": false, "feedback_incorrect": "Incorrect. This option has the same expected value as option A but is even riskier, which a risk-averse person would avoid."}, {"id": "d", "text": "A 90% chance of losing $100 and 10% chance of winning $1000", "is_correct": false, "feedback_incorrect": "Incorrect. This option has a negative expected value and high risk, which a risk-averse person would avoid."}]}}}, {"id": "dm-screen4-applications", "type": "lesson_screen", "order": 4, "estimatedTimeSeconds": 60, "content": {"headline": "Applications of Decision Theory", "body_md": "Decision theory using functions and probability has many applications:\n\n- **Finance**: Portfolio optimization, investment decisions\n- **Insurance**: Setting premiums, managing risk\n- **Medicine**: Choosing treatments based on success probabilities\n- **Business**: Product development, market entry decisions\n- **Public policy**: Resource allocation, risk management\n- **Personal decisions**: Career choices, major purchases\n\nIn each case, we use functions to model the relationship between actions, outcomes, and values.", "visual": {"type": "unsplash_search", "value": "decision making business"}, "interactive_element": {"type": "multiple_choice_text", "question": "A pharmaceutical company is deciding whether to continue developing a new drug. Development costs $10 million more, with a 30% chance of success leading to $50 million profit and 70% chance of failure with no return. What is the expected value of continuing development?", "options": [{"id": "a", "text": "$5 million", "is_correct": true, "feedback_correct": "Correct! E[continue] = 0.3($50M) - $10M = $15M - $10M = $5M"}, {"id": "b", "text": "$15 million", "is_correct": false, "feedback_incorrect": "Incorrect. You need to subtract the $10M development cost from the expected return of 0.3($50M) = $15M."}, {"id": "c", "text": "-$10 million", "is_correct": false, "feedback_incorrect": "Incorrect. This would be the result if there was 0% chance of success, but there's a 30% chance of success."}, {"id": "d", "text": "$40 million", "is_correct": false, "feedback_incorrect": "Incorrect. This calculation doesn't account for the probability of success or the development cost."}]}}}, {"id": "dm-screen5-conclusion", "type": "lesson_screen", "order": 5, "estimatedTimeSeconds": 30, "content": {"headline": "Functions and Probability: Tools for Optimal Decisions", "body_md": "Great job! You now understand how functions and probability work together to help make optimal decisions under uncertainty. These powerful mathematical tools provide a framework for rational decision-making in complex, uncertain situations.", "visual": {"type": "unsplash_search", "value": "decision making success"}, "interactive_element": {"type": "button", "text": "Continue to Module Test", "action": "next_lesson"}}}]}], "endOfModuleAssessment": {"id": "applications-of-functions-and-probability-test", "title": "Functions and Probability in Action", "description": "Apply your knowledge to solve real-world problems involving functions and probability.", "type": "module_test_interactive", "estimatedTimeMinutes": 15, "passingScorePercentage": 70, "contentBlocks": [{"id": "afp-test-intro", "type": "test_screen_intro", "order": 1, "estimatedTimeSeconds": 30, "content": {"headline": "Functions and Probability in Action: Test Your Knowledge", "body_md": "Let's see how well you can apply functions and probability concepts to solve real-world problems!", "visual": {"type": "unsplash_search", "value": "mathematics test"}, "interactive_element": {"type": "button", "text": "Begin Test", "action": "next_screen"}}}, {"id": "afp-test-q1", "type": "test_screen_question", "order": 2, "estimatedTimeSeconds": 60, "content": {"headline": "Question 1: Random Processes", "body_md": "A website receives an average of 3 visitors per minute. Assuming visitors arrive according to a Poisson process, what is the probability of receiving exactly 5 visitors in a given minute?", "interactive_element": {"type": "multiple_choice_text", "options": [{"id": "a", "text": "0.101", "is_correct": true, "feedback_correct": "Correct! Using the Poisson PMF: P(X = 5) = e^(-3) × 3^5 / 5! ≈ 0.101"}, {"id": "b", "text": "0.168", "is_correct": false, "feedback_incorrect": "Incorrect. This is the probability of receiving exactly 3 visitors (the mean), not 5."}, {"id": "c", "text": "0.625", "is_correct": false, "feedback_incorrect": "Incorrect. This value is too high for a Poisson probability with mean 3."}, {"id": "d", "text": "0.050", "is_correct": false, "feedback_incorrect": "Incorrect. The calculation using the Poisson PMF gives P(X = 5) ≈ 0.101."}]}}}, {"id": "afp-test-q2", "type": "test_screen_question", "order": 3, "estimatedTimeSeconds": 60, "content": {"headline": "Question 2: Linear Functions in Probability", "body_md": "An investment portfolio consists of 40% in Stock A with expected return 6%, 35% in Stock B with expected return 8%, and 25% in Stock C with expected return 4%. What is the expected return of the portfolio?", "interactive_element": {"type": "text_input_quick", "correct_answer_regex": "^6\\.2$|^6\\.20$|^6\\.2\\%$|^6\\.20\\%$", "placeholder": "Enter your answer as a percentage", "feedback_correct": "Correct! E[Return] = 0.4(6%) + 0.35(8%) + 0.25(4%) = 2.4% + 2.8% + 1% = 6.2%", "feedback_incorrect": "Incorrect. Calculate the weighted average: 0.4(6%) + 0.35(8%) + 0.25(4%) = 2.4% + 2.8% + 1% = 6.2%"}}}, {"id": "afp-test-q3", "type": "test_screen_question", "order": 4, "estimatedTimeSeconds": 60, "content": {"headline": "Question 3: Exponential Functions in Probability", "body_md": "A radioactive substance decays according to the function A(t) = A₀e^(-0.12t), where t is measured in years. If you start with 100 grams, how many years will it take for the amount to reduce to 50 grams?", "interactive_element": {"type": "text_input_quick", "correct_answer_regex": "^5\\.8$|^5\\.77$|^5\\.775$|^5\\.78$", "placeholder": "Enter your answer in years", "feedback_correct": "Correct! Solve 50 = 100e^(-0.12t) → 0.5 = e^(-0.12t) → -0.12t = ln(0.5) → t = -ln(0.5)/0.12 ≈ 5.78 years", "feedback_incorrect": "Incorrect. Solve 50 = 100e^(-0.12t) → 0.5 = e^(-0.12t) → -0.12t = ln(0.5) → t = -ln(0.5)/0.12 ≈ 5.78 years"}}}, {"id": "afp-test-q4", "type": "test_screen_question", "order": 5, "estimatedTimeSeconds": 60, "content": {"headline": "Question 4: Data Analysis", "body_md": "A researcher collects data on the heights of adult males and finds that the heights follow a normal distribution with mean 175 cm and standard deviation 7 cm. What percentage of adult males are taller than 182 cm?", "interactive_element": {"type": "multiple_choice_text", "options": [{"id": "a", "text": "50%", "is_correct": false, "feedback_incorrect": "Incorrect. 50% would be above the mean (175 cm), not above 182 cm."}, {"id": "b", "text": "16%", "is_correct": true, "feedback_correct": "Correct! 182 cm is 1 standard deviation above the mean, and approximately 16% of values in a normal distribution are more than 1 standard deviation above the mean."}, {"id": "c", "text": "2.5%", "is_correct": false, "feedback_incorrect": "Incorrect. 2.5% would be more than 2 standard deviations above the mean (189 cm), not 182 cm."}, {"id": "d", "text": "34%", "is_correct": false, "feedback_incorrect": "Incorrect. 34% would be between the mean and 1 standard deviation above the mean, not above 182 cm."}]}}}, {"id": "afp-test-q5", "type": "test_screen_question", "order": 6, "estimatedTimeSeconds": 60, "content": {"headline": "Question 5: Decision Making", "body_md": "A company is deciding whether to launch a new product. Development costs $200,000, and market research indicates a 60% chance of success (yielding $500,000 profit) and 40% chance of failure (yielding no return). What is the expected value of launching the product?", "interactive_element": {"type": "text_input_quick", "correct_answer_regex": "^\\$?100,?000$|^\\$?100000$|^100,?000$|^100000$", "placeholder": "Enter your answer in dollars", "feedback_correct": "Correct! E[launch] = 0.6($500,000) - $200,000 = $300,000 - $200,000 = $100,000", "feedback_incorrect": "Incorrect. Calculate E[launch] = 0.6($500,000) - $200,000 = $300,000 - $200,000 = $100,000"}}}, {"id": "afp-test-conclusion", "type": "test_screen_conclusion", "order": 7, "estimatedTimeSeconds": 30, "content": {"headline": "Functions and Probability in Action: Test Complete", "body_md": "Great job completing the test! You've demonstrated your ability to apply functions and probability concepts to solve real-world problems.", "visual": {"type": "unsplash_search", "value": "mathematics success"}, "interactive_element": {"type": "button", "text": "Return to Module", "action": "return_to_module"}}}]}}