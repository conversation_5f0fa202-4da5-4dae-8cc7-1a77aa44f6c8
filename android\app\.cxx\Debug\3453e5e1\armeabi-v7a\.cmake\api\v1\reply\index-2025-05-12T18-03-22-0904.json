{"cmake": {"generator": {"multiConfig": false, "name": "Ninja"}, "paths": {"cmake": "D:/AndroidSDK/cmake/3.22.1/bin/cmake.exe", "cpack": "D:/AndroidSDK/cmake/3.22.1/bin/cpack.exe", "ctest": "D:/AndroidSDK/cmake/3.22.1/bin/ctest.exe", "root": "D:/AndroidSDK/cmake/3.22.1/share/cmake-3.22"}, "version": {"isDirty": true, "major": 3, "minor": 22, "patch": 1, "string": "3.22.1-g37088a8-dirty", "suffix": "g37088a8"}}, "objects": [{"jsonFile": "codemodel-v2-c3ff619174638afc4119.json", "kind": "codemodel", "version": {"major": 2, "minor": 3}}, {"jsonFile": "cache-v2-fbc2b45e1d0745c6e08d.json", "kind": "cache", "version": {"major": 2, "minor": 0}}, {"jsonFile": "cmakeFiles-v1-ee7470f56a88b771d709.json", "kind": "cmakeFiles", "version": {"major": 1, "minor": 0}}], "reply": {"client-agp": {"cache-v2": {"jsonFile": "cache-v2-fbc2b45e1d0745c6e08d.json", "kind": "cache", "version": {"major": 2, "minor": 0}}, "cmakeFiles-v1": {"jsonFile": "cmakeFiles-v1-ee7470f56a88b771d709.json", "kind": "cmakeFiles", "version": {"major": 1, "minor": 0}}, "codemodel-v2": {"jsonFile": "codemodel-v2-c3ff619174638afc4119.json", "kind": "codemodel", "version": {"major": 2, "minor": 3}}}}}