{"id": "transformations-and-probability-rules", "title": "TRANSFORMATIONS AND PRO<PERSON><PERSON>LITY RULES", "description": "Understand how modifying functions alters their graphs and learn fundamental probability rules.", "order": 3, "lessons": [{"id": "shifting-functions", "title": "Shifting Functions: Up, Down, Left, Right", "description": "Visualize graph translations.", "order": 1, "type": "interactive_lesson", "estimatedTimeMinutes": 10, "contentBlocks": [{"id": "sf-screen1-intro", "type": "lesson_screen", "order": 1, "estimatedTimeSeconds": 30, "content": {"headline": "Moving Graphs Around", "body_md": "Function transformations allow us to move and reshape graphs. The simplest transformations are shifts (or translations) that move a graph up, down, left, or right without changing its shape.", "visual": {"type": "giphy_search", "value": "function transformation graph"}, "interactive_element": {"type": "button", "text": "Let's Explore Transformations!", "action": "next_screen"}}}, {"id": "sf-screen2-vertical-shifts", "type": "lesson_screen", "order": 2, "estimatedTimeSeconds": 60, "content": {"headline": "Vertical Shifts: Up and Down", "body_md": "To shift a function vertically:\n\n- **Shift up**: Add a positive constant → f(x) + c\n- **Shift down**: Add a negative constant → f(x) - c\n\nFor example, if f(x) = x², then f(x) + 3 = x² + 3 shifts the parabola up 3 units.", "visual": {"type": "giphy_search", "value": "vertical shift function"}, "interactive_element": {"type": "multiple_choice_text", "question": "How would you shift the function f(x) = |x| down 4 units?", "options": [{"id": "a", "text": "f(x) = |x| + 4", "is_correct": false, "feedback_incorrect": "Incorrect. Adding 4 would shift the graph up, not down."}, {"id": "b", "text": "f(x) = |x| - 4", "is_correct": true, "feedback_correct": "Correct! Subtracting 4 shifts the graph down 4 units."}, {"id": "c", "text": "f(x) = |x - 4|", "is_correct": false, "feedback_incorrect": "Incorrect. This shifts the graph right, not down."}, {"id": "d", "text": "f(x) = |x + 4|", "is_correct": false, "feedback_incorrect": "Incorrect. This shifts the graph left, not down."}]}}}, {"id": "sf-screen3-horizontal-shifts", "type": "lesson_screen", "order": 3, "estimatedTimeSeconds": 60, "content": {"headline": "Horizontal Shifts: Left and Right", "body_md": "To shift a function horizontally:\n\n- **Shift right**: Subtract from the input → f(x - c)\n- **Shift left**: Add to the input → f(x + c)\n\nFor example, if f(x) = x², then f(x - 2) = (x - 2)² shifts the parabola right 2 units.", "visual": {"type": "giphy_search", "value": "horizontal shift function"}, "interactive_element": {"type": "multiple_choice_text", "question": "How would you shift the function f(x) = √x left 3 units?", "options": [{"id": "a", "text": "f(x) = √(x - 3)", "is_correct": false, "feedback_incorrect": "Incorrect. This shifts the graph right, not left."}, {"id": "b", "text": "f(x) = √(x + 3)", "is_correct": true, "feedback_correct": "Correct! Adding 3 to the input shifts the graph left 3 units."}, {"id": "c", "text": "f(x) = √x - 3", "is_correct": false, "feedback_incorrect": "Incorrect. This shifts the graph down, not left."}, {"id": "d", "text": "f(x) = √x + 3", "is_correct": false, "feedback_incorrect": "Incorrect. This shifts the graph up, not left."}]}}}, {"id": "sf-screen4-combined-shifts", "type": "lesson_screen", "order": 4, "estimatedTimeSeconds": 60, "content": {"headline": "Combining Shifts", "body_md": "We can combine horizontal and vertical shifts to move a graph to any position:\n\nf(x) → f(x - h) + k\n\nThis shifts the graph:\n- Right h units (if h > 0) or left |h| units (if h < 0)\n- Up k units (if k > 0) or down |k| units (if k < 0)", "visual": {"type": "unsplash_search", "value": "function transformation graph"}, "interactive_element": {"type": "text_input_quick", "question": "If f(x) = x², write the function that shifts f(x) right 2 units and up 3 units.", "correct_answer_regex": "^\\s*f\\s*\\(\\s*x\\s*\\)\\s*=\\s*\\(\\s*x\\s*-\\s*2\\s*\\)\\s*\\^\\s*2\\s*\\+\\s*3\\s*$|^\\s*f\\s*\\(\\s*x\\s*\\)\\s*=\\s*\\(\\s*x\\s*-\\s*2\\s*\\)\\s*\\^2\\s*\\+\\s*3\\s*$|^\\s*\\(\\s*x\\s*-\\s*2\\s*\\)\\s*\\^\\s*2\\s*\\+\\s*3\\s*$|^\\s*\\(\\s*x\\s*-\\s*2\\s*\\)\\s*\\^2\\s*\\+\\s*3\\s*$", "placeholder": "Enter the function", "feedback_correct": "Correct! f(x) = (x - 2)² + 3 shifts the parabola right 2 units and up 3 units.", "feedback_incorrect": "Not quite. To shift right 2 units, replace x with (x - 2). To shift up 3 units, add 3 to the function."}}}, {"id": "sf-screen5-interactive", "type": "lesson_screen", "order": 5, "estimatedTimeSeconds": 180, "content": {"headline": "Try It Yourself: Function Transformer", "body_md": "Now it's your turn to experiment with function transformations! Use the interactive tool below to see how shifting a function affects its graph. Try different functions and transformations to build your intuition.", "visual": {"type": "unsplash_search", "value": "interactive math"}, "interactive_element": {"type": "interactive_widget", "widget_type": "interactive_function_transformer", "data": {"title": "Function Transformer: Shifting Functions", "description": "Experiment with horizontal and vertical shifts to see how they affect different functions.", "functionTypes": ["linear", "quadratic", "absolute", "sine", "cosine"], "minX": -10, "maxX": 10, "minY": -10, "maxY": 10, "gridLines": 10, "examples": [{"title": "Vertical Shift Up", "description": "f(x) = x² + 3 (Shift up 3 units)", "functionType": "quadratic", "horizontalShift": 0.0, "verticalShift": 3.0, "horizontalStretch": 1.0, "verticalStretch": 1.0, "reflectX": false, "reflectY": false}, {"title": "Vertical Shift Down", "description": "f(x) = x² - 2 (Shift down 2 units)", "functionType": "quadratic", "horizontalShift": 0.0, "verticalShift": -2.0, "horizontalStretch": 1.0, "verticalStretch": 1.0, "reflectX": false, "reflectY": false}, {"title": "Horizontal Shift Right", "description": "f(x) = (x - 2)² (Shift right 2 units)", "functionType": "quadratic", "horizontalShift": 2.0, "verticalShift": 0.0, "horizontalStretch": 1.0, "verticalStretch": 1.0, "reflectX": false, "reflectY": false}, {"title": "Horizontal Shift Left", "description": "f(x) = (x + 3)² (Shift left 3 units)", "functionType": "quadratic", "horizontalShift": -3.0, "verticalShift": 0.0, "horizontalStretch": 1.0, "verticalStretch": 1.0, "reflectX": false, "reflectY": false}, {"title": "Combined Shifts", "description": "f(x) = (x - 1)² + 2 (Right 1, Up 2)", "functionType": "quadratic", "horizontalShift": 1.0, "verticalShift": 2.0, "horizontalStretch": 1.0, "verticalStretch": 1.0, "reflectX": false, "reflectY": false}]}}}}, {"id": "sf-screen6-conclusion", "type": "lesson_screen", "order": 6, "estimatedTimeSeconds": 30, "content": {"headline": "Shifting Functions: Moving Without Changing Shape", "body_md": "Great job! You now understand how to shift functions horizontally and vertically. These transformations allow you to move a graph to any position without changing its shape.", "visual": {"type": "unsplash_search", "value": "function transformation"}, "interactive_element": {"type": "button", "text": "Continue to Next Lesson", "action": "next_lesson"}}}]}, {"id": "stretching-reflecting-functions", "title": "Stretching and Reflecting Functions", "description": "See how graphs change shape and orientation.", "order": 2, "type": "interactive_lesson", "estimatedTimeMinutes": 10, "contentBlocks": [{"id": "srf-screen1-intro", "type": "lesson_screen", "order": 1, "estimatedTimeSeconds": 30, "content": {"headline": "Changing a Graph's Shape and Orientation", "body_md": "Beyond shifting, we can transform functions by stretching, compressing, and reflecting them. These transformations change the shape or orientation of the graph.", "visual": {"type": "giphy_search", "value": "function reflection stretch"}, "interactive_element": {"type": "button", "text": "Let's Explore <PERSON>hape <PERSON>!", "action": "next_screen"}}}, {"id": "srf-screen2-vertical-stretch", "type": "lesson_screen", "order": 2, "estimatedTimeSeconds": 60, "content": {"headline": "Vertical Stretching and Compression", "body_md": "To stretch or compress a function vertically:\n\n- **Vertical stretch**: Multiply by a constant > 1 → a·f(x) where a > 1\n- **Vertical compression**: Multiply by a constant between 0 and 1 → a·f(x) where 0 < a < 1\n\nFor example, if f(x) = x², then 3f(x) = 3x² stretches the parabola vertically by a factor of 3.", "visual": {"type": "giphy_search", "value": "vertical stretch function"}, "interactive_element": {"type": "multiple_choice_text", "question": "How would you vertically compress the function f(x) = |x| by a factor of 1/2?", "options": [{"id": "a", "text": "f(x) = 2|x|", "is_correct": false, "feedback_incorrect": "Incorrect. Multiplying by 2 would stretch the graph, not compress it."}, {"id": "b", "text": "f(x) = |x/2|", "is_correct": false, "feedback_incorrect": "Incorrect. This compresses the graph horizontally, not vertically."}, {"id": "c", "text": "f(x) = |x|/2", "is_correct": true, "feedback_correct": "Correct! Multiplying by 1/2 compresses the graph vertically by a factor of 1/2."}, {"id": "d", "text": "f(x) = |2x|", "is_correct": false, "feedback_incorrect": "Incorrect. This compresses the graph horizontally, not vertically."}]}}}, {"id": "srf-screen3-horizontal-stretch", "type": "lesson_screen", "order": 3, "estimatedTimeSeconds": 60, "content": {"headline": "Horizontal Stretching and Compression", "body_md": "To stretch or compress a function horizontally:\n\n- **Horizontal compression**: Multiply the input by a constant > 1 → f(bx) where b > 1\n- **Horizontal stretch**: Multiply the input by a constant between 0 and 1 → f(bx) where 0 < b < 1\n\nFor example, if f(x) = x², then f(2x) = (2x)² = 4x² compresses the parabola horizontally by a factor of 2.", "visual": {"type": "giphy_search", "value": "horizontal stretch function"}, "interactive_element": {"type": "multiple_choice_text", "question": "How would you horizontally stretch the function f(x) = cos(x) by a factor of 3?", "options": [{"id": "a", "text": "f(x) = cos(3x)", "is_correct": false, "feedback_incorrect": "Incorrect. This compresses the graph horizontally by a factor of 3, not stretches it."}, {"id": "b", "text": "f(x) = cos(x/3)", "is_correct": true, "feedback_correct": "Correct! Dividing the input by 3 stretches the graph horizontally by a factor of 3."}, {"id": "c", "text": "f(x) = 3cos(x)", "is_correct": false, "feedback_incorrect": "Incorrect. This stretches the graph vertically, not horizontally."}, {"id": "d", "text": "f(x) = cos(x)/3", "is_correct": false, "feedback_incorrect": "Incorrect. This compresses the graph vertically, not horizontally."}]}}}, {"id": "srf-screen4-reflections", "type": "lesson_screen", "order": 4, "estimatedTimeSeconds": 60, "content": {"headline": "Reflections", "body_md": "We can reflect a function across the coordinate axes:\n\n- **Reflection across the x-axis**: Negate the function → -f(x)\n- **Reflection across the y-axis**: Negate the input → f(-x)\n\nFor example, if f(x) = x³, then -f(x) = -x³ reflects the cubic function across the x-axis.", "visual": {"type": "giphy_search", "value": "function reflection"}, "interactive_element": {"type": "multiple_choice_text", "question": "How would you reflect the function f(x) = √x across the y-axis?", "options": [{"id": "a", "text": "f(x) = -√x", "is_correct": false, "feedback_incorrect": "Incorrect. This reflects the graph across the x-axis, not the y-axis."}, {"id": "b", "text": "f(x) = √(-x)", "is_correct": false, "feedback_incorrect": "Incorrect. This is not a real-valued function for x > 0 since you can't take the square root of a negative number."}, {"id": "c", "text": "f(x) = √|x|", "is_correct": false, "feedback_incorrect": "Incorrect. This doesn't reflect the function; it just ensures the input is non-negative."}, {"id": "d", "text": "f(-x) = √(-x) is not possible for this function", "is_correct": true, "feedback_correct": "Correct! The function f(x) = √x cannot be reflected across the y-axis because √(-x) would involve taking the square root of negative numbers, which gives complex numbers."}]}}}, {"id": "srf-screen5-interactive", "type": "lesson_screen", "order": 5, "estimatedTimeSeconds": 180, "content": {"headline": "Try It Yourself: Function Transformer", "body_md": "Now it's your turn to experiment with stretching, compressing, and reflecting functions! Use the interactive tool below to see how these transformations affect the graph. Try different functions and transformations to build your intuition.", "visual": {"type": "unsplash_search", "value": "interactive math"}, "interactive_element": {"type": "interactive_widget", "widget_type": "interactive_function_transformer", "data": {"title": "Function Transformer: Stretching and Reflecting", "description": "Experiment with stretching, compressing, and reflecting to see how they affect different functions.", "functionTypes": ["linear", "quadratic", "absolute", "sine", "cosine"], "minX": -10, "maxX": 10, "minY": -10, "maxY": 10, "gridLines": 10, "examples": [{"title": "Vertical Stretch", "description": "f(x) = 3x² (<PERSON>retch vertically by factor of 3)", "functionType": "quadratic", "horizontalShift": 0.0, "verticalShift": 0.0, "horizontalStretch": 1.0, "verticalStretch": 3.0, "reflectX": false, "reflectY": false}, {"title": "Vertical Compression", "description": "f(x) = 0.5x² (Compress vertically by factor of 0.5)", "functionType": "quadratic", "horizontalShift": 0.0, "verticalShift": 0.0, "horizontalStretch": 1.0, "verticalStretch": 0.5, "reflectX": false, "reflectY": false}, {"title": "Horizontal Compression", "description": "f(x) = (2x)² (Compress horizontally by factor of 2)", "functionType": "quadratic", "horizontalShift": 0.0, "verticalShift": 0.0, "horizontalStretch": 0.5, "verticalStretch": 1.0, "reflectX": false, "reflectY": false}, {"title": "Horizontal Stretch", "description": "f(x) = (x/2)² (Stretch horizontally by factor of 2)", "functionType": "quadratic", "horizontalShift": 0.0, "verticalShift": 0.0, "horizontalStretch": 2.0, "verticalStretch": 1.0, "reflectX": false, "reflectY": false}, {"title": "Reflection Across X-axis", "description": "f(x) = -x² (Reflect across x-axis)", "functionType": "quadratic", "horizontalShift": 0.0, "verticalShift": 0.0, "horizontalStretch": 1.0, "verticalStretch": 1.0, "reflectX": true, "reflectY": false}, {"title": "Reflection Across Y-axis", "description": "f(x) = (-x)² (Reflect across y-axis)", "functionType": "quadratic", "horizontalShift": 0.0, "verticalShift": 0.0, "horizontalStretch": 1.0, "verticalStretch": 1.0, "reflectX": false, "reflectY": true}]}}}}, {"id": "srf-screen6-conclusion", "type": "lesson_screen", "order": 6, "estimatedTimeSeconds": 30, "content": {"headline": "Transforming Functions: Changing Shape and Orientation", "body_md": "Excellent! You now understand how to stretch, compress, and reflect functions. These transformations, combined with shifts, give you powerful tools to manipulate graphs.", "visual": {"type": "unsplash_search", "value": "function transformation"}, "interactive_element": {"type": "button", "text": "Continue to Next Lesson", "action": "next_lesson"}}}]}, {"id": "independent-events", "title": "Independent Events: Separate Outcomes", "description": "Understand when events don't influence each other.", "order": 3, "type": "interactive_lesson", "estimatedTimeMinutes": 10, "contentBlocks": [{"id": "ie-screen1-intro", "type": "lesson_screen", "order": 1, "estimatedTimeSeconds": 30, "content": {"headline": "Independent Events: When Outcomes Don't Affect Each Other", "body_md": "Independent events are events where the occurrence of one event does not affect the probability of the other event. Like flipping a coin twice - the second flip isn't affected by what happened on the first flip.", "visual": {"type": "giphy_search", "value": "coin flip probability"}, "interactive_element": {"type": "button", "text": "Let's Explore Independence!", "action": "next_screen"}}}, {"id": "ie-screen2-definition", "type": "lesson_screen", "order": 2, "estimatedTimeSeconds": 60, "content": {"headline": "Defining Independence", "body_md": "Two events A and B are independent if the probability of B occurring is the same whether A has occurred or not.\n\nMathematically: P(B|A) = P(B)\n\nWhere P(B|A) is the conditional probability of B given that A has occurred.", "visual": {"type": "unsplash_search", "value": "dice probability"}, "interactive_element": {"type": "multiple_choice_text", "question": "Which of these pairs of events are independent?", "options": [{"id": "a", "text": "Drawing a king from a deck, then drawing another king without replacing the first card", "is_correct": false, "feedback_incorrect": "Incorrect. These events are dependent because removing a king changes the probability of drawing another king."}, {"id": "b", "text": "Rolling a die and getting a 6, then flipping a coin and getting heads", "is_correct": true, "feedback_correct": "Correct! The outcome of the die roll doesn't affect the probability of getting heads on the coin flip."}, {"id": "c", "text": "It rains today, then it rains tomorrow", "is_correct": false, "feedback_incorrect": "Incorrect. Weather patterns are typically dependent - today's weather often influences tomorrow's weather."}, {"id": "d", "text": "Selecting a student who studies math, then selecting a student who passes the exam", "is_correct": false, "feedback_incorrect": "Incorrect. These events are likely dependent since studying math probably affects the probability of passing the exam."}]}}}, {"id": "ie-screen3-multiplication-rule", "type": "lesson_screen", "order": 3, "estimatedTimeSeconds": 60, "content": {"headline": "The Multiplication Rule for Independent Events", "body_md": "For independent events A and B, the probability of both events occurring is the product of their individual probabilities:\n\nP(A and B) = P(A) × P(B)\n\nFor example, the probability of getting heads on two consecutive coin flips is 1/2 × 1/2 = 1/4.", "visual": {"type": "giphy_search", "value": "probability multiplication"}, "interactive_element": {"type": "text_input_quick", "question": "What is the probability of rolling a 5 on a die and then drawing a heart from a standard deck of cards?", "correct_answer_regex": "^1\\/24$|^0\\.0416$|^0\\.0417$|^0\\.042$", "placeholder": "Enter your answer as a fraction or decimal", "feedback_correct": "Correct! P(rolling 5) = 1/6, P(drawing heart) = 13/52 = 1/4, so P(both) = 1/6 × 1/4 = 1/24 ≈ 0.0417", "feedback_incorrect": "Not quite. Calculate P(rolling 5) × P(drawing heart) = 1/6 × 1/4 = 1/24"}}}, {"id": "ie-screen4-real-world", "type": "lesson_screen", "order": 4, "estimatedTimeSeconds": 60, "content": {"headline": "Independent Events in the Real World", "body_md": "Independent events are common in many scenarios:\n\n- Consecutive rolls of a die\n- Drawing cards with replacement\n- Separate lottery drawings\n- Flipping a coin multiple times\n- Random selection from different groups", "visual": {"type": "unsplash_search", "value": "lottery probability"}, "interactive_element": {"type": "multiple_choice_text", "question": "A family has two children. Assuming the probability of having a boy or girl is equal, what is the probability that both children are girls?", "options": [{"id": "a", "text": "1/4", "is_correct": true, "feedback_correct": "Correct! P(first child is girl) = 1/2, P(second child is girl) = 1/2, so P(both are girls) = 1/2 × 1/2 = 1/4"}, {"id": "b", "text": "1/2", "is_correct": false, "feedback_incorrect": "Incorrect. This would be the probability of just one child being a girl."}, {"id": "c", "text": "1/3", "is_correct": false, "feedback_incorrect": "Incorrect. The probability is 1/4 because we multiply 1/2 × 1/2."}, {"id": "d", "text": "2/3", "is_correct": false, "feedback_incorrect": "Incorrect. The probability is 1/4 because we multiply 1/2 × 1/2."}]}}}, {"id": "ie-screen5-conclusion", "type": "lesson_screen", "order": 5, "estimatedTimeSeconds": 30, "content": {"headline": "Independent Events: When Past Doesn't Affect Future", "body_md": "Great job! You now understand independent events and how to calculate their combined probabilities. This concept is fundamental in probability theory and has many applications in statistics and data analysis.", "visual": {"type": "unsplash_search", "value": "probability independence"}, "interactive_element": {"type": "button", "text": "Continue to Next Lesson", "action": "next_lesson"}}}]}, {"id": "dependent-events", "title": "Dependent Events: Linked Outcomes", "description": "See how one event affects subsequent ones.", "order": 4, "type": "interactive_lesson", "estimatedTimeMinutes": 10, "contentBlocks": [{"id": "de-screen1-intro", "type": "lesson_screen", "order": 1, "estimatedTimeSeconds": 30, "content": {"headline": "Dependent Events: When Outcomes Are Connected", "body_md": "Dependent events are events where the occurrence of one event affects the probability of the other event. Like drawing cards without replacement - each card you draw changes the composition of the deck for the next draw.", "visual": {"type": "giphy_search", "value": "card drawing probability"}, "interactive_element": {"type": "button", "text": "Let's Explore Dependence!", "action": "next_screen"}}}, {"id": "de-screen2-definition", "type": "lesson_screen", "order": 2, "estimatedTimeSeconds": 60, "content": {"headline": "Defining Dependence", "body_md": "Two events A and B are dependent if the probability of B occurring changes when we know that A has occurred.\n\nMathematically: P(B|A) ≠ P(B)\n\nWhere P(B|A) is the conditional probability of B given that A has occurred.", "visual": {"type": "unsplash_search", "value": "conditional probability"}, "interactive_element": {"type": "multiple_choice_text", "question": "Which of these pairs of events are dependent?", "options": [{"id": "a", "text": "Rolling a die twice", "is_correct": false, "feedback_incorrect": "Incorrect. Die rolls are independent events - the first roll doesn't affect the second."}, {"id": "b", "text": "Flipping a coin and rolling a die", "is_correct": false, "feedback_incorrect": "Incorrect. These events are independent - the coin flip doesn't affect the die roll."}, {"id": "c", "text": "Drawing two cards from a deck without replacement", "is_correct": true, "feedback_correct": "Correct! When you draw the first card, it changes the composition of the deck, affecting the probability for the second draw."}, {"id": "d", "text": "Spinning two different spinners", "is_correct": false, "feedback_incorrect": "Incorrect. Spinning different spinners creates independent events."}]}}}, {"id": "de-screen3-conditional-probability", "type": "lesson_screen", "order": 3, "estimatedTimeSeconds": 60, "content": {"headline": "Conditional Probability", "body_md": "Conditional probability is the probability of an event occurring given that another event has already occurred.\n\nThe formula is: P(B|A) = P(A and B) / P(A)\n\nFor example, if you draw 2 cards from a standard deck, the probability of getting a king on the second draw, given that the first card was a queen, is 4/51.", "visual": {"type": "giphy_search", "value": "conditional probability"}, "interactive_element": {"type": "text_input_quick", "question": "A bag contains 5 red marbles and 7 blue marbles. If you draw 2 marbles without replacement, what is the probability that the second marble is red, given that the first marble was blue?", "correct_answer_regex": "^5\\/11$|^0\\.4545$|^0\\.455$|^0\\.45$", "placeholder": "Enter your answer as a fraction or decimal", "feedback_correct": "Correct! After drawing a blue marble, there are 4 red and 7 blue marbles left. P(second is red | first is blue) = 5/11 ≈ 0.455", "feedback_incorrect": "Not quite. After drawing a blue marble, there are 5 red and 6 blue marbles left, for a total of 11 marbles. So P(second is red | first is blue) = 5/11."}}}, {"id": "de-screen4-multiplication-rule", "type": "lesson_screen", "order": 4, "estimatedTimeSeconds": 60, "content": {"headline": "The Multiplication Rule for Dependent Events", "body_md": "For dependent events A and B, the probability of both events occurring is:\n\nP(A and B) = P(A) × P(B|A)\n\nFor example, the probability of drawing 2 aces from a deck without replacement is:\nP(first ace) × P(second ace | first ace) = 4/52 × 3/51 = 1/221", "visual": {"type": "unsplash_search", "value": "probability cards"}, "interactive_element": {"type": "multiple_choice_text", "question": "What is the probability of drawing 2 hearts in a row from a standard deck without replacement?", "options": [{"id": "a", "text": "13/52 × 13/52 = 169/2704", "is_correct": false, "feedback_incorrect": "Incorrect. This calculation assumes independence, but the events are dependent."}, {"id": "b", "text": "13/52 × 12/51 = 156/2652 = 13/221", "is_correct": true, "feedback_correct": "Correct! P(first heart) = 13/52, and P(second heart | first heart) = 12/51, so P(both hearts) = 13/52 × 12/51 = 13/221"}, {"id": "c", "text": "1/4 × 1/4 = 1/16", "is_correct": false, "feedback_incorrect": "Incorrect. This calculation assumes independence, but the events are dependent."}, {"id": "d", "text": "13/52 × 13/51 = 169/2652", "is_correct": false, "feedback_incorrect": "Incorrect. After drawing the first heart, there are only 12 hearts left, not 13."}]}}}, {"id": "de-screen5-interactive", "type": "lesson_screen", "order": 5, "estimatedTimeSeconds": 180, "content": {"headline": "Try It Yourself: Conditional Probability Calculator", "body_md": "Now it's your turn to explore conditional probabilities! Use the interactive calculator below to see how dependent events work in different scenarios. Try different event combinations to build your understanding of conditional probability.", "visual": {"type": "unsplash_search", "value": "probability calculator"}, "interactive_element": {"type": "interactive_widget", "widget_type": "interactive_conditional_probability_calculator", "data": {"title": "Conditional Probability Calculator", "description": "Calculate conditional probabilities for different scenarios and see how events influence each other.", "scenarioTypes": ["cards", "dice", "marbles"], "scenarios": [{"title": "Drawing Cards", "description": "Drawing a heart given that the card is red", "type": "cards", "eventA": "red card", "eventB": "heart"}, {"title": "Drawing Cards", "description": "Drawing a face card given that the card is a spade", "type": "cards", "eventA": "spade", "eventB": "face card"}, {"title": "Drawing Marbles", "description": "Drawing a red marble after drawing a blue marble (without replacement)", "type": "marbles", "eventA": "first draw is blue", "eventB": "second draw is red"}, {"title": "Drawing Marbles", "description": "Drawing a green marble after drawing a red marble (without replacement)", "type": "marbles", "eventA": "first draw is red", "eventB": "second draw is green"}]}}}}, {"id": "de-screen6-conclusion", "type": "lesson_screen", "order": 6, "estimatedTimeSeconds": 30, "content": {"headline": "Dependent Events: When Past Affects Future", "body_md": "Excellent! You now understand dependent events and how to calculate their combined probabilities using conditional probability. This concept is crucial for analyzing many real-world scenarios where events influence each other.", "visual": {"type": "unsplash_search", "value": "probability dependence"}, "interactive_element": {"type": "button", "text": "Continue to Next Lesson", "action": "next_lesson"}}}]}, {"id": "addition-multiplication-rules", "title": "The Addition and Multiplication Rules (Visualized)", "description": "Understand how to combine probabilities.", "order": 5, "type": "interactive_lesson", "estimatedTimeMinutes": 10, "contentBlocks": [{"id": "amr-screen1-intro", "type": "lesson_screen", "order": 1, "estimatedTimeSeconds": 30, "content": {"headline": "Combining Probabilities: Addition and Multiplication", "body_md": "We've seen how to find the probability of multiple events occurring together (using multiplication). Now let's explore how to find the probability of at least one of several events occurring (using addition).", "visual": {"type": "giphy_search", "value": "probability venn diagram"}, "interactive_element": {"type": "button", "text": "Let's Explore Probability Rules!", "action": "next_screen"}}}, {"id": "amr-screen2-addition-rule", "type": "lesson_screen", "order": 2, "estimatedTimeSeconds": 60, "content": {"headline": "The Addition Rule", "body_md": "The addition rule helps us find the probability of either event A OR event B occurring.\n\nFor mutually exclusive events (can't happen together):\nP(A or B) = P(A) + P(B)\n\nFor non-mutually exclusive events (can happen together):\nP(A or B) = P(A) + P(B) - P(A and B)", "visual": {"type": "giphy_search", "value": "venn diagram probability"}, "interactive_element": {"type": "multiple_choice_text", "question": "Which of these pairs of events are mutually exclusive?", "options": [{"id": "a", "text": "Drawing a king and drawing a heart from a deck", "is_correct": false, "feedback_incorrect": "Incorrect. These events are not mutually exclusive because you can draw the king of hearts."}, {"id": "b", "text": "Rolling an even number and rolling an odd number on a die", "is_correct": true, "feedback_correct": "Correct! A number cannot be both even and odd, so these events are mutually exclusive."}, {"id": "c", "text": "Selecting a student who is female and selecting a student who is in the math club", "is_correct": false, "feedback_incorrect": "Incorrect. These events are not mutually exclusive because a student can be both female and in the math club."}, {"id": "d", "text": "Drawing a red card and drawing a black card from a deck", "is_correct": true, "feedback_correct": "Correct! A card cannot be both red and black, so these events are mutually exclusive."}]}}}, {"id": "amr-screen3-addition-examples", "type": "lesson_screen", "order": 3, "estimatedTimeSeconds": 60, "content": {"headline": "Addition Rule Examples", "body_md": "Example 1 (Mutually Exclusive):\nWhat is the probability of rolling a 1 OR a 6 on a die?\nP(1 or 6) = P(1) + P(6) = 1/6 + 1/6 = 2/6 = 1/3\n\nExample 2 (Not Mutually Exclusive):\nWhat is the probability of drawing a king OR a heart from a deck?\nP(king or heart) = P(king) + P(heart) - P(king of heart)\nP(king or heart) = 4/52 + 13/52 - 1/52 = 16/52 = 4/13", "visual": {"type": "unsplash_search", "value": "probability cards dice"}, "interactive_element": {"type": "text_input_quick", "question": "What is the probability of rolling an even number OR a number greater than 4 on a six-sided die?", "correct_answer_regex": "^2\\/3$|^0\\.6667$|^0\\.667$|^0\\.67$|^0\\.7$|^4\\/6$", "placeholder": "Enter your answer as a fraction or decimal", "feedback_correct": "Correct! P(even) = 3/6, P(>4) = 2/6, P(even and >4) = 1/6, so P(even or >4) = 3/6 + 2/6 - 1/6 = 4/6 = 2/3", "feedback_incorrect": "Not quite. Use the addition rule: P(A or B) = P(A) + P(B) - P(A and B). Even numbers are {2,4,6} and numbers >4 are {5,6}, with {6} in both."}}}, {"id": "amr-screen4-combined-rules", "type": "lesson_screen", "order": 4, "estimatedTimeSeconds": 60, "content": {"headline": "Combining Addition and Multiplication Rules", "body_md": "We can combine the addition and multiplication rules to solve more complex probability problems.\n\nFor example, when drawing 2 cards from a deck, what is the probability of getting at least one ace?\n\nP(at least one ace) = 1 - P(no aces) = 1 - P(non-ace and non-ace)\nP(at least one ace) = 1 - (48/52 × 47/51) = 1 - 0.8689 = 0.1311", "visual": {"type": "unsplash_search", "value": "probability cards"}, "interactive_element": {"type": "multiple_choice_text", "question": "When rolling two dice, what is the probability of getting at least one 6?", "options": [{"id": "a", "text": "1/6", "is_correct": false, "feedback_incorrect": "Incorrect. This is the probability of getting a 6 on one die, not at least one 6 when rolling two dice."}, {"id": "b", "text": "2/6 = 1/3", "is_correct": false, "feedback_incorrect": "Incorrect. This calculation doesn't account for the possibility of getting two 6s."}, {"id": "c", "text": "1 - (5/6)² = 11/36", "is_correct": true, "feedback_correct": "Correct! P(at least one 6) = 1 - P(no 6s) = 1 - (5/6)² = 1 - 25/36 = 11/36"}, {"id": "d", "text": "1/6 + 1/6 = 2/6 = 1/3", "is_correct": false, "feedback_incorrect": "Incorrect. This calculation incorrectly adds the probabilities without accounting for overlap."}]}}}, {"id": "amr-screen5-interactive", "type": "lesson_screen", "order": 5, "estimatedTimeSeconds": 180, "content": {"headline": "Try It Yourself: Probability Rules Visualizer", "body_md": "Now it's your turn to explore the addition and multiplication rules of probability! Use the interactive visualizer below to see how these rules work with different probability values. Experiment with different scenarios to deepen your understanding.", "visual": {"type": "unsplash_search", "value": "probability venn diagram"}, "interactive_element": {"type": "interactive_widget", "widget_type": "interactive_probability_rules_visualizer", "data": {"title": "Probability Rules Visualizer", "description": "Visualize addition and multiplication rules of probability using Venn diagrams and interactive controls.", "ruleTypes": ["addition", "multiplication"], "examples": [{"title": "Mutually Exclusive Events", "description": "Events that cannot occur together (e.g., rolling an odd or even number)", "rule": "addition", "probabilityA": 0.5, "probabilityB": 0.5, "probabilityAandB": 0.0, "mutuallyExclusive": true, "independent": false}, {"title": "Independent Events", "description": "Events where one does not affect the other (e.g., flipping two coins)", "rule": "multiplication", "probabilityA": 0.5, "probabilityB": 0.5, "probabilityAandB": 0.25, "mutuallyExclusive": false, "independent": true}, {"title": "Overlapping Events", "description": "Events that can occur together (e.g., drawing a heart or a face card)", "rule": "addition", "probabilityA": 0.25, "probabilityB": 0.25, "probabilityAandB": 0.0625, "mutuallyExclusive": false, "independent": false}, {"title": "Dependent Events", "description": "Events where one affects the other (e.g., drawing cards without replacement)", "rule": "multiplication", "probabilityA": 0.25, "probabilityB": 0.25, "probabilityAandB": 0.05, "mutuallyExclusive": false, "independent": false}]}}}}, {"id": "amr-screen6-conclusion", "type": "lesson_screen", "order": 6, "estimatedTimeSeconds": 30, "content": {"headline": "Probability Rules: Powerful Tools for Analysis", "body_md": "Great job! You now understand the addition and multiplication rules of probability and how to combine them. These powerful tools allow you to analyze complex scenarios involving multiple events.", "visual": {"type": "unsplash_search", "value": "probability mathematics"}, "interactive_element": {"type": "button", "text": "Continue to Module Test", "action": "next_lesson"}}}]}], "endOfModuleAssessment": {"id": "transformations-and-probability-rules-test", "title": "Transformations and Combined Probability", "description": "Test your understanding of function transformations and probability rules.", "type": "module_test_interactive", "estimatedTimeMinutes": 15, "passingScorePercentage": 70, "contentBlocks": [{"id": "tpr-test-intro", "type": "test_screen_intro", "order": 1, "estimatedTimeSeconds": 30, "content": {"headline": "Transformations and Probability Rules: Test Your Knowledge", "body_md": "Let's see how well you understand function transformations and probability rules!", "visual": {"type": "unsplash_search", "value": "mathematics test"}, "interactive_element": {"type": "button", "text": "Begin Test", "action": "next_screen"}}}, {"id": "tpr-test-q1", "type": "test_screen_question", "order": 2, "estimatedTimeSeconds": 60, "content": {"headline": "Question 1: Function Transformations", "body_md": "If f(x) = x², which function represents f(x) shifted left 3 units and down 2 units?", "interactive_element": {"type": "multiple_choice_text", "options": [{"id": "a", "text": "f(x) = (x - 3)² - 2", "is_correct": false, "feedback_incorrect": "Incorrect. This shifts the function right 3 units, not left."}, {"id": "b", "text": "f(x) = (x + 3)² - 2", "is_correct": true, "feedback_correct": "Correct! Adding 3 to the input shifts the graph left 3 units, and subtracting 2 shifts it down 2 units."}, {"id": "c", "text": "f(x) = (x - 3)² + 2", "is_correct": false, "feedback_incorrect": "Incorrect. This shifts the function right 3 units and up 2 units."}, {"id": "d", "text": "f(x) = (x + 3)² + 2", "is_correct": false, "feedback_incorrect": "Incorrect. This shifts the function left 3 units and up 2 units."}]}}}, {"id": "tpr-test-q2", "type": "test_screen_question", "order": 3, "estimatedTimeSeconds": 60, "content": {"headline": "Question 2: Vertical Stretching", "body_md": "How would you vertically stretch the function f(x) = sin(x) by a factor of 3?", "interactive_element": {"type": "multiple_choice_text", "options": [{"id": "a", "text": "f(x) = sin(3x)", "is_correct": false, "feedback_incorrect": "Incorrect. This horizontally compresses the function by a factor of 3, not vertically stretches it."}, {"id": "b", "text": "f(x) = 3sin(x)", "is_correct": true, "feedback_correct": "Correct! Multiplying the function by 3 stretches it vertically by a factor of 3."}, {"id": "c", "text": "f(x) = sin(x/3)", "is_correct": false, "feedback_incorrect": "Incorrect. This horizontally stretches the function by a factor of 3, not vertically stretches it."}, {"id": "d", "text": "f(x) = sin(x) + 3", "is_correct": false, "feedback_incorrect": "Incorrect. This shifts the function up 3 units, not stretches it."}]}}}, {"id": "tpr-test-q3", "type": "test_screen_question", "order": 4, "estimatedTimeSeconds": 60, "content": {"headline": "Question 3: Independent Events", "body_md": "A fair coin is flipped 3 times. What is the probability of getting exactly 2 heads?", "interactive_element": {"type": "text_input_quick", "correct_answer_regex": "^3\\/8$|^0\\.375$|^0\\.38$", "placeholder": "Enter your answer as a fraction or decimal", "feedback_correct": "Correct! There are 3 ways to get exactly 2 heads (HHT, HTH, THH) out of 8 possible outcomes, so P = 3/8 = 0.375", "feedback_incorrect": "Incorrect. There are 3 ways to get exactly 2 heads (HHT, HTH, THH) out of 8 possible outcomes, so P = 3/8 = 0.375"}}}, {"id": "tpr-test-q4", "type": "test_screen_question", "order": 5, "estimatedTimeSeconds": 60, "content": {"headline": "Question 4: Dependent Events", "body_md": "A bag contains 4 red marbles and 6 blue marbles. If you draw 2 marbles without replacement, what is the probability that both are red?", "interactive_element": {"type": "text_input_quick", "correct_answer_regex": "^6\\/45$|^2\\/15$|^0\\.1333$|^0\\.133$|^0\\.13$", "placeholder": "Enter your answer as a fraction or decimal", "feedback_correct": "Correct! P(first red) = 4/10, P(second red | first red) = 3/9, so P(both red) = 4/10 × 3/9 = 12/90 = 2/15 ≈ 0.133", "feedback_incorrect": "Incorrect. Use the multiplication rule for dependent events: P(first red) × P(second red | first red) = 4/10 × 3/9 = 12/90 = 2/15"}}}, {"id": "tpr-test-q5", "type": "test_screen_question", "order": 6, "estimatedTimeSeconds": 60, "content": {"headline": "Question 5: Addition Rule", "body_md": "When rolling a six-sided die, what is the probability of rolling an even number OR a number less than 3?", "interactive_element": {"type": "multiple_choice_text", "options": [{"id": "a", "text": "3/6 + 2/6 = 5/6", "is_correct": false, "feedback_incorrect": "Incorrect. This calculation doesn't account for the overlap between the two events."}, {"id": "b", "text": "3/6 + 2/6 - 1/6 = 4/6 = 2/3", "is_correct": true, "feedback_correct": "Correct! P(even) = 3/6, P(<3) = 2/6, P(even and <3) = 1/6, so P(even or <3) = 3/6 + 2/6 - 1/6 = 4/6 = 2/3"}, {"id": "c", "text": "3/6 × 2/6 = 6/36 = 1/6", "is_correct": false, "feedback_incorrect": "Incorrect. This uses the multiplication rule, which is for finding the probability of both events occurring, not either event."}, {"id": "d", "text": "1 - (3/6 × 4/6) = 1 - 12/36 = 1 - 1/3 = 2/3", "is_correct": false, "feedback_incorrect": "Incorrect. This calculation is not appropriate for this problem."}]}}}, {"id": "tpr-test-conclusion", "type": "test_screen_conclusion", "order": 7, "estimatedTimeSeconds": 30, "content": {"headline": "Transformations and Probability Rules: Test Complete", "body_md": "Great job completing the test! You've demonstrated your understanding of function transformations and probability rules.", "visual": {"type": "unsplash_search", "value": "mathematics success"}, "interactive_element": {"type": "button", "text": "Return to Module", "action": "return_to_module"}}}]}}