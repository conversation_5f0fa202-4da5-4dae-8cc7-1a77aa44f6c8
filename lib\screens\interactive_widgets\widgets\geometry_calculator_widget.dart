import 'dart:math' as math;
import 'package:flutter/material.dart';

class GeometryCalculatorWidget extends StatefulWidget {
  final Map<String, dynamic> data;

  const GeometryCalculatorWidget({
    super.key,
    this.data = const {},
  });

  @override
  State<GeometryCalculatorWidget> createState() =>
      _GeometryCalculatorWidgetState();
}

class _GeometryCalculatorWidgetState extends State<GeometryCalculatorWidget> {
  late String _selectedShape;
  late String _selectedCalculation;
  final Map<String, List<TextEditingController>> _controllers = {};
  final Map<String, List<FocusNode>> _focusNodes = {};
  final Map<String, String> _results = {};

  @override
  void initState() {
    super.initState();
    _initializeCalculator();
  }

  @override
  void dispose() {
    // Dispose all controllers and focus nodes
    for (final controllers in _controllers.values) {
      for (final controller in controllers) {
        controller.dispose();
      }
    }
    for (final focusNodes in _focusNodes.values) {
      for (final focusNode in focusNodes) {
        focusNode.dispose();
      }
    }
    super.dispose();
  }

  void _initializeCalculator() {
    final shapes = List<String>.from(widget.data['shapes'] ?? ['Circle', 'Square', 'Rectangle', 'Triangle']);
    final defaultShape =
        widget.data['defaultShape'] as String? ??
        (shapes.isNotEmpty ? shapes[0] : 'Circle');
    final calculations =
        widget.data['calculations'] as Map<String, dynamic>? ?? {
          'Circle': ['Area', 'Circumference'],
          'Square': ['Area', 'Perimeter'],
          'Rectangle': ['Area', 'Perimeter'],
          'Triangle': ['Area', 'Perimeter'],
          'Sphere': ['Surface Area', 'Volume'],
          'Cube': ['Surface Area', 'Volume'],
        };

    _selectedShape = defaultShape;

    // Initialize controllers and focus nodes for each shape
    for (final shape in shapes) {
      final shapeCalculations = List<String>.from(calculations[shape] ?? []);
      _selectedCalculation =
          shapeCalculations.isNotEmpty ? shapeCalculations[0] : '';

      // Create controllers and focus nodes based on shape
      switch (shape) {
        case 'Circle':
          _controllers[shape] = [TextEditingController()]; // radius
          _focusNodes[shape] = [FocusNode()];
          break;
        case 'Square':
          _controllers[shape] = [TextEditingController()]; // side
          _focusNodes[shape] = [FocusNode()];
          break;
        case 'Rectangle':
          _controllers[shape] = [
            TextEditingController(), // length
            TextEditingController(), // width
          ];
          _focusNodes[shape] = [FocusNode(), FocusNode()];
          break;
        case 'Triangle':
          _controllers[shape] = [
            TextEditingController(), // Base or Side 1
            TextEditingController(), // Height or Side 2
            TextEditingController(), // Side 3 (for perimeter)
          ];
          _focusNodes[shape] = [FocusNode(), FocusNode(), FocusNode()];
          break;
        case 'Sphere':
          _controllers[shape] = [TextEditingController()]; // radius
          _focusNodes[shape] = [FocusNode()];
          break;
        case 'Cube':
          _controllers[shape] = [TextEditingController()]; // side
          _focusNodes[shape] = [FocusNode()];
          break;
        default:
          _controllers[shape] = [TextEditingController()];
          _focusNodes[shape] = [FocusNode()];
      }
    }
  }

  void _calculate() {
    final calculations =
        widget.data['calculations'] as Map<String, dynamic>? ?? {
          'Circle': ['Area', 'Circumference'],
          'Square': ['Area', 'Perimeter'],
          'Rectangle': ['Area', 'Perimeter'],
          'Triangle': ['Area', 'Perimeter'],
          'Sphere': ['Surface Area', 'Volume'],
          'Cube': ['Surface Area', 'Volume'],
        };
    final shapeCalculations = List<String>.from(
      calculations[_selectedShape] ?? [],
    );

    if (!shapeCalculations.contains(_selectedCalculation)) return;

    String result = '';

    try {
      switch (_selectedShape) {
        case 'Circle':
          final radius =
              double.tryParse(_controllers[_selectedShape]![0].text) ?? 0;
          if (_selectedCalculation == 'Area') {
            result = (math.pi * radius * radius).toStringAsFixed(2);
          } else if (_selectedCalculation == 'Circumference') {
            result = (2 * math.pi * radius).toStringAsFixed(2);
          }
          break;

        case 'Square':
          final side =
              double.tryParse(_controllers[_selectedShape]![0].text) ?? 0;
          if (_selectedCalculation == 'Area') {
            result = (side * side).toStringAsFixed(2);
          } else if (_selectedCalculation == 'Perimeter') {
            result = (4 * side).toStringAsFixed(2);
          }
          break;

        case 'Rectangle':
          final length =
              double.tryParse(_controllers[_selectedShape]![0].text) ?? 0;
          final width =
              double.tryParse(_controllers[_selectedShape]![1].text) ?? 0;
          if (_selectedCalculation == 'Area') {
            result = (length * width).toStringAsFixed(2);
          } else if (_selectedCalculation == 'Perimeter') {
            result = (2 * (length + width)).toStringAsFixed(2);
          }
          break;

        case 'Triangle':
          if (_selectedCalculation == 'Area') {
            final base =
                double.tryParse(_controllers[_selectedShape]![0].text) ?? 0;
            final height =
                double.tryParse(_controllers[_selectedShape]![1].text) ?? 0;
            if (base > 0 && height > 0) {
              result = (0.5 * base * height).toStringAsFixed(2);
            } else {
              result = 'Invalid input';
            }
          } else if (_selectedCalculation == 'Perimeter') {
            final side1 =
                double.tryParse(_controllers[_selectedShape]![0].text) ?? 0;
            final side2 =
                double.tryParse(_controllers[_selectedShape]![1].text) ?? 0;
            final side3 =
                double.tryParse(_controllers[_selectedShape]![2].text) ?? 0;
            if (side1 > 0 && side2 > 0 && side3 > 0) {
              // Basic triangle inequality check could be added here if desired
              result = (side1 + side2 + side3).toStringAsFixed(2);
            } else {
              result = 'Invalid input';
            }
          }
          break;

        case 'Sphere':
          final radius =
              double.tryParse(_controllers[_selectedShape]![0].text) ?? 0;
          if (_selectedCalculation == 'Surface Area') {
            result = (4 * math.pi * radius * radius).toStringAsFixed(2);
          } else if (_selectedCalculation == 'Volume') {
            result = ((4 / 3) * math.pi * radius * radius * radius)
                .toStringAsFixed(2);
          }
          break;

        case 'Cube':
          final side =
              double.tryParse(_controllers[_selectedShape]![0].text) ?? 0;
          if (_selectedCalculation == 'Surface Area') {
            result = (6 * side * side).toStringAsFixed(2);
          } else if (_selectedCalculation == 'Volume') {
            result = (side * side * side).toStringAsFixed(2);
          }
          break;
      }
    } catch (e) {
      result = 'Error';
    }

    setState(() {
      _results[_selectedShape] = result;
    });
  }

  @override
  Widget build(BuildContext context) {
    final shapes = List<String>.from(widget.data['shapes'] ?? ['Circle', 'Square', 'Rectangle', 'Triangle']);
    final calculations =
        widget.data['calculations'] as Map<String, dynamic>? ?? {
          'Circle': ['Area', 'Circumference'],
          'Square': ['Area', 'Perimeter'],
          'Rectangle': ['Area', 'Perimeter'],
          'Triangle': ['Area', 'Perimeter'],
          'Sphere': ['Surface Area', 'Volume'],
          'Cube': ['Surface Area', 'Volume'],
        };
    final shapeCalculations = List<String>.from(
      calculations[_selectedShape] ?? [],
    );

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Shape selector
        Padding(
          padding: const EdgeInsets.only(bottom: 16.0),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              const Text(
                'Select Shape:',
                style: TextStyle(fontWeight: FontWeight.bold, fontSize: 16),
              ),
              const SizedBox(height: 8),
              SingleChildScrollView(
                scrollDirection: Axis.horizontal,
                child: Row(
                  children:
                      shapes.map((shape) {
                        return Padding(
                          padding: const EdgeInsets.only(right: 8.0),
                          child: ChoiceChip(
                            label: Text(shape),
                            selected: _selectedShape == shape,
                            onSelected: (selected) {
                              if (selected) {
                                setState(() {
                                  _selectedShape = shape;
                                  final shapeCalcs = List<String>.from(
                                    calculations[shape] ?? [],
                                  );
                                  _selectedCalculation =
                                      shapeCalcs.isNotEmpty
                                          ? shapeCalcs[0]
                                          : '';
                                });
                              }
                            },
                          ),
                        );
                      }).toList(),
                ),
              ),
            ],
          ),
        ),

        // Calculation selector
        if (shapeCalculations.isNotEmpty)
          Padding(
            padding: const EdgeInsets.only(bottom: 16.0),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                const Text(
                  'Select Calculation:',
                  style: TextStyle(fontWeight: FontWeight.bold, fontSize: 16),
                ),
                const SizedBox(height: 8),
                Row(
                  children:
                      shapeCalculations.map((calculation) {
                        return Padding(
                          padding: const EdgeInsets.only(right: 8.0),
                          child: ChoiceChip(
                            label: Text(calculation),
                            selected: _selectedCalculation == calculation,
                            onSelected: (selected) {
                              if (selected) {
                                setState(() {
                                  _selectedCalculation = calculation;
                                });
                              }
                            },
                          ),
                        );
                      }).toList(),
                ),
              ],
            ),
          ),

        // Input fields
        Padding(
          padding: const EdgeInsets.only(bottom: 16.0),
          child: _buildInputFields(),
        ),

        // Calculate button
        ElevatedButton(
          onPressed: _calculate,
          style: ElevatedButton.styleFrom(
            backgroundColor: Colors.blue,
            foregroundColor: Colors.white,
            padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
          ),
          child: const Text('Calculate'),
        ),

        // Result
        if (_results.containsKey(_selectedShape) &&
            _results[_selectedShape]!.isNotEmpty)
          Padding(
            padding: const EdgeInsets.only(top: 16.0),
            child: Container(
              width: double.infinity,
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: Colors.green[50],
                borderRadius: BorderRadius.circular(8),
                border: Border.all(color: Colors.green[300]!),
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    '$_selectedCalculation:',
                    style: const TextStyle(
                      fontWeight: FontWeight.bold,
                      fontSize: 16,
                    ),
                  ),
                  const SizedBox(height: 8),
                  Text(
                    _results[_selectedShape]!,
                    style: const TextStyle(
                      fontSize: 24,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  const SizedBox(height: 4),
                  Text(
                    _getResultUnit(),
                    style: TextStyle(
                      fontSize: 14,
                      color: Colors.grey[700],
                      fontStyle: FontStyle.italic,
                    ),
                  ),
                ],
              ),
            ),
          ),
      ],
    );
  }

  Widget _buildInputFields() {
    switch (_selectedShape) {
      case 'Circle':
        return _buildCircleInputs();
      case 'Square':
        return _buildSquareInputs();
      case 'Rectangle':
        return _buildRectangleInputs();
      case 'Triangle':
        return _buildTriangleInputs();
      case 'Sphere':
        return _buildSphereInputs();
      case 'Cube':
        return _buildCubeInputs();
      default:
        return const SizedBox.shrink();
    }
  }

  Widget _buildCircleInputs() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          'Enter Radius:',
          style: TextStyle(fontWeight: FontWeight.bold),
        ),
        const SizedBox(height: 8),
        TextField(
          controller: _controllers[_selectedShape]![0],
          focusNode: _focusNodes[_selectedShape]![0],
          keyboardType: TextInputType.number,
          decoration: const InputDecoration(
            labelText: 'Radius',
            border: OutlineInputBorder(),
            suffixText: 'units',
          ),
          onSubmitted: (_) => _calculate(),
        ),
      ],
    );
  }

  Widget _buildSquareInputs() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          'Enter Side Length:',
          style: TextStyle(fontWeight: FontWeight.bold),
        ),
        const SizedBox(height: 8),
        TextField(
          controller: _controllers[_selectedShape]![0],
          focusNode: _focusNodes[_selectedShape]![0],
          keyboardType: TextInputType.number,
          decoration: const InputDecoration(
            labelText: 'Side',
            border: OutlineInputBorder(),
            suffixText: 'units',
          ),
          onSubmitted: (_) => _calculate(),
        ),
      ],
    );
  }

  Widget _buildRectangleInputs() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          'Enter Dimensions:',
          style: TextStyle(fontWeight: FontWeight.bold),
        ),
        const SizedBox(height: 8),
        TextField(
          controller: _controllers[_selectedShape]![0],
          focusNode: _focusNodes[_selectedShape]![0],
          keyboardType: TextInputType.number,
          decoration: const InputDecoration(
            labelText: 'Length',
            border: OutlineInputBorder(),
            suffixText: 'units',
          ),
        ),
        const SizedBox(height: 8),
        TextField(
          controller: _controllers[_selectedShape]![1],
          focusNode: _focusNodes[_selectedShape]![1],
          keyboardType: TextInputType.number,
          decoration: const InputDecoration(
            labelText: 'Width',
            border: OutlineInputBorder(),
            suffixText: 'units',
          ),
          onSubmitted: (_) => _calculate(),
        ),
      ],
    );
  }

  Widget _buildTriangleInputs() {
    if (_selectedCalculation == 'Area') {
      return Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text(
            'Enter Triangle Dimensions for Area:',
            style: TextStyle(fontWeight: FontWeight.bold),
          ),
          const SizedBox(height: 8),
          TextField(
            controller: _controllers[_selectedShape]![0],
            focusNode: _focusNodes[_selectedShape]![0],
            keyboardType: TextInputType.number,
            decoration: const InputDecoration(
              labelText: 'Base',
              border: OutlineInputBorder(),
              suffixText: 'units',
            ),
          ),
          const SizedBox(height: 8),
          TextField(
            controller: _controllers[_selectedShape]![1],
            focusNode: _focusNodes[_selectedShape]![1],
            keyboardType: TextInputType.number,
            decoration: const InputDecoration(
              labelText: 'Height',
              border: OutlineInputBorder(),
              suffixText: 'units',
            ),
            onSubmitted: (_) => _calculate(),
          ),
        ],
      );
    } else if (_selectedCalculation == 'Perimeter') {
      return Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text(
            'Enter Triangle Dimensions for Perimeter:',
            style: TextStyle(fontWeight: FontWeight.bold),
          ),
          const SizedBox(height: 8),
          TextField(
            controller: _controllers[_selectedShape]![0],
            focusNode: _focusNodes[_selectedShape]![0],
            keyboardType: TextInputType.number,
            decoration: const InputDecoration(
              labelText: 'Side 1',
              border: OutlineInputBorder(),
              suffixText: 'units',
            ),
          ),
          const SizedBox(height: 8),
          TextField(
            controller: _controllers[_selectedShape]![1],
            focusNode: _focusNodes[_selectedShape]![1],
            keyboardType: TextInputType.number,
            decoration: const InputDecoration(
              labelText: 'Side 2',
              border: OutlineInputBorder(),
              suffixText: 'units',
            ),
          ),
          const SizedBox(height: 8),
          TextField(
            controller: _controllers[_selectedShape]![2],
            focusNode: _focusNodes[_selectedShape]![2],
            keyboardType: TextInputType.number,
            decoration: const InputDecoration(
              labelText: 'Side 3',
              border: OutlineInputBorder(),
              suffixText: 'units',
            ),
            onSubmitted: (_) => _calculate(),
          ),
        ],
      );
    }
    return const SizedBox.shrink(); // Should not happen
  }

  Widget _buildSphereInputs() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          'Enter Radius:',
          style: TextStyle(fontWeight: FontWeight.bold),
        ),
        const SizedBox(height: 8),
        TextField(
          controller: _controllers[_selectedShape]![0],
          focusNode: _focusNodes[_selectedShape]![0],
          keyboardType: TextInputType.number,
          decoration: const InputDecoration(
            labelText: 'Radius',
            border: OutlineInputBorder(),
            suffixText: 'units',
          ),
          onSubmitted: (_) => _calculate(),
        ),
      ],
    );
  }

  Widget _buildCubeInputs() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          'Enter Side Length:',
          style: TextStyle(fontWeight: FontWeight.bold),
        ),
        const SizedBox(height: 8),
        TextField(
          controller: _controllers[_selectedShape]![0],
          focusNode: _focusNodes[_selectedShape]![0],
          keyboardType: TextInputType.number,
          decoration: const InputDecoration(
            labelText: 'Side',
            border: OutlineInputBorder(),
            suffixText: 'units',
          ),
          onSubmitted: (_) => _calculate(),
        ),
      ],
    );
  }

  String _getResultUnit() {
    if (_selectedCalculation.contains('Area')) {
      return 'square units';
    } else if (_selectedCalculation.contains('Volume')) {
      return 'cubic units';
    } else {
      return 'units';
    }
  }
}
