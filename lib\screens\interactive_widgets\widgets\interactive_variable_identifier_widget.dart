import 'package:flutter/material.dart';

/// A widget that helps users identify and classify variables in scientific experiments
/// Users can learn to distinguish between independent, dependent, and controlled variables
class InteractiveVariableIdentifierWidget extends StatefulWidget {
  /// The data for this widget
  final Map<String, dynamic> data;

  /// Optional callback when the widget state changes
  final Function(bool isCompleted)? onStateChanged;

  const InteractiveVariableIdentifierWidget({
    Key? key,
    required this.data,
    this.onStateChanged,
  }) : super(key: key);

  /// Factory constructor to create the widget from JSON data
  factory InteractiveVariableIdentifierWidget.fromData(Map<String, dynamic> data) {
    return InteractiveVariableIdentifierWidget(
      data: data,
    );
  }

  @override
  State<InteractiveVariableIdentifierWidget> createState() => _InteractiveVariableIdentifierWidgetState();
}

class _InteractiveVariableIdentifierWidgetState extends State<InteractiveVariableIdentifierWidget> {
  // Experiment scenarios
  late List<ExperimentScenario> _scenarios;
  late int _currentScenarioIndex;
  
  // UI state
  late bool _isCompleted;
  late bool _showExplanation;
  late bool _showFeedback;
  late bool _isCorrect;
  
  // User selections
  late String _selectedIndependentVariable;
  late String _selectedDependentVariable;
  late List<String> _selectedControlledVariables;
  
  // Colors
  late Color _primaryColor;
  late Color _secondaryColor;
  late Color _accentColor;
  late Color _backgroundColor;
  late Color _textColor;

  @override
  void initState() {
    super.initState();
    
    // Initialize colors
    _primaryColor = _getColorFromHex(widget.data['primaryColor'] ?? '#2196F3');
    _secondaryColor = _getColorFromHex(widget.data['secondaryColor'] ?? '#FF9800');
    _accentColor = _getColorFromHex(widget.data['accentColor'] ?? '#4CAF50');
    _backgroundColor = _getColorFromHex(widget.data['backgroundColor'] ?? '#FFFFFF');
    _textColor = _getColorFromHex(widget.data['textColor'] ?? '#212121');
    
    // Initialize scenarios
    _initializeScenarios();
    
    // Initialize state
    _currentScenarioIndex = 0;
    _isCompleted = false;
    _showExplanation = false;
    _showFeedback = false;
    _isCorrect = false;
    
    // Initialize selections
    _resetSelections();
  }

  // Initialize the experiment scenarios
  void _initializeScenarios() {
    final List<dynamic> scenariosData = widget.data['scenarios'] ?? _getDefaultScenarios();
    
    _scenarios = scenariosData.map((scenarioData) {
      return ExperimentScenario(
        title: scenarioData['title'] ?? '',
        description: scenarioData['description'] ?? '',
        variables: List<String>.from(scenarioData['variables'] ?? []),
        independentVariable: scenarioData['independentVariable'] ?? '',
        dependentVariable: scenarioData['dependentVariable'] ?? '',
        controlledVariables: List<String>.from(scenarioData['controlledVariables'] ?? []),
        explanation: scenarioData['explanation'] ?? '',
      );
    }).toList();
  }

  // Get default scenarios if none are provided
  List<Map<String, dynamic>> _getDefaultScenarios() {
    return [
      {
        'title': 'Plant Growth Experiment',
        'description': 'A scientist wants to determine how the amount of sunlight affects plant growth. They set up an experiment with identical plants, soil, water, and temperature, but vary the hours of sunlight each plant receives. They measure the height of each plant after 2 weeks.',
        'variables': [
          'Amount of sunlight',
          'Plant height',
          'Type of plant',
          'Amount of water',
          'Soil composition',
          'Temperature',
          'Container size',
        ],
        'independentVariable': 'Amount of sunlight',
        'dependentVariable': 'Plant height',
        'controlledVariables': [
          'Type of plant',
          'Amount of water',
          'Soil composition',
          'Temperature',
          'Container size',
        ],
        'explanation': 'In this experiment, the amount of sunlight is the independent variable because it is what the scientist is deliberately changing. The plant height is the dependent variable because it is what is being measured to see how it responds to changes in sunlight. The other factors (type of plant, amount of water, soil composition, temperature, and container size) are controlled variables because they are kept constant to ensure that any changes in plant height are due to changes in sunlight, not other factors.',
      },
      {
        'title': 'Exercise and Heart Rate',
        'description': 'A researcher is investigating how exercise intensity affects heart rate. Participants exercise at different intensities (walking, jogging, running) for the same amount of time. The researcher measures each participant\'s heart rate immediately after exercise.',
        'variables': [
          'Exercise intensity',
          'Heart rate',
          'Duration of exercise',
          'Age of participants',
          'Fitness level of participants',
          'Time of day',
          'Room temperature',
        ],
        'independentVariable': 'Exercise intensity',
        'dependentVariable': 'Heart rate',
        'controlledVariables': [
          'Duration of exercise',
          'Age of participants',
          'Fitness level of participants',
          'Time of day',
          'Room temperature',
        ],
        'explanation': 'In this experiment, exercise intensity is the independent variable because it is what the researcher is deliberately changing. Heart rate is the dependent variable because it is what is being measured to see how it responds to changes in exercise intensity. The other factors (duration of exercise, age and fitness level of participants, time of day, and room temperature) are controlled variables because they are kept constant to ensure that any changes in heart rate are due to changes in exercise intensity, not other factors.',
      },
      {
        'title': 'Study Time and Test Scores',
        'description': 'An educator wants to determine if the amount of time spent studying affects test scores. Students are assigned different study times (30 minutes, 60 minutes, 90 minutes) and then take the same test. The educator records each student\'s test score.',
        'variables': [
          'Study time',
          'Test score',
          'Difficulty of test',
          'Prior knowledge of subject',
          'Time of day for studying',
          'Study environment',
          'Student\'s age',
        ],
        'independentVariable': 'Study time',
        'dependentVariable': 'Test score',
        'controlledVariables': [
          'Difficulty of test',
          'Prior knowledge of subject',
          'Time of day for studying',
          'Study environment',
          'Student\'s age',
        ],
        'explanation': 'In this experiment, study time is the independent variable because it is what the educator is deliberately changing. Test score is the dependent variable because it is what is being measured to see how it responds to changes in study time. The other factors (difficulty of test, prior knowledge, time of day, study environment, and student\'s age) are controlled variables because they are kept constant to ensure that any changes in test scores are due to changes in study time, not other factors.',
      },
    ];
  }

  // Get color from hex string
  Color _getColorFromHex(String hexColor) {
    hexColor = hexColor.replaceAll('#', '');
    if (hexColor.length == 6) {
      hexColor = 'FF$hexColor';
    }
    return Color(int.parse(hexColor, radix: 16));
  }

  // Reset user selections
  void _resetSelections() {
    _selectedIndependentVariable = '';
    _selectedDependentVariable = '';
    _selectedControlledVariables = [];
    _showFeedback = false;
  }

  // Select an independent variable
  void _selectIndependentVariable(String variable) {
    setState(() {
      _selectedIndependentVariable = variable;
    });
  }

  // Select a dependent variable
  void _selectDependentVariable(String variable) {
    setState(() {
      _selectedDependentVariable = variable;
    });
  }

  // Toggle a controlled variable selection
  void _toggleControlledVariable(String variable) {
    setState(() {
      if (_selectedControlledVariables.contains(variable)) {
        _selectedControlledVariables.remove(variable);
      } else {
        _selectedControlledVariables.add(variable);
      }
    });
  }

  // Check if the user's selections are correct
  void _checkAnswers() {
    final scenario = _scenarios[_currentScenarioIndex];
    
    bool isIndependentCorrect = _selectedIndependentVariable == scenario.independentVariable;
    bool isDependentCorrect = _selectedDependentVariable == scenario.dependentVariable;
    
    // Check if controlled variables match (order doesn't matter)
    bool areControlledCorrect = _selectedControlledVariables.length == scenario.controlledVariables.length &&
                               _selectedControlledVariables.every((variable) => scenario.controlledVariables.contains(variable));
    
    setState(() {
      _isCorrect = isIndependentCorrect && isDependentCorrect && areControlledCorrect;
      _showFeedback = true;
    });
  }

  // Go to the next scenario
  void _nextScenario() {
    if (_currentScenarioIndex < _scenarios.length - 1) {
      setState(() {
        _currentScenarioIndex++;
        _resetSelections();
        _showExplanation = false;
      });
    } else if (!_isCompleted) {
      setState(() {
        _isCompleted = true;
      });
      widget.onStateChanged?.call(true);
    }
  }

  // Go to the previous scenario
  void _previousScenario() {
    if (_currentScenarioIndex > 0) {
      setState(() {
        _currentScenarioIndex--;
        _resetSelections();
        _showExplanation = false;
      });
    }
  }

  // Toggle explanation visibility
  void _toggleExplanation() {
    setState(() {
      _showExplanation = !_showExplanation;
    });
  }

  // Reset the widget
  void _resetWidget() {
    setState(() {
      _currentScenarioIndex = 0;
      _resetSelections();
      _isCompleted = false;
      _showExplanation = false;
    });
  }

  @override
  Widget build(BuildContext context) {
    final scenario = _scenarios[_currentScenarioIndex];
    
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: _backgroundColor,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withOpacity(0.2),
            spreadRadius: 1,
            blurRadius: 3,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Title
          Text(
            widget.data['title'] ?? 'Variable Identifier',
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
              color: _textColor,
            ),
          ),
          
          const SizedBox(height: 8),
          
          // Scenario progress
          Row(
            children: [
              Text(
                'Scenario ${_currentScenarioIndex + 1} of ${_scenarios.length}',
                style: TextStyle(
                  fontSize: 14,
                  fontWeight: FontWeight.bold,
                  color: _primaryColor,
                ),
              ),
              const Spacer(),
              if (_showFeedback && _isCorrect)
                Icon(Icons.check_circle, color: _accentColor),
              if (_showFeedback && !_isCorrect)
                Icon(Icons.cancel, color: _secondaryColor),
            ],
          ),
          
          const SizedBox(height: 8),
          
          // Scenario title
          Text(
            scenario.title,
            style: TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.bold,
              color: _textColor,
            ),
          ),
          
          const SizedBox(height: 8),
          
          // Scenario description
          Container(
            padding: const EdgeInsets.all(12),
            decoration: BoxDecoration(
              color: _primaryColor.withOpacity(0.1),
              borderRadius: BorderRadius.circular(8),
              border: Border.all(color: _primaryColor.withOpacity(0.3)),
            ),
            child: Text(
              scenario.description,
              style: TextStyle(
                fontSize: 14,
                color: _textColor,
              ),
            ),
          ),
          
          const SizedBox(height: 16),
          
          // Variable selection
          Text(
            'Identify the variables in this experiment:',
            style: TextStyle(
              fontSize: 14,
              fontWeight: FontWeight.bold,
              color: _textColor,
            ),
          ),
          
          const SizedBox(height: 8),
          
          // Independent variable selection
          Text(
            'Independent Variable (what is changed):',
            style: TextStyle(
              fontSize: 14,
              color: _textColor,
            ),
          ),
          
          const SizedBox(height: 4),
          
          Wrap(
            spacing: 8,
            runSpacing: 8,
            children: scenario.variables.map((variable) {
              return ChoiceChip(
                label: Text(variable),
                selected: _selectedIndependentVariable == variable,
                onSelected: (selected) {
                  if (selected) _selectIndependentVariable(variable);
                },
              );
            }).toList(),
          ),
          
          const SizedBox(height: 8),
          
          // Dependent variable selection
          Text(
            'Dependent Variable (what is measured):',
            style: TextStyle(
              fontSize: 14,
              color: _textColor,
            ),
          ),
          
          const SizedBox(height: 4),
          
          Wrap(
            spacing: 8,
            runSpacing: 8,
            children: scenario.variables.map((variable) {
              return ChoiceChip(
                label: Text(variable),
                selected: _selectedDependentVariable == variable,
                onSelected: (selected) {
                  if (selected) _selectDependentVariable(variable);
                },
              );
            }).toList(),
          ),
          
          const SizedBox(height: 8),
          
          // Controlled variables selection
          Text(
            'Controlled Variables (what is kept constant):',
            style: TextStyle(
              fontSize: 14,
              color: _textColor,
            ),
          ),
          
          const SizedBox(height: 4),
          
          Wrap(
            spacing: 8,
            runSpacing: 8,
            children: scenario.variables.map((variable) {
              return FilterChip(
                label: Text(variable),
                selected: _selectedControlledVariables.contains(variable),
                onSelected: (selected) {
                  _toggleControlledVariable(variable);
                },
              );
            }).toList(),
          ),
          
          const SizedBox(height: 16),
          
          // Feedback
          if (_showFeedback)
            Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: _isCorrect ? _accentColor.withOpacity(0.1) : _secondaryColor.withOpacity(0.1),
                borderRadius: BorderRadius.circular(8),
                border: Border.all(
                  color: _isCorrect ? _accentColor.withOpacity(0.3) : _secondaryColor.withOpacity(0.3),
                ),
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    _isCorrect ? 'Correct!' : 'Not quite right. Try again!',
                    style: TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.bold,
                      color: _isCorrect ? _accentColor : _secondaryColor,
                    ),
                  ),
                  if (!_isCorrect) ...[
                    const SizedBox(height: 8),
                    Text(
                      'Review the experiment description and try to identify the variables correctly.',
                      style: TextStyle(
                        fontSize: 14,
                        color: _textColor,
                      ),
                    ),
                  ],
                ],
              ),
            ),
          
          const SizedBox(height: 16),
          
          // Explanation
          if (_showExplanation)
            Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: _accentColor.withOpacity(0.1),
                borderRadius: BorderRadius.circular(8),
                border: Border.all(color: _accentColor.withOpacity(0.3)),
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'Explanation:',
                    style: TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.bold,
                      color: _accentColor,
                    ),
                  ),
                  const SizedBox(height: 8),
                  Text(
                    scenario.explanation,
                    style: TextStyle(
                      fontSize: 14,
                      color: _textColor,
                    ),
                  ),
                ],
              ),
            ),
          
          const SizedBox(height: 16),
          
          // Navigation buttons
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              ElevatedButton(
                onPressed: _currentScenarioIndex > 0 ? _previousScenario : null,
                style: ElevatedButton.styleFrom(
                  backgroundColor: _secondaryColor,
                  foregroundColor: Colors.white,
                ),
                child: const Text('Previous'),
              ),
              if (_showFeedback && _isCorrect)
                ElevatedButton(
                  onPressed: _toggleExplanation,
                  style: ElevatedButton.styleFrom(
                    backgroundColor: _accentColor,
                    foregroundColor: Colors.white,
                  ),
                  child: Text(_showExplanation ? 'Hide Explanation' : 'Show Explanation'),
                )
              else
                ElevatedButton(
                  onPressed: _selectedIndependentVariable.isNotEmpty && 
                             _selectedDependentVariable.isNotEmpty && 
                             _selectedControlledVariables.isNotEmpty ? 
                             _checkAnswers : null,
                  style: ElevatedButton.styleFrom(
                    backgroundColor: _accentColor,
                    foregroundColor: Colors.white,
                  ),
                  child: const Text('Check Answers'),
                ),
              ElevatedButton(
                onPressed: (_showFeedback && _isCorrect) ? 
                           (_currentScenarioIndex < _scenarios.length - 1 ? _nextScenario : (_isCompleted ? _resetWidget : _nextScenario)) : 
                           null,
                style: ElevatedButton.styleFrom(
                  backgroundColor: _primaryColor,
                  foregroundColor: Colors.white,
                ),
                child: Text(_currentScenarioIndex < _scenarios.length - 1 ? 'Next' : (_isCompleted ? 'Restart' : 'Complete')),
              ),
            ],
          ),
          
          // Widget name tag at the bottom
          if (widget.data['showNameTag'] ?? true)
            Padding(
              padding: const EdgeInsets.only(top: 8),
              child: Text(
                'InteractiveVariableIdentifierWidget',
                style: TextStyle(
                  color: Colors.grey[600],
                  fontSize: 12,
                  fontStyle: FontStyle.italic,
                ),
              ),
            ),
        ],
      ),
    );
  }
}

/// Model class for experiment scenarios
class ExperimentScenario {
  final String title;
  final String description;
  final List<String> variables;
  final String independentVariable;
  final String dependentVariable;
  final List<String> controlledVariables;
  final String explanation;
  
  ExperimentScenario({
    required this.title,
    required this.description,
    required this.variables,
    required this.independentVariable,
    required this.dependentVariable,
    required this.controlledVariables,
    required this.explanation,
  });
}
