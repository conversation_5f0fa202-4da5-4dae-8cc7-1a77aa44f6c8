import 'dart:convert';
import 'package:flutter/foundation.dart';
import 'package:flutter/services.dart';
import '../models/interactive_widget_model.dart';

class InteractiveWidgetService {
  static final InteractiveWidgetService _instance =
      InteractiveWidgetService._internal();

  factory InteractiveWidgetService() {
    return _instance;
  }

  InteractiveWidgetService._internal();

  List<InteractiveWidgetModel> _widgets = [];
  Map<String, List<InteractiveWidgetModel>> _categorizedWidgets = {};

  Future<void> initialize() async {
    try {
      final String jsonString = await rootBundle.loadString(
        'assets/data/interactive_widgets.json',
      );
      final List<dynamic> jsonList = json.decode(jsonString);

      // Process categorized widgets
      _categorizedWidgets = {};
      _widgets = [];

      for (final categoryData in jsonList) {
        final String category = categoryData['category'] as String;
        final List<dynamic> widgetsData =
            categoryData['widgets'] as List<dynamic>;

        final List<InteractiveWidgetModel> categoryWidgets = [];

        for (final widgetData in widgetsData) {
          try {
            // Make sure all required fields are present and of the correct type
            final id = widgetData['id'] as String;
            final name = widgetData['name'] as String;
            final type = widgetData['type'] as String;
            final description = widgetData['description'] as String;
            final data = widgetData['data'] as Map<String, dynamic>;
            final isImplemented = widgetData['isImplemented'] as bool;

            final widget = InteractiveWidgetModel(
              id: id,
              name: name,
              type: type,
              category: category, // Use the category from the parent object
              description: description,
              data: data,
              isImplemented: isImplemented,
            );

            categoryWidgets.add(widget);
          } catch (e) {
            debugPrint('Error parsing widget: $e');
            // Skip this widget and continue with the next one
          }
        }

        _categorizedWidgets[category] = categoryWidgets;
        _widgets.addAll(categoryWidgets);
      }

      debugPrint('Loaded ${_widgets.length} interactive widgets');
    } catch (e) {
      debugPrint('Error loading interactive widgets: $e');
      _widgets = _getSampleWidgets();
    }
  }

  List<InteractiveWidgetModel> getInteractiveWidgets() {
    return _widgets;
  }

  List<InteractiveWidgetModel> getImplementedWidgets() {
    return _widgets.where((widget) => widget.isImplemented).toList();
  }

  List<InteractiveWidgetModel> getWidgetsByCategory(String category) {
    return _categorizedWidgets[category] ?? [];
  }

  List<String> getCategories() {
    return _categorizedWidgets.keys.toList()..sort();
  }

  Map<String, List<InteractiveWidgetModel>> getCategorizedWidgets() {
    return _categorizedWidgets;
  }

  List<InteractiveWidgetModel> getWidgetsByType(String type) {
    return _widgets.where((widget) => widget.type == type).toList();
  }

  List<String> getWidgetTypes() {
    final Set<String> types = {};
    for (final widget in _widgets) {
      types.add(widget.type);
    }
    return types.toList()..sort();
  }

  InteractiveWidgetModel? getWidgetById(String id) {
    try {
      return _widgets.firstWhere((widget) => widget.id == id);
    } catch (e) {
      return null;
    }
  }

  List<InteractiveWidgetModel> _getSampleWidgets() {
    return [
      // GIF Player
      InteractiveWidgetModel(
        id: 'gif_player_1',
        name: 'Pattern Recognition Animation',
        type: 'GIF Player',
        category: 'Visual',
        description: 'A GIF showing pattern recognition in sequences.',
        data: {
          'gifUrl':
              'https://media.giphy.com/media/3o7TKSjRrfIPjeiVyM/giphy.gif',
          'caption': 'Recognizing and extending number patterns',
          'autoPlay': true,
          'loopCount': -1, // -1 means infinite loop
        },
        isImplemented: true,
      ),

      InteractiveWidgetModel(
        id: 'gif_player_2',
        name: 'Function Graphing Animation',
        type: 'GIF Player',
        category: 'Visual',
        description: 'A GIF showing how functions are graphed.',
        data: {
          'gifUrl':
              'https://media.giphy.com/media/3osxYc2axjCJNsCXyE/giphy.gif',
          'caption':
              'Visualizing how functions transform when parameters change',
          'autoPlay': true,
          'loopCount': 3,
        },
        isImplemented: true,
      ),

      InteractiveWidgetModel(
        id: 'gif_player_3',
        name: 'Sorting Algorithm Visualization',
        type: 'GIF Player',
        category: 'Visual',
        description: 'A GIF showing how sorting algorithms work.',
        data: {
          'gifUrl': 'https://media.giphy.com/media/l3vRmVv5P01I5NDAA/giphy.gif',
          'caption': 'Bubble sort algorithm in action',
          'autoPlay': true,
          'loopCount': 2,
        },
        isImplemented: true,
      ),

      // Multiple Choice
      InteractiveWidgetModel(
        id: 'multiple_choice_1',
        name: 'Math Puzzle Challenge',
        type: 'Multiple Choice',
        category: 'Quiz',
        description: 'A challenging math puzzle with multiple choice answers.',
        data: {
          'question': 'If the pattern is 2, 6, 12, 20, 30, what comes next?',
          'options': ['42', '40', '36', '32'],
          'correctAnswer': '42',
          'explanation':
              'The pattern follows the sequence of differences: +4, +6, +8, +10, +12. So 30 + 12 = 42.',
        },
        isImplemented: true,
      ),

      InteractiveWidgetModel(
        id: 'multiple_choice_2',
        name: 'Geometry Calculator Quiz',
        type: 'Multiple Choice',
        category: 'Quiz',
        description: 'A quiz testing knowledge of geometric formulas.',
        data: {
          'question': 'What is the area of a circle with radius 5 units?',
          'options': [
            '25π square units',
            '10π square units',
            '5π square units',
            '15π square units',
          ],
          'correctAnswer': '25π square units',
          'explanation':
              'The area of a circle is calculated using the formula A = πr². With r = 5, we get A = π × 5² = 25π square units.',
        },
        isImplemented: true,
      ),

      InteractiveWidgetModel(
        id: 'multiple_choice_3',
        name: 'Unit Conversion Challenge',
        type: 'Multiple Choice',
        category: 'Quiz',
        description: 'A quiz testing unit conversion skills.',
        data: {
          'question': 'Convert 3.5 kilometers to meters.',
          'options': [
            '35 meters',
            '350 meters',
            '3,500 meters',
            '35,000 meters',
          ],
          'correctAnswer': '3,500 meters',
          'explanation':
              '1 kilometer = 1,000 meters. Therefore, 3.5 kilometers = 3.5 × 1,000 = 3,500 meters.',
        },
        isImplemented: true,
      ),

      InteractiveWidgetModel(
        id: 'multiple_choice_4',
        name: 'Scientific Calculator Problem',
        type: 'Multiple Choice',
        category: 'Quiz',
        description:
            'A problem that would typically require a scientific calculator.',
        data: {
          'question': 'Calculate sin(30°) × cos(60°).',
          'options': ['0.25', '0.433', '0.5', '0.75'],
          'correctAnswer': '0.25',
          'explanation':
              'sin(30°) = 0.5 and cos(60°) = 0.5. Therefore, sin(30°) × cos(60°) = 0.5 × 0.5 = 0.25.',
        },
        isImplemented: true,
      ),

      // Interactive Diagram
      InteractiveWidgetModel(
        id: 'interactive_diagram_1',
        name: 'Math Whiteboard Demo',
        type: 'Interactive Diagram',
        category: 'Diagram',
        description: 'An interactive whiteboard for mathematical concepts.',
        data: {
          'imagePath': 'assets/images/placeholder.png',
          'interactivePoints': [
            {'x': 0.2, 'y': 0.3, 'label': 'Draw', 'type': 'toggle'},
            {'x': 0.5, 'y': 0.3, 'label': 'Erase', 'type': 'toggle'},
            {'x': 0.8, 'y': 0.3, 'label': 'Clear', 'type': 'toggle'},
          ],
        },
        isImplemented: true,
      ),

      InteractiveWidgetModel(
        id: 'interactive_diagram_2',
        name: 'Function Grapher Demo',
        type: 'Interactive Diagram',
        category: 'Diagram',
        description:
            'An interactive function grapher for visualizing mathematical functions.',
        data: {
          'imagePath': 'assets/images/placeholder.png',
          'interactivePoints': [
            {'x': 0.3, 'y': 0.2, 'label': 'Linear', 'type': 'toggle'},
            {'x': 0.5, 'y': 0.2, 'label': 'Quadratic', 'type': 'toggle'},
            {'x': 0.7, 'y': 0.2, 'label': 'Exponential', 'type': 'toggle'},
            {'x': 0.5, 'y': 0.7, 'label': 'Graph', 'type': 'indicator'},
          ],
        },
        isImplemented: true,
      ),

      InteractiveWidgetModel(
        id: 'interactive_diagram_3',
        name: 'Sorting Algorithm Visualizer',
        type: 'Interactive Diagram',
        category: 'Diagram',
        description: 'An interactive visualization of sorting algorithms.',
        data: {
          'imagePath': 'assets/images/placeholder.png',
          'interactivePoints': [
            {'x': 0.2, 'y': 0.2, 'label': 'Bubble Sort', 'type': 'toggle'},
            {'x': 0.4, 'y': 0.2, 'label': 'Quick Sort', 'type': 'toggle'},
            {'x': 0.6, 'y': 0.2, 'label': 'Merge Sort', 'type': 'toggle'},
            {'x': 0.8, 'y': 0.2, 'label': 'Start', 'type': 'toggle'},
            {'x': 0.5, 'y': 0.7, 'label': 'Visualization', 'type': 'indicator'},
          ],
        },
        isImplemented: true,
      ),

      // Mini-Game
      InteractiveWidgetModel(
        id: 'mini_game_1',
        name: 'Math Puzzle Game',
        type: 'Mini-Game',
        category: 'Game',
        description:
            'A simple math puzzle game where users solve equations against a timer.',
        data: {
          'difficulty': 'medium',
          'timeLimit': 60,
          'equations': [
            {'problem': '5 + 7', 'answer': '12'},
            {'problem': '8 * 9', 'answer': '72'},
            {'problem': '15 - 6', 'answer': '9'},
          ],
        },
        isImplemented: false,
      ),

      InteractiveWidgetModel(
        id: 'mini_game_2',
        name: 'Pattern Recognition Game',
        type: 'Mini-Game',
        category: 'Game',
        description:
            'A game where users identify patterns in sequences of numbers or shapes.',
        data: {
          'difficulty': 'easy',
          'timeLimit': 90,
          'sequences': [
            {
              'sequence': [2, 4, 6, 8],
              'next': '10',
              'pattern': 'Add 2',
            },
            {
              'sequence': [1, 3, 9, 27],
              'next': '81',
              'pattern': 'Multiply by 3',
            },
            {
              'sequence': [1, 4, 9, 16],
              'next': '25',
              'pattern': 'Square numbers',
            },
          ],
        },
        isImplemented: false,
      ),

      // Interactive Converter
      InteractiveWidgetModel(
        id: 'interactive_converter_1',
        name: 'Unit Converter',
        type: 'Interactive Converter',
        category: 'Tool',
        description:
            'A tool for converting between different units of measurement.',
        data: {
          'categories': ['Length', 'Weight', 'Temperature'],
          'defaultCategory': 'Length',
          'units': {
            'Length': ['Meters', 'Feet', 'Inches', 'Kilometers', 'Miles'],
            'Weight': ['Kilograms', 'Pounds', 'Ounces', 'Grams'],
            'Temperature': ['Celsius', 'Fahrenheit', 'Kelvin'],
          },
        },
        isImplemented: false,
      ),

      // Interactive Tool
      InteractiveWidgetModel(
        id: 'interactive_tool_1',
        name: 'Geometry Calculator',
        type: 'Interactive Tool',
        category: 'Tool',
        description:
            'A tool for calculating areas, perimeters, and volumes of geometric shapes.',
        data: {
          'shapes': [
            'Circle',
            'Square',
            'Rectangle',
            'Triangle',
            'Sphere',
            'Cube',
          ],
          'defaultShape': 'Circle',
          'calculations': {
            'Circle': ['Area', 'Circumference'],
            'Square': ['Area', 'Perimeter'],
            'Rectangle': ['Area', 'Perimeter'],
            'Triangle': ['Area', 'Perimeter'],
            'Sphere': ['Surface Area', 'Volume'],
            'Cube': ['Surface Area', 'Volume'],
          },
        },
        isImplemented: false,
      ),

      // Interactive Calculator
      InteractiveWidgetModel(
        id: 'interactive_calculator_1',
        name: 'Scientific Calculator',
        type: 'Interactive Calculator',
        category: 'Tool',
        description:
            'A scientific calculator with advanced mathematical functions.',
        data: {
          'functions': ['Basic', 'Trigonometry', 'Logarithms', 'Statistics'],
          'defaultFunction': 'Basic',
          'history': true,
          'memoryStorage': true,
        },
        isImplemented: false,
      ),

      // Interactive Simulation
      InteractiveWidgetModel(
        id: 'interactive_simulation_1',
        name: 'Pendulum Simulation',
        type: 'Interactive Simulation',
        category: 'Simulation',
        description:
            'An interactive simulation of a pendulum where users can adjust parameters and see the effects.',
        data: {
          'parameters': [
            {
              'name': 'Length',
              'min': 0.1,
              'max': 2.0,
              'default': 1.0,
              'unit': 'm',
            },
            {
              'name': 'Gravity',
              'min': 1.0,
              'max': 20.0,
              'default': 9.8,
              'unit': 'm/s²',
            },
            {
              'name': 'Initial Angle',
              'min': 0,
              'max': 90,
              'default': 45,
              'unit': '°',
            },
          ],
        },
        isImplemented: false,
      ),

      // Interactive Grapher
      InteractiveWidgetModel(
        id: 'interactive_grapher_1',
        name: 'Function Grapher',
        type: 'Interactive Grapher',
        category: 'Tool',
        description:
            'A tool for graphing mathematical functions and exploring their properties.',
        data: {
          'functions': ['y = x^2', 'y = sin(x)', 'y = log(x)'],
          'xRange': [-10, 10],
          'yRange': [-10, 10],
          'grid': true,
          'axes': true,
        },
        isImplemented: false,
      ),

      // Interactive Visualizer
      InteractiveWidgetModel(
        id: 'interactive_visualizer_1',
        name: 'Sorting Algorithm Visualizer',
        type: 'Interactive Visualizer',
        category: 'Computer Science',
        description: 'A visualization tool for different sorting algorithms.',
        data: {
          'algorithms': [
            'Bubble Sort',
            'Quick Sort',
            'Merge Sort',
            'Insertion Sort',
          ],
          'defaultAlgorithm': 'Bubble Sort',
          'arraySize': 50,
          'speed': 'medium',
        },
        isImplemented: false,
      ),

      // Interactive Probability Distribution Visualizer
      InteractiveWidgetModel(
        id: 'interactive_probability_distribution_visualizer_1',
        name: 'Probability Distribution Visualizer',
        type: 'interactive_probability_distribution_visualizer',
        category: 'maths',
        description: 'An interactive tool for exploring probability distributions and calculating probabilities.',
        data: {
          'title': 'Probability Distribution Visualizer',
          'description': 'Explore probability distributions and calculate probabilities for different ranges.',
          'primary_color': '#2196F3',
          'secondary_color': '#FF9800',
          'accent_color': '#4CAF50',
          'text_color': '#212121',
          'background_color': '#FFFFFF',
          'showNameTag': true,
        },
        isImplemented: true,
      ),

      // Interactive Whiteboard
      InteractiveWidgetModel(
        id: 'interactive_whiteboard_1',
        name: 'Math Whiteboard',
        type: 'Interactive Whiteboard',
        category: 'Tool',
        description:
            'A digital whiteboard for solving mathematical problems with drawing and text tools.',
        data: {
          'tools': ['Pen', 'Eraser', 'Text', 'Shapes', 'Grid'],
          'defaultTool': 'Pen',
          'colors': ['Black', 'Blue', 'Red', 'Green'],
          'defaultColor': 'Black',
          'gridOptions': ['None', '1cm', '0.5cm'],
          'defaultGrid': 'None',
        },
        isImplemented: true,
      ),

      // Interactive Pythagorean Theorem Visualizer
      InteractiveWidgetModel(
        id: 'interactive_pythagorean_theorem_viz_1',
        name: 'Pythagorean Theorem Visualizer',
        type: 'interactive_pythagorean_theorem_viz',
        category: 'maths',
        description: 'An interactive visualization of the Pythagorean theorem (a² + b² = c²).',
        data: {
          'title': 'Pythagorean Theorem Visualizer',
          'description': 'Drag the vertices to see how a² + b² = c² holds for any right-angled triangle.',
          'triangleColor': '#BBDEFB',
          'squareAColor': '#FFCDD2',
          'squareBColor': '#C8E6C9',
          'squareCColor': '#E1BEE7',
          'textColor': '#212121',
          'showSquares': true,
          'showAreas': true,
          'showFormula': true,
          'animateSquares': true,
          'showNameTag': true,
        },
        isImplemented: true,
      ),

      // Interactive Circle Properties Visualizer
      InteractiveWidgetModel(
        id: 'interactive_circle_properties_viz_1',
        name: 'Circle Properties Visualizer',
        type: 'interactive_circle_properties_viz',
        category: 'maths',
        description: 'An interactive visualization of circle properties (radius, diameter, circumference).',
        data: {
          'title': 'Circle Properties Visualizer',
          'description': 'Adjust the radius to see how it affects the diameter and circumference.',
          'initialRadius': 50.0,
          'minRadius': 20.0,
          'maxRadius': 120.0,
          'circleColor': '#BBDEFB',
          'radiusColor': '#F44336',
          'diameterColor': '#4CAF50',
          'circumferenceColor': '#9C27B0',
          'textColor': '#212121',
          'showRadius': true,
          'showDiameter': true,
          'showCircumference': true,
          'showLabels': true,
          'showValues': true,
          'showPi': true,
          'showNameTag': true,
        },
        isImplemented: true,
      ),

      // Interactive Transformation Identification Game
      InteractiveWidgetModel(
        id: 'interactive_transformation_identification_game_1',
        name: 'Transformation Identification Game',
        type: 'interactive_transformation_identification_game',
        category: 'maths',
        description: 'A game to identify geometric transformations (translation, rotation, reflection, dilation).',
        data: {
          'title': 'Transformation Identification Game',
          'description': 'Identify the transformation that maps Figure A to Figure B.',
          'figure_a_src': 'assets/images/geo_puzzles/figure_A1.svg',
          'figure_b_src': 'assets/images/geo_puzzles/figure_B1_rotated.svg',
          'correct_transformation': 'rotation',
          'transformation_options': ['translation', 'rotation', 'reflection', 'dilation'],
          'primaryColor': '#2196F3',
          'correctColor': '#4CAF50',
          'incorrectColor': '#F44336',
          'textColor': '#212121',
          'correctFeedback': 'Correct! The transformation is a rotation.',
          'incorrectFeedback': 'Not quite. Look at how the orientation changes.',
          'showNameTag': true,
        },
        isImplemented: true,
      ),

      // Rotation Interactive Game
      InteractiveWidgetModel(
        id: 'rotation_interactive_game_1',
        name: 'Rotation Interactive Game',
        type: 'rotation_interactive_game',
        category: 'maths',
        description: 'An interactive game to explore rotations of shapes around a point.',
        data: {
          'title': 'Rotation Interactive Game',
          'description': 'Rotate the shape around the center point by the specified angle.',
          'shape': 'arrow',
          'center_of_rotation': 'origin',
          'angle_degrees': 90,
          'direction': 'clockwise',
          'shapeColor': '#2196F3',
          'rotatedShapeColor': '#2196F380',
          'centerColor': '#F44336',
          'textColor': '#212121',
          'feedback_correct': 'Nice rotation! You rotated the shape by 90° clockwise.',
          'showNameTag': true,
        },
        isImplemented: true,
      ),

      // Interactive Function Machine
      InteractiveWidgetModel(
        id: 'interactive_function_machine_1',
        name: 'Function Machine',
        type: 'interactive_function_machine',
        category: 'maths',
        description: 'An interactive function machine that takes inputs and produces outputs based on a rule.',
        data: {
          'title': 'Function Machine',
          'description': 'Enter an input value and see what comes out based on the function rule.',
          'function_rule_display': '2x + 3',
          'function_logic': 'output = 2 * input + 3',
          'primary_color': '#2196F3',
          'secondary_color': '#FF9800',
          'accent_color': '#4CAF50',
          'background_color': '#FFFFFF',
          'text_color': '#212121',
          'showNameTag': true,
        },
        isImplemented: true,
      ),

      // Interactive Function Identifier
      InteractiveWidgetModel(
        id: 'interactive_function_identifier_1',
        name: 'Function Identifier',
        type: 'interactive_function_identifier',
        category: 'maths',
        description: 'An interactive widget that helps users identify whether a relation is a function using the vertical line test.',
        data: {
          'title': 'Function Identifier',
          'description': 'Determine whether each relation is a function by applying the vertical line test.',
          'primary_color': '#2196F3',
          'secondary_color': '#FF9800',
          'accent_color': '#4CAF50',
          'background_color': '#FFFFFF',
          'text_color': '#212121',
          'showNameTag': true,
        },
        isImplemented: true,
      ),

      // Interactive Domain and Range Explorer
      InteractiveWidgetModel(
        id: 'interactive_domain_range_explorer_1',
        name: 'Domain and Range Explorer',
        type: 'interactive_domain_range_explorer',
        category: 'maths',
        description: 'An interactive widget that helps users understand and visualize the domain and range of functions.',
        data: {
          'title': 'Domain and Range Explorer',
          'description': 'Explore the domain and range of various functions and understand their restrictions.',
          'primary_color': '#2196F3',
          'secondary_color': '#FF9800',
          'accent_color': '#4CAF50',
          'background_color': '#FFFFFF',
          'text_color': '#212121',
          'domain_color': '#9C27B0',
          'range_color': '#009688',
          'showNameTag': true,
        },
        isImplemented: true,
      ),

      // Interactive Function Notation Practice
      InteractiveWidgetModel(
        id: 'interactive_function_notation_practice_1',
        name: 'Function Notation Practice',
        type: 'interactive_function_notation_practice',
        category: 'maths',
        description: 'An interactive widget that helps users practice function notation by evaluating functions at specific values.',
        data: {
          'title': 'Function Notation Practice',
          'description': 'Practice evaluating functions at specific input values and understand function notation.',
          'primary_color': '#2196F3',
          'secondary_color': '#FF9800',
          'accent_color': '#4CAF50',
          'background_color': '#FFFFFF',
          'text_color': '#212121',
          'showNameTag': true,
        },
        isImplemented: true,
      ),

      // Interactive Function Fundamentals Test
      InteractiveWidgetModel(
        id: 'interactive_function_fundamentals_test_1',
        name: 'Function Fundamentals Test',
        type: 'interactive_function_fundamentals_test',
        category: 'maths',
        description: 'A comprehensive assessment widget for testing understanding of function fundamentals.',
        data: {
          'title': 'Function Fundamentals Test',
          'description': 'Test your understanding of function concepts including identification, notation, domain, and range.',
          'primary_color': '#2196F3',
          'secondary_color': '#FF9800',
          'accent_color': '#4CAF50',
          'background_color': '#FFFFFF',
          'text_color': '#212121',
          'showNameTag': true,
        },
        isImplemented: true,
      ),

      // Interactive Linear Function Explorer
      InteractiveWidgetModel(
        id: 'interactive_linear_function_explorer_1',
        name: 'Linear Function Explorer',
        type: 'interactive_linear_function_explorer',
        category: 'maths',
        description: 'An interactive widget that helps users explore linear functions by visualizing how changes to parameters affect the graph.',
        data: {
          'title': 'Linear Function Explorer',
          'description': 'Explore linear functions by adjusting the slope and y-intercept to see how they affect the graph.',
          'primary_color': '#2196F3',
          'secondary_color': '#FF9800',
          'accent_color': '#4CAF50',
          'background_color': '#FFFFFF',
          'text_color': '#212121',
          'showNameTag': true,
        },
        isImplemented: true,
      ),

      // Interactive Quadratic Function Explorer
      InteractiveWidgetModel(
        id: 'interactive_quadratic_function_explorer_1',
        name: 'Quadratic Function Explorer',
        type: 'interactive_quadratic_function_explorer',
        category: 'maths',
        description: 'An interactive widget that helps users explore quadratic functions by visualizing how changes to parameters affect the graph.',
        data: {
          'title': 'Quadratic Function Explorer',
          'description': 'Explore quadratic functions by adjusting the coefficients a, b, and c to see how they affect the graph.',
          'primary_color': '#2196F3',
          'secondary_color': '#FF9800',
          'accent_color': '#4CAF50',
          'background_color': '#FFFFFF',
          'text_color': '#212121',
          'showNameTag': true,
        },
        isImplemented: true,
      ),

      // Interactive Exponential Function Explorer
      InteractiveWidgetModel(
        id: 'interactive_exponential_function_explorer_1',
        name: 'Exponential Function Explorer',
        type: 'interactive_exponential_function_explorer',
        category: 'maths',
        description: 'An interactive widget that helps users explore exponential functions by visualizing how changes to parameters affect the graph.',
        data: {
          'title': 'Exponential Function Explorer',
          'description': 'Explore exponential functions by adjusting the parameters a, b, and c to see how they affect the graph.',
          'primary_color': '#2196F3',
          'secondary_color': '#FF9800',
          'accent_color': '#4CAF50',
          'background_color': '#FFFFFF',
          'text_color': '#212121',
          'showNameTag': true,
        },
        isImplemented: true,
      ),

      // Interactive Probability Calculator
      InteractiveWidgetModel(
        id: 'interactive_probability_calculator_1',
        name: 'Probability Calculator',
        type: 'interactive_probability_calculator',
        category: 'maths',
        description: 'An interactive widget that helps users calculate and understand various probability concepts.',
        data: {
          'title': 'Probability Calculator',
          'description': 'Calculate probabilities for different types of events and explore probability concepts.',
          'primary_color': '#2196F3',
          'secondary_color': '#FF9800',
          'accent_color': '#4CAF50',
          'background_color': '#FFFFFF',
          'text_color': '#212121',
          'showNameTag': true,
        },
        isImplemented: true,
      ),

      // Interactive Function Representation Tool
      InteractiveWidgetModel(
        id: 'interactive_function_representation_tool_1',
        name: 'Function Representation Tool',
        type: 'interactive_function_representation_tool',
        category: 'maths',
        description: 'An interactive tool for exploring different representations of functions (equations, tables, graphs, and verbal descriptions).',
        data: {
          'title': 'Function Representation Tool',
          'description': 'Explore different ways to represent the same function.',
          'primary_color': '#2196F3',
          'secondary_color': '#FF9800',
          'accent_color': '#4CAF50',
          'background_color': '#FFFFFF',
          'text_color': '#212121',
          'predefined_functions': [
            {
              'name': 'Linear Function',
              'equation': 'f(x) = 2x + 3',
              'description': 'A linear function with slope 2 and y-intercept 3. This type of function creates a straight line when graphed.'
            },
            {
              'name': 'Quadratic Function',
              'equation': 'f(x) = x² - 2x + 1',
              'description': 'A quadratic function that forms a parabola when graphed. This particular function has a minimum value at x = 1.'
            },
            {
              'name': 'Absolute Value Function',
              'equation': 'f(x) = |x|',
              'description': 'The absolute value function returns the distance of x from zero, regardless of whether x is positive or negative.'
            }
          ],
          'showNameTag': true,
        },
        isImplemented: true,
      ),

      // Interactive Arithmetic Sequence Explorer
      InteractiveWidgetModel(
        id: 'interactive_arithmetic_sequence_explorer_1',
        name: 'Arithmetic Sequence Explorer',
        type: 'interactive_arithmetic_sequence_explorer',
        category: 'maths',
        description: 'An interactive tool to explore arithmetic sequences and their properties.',
        data: {
          'title': 'Arithmetic Sequence Explorer',
          'description': 'Adjust the first term and common difference to see how the sequence changes.',
          'first_term': 1.0,
          'common_difference': 2.0,
          'number_of_terms': 10,
          'primary_color': '#2196F3',
          'secondary_color': '#FF9800',
          'tertiary_color': '#4CAF50',
          'background_color': '#FFFFFF',
          'text_color': '#212121',
          'show_formula': true,
          'show_differences': true,
          'show_graph': true,
          'show_table': true,
          'min_first_term': -10.0,
          'max_first_term': 10.0,
          'min_common_difference': -10.0,
          'max_common_difference': 10.0,
          'showNameTag': true,
        },
        isImplemented: true,
      ),

      // Interactive Geometric Sequence Explorer
      InteractiveWidgetModel(
        id: 'interactive_geometric_sequence_explorer_1',
        name: 'Geometric Sequence Explorer',
        type: 'interactive_geometric_sequence_explorer',
        category: 'maths',
        description: 'An interactive tool to explore geometric sequences and their properties.',
        data: {
          'title': 'Geometric Sequence Explorer',
          'description': 'Adjust the first term and common ratio to see how the sequence changes.',
          'first_term': 1.0,
          'common_ratio': 2.0,
          'number_of_terms': 10,
          'primary_color': '#2196F3',
          'secondary_color': '#FF9800',
          'tertiary_color': '#4CAF50',
          'background_color': '#FFFFFF',
          'text_color': '#212121',
          'show_formula': true,
          'show_ratios': true,
          'show_graph': true,
          'show_table': true,
          'use_log_scale': true,
          'min_first_term': 0.1,
          'max_first_term': 10.0,
          'min_common_ratio': 0.1,
          'max_common_ratio': 5.0,
          'showNameTag': true,
        },
        isImplemented: true,
      ),

      // Interactive Argument Analyzer
      InteractiveWidgetModel(
        id: 'interactive_argument_analyzer_1',
        name: 'Argument Analyzer',
        type: 'interactive_argument_analyzer',
        category: 'reasoning',
        description: 'Analyze and evaluate different types of arguments.',
        data: {
          'title': 'Argument Analyzer',
          'description': 'Analyze arguments to identify their type, strength, and potential fallacies.',
          'primaryColor': '#673AB7',
          'secondaryColor': '#2196F3',
          'accentColor': '#FF9800',
          'arguments': [
            {
              'id': 'argument_1',
              'text': 'All humans need oxygen to survive. Socrates is human. Therefore, Socrates needs oxygen to survive.',
              'premises': ['All humans need oxygen to survive', 'Socrates is human'],
              'conclusion': 'Socrates needs oxygen to survive',
              'argumentType': 'deductive',
              'argumentStrength': 'strong',
              'fallacies': [],
              'explanation': 'This is a valid deductive argument in the form of a syllogism. The premises logically lead to the conclusion with certainty. Since the premises are true, and the logical form is valid, this is a sound argument.'
            },
            {
              'id': 'argument_2',
              'text': 'Every time I have worn my lucky socks, my team has won. I am wearing my lucky socks today. Therefore, my team will win today.',
              'premises': ['Every time I have worn my lucky socks, my team has won', 'I am wearing my lucky socks today'],
              'conclusion': 'My team will win today',
              'argumentType': 'inductive',
              'argumentStrength': 'weak',
              'fallacies': ['false_dichotomy'],
              'explanation': 'This is an inductive argument that commits the correlation-causation fallacy. Just because two events occurred together in the past does not mean one caused the other. There is no causal relationship between wearing socks and a team winning.'
            },
            {
              'id': 'argument_3',
              'text': 'Dr. Smith, who has a PhD in climate science, says that climate change is real. Therefore, climate change is real.',
              'premises': ['Dr. Smith, who has a PhD in climate science, says that climate change is real'],
              'conclusion': 'Climate change is real',
              'argumentType': 'inductive',
              'argumentStrength': 'moderate',
              'fallacies': ['appeal_to_authority'],
              'explanation': 'This is an inductive argument that relies on expert testimony. While appealing to relevant expertise can strengthen an argument, relying solely on one authority without additional evidence is an appeal to authority fallacy. The argument would be stronger with multiple experts and supporting evidence.'
            },
            {
              'id': 'argument_4',
              'text': 'The best explanation for the patient symptoms of fever, cough, and fatigue is that they have the flu. Therefore, the patient likely has the flu.',
              'premises': ['The patient has symptoms of fever, cough, and fatigue', 'The best explanation for these symptoms is the flu'],
              'conclusion': 'The patient likely has the flu',
              'argumentType': 'abductive',
              'argumentStrength': 'moderate',
              'fallacies': [],
              'explanation': 'This is an abductive argument that reasons to the best explanation. It does not guarantee the conclusion but provides a reasonable inference based on available evidence. The strength is moderate because while flu might explain the symptoms, other conditions could also cause them.'
            }
          ]
        },
        isImplemented: true,
      ),

      // Interactive Algorithm Flowchart Builder
      InteractiveWidgetModel(
        id: 'interactive_algorithm_flowchart_builder_1',
        name: 'Algorithm Flowchart Builder',
        type: 'interactive_algorithm_flowchart_builder',
        category: 'computer_science',
        description: 'Build and visualize algorithm flowcharts to understand computational thinking.',
        data: {
          'title': 'Algorithm Flowchart Builder',
          'description': 'Create a flowchart to visualize an algorithm step by step.',
          'primaryColor': '#2196F3',
          'secondaryColor': '#4CAF50',
          'accentColor': '#FF9800',
          'challengeTitle': 'Build a Sorting Algorithm',
          'challengeDescription': 'Create a flowchart for a simple sorting algorithm that arranges numbers in ascending order.',
          'availableNodes': [
            {
              'id': 'start_node',
              'type': 'start',
              'text': 'Start',
              'x': 50,
              'y': 50
            },
            {
              'id': 'input_node',
              'type': 'input',
              'text': 'Input Array',
              'x': 50,
              'y': 120
            },
            {
              'id': 'process_node_1',
              'type': 'process',
              'text': 'Initialize i = 0',
              'x': 50,
              'y': 190
            },
            {
              'id': 'decision_node_1',
              'type': 'decision',
              'text': 'i < length - 1?',
              'x': 50,
              'y': 260
            },
            {
              'id': 'process_node_2',
              'type': 'process',
              'text': 'Initialize j = 0',
              'x': 50,
              'y': 330
            },
            {
              'id': 'decision_node_2',
              'type': 'decision',
              'text': 'j < length - i - 1?',
              'x': 50,
              'y': 400
            },
            {
              'id': 'decision_node_3',
              'type': 'decision',
              'text': 'array[j] > array[j+1]?',
              'x': 50,
              'y': 470
            },
            {
              'id': 'process_node_3',
              'type': 'process',
              'text': 'Swap array[j] and array[j+1]',
              'x': 50,
              'y': 540
            },
            {
              'id': 'process_node_4',
              'type': 'process',
              'text': 'j = j + 1',
              'x': 50,
              'y': 610
            },
            {
              'id': 'process_node_5',
              'type': 'process',
              'text': 'i = i + 1',
              'x': 50,
              'y': 680
            },
            {
              'id': 'output_node',
              'type': 'output',
              'text': 'Output Sorted Array',
              'x': 50,
              'y': 750
            },
            {
              'id': 'end_node',
              'type': 'end',
              'text': 'End',
              'x': 50,
              'y': 820
            }
          ],
          'solutionNodes': [
            {
              'id': 'start_node_sol',
              'type': 'start',
              'text': 'Start',
              'x': 300,
              'y': 50
            },
            {
              'id': 'input_node_sol',
              'type': 'input',
              'text': 'Input Array',
              'x': 300,
              'y': 120
            },
            {
              'id': 'process_node_1_sol',
              'type': 'process',
              'text': 'Initialize i = 0',
              'x': 300,
              'y': 190
            },
            {
              'id': 'decision_node_1_sol',
              'type': 'decision',
              'text': 'i < length - 1?',
              'x': 300,
              'y': 260
            },
            {
              'id': 'process_node_2_sol',
              'type': 'process',
              'text': 'Initialize j = 0',
              'x': 300,
              'y': 330
            },
            {
              'id': 'decision_node_2_sol',
              'type': 'decision',
              'text': 'j < length - i - 1?',
              'x': 300,
              'y': 400
            },
            {
              'id': 'decision_node_3_sol',
              'type': 'decision',
              'text': 'array[j] > array[j+1]?',
              'x': 300,
              'y': 470
            },
            {
              'id': 'process_node_3_sol',
              'type': 'process',
              'text': 'Swap array[j] and array[j+1]',
              'x': 300,
              'y': 540
            },
            {
              'id': 'process_node_4_sol',
              'type': 'process',
              'text': 'j = j + 1',
              'x': 300,
              'y': 610
            },
            {
              'id': 'process_node_5_sol',
              'type': 'process',
              'text': 'i = i + 1',
              'x': 300,
              'y': 680
            },
            {
              'id': 'output_node_sol',
              'type': 'output',
              'text': 'Output Sorted Array',
              'x': 300,
              'y': 750
            },
            {
              'id': 'end_node_sol',
              'type': 'end',
              'text': 'End',
              'x': 300,
              'y': 820
            }
          ],
          'solutionConnections': [
            {
              'fromNodeId': 'start_node_sol',
              'toNodeId': 'input_node_sol',
              'label': ''
            },
            {
              'fromNodeId': 'input_node_sol',
              'toNodeId': 'process_node_1_sol',
              'label': ''
            },
            {
              'fromNodeId': 'process_node_1_sol',
              'toNodeId': 'decision_node_1_sol',
              'label': ''
            },
            {
              'fromNodeId': 'decision_node_1_sol',
              'toNodeId': 'process_node_2_sol',
              'label': 'Yes'
            },
            {
              'fromNodeId': 'decision_node_1_sol',
              'toNodeId': 'output_node_sol',
              'label': 'No'
            },
            {
              'fromNodeId': 'process_node_2_sol',
              'toNodeId': 'decision_node_2_sol',
              'label': ''
            },
            {
              'fromNodeId': 'decision_node_2_sol',
              'toNodeId': 'decision_node_3_sol',
              'label': 'Yes'
            },
            {
              'fromNodeId': 'decision_node_2_sol',
              'toNodeId': 'process_node_5_sol',
              'label': 'No'
            },
            {
              'fromNodeId': 'decision_node_3_sol',
              'toNodeId': 'process_node_3_sol',
              'label': 'Yes'
            },
            {
              'fromNodeId': 'decision_node_3_sol',
              'toNodeId': 'process_node_4_sol',
              'label': 'No'
            },
            {
              'fromNodeId': 'process_node_3_sol',
              'toNodeId': 'process_node_4_sol',
              'label': ''
            },
            {
              'fromNodeId': 'process_node_4_sol',
              'toNodeId': 'decision_node_2_sol',
              'label': ''
            },
            {
              'fromNodeId': 'process_node_5_sol',
              'toNodeId': 'decision_node_1_sol',
              'label': ''
            },
            {
              'fromNodeId': 'output_node_sol',
              'toNodeId': 'end_node_sol',
              'label': ''
            }
          ]
        },
        isImplemented: true,
      ),

      // Interactive Ratio Visualizer
      InteractiveWidgetModel(
        id: 'interactive_ratio_visualizer_1',
        name: 'Ratio Visualizer',
        type: 'interactive_ratio_visualizer',
        category: 'maths',
        description: 'An interactive tool to visualize ratios and proportions.',
        data: {
          'title': 'Ratio Visualizer',
          'description': 'Adjust the numerator and denominator to see different ratio visualizations.',
          'numerator': 3,
          'denominator': 4,
          'visualization_type': 'Blocks',
          'primary_color': '#2196F3',
          'secondary_color': '#BDBDBD',
          'background_color': '#FFFFFF',
          'text_color': '#212121',
          'show_decimal': true,
          'show_percentage': true,
          'show_fraction': true,
          'showNameTag': true,
        },
        isImplemented: true,
      ),

      // Interactive Model Builder
      InteractiveWidgetModel(
        id: 'interactive_model_builder_1',
        name: 'Scientific Model Builder',
        type: 'interactive_model_builder',
        category: 'Science',
        description: 'An interactive tool for building and testing scientific models.',
        data: {
          'title': 'Scientific Model Builder',
          'description': 'Build models to explain scientific phenomena by selecting the appropriate components.',
          'primaryColor': '#2196F3',
          'secondaryColor': '#FF9800',
          'accentColor': '#4CAF50',
          'backgroundColor': '#FFFFFF',
          'textColor': '#212121',
          'showNameTag': true,
          'scenarios': [
            {
              'title': 'Climate Change Model',
              'description': 'Build a model to explain global climate change.',
              'domain': 'Earth Science',
              'phenomenon': 'Global average temperatures have been rising over the past century, with accelerated warming in recent decades. This has been accompanied by melting ice caps, rising sea levels, and increased frequency of extreme weather events.',
              'correctComponents': [
                'greenhouse_gases',
                'solar_radiation',
                'heat_trapping',
                'human_activity'
              ],
              'explanation': 'A comprehensive climate change model includes greenhouse gases (like CO2 and methane) that trap heat from solar radiation in the atmosphere. Human activities, particularly burning fossil fuels and deforestation, have increased the concentration of these gases, enhancing the natural greenhouse effect and leading to global warming.',
              'feedbackCorrect': 'Excellent! Your climate change model correctly incorporates the key components: greenhouse gases that trap solar radiation, amplified by human activities. This model explains the observed warming trend and its consequences.',
              'feedbackIncorrect': 'Your climate model is missing some key components. A comprehensive climate change model needs to account for greenhouse gases, solar radiation, heat trapping mechanisms, and human activities that enhance these effects.'
            },
            {
              'title': 'Cell Division Model',
              'description': 'Build a model to explain how cells divide and reproduce.',
              'domain': 'Biology',
              'phenomenon': 'Organisms grow and repair themselves through cell division. This process must ensure that genetic information is accurately copied and distributed to daughter cells.',
              'correctComponents': [
                'dna_replication',
                'chromosome_separation',
                'cell_membrane_division',
                'cell_cycle_regulation'
              ],
              'explanation': 'Cell division involves several coordinated processes: DNA replication ensures genetic material is duplicated, chromosome separation (mitosis) distributes this material equally, and cell membrane division (cytokinesis) creates two separate cells. The cell cycle is regulated by checkpoints that ensure each step occurs correctly and in the proper sequence.',
              'feedbackCorrect': 'Excellent! Your cell division model correctly includes DNA replication, chromosome separation, cell membrane division, and cell cycle regulation. This model explains how cells reproduce while maintaining genetic integrity.',
              'feedbackIncorrect': 'Your cell division model is missing some key components. A complete model needs to account for DNA replication, chromosome separation, cell membrane division, and cell cycle regulation.'
            }
          ],
          'components': [
            {
              'id': 'greenhouse_gases',
              'name': 'Greenhouse Gases',
              'description': 'Gases like CO2 and methane that trap heat in the atmosphere',
              'category': 'Earth Science',
              'icon': 'cloud'
            },
            {
              'id': 'solar_radiation',
              'name': 'Solar Radiation',
              'description': 'Energy from the sun that warms Earth\'s surface',
              'category': 'Earth Science',
              'icon': 'wb_sunny'
            },
            {
              'id': 'heat_trapping',
              'name': 'Heat Trapping',
              'description': 'Process where atmospheric gases prevent heat from escaping into space',
              'category': 'Earth Science',
              'icon': 'whatshot'
            },
            {
              'id': 'human_activity',
              'name': 'Human Activity',
              'description': 'Actions like burning fossil fuels that release greenhouse gases',
              'category': 'Earth Science',
              'icon': 'factory'
            },
            {
              'id': 'natural_cycles',
              'name': 'Natural Cycles',
              'description': 'Regular climate variations like Milankovitch cycles',
              'category': 'Earth Science',
              'icon': 'autorenew'
            },
            {
              'id': 'dna_replication',
              'name': 'DNA Replication',
              'description': 'Process of copying genetic material before cell division',
              'category': 'Biology',
              'icon': 'content_copy'
            },
            {
              'id': 'chromosome_separation',
              'name': 'Chromosome Separation',
              'description': 'Distribution of chromosomes to daughter cells during mitosis',
              'category': 'Biology',
              'icon': 'call_split'
            },
            {
              'id': 'cell_membrane_division',
              'name': 'Cell Membrane Division',
              'description': 'Process of dividing the cell membrane to form two cells',
              'category': 'Biology',
              'icon': 'crop'
            },
            {
              'id': 'cell_cycle_regulation',
              'name': 'Cell Cycle Regulation',
              'description': 'Checkpoints that control the progression of cell division',
              'category': 'Biology',
              'icon': 'timer'
            },
            {
              'id': 'protein_synthesis',
              'name': 'Protein Synthesis',
              'description': 'Production of proteins based on genetic instructions',
              'category': 'Biology',
              'icon': 'build'
            }
          ]
        },
        isImplemented: true,
      ),

      // Interactive Theory Evaluation Tool
      InteractiveWidgetModel(
        id: 'interactive_theory_evaluation_tool_1',
        name: 'Scientific Theory Evaluation Tool',
        type: 'interactive_theory_evaluation_tool',
        category: 'Science',
        description: 'An interactive tool for evaluating scientific theories based on various criteria.',
        data: {
          'title': 'Scientific Theory Evaluation Tool',
          'description': 'Evaluate scientific theories based on key criteria to determine their strength and validity.',
          'primaryColor': '#3F51B5',
          'secondaryColor': '#FF5722',
          'accentColor': '#009688',
          'backgroundColor': '#FFFFFF',
          'textColor': '#212121',
          'showNameTag': true,
          'theories': [
            {
              'id': 'evolution',
              'name': 'Theory of Evolution by Natural Selection',
              'description': 'The theory that all species of organisms arise and develop through the natural selection of small, inherited variations that increase the individual\'s ability to compete, survive, and reproduce.',
              'expertEvaluation': 'The theory of evolution by natural selection is considered one of the most robust scientific theories, supported by evidence from multiple fields including paleontology, comparative anatomy, genetics, and molecular biology. It has strong explanatory and predictive power, has been extensively tested, and has withstood numerous challenges.',
              'highScoreFeedback': 'Your evaluation aligns with the scientific consensus. The theory of evolution is indeed well-supported by extensive evidence across multiple scientific disciplines, has strong explanatory and predictive power, and has been refined over time to address new findings.',
              'mediumScoreFeedback': 'Your evaluation shows some understanding of the theory\'s strengths, but you may be underestimating its empirical support or explanatory power. The theory of evolution is considered one of the most robust scientific theories, supported by multiple lines of evidence.',
              'lowScoreFeedback': 'Your evaluation suggests some misconceptions about the theory of evolution. In the scientific community, it is considered one of the most well-established theories, with overwhelming empirical support from multiple disciplines and strong explanatory and predictive power.'
            },
            {
              'id': 'plate_tectonics',
              'name': 'Theory of Plate Tectonics',
              'description': 'The theory that Earth\'s outer shell is divided into several plates that glide over the mantle, explaining continental drift, earthquakes, volcanic activity, and mountain formation.',
              'expertEvaluation': 'The theory of plate tectonics is widely accepted in the scientific community, supported by evidence from geology, paleontology, and geophysics. It successfully explains a wide range of geological phenomena and has strong predictive power. The theory has been refined over time to incorporate new findings about mantle convection and the driving forces behind plate movement.',
              'highScoreFeedback': 'Your evaluation aligns with the scientific consensus. The theory of plate tectonics is indeed well-supported by geological evidence, has strong explanatory power for diverse phenomena like earthquakes and mountain formation, and has been refined over time.',
              'mediumScoreFeedback': 'Your evaluation shows some understanding of the theory\'s strengths, but you may be underestimating its empirical support or explanatory power. The theory of plate tectonics is considered a fundamental theory in geology with strong evidence supporting it.',
              'lowScoreFeedback': 'Your evaluation suggests some misconceptions about the theory of plate tectonics. In the scientific community, it is considered a well-established theory with strong empirical support and explanatory power for a wide range of geological phenomena.'
            },
            {
              'id': 'big_bang',
              'name': 'Big Bang Theory',
              'description': 'The theory that the universe began as a singularity approximately 13.8 billion years ago and has been expanding ever since.',
              'expertEvaluation': 'The Big Bang theory is the prevailing cosmological model for the observable universe, supported by multiple lines of evidence including cosmic microwave background radiation, the abundance of light elements, and the observed expansion of the universe. While there are still open questions about the earliest moments of the universe and the nature of dark matter and dark energy, the core theory is widely accepted in the scientific community.',
              'highScoreFeedback': 'Your evaluation aligns with the scientific consensus. The Big Bang theory is indeed well-supported by astronomical observations, has strong explanatory power for the observed features of our universe, and has been refined over time to address new findings.',
              'mediumScoreFeedback': 'Your evaluation shows some understanding of the theory\'s strengths, but you may be underestimating its empirical support or explanatory power. The Big Bang theory is considered the most successful and widely accepted cosmological model.',
              'lowScoreFeedback': 'Your evaluation suggests some misconceptions about the Big Bang theory. In the scientific community, it is considered the most successful cosmological model, with strong empirical support from multiple astronomical observations.'
            }
          ],
          'criteria': [
            {
              'id': 'empirical_support',
              'name': 'Empirical Support',
              'description': 'The extent to which the theory is supported by observable evidence',
              'weight': 5
            },
            {
              'id': 'explanatory_power',
              'name': 'Explanatory Power',
              'description': 'How well the theory explains observed phenomena',
              'weight': 4
            },
            {
              'id': 'predictive_power',
              'name': 'Predictive Power',
              'description': 'The ability of the theory to make testable predictions',
              'weight': 4
            },
            {
              'id': 'falsifiability',
              'name': 'Falsifiability',
              'description': 'Whether the theory can be proven false through observation or experiment',
              'weight': 3
            },
            {
              'id': 'parsimony',
              'name': 'Parsimony',
              'description': 'The simplicity of the theory relative to its explanatory power',
              'weight': 2
            },
            {
              'id': 'coherence',
              'name': 'Coherence',
              'description': 'How well the theory fits with other established scientific knowledge',
              'weight': 3
            }
          ]
        },
        isImplemented: true,
      ),

      // Interactive Matrix Operations Visualizer
      InteractiveWidgetModel(
        id: 'interactive_matrix_operations_visualizer_1',
        name: 'Matrix Operations Visualizer',
        type: 'interactive_matrix_operations_visualizer',
        category: 'Mathematics',
        description:
            'An interactive tool for visualizing matrix operations step by step.',
        data: {
          'title': 'Matrix Operations Visualizer',
          'operations': [
            'Addition',
            'Subtraction',
            'Multiplication',
            'Transpose',
            'Determinant',
            'Inverse'
          ],
          'defaultOperation': 'Addition',
          'primaryColor': '#2196F3',
          'secondaryColor': '#4CAF50',
          'highlightColor': '#FF9800',
          'resultColor': '#9C27B0',
          'showSteps': true,
          'matrixARows': 2,
          'matrixACols': 2,
          'matrixBRows': 2,
          'matrixBCols': 2,
          'matrixA': [
            [1.0, 2.0],
            [3.0, 4.0]
          ],
          'matrixB': [
            [5.0, 6.0],
            [7.0, 8.0]
          ],
          'showNameTag': true,
        },
        isImplemented: true,
      ),

      // Interactive Number Base Converter
      InteractiveWidgetModel(
        id: 'interactive_number_base_converter_1',
        name: 'Number Base Converter',
        type: 'interactive_number_base_converter',
        category: 'Mathematics',
        description:
            'An interactive tool for converting numbers between different bases with step-by-step explanations.',
        data: {
          'title': 'Number Base Converter',
          'primaryColor': '#3F51B5',
          'secondaryColor': '#009688',
          'accentColor': '#FF5722',
          'showSteps': true,
          'defaultInputValue': '101',
          'defaultFromBase': 2,
          'defaultToBase': 10,
          'showNameTag': true,
        },
        isImplemented: true,
      ),

      // Truth Table Explorer
      InteractiveWidgetModel(
        id: 'truth_table_explorer_1',
        name: 'Truth Table Explorer',
        type: 'Logic',
        category: 'Logic',
        description:
            'An interactive widget for exploring truth tables of logical propositions.',
        data: {
          'proposition': 'P → Q',
          'variables': ['P', 'Q'],
          'correctAnswers': [true, false, true, true],
          'explanation':
              'A conditional statement P → Q is only false when P is true and Q is false.',
          'showNameTag': true,
        },
        isImplemented: true,
      ),

      // Interactive Proof by Contradiction
      InteractiveWidgetModel(
        id: 'interactive_proof_contradiction_1',
        name: 'Proof by Contradiction',
        type: 'Logic',
        category: 'Logic',
        description:
            'An interactive widget for exploring proofs by contradiction.',
        data: {
          'statement': '√2 is irrational',
          'negated_statement':
              '√2 is rational (can be expressed as a/b where a and b are integers with no common factors)',
          'proof_steps': [
            {
              'prompt':
                  'If √2 is rational, then it can be written as a/b where a and b are integers with no common factors. What can we derive?',
              'options': ['a² = 2b²', 'a² = b²', 'a = 2b', 'a = b²'],
              'correct_option_index': 0,
              'correct_feedback':
                  'Correct! Squaring both sides of √2 = a/b gives us 2 = a²/b², which means a² = 2b².',
              'incorrect_feedback':
                  'Not quite. If √2 = a/b, then squaring both sides gives us 2 = a²/b², which means a² = 2b².',
            },
            {
              'prompt': 'If a² = 2b², what can we conclude about a?',
              'options': [
                'a is odd',
                'a is even',
                'a is prime',
                'a is irrational',
              ],
              'correct_option_index': 1,
              'correct_feedback':
                  'Correct! Since a² = 2b², a² is even. If a² is even, then a must be even.',
              'incorrect_feedback':
                  'Not quite. Since a² = 2b², a² is even. If a² is even, then a must be even (odd × odd = odd).',
            },
            {
              'prompt':
                  'If a is even, we can write a = 2k for some integer k. Substituting this into a² = 2b², what do we get?',
              'options': ['b² = 2k²', 'b² = k²', 'b² = 4k²', 'b² = k'],
              'correct_option_index': 0,
              'correct_feedback':
                  'Correct! Substituting a = 2k into a² = 2b² gives us (2k)² = 2b², which simplifies to 4k² = 2b², and finally b² = 2k².',
              'incorrect_feedback':
                  'Not quite. Substituting a = 2k into a² = 2b² gives us (2k)² = 2b², which simplifies to 4k² = 2b², and finally b² = 2k².',
            },
            {
              'prompt': 'What can we conclude about b from b² = 2k²?',
              'options': [
                'b is odd',
                'b is even',
                'b is prime',
                'b is irrational',
              ],
              'correct_option_index': 1,
              'correct_feedback':
                  'Correct! Since b² = 2k², b² is even. If b² is even, then b must be even.',
              'incorrect_feedback':
                  'Not quite. Since b² = 2k², b² is even. If b² is even, then b must be even (odd × odd = odd).',
            },
            {
              'prompt':
                  'If both a and b are even, what contradiction do we have?',
              'options': [
                'This contradicts our assumption that √2 is rational',
                'This contradicts our assumption that a and b have no common factors',
                'This contradicts the definition of irrational numbers',
                'This contradicts the properties of square roots',
              ],
              'correct_option_index': 1,
              'correct_feedback':
                  'Correct! We assumed a/b was in simplest form with no common factors, but we proved both a and b are even, meaning they share a common factor of 2.',
              'incorrect_feedback':
                  'Not quite. We assumed a/b was in simplest form with no common factors, but we proved both a and b are even, meaning they share a common factor of 2.',
            },
          ],
          'showNameTag': true,
        },
        isImplemented: true,
      ),

      // Interactive Syllogism Builder
      InteractiveWidgetModel(
        id: 'interactive_syllogism_builder_1',
        name: 'Syllogism Builder',
        type: 'Logic',
        category: 'Logic',
        description:
            'An interactive widget for building and validating syllogisms.',
        data: {
          'title': 'Build a Valid Syllogism',
          'valid_feedback':
              'Excellent! You\'ve constructed a valid syllogism that follows the correct logical form.',
          'invalid_feedback':
              'This combination doesn\'t form a valid syllogism. Check the logical structure and try again.',
          'correct_combination': [0, 0, 0],
          'major_premise_options': [
            {
              'text': 'All mammals are warm-blooded.',
              'explanation':
                  'This is a universal affirmative statement (All A are B).',
            },
            {
              'text': 'No reptiles are warm-blooded.',
              'explanation':
                  'This is a universal negative statement (No A are B).',
            },
            {
              'text': 'Some birds are flightless.',
              'explanation':
                  'This is a particular affirmative statement (Some A are B).',
            },
          ],
          'minor_premise_options': [
            {
              'text': 'All whales are mammals.',
              'explanation':
                  'This is a universal affirmative statement (All C are A).',
            },
            {
              'text': 'Some animals are reptiles.',
              'explanation':
                  'This is a particular affirmative statement (Some C are A).',
            },
            {
              'text': 'No birds are mammals.',
              'explanation':
                  'This is a universal negative statement (No C are A).',
            },
          ],
          'conclusion_options': [
            {
              'text': 'Therefore, all whales are warm-blooded.',
              'explanation':
                  'This is a universal affirmative conclusion (All C are B).',
            },
            {
              'text': 'Therefore, some animals are warm-blooded.',
              'explanation':
                  'This is a particular affirmative conclusion (Some C are B).',
            },
            {
              'text': 'Therefore, no whales are reptiles.',
              'explanation':
                  'This is a universal negative conclusion (No C are A).',
            },
          ],
          'showNameTag': true,
        },
        isImplemented: true,
      ),

      // Interactive Triangle Angle Sum
      InteractiveWidgetModel(
        id: 'interactive_triangle_angle_sum_1',
        name: 'Triangle Angle Sum',
        type: 'Geometry',
        category: 'Mathematics',
        description:
            'An interactive widget demonstrating that the sum of interior angles in a triangle is always 180°.',
        data: {
          'vertices': [
            {'x': 150, 'y': 50}, // Top vertex
            {'x': 50, 'y': 250}, // Bottom left vertex
            {'x': 250, 'y': 250}, // Bottom right vertex
          ],
          'triangle_color': '#1E88E5',
          'angle_color': '#FF9800',
          'vertex_color': '#2196F3',
          'text_color': '#212121',
          'show_angles': true,
          'show_degrees': true,
          'show_sum': true,
          'challenge_mode': false,
          'target_sum': 180.0,
          'tolerance': 2.0,
          'challenge_text':
              'Create a triangle with interior angles summing to 180°',
          'showNameTag': true,
        },
        isImplemented: true,
      ),

      // Interactive Fallacy Identification
      InteractiveWidgetModel(
        id: 'interactive_fallacy_identification_1',
        name: 'Fallacy Identification',
        type: 'Logic',
        category: 'Reasoning',
        description:
            'An interactive widget for identifying logical fallacies in arguments.',
        data: {
          'title': 'Identify the Fallacy',
          'scenario':
              'John argues: "Many great scientists like Einstein and Newton believed in God. Therefore, God must exist."',
          'options': [
            {
              'name': 'Appeal to Authority',
              'description':
                  'Using the opinion of an authority figure as evidence in an argument',
            },
            {
              'name': 'Ad Hominem',
              'description':
                  'Attacking the person instead of addressing their argument',
            },
            {
              'name': 'False Dichotomy',
              'description': 'Presenting only two options when others exist',
            },
            {
              'name': 'Slippery Slope',
              'description':
                  'Asserting that a small step will lead to a chain of events ending in disaster',
            },
          ],
          'correct_option_index': 0,
          'explanation':
              'This is an Appeal to Authority fallacy. The argument relies on the authority of famous scientists rather than providing evidence for God\'s existence. Even though Einstein and Newton were brilliant scientists, their personal beliefs about God don\'t constitute proof of God\'s existence.',
          'primary_color': '#6200EE',
          'correct_color': '#4CAF50',
          'incorrect_color': '#F44336',
          'neutral_color': '#F5F5F5',
          'showNameTag': true,
        },
        isImplemented: true,
      ),

      // Interactive Conditional Flow
      InteractiveWidgetModel(
        id: 'interactive_conditional_flow_1',
        name: 'Conditional Logic Flow',
        type: 'Logic',
        category: 'Reasoning',
        description:
            'An interactive widget demonstrating conditional logic flow with if-then statements.',
        data: {
          'title': 'Conditional Logic Flow',
          'condition': 'It is raining',
          'consequent': 'Take an umbrella',
          'alternative': 'Leave umbrella at home',
          'show_alternative': true,
          'true_color': '#4CAF50',
          'false_color': '#F44336',
          'neutral_color': '#E0E0E0',
          'highlight_color': '#2196F3',
          'scenarios': [
            {
              'condition_text': 'It is raining heavily outside',
              'condition_value': true,
              'consequent_text': 'I should take an umbrella',
              'question':
                  'If it is raining heavily outside, then I should take an umbrella. Given that it IS raining heavily, which statement is logically valid?',
              'options': [
                {
                  'text': 'I should take an umbrella',
                  'is_correct': true,
                  'explanation':
                      'Correct! This follows the modus ponens rule: If P, then Q. P is true, therefore Q is true. Since it is raining (P is true), the consequent "I should take an umbrella" (Q) must be true.',
                },
                {
                  'text': 'I should not take an umbrella',
                  'is_correct': false,
                  'explanation':
                      'Incorrect. This contradicts the conditional statement. If it is raining (which it is), then the logical conclusion is that you should take an umbrella.',
                },
                {
                  'text':
                      'It is not possible to determine if I should take an umbrella',
                  'is_correct': false,
                  'explanation':
                      'Incorrect. The conditional statement clearly states that if it is raining, then you should take an umbrella. Since it is raining, we can determine that you should take an umbrella.',
                },
              ],
            },
            {
              'condition_text': 'It is sunny outside',
              'condition_value': false,
              'consequent_text': 'I should take an umbrella',
              'question':
                  'If it is raining heavily outside, then I should take an umbrella. Given that it is NOT raining (it\'s sunny), which statement is logically valid?',
              'options': [
                {
                  'text': 'I should take an umbrella',
                  'is_correct': false,
                  'explanation':
                      'Incorrect. The conditional statement only tells us what to do if it is raining. It doesn\'t specify what to do if it\'s not raining.',
                },
                {
                  'text': 'I should not take an umbrella',
                  'is_correct': false,
                  'explanation':
                      'Incorrect. The conditional statement doesn\'t tell us what to do if it\'s not raining. It only specifies what to do if it is raining.',
                },
                {
                  'text':
                      'It is not possible to determine if I should take an umbrella',
                  'is_correct': true,
                  'explanation':
                      'Correct! This is the fallacy of denying the antecedent. If P, then Q. P is false, but we cannot determine whether Q is true or false based solely on this information. The conditional statement doesn\'t tell us what to do when it\'s not raining.',
                },
              ],
            },
          ],
          'showNameTag': true,
        },
        isImplemented: true,
      ),

      // Interactive Balance Scale Analogy
      InteractiveWidgetModel(
        id: 'interactive_balance_scale_analogy_1',
        name: 'Balance Scale Equation Solver',
        type: 'Math',
        category: 'Mathematics',
        description:
            'An interactive widget demonstrating equation solving using a balance scale analogy.',
        data: {
          'equation': 'x + 3 = 5',
          'variable_name': 'x',
          'solution': '2',
          'steps': [
            {
              'equation': 'x + 3 = 5',
              'operation': 'Initial equation',
              'explanation':
                  'We start with the given equation. The left side has x and 3, and the right side has 5.',
            },
            {
              'equation': 'x + 3 - 3 = 5 - 3',
              'operation': 'Subtract 3 from both sides',
              'explanation':
                  'To isolate the variable, we subtract 3 from both sides of the equation. This keeps the equation balanced.',
            },
            {
              'equation': 'x = 2',
              'operation': 'Simplify',
              'explanation':
                  'After simplifying, we get x = 2. We have successfully solved for x.',
            },
          ],
          'primary_color': '#2196F3',
          'variable_color': '#9C27B0',
          'constant_color': '#FF9800',
          'operation_color': '#4CAF50',
          'showNameTag': true,
        },
        isImplemented: true,
      ),

      // Interactive Equation Solver
      InteractiveWidgetModel(
        id: 'interactive_equation_solver_1',
        name: 'Interactive Equation Solver',
        type: 'Math',
        category: 'Mathematics',
        description:
            'An interactive widget that allows users to solve equations step by step.',
        data: {
          'initial_equation': '2x + 5 = 15',
          'variable_name': 'x',
          'solution': '5',
          'available_operations': [
            'Add to both sides',
            'Subtract from both sides',
            'Multiply both sides',
            'Divide both sides',
            'Combine like terms',
            'Distribute',
            'Simplify',
          ],
          'primary_color': '#2196F3',
          'success_color': '#4CAF50',
          'error_color': '#F44336',
          'hint_color': '#FF9800',
          'showNameTag': true,
        },
        isImplemented: true,
      ),

      // Interactive Step-by-Step Equation Solver
      InteractiveWidgetModel(
        id: 'interactive_step_by_step_equation_solver_1',
        name: 'Step-by-Step Equation Solver',
        type: 'Math',
        category: 'Mathematics',
        description:
            'A guided interactive widget that helps users solve equations step by step with multiple choice options.',
        data: {
          'initial_equation': '3x + 7 = 22',
          'variable_name': 'x',
          'solution': '5',
          'solution_steps': [
            {
              'equation': '3x + 7 = 22',
              'operation': 'Initial equation',
              'explanation': 'We start with the given equation.',
              'options': [],
            },
            {
              'equation': '3x + 7 - 7 = 22 - 7',
              'operation': 'Subtract 7 from both sides',
              'explanation':
                  'To isolate the variable term, we subtract 7 from both sides of the equation.',
              'options': [
                {
                  'text': 'Subtract 7 from both sides',
                  'is_correct': true,
                  'explanation':
                      'Correct! Subtracting 7 from both sides helps isolate the variable term.',
                },
                {
                  'text': 'Add 7 to both sides',
                  'is_correct': false,
                  'explanation':
                      'Adding 7 to both sides would make the equation more complex.',
                },
                {
                  'text': 'Divide both sides by 3',
                  'is_correct': false,
                  'explanation':
                      'We should first isolate the variable term (3x) before dividing.',
                },
              ],
            },
            {
              'equation': '3x = 15',
              'operation': 'Simplify',
              'explanation':
                  'After subtracting 7 from both sides, we get 3x = 15.',
              'options': [
                {
                  'text': 'Simplify the equation',
                  'is_correct': true,
                  'explanation':
                      'Correct! 22 - 7 = 15, so the equation becomes 3x = 15.',
                },
              ],
            },
            {
              'equation': '3x ÷ 3 = 15 ÷ 3',
              'operation': 'Divide both sides by 3',
              'explanation':
                  'To isolate x, we divide both sides of the equation by 3.',
              'options': [
                {
                  'text': 'Divide both sides by 3',
                  'is_correct': true,
                  'explanation':
                      'Correct! Dividing both sides by 3 will isolate the variable x.',
                },
                {
                  'text': 'Multiply both sides by 3',
                  'is_correct': false,
                  'explanation':
                      'Multiplying by 3 would make the equation more complex.',
                },
                {
                  'text': 'Subtract 3 from both sides',
                  'is_correct': false,
                  'explanation':
                      'Subtracting 3 would not help isolate the variable.',
                },
              ],
            },
            {
              'equation': 'x = 5',
              'operation': 'Simplify',
              'explanation': 'After dividing both sides by 3, we get x = 5.',
              'options': [
                {
                  'text': 'Simplify the equation',
                  'is_correct': true,
                  'explanation':
                      'Correct! 15 ÷ 3 = 5, so the equation becomes x = 5.',
                },
              ],
            },
          ],
          'primary_color': '#2196F3',
          'success_color': '#4CAF50',
          'error_color': '#F44336',
          'hint_color': '#FF9800',
          'neutral_color': '#E0E0E0',
          'showNameTag': true,
        },
        isImplemented: true,
      ),

      // Interactive Word Problem Translator
      InteractiveWidgetModel(
        id: 'interactive_word_problem_translator_1',
        name: 'Word Problem Translator',
        type: 'Math',
        category: 'Mathematics',
        description:
            'An interactive widget that helps users translate word problems into algebraic equations.',
        data: {
          'problems': [
            {
              'problem_text': 'A number plus 7 equals 15. Find the number.',
              'variable_definition': 'Let x = the unknown number',
              'equation': 'x + 7 = 15',
              'solution': '8',
              'hint':
                  'Identify the unknown quantity and assign a variable to it. Then translate the word "plus" to the + operation.',
              'explanation':
                  'We let x represent the unknown number. The phrase "a number plus 7" translates to "x + 7". The phrase "equals 15" translates to "= 15". So the equation is x + 7 = 15.',
            },
            {
              'problem_text':
                  'When 5 is subtracted from twice a number, the result is 13. Find the number.',
              'variable_definition': 'Let x = the unknown number',
              'equation': '2x - 5 = 13',
              'solution': '9',
              'hint':
                  'Identify the unknown quantity and assign a variable to it. "Twice a number" means 2 times the variable.',
              'explanation':
                  'We let x represent the unknown number. "Twice a number" translates to "2x". "5 is subtracted from" means "2x - 5". "The result is 13" translates to "= 13". So the equation is 2x - 5 = 13.',
            },
            {
              'problem_text':
                  'The sum of three consecutive integers is 42. Find the smallest integer.',
              'variable_definition': 'Let x = the smallest integer',
              'equation': 'x + (x + 1) + (x + 2) = 42',
              'solution': '13',
              'hint':
                  'If x is the smallest integer, then the next consecutive integers are x + 1 and x + 2.',
              'explanation':
                  'We let x represent the smallest integer. The next consecutive integers are x + 1 and x + 2. The sum of these three integers is x + (x + 1) + (x + 2) = 42.',
            },
          ],
          'primary_color': '#2196F3',
          'success_color': '#4CAF50',
          'error_color': '#F44336',
          'hint_color': '#FF9800',
          'neutral_color': '#E0E0E0',
          'showNameTag': true,
        },
        isImplemented: true,
      ),

      // Interactive Counterexample Builder
      InteractiveWidgetModel(
        id: 'interactive_counterexample_builder_1',
        name: 'Counterexample Builder',
        type: 'Logic',
        category: 'Mathematical Thinking',
        description:
            'An interactive widget that helps users build counterexamples to disprove mathematical statements.',
        data: {
          'statements': [
            {
              'statement_text': 'All even numbers are divisible by 4.',
              'is_true': false,
              'hint': 'Think about even numbers that are not multiples of 4.',
              'sample_counterexample':
                  '6 is an even number, but it is not divisible by 4.',
              'explanation':
                  'This statement is false. While all numbers divisible by 4 are even, not all even numbers are divisible by 4. For example, 2, 6, 10, and 14 are even numbers that are not divisible by 4.',
            },
            {
              'statement_text':
                  'If a quadrilateral has four equal sides, then it is a square.',
              'is_true': false,
              'hint': 'Consider shapes with equal sides but different angles.',
              'sample_counterexample':
                  'A rhombus has four equal sides, but it is not a square if its angles are not 90 degrees.',
              'explanation':
                  'This statement is false. A quadrilateral with four equal sides is called a rhombus. A square is a special case of a rhombus where all angles are 90 degrees. A rhombus with angles that are not 90 degrees is a counterexample.',
            },
            {
              'statement_text': 'If x² = 4, then x = 2.',
              'is_true': false,
              'hint':
                  'Remember that a squared number can have two possible square roots.',
              'sample_counterexample': 'If x = -2, then x² = 4, but x ≠ 2.',
              'explanation':
                  'This statement is false. The equation x² = 4 has two solutions: x = 2 and x = -2. So the statement "If x² = 4, then x = 2" is not always true, as x could also equal -2.',
            },
            {
              'statement_text':
                  'For all real numbers a and b, if a < b, then a² < b².',
              'is_true': false,
              'hint': 'Consider what happens when both numbers are negative.',
              'sample_counterexample':
                  'If a = -3 and b = -2, then a < b, but a² = 9 and b² = 4, so a² > b².',
              'explanation':
                  'This statement is false. When both a and b are negative and a < b, then a² > b². For example, if a = -3 and b = -2, then a < b, but a² = 9 and b² = 4, so a² > b².',
            },
            {
              'statement_text': 'The sum of two prime numbers is always odd.',
              'is_true': false,
              'hint': 'Consider the smallest prime number and its properties.',
              'sample_counterexample':
                  '3 + 2 = 5, which is odd, but 2 + 2 = 4, which is even.',
              'explanation':
                  'This statement is false. While many pairs of prime numbers do sum to an odd number, there are counterexamples. The number 2 is the only even prime number. If we add 2 to any other prime number (which must be odd), we get an odd sum. But if we add 2 to itself, we get 2 + 2 = 4, which is even.',
            },
          ],
          'primary_color': '#2196F3',
          'success_color': '#4CAF50',
          'error_color': '#F44336',
          'hint_color': '#FF9800',
          'neutral_color': '#E0E0E0',
          'showNameTag': true,
        },
        isImplemented: true,
      ),

      // Interactive Logical Chain Constructor
      InteractiveWidgetModel(
        id: 'interactive_logical_chain_constructor_1',
        name: 'Logical Chain Constructor',
        type: 'Logic',
        category: 'Mathematical Thinking',
        description:
            'An interactive widget that helps users construct logical chains of reasoning.',
        data: {
          'problems': [
            {
              'title': 'Socrates is Mortal',
              'description':
                  'Arrange the statements to form a valid logical chain of reasoning.',
              'statements': [
                'All men are mortal.',
                'Socrates is a man.',
                'Therefore, Socrates is mortal.',
              ],
              'correct_order': [0, 1, 2],
              'hint':
                  'Start with the general premise about all men, then apply it to the specific case of Socrates.',
              'explanation':
                  'This is a classic syllogism. We start with the major premise "All men are mortal," followed by the minor premise "Socrates is a man." From these two premises, we can logically conclude that "Socrates is mortal."',
            },
            {
              'title': 'Divisibility by 6',
              'description':
                  'Arrange the statements to form a valid logical chain of reasoning about divisibility by 6.',
              'statements': [
                'If a number is divisible by 6, then it is divisible by both 2 and 3.',
                'The number 18 is divisible by both 2 and 3.',
                'The number 18 is divisible by 2 because it is even.',
                'The number 18 is divisible by 3 because the sum of its digits (1+8=9) is divisible by 3.',
                'Therefore, 18 is divisible by 6.',
              ],
              'correct_order': [0, 2, 3, 1, 4],
              'hint':
                  'Start with the general rule about divisibility by 6, then verify the conditions for the number 18.',
              'explanation':
                  'We begin with the rule that a number is divisible by 6 if and only if it is divisible by both 2 and 3. Then we verify that 18 is divisible by 2 (it\'s even) and that 18 is divisible by 3 (sum of digits is divisible by 3). Since both conditions are met, we can conclude that 18 is divisible by 6.',
            },
            {
              'title': 'Pythagorean Theorem Application',
              'description':
                  'Arrange the statements to form a valid logical chain of reasoning to find the length of the hypotenuse.',
              'statements': [
                'In a right triangle, the square of the hypotenuse equals the sum of the squares of the other two sides (Pythagorean Theorem).',
                'We have a right triangle with sides of length 3 and 4.',
                'The square of the hypotenuse = 3² + 4² = 9 + 16 = 25.',
                'The hypotenuse = √25 = 5.',
              ],
              'correct_order': [0, 1, 2, 3],
              'hint':
                  'Start with the theorem, then apply it to the specific triangle.',
              'explanation':
                  'We start with the Pythagorean Theorem (a² + b² = c²), then identify our specific triangle with sides 3 and 4. We calculate the square of the hypotenuse as 3² + 4² = 25, and finally take the square root to find the hypotenuse length of 5.',
            },
          ],
          'primary_color': '#2196F3',
          'success_color': '#4CAF50',
          'error_color': '#F44336',
          'hint_color': '#FF9800',
          'neutral_color': '#E0E0E0',
          'showNameTag': true,
        },
        isImplemented: true,
      ),

      // Interactive Logical Fallacy Quiz
      InteractiveWidgetModel(
        id: 'interactive_logical_fallacy_quiz_1',
        name: 'Logical Fallacy Quiz',
        type: 'Logic',
        category: 'Reasoning',
        description:
            'An interactive quiz that tests users\' ability to identify logical fallacies in various scenarios.',
        data: {
          'title': 'Logical Fallacy Quiz',
          'showProgress': true,
          'scenarios': [
            {
              'scenario':
                  'A politician argues: "My opponent wants to increase funding for public education. This is just the first step in a plan to raise your taxes to astronomical levels and create a socialist state."',
              'question': 'What fallacy is being used in this argument?',
              'options': [
                {
                  'text': 'Slippery Slope',
                  'is_correct': true,
                  'feedback_correct':
                      'Correct! This is a classic Slippery Slope fallacy. The argument assumes that one action (increasing education funding) will inevitably lead to a chain of increasingly extreme consequences without providing evidence for this chain of events.',
                  'feedback_incorrect':
                      'Think about how the argument predicts a series of increasingly extreme consequences from a single action.',
                },
                {
                  'text': 'Ad Hominem',
                  'is_correct': false,
                  'feedback_incorrect':
                      'Not quite. An Ad Hominem fallacy attacks the person rather than their argument. This argument doesn\'t attack the opponent\'s character.',
                },
                {
                  'text': 'False Dichotomy',
                  'is_correct': false,
                  'feedback_incorrect':
                      'Not quite. A False Dichotomy presents only two options when more exist. This argument is predicting a chain of events, not presenting limited options.',
                },
                {
                  'text': 'Appeal to Authority',
                  'is_correct': false,
                  'feedback_incorrect':
                      'Not quite. An Appeal to Authority uses the opinion of an authority figure as evidence. This argument doesn\'t cite any authority.',
                },
              ],
              'explanation':
                  'The Slippery Slope fallacy occurs when someone claims that a relatively small first step will inevitably lead to a chain of related events, usually culminating in some significant negative impact. This argument assumes that increasing education funding will automatically lead to extreme tax increases and socialism without providing evidence for this chain of events.',
            },
            {
              'scenario':
                  'During a debate about climate policy, one person says: "We shouldn\'t listen to Dr. Smith\'s research on climate change. He drives an SUV, so he\'s clearly a hypocrite."',
              'question': 'What fallacy is being used in this argument?',
              'options': [
                {
                  'text': 'Ad Hominem',
                  'is_correct': true,
                  'feedback_correct':
                      'Correct! This is an Ad Hominem fallacy. The argument attacks Dr. Smith\'s character (calling him a hypocrite) rather than addressing the content of his research.',
                  'feedback_incorrect':
                      'Think about how the argument focuses on the person rather than their research.',
                },
                {
                  'text': 'Straw Man',
                  'is_correct': false,
                  'feedback_incorrect':
                      'Not quite. A Straw Man fallacy misrepresents someone\'s argument to make it easier to attack. This argument attacks the person, not a misrepresentation of their argument.',
                },
                {
                  'text': 'Appeal to Authority',
                  'is_correct': false,
                  'feedback_incorrect':
                      'Not quite. An Appeal to Authority uses the opinion of an authority figure as evidence. This argument is actually attacking an authority figure, not appealing to one.',
                },
                {
                  'text': 'Post Hoc Ergo Propter Hoc',
                  'is_correct': false,
                  'feedback_incorrect':
                      'Not quite. Post Hoc Ergo Propter Hoc assumes that if one event follows another, the first caused the second. This argument doesn\'t make a causal claim.',
                },
              ],
              'explanation':
                  'The Ad Hominem fallacy attacks the person making the argument rather than addressing the argument itself. In this case, the person is dismissing Dr. Smith\'s research based on his personal choices (driving an SUV), which has no bearing on the validity of his research. Even if Dr. Smith were a hypocrite, his research could still be sound.',
            },
            {
              'scenario':
                  'A student argues: "Einstein believed in God, and he was one of the smartest people ever. Therefore, God must exist."',
              'question': 'What fallacy is being used in this argument?',
              'options': [
                {
                  'text': 'Appeal to Authority',
                  'is_correct': true,
                  'feedback_correct':
                      'Correct! This is an Appeal to Authority fallacy. The argument uses Einstein\'s belief as evidence for God\'s existence, but Einstein\'s expertise in physics doesn\'t make him an authority on theology.',
                  'feedback_incorrect':
                      'Think about how the argument relies on Einstein\'s status rather than evidence.',
                },
                {
                  'text': 'False Dichotomy',
                  'is_correct': false,
                  'feedback_incorrect':
                      'Not quite. A False Dichotomy presents only two options when more exist. This argument doesn\'t present limited options.',
                },
                {
                  'text': 'Slippery Slope',
                  'is_correct': false,
                  'feedback_incorrect':
                      'Not quite. A Slippery Slope fallacy claims one event will lead to a chain of increasingly negative events. This argument doesn\'t predict a chain of events.',
                },
                {
                  'text': 'Post Hoc Ergo Propter Hoc',
                  'is_correct': false,
                  'feedback_incorrect':
                      'Not quite. Post Hoc Ergo Propter Hoc assumes that if one event follows another, the first caused the second. This argument doesn\'t make a causal claim.',
                },
              ],
              'explanation':
                  'The Appeal to Authority fallacy occurs when someone claims something is true simply because an authority figure said it, without providing actual evidence. While Einstein was brilliant in physics, his personal religious beliefs don\'t constitute evidence for God\'s existence. The argument relies on Einstein\'s status rather than presenting actual evidence.',
            },
          ],
          'primary_color': '#2196F3',
          'success_color': '#4CAF50',
          'error_color': '#F44336',
          'hint_color': '#FF9800',
          'neutral_color': '#E0E0E0',
          'showNameTag': true,
        },
        isImplemented: true,
      ),

      // Interactive Logic Puzzle
      InteractiveWidgetModel(
        id: 'interactive_logic_puzzle_1',
        name: 'Logic Puzzle',
        type: 'Logic',
        category: 'Mathematical Thinking',
        description:
            'An interactive logic puzzle that challenges users to solve problems using deductive reasoning.',
        data: {
          'puzzleType': 'multiple_choice',
          'title': 'The Three Doors Puzzle',
          'description':
              'You face three doors: Red, Blue, Green.\n*   Behind one door is treasure; behind the other two are traps.\n*   Each door has a statement, but **only one statement is true**.\n\n*   **Red Door:** \"The treasure is not behind the Blue door.\"\n*   **Blue Door:** \"The treasure is not behind this door.\"\n*   **Green Door:** \"The treasure is behind this door.\"',
          'options': [
            {'id': 'red', 'text': 'Red Door'},
            {'id': 'blue', 'text': 'Blue Door'},
            {'id': 'green', 'text': 'Green Door'},
          ],
          'correctAnswer': 'blue',
          'correctFeedback':
              'Correct! The only scenario where exactly one statement is true is if the Red Door\'s statement (\"Treasure not behind Blue\") is true. This implies the treasure IS behind the Blue door (making Blue Door\'s statement false) and NOT behind the Green door (making Green Door\'s statement false).',
          'incorrectFeedback':
              'Not quite right. Think carefully about which scenario would make exactly one statement true.',
          'explanation':
              'Let\'s analyze each possibility:\n\n1. If Red Door\'s statement is true (\"Treasure not behind Blue\"), then the treasure must be behind Red or Green. If it\'s behind Red, then Blue\'s statement is true too (\"Treasure not behind Blue\"), which violates our rule. If it\'s behind Green, then Green\'s statement is true too, again violating our rule.\n\n2. If Blue Door\'s statement is true (\"Treasure not behind Blue\"), then the treasure must be behind Red or Green. If it\'s behind Red, Red\'s statement is false (since it says treasure not behind Blue, but it\'s actually not behind Red). If it\'s behind Green, Green\'s statement is true too, violating our rule.\n\n3. If Green Door\'s statement is true (\"Treasure behind Green\"), then the treasure is behind Green. This makes Red\'s statement false (since it says treasure not behind Blue, but it\'s actually behind Green). Blue\'s statement is true (\"Treasure not behind Blue\"), violating our rule.\n\nThe only consistent scenario is if Red\'s statement is true, Blue\'s and Green\'s are false, and the treasure is behind the Blue door.',
          'showHints': true,
          'hint':
              'Try working through each possibility systematically. If Red\'s statement is true, what does that tell you about where the treasure is?',
          'maxAttempts': 3,
          'showNameTag': true,
        },
        isImplemented: true,
      ),

      // Interactive Logic Puzzle - Liar and Truth-teller
      InteractiveWidgetModel(
        id: 'interactive_logic_puzzle_2',
        name: 'Logic Puzzle - Liar and Truth-teller',
        type: 'Logic',
        category: 'Mathematical Thinking',
        description:
            'An interactive logic puzzle about finding the truth when one person always lies and one always tells the truth.',
        data: {
          'puzzleType': 'text_input',
          'title': 'The Liar and the Truth-teller',
          'description':
              'You meet two guards, Alex and Ben, at a fork in the road. One path leads to safety, the other to danger.\n*   One guard **always tells the truth**.\n*   The other guard **always lies**.\n*   You can ask **only one question** to **only one guard**.\n\nWhat single question can you ask to find the path to safety?',
          'correctAnswer': 'what would the other guard say is the safe path',
          'correctFeedback':
              'Excellent! That\'s the classic solution to this puzzle. By asking \"What would the other guard say is the safe path?\", you\'ll always get the wrong answer, regardless of which guard you ask. So you should take the opposite path!',
          'incorrectFeedback':
              'That\'s not quite the optimal question. Think about how you can use one guard\'s statement about the other to determine the truth.',
          'explanation':
              'This is a classic logic puzzle with an elegant solution. If you ask either guard \"What would the other guard say is the safe path?\", you\'ll always get the wrong answer!\n\nHere\'s why:\n- If you ask the truth-teller, they will honestly tell you what the liar would say, which would be the wrong path.\n- If you ask the liar, they will lie about what the truth-teller would say, which means they\'ll give you the wrong path.\n\nSince you know the answer will always be the wrong path, you should take the opposite path from what either guard tells you.',
          'showHints': true,
          'hint':
              'Think about how you can use one guard\'s knowledge about the other guard to your advantage. What if you asked about what the other guard would say?',
          'maxAttempts': 3,
          'showNameTag': true,
        },
        isImplemented: true,
      ),

      // Interactive Absolute Value Explorer
      InteractiveWidgetModel(
        id: 'interactive_absolute_value_explorer_1',
        name: 'Absolute Value Explorer',
        type: 'interactive_absolute_value_explorer',
        category: 'Math',
        description: 'An interactive tool for exploring absolute value functions and their properties.',
        data: {
          'title': 'Absolute Value Explorer',
          'description': 'Explore the properties of absolute value functions by adjusting the parameters of f(x) = a|x - h| + k.',
          'initialA': 1.0,
          'initialH': 0.0,
          'initialK': 0.0,
          'minX': -10.0,
          'maxX': 10.0,
          'minY': -10.0,
          'maxY': 10.0,
          'initialSelectedX': 2.0,
          'primaryColor': '#2196F3',
          'secondaryColor': '#FF9800',
          'accentColor': '#4CAF50',
          'textColor': '#212121',
          'challengeMode': true,
          'challenges': [
            {
              'description': 'Adjust the parameters to create the function f(x) = 2|x - 3| - 1',
              'targetA': 2.0,
              'targetH': 3.0,
              'targetK': -1.0,
              'successMessage': 'Great job! You\'ve correctly created f(x) = 2|x - 3| - 1.',
              'hint': 'Try setting a=2, h=3, and k=-1.'
            }
          ],
          'showNameTag': true
        },
        isImplemented: true,
      ),

      // Interactive Pendulum Simulation
      InteractiveWidgetModel(
        id: 'interactive_pendulum_simulation_1',
        name: 'Pendulum Simulation',
        type: 'interactive_pendulum_simulation',
        category: 'Physics',
        description: 'An interactive simulation of a pendulum with adjustable parameters.',
        data: {
          'title': 'Pendulum Simulation',
          'description': 'Explore the physics of a pendulum by adjusting its length, gravity, and other parameters.',
          'initialLength': 1.0,
          'initialGravity': 9.8,
          'initialAngle': 45.0,
          'initialDamping': 0.1,
          'primaryColor': '#3F51B5',
          'secondaryColor': '#FF5722',
          'accentColor': '#009688',
          'textColor': '#212121',
          'challengeMode': true,
          'challenges': [
            {
              'description': 'Adjust the parameters to achieve a pendulum with a period of 2.0 seconds.',
              'targetPeriod': '2.0',
              'successMessage': 'Great job! You\'ve created a pendulum with a period of 2.0 seconds.',
              'hint': 'Remember that the period T = 2π√(L/g). Try adjusting the length and gravity.'
            }
          ],
          'showNameTag': true
        },
        isImplemented: true,
      ),

      // Interactive Unit Converter
      InteractiveWidgetModel(
        id: 'interactive_unit_converter_1',
        name: 'Unit Converter',
        type: 'interactive_unit_converter',
        category: 'Tools',
        description: 'An interactive tool for converting between different units of measurement.',
        data: {
          'title': 'Unit Converter',
          'description': 'Convert between different units of measurement across various categories.',
          'initialCategory': 'length',
          'initialFromUnit': 'm',
          'initialToUnit': 'km',
          'initialInputValue': '1000',
          'primaryColor': '#673AB7',
          'secondaryColor': '#FFC107',
          'accentColor': '#00BCD4',
          'textColor': '#212121',
          'challengeMode': true,
          'challenges': [
            {
              'description': 'Convert 5 kilometers to meters.',
              'targetCategory': 'length',
              'targetFromUnit': 'km',
              'targetToUnit': 'm',
              'targetInputValue': '5',
              'successMessage': 'Great job! 5 kilometers equals 5000 meters.',
              'hint': 'Select the length category, set kilometers as the from unit, and meters as the to unit.'
            }
          ],
          'showNameTag': true
        },
        isImplemented: true,
      ),

      // Interactive Sorting Algorithm Visualizer
      InteractiveWidgetModel(
        id: 'interactive_sorting_algorithm_visualizer_1',
        name: 'Sorting Algorithm Visualizer',
        type: 'interactive_sorting_algorithm_visualizer',
        category: 'Computer Science',
        description: 'An interactive tool for visualizing different sorting algorithms.',
        data: {
          'title': 'Sorting Algorithm Visualizer',
          'description': 'Visualize and compare different sorting algorithms to understand how they work.',
          'initialArraySize': 20,
          'initialAlgorithm': 'bubble',
          'initialSpeed': 0.5,
          'primaryColor': '#2196F3',
          'secondaryColor': '#FF9800',
          'accentColor': '#4CAF50',
          'textColor': '#212121',
          'currentIndexColor': '#F44336',
          'compareIndexColor': '#9C27B0',
          'sortedColor': '#4CAF50',
          'challengeMode': true,
          'challenges': [
            {
              'description': 'Use the Selection Sort algorithm to sort the array.',
              'targetAlgorithm': 'selection',
              'successMessage': 'Great job! You\'ve successfully used Selection Sort to sort the array.',
              'hint': 'Selection Sort works by repeatedly finding the minimum element from the unsorted part and putting it at the beginning.'
            }
          ],
          'showNameTag': true
        },
        isImplemented: true,
      ),

      // Interactive Compound Inequality Builder
      InteractiveWidgetModel(
        id: 'interactive_compound_inequality_builder_1',
        name: 'Compound Inequality Builder',
        type: 'interactive_compound_inequality_builder',
        category: 'Math',
        description: 'An interactive tool for building and visualizing compound inequalities.',
        data: {
          'title': 'Compound Inequality Builder',
          'description': 'Build and visualize compound inequalities using AND/OR operators.',
          'minValue': -10.0,
          'maxValue': 10.0,
          'initialLogicalOperator': 'AND',
          'initialInequalities': [
            {
              'leftValue': 0.0,
              'rightValue': 5.0,
              'operator': '<',
              'isLeftVariable': true,
              'isRightVariable': false,
            },
          ],
          'primaryColor': '#3F51B5',
          'secondaryColor': '#FF5722',
          'accentColor': '#009688',
          'textColor': '#212121',
          'challengeMode': true,
          'challenges': [
            {
              'description': 'Build the compound inequality: x > -3 AND x < 2',
              'targetOperator': 'AND',
              'targetInequalities': [
                {
                  'leftValue': 0.0,
                  'rightValue': -3.0,
                  'operator': '>',
                  'isLeftVariable': true,
                  'isRightVariable': false,
                },
                {
                  'leftValue': 0.0,
                  'rightValue': 2.0,
                  'operator': '<',
                  'isLeftVariable': true,
                  'isRightVariable': false,
                },
              ],
              'successMessage': 'Great job! You\'ve correctly built the compound inequality x > -3 AND x < 2.',
              'hint': 'You need to create two inequalities connected with the AND operator.'
            }
          ],
          'showNameTag': true
        },
        isImplemented: true,
      ),

      // Interactive Coordinate Plane Grapher
      InteractiveWidgetModel(
        id: 'interactive_coordinate_plane_grapher_1',
        name: 'Coordinate Plane Grapher',
        type: 'interactive_coordinate_plane_grapher',
        category: 'Math',
        description: 'An interactive tool for graphing points, lines, and functions on a coordinate plane.',
        data: {
          'title': 'Coordinate Plane Grapher',
          'description': 'Graph points, lines, and functions on a coordinate plane to visualize mathematical relationships.',
          'xMin': -10.0,
          'xMax': 10.0,
          'yMin': -10.0,
          'yMax': 10.0,
          'gridSpacing': 1.0,
          'showGrid': true,
          'showAxes': true,
          'gridColor': '#CCCCCC',
          'axisColor': '#000000',
          'pointColor': '#FF5722',
          'lineColor': '#2196F3',
          'functionColor': '#4CAF50',
          'initialMode': 'point',
          'initialPoints': [
            {
              'x': 2.0,
              'y': 3.0,
              'label': 'A',
            },
            {
              'x': -4.0,
              'y': 1.0,
              'label': 'B',
            },
          ],
          'initialLines': [
            {
              'startX': 2.0,
              'startY': 3.0,
              'endX': -4.0,
              'endY': 1.0,
              'label': 'Line AB',
            },
          ],
          'initialFunctions': [
            {
              'expression': 'y = x^2',
              'color': '#4CAF50',
            },
          ],
          'showNameTag': true,
        },
        isImplemented: true,
      ),

      // Interactive System Solver
      InteractiveWidgetModel(
        id: 'interactive_system_solver_1',
        name: 'System of Equations Solver',
        type: 'interactive_system_solver',
        category: 'Math',
        description: 'An interactive tool for solving systems of linear equations using different methods.',
        data: {
          'title': 'System of Equations Solver',
          'description': 'Solve systems of linear equations using substitution, elimination, or graphical methods.',
          'primaryColor': '#2196F3',
          'secondaryColor': '#FF9800',
          'accentColor': '#4CAF50',
          'textColor': '#212121',
          'initialMethod': 'substitution',
          'initialEquations': [
            {
              'a': 1.0,
              'b': 1.0,
              'c': 10.0,
            },
            {
              'a': 2.0,
              'b': -1.0,
              'c': 5.0,
            },
          ],
          'showNameTag': true,
        },
        isImplemented: true,
      ),

      // Interactive Elimination Method Visualizer
      InteractiveWidgetModel(
        id: 'interactive_elimination_method_visualizer_1',
        name: 'Elimination Method Visualizer',
        type: 'interactive_elimination_method_visualizer',
        category: 'Math',
        description: 'An interactive tool for visualizing the elimination method for solving systems of linear equations.',
        data: {
          'title': 'Elimination Method Visualizer',
          'description': 'Visualize the step-by-step process of solving a system of linear equations using the elimination method.',
          'primaryColor': '#2196F3',
          'secondaryColor': '#FF9800',
          'accentColor': '#4CAF50',
          'textColor': '#212121',
          'highlightColor': '#FFEB3B',
          'initialEquations': [
            {
              'a': 2.0,
              'b': 3.0,
              'c': 8.0,
            },
            {
              'a': 5.0,
              'b': 1.0,
              'c': 14.0,
            },
          ],
          'showNameTag': true,
        },
        isImplemented: true,
      ),

      // Interactive Substitution Method Visualizer
      InteractiveWidgetModel(
        id: 'interactive_substitution_method_visualizer_1',
        name: 'Substitution Method Visualizer',
        type: 'interactive_substitution_method_visualizer',
        category: 'Math',
        description: 'An interactive tool for visualizing the substitution method for solving systems of linear equations.',
        data: {
          'title': 'Substitution Method Visualizer',
          'description': 'Visualize the step-by-step process of solving a system of linear equations using the substitution method.',
          'primaryColor': '#2196F3',
          'secondaryColor': '#FF9800',
          'accentColor': '#4CAF50',
          'textColor': '#212121',
          'highlightColor': '#FFEB3B',
          'initialEquations': [
            {
              'a': 3.0,
              'b': 2.0,
              'c': 7.0,
            },
            {
              'a': 1.0,
              'b': -1.0,
              'c': 0.0,
            },
          ],
          'showNameTag': true,
        },
        isImplemented: true,
      ),

      // Interactive Scientific Method Flowchart
      InteractiveWidgetModel(
        id: 'interactive_scientific_method_flowchart_1',
        name: 'Scientific Method Flowchart',
        type: 'interactive_scientific_method_flowchart',
        category: 'Science',
        description: 'An interactive flowchart demonstrating the steps of the scientific method.',
        data: {
          'title': 'The Scientific Method',
          'description': 'Explore the steps of the scientific method through this interactive flowchart.',
          'primaryColor': '#2196F3',
          'secondaryColor': '#FF9800',
          'accentColor': '#4CAF50',
          'backgroundColor': '#FFFFFF',
          'textColor': '#212121',
          'steps': [
            {
              'title': 'Ask a Question',
              'description': 'The scientific method begins with a question about something you observe.',
              'example': 'Example: Why do plants grow toward light?',
              'icon': 'help_outline',
            },
            {
              'title': 'Research',
              'description': 'Gather information and see what is already known about your question.',
              'example': 'Example: Read about phototropism and plant biology.',
              'icon': 'search',
            },
            {
              'title': 'Form a Hypothesis',
              'description': 'Propose an explanation that can be tested.',
              'example': 'Example: Plants grow toward light because it helps them photosynthesize more efficiently.',
              'icon': 'lightbulb_outline',
            },
            {
              'title': 'Test with an Experiment',
              'description': 'Design and perform an experiment to test your hypothesis.',
              'example': 'Example: Place plants in different lighting conditions and measure growth direction.',
              'icon': 'science',
            },
            {
              'title': 'Analyze Data',
              'description': 'Collect and analyze the data from your experiment.',
              'example': 'Example: Measure the angle of growth and compare between different light conditions.',
              'icon': 'assessment',
            },
            {
              'title': 'Draw Conclusions',
              'description': 'Determine if your hypothesis is supported or refuted by the data.',
              'example': 'Example: Plants consistently grew toward the light source, supporting the hypothesis.',
              'icon': 'fact_check',
            },
            {
              'title': 'Communicate Results',
              'description': 'Share your findings with others so they can be reviewed and verified.',
              'example': 'Example: Write a report or present your findings to peers.',
              'icon': 'share',
            },
            {
              'title': 'Refine and Repeat',
              'description': 'Based on feedback and new questions, refine your experiment or ask new questions.',
              'example': 'Example: Now investigate which wavelengths of light cause the strongest growth response.',
              'icon': 'refresh',
            },
          ],
          'showNameTag': true,
        },
        isImplemented: true,
      ),

      // Interactive Hypothesis Builder
      InteractiveWidgetModel(
        id: 'interactive_hypothesis_builder_1',
        name: 'Hypothesis Builder',
        type: 'interactive_hypothesis_builder',
        category: 'Science',
        description: 'An interactive tool for building and testing scientific hypotheses.',
        data: {
          'title': 'Hypothesis Builder',
          'description': 'Create a testable scientific hypothesis by following the steps.',
          'primaryColor': '#2196F3',
          'secondaryColor': '#FF9800',
          'accentColor': '#4CAF50',
          'backgroundColor': '#FFFFFF',
          'textColor': '#212121',
          'ifPhrases': [
            'If plants receive more sunlight',
            'If temperature increases',
            'If students study for longer periods',
            'If water is heated to 100°C',
            'If exercise frequency increases',
          ],
          'thenPhrases': [
            'then they will grow taller',
            'then ice will melt faster',
            'then test scores will improve',
            'then it will boil',
            'then weight loss will occur',
          ],
          'becausePhrases': [
            'because photosynthesis increases',
            'because molecular motion increases',
            'because more information is retained',
            'because water molecules gain enough energy to change state',
            'because more calories are burned',
          ],
          'variables': [
            'Amount of sunlight',
            'Temperature',
            'Study time',
            'Water temperature',
            'Exercise frequency',
          ],
          'controlledVariables': [
            'Type of plant',
            'Type of ice',
            'Study material',
            'Atmospheric pressure',
            'Diet',
          ],
          'showNameTag': true,
        },
        isImplemented: true,
      ),

      // Interactive Experimental Design Tool
      InteractiveWidgetModel(
        id: 'interactive_experimental_design_tool_1',
        name: 'Experimental Design Tool',
        type: 'interactive_experimental_design_tool',
        category: 'Science',
        description: 'An interactive tool for designing and evaluating scientific experiments.',
        data: {
          'title': 'Experimental Design Tool',
          'description': 'Design a scientific experiment by following the steps.',
          'primaryColor': '#2196F3',
          'secondaryColor': '#FF9800',
          'accentColor': '#4CAF50',
          'backgroundColor': '#FFFFFF',
          'textColor': '#212121',
          'researchQuestions': [
            'How does the amount of sunlight affect plant growth?',
            'Does temperature affect the rate of a chemical reaction?',
            'How does study time affect test scores?',
            'Does exercise frequency affect weight loss?',
            'How does water temperature affect the rate of dissolution?',
          ],
          'independentVariables': [
            'Amount of sunlight',
            'Temperature',
            'Study time',
            'Exercise frequency',
            'Water temperature',
          ],
          'dependentVariables': [
            'Plant height',
            'Reaction rate',
            'Test scores',
            'Weight loss',
            'Dissolution rate',
          ],
          'controlledVariables': [
            'Type of plant',
            'Type of reactants',
            'Study material',
            'Diet',
            'Type of solute',
            'Amount of water',
            'Soil type',
            'Container size',
            'Room humidity',
          ],
          'experimentalGroups': [
            'Plants receiving 12 hours of sunlight',
            'Reaction at 50°C',
            'Students studying for 3 hours',
            'Exercise 5 times per week',
            'Water at 80°C',
          ],
          'controlGroups': [
            'Plants receiving 6 hours of sunlight',
            'Reaction at 25°C',
            'Students studying for 1 hour',
            'Exercise 2 times per week',
            'Water at 20°C',
          ],
          'dataCollectionMethods': [
            'Measure plant height with a ruler',
            'Time the reaction with a stopwatch',
            'Record test scores',
            'Weigh participants on a scale',
            'Measure the amount of dissolved solute',
          ],
          'showNameTag': true,
        },
        isImplemented: true,
      ),

      // Interactive Variable Identifier
      InteractiveWidgetModel(
        id: 'interactive_variable_identifier_1',
        name: 'Variable Identifier',
        type: 'interactive_variable_identifier',
        category: 'Science',
        description: 'An interactive tool for identifying and classifying variables in scientific experiments.',
        data: {
          'title': 'Variable Identifier',
          'description': 'Learn to identify independent, dependent, and controlled variables in scientific experiments.',
          'primaryColor': '#2196F3',
          'secondaryColor': '#FF9800',
          'accentColor': '#4CAF50',
          'backgroundColor': '#FFFFFF',
          'textColor': '#212121',
          'scenarios': [
            {
              'title': 'Plant Growth Experiment',
              'description': 'A scientist wants to determine how the amount of sunlight affects plant growth. They set up an experiment with identical plants, soil, water, and temperature, but vary the hours of sunlight each plant receives. They measure the height of each plant after 2 weeks.',
              'variables': [
                'Amount of sunlight',
                'Plant height',
                'Type of plant',
                'Amount of water',
                'Soil composition',
                'Temperature',
                'Container size',
              ],
              'independentVariable': 'Amount of sunlight',
              'dependentVariable': 'Plant height',
              'controlledVariables': [
                'Type of plant',
                'Amount of water',
                'Soil composition',
                'Temperature',
                'Container size',
              ],
              'explanation': 'In this experiment, the amount of sunlight is the independent variable because it is what the scientist is deliberately changing. The plant height is the dependent variable because it is what is being measured to see how it responds to changes in sunlight. The other factors (type of plant, amount of water, soil composition, temperature, and container size) are controlled variables because they are kept constant to ensure that any changes in plant height are due to changes in sunlight, not other factors.',
            },
            {
              'title': 'Exercise and Heart Rate',
              'description': 'A researcher is investigating how exercise intensity affects heart rate. Participants exercise at different intensities (walking, jogging, running) for the same amount of time. The researcher measures each participant\'s heart rate immediately after exercise.',
              'variables': [
                'Exercise intensity',
                'Heart rate',
                'Duration of exercise',
                'Age of participants',
                'Fitness level of participants',
                'Time of day',
                'Room temperature',
              ],
              'independentVariable': 'Exercise intensity',
              'dependentVariable': 'Heart rate',
              'controlledVariables': [
                'Duration of exercise',
                'Age of participants',
                'Fitness level of participants',
                'Time of day',
                'Room temperature',
              ],
              'explanation': 'In this experiment, exercise intensity is the independent variable because it is what the researcher is deliberately changing. Heart rate is the dependent variable because it is what is being measured to see how it responds to changes in exercise intensity. The other factors (duration of exercise, age and fitness level of participants, time of day, and room temperature) are controlled variables because they are kept constant to ensure that any changes in heart rate are due to changes in exercise intensity, not other factors.',
            },
            {
              'title': 'Study Time and Test Scores',
              'description': 'An educator wants to determine if the amount of time spent studying affects test scores. Students are assigned different study times (30 minutes, 60 minutes, 90 minutes) and then take the same test. The educator records each student\'s test score.',
              'variables': [
                'Study time',
                'Test score',
                'Difficulty of test',
                'Prior knowledge of subject',
                'Time of day for studying',
                'Study environment',
                'Student\'s age',
              ],
              'independentVariable': 'Study time',
              'dependentVariable': 'Test score',
              'controlledVariables': [
                'Difficulty of test',
                'Prior knowledge of subject',
                'Time of day for studying',
                'Study environment',
                'Student\'s age',
              ],
              'explanation': 'In this experiment, study time is the independent variable because it is what the educator is deliberately changing. Test score is the dependent variable because it is what is being measured to see how it responds to changes in study time. The other factors (difficulty of test, prior knowledge, time of day, study environment, and student\'s age) are controlled variables because they are kept constant to ensure that any changes in test scores are due to changes in study time, not other factors.',
            },
          ],
          'showNameTag': true,
        },
        isImplemented: true,
      ),

      // Interactive Data Visualization Tool
      InteractiveWidgetModel(
        id: 'interactive_data_visualization_tool_1',
        name: 'Data Visualization Tool',
        type: 'interactive_data_visualization_tool',
        category: 'Science',
        description: 'An interactive tool for visualizing and exploring data through different chart types.',
        data: {
          'title': 'Data Visualization Tool',
          'description': 'Explore data through different visualization methods and analyze patterns.',
          'primaryColor': '#2196F3',
          'secondaryColor': '#FF9800',
          'accentColor': '#4CAF50',
          'backgroundColor': '#FFFFFF',
          'textColor': '#212121',
          'dataSets': [
            {
              'title': 'Monthly Temperature Data',
              'description': 'Average monthly temperatures for a city over a year.',
              'xAxisLabel': 'Month',
              'yAxisLabel': 'Temperature (°C)',
              'dataPoints': [
                {'x': 1, 'y': 5, 'label': 'Jan'},
                {'x': 2, 'y': 7, 'label': 'Feb'},
                {'x': 3, 'y': 10, 'label': 'Mar'},
                {'x': 4, 'y': 14, 'label': 'Apr'},
                {'x': 5, 'y': 18, 'label': 'May'},
                {'x': 6, 'y': 22, 'label': 'Jun'},
                {'x': 7, 'y': 25, 'label': 'Jul'},
                {'x': 8, 'y': 24, 'label': 'Aug'},
                {'x': 9, 'y': 20, 'label': 'Sep'},
                {'x': 10, 'y': 15, 'label': 'Oct'},
                {'x': 11, 'y': 10, 'label': 'Nov'},
                {'x': 12, 'y': 6, 'label': 'Dec'},
              ],
              'explanation': 'This chart shows the seasonal temperature pattern with higher temperatures in summer months (June-August) and lower temperatures in winter months (December-February). The data follows a sinusoidal pattern typical of annual temperature cycles in temperate regions.',
            },
            {
              'title': 'Student Test Scores',
              'description': 'Distribution of test scores for a class of 30 students.',
              'xAxisLabel': 'Score Range',
              'yAxisLabel': 'Number of Students',
              'dataPoints': [
                {'x': 1, 'y': 2, 'label': '0-10'},
                {'x': 2, 'y': 3, 'label': '11-20'},
                {'x': 3, 'y': 4, 'label': '21-30'},
                {'x': 4, 'y': 5, 'label': '31-40'},
                {'x': 5, 'y': 7, 'label': '41-50'},
                {'x': 6, 'y': 4, 'label': '51-60'},
                {'x': 7, 'y': 3, 'label': '61-70'},
                {'x': 8, 'y': 1, 'label': '71-80'},
                {'x': 9, 'y': 1, 'label': '81-90'},
                {'x': 10, 'y': 0, 'label': '91-100'},
              ],
              'explanation': 'This chart shows a normal distribution of test scores with most students scoring in the middle range (41-50) and fewer students at the extreme ends. This is a typical pattern for test scores and follows a bell curve distribution.',
            },
            {
              'title': 'Plant Growth Experiment',
              'description': 'Plant height (cm) measured over 8 weeks with different amounts of sunlight.',
              'xAxisLabel': 'Week',
              'yAxisLabel': 'Height (cm)',
              'dataPoints': [
                {'x': 1, 'y': 2, 'label': 'Week 1'},
                {'x': 2, 'y': 5, 'label': 'Week 2'},
                {'x': 3, 'y': 9, 'label': 'Week 3'},
                {'x': 4, 'y': 14, 'label': 'Week 4'},
                {'x': 5, 'y': 18, 'label': 'Week 5'},
                {'x': 6, 'y': 21, 'label': 'Week 6'},
                {'x': 7, 'y': 23, 'label': 'Week 7'},
                {'x': 8, 'y': 24, 'label': 'Week 8'},
              ],
              'explanation': 'This chart shows an exponential growth pattern initially (weeks 1-4) followed by a plateau (weeks 6-8). This is typical of plant growth where rapid growth occurs in the early stages and then slows down as the plant matures.',
            },
            {
              'title': 'Energy Sources',
              'description': 'Distribution of energy sources for electricity generation.',
              'xAxisLabel': 'Energy Source',
              'yAxisLabel': 'Percentage (%)',
              'dataPoints': [
                {'x': 1, 'y': 38, 'label': 'Coal'},
                {'x': 2, 'y': 23, 'label': 'Natural Gas'},
                {'x': 3, 'y': 20, 'label': 'Nuclear'},
                {'x': 4, 'y': 7, 'label': 'Hydro'},
                {'x': 5, 'y': 6, 'label': 'Wind'},
                {'x': 6, 'y': 3, 'label': 'Solar'},
                {'x': 7, 'y': 3, 'label': 'Biomass'},
              ],
              'explanation': 'This chart shows that fossil fuels (coal and natural gas) still dominate electricity generation, accounting for 61% of the total. Renewable sources (hydro, wind, solar, biomass) together make up 19%, while nuclear provides 20%. This visualization helps understand the current energy mix and the potential for transitioning to more renewable sources.',
            },
          ],
          'showNameTag': true,
        },
        isImplemented: true,
      ),

      // Interactive Statistical Analysis Calculator
      InteractiveWidgetModel(
        id: 'interactive_statistical_analysis_calculator_1',
        name: 'Statistical Analysis Calculator',
        type: 'interactive_statistical_analysis_calculator',
        category: 'Science',
        description: 'An interactive tool for performing statistical analysis on data sets.',
        data: {
          'title': 'Statistical Analysis Calculator',
          'description': 'Perform statistical analysis on various data sets and interpret the results.',
          'primaryColor': '#2196F3',
          'secondaryColor': '#FF9800',
          'accentColor': '#4CAF50',
          'backgroundColor': '#FFFFFF',
          'textColor': '#212121',
          'dataSets': [
            {
              'title': 'Student Test Scores',
              'description': 'Test scores from a class of 30 students.',
              'values': [65, 70, 75, 80, 85, 90, 95, 60, 65, 70, 75, 80, 85, 90, 75, 80, 85, 90, 95, 100, 55, 60, 65, 70, 75, 80, 85, 90, 95, 100],
              'explanation': 'This data set represents test scores from a class of 30 students. The scores range from 55 to 100, with a mean of approximately 80 and a standard deviation of about 12. The distribution is slightly negatively skewed, indicating that more students scored above the mean than below it.',
            },
            {
              'title': 'Heights (cm)',
              'description': 'Heights of 20 adults in centimeters.',
              'values': [165, 170, 175, 180, 185, 190, 160, 165, 170, 175, 180, 185, 175, 180, 185, 190, 165, 170, 175, 180],
              'explanation': 'This data set represents the heights of 20 adults in centimeters. The heights range from 160 cm to 190 cm, with a mean of approximately 175 cm and a standard deviation of about 8 cm. The distribution is approximately normal, which is typical for human height data.',
            },
            {
              'title': 'Monthly Expenses (USD)',
              'description': 'Monthly expenses in dollars for 15 households.',
              'values': [1200, 1500, 1800, 2100, 2400, 2700, 3000, 1350, 1650, 1950, 2250, 2550, 2850, 3150, 3450],
              'explanation': 'This data set represents monthly expenses in dollars for 15 households. The expenses range from 1,200 to 3,450 USD, with a mean of approximately 2,200 USD and a standard deviation of about 700 USD. The distribution is approximately uniform, indicating that the households have a wide range of spending habits.',
            },
            {
              'title': 'Reaction Times (ms)',
              'description': 'Reaction times in milliseconds for 25 participants.',
              'values': [220, 240, 260, 280, 300, 320, 340, 230, 250, 270, 290, 310, 330, 350, 225, 245, 265, 285, 305, 325, 345, 235, 255, 275, 295],
              'explanation': 'This data set represents reaction times in milliseconds for 25 participants. The reaction times range from 220 ms to 350 ms, with a mean of approximately 285 ms and a standard deviation of about 40 ms. The distribution is approximately normal, which is typical for reaction time data.',
            },
          ],
          'showNameTag': true,
        },
        isImplemented: true,
      ),

      // Interactive Measurement Error Simulator
      InteractiveWidgetModel(
        id: 'interactive_measurement_error_simulator_1',
        name: 'Measurement Error Simulator',
        type: 'interactive_measurement_error_simulator',
        category: 'Science',
        description: 'An interactive tool for simulating measurement errors and their effects on data collection.',
        data: {
          'title': 'Measurement Error Simulator',
          'description': 'Explore how random and systematic errors affect measurements and learn how to minimize them.',
          'primaryColor': '#2196F3',
          'secondaryColor': '#FF9800',
          'accentColor': '#4CAF50',
          'backgroundColor': '#FFFFFF',
          'textColor': '#212121',
          'scenarios': [
            {
              'title': 'Length Measurement',
              'description': 'Measure the length of a metal rod using a ruler.',
              'trueValue': 25.0,
              'defaultRandomError': 0.2,
              'defaultSystematicError': 0.0,
              'defaultMeasurements': 10,
              'randomErrorDescription': 'Random errors in length measurement can occur due to slight variations in how you align the ruler, read the scale, or position the object.',
              'systematicErrorDescription': 'Systematic errors in length measurement can occur if the ruler is damaged, poorly calibrated, or if there is a consistent error in how you use it (e.g., always starting from the 1cm mark instead of 0).',
              'explanation': 'Length measurements are subject to both random and systematic errors. Random errors cause measurements to scatter around the true value, while systematic errors cause a consistent offset. Increasing the number of measurements helps reduce the effect of random errors but does not affect systematic errors.',
              'unit': 'cm',
            },
            {
              'title': 'Temperature Measurement',
              'description': 'Measure the temperature of a water bath using a thermometer.',
              'trueValue': 37.0,
              'defaultRandomError': 0.3,
              'defaultSystematicError': 0.0,
              'defaultMeasurements': 10,
              'randomErrorDescription': 'Random errors in temperature measurement can occur due to fluctuations in the water bath, reading the thermometer at different angles, or momentary changes in the environment.',
              'systematicErrorDescription': 'Systematic errors in temperature measurement can occur if the thermometer is poorly calibrated, if there is a consistent heat source nearby, or if the thermometer is consistently read incorrectly.',
              'explanation': 'Temperature measurements are particularly sensitive to environmental factors. Random errors can be reduced by taking multiple measurements and averaging them. Systematic errors require recalibration of the instrument or adjustment of the measurement technique.',
              'unit': '°C',
            },
            {
              'title': 'Mass Measurement',
              'description': 'Measure the mass of a small object using a digital scale.',
              'trueValue': 15.0,
              'defaultRandomError': 0.1,
              'defaultSystematicError': 0.0,
              'defaultMeasurements': 10,
              'randomErrorDescription': 'Random errors in mass measurement can occur due to air currents, vibrations, or slight variations in how the object is placed on the scale.',
              'systematicErrorDescription': 'Systematic errors in mass measurement can occur if the scale is not properly calibrated, if there is a consistent error in taring (zeroing) the scale, or if there is a consistent external force affecting the measurements.',
              'explanation': 'Mass measurements with digital scales are generally precise but can still be affected by environmental factors. Random errors can be minimized by ensuring stable conditions and taking multiple measurements. Systematic errors require proper calibration of the scale.',
              'unit': 'g',
            },
            {
              'title': 'Time Measurement',
              'description': 'Measure the time it takes for a pendulum to complete 10 oscillations using a stopwatch.',
              'trueValue': 20.0,
              'defaultRandomError': 0.4,
              'defaultSystematicError': 0.0,
              'defaultMeasurements': 10,
              'randomErrorDescription': 'Random errors in time measurement can occur due to human reaction time variations when starting and stopping the stopwatch, or slight variations in identifying the exact moment of an oscillation.',
              'systematicErrorDescription': 'Systematic errors in time measurement can occur if there is a consistent delay in starting or stopping the stopwatch, or if the stopwatch itself runs consistently fast or slow.',
              'explanation': 'Time measurements involving human reaction time are particularly prone to random errors. These can be reduced by taking multiple measurements or using automated timing systems. Systematic errors in timing can be identified by calibrating against a known standard.',
              'unit': 's',
            },
          ],
          'showNameTag': true,
        },
        isImplemented: true,
      ),

      // Interactive Graph Interpretation Exercise
      InteractiveWidgetModel(
        id: 'interactive_graph_interpretation_exercise_1',
        name: 'Graph Interpretation Exercise',
        type: 'interactive_graph_interpretation_exercise',
        category: 'Science',
        description: 'An interactive exercise for interpreting scientific graphs and data visualizations.',
        data: {
          'title': 'Graph Interpretation Exercise',
          'description': 'Analyze scientific graphs and answer questions about trends, relationships, and conclusions.',
          'primaryColor': '#2196F3',
          'secondaryColor': '#FF9800',
          'accentColor': '#4CAF50',
          'backgroundColor': '#FFFFFF',
          'textColor': '#212121',
          'exercises': [
            {
              'title': 'Population Growth Over Time',
              'description': 'The graph shows the population of a bacterial culture over time.',
              'graphType': 'line',
              'xAxisLabel': 'Time (hours)',
              'yAxisLabel': 'Population (thousands)',
              'dataPoints': [
                {'x': 0, 'y': 10, 'label': '0h'},
                {'x': 1, 'y': 15, 'label': '1h'},
                {'x': 2, 'y': 22, 'label': '2h'},
                {'x': 3, 'y': 33, 'label': '3h'},
                {'x': 4, 'y': 50, 'label': '4h'},
                {'x': 5, 'y': 74, 'label': '5h'},
                {'x': 6, 'y': 111, 'label': '6h'},
                {'x': 7, 'y': 166, 'label': '7h'},
                {'x': 8, 'y': 220, 'label': '8h'},
                {'x': 9, 'y': 240, 'label': '9h'},
                {'x': 10, 'y': 245, 'label': '10h'},
              ],
              'questions': [
                {
                  'text': 'What type of growth pattern does this graph show?',
                  'options': [
                    'Linear growth',
                    'Exponential growth followed by a plateau',
                    'Logarithmic growth',
                    'Constant growth rate',
                  ],
                  'correctAnswerIndex': 1,
                  'explanation': 'The graph shows exponential growth (hours 0-7) followed by a plateau (hours 8-10). This is typical of bacterial growth in a limited environment, where resources eventually become scarce.',
                },
                {
                  'text': 'During which time period was the growth rate highest?',
                  'options': [
                    'Between 0-2 hours',
                    'Between 3-5 hours',
                    'Between 6-8 hours',
                    'Between 8-10 hours',
                  ],
                  'correctAnswerIndex': 2,
                  'explanation': 'The growth rate (slope of the curve) is steepest between 6-8 hours, indicating the highest rate of population increase during this period.',
                },
                {
                  'text': 'What might explain the plateau after 8 hours?',
                  'options': [
                    'The bacteria died',
                    'The experiment ended',
                    'The bacteria reached carrying capacity due to limited resources',
                    'The temperature decreased',
                  ],
                  'correctAnswerIndex': 2,
                  'explanation': 'The plateau indicates that the bacterial population reached its carrying capacity, which occurs when limited resources (nutrients, space, etc.) prevent further population growth.',
                },
              ],
              'explanation': 'This graph illustrates a typical bacterial growth curve with distinct phases: lag phase (minimal growth at the beginning), exponential phase (rapid growth in the middle), and stationary phase (plateau at the end). Understanding these phases is crucial for interpreting microbial growth in various environments.',
            },
            {
              'title': 'Temperature vs. Reaction Rate',
              'description': 'The graph shows the relationship between temperature and the rate of a chemical reaction.',
              'graphType': 'line',
              'xAxisLabel': 'Temperature (°C)',
              'yAxisLabel': 'Reaction Rate (mol/L·s)',
              'dataPoints': [
                {'x': 0, 'y': 0.1, 'label': '0°C'},
                {'x': 10, 'y': 0.2, 'label': '10°C'},
                {'x': 20, 'y': 0.4, 'label': '20°C'},
                {'x': 30, 'y': 0.8, 'label': '30°C'},
                {'x': 40, 'y': 1.6, 'label': '40°C'},
                {'x': 50, 'y': 3.2, 'label': '50°C'},
                {'x': 60, 'y': 6.4, 'label': '60°C'},
                {'x': 70, 'y': 12.8, 'label': '70°C'},
                {'x': 80, 'y': 25.6, 'label': '80°C'},
                {'x': 90, 'y': 30.0, 'label': '90°C'},
                {'x': 100, 'y': 15.0, 'label': '100°C'},
              ],
              'questions': [
                {
                  'text': 'How does temperature generally affect the reaction rate up to 90°C?',
                  'options': [
                    'Temperature has no effect on reaction rate',
                    'Reaction rate decreases as temperature increases',
                    'Reaction rate increases linearly with temperature',
                    'Reaction rate increases exponentially with temperature',
                  ],
                  'correctAnswerIndex': 3,
                  'explanation': 'The graph shows an exponential increase in reaction rate as temperature increases up to 90°C. This follows the Arrhenius equation, which describes how reaction rates typically increase exponentially with temperature.',
                },
                {
                  'text': 'What happens to the reaction rate above 90°C?',
                  'options': [
                    'It continues to increase',
                    'It plateaus',
                    'It decreases',
                    'It remains constant',
                  ],
                  'correctAnswerIndex': 2,
                  'explanation': 'The reaction rate decreases above 90°C, as shown by the downward slope of the curve between 90°C and 100°C.',
                },
                {
                  'text': 'What might explain the decrease in reaction rate above 90°C?',
                  'options': [
                    'The reactants are being used up',
                    'The enzyme or catalyst is being denatured by high temperature',
                    'The reaction is reaching equilibrium',
                    'The measurement equipment is malfunctioning',
                  ],
                  'correctAnswerIndex': 1,
                  'explanation': 'The decrease in reaction rate above 90°C is likely due to the denaturation of an enzyme or degradation of a catalyst at high temperatures. This is common in biological reactions or catalyzed chemical reactions.',
                },
              ],
              'explanation': 'This graph demonstrates the relationship between temperature and reaction rate, showing both the expected exponential increase (following the Arrhenius equation) and the effects of high temperatures on biological catalysts or enzymes. The optimal temperature for this reaction appears to be around 90°C, after which the rate decreases due to catalyst degradation.',
            },
            {
              'title': 'Plant Growth Under Different Light Conditions',
              'description': 'The graph shows the growth of plants under different light intensities over a 4-week period.',
              'graphType': 'bar',
              'xAxisLabel': 'Light Intensity (lux)',
              'yAxisLabel': 'Plant Height (cm)',
              'dataPoints': [
                {'x': 1, 'y': 5, 'label': '1,000 lux'},
                {'x': 2, 'y': 12, 'label': '5,000 lux'},
                {'x': 3, 'y': 18, 'label': '10,000 lux'},
                {'x': 4, 'y': 22, 'label': '15,000 lux'},
                {'x': 5, 'y': 20, 'label': '20,000 lux'},
                {'x': 6, 'y': 15, 'label': '25,000 lux'},
              ],
              'questions': [
                {
                  'text': 'At which light intensity did plants grow tallest?',
                  'options': [
                    '5,000 lux',
                    '10,000 lux',
                    '15,000 lux',
                    '20,000 lux',
                  ],
                  'correctAnswerIndex': 2,
                  'explanation': 'The plants grew tallest (22 cm) at 15,000 lux, as shown by the highest bar on the graph.',
                },
                {
                  'text': 'What happens to plant growth at very high light intensities (above 15,000 lux)?',
                  'options': [
                    'Growth continues to increase',
                    'Growth remains constant',
                    'Growth decreases',
                    'Growth stops completely',
                  ],
                  'correctAnswerIndex': 2,
                  'explanation': 'Plant growth decreases at light intensities above 15,000 lux, as shown by the shorter bars for 20,000 and 25,000 lux.',
                },
                {
                  'text': 'Based on this graph, what conclusion can you draw about the relationship between light intensity and plant growth?',
                  'options': [
                    'More light always leads to better growth',
                    'Light has no significant effect on plant growth',
                    'Plants grow best at moderate light levels, with too little or too much light inhibiting growth',
                    'Plants only need minimal light to achieve maximum growth',
                  ],
                  'correctAnswerIndex': 2,
                  'explanation': 'The graph shows that plants grow best at moderate light levels (around 15,000 lux), with both too little light (1,000 lux) and too much light (25,000 lux) resulting in reduced growth. This demonstrates that there is an optimal range of light intensity for plant growth.',
                },
              ],
              'explanation': 'This graph illustrates the concept of optimal environmental conditions for plant growth. While light is essential for photosynthesis, excessive light can cause photoinhibition or heat stress, reducing growth. Similarly, insufficient light limits photosynthesis. Understanding these relationships is important for optimizing plant growth in agricultural and ecological contexts.',
            },
            {
              'title': 'Correlation Between Study Time and Test Scores',
              'description': 'The scatter plot shows the relationship between hours spent studying and test scores for 20 students.',
              'graphType': 'scatter',
              'xAxisLabel': 'Study Time (hours)',
              'yAxisLabel': 'Test Score (%)',
              'dataPoints': [
                {'x': 1, 'y': 65, 'label': 'Student 1'},
                {'x': 2, 'y': 70, 'label': 'Student 2'},
                {'x': 1.5, 'y': 68, 'label': 'Student 3'},
                {'x': 3, 'y': 75, 'label': 'Student 4'},
                {'x': 4, 'y': 80, 'label': 'Student 5'},
                {'x': 2.5, 'y': 72, 'label': 'Student 6'},
                {'x': 5, 'y': 85, 'label': 'Student 7'},
                {'x': 6, 'y': 88, 'label': 'Student 8'},
                {'x': 4.5, 'y': 82, 'label': 'Student 9'},
                {'x': 7, 'y': 90, 'label': 'Student 10'},
                {'x': 8, 'y': 92, 'label': 'Student 11'},
                {'x': 3.5, 'y': 76, 'label': 'Student 12'},
                {'x': 5.5, 'y': 86, 'label': 'Student 13'},
                {'x': 6.5, 'y': 89, 'label': 'Student 14'},
                {'x': 7.5, 'y': 91, 'label': 'Student 15'},
                {'x': 2, 'y': 65, 'label': 'Student 16'},
                {'x': 4, 'y': 75, 'label': 'Student 17'},
                {'x': 6, 'y': 82, 'label': 'Student 18'},
                {'x': 8, 'y': 88, 'label': 'Student 19'},
                {'x': 9, 'y': 95, 'label': 'Student 20'},
              ],
              'questions': [
                {
                  'text': 'What type of correlation does this scatter plot show?',
                  'options': [
                    'No correlation',
                    'Weak negative correlation',
                    'Strong positive correlation',
                    'Perfect correlation',
                  ],
                  'correctAnswerIndex': 2,
                  'explanation': 'The scatter plot shows a strong positive correlation between study time and test scores. As study time increases, test scores tend to increase as well, with points generally following an upward trend.',
                },
                {
                  'text': 'Based on this data, what would you predict for a student who studies for 5 hours?',
                  'options': [
                    'A score below 70%',
                    'A score between 70-80%',
                    'A score between 80-90%',
                    'A score above 90%',
                  ],
                  'correctAnswerIndex': 2,
                  'explanation': 'Based on the trend in the data, a student who studies for 5 hours would likely score between 80-90%. Looking at the data points around x=5, we see scores of 85% and 82%.',
                },
                {
                  'text': 'Which statement best describes the relationship shown in this graph?',
                  'options': [
                    'Study time causes test scores to increase',
                    'There is a positive association between study time and test scores',
                    'Studying guarantees high test scores',
                    'Test scores determine how long students study',
                  ],
                  'correctAnswerIndex': 1,
                  'explanation': 'The graph shows a positive association between study time and test scores, meaning that higher study times tend to be associated with higher test scores. However, correlation does not necessarily imply causation, and other factors may also influence test scores.',
                },
              ],
              'explanation': 'This scatter plot demonstrates correlation between two variables. While there is a clear positive association between study time and test scores, it\'s important to note that correlation does not necessarily imply causation. Other factors not shown in the graph (prior knowledge, study efficiency, etc.) may also influence test scores. Understanding correlational data is crucial for interpreting scientific research and avoiding unwarranted causal conclusions.',
            },
          ],
          'showNameTag': true,
        },
        isImplemented: true,
      ),

      // Interactive Prediction Generator
      InteractiveWidgetModel(
        id: 'interactive_prediction_generator_1',
        name: 'Scientific Prediction Generator',
        type: 'interactive_prediction_generator',
        category: 'Science',
        description: 'An interactive tool for generating predictions based on scientific theories and variables.',
        data: {
          'title': 'Scientific Prediction Generator',
          'description': 'Generate predictions based on scientific theories by adjusting variables.',
          'primaryColor': '#673AB7',
          'secondaryColor': '#FFC107',
          'accentColor': '#00BCD4',
          'backgroundColor': '#FFFFFF',
          'textColor': '#212121',
          'showNameTag': true,
          'theories': [
            {
              'id': 'ideal_gas_law',
              'name': 'Ideal Gas Law',
              'description': 'The ideal gas law is the equation of state of a hypothetical ideal gas. It relates pressure, volume, temperature, and the amount of gas.',
              'explanation': 'The ideal gas law (PV = nRT) predicts how gases will respond to changes in pressure, volume, temperature, or quantity. When temperature increases, gas molecules move faster and collide more forcefully with container walls, increasing pressure if volume is constant. When volume decreases, molecules are confined to a smaller space, increasing collision frequency and thus pressure if temperature is constant.',
              'correctFeedback': 'Excellent! Your predictions correctly follow from the ideal gas law. As pressure is inversely proportional to volume (at constant temperature), and directly proportional to temperature (at constant volume).',
              'incorrectFeedback': 'Some of your predictions don\'t align with the ideal gas law. Remember that pressure is inversely proportional to volume (at constant temperature), and directly proportional to temperature (at constant volume).',
              'variables': [
                {
                  'id': 'temperature',
                  'name': 'Temperature',
                  'description': 'The temperature of the gas',
                  'unit': 'K',
                  'minValue': 200.0,
                  'maxValue': 500.0,
                  'defaultValue': 300.0
                },
                {
                  'id': 'volume',
                  'name': 'Volume',
                  'description': 'The volume of the container',
                  'unit': 'L',
                  'minValue': 1.0,
                  'maxValue': 10.0,
                  'defaultValue': 5.0
                },
                {
                  'id': 'moles',
                  'name': 'Amount of Gas',
                  'description': 'The number of moles of gas',
                  'unit': 'mol',
                  'minValue': 0.5,
                  'maxValue': 5.0,
                  'defaultValue': 1.0
                }
              ],
              'predictions': [
                {
                  'id': 'high_pressure',
                  'text': 'The pressure will be high (above 2 atm)',
                  'conditions': [
                    {
                      'variableId': 'temperature',
                      'operator': '>',
                      'value': 400.0
                    },
                    {
                      'variableId': 'volume',
                      'operator': '<',
                      'value': 3.0
                    }
                  ]
                },
                {
                  'id': 'low_pressure',
                  'text': 'The pressure will be low (below 0.5 atm)',
                  'conditions': [
                    {
                      'variableId': 'temperature',
                      'operator': '<',
                      'value': 250.0
                    },
                    {
                      'variableId': 'volume',
                      'operator': '>',
                      'value': 8.0
                    }
                  ]
                },
                {
                  'id': 'moderate_pressure',
                  'text': 'The pressure will be moderate (between 0.5 and 2 atm)',
                  'conditions': [
                    {
                      'variableId': 'temperature',
                      'operator': '>=',
                      'value': 250.0
                    },
                    {
                      'variableId': 'temperature',
                      'operator': '<=',
                      'value': 400.0
                    },
                    {
                      'variableId': 'volume',
                      'operator': '>=',
                      'value': 3.0
                    },
                    {
                      'variableId': 'volume',
                      'operator': '<=',
                      'value': 8.0
                    }
                  ]
                },
                {
                  'id': 'increasing_pressure',
                  'text': 'The pressure will increase if the temperature is raised further',
                  'conditions': [
                    {
                      'variableId': 'volume',
                      'operator': '<',
                      'value': 10.0
                    }
                  ]
                },
                {
                  'id': 'decreasing_pressure',
                  'text': 'The pressure will decrease if the volume is increased further',
                  'conditions': [
                    {
                      'variableId': 'volume',
                      'operator': '<',
                      'value': 10.0
                    }
                  ]
                }
              ]
            },
            {
              'id': 'pendulum_motion',
              'name': 'Pendulum Motion',
              'description': 'A pendulum is a weight suspended from a pivot so that it can swing freely. The motion of a pendulum is governed by the laws of physics.',
              'explanation': 'The period of a pendulum depends primarily on its length and the gravitational acceleration. For small oscillations, the period is approximately T = 2π√(L/g), where L is the length and g is the gravitational acceleration. The period is independent of the mass of the pendulum and its amplitude (for small angles). This is why pendulums were historically used in clocks.',
              'correctFeedback': 'Excellent! Your predictions correctly follow from the physics of pendulum motion. The period of a pendulum depends primarily on its length, not its mass or amplitude (for small angles).',
              'incorrectFeedback': 'Some of your predictions don\'t align with the physics of pendulum motion. Remember that the period of a pendulum depends primarily on its length, not its mass or amplitude (for small angles).',
              'variables': [
                {
                  'id': 'length',
                  'name': 'Length',
                  'description': 'The length of the pendulum',
                  'unit': 'm',
                  'minValue': 0.1,
                  'maxValue': 2.0,
                  'defaultValue': 1.0
                },
                {
                  'id': 'mass',
                  'name': 'Mass',
                  'description': 'The mass of the pendulum bob',
                  'unit': 'kg',
                  'minValue': 0.1,
                  'maxValue': 2.0,
                  'defaultValue': 0.5
                },
                {
                  'id': 'angle',
                  'name': 'Initial Angle',
                  'description': 'The initial angle of displacement',
                  'unit': '°',
                  'minValue': 5.0,
                  'maxValue': 45.0,
                  'defaultValue': 15.0
                }
              ],
              'predictions': [
                {
                  'id': 'long_period',
                  'text': 'The pendulum will have a long period (above 2 seconds)',
                  'conditions': [
                    {
                      'variableId': 'length',
                      'operator': '>',
                      'value': 1.0
                    }
                  ]
                },
                {
                  'id': 'short_period',
                  'text': 'The pendulum will have a short period (below 1 second)',
                  'conditions': [
                    {
                      'variableId': 'length',
                      'operator': '<',
                      'value': 0.25
                    }
                  ]
                },
                {
                  'id': 'mass_effect',
                  'text': 'Changing the mass will significantly affect the period',
                  'conditions': []
                },
                {
                  'id': 'angle_effect_small',
                  'text': 'The period will be approximately independent of the initial angle',
                  'conditions': [
                    {
                      'variableId': 'angle',
                      'operator': '<',
                      'value': 20.0
                    }
                  ]
                },
                {
                  'id': 'angle_effect_large',
                  'text': 'The period will be noticeably affected by the initial angle',
                  'conditions': [
                    {
                      'variableId': 'angle',
                      'operator': '>',
                      'value': 30.0
                    }
                  ]
                }
              ]
            }
          ]
        },
        isImplemented: true,
      ),

      // Interactive Model Comparison Tool
      InteractiveWidgetModel(
        id: 'interactive_model_comparison_tool_1',
        name: 'Scientific Model Comparison Tool',
        type: 'interactive_model_comparison_tool',
        category: 'Science',
        description: 'An interactive tool for comparing different scientific models across various scenarios.',
        data: {
          'title': 'Scientific Model Comparison Tool',
          'description': 'Compare different scientific models to determine which best explains various scenarios.',
          'primaryColor': '#009688',
          'secondaryColor': '#E91E63',
          'accentColor': '#FFC107',
          'backgroundColor': '#FFFFFF',
          'textColor': '#212121',
          'showNameTag': true,
          'models': [
            {
              'id': 'geocentric',
              'name': 'Geocentric Model',
              'shortDescription': 'Earth-centered model of the solar system',
              'fullDescription': 'The geocentric model places Earth at the center of the universe, with the Sun, Moon, planets, and stars revolving around it. This was the prevailing view in ancient Greece and was formalized by Ptolemy.',
              'highScoreFeedback': 'While the geocentric model can explain some basic observations like daily rising and setting of celestial bodies, it requires complex mechanisms like epicycles to account for retrograde motion and fails to explain many other astronomical phenomena.',
              'mediumScoreFeedback': 'The geocentric model struggles to explain many astronomical observations without adding complex, ad hoc mechanisms like epicycles and equants.',
              'lowScoreFeedback': 'The geocentric model is inadequate for explaining this scenario, which is better accounted for by heliocentric models.'
            },
            {
              'id': 'heliocentric',
              'name': 'Heliocentric Model',
              'shortDescription': 'Sun-centered model of the solar system',
              'fullDescription': 'The heliocentric model places the Sun at the center of the solar system, with Earth and other planets orbiting around it. This view was proposed by Copernicus and later refined by Kepler and Galileo.',
              'highScoreFeedback': 'The heliocentric model elegantly explains this scenario, providing a simpler and more accurate framework than the geocentric model.',
              'mediumScoreFeedback': 'The heliocentric model explains this scenario reasonably well, though some aspects might require additional considerations.',
              'lowScoreFeedback': 'The heliocentric model struggles to fully account for this scenario without additional modifications or considerations.'
            },
            {
              'id': 'newtonian',
              'name': 'Newtonian Gravity',
              'shortDescription': 'Classical model of gravitational attraction',
              'fullDescription': 'Newton\'s theory of gravity describes gravity as a force of attraction between masses, proportional to their masses and inversely proportional to the square of the distance between them. It successfully explains planetary motion and many everyday gravitational phenomena.',
              'highScoreFeedback': 'Newton\'s gravitational theory provides an excellent explanation for this scenario, accurately predicting the observed phenomena through its mathematical framework.',
              'mediumScoreFeedback': 'Newton\'s gravitational theory explains this scenario reasonably well, though some aspects might be better addressed by Einstein\'s general relativity.',
              'lowScoreFeedback': 'Newton\'s gravitational theory is inadequate for explaining this scenario, which involves conditions where relativistic effects become significant.'
            },
            {
              'id': 'relativity',
              'name': 'General Relativity',
              'shortDescription': 'Einstein\'s model of gravity as spacetime curvature',
              'fullDescription': 'Einstein\'s general theory of relativity describes gravity not as a force, but as a curvature of spacetime caused by mass and energy. It extends Newton\'s theory to explain gravitational phenomena in extreme conditions and has been confirmed by numerous experiments.',
              'highScoreFeedback': 'General relativity provides the most comprehensive explanation for this scenario, accounting for effects that Newtonian gravity cannot explain.',
              'mediumScoreFeedback': 'General relativity explains this scenario well, though the full mathematical treatment might be more complex than necessary for some aspects.',
              'lowScoreFeedback': 'Even general relativity struggles to fully account for this scenario, which might involve quantum effects or other phenomena beyond classical spacetime.'
            }
          ],
          'scenarios': [
            {
              'id': 'retrograde_motion',
              'name': 'Retrograde Motion of Mars',
              'description': 'From Earth, Mars appears to occasionally reverse its direction of motion against the background of fixed stars, moving in a loop-like pattern before resuming its normal path.',
              'expertAnalysis': 'Retrograde motion is elegantly explained by the heliocentric model: it\'s an optical illusion that occurs when Earth, moving in a faster inner orbit, overtakes Mars in its slower outer orbit. The geocentric model requires complex epicycles to explain this phenomenon. Newtonian gravity and general relativity both incorporate the heliocentric view and provide the physical laws governing planetary motion, with general relativity offering minor refinements to Newtonian predictions.'
            },
            {
              'id': 'mercury_orbit',
              'name': 'Mercury\'s Orbital Precession',
              'description': 'The point of Mercury\'s closest approach to the Sun (perihelion) shifts slightly with each orbit, more than can be accounted for by the gravitational influence of other planets.',
              'expertAnalysis': 'Mercury\'s orbital precession is a key phenomenon that distinguishes between Newtonian gravity and general relativity. Newtonian physics could explain most but not all of the precession, leaving a discrepancy of 43 arcseconds per century. Einstein\'s general relativity, which describes gravity as spacetime curvature, perfectly accounts for this discrepancy. This was one of the first confirmations of Einstein\'s theory. Neither the geocentric nor basic heliocentric models address this phenomenon.'
            },
            {
              'id': 'gravitational_lensing',
              'name': 'Gravitational Lensing',
              'description': 'Light from distant stars is observed to bend as it passes near massive objects like the Sun, causing the apparent position of the stars to shift.',
              'expertAnalysis': 'Gravitational lensing is a phenomenon uniquely predicted by Einstein\'s general relativity, which describes how mass curves spacetime, causing light to follow this curved path. Newtonian gravity predicts some bending but only half the amount observed. The effect was famously confirmed during a solar eclipse in 1919, providing strong evidence for Einstein\'s theory. Neither the geocentric nor heliocentric models address light bending, as they focus on planetary positions rather than the nature of gravity itself.'
            }
          ]
        },
        isImplemented: true,
      ),

      // Interactive Evidence Evaluator
      InteractiveWidgetModel(
        id: 'interactive_evidence_evaluator_1',
        name: 'Scientific Evidence Evaluator',
        type: 'interactive_evidence_evaluator',
        category: 'Science',
        description: 'An interactive tool for evaluating scientific evidence in relation to claims.',
        data: {
          'title': 'Scientific Evidence Evaluator',
          'description': 'Evaluate whether evidence supports, contradicts, or is not relevant to a scientific claim.',
          'primaryColor': '#3F51B5',
          'secondaryColor': '#FF5722',
          'accentColor': '#009688',
          'backgroundColor': '#FFFFFF',
          'textColor': '#212121',
          'showNameTag': true,
          'scenarios': [
            {
              'id': 'climate_change',
              'claim': 'Human activities are causing global climate change',
              'description': 'Evaluate whether each piece of evidence supports, contradicts, or is not relevant to the claim that human activities are causing global climate change.',
              'correctFeedback': 'Excellent! You have correctly evaluated all the evidence. Scientific reasoning requires carefully assessing how evidence relates to claims.',
              'incorrectFeedback': 'Some of your evaluations need revision. Remember to consider whether each piece of evidence directly supports or contradicts the specific claim being made.',
              'explanation': 'When evaluating evidence for climate change, it is important to distinguish between evidence that directly addresses human causation versus natural climate variations. Temperature records, greenhouse gas measurements, and correlation with human activities provide supporting evidence. Evidence about past climate changes may be relevant for context but does not necessarily contradict human influence on current changes. Unrelated weather events are typically not relevant to the broader climate trend claim.',
              'evidenceItems': [
                {
                  'id': 'cc_evidence_1',
                  'text': 'Global average temperatures have increased by about 1°C since pre-industrial times.',
                  'correctRating': 'supports'
                },
                {
                  'id': 'cc_evidence_2',
                  'text': 'Atmospheric CO2 levels have increased from 280 ppm to over 410 ppm since the Industrial Revolution.',
                  'correctRating': 'supports'
                },
                {
                  'id': 'cc_evidence_3',
                  'text': 'The Earth has experienced multiple ice ages and warm periods throughout its history, long before human industrial activity.',
                  'correctRating': 'contradicts'
                },
                {
                  'id': 'cc_evidence_4',
                  'text': 'A record-breaking snowstorm hit the northeastern United States last winter.',
                  'correctRating': 'not_relevant'
                },
                {
                  'id': 'cc_evidence_5',
                  'text': 'The timing of CO2 increase closely correlates with increased fossil fuel consumption.',
                  'correctRating': 'supports'
                }
              ]
            },
            {
              'id': 'vaccine_safety',
              'claim': 'COVID-19 vaccines are safe for the general adult population',
              'description': 'Evaluate whether each piece of evidence supports, contradicts, or is not relevant to the claim that COVID-19 vaccines are safe for the general adult population.',
              'correctFeedback': 'Well done! You have correctly evaluated the evidence regarding vaccine safety. Scientific reasoning requires distinguishing between statistical evidence, anecdotes, and irrelevant information.',
              'incorrectFeedback': 'Some of your evaluations need revision. When assessing vaccine safety, it is important to consider the quality and relevance of evidence, distinguishing between large-scale studies and individual reports.',
              'explanation': 'Evaluating vaccine safety requires examining large-scale clinical trial data and post-approval monitoring, which provide statistical evidence about safety across populations. Rare adverse events must be weighed against background rates and overall benefits. Individual anecdotes, while emotionally compelling, do not provide statistical evidence about general safety. Information about vaccine efficacy, while important, addresses a different question than safety.',
              'evidenceItems': [
                {
                  'id': 'vs_evidence_1',
                  'text': 'Clinical trials involving tens of thousands of participants showed no serious safety concerns for the general population.',
                  'correctRating': 'supports'
                },
                {
                  'id': 'vs_evidence_2',
                  'text': 'Some individuals reported experiencing headaches and fatigue after vaccination.',
                  'correctRating': 'not_relevant'
                },
                {
                  'id': 'vs_evidence_3',
                  'text': 'Rare cases of myocarditis (heart inflammation) have been reported, primarily in young males, at a rate of approximately 1 in 20,000.',
                  'correctRating': 'contradicts'
                },
                {
                  'id': 'vs_evidence_4',
                  'text': 'Over 5 billion people worldwide have received at least one dose of a COVID-19 vaccine.',
                  'correctRating': 'not_relevant'
                },
                {
                  'id': 'vs_evidence_5',
                  'text': 'Post-approval safety monitoring has not identified any widespread serious adverse effects in the general population.',
                  'correctRating': 'supports'
                }
              ]
            },
            {
              'id': 'diet_health',
              'claim': 'A Mediterranean diet reduces the risk of heart disease',
              'description': 'Evaluate whether each piece of evidence supports, contradicts, or is not relevant to the claim that a Mediterranean diet reduces the risk of heart disease.',
              'correctFeedback': 'Excellent analysis! You have correctly evaluated the evidence regarding the Mediterranean diet and heart disease risk. Scientific reasoning requires distinguishing between controlled studies, correlational data, and irrelevant information.',
              'incorrectFeedback': 'Some of your evaluations need revision. When assessing dietary effects on health, it is important to consider study design, population relevance, and whether the evidence directly addresses the specific claim.',
              'explanation': 'Evaluating dietary claims requires examining controlled trials, large observational studies, and mechanistic evidence. The strongest evidence comes from randomized controlled trials showing direct effects on disease outcomes or biomarkers. Observational studies showing correlations provide supporting evidence but cannot establish causation alone. Cultural or demographic information may provide context but does not directly address the causal relationship. Evidence about other diets may be relevant for comparison but does not necessarily contradict the specific claim.',
              'evidenceItems': [
                {
                  'id': 'dh_evidence_1',
                  'text': 'A randomized controlled trial with 7,500 participants showed a 30% reduction in cardiovascular events among those following a Mediterranean diet compared to a low-fat diet.',
                  'correctRating': 'supports'
                },
                {
                  'id': 'dh_evidence_2',
                  'text': 'People living in Mediterranean countries have historically had lower rates of heart disease than those in Northern Europe and North America.',
                  'correctRating': 'supports'
                },
                {
                  'id': 'dh_evidence_3',
                  'text': 'A study found that a low-carbohydrate diet led to greater weight loss than a Mediterranean diet over a 6-month period.',
                  'correctRating': 'not_relevant'
                },
                {
                  'id': 'dh_evidence_4',
                  'text': 'Laboratory studies show that olive oil, a key component of the Mediterranean diet, contains antioxidants that reduce inflammation markers associated with heart disease.',
                  'correctRating': 'supports'
                },
                {
                  'id': 'dh_evidence_5',
                  'text': 'Some individuals with specific genetic variations do not show improved cholesterol levels when following a Mediterranean diet.',
                  'correctRating': 'contradicts'
                }
              ]
            }
          ]
        },
        isImplemented: true,
      ),

      // Interactive Argument Strength Analyzer
      InteractiveWidgetModel(
        id: 'interactive_argument_strength_analyzer_1',
        name: 'Scientific Argument Strength Analyzer',
        type: 'interactive_argument_strength_analyzer',
        category: 'Science',
        description: 'An interactive tool for analyzing the strength of scientific arguments based on various criteria.',
        data: {
          'title': 'Scientific Argument Strength Analyzer',
          'description': 'Analyze the strength of scientific arguments based on key criteria.',
          'primaryColor': '#673AB7',
          'secondaryColor': '#FF9800',
          'accentColor': '#4CAF50',
          'backgroundColor': '#FFFFFF',
          'textColor': '#212121',
          'showNameTag': true,
          'scenarios': [
            {
              'id': 'climate_policy',
              'claim': 'Governments should implement carbon taxes to reduce greenhouse gas emissions',
              'argument': 'Carbon taxes are an effective policy tool for reducing greenhouse gas emissions. Economic models show that putting a price on carbon incentivizes businesses and consumers to reduce emissions through more efficient energy use and switching to cleaner energy sources. Several countries that have implemented carbon taxes, including Sweden and British Columbia, have seen emissions decrease while maintaining economic growth. Carbon taxes also generate revenue that can be used to fund clean energy research or returned to citizens as dividends. While there are concerns about impacts on low-income households and energy-intensive industries, these can be addressed through targeted rebates and gradual implementation.',
              'highScoreFeedback': 'This is a strong argument that effectively uses evidence, addresses counterarguments, and presents a logical case for carbon taxes.',
              'mediumScoreFeedback': 'This argument has some strengths but could be improved with more specific evidence or better reasoning in certain areas.',
              'lowScoreFeedback': 'This argument has significant weaknesses in its evidence, reasoning, or structure that undermine its persuasiveness.',
              'expertAnalysis': 'This argument for carbon taxes is relatively strong. It cites specific examples (Sweden, British Columbia) where carbon taxes have been effective, explains the economic mechanism by which they work, acknowledges potential counterarguments, and offers solutions to those concerns. The argument could be strengthened with more specific data on emission reductions in the cited examples and more detailed analysis of how rebates would work to protect vulnerable populations. The argument is primarily consequentialist, focusing on outcomes rather than rights or justice considerations, which might be seen as a limitation by some.',
              'criteria': [
                {
                  'id': 'evidence_quality',
                  'name': 'Evidence Quality',
                  'description': 'The strength, relevance, and reliability of the evidence presented',
                  'weight': 5,
                  'highScoreFeedback': 'The argument uses strong evidence, citing specific examples of carbon tax implementation and their outcomes.',
                  'mediumScoreFeedback': 'The argument includes some evidence but could benefit from more specific data or examples.',
                  'lowScoreFeedback': 'The argument lacks sufficient evidence or relies on questionable sources.'
                },
                {
                  'id': 'logical_reasoning',
                  'name': 'Logical Reasoning',
                  'description': 'The clarity and validity of the reasoning connecting evidence to conclusions',
                  'weight': 4,
                  'highScoreFeedback': 'The argument presents clear, logical reasoning about how carbon taxes create incentives that reduce emissions.',
                  'mediumScoreFeedback': 'The reasoning is generally sound but contains some gaps or unclear connections.',
                  'lowScoreFeedback': 'The reasoning contains significant logical flaws or fails to connect evidence to conclusions.'
                },
                {
                  'id': 'counterargument_address',
                  'name': 'Addressing Counterarguments',
                  'description': 'How well the argument acknowledges and responds to potential objections',
                  'weight': 3,
                  'highScoreFeedback': 'The argument effectively acknowledges concerns about impacts on low-income households and industries, offering specific solutions.',
                  'mediumScoreFeedback': 'The argument acknowledges some counterarguments but could address them more thoroughly.',
                  'lowScoreFeedback': 'The argument fails to address obvious counterarguments or dismisses them without adequate response.'
                },
                {
                  'id': 'clarity_organization',
                  'name': 'Clarity and Organization',
                  'description': 'How clearly the argument is presented and how well it is structured',
                  'weight': 2,
                  'highScoreFeedback': 'The argument is well-organized, with a clear structure that enhances understanding.',
                  'mediumScoreFeedback': 'The argument is generally clear but could benefit from better organization or more precise language.',
                  'lowScoreFeedback': 'The argument is poorly organized or unclear, making it difficult to follow.'
                }
              ]
            },
            {
              'id': 'gmo_safety',
              'claim': 'Genetically modified foods are safe for human consumption',
              'argument': 'Genetically modified (GM) foods have been extensively tested and found to be safe for human consumption. Major scientific organizations worldwide, including the World Health Organization, the American Medical Association, and the National Academy of Sciences, have concluded that GM foods currently available are safe to eat. Hundreds of independent studies have found no evidence of harm from consuming GM foods. The process of genetic modification is often more precise than traditional breeding methods. While some critics point to potential allergenicity or unintended effects, regulatory systems require rigorous safety assessments before GM foods reach the market. Each GM food should be evaluated individually, but the scientific consensus supports the general safety of GM foods that have passed regulatory approval.',
              'highScoreFeedback': 'This is a strong argument that effectively uses scientific consensus, addresses concerns, and presents a nuanced view of GM food safety.',
              'mediumScoreFeedback': 'This argument has some strengths in citing scientific authorities but could be improved with more specific evidence or better addressing of certain concerns.',
              'lowScoreFeedback': 'This argument has significant weaknesses in how it presents the scientific evidence or addresses legitimate concerns about GM foods.',
              'expertAnalysis': 'This argument for GM food safety is well-constructed. It cites scientific consensus from multiple reputable organizations, acknowledges the need for individual assessment of GM foods rather than making blanket claims, and addresses common concerns about allergenicity and unintended effects. The argument could be strengthened by citing specific studies rather than just mentioning their existence and by more thoroughly explaining the regulatory process. The argument appropriately focuses on scientific evidence rather than economic or political considerations, which is appropriate for a safety claim.',
              'criteria': [
                {
                  'id': 'scientific_consensus',
                  'name': 'Scientific Consensus',
                  'description': 'How accurately the argument represents the scientific consensus',
                  'weight': 5,
                  'highScoreFeedback': 'The argument accurately represents the strong scientific consensus on GM food safety, citing multiple authoritative organizations.',
                  'mediumScoreFeedback': 'The argument references scientific consensus but could provide more specific information about the extent of agreement.',
                  'lowScoreFeedback': 'The argument misrepresents the scientific consensus or fails to acknowledge it.'
                },
                {
                  'id': 'evidence_quality',
                  'name': 'Evidence Quality',
                  'description': 'The strength, relevance, and reliability of the evidence presented',
                  'weight': 4,
                  'highScoreFeedback': 'The argument cites strong evidence from reputable scientific organizations and studies.',
                  'mediumScoreFeedback': 'The argument includes some evidence but could benefit from more specific studies or data.',
                  'lowScoreFeedback': 'The argument lacks sufficient evidence or relies on questionable sources.'
                },
                {
                  'id': 'nuance_complexity',
                  'name': 'Nuance and Complexity',
                  'description': 'How well the argument acknowledges the complexity of the issue',
                  'weight': 3,
                  'highScoreFeedback': 'The argument shows appropriate nuance, acknowledging that each GM food should be evaluated individually rather than making sweeping claims.',
                  'mediumScoreFeedback': 'The argument shows some nuance but could better acknowledge certain complexities of the issue.',
                  'lowScoreFeedback': 'The argument oversimplifies the issue or makes sweeping generalizations.'
                },
                {
                  'id': 'counterargument_address',
                  'name': 'Addressing Concerns',
                  'description': 'How well the argument acknowledges and responds to common concerns',
                  'weight': 3,
                  'highScoreFeedback': 'The argument effectively acknowledges and addresses common concerns about allergenicity and unintended effects.',
                  'mediumScoreFeedback': 'The argument acknowledges some concerns but could address them more thoroughly.',
                  'lowScoreFeedback': 'The argument fails to address obvious concerns or dismisses them without adequate response.'
                }
              ]
            },
            {
              'id': 'vaccine_mandate',
              'claim': 'School vaccine requirements are justified for preventing disease outbreaks',
              'argument': 'School vaccine requirements are justified to prevent disease outbreaks and protect public health. Vaccines have dramatically reduced the incidence of many dangerous childhood diseases, but their effectiveness at the population level depends on high vaccination rates to maintain herd immunity. When vaccination rates fall below certain thresholds, preventable disease outbreaks can occur, as demonstrated by recent measles outbreaks in communities with low vaccination rates. Children in schools are in close contact, creating conditions where diseases can spread rapidly. While parents generally have authority over their children healthcare, this authority is not absolute when decisions affect others health. Medical exemptions should be available for children with legitimate contraindications, but non-medical exemptions should be limited. The benefits of mandatory vaccination policies for preventing serious diseases outweigh the limitations on parental choice.',
              'highScoreFeedback': 'This is a strong argument that effectively balances public health considerations with individual rights, using evidence of herd immunity and recent outbreaks.',
              'mediumScoreFeedback': 'This argument has some strengths in its public health reasoning but could better address certain ethical considerations or provide more specific evidence.',
              'lowScoreFeedback': 'This argument has significant weaknesses in how it addresses individual rights or presents public health evidence.',
              'expertAnalysis': 'This argument for school vaccine requirements is well-balanced. It explains the scientific basis for vaccination policies (herd immunity), provides evidence of consequences when vaccination rates drop (recent measles outbreaks), acknowledges competing values (parental authority), and offers a compromise position (medical exemptions). The argument could be strengthened with more specific data on outbreak prevention in highly vaccinated populations and more detailed discussion of the ethical principles that justify limiting parental choice. The argument appropriately weighs both consequentialist considerations (preventing harm) and rights-based considerations (parental authority vs. collective welfare).',
              'criteria': [
                {
                  'id': 'public_health_evidence',
                  'name': 'Public Health Evidence',
                  'description': 'The quality of evidence regarding vaccination effectiveness and herd immunity',
                  'weight': 4,
                  'highScoreFeedback': 'The argument effectively explains herd immunity and cites evidence of outbreaks in low-vaccination communities.',
                  'mediumScoreFeedback': 'The argument includes some public health evidence but could provide more specific data or examples.',
                  'lowScoreFeedback': 'The argument lacks sufficient public health evidence or misrepresents the science of vaccination.'
                },
                {
                  'id': 'ethical_reasoning',
                  'name': 'Ethical Reasoning',
                  'description': 'How well the argument balances competing ethical principles',
                  'weight': 5,
                  'highScoreFeedback': 'The argument thoughtfully balances parental authority against public health needs, with clear ethical reasoning.',
                  'mediumScoreFeedback': 'The argument acknowledges competing ethical principles but could develop the ethical reasoning more thoroughly.',
                  'lowScoreFeedback': 'The argument fails to adequately address important ethical considerations or presents an unbalanced view.'
                },
                {
                  'id': 'practical_implementation',
                  'name': 'Practical Implementation',
                  'description': 'How well the argument addresses practical aspects of implementation',
                  'weight': 3,
                  'highScoreFeedback': 'The argument offers practical solutions like medical exemptions while limiting non-medical exemptions.',
                  'mediumScoreFeedback': 'The argument touches on implementation but could provide more detail about how policies should work in practice.',
                  'lowScoreFeedback': 'The argument ignores important practical considerations about implementing vaccine requirements.'
                },
                {
                  'id': 'counterargument_address',
                  'name': 'Addressing Counterarguments',
                  'description': 'How well the argument acknowledges and responds to objections',
                  'weight': 3,
                  'highScoreFeedback': 'The argument effectively acknowledges concerns about parental authority and addresses them thoughtfully.',
                  'mediumScoreFeedback': 'The argument acknowledges some counterarguments but could address them more thoroughly.',
                  'lowScoreFeedback': 'The argument fails to address obvious counterarguments or dismisses them without adequate response.'
                }
              ]
            }
          ]
        },
        isImplemented: true,
      ),

      // Interactive Correlation vs. Causation Explorer
      InteractiveWidgetModel(
        id: 'interactive_correlation_causation_explorer_1',
        name: 'Correlation vs. Causation Explorer',
        type: 'interactive_correlation_causation_explorer',
        category: 'Science',
        description: 'An interactive tool for exploring the difference between correlation and causation in scientific data.',
        data: {
          'title': 'Correlation vs. Causation Explorer',
          'description': 'Analyze scenarios to determine whether relationships represent causation, correlation, or coincidence.',
          'primaryColor': '#009688',
          'secondaryColor': '#FF5722',
          'accentColor': '#FFC107',
          'backgroundColor': '#FFFFFF',
          'textColor': '#212121',
          'showNameTag': true,
          'scenarios': [
            {
              'id': 'ice_cream_drownings',
              'description': 'Research shows that ice cream sales and drowning deaths both increase during summer months. As ice cream sales increase, so do drowning incidents.',
              'chartUrl': 'https://i.imgur.com/JfVIY5P.png',
              'correctRelationship': 'correlation',
              'correctFeedback': 'Correct! This is a correlation. Both ice cream sales and drowning incidents increase during summer months, but one does not cause the other. They share a common cause: warmer weather leads to more swimming (increasing drowning risk) and more ice cream consumption.',
              'incorrectFeedback': 'This is not a causal relationship. Both ice cream sales and drowning incidents increase during summer months, but one does not cause the other. They share a common cause: warmer weather leads to more swimming (increasing drowning risk) and more ice cream consumption.',
              'expertExplanation': 'This is a classic example of a spurious correlation with a confounding variable (summer/warm weather). The relationship between ice cream sales and drownings exists, but it\'s not causal - both are independently affected by the season. This illustrates the principle that "correlation does not imply causation." To establish causation, we would need to control for the confounding variable and see if the relationship persists, which it wouldn\'t in this case.'
            },
            {
              'id': 'smoking_cancer',
              'description': 'Long-term studies show that people who smoke cigarettes have a significantly higher rate of lung cancer than non-smokers. The risk increases with the number of cigarettes smoked per day and years of smoking.',
              'chartUrl': 'https://i.imgur.com/8ZkWJZU.png',
              'correctRelationship': 'causal',
              'correctFeedback': 'Correct! This is a causal relationship. Extensive research has established that smoking causes lung cancer through multiple mechanisms, including DNA damage from carcinogens in tobacco smoke.',
              'incorrectFeedback': 'This is actually a causal relationship. While correlation alone doesn\'t prove causation, in this case, extensive research has established that smoking causes lung cancer through multiple mechanisms, including DNA damage from carcinogens in tobacco smoke.',
              'expertExplanation': 'The smoking-cancer relationship is one of the most thoroughly established causal relationships in epidemiology. It meets all of Bradford Hill\'s criteria for causation: strength of association, consistency across studies, specificity, temporality (smoking precedes cancer), biological gradient (dose-response), plausibility, coherence with existing knowledge, experimental evidence, and analogy. Laboratory studies have identified specific carcinogens in tobacco smoke and the mechanisms by which they damage DNA and lead to cancer.'
            },
            {
              'id': 'storks_babies',
              'description': 'A study in Europe found that regions with more storks also had higher birth rates. The correlation was statistically significant across multiple countries.',
              'chartUrl': 'https://i.imgur.com/JKMdNyL.png',
              'correctRelationship': 'coincidence',
              'correctFeedback': 'Correct! This is a coincidence or spurious correlation. The apparent relationship between storks and birth rates is explained by other factors - rural areas tend to have both more storks (due to suitable habitat) and higher birth rates (due to demographic factors).',
              'incorrectFeedback': 'This is actually a coincidence or spurious correlation. The apparent relationship between storks and birth rates is explained by other factors - rural areas tend to have both more storks (due to suitable habitat) and higher birth rates (due to demographic factors).',
              'expertExplanation': 'This example illustrates how correlations can sometimes be entirely coincidental or explained by underlying factors not included in the analysis. The stork-birth rate correlation likely exists because rural areas provide better habitat for storks and also tend to have higher birth rates due to socioeconomic and cultural factors. This demonstrates why correlation alone is insufficient to establish a meaningful relationship between variables, let alone causation.'
            },
            {
              'id': 'vaccination_autism',
              'description': 'Some parents have noticed that their children\'s autism symptoms became apparent shortly after receiving childhood vaccinations, leading to concerns about a possible link.',
              'chartUrl': 'https://i.imgur.com/L2QspWB.png',
              'correctRelationship': 'coincidence',
              'correctFeedback': 'Correct! This is a coincidence. Extensive scientific research has found no causal link between vaccines and autism. The timing appears related because autism symptoms often become noticeable around the same age that children receive certain vaccinations.',
              'incorrectFeedback': 'This is actually a coincidence. Extensive scientific research has found no causal link between vaccines and autism. The timing appears related because autism symptoms often become noticeable around the same age that children receive certain vaccinations.',
              'expertExplanation': 'This example illustrates the post hoc fallacy (post hoc ergo propter hoc - "after this, therefore because of this"). Autism symptoms typically become apparent around 12-24 months, which happens to be when several childhood vaccines are administered. Multiple large-scale epidemiological studies involving millions of children have found no link between vaccines and autism. This case demonstrates why anecdotal observations need to be tested with controlled studies to establish or rule out causation.'
            },
            {
              'id': 'exercise_health',
              'description': 'Long-term studies show that people who exercise regularly have lower rates of heart disease, better mental health, and longer lifespans compared to sedentary individuals.',
              'chartUrl': 'https://i.imgur.com/9XYZaBC.png',
              'correctRelationship': 'causal',
              'correctFeedback': 'Correct! This is a causal relationship. Randomized controlled trials and mechanistic studies have established that exercise directly improves cardiovascular health, mental wellbeing, and other health outcomes through multiple biological pathways.',
              'incorrectFeedback': 'This is actually a causal relationship. While observational studies show correlation, randomized controlled trials and mechanistic studies have established that exercise directly improves cardiovascular health, mental wellbeing, and other health outcomes through multiple biological pathways.',
              'expertExplanation': 'The exercise-health relationship is supported by multiple lines of evidence that establish causation: 1) Randomized controlled trials show that exercise interventions improve health outcomes; 2) Dose-response relationships exist between exercise amount and health benefits; 3) Biological mechanisms have been identified (improved cardiovascular function, reduced inflammation, hormonal changes, etc.); 4) The effects are consistent across populations; and 5) The relationship is coherent with our understanding of human physiology. This example demonstrates how multiple types of evidence can work together to establish causation.'
            }
          ]
        },
        isImplemented: true,
      ),

      // Interactive Logical Fallacy Detector
      InteractiveWidgetModel(
        id: 'interactive_logical_fallacy_detector_1',
        name: 'Logical Fallacy Detector',
        type: 'interactive_logical_fallacy_detector',
        category: 'Science',
        description: 'An interactive tool for identifying logical fallacies in scientific arguments.',
        data: {
          'title': 'Logical Fallacy Detector',
          'description': 'Identify logical fallacies in scientific arguments and explanations.',
          'primaryColor': '#673AB7',
          'secondaryColor': '#FF9800',
          'accentColor': '#4CAF50',
          'backgroundColor': '#FFFFFF',
          'textColor': '#212121',
          'showNameTag': true,
          'fallacies': [
            {
              'id': 'ad_hominem',
              'name': 'Ad Hominem',
              'shortDescription': 'Attacking the person instead of addressing their argument',
              'fullDescription': 'An ad hominem fallacy occurs when someone attacks the person making an argument rather than addressing the argument itself. It attempts to undermine the argument by attacking the credibility, character, or other attribute of the person making the argument, rather than providing evidence against the argument.',
              'example': 'Dr. Smith argues that climate change is caused by human activities, but he drives an SUV, so his argument must be wrong.'
            },
            {
              'id': 'appeal_to_authority',
              'name': 'Appeal to Authority',
              'shortDescription': 'Using an authority figure as evidence without addressing the argument',
              'fullDescription': 'An appeal to authority fallacy occurs when someone uses the opinion or position of an authority figure or institution to support their argument, instead of providing relevant evidence or reasoning. While expert opinions can be valuable, they are not infallible and should not be the sole basis for an argument.',
              'example': 'Dr. Johnson is a renowned physicist, and he says that homeopathy works, so it must be effective.'
            },
            {
              'id': 'false_dichotomy',
              'name': 'False Dichotomy',
              'shortDescription': 'Presenting only two options when others exist',
              'fullDescription': 'A false dichotomy (also called false dilemma or black-and-white thinking) presents a situation as having only two possible options, when in reality there are more options available. It oversimplifies complex issues and forces a choice between two extremes while ignoring middle ground or alternative solutions.',
              'example': 'Either we continue using fossil fuels, or we return to a pre-industrial lifestyle. There is no middle ground.'
            },
            {
              'id': 'slippery_slope',
              'name': 'Slippery Slope',
              'shortDescription': 'Claiming one event will lead to a series of negative events without evidence',
              'fullDescription': 'A slippery slope fallacy occurs when someone argues that a relatively small first step will inevitably lead to significant and often negative consequences, without providing evidence for the claimed chain of events. It exaggerates the potential consequences of an action and assumes that one event must lead to another.',
              'example': 'If we allow scientists to edit genes to cure diseases, soon they will be creating designer babies, and eventually, we will have a society of genetically engineered superhumans dominating regular people.'
            },
            {
              'id': 'post_hoc',
              'name': 'Post Hoc Ergo Propter Hoc',
              'shortDescription': 'Assuming that because one event followed another, the first caused the second',
              'fullDescription': 'Post hoc ergo propter hoc (Latin for "after this, therefore because of this") is a fallacy that assumes causation based merely on correlation in time. Just because one event follows another, it does not mean the first event caused the second. This fallacy ignores other potential causes and confounding variables.',
              'example': 'I took this herbal supplement, and my cold symptoms improved the next day. Therefore, the supplement cured my cold.'
            },
            {
              'id': 'straw_man',
              'name': 'Straw Man',
              'shortDescription': 'Misrepresenting an opponent\'s argument to make it easier to attack',
              'fullDescription': 'A straw man fallacy occurs when someone misrepresents or oversimplifies an opponent\'s position to make it easier to attack. By distorting the original argument, they create a "straw man" that they can easily knock down, while not actually addressing the real argument.',
              'example': 'Scientists want to study the potential risks of new technology, but they just want to stop all technological progress and return us to the Stone Age.'
            },
            {
              'id': 'hasty_generalization',
              'name': 'Hasty Generalization',
              'shortDescription': 'Drawing a broad conclusion from insufficient evidence',
              'fullDescription': 'A hasty generalization occurs when someone draws a broad conclusion based on insufficient or unrepresentative evidence. It involves making claims about an entire group or phenomenon based on a small sample that may not be representative of the whole.',
              'example': 'I know two scientists who made errors in their research, so scientific research is generally unreliable.'
            },
            {
              'id': 'circular_reasoning',
              'name': 'Circular Reasoning',
              'shortDescription': 'Using the conclusion as a premise in the argument',
              'fullDescription': 'Circular reasoning (also called begging the question) occurs when the conclusion of an argument is used as one of its premises. The argument essentially assumes what it\'s trying to prove, making it logically invalid because it doesn\'t provide independent evidence for the conclusion.',
              'example': 'This scientific theory must be true because the evidence supports it, and we know the evidence is reliable because it supports a true theory.'
            }
          ],
          'scenarios': [
            {
              'id': 'climate_scientist',
              'argument': 'Dr. Williams argues that climate change is a serious threat, but she flies to international conferences several times a year. How can we trust her research when she contributes so much to carbon emissions herself?',
              'correctFallacyId': 'ad_hominem',
              'correctFeedback': 'Correct! This is an ad hominem fallacy. The argument attacks Dr. Williams personally (her travel habits) rather than addressing her research or evidence about climate change. Whether she flies frequently has no bearing on the validity of her scientific findings.',
              'incorrectFeedback': 'This is actually an ad hominem fallacy. The argument attacks Dr. Williams personally (her travel habits) rather than addressing her research or evidence about climate change. Whether she flies frequently has no bearing on the validity of her scientific findings.',
              'expertExplanation': 'This is a classic ad hominem attack that attempts to discredit scientific findings by pointing to perceived hypocrisy in the scientist\'s personal behavior. The logical error is that the scientist\'s personal carbon footprint has no bearing on the validity of climate science data or conclusions. Even if the scientist were indeed hypocritical (which is debatable, as attending conferences may be necessary for scientific work), it would not invalidate the evidence they present. This fallacy is particularly common in climate science discussions, where personal lifestyle choices are often used to deflect from the scientific evidence.'
            },
            {
              'id': 'vaccine_safety',
              'argument': 'My child received the MMR vaccine last week, and three days later developed a rash and fever. This proves that the vaccine caused these symptoms and is unsafe.',
              'correctFallacyId': 'post_hoc',
              'correctFeedback': 'Correct! This is a post hoc ergo propter hoc fallacy. The argument assumes that because the symptoms followed vaccination, they were caused by the vaccine, without considering other possible causes or the normal rate of such symptoms in the population.',
              'incorrectFeedback': 'This is actually a post hoc ergo propter hoc fallacy. The argument assumes that because the symptoms followed vaccination, they were caused by the vaccine, without considering other possible causes or the normal rate of such symptoms in the population.',
              'expertExplanation': 'This argument commits the post hoc fallacy by assuming causation based solely on temporal sequence. While vaccines can cause side effects, determining causation requires more than just temporal proximity. The child could have contracted an unrelated illness, or the symptoms might be coincidental. Scientific evaluation of vaccine safety involves comparing symptom rates in vaccinated and unvaccinated populations and distinguishing between correlation and causation through controlled studies. This fallacy is particularly problematic in vaccine discussions because coincidental illnesses following vaccination are statistically inevitable given the millions of doses administered.'
            },
            {
              'id': 'gmo_debate',
              'argument': 'Scientists want to study the safety of GMO crops, but they just want to block all agricultural innovation and force everyone to use inefficient organic farming methods that can\'t feed the world.',
              'correctFallacyId': 'straw_man',
              'correctFeedback': 'Correct! This is a straw man fallacy. The argument misrepresents the position of scientists who want to study GMO safety, attributing to them an extreme anti-technology stance they likely don\'t hold. Most scientists advocating for safety studies are not opposed to all agricultural innovation.',
              'incorrectFeedback': 'This is actually a straw man fallacy. The argument misrepresents the position of scientists who want to study GMO safety, attributing to them an extreme anti-technology stance they likely don\'t hold. Most scientists advocating for safety studies are not opposed to all agricultural innovation.',
              'expertExplanation': 'This argument constructs a straw man by mischaracterizing the position of scientists who advocate for GMO safety studies. It transforms a reasonable position (studying potential risks of new technologies) into an extreme one (blocking all innovation and forcing inefficient farming). In reality, most scientists who study GMO safety are not categorically opposed to genetic engineering or agricultural innovation. Many support a balanced approach that includes both technological advancement and appropriate safety assessments. This fallacy prevents productive dialogue by creating a caricature of the opposing view rather than engaging with the actual arguments.'
            },
            {
              'id': 'medicine_choice',
              'argument': 'Either we embrace all alternative medicine practices, or we rely solely on pharmaceutical drugs with their many side effects. There\'s no middle ground in healthcare.',
              'correctFallacyId': 'false_dichotomy',
              'correctFeedback': 'Correct! This is a false dichotomy fallacy. The argument presents only two extreme options (all alternative medicine or only pharmaceutical drugs) when in reality, there are many nuanced approaches to healthcare that combine evidence-based treatments from various sources.',
              'incorrectFeedback': 'This is actually a false dichotomy fallacy. The argument presents only two extreme options (all alternative medicine or only pharmaceutical drugs) when in reality, there are many nuanced approaches to healthcare that combine evidence-based treatments from various sources.',
              'expertExplanation': 'This argument creates a false dichotomy by presenting healthcare choices as an either/or situation with only two extreme options. In reality, healthcare exists on a spectrum with many possible approaches. Evidence-based medicine can incorporate treatments from various traditions if they demonstrate efficacy in clinical trials. Many healthcare providers take integrative approaches, recommending conventional treatments alongside complementary approaches with evidence of benefit. The fallacy oversimplifies a complex issue and forces an artificial choice between two extremes, when the optimal approach often involves nuanced evaluation of evidence for specific treatments regardless of their origin.'
            },
            {
              'id': 'gene_editing',
              'argument': 'If we allow scientists to use CRISPR for treating genetic diseases, soon they\'ll be editing embryos for intelligence and athletic ability, then creating genetically engineered soldiers, and eventually, we\'ll have a dystopian society where genetic engineering is mandatory for all children.',
              'correctFallacyId': 'slippery_slope',
              'correctFeedback': 'Correct! This is a slippery slope fallacy. The argument claims that allowing one application of gene editing (treating diseases) will inevitably lead to a series of increasingly problematic applications, without providing evidence for this chain of events or acknowledging the possibility of regulation.',
              'incorrectFeedback': 'This is actually a slippery slope fallacy. The argument claims that allowing one application of gene editing (treating diseases) will inevitably lead to a series of increasingly problematic applications, without providing evidence for this chain of events or acknowledging the possibility of regulation.',
              'expertExplanation': 'This argument exemplifies the slippery slope fallacy by suggesting that a specific, limited application of gene editing technology will inevitably lead to a dystopian future through a series of escalating steps. It assumes that there are no meaningful distinctions between therapeutic applications and enhancement, and that social and regulatory mechanisms cannot effectively draw boundaries. While ethical concerns about gene editing are legitimate, this argument exaggerates by presenting a worst-case scenario as inevitable without providing evidence for the claimed causal chain. Effective governance, ethical oversight, and societal values can create boundaries between acceptable and unacceptable applications of technology.'
            }
          ]
        },
        isImplemented: true,
      ),

      // Interactive Timeline of Scientific Discoveries
      InteractiveWidgetModel(
        id: 'interactive_timeline_scientific_discoveries_1',
        name: 'Timeline of Scientific Discoveries',
        type: 'interactive_timeline_scientific_discoveries',
        category: 'Science',
        description: 'An interactive timeline exploring major scientific discoveries throughout history.',
        data: {
          'title': 'Timeline of Scientific Discoveries',
          'description': 'Explore major scientific discoveries and their impact on our understanding of the world.',
          'primaryColor': '#3F51B5',
          'secondaryColor': '#FF9800',
          'accentColor': '#4CAF50',
          'backgroundColor': '#FFFFFF',
          'textColor': '#212121',
          'showNameTag': true,
          'periods': [
            {
              'id': 'ancient',
              'name': 'Ancient Science',
              'startYear': -3000,
              'endYear': 500,
              'description': 'The foundations of scientific thought were laid in ancient civilizations like Mesopotamia, Egypt, Greece, China, and India. This period saw the development of mathematics, astronomy, medicine, and natural philosophy.',
              'quizQuestions': [
                {
                  'question': 'Which ancient Greek philosopher proposed that the Earth was round based on observations of lunar eclipses?',
                  'options': ['Aristotle', 'Pythagoras', 'Democritus', 'Plato'],
                  'correctOptionIndex': 0,
                  'explanation': 'Aristotle (384-322 BCE) provided several observations supporting a spherical Earth, including the circular shadow of Earth on the Moon during lunar eclipses.'
                },
                {
                  'question': 'The ancient Chinese invention of paper is credited to:',
                  'options': ['Confucius', 'Cai Lun', 'Emperor Qin Shi Huang', 'Zhang Heng'],
                  'correctOptionIndex': 1,
                  'explanation': 'Cai Lun, a court official during the Han Dynasty, is traditionally credited with inventing the papermaking process around 105 CE, using materials like bark, hemp, rags, and fishing nets.'
                },
                {
                  'question': 'Which ancient civilization developed a base-60 number system that we still use today for measuring time and angles?',
                  'options': ['Egyptians', 'Romans', 'Babylonians', 'Mayans'],
                  'correctOptionIndex': 2,
                  'explanation': 'The Babylonians developed the sexagesimal (base-60) system around 3000 BCE, which is why we have 60 seconds in a minute, 60 minutes in an hour, and 360 degrees in a circle.'
                }
              ]
            },
            {
              'id': 'medieval',
              'name': 'Medieval & Islamic Golden Age',
              'startYear': 500,
              'endYear': 1400,
              'description': 'While Europe experienced the Dark Ages, the Islamic world preserved and expanded upon ancient knowledge. Islamic scholars made significant advances in mathematics, astronomy, optics, medicine, and chemistry, while also translating and preserving ancient Greek texts.',
              'quizQuestions': [
                {
                  'question': 'Which Islamic scholar wrote "The Canon of Medicine," a medical encyclopedia used in European universities until the 17th century?',
                  'options': ['Al-Khwarizmi', 'Ibn al-Haytham', 'Ibn Sina (Avicenna)', 'Al-Razi'],
                  'correctOptionIndex': 2,
                  'explanation': 'Ibn Sina (980-1037 CE), known as Avicenna in the West, wrote "The Canon of Medicine," which systematized medical knowledge and remained a standard medical text in Europe for centuries.'
                },
                {
                  'question': 'The concept of "algorithm" is derived from the name of which medieval scholar?',
                  'options': ['Al-Khwarizmi', 'Ibn Rushd', 'Al-Biruni', 'Omar Khayyam'],
                  'correctOptionIndex': 0,
                  'explanation': 'The word "algorithm" comes from the Latinized version of the name of Muhammad ibn Musa al-Khwarizmi (c. 780-850 CE), a Persian mathematician who wrote influential works on algebra and Hindu-Arabic numerals.'
                },
                {
                  'question': 'Which medieval Islamic scholar made significant contributions to the field of optics and proposed that light travels in straight lines?',
                  'options': ['Al-Farabi', 'Ibn al-Haytham (Alhazen)', 'Al-Kindi', 'Ibn Rushd (Averroes)'],
                  'correctOptionIndex': 1,
                  'explanation': 'Ibn al-Haytham (965-1040 CE), known as Alhazen in the West, wrote the "Book of Optics," which correctly explained vision as light reflecting from objects into the eye, rather than emanating from the eye as previously believed.'
                }
              ]
            },
            {
              'id': 'renaissance',
              'name': 'Renaissance & Scientific Revolution',
              'startYear': 1400,
              'endYear': 1700,
              'description': 'This transformative period saw the rebirth of classical learning in Europe and the development of the scientific method. Major advances in astronomy, physics, anatomy, and natural philosophy fundamentally changed our understanding of the universe and our place in it.',
              'quizQuestions': [
                {
                  'question': 'Who published "On the Revolutions of the Celestial Spheres," proposing a heliocentric model of the solar system?',
                  'options': ['Galileo Galilei', 'Johannes Kepler', 'Nicolaus Copernicus', 'Tycho Brahe'],
                  'correctOptionIndex': 2,
                  'explanation': 'Nicolaus Copernicus published his heliocentric theory in "On the Revolutions of the Celestial Spheres" in 1543, just before his death. This work challenged the geocentric model that had dominated astronomy for over a millennium.'
                },
                {
                  'question': 'Which scientist is credited with establishing the scientific method and advocating for empirical, evidence-based approaches?',
                  'options': ['René Descartes', 'Francis Bacon', 'Isaac Newton', 'Robert Boyle'],
                  'correctOptionIndex': 1,
                  'explanation': 'Francis Bacon (1561-1626) advocated for the scientific method based on inductive reasoning and empirical evidence in works like "Novum Organum." He emphasized systematic observation and experimentation rather than relying on ancient authorities.'
                },
                {
                  'question': 'Who discovered the circulation of blood in the human body?',
                  'options': ['Andreas Vesalius', 'William Harvey', 'Paracelsus', 'Ambroise Paré'],
                  'correctOptionIndex': 1,
                  'explanation': 'William Harvey published his discovery of blood circulation in "De Motu Cordis" (1628), demonstrating that the heart pumps blood through a closed system of vessels, contradicting Galen\'s ancient theory that had dominated medicine for centuries.'
                }
              ]
            },
            {
              'id': 'enlightenment',
              'name': 'Enlightenment & Industrial Revolution',
              'startYear': 1700,
              'endYear': 1900,
              'description': 'The 18th and 19th centuries saw the application of scientific knowledge to technology, leading to the Industrial Revolution. This period witnessed major advances in chemistry, biology, geology, electricity, and thermodynamics, along with the development of evolutionary theory.',
              'quizQuestions': [
                {
                  'question': 'Who formulated the theory of evolution by natural selection?',
                  'options': ['Jean-Baptiste Lamarck', 'Charles Darwin', 'Gregor Mendel', 'Alfred Russel Wallace'],
                  'correctOptionIndex': 1,
                  'explanation': 'Charles Darwin published "On the Origin of Species" in 1859, presenting his theory of evolution by natural selection. Alfred Russel Wallace independently developed similar ideas, which prompted Darwin to publish his work.'
                },
                {
                  'question': 'Which scientist discovered that microorganisms cause fermentation and disease, leading to the development of pasteurization?',
                  'options': ['Louis Pasteur', 'Robert Koch', 'Joseph Lister', 'Alexander Fleming'],
                  'correctOptionIndex': 0,
                  'explanation': 'Louis Pasteur (1822-1895) demonstrated that microorganisms cause fermentation and developed pasteurization to prevent spoilage. His work supported the germ theory of disease and led to major advances in preventing infectious diseases.'
                },
                {
                  'question': 'Who developed the periodic table of elements?',
                  'options': ['Antoine Lavoisier', 'John Dalton', 'Dmitri Mendeleev', 'Marie Curie'],
                  'correctOptionIndex': 2,
                  'explanation': 'Dmitri Mendeleev published the first widely recognized periodic table in 1869, organizing elements by atomic weight and chemical properties. His table successfully predicted the properties of elements that had not yet been discovered.'
                }
              ]
            },
            {
              'id': 'modern',
              'name': 'Modern Science',
              'startYear': 1900,
              'endYear': 2023,
              'description': 'The 20th and 21st centuries have seen revolutionary changes in our understanding of the universe, from the subatomic to the cosmic scale. Quantum mechanics, relativity, molecular biology, computer science, and space exploration have transformed both our knowledge and our technological capabilities.',
              'quizQuestions': [
                {
                  'question': 'Who proposed the theory of general relativity, which describes gravity as a curvature of spacetime?',
                  'options': ['Niels Bohr', 'Max Planck', 'Albert Einstein', 'Werner Heisenberg'],
                  'correctOptionIndex': 2,
                  'explanation': 'Albert Einstein published his theory of general relativity in 1915, describing gravity not as a force but as a curvature of spacetime caused by mass and energy. The theory has been confirmed by numerous observations and experiments.'
                },
                {
                  'question': 'The structure of DNA was discovered by:',
                  'options': ['Watson and Crick', 'Franklin and Wilkins', 'Pauling and Corey', 'Avery, MacLeod, and McCarty'],
                  'correctOptionIndex': 0,
                  'explanation': 'James Watson and Francis Crick published their model of the DNA double helix in 1953, based partly on X-ray crystallography data produced by Rosalind Franklin and Maurice Wilkins. This discovery revealed how genetic information is stored and replicated.'
                },
                {
                  'question': 'Which technology allowed scientists to edit genes with unprecedented precision, revolutionizing genetic engineering?',
                  'options': ['PCR', 'CRISPR-Cas9', 'RNA interference', 'Recombinant DNA'],
                  'correctOptionIndex': 1,
                  'explanation': 'CRISPR-Cas9, adapted from a bacterial immune system, was developed as a gene-editing tool in the 2010s. It allows scientists to make precise changes to DNA sequences, with applications in medicine, agriculture, and basic research.'
                }
              ]
            }
          ],
          'discoveries': [
            {
              'id': 'geometry',
              'title': 'Elements of Geometry',
              'year': -300,
              'scientist': 'Euclid',
              'description': 'Euclid compiled and systematized the mathematical knowledge of his time in "Elements," a 13-book treatise that became the foundation of geometry for over 2,000 years. It introduced the axiomatic method, starting with basic definitions and postulates to derive more complex theorems.',
              'impact': 'Euclidean geometry became the standard mathematical system taught for millennia and influenced fields beyond mathematics, including philosophy, architecture, and the scientific method itself.'
            },
            {
              'id': 'heliocentric',
              'title': 'Heliocentric Model of the Solar System',
              'year': 1543,
              'scientist': 'Nicolaus Copernicus',
              'description': 'Copernicus proposed that the Earth and other planets orbit around the Sun, contradicting the geocentric (Earth-centered) model that had dominated astronomy since ancient times. His work "On the Revolutions of the Celestial Spheres" was published as he lay on his deathbed.',
              'impact': 'The Copernican Revolution fundamentally changed our understanding of Earth\'s place in the cosmos, initiating the Scientific Revolution and challenging religious and philosophical assumptions about humanity\'s central position in the universe.'
            },
            {
              'id': 'gravity',
              'title': 'Universal Law of Gravitation',
              'year': 1687,
              'scientist': 'Isaac Newton',
              'description': 'Newton formulated the law of universal gravitation, stating that every particle of matter in the universe attracts every other particle with a force proportional to the product of their masses and inversely proportional to the square of the distance between them. He published this in his "Principia Mathematica," along with his three laws of motion.',
              'impact': 'Newton\'s work unified terrestrial and celestial physics, explaining diverse phenomena from falling apples to planetary orbits with the same mathematical principles. His laws dominated physics for over two centuries and enabled precise predictions of astronomical events.'
            },
            {
              'id': 'oxygen',
              'title': 'Discovery of Oxygen',
              'year': 1774,
              'scientist': 'Joseph Priestley',
              'description': 'Priestley isolated oxygen by heating mercuric oxide and collecting the gas released. He noted that a candle burned more brightly in this gas and that mice could breathe it longer than regular air. Antoine Lavoisier later named the gas "oxygen" and explained its role in combustion and respiration.',
              'impact': 'The discovery of oxygen was crucial to understanding combustion, respiration, and chemical reactions. It helped overturn the phlogiston theory and contributed to the development of modern chemistry.'
            },
            {
              'id': 'evolution',
              'title': 'Theory of Evolution by Natural Selection',
              'year': 1859,
              'scientist': 'Charles Darwin',
              'description': 'After decades of research and observation, Darwin published "On the Origin of Species," proposing that species evolve over generations through natural selection. Individuals with traits better suited to their environment are more likely to survive and reproduce, passing those advantageous traits to offspring.',
              'impact': 'Darwin\'s theory revolutionized biology, providing a unifying explanation for the diversity of life and its adaptation to environments. It fundamentally changed our understanding of humanity\'s place in nature and continues to be the cornerstone of modern biology.'
            },
            {
              'id': 'xrays',
              'title': 'Discovery of X-rays',
              'year': 1895,
              'scientist': 'Wilhelm Röntgen',
              'description': 'Röntgen discovered a new type of radiation while experimenting with cathode rays. These "X-rays" could pass through many materials that block visible light and could expose photographic plates. He produced the first X-ray image of his wife\'s hand, showing her bones and wedding ring.',
              'impact': 'X-rays revolutionized medicine by allowing non-invasive imaging of the body\'s interior. They also became essential tools in materials science, astronomy, and security screening. Röntgen received the first Nobel Prize in Physics in 1901 for this discovery.'
            },
            {
              'id': 'radioactivity',
              'title': 'Discovery of Radioactivity',
              'year': 1896,
              'scientist': 'Henri Becquerel',
              'description': 'Becquerel discovered that uranium salts spontaneously emit penetrating radiation that could fog photographic plates, even when the plates were wrapped in black paper. This phenomenon, later named radioactivity by Marie Curie, was the first evidence of nuclear energy.',
              'impact': 'The discovery of radioactivity led to new understandings of atomic structure, the development of nuclear energy, radiometric dating techniques, and medical applications in both diagnosis and treatment of diseases.'
            },
            {
              'id': 'quantum',
              'title': 'Quantum Theory',
              'year': 1900,
              'scientist': 'Max Planck',
              'description': 'Planck proposed that energy is emitted and absorbed in discrete "quanta" (packets) rather than continuously. This revolutionary idea, which he developed to explain blackbody radiation, contradicted classical physics and laid the foundation for quantum mechanics.',
              'impact': 'Quantum theory fundamentally changed our understanding of the subatomic world and led to technologies like lasers, transistors, and computers. It revealed the probabilistic nature of reality at the smallest scales and continues to challenge our intuitions about the physical world.'
            },
            {
              'id': 'relativity',
              'title': 'Theory of General Relativity',
              'year': 1915,
              'scientist': 'Albert Einstein',
              'description': 'Einstein proposed that gravity is not a force but a curvature of spacetime caused by mass and energy. This geometric theory of gravity extended his earlier special relativity theory and provided a new framework for understanding the universe.',
              'impact': 'General relativity revolutionized our understanding of space, time, and gravity. It predicted phenomena like gravitational waves, black holes, and the expansion of the universe, all later confirmed by observation. It remains our best description of gravity at cosmic scales.'
            },
            {
              'id': 'expanding_universe',
              'title': 'Expanding Universe',
              'year': 1929,
              'scientist': 'Edwin Hubble',
              'description': 'Hubble discovered that galaxies are moving away from us at speeds proportional to their distance, indicating that the universe is expanding. This observation, now known as Hubble\'s Law, was based on measurements of redshifts in galactic spectra.',
              'impact': 'The discovery of the expanding universe transformed cosmology, leading to the Big Bang theory and challenging the previous view of a static, eternal universe. It fundamentally changed our understanding of the universe\'s origin, evolution, and ultimate fate.'
            },
            {
              'id': 'dna_structure',
              'title': 'Structure of DNA',
              'year': 1953,
              'scientist': 'James Watson and Francis Crick',
              'description': 'Watson and Crick proposed the double helix structure of DNA, based partly on X-ray crystallography data from Rosalind Franklin and Maurice Wilkins. Their model showed how the molecule could store genetic information and replicate itself.',
              'impact': 'Understanding DNA\'s structure revolutionized biology and medicine, enabling genetic engineering, personalized medicine, forensic DNA analysis, and a deeper understanding of evolution and inheritance at the molecular level.'
            },
            {
              'id': 'plate_tectonics',
              'title': 'Theory of Plate Tectonics',
              'year': 1968,
              'scientist': 'Multiple scientists',
              'description': 'Building on Alfred Wegener\'s earlier continental drift hypothesis, geologists in the 1960s developed the theory of plate tectonics, explaining that Earth\'s lithosphere is divided into plates that move over the asthenosphere, driven by convection in the mantle.',
              'impact': 'Plate tectonics unified various geological phenomena, explaining earthquakes, volcanoes, mountain formation, and the distribution of fossils. It revolutionized our understanding of Earth\'s dynamic nature and history, becoming the central paradigm of modern geology.'
            },
            {
              'id': 'higgs_boson',
              'title': 'Discovery of the Higgs Boson',
              'year': 2012,
              'scientist': 'CERN (European Organization for Nuclear Research)',
              'description': 'Scientists at CERN\'s Large Hadron Collider discovered the Higgs boson, a fundamental particle predicted by the Standard Model of particle physics. The Higgs field associated with this particle gives mass to other elementary particles.',
              'impact': 'The discovery confirmed a key prediction of the Standard Model, explaining how particles acquire mass. It represented the culmination of a 50-year search and demonstrated the power of international scientific collaboration and advanced technology in exploring fundamental physics.'
            },
            {
              'id': 'crispr',
              'title': 'CRISPR Gene Editing',
              'year': 2012,
              'scientist': 'Jennifer Doudna and Emmanuelle Charpentier',
              'description': 'Doudna and Charpentier developed CRISPR-Cas9 as a gene-editing tool, adapting a system that bacteria use to defend against viruses. CRISPR allows scientists to make precise changes to DNA sequences in living organisms.',
              'impact': 'CRISPR has revolutionized genetic engineering, making it faster, cheaper, and more precise. It has applications in medicine (potentially curing genetic diseases), agriculture (developing more resilient crops), and basic research, while also raising important ethical questions about human genetic modification.'
            }
          ]
        },
        isImplemented: true,
      ),

      // Interactive Emerging Technology Explorer
      InteractiveWidgetModel(
        id: 'interactive_emerging_technology_explorer_1',
        name: 'Emerging Technology Explorer',
        type: 'interactive_emerging_technology_explorer',
        category: 'Science',
        description: 'An interactive tool for exploring emerging technologies and their potential impacts on society.',
        data: {
          'title': 'Emerging Technology Explorer',
          'description': 'Explore cutting-edge technologies and analyze their potential impacts on society, the economy, and the environment.',
          'primaryColor': '#00BCD4',
          'secondaryColor': '#FF5722',
          'accentColor': '#8BC34A',
          'backgroundColor': '#FFFFFF',
          'textColor': '#212121',
          'showNameTag': true,
          'technologies': [
            {
              'id': 'quantum_computing',
              'name': 'Quantum Computing',
              'description': 'Quantum computers leverage quantum mechanical phenomena like superposition and entanglement to perform computations that would be practically impossible for classical computers. Unlike classical bits that are either 0 or 1, quantum bits (qubits) can exist in multiple states simultaneously, potentially enabling exponential computational speedups for certain problems.',
              'status': 'Developing',
              'keyFeatures': [
                'Qubits can exist in multiple states simultaneously (superposition)',
                'Quantum entanglement allows for correlated computations',
                'Potential exponential speedup for specific problems like factoring large numbers and simulating quantum systems',
                'Requires extreme cooling to near absolute zero to maintain quantum coherence',
                'Different physical implementations include superconducting circuits, trapped ions, and topological qubits'
              ],
              'currentApplications': [
                'Cryptography research (both breaking existing systems and creating quantum-resistant ones)',
                'Optimization problems in logistics, finance, and machine learning',
                'Quantum chemistry simulations for drug discovery and materials science',
                'Academic and industrial research to improve quantum hardware and algorithms'
              ],
              'futurePossibilities': [
                'Breaking current encryption standards, necessitating new cryptographic approaches',
                'Accelerating drug discovery through precise molecular simulations',
                'Optimizing complex systems like traffic flow, supply chains, and financial portfolios',
                'Advancing artificial intelligence through quantum machine learning',
                'Simulating quantum physics phenomena that are impossible to model with classical computers'
              ],
              'potentialImpacts': [
                {
                  'id': 'security',
                  'name': 'Cybersecurity Transformation',
                  'description': 'Quantum computers could break widely-used encryption methods, requiring new quantum-resistant cryptography.',
                  'category': 'Societal'
                },
                {
                  'id': 'scientific',
                  'name': 'Scientific Breakthroughs',
                  'description': 'Quantum simulations could revolutionize materials science, drug discovery, and our understanding of quantum physics.',
                  'category': 'Societal'
                },
                {
                  'id': 'economic_disruption',
                  'name': 'Economic Disruption',
                  'description': 'Industries relying on computational advantages could be transformed, creating new market leaders and obsoleting current approaches.',
                  'category': 'Economic'
                },
                {
                  'id': 'optimization',
                  'name': 'Resource Optimization',
                  'description': 'Quantum algorithms could optimize resource allocation, potentially reducing waste and energy consumption.',
                  'category': 'Environmental'
                },
                {
                  'id': 'digital_divide',
                  'name': 'Quantum Digital Divide',
                  'description': 'Access to quantum computing capabilities could create new inequalities between nations and organizations.',
                  'category': 'Ethical'
                },
                {
                  'id': 'energy_consumption',
                  'name': 'Energy Requirements',
                  'description': 'Quantum computers require significant energy for cooling and operation, potentially increasing carbon footprints.',
                  'category': 'Environmental'
                }
              ],
              'expertAnalysis': 'Quantum computing represents one of the most significant paradigm shifts in computational technology since the invention of the transistor. While current quantum computers are still in their early stages with limited practical applications, the field is advancing rapidly. The most immediate impact will likely be in specialized scientific applications rather than general-purpose computing. The cybersecurity implications are particularly significant, as quantum computers could potentially break widely-used encryption methods, though quantum-resistant algorithms are already being developed. The technology\'s environmental impact is complex - while quantum computers themselves require significant energy for cooling, their optimization capabilities could lead to substantial energy savings in other domains. Access to quantum computing resources will likely be unevenly distributed initially, raising concerns about a new "quantum divide" between nations and organizations. Overall, quantum computing should be viewed as a transformative but specialized technology that will complement rather than replace classical computing for most applications in the foreseeable future.'
            },
            {
              'id': 'crispr',
              'name': 'CRISPR Gene Editing',
              'description': 'CRISPR-Cas9 is a revolutionary gene-editing technology that allows scientists to make precise changes to DNA sequences in living organisms. Adapted from a natural defense system in bacteria, CRISPR functions like molecular scissors that can cut DNA at specific locations, allowing genes to be removed, added, or altered with unprecedented precision, speed, and cost-effectiveness.',
              'status': 'Emerging',
              'keyFeatures': [
                'Precise targeting of specific DNA sequences using guide RNA',
                'Ability to delete, insert, or modify genes with high accuracy',
                'Significantly faster and cheaper than previous gene-editing methods',
                'Can be applied to virtually any organism, from bacteria to humans',
                'Continuously improving with new variants like base editors and prime editors'
              ],
              'currentApplications': [
                'Basic research to understand gene function',
                'Agricultural applications to develop disease-resistant crops and improve yields',
                'Industrial biotechnology for enzyme and biofuel production',
                'Early-stage clinical trials for treating genetic diseases like sickle cell anemia and certain cancers',
                'Development of disease models in laboratory animals'
              ],
              'futurePossibilities': [
                'Curing genetic diseases by correcting mutations in affected cells',
                'Creating engineered cell therapies for cancer and autoimmune diseases',
                'Developing more nutritious and climate-resilient crops',
                'Reviving extinct species or creating modified organisms for environmental restoration',
                'Human enhancement through germline editing (raising significant ethical concerns)'
              ],
              'potentialImpacts': [
                {
                  'id': 'medical',
                  'name': 'Medical Revolution',
                  'description': 'CRISPR could transform medicine by enabling cures for genetic diseases and new approaches to treating cancer and infectious diseases.',
                  'category': 'Societal'
                },
                {
                  'id': 'agricultural',
                  'name': 'Agricultural Transformation',
                  'description': 'Gene-edited crops could increase yields, nutrition, and resilience to climate change and diseases.',
                  'category': 'Economic'
                },
                {
                  'id': 'biodiversity',
                  'name': 'Biodiversity Effects',
                  'description': 'Gene drives could control disease vectors or invasive species but might disrupt ecosystems in unpredictable ways.',
                  'category': 'Environmental'
                },
                {
                  'id': 'equity',
                  'name': 'Healthcare Equity',
                  'description': 'Access to gene therapy treatments could be limited by cost, potentially widening health disparities.',
                  'category': 'Ethical'
                },
                {
                  'id': 'germline',
                  'name': 'Germline Modification',
                  'description': 'Editing genes that will be passed to future generations raises profound ethical questions about consent and human evolution.',
                  'category': 'Ethical'
                },
                {
                  'id': 'dual_use',
                  'name': 'Dual-Use Concerns',
                  'description': 'CRISPR technology could potentially be misused to create biological weapons or for bioterrorism.',
                  'category': 'Societal'
                }
              ],
              'expertAnalysis': 'CRISPR represents a genuine revolution in biotechnology, offering unprecedented precision in genetic manipulation with wide-ranging applications. In medicine, CRISPR-based therapies are already showing promise in clinical trials for genetic diseases like sickle cell anemia and beta-thalassemia. Agricultural applications are advancing rapidly, with gene-edited crops poised to address challenges in food security and climate adaptation. However, the technology raises significant ethical concerns, particularly regarding germline editing that would affect future generations, as demonstrated by the controversial birth of CRISPR-edited babies in China in 2018. Environmental applications, such as gene drives to control disease vectors, offer potential benefits but also risks of ecological disruption. The accessibility of CRISPR technology—being relatively inexpensive and straightforward compared to previous gene-editing methods—democratizes its use but also raises biosecurity concerns. Regulatory frameworks are still evolving and vary significantly across countries, creating challenges for governance. Overall, CRISPR\'s transformative potential requires thoughtful oversight that balances innovation with precaution, particularly for applications that could have irreversible consequences.'
            },
            {
              'id': 'artificial_intelligence',
              'name': 'Artificial Intelligence',
              'description': 'Artificial Intelligence (AI) encompasses computer systems designed to perform tasks that typically require human intelligence, such as visual perception, speech recognition, decision-making, and language translation. Modern AI is primarily based on machine learning techniques, especially deep learning, where neural networks learn patterns from data rather than following explicitly programmed instructions.',
              'status': 'Established',
              'keyFeatures': [
                'Machine learning systems that improve with experience and data',
                'Deep neural networks capable of identifying complex patterns',
                'Natural language processing for understanding and generating human language',
                'Computer vision for interpreting and analyzing visual information',
                'Reinforcement learning for decision-making in dynamic environments'
              ],
              'currentApplications': [
                'Virtual assistants and chatbots (Siri, Alexa, ChatGPT)',
                'Recommendation systems for content and products',
                'Medical image analysis and diagnostic assistance',
                'Fraud detection in financial services',
                'Autonomous vehicles and advanced driver-assistance systems',
                'Content creation including text, images, and music'
              ],
              'futurePossibilities': [
                'Fully autonomous transportation systems',
                'Personalized education tailored to individual learning styles',
                'AI-driven scientific discovery in medicine and materials science',
                'More capable robots for eldercare, manufacturing, and hazardous environments',
                'General artificial intelligence approaching human-level capabilities across domains',
                'AI systems that can explain their reasoning and decisions'
              ],
              'potentialImpacts': [
                {
                  'id': 'labor',
                  'name': 'Workforce Transformation',
                  'description': 'AI automation could displace many jobs while creating new ones, requiring significant workforce transitions and reskilling.',
                  'category': 'Economic'
                },
                {
                  'id': 'inequality',
                  'name': 'Economic Inequality',
                  'description': 'Benefits of AI may disproportionately flow to technology companies and highly skilled workers, potentially widening economic divides.',
                  'category': 'Economic'
                },
                {
                  'id': 'privacy',
                  'name': 'Privacy Concerns',
                  'description': 'AI systems often require vast amounts of data, raising concerns about surveillance and the use of personal information.',
                  'category': 'Ethical'
                },
                {
                  'id': 'bias',
                  'name': 'Algorithmic Bias',
                  'description': 'AI systems can perpetuate or amplify existing biases in their training data, leading to unfair outcomes.',
                  'category': 'Ethical'
                },
                {
                  'id': 'autonomy',
                  'name': 'Human Autonomy',
                  'description': 'Increasing reliance on AI for decision-making could reduce human agency and critical thinking.',
                  'category': 'Societal'
                },
                {
                  'id': 'environmental',
                  'name': 'Environmental Impact',
                  'description': 'Training large AI models requires significant energy, but AI can also optimize resource use in various industries.',
                  'category': 'Environmental'
                }
              ],
              'expertAnalysis': 'Artificial intelligence represents perhaps the most pervasive emerging technology, already transforming numerous sectors and likely to have far more profound effects in coming decades. Current AI systems excel at narrow tasks but continue to advance in capabilities and domains of application. The economic impacts are particularly significant, with AI automation potentially affecting 15-30% of existing jobs while creating new roles that may require different skills. This transition will require substantial investment in education and training to prevent widespread technological unemployment. The concentration of AI capabilities in large technology companies raises concerns about market power and data monopolies. Ethical challenges include algorithmic bias, where AI systems can perpetuate or amplify societal inequities, and privacy concerns as AI systems collect and analyze vast amounts of personal data. The environmental impact is mixed - while training large AI models requires significant energy, AI applications in energy management, transportation, and manufacturing could yield substantial efficiency gains. Governance frameworks for AI are still developing, with ongoing debates about appropriate regulation to ensure safety, fairness, and alignment with human values. As AI systems become more capable and autonomous, questions about meaningful human control and the long-term trajectory of the technology will become increasingly important.'
            },
            {
              'id': 'brain_computer_interfaces',
              'name': 'Brain-Computer Interfaces',
              'description': 'Brain-Computer Interfaces (BCIs) create direct communication pathways between the brain and external devices, allowing for monitoring or control of technology through neural activity. BCIs can be invasive (implanted in the brain), partially invasive (placed inside the skull but not in brain tissue), or non-invasive (worn externally), with varying levels of signal resolution and capabilities.',
              'status': 'Emerging',
              'keyFeatures': [
                'Direct interpretation of neural signals for device control',
                'Range from non-invasive (EEG headsets) to invasive (implanted electrodes)',
                'Can be unidirectional (reading brain activity) or bidirectional (also stimulating the brain)',
                'Requires sophisticated signal processing and machine learning algorithms',
                'Increasingly miniaturized and wireless designs'
              ],
              'currentApplications': [
                'Medical applications for patients with paralysis or locked-in syndrome',
                'Prosthetic limb control for amputees',
                'Treatment of neurological conditions like epilepsy and Parkinson\'s disease',
                'Neurofeedback for mental health conditions',
                'Simple consumer applications for meditation and focus enhancement',
                'Research tools for understanding brain function'
              ],
              'futurePossibilities': [
                'Restoration of sensory functions like vision and hearing',
                'Enhanced human cognition and memory',
                'Direct brain-to-brain communication',
                'Seamless control of digital devices and virtual environments',
                'Treatment for a wider range of neurological and psychiatric conditions',
                'Integration with artificial intelligence for augmented cognition'
              ],
              'potentialImpacts': [
                {
                  'id': 'medical',
                  'name': 'Medical Breakthroughs',
                  'description': 'BCIs could restore function for people with paralysis, sensory impairments, or neurological disorders.',
                  'category': 'Societal'
                },
                {
                  'id': 'privacy',
                  'name': 'Neural Privacy',
                  'description': 'BCIs could potentially access and record private thoughts, raising unprecedented privacy concerns.',
                  'category': 'Ethical'
                },
                {
                  'id': 'inequality',
                  'name': 'Cognitive Inequality',
                  'description': 'If enhancement applications develop, access might be limited to the wealthy, creating new forms of inequality.',
                  'category': 'Societal'
                },
                {
                  'id': 'identity',
                  'name': 'Human Identity',
                  'description': 'Direct brain-machine integration raises questions about autonomy, identity, and what it means to be human.',
                  'category': 'Ethical'
                },
                {
                  'id': 'security',
                  'name': 'Neural Security',
                  'description': 'BCIs could be vulnerable to hacking, potentially allowing unauthorized access to or control of neural functions.',
                  'category': 'Societal'
                },
                {
                  'id': 'economic',
                  'name': 'Economic Potential',
                  'description': 'BCI technologies could create new industries and applications, from healthcare to entertainment and productivity.',
                  'category': 'Economic'
                }
              ],
              'expertAnalysis': 'Brain-computer interfaces represent a frontier technology with profound implications for human health, cognition, and even our conception of humanity. The near-term impacts will primarily be medical, with promising applications for patients with paralysis, sensory impairments, and neurological disorders. Companies like Neuralink, Synchron, and Paradromics are advancing invasive BCI technology, while others develop non-invasive approaches with lower resolution but fewer risks. The ethical considerations are particularly complex, including unprecedented questions about neural privacy, identity, and autonomy. If BCIs advance to enhancement applications, they could create new forms of inequality between the enhanced and unenhanced, though this remains speculative. Security concerns are significant, as BCIs could potentially be vulnerable to hacking or unauthorized access. The regulatory landscape is still developing, with medical BCIs regulated as medical devices but less clarity for non-medical applications. Public perception and acceptance will be crucial factors in the technology\'s development trajectory. Overall, BCIs hold transformative potential, particularly for medical applications, but require careful governance to navigate the profound ethical questions they raise about the relationship between humans and technology.'
            },
            {
              'id': 'renewable_energy',
              'name': 'Advanced Renewable Energy',
              'description': 'Advanced renewable energy technologies harness natural processes to generate electricity with minimal environmental impact. Beyond established technologies like solar photovoltaics and wind turbines, emerging approaches include next-generation solar cells, advanced energy storage, floating offshore wind, enhanced geothermal systems, and marine energy technologies that capture energy from waves, tides, and ocean temperature differences.',
              'status': 'Developing',
              'keyFeatures': [
                'Increasingly cost-competitive with fossil fuels without subsidies',
                'Modular and scalable from small off-grid applications to utility-scale installations',
                'Zero direct emissions during operation',
                'Diverse technologies harnessing various natural energy flows',
                'Increasingly integrated with advanced storage and smart grid technologies'
              ],
              'currentApplications': [
                'Grid electricity generation with rapidly growing market share',
                'Off-grid power for remote locations and developing regions',
                'Distributed generation for resilience and energy independence',
                'Green hydrogen production for industrial processes and transportation',
                'Microgrids combining multiple renewable sources with storage',
                'Building-integrated renewable energy systems'
              ],
              'futurePossibilities': [
                'Perovskite and multi-junction solar cells with significantly higher efficiencies',
                'Floating offshore wind farms in deep water locations',
                'Grid-scale storage enabling 100% renewable electricity systems',
                'Enhanced geothermal systems accessing heat anywhere on Earth',
                'Commercially viable fusion energy',
                'Integrated energy systems combining multiple renewable sources with AI optimization'
              ],
              'potentialImpacts': [
                {
                  'id': 'climate',
                  'name': 'Climate Change Mitigation',
                  'description': 'Widespread adoption could dramatically reduce greenhouse gas emissions from the energy sector.',
                  'category': 'Environmental'
                },
                {
                  'id': 'air_quality',
                  'name': 'Air Quality Improvement',
                  'description': 'Replacing fossil fuels reduces air pollutants that cause respiratory and cardiovascular diseases.',
                  'category': 'Societal'
                },
                {
                  'id': 'economic_transformation',
                  'name': 'Energy Economy Transformation',
                  'description': 'Shift from fuel-based to technology-based energy systems changes economic models and geopolitics.',
                  'category': 'Economic'
                },
                {
                  'id': 'land_use',
                  'name': 'Land and Resource Use',
                  'description': 'Large-scale deployment requires significant land area and specific materials, with potential environmental impacts.',
                  'category': 'Environmental'
                },
                {
                  'id': 'energy_access',
                  'name': 'Energy Access',
                  'description': 'Distributed renewable systems could provide electricity to the 770 million people currently without access.',
                  'category': 'Societal'
                },
                {
                  'id': 'just_transition',
                  'name': 'Just Transition Challenges',
                  'description': 'Communities dependent on fossil fuel industries require support during the energy transition.',
                  'category': 'Ethical'
                }
              ],
              'expertAnalysis': 'Advanced renewable energy technologies represent one of the most important technological transformations of our time, with profound implications for climate change mitigation, economic systems, and geopolitics. The economics of renewables have improved dramatically, with solar and wind now the cheapest forms of new electricity generation in most markets. The environmental benefits extend beyond climate change mitigation to reduced air pollution, water conservation, and potentially reduced land disturbance compared to fossil fuel extraction. However, challenges remain in materials supply chains, intermittency management, and grid integration. Energy storage technologies are advancing rapidly but still require further cost reductions and performance improvements for full renewable integration. The economic transformation involves shifting from fuel-based to technology-based energy systems, changing business models and potentially reducing energy geopolitical tensions while creating new material supply dependencies. Social equity considerations include ensuring a just transition for fossil fuel workers and communities, and expanding energy access in developing regions. Policy frameworks remain crucial for accelerating deployment, with carbon pricing, renewable portfolio standards, and research funding all playing important roles. Overall, advanced renewable energy technologies offer a path to sustainable energy systems but require thoughtful implementation to maximize benefits and minimize unintended consequences.'
            }
          ]
        },
        isImplemented: true,
      ),
    ];
  }
}
