import 'package:flutter/material.dart';
import 'dart:math' as math;

/// A widget that visualizes one-step equations using a number line.
class InteractiveNumberLineEquationVisualizerWidget extends StatefulWidget {
  final Map<String, dynamic> data;
  final Function(bool)? onStateChanged;
  final Color primaryColor;
  final Color secondaryColor;
  final Color backgroundColor;
  final Color textColor;

  const InteractiveNumberLineEquationVisualizerWidget({
    super.key,
    this.data = const {},
    this.onStateChanged,
    this.primaryColor = Colors.blue,
    this.secondaryColor = Colors.orange,
    this.backgroundColor = Colors.white,
    this.textColor = Colors.black87,
  });

  @override
  State<InteractiveNumberLineEquationVisualizerWidget> createState() =>
      _InteractiveNumberLineEquationVisualizerWidgetState();
}

class _InteractiveNumberLineEquationVisualizerWidgetState
    extends State<InteractiveNumberLineEquationVisualizerWidget>
    with SingleTickerProviderStateMixin {
  // Animation controller
  late AnimationController _animationController;
  late Animation<double> _animation;

  // State variables
  bool _isCompleted = false;
  bool _isAnimating = false;
  int _currentStep = 0;
  int _currentEquationIndex = 0;
  String? _userAnswer;
  bool _showSolution = false;
  bool _isCorrect = false;
  String? _feedbackMessage;

  // Number line variables
  double _minValue = -10;
  double _maxValue = 20;
  double _startValue = 0;
  double _endValue = 0;
  double _currentValue = 0;
  String _operation = '';

  // Equation data
  List<EquationData> _equations = [];
  late EquationData _currentEquation;

  @override
  void initState() {
    super.initState();

    // Initialize animation controller
    _animationController = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 1500),
    );

    _animation = CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeInOut,
    );

    // Add listener to animation controller
    _animationController.addStatusListener((status) {
      if (status == AnimationStatus.completed) {
        setState(() {
          _currentStep++;
          if (_currentStep < _currentEquation.steps.length) {
            _setupStepAnimation();
            _animationController.reset();
            _animationController.forward();
          } else {
            _isAnimating = false;
          }
        });
      }
    });

    // Initialize equations
    _initializeEquations();
    _currentEquation = _equations[_currentEquationIndex];
    _setupNumberLineForCurrentEquation();
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  void _initializeEquations() {
    // Check if equations are provided in the data
    if (widget.data.containsKey('equations') &&
        widget.data['equations'] is List &&
        widget.data['equations'].isNotEmpty) {

      final equationsData = widget.data['equations'] as List;
      for (final eqData in equationsData) {
        if (eqData is Map<String, dynamic>) {
          final equation = EquationData.fromJson(eqData);
          _equations.add(equation);
        }
      }
    }

    // If no equations were provided, create default ones
    if (_equations.isEmpty) {
      _equations = [
        EquationData(
          initialEquation: 'x + 5 = 12',
          variableName: 'x',
          solution: '7',
          operation: 'subtraction',
          steps: [
            EquationStep(
              equation: 'x + 5 = 12',
              explanation: 'We start with the original equation.',
              operation: 'Original equation',
              startValue: 0,
              endValue: 12,
              operationValue: 5,
            ),
            EquationStep(
              equation: 'x + 5 - 5 = 12 - 5',
              explanation: 'To isolate the variable, we subtract 5 from both sides.',
              operation: 'Subtract 5 from both sides',
              startValue: 12,
              endValue: 7,
              operationValue: 5,
            ),
            EquationStep(
              equation: 'x = 7',
              explanation: 'Simplify to get the solution.',
              operation: 'Simplify',
              startValue: 7,
              endValue: 7,
              operationValue: 0,
            ),
          ],
        ),
        EquationData(
          initialEquation: 'x - 3 = 8',
          variableName: 'x',
          solution: '11',
          operation: 'addition',
          steps: [
            EquationStep(
              equation: 'x - 3 = 8',
              explanation: 'We start with the original equation.',
              operation: 'Original equation',
              startValue: 0,
              endValue: 8,
              operationValue: 3,
            ),
            EquationStep(
              equation: 'x - 3 + 3 = 8 + 3',
              explanation: 'To isolate the variable, we add 3 to both sides.',
              operation: 'Add 3 to both sides',
              startValue: 8,
              endValue: 11,
              operationValue: 3,
            ),
            EquationStep(
              equation: 'x = 11',
              explanation: 'Simplify to get the solution.',
              operation: 'Simplify',
              startValue: 11,
              endValue: 11,
              operationValue: 0,
            ),
          ],
        ),
        EquationData(
          initialEquation: '3x = 15',
          variableName: 'x',
          solution: '5',
          operation: 'division',
          steps: [
            EquationStep(
              equation: '3x = 15',
              explanation: 'We start with the original equation.',
              operation: 'Original equation',
              startValue: 0,
              endValue: 15,
              operationValue: 3,
            ),
            EquationStep(
              equation: '3x ÷ 3 = 15 ÷ 3',
              explanation: 'To isolate the variable, we divide both sides by 3.',
              operation: 'Divide both sides by 3',
              startValue: 15,
              endValue: 5,
              operationValue: 3,
            ),
            EquationStep(
              equation: 'x = 5',
              explanation: 'Simplify to get the solution.',
              operation: 'Simplify',
              startValue: 5,
              endValue: 5,
              operationValue: 0,
            ),
          ],
        ),
        EquationData(
          initialEquation: 'x/4 = 5',
          variableName: 'x',
          solution: '20',
          operation: 'multiplication',
          steps: [
            EquationStep(
              equation: 'x/4 = 5',
              explanation: 'We start with the original equation.',
              operation: 'Original equation',
              startValue: 0,
              endValue: 5,
              operationValue: 4,
            ),
            EquationStep(
              equation: 'x/4 × 4 = 5 × 4',
              explanation: 'To isolate the variable, we multiply both sides by 4.',
              operation: 'Multiply both sides by 4',
              startValue: 5,
              endValue: 20,
              operationValue: 4,
            ),
            EquationStep(
              equation: 'x = 20',
              explanation: 'Simplify to get the solution.',
              operation: 'Simplify',
              startValue: 20,
              endValue: 20,
              operationValue: 0,
            ),
          ],
        ),
      ];
    }
  }

  void _setupNumberLineForCurrentEquation() {
    // Set the min and max values for the number line based on the equation
    final solution = double.tryParse(_currentEquation.solution) ?? 0;

    // Adjust min and max values to ensure the solution is visible
    _minValue = math.min(-5, solution - 10);
    _maxValue = math.max(25, solution + 10);

    // Reset current step
    _currentStep = 0;

    // Setup initial values
    if (_currentEquation.steps.isNotEmpty) {
      final firstStep = _currentEquation.steps[0];
      _startValue = firstStep.startValue;
      _endValue = firstStep.endValue;
      _currentValue = _startValue;
      _operation = firstStep.operation;
    }
  }

  void _setupStepAnimation() {
    if (_currentStep < _currentEquation.steps.length) {
      final step = _currentEquation.steps[_currentStep];
      _startValue = step.startValue;
      _endValue = step.endValue;
      _currentValue = _startValue;
      _operation = step.operation;
    }
  }

  void _startAnimation() {
    if (_currentEquation.steps.isEmpty) return;

    setState(() {
      _currentStep = 0;
      _isAnimating = true;
      _showSolution = true;
      _setupStepAnimation();
    });

    _animationController.reset();
    _animationController.forward();
  }

  void _stopAnimation() {
    setState(() {
      _isAnimating = false;
    });
    _animationController.stop();
  }

  void _resetAnimation() {
    setState(() {
      _currentStep = 0;
      _isAnimating = false;
      _showSolution = false;
    });
    _animationController.reset();
  }

  void _nextEquation() {
    if (_currentEquationIndex < _equations.length - 1) {
      setState(() {
        _currentEquationIndex++;
        _currentEquation = _equations[_currentEquationIndex];
        _setupNumberLineForCurrentEquation();
        _resetAnimation();
        _userAnswer = null;
        _isCorrect = false;
        _feedbackMessage = null;
      });
    } else {
      // All equations completed
      setState(() {
        _isCompleted = true;
      });
      widget.onStateChanged?.call(true);
    }
  }

  void _previousEquation() {
    if (_currentEquationIndex > 0) {
      setState(() {
        _currentEquationIndex--;
        _currentEquation = _equations[_currentEquationIndex];
        _setupNumberLineForCurrentEquation();
        _resetAnimation();
        _userAnswer = null;
        _isCorrect = false;
        _feedbackMessage = null;
      });
    }
  }

  void _checkAnswer(String answer) {
    final isCorrect = answer.trim() == _currentEquation.solution.trim();

    setState(() {
      _userAnswer = answer;
      _isCorrect = isCorrect;

      if (isCorrect) {
        _feedbackMessage = 'Correct! ${_currentEquation.variableName} = ${_currentEquation.solution} is the solution.';
      } else {
        _feedbackMessage = 'Not quite. Try again or check the solution.';
      }
    });
  }

  void _resetWidget() {
    setState(() {
      _currentEquationIndex = 0;
      _currentEquation = _equations[_currentEquationIndex];
      _setupNumberLineForCurrentEquation();
      _resetAnimation();
      _userAnswer = null;
      _isCorrect = false;
      _feedbackMessage = null;
      _isCompleted = false;
    });
    widget.onStateChanged?.call(false);
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: widget.backgroundColor,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withOpacity(0.2),
            spreadRadius: 1,
            blurRadius: 3,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: _isCompleted ? _buildCompletionScreen() : _buildMainContent(),
    );
  }

  Widget _buildMainContent() {
    // Calculate current value based on animation
    if (_isAnimating) {
      _currentValue = _startValue + (_endValue - _startValue) * _animation.value;
    }

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Title and progress indicator
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Text(
              'Number Line Equation Visualizer',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
                color: widget.primaryColor,
              ),
            ),
            Text(
              'Equation ${_currentEquationIndex + 1}/${_equations.length}',
              style: TextStyle(
                fontSize: 14,
                color: widget.textColor.withOpacity(0.7),
              ),
            ),
          ],
        ),

        const SizedBox(height: 16),

        // Current equation
        Container(
          width: double.infinity,
          padding: const EdgeInsets.symmetric(vertical: 12, horizontal: 16),
          decoration: BoxDecoration(
            color: widget.primaryColor.withOpacity(0.1),
            borderRadius: BorderRadius.circular(8),
            border: Border.all(color: widget.primaryColor.withOpacity(0.3)),
          ),
          child: Text(
            _currentEquation.initialEquation,
            style: TextStyle(
              fontSize: 24,
              fontWeight: FontWeight.bold,
              color: widget.textColor,
            ),
            textAlign: TextAlign.center,
          ),
        ),

        const SizedBox(height: 16),

        // Current step explanation
        if (_showSolution && _currentStep < _currentEquation.steps.length) ...[
          Container(
            width: double.infinity,
            padding: const EdgeInsets.all(12),
            decoration: BoxDecoration(
              color: widget.secondaryColor.withOpacity(0.1),
              borderRadius: BorderRadius.circular(8),
              border: Border.all(color: widget.secondaryColor.withOpacity(0.3)),
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'Step ${_currentStep + 1}: ${_currentEquation.steps[_currentStep].operation}',
                  style: TextStyle(
                    fontWeight: FontWeight.bold,
                    color: widget.secondaryColor,
                  ),
                ),
                const SizedBox(height: 4),
                Text(
                  _currentEquation.steps[_currentStep].equation,
                  style: TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.w500,
                    color: widget.textColor,
                  ),
                ),
                const SizedBox(height: 4),
                Text(
                  _currentEquation.steps[_currentStep].explanation,
                  style: TextStyle(
                    color: widget.textColor.withOpacity(0.8),
                  ),
                ),
              ],
            ),
          ),
          const SizedBox(height: 16),
        ],

        // Number line visualization
        Container(
          height: 120,
          width: double.infinity,
          decoration: BoxDecoration(
            color: Colors.grey.shade100,
            borderRadius: BorderRadius.circular(8),
          ),
          child: CustomPaint(
            painter: NumberLinePainter(
              minValue: _minValue,
              maxValue: _maxValue,
              currentValue: _currentValue,
              targetValue: _endValue,
              lineColor: widget.primaryColor,
              markerColor: widget.secondaryColor,
              textColor: widget.textColor,
              isAnimating: _isAnimating,
              animationValue: _animation.value,
            ),
          ),
        ),

        const SizedBox(height: 24),

        // Instruction
        if (!_showSolution) ...[
          Text(
            'Solve for ${_currentEquation.variableName}:',
            style: TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.w500,
              color: widget.textColor,
            ),
          ),

          const SizedBox(height: 8),

          // User input area
          Row(
            children: [
              Expanded(
                child: TextField(
                  decoration: InputDecoration(
                    labelText: 'Your answer',
                    hintText: 'Enter the value of ${_currentEquation.variableName}',
                    border: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(8),
                    ),
                    suffixIcon: IconButton(
                      icon: const Icon(Icons.check_circle),
                      onPressed: () {
                        if (_userAnswer != null && _userAnswer!.isNotEmpty) {
                          _checkAnswer(_userAnswer!);
                        }
                      },
                    ),
                  ),
                  keyboardType: TextInputType.number,
                  onChanged: (value) {
                    setState(() {
                      _userAnswer = value;
                    });
                  },
                  onSubmitted: (value) {
                    if (value.isNotEmpty) {
                      _checkAnswer(value);
                    }
                  },
                ),
              ),
              const SizedBox(width: 8),
              ElevatedButton(
                onPressed: _startAnimation,
                style: ElevatedButton.styleFrom(
                  backgroundColor: widget.secondaryColor,
                  padding: const EdgeInsets.symmetric(vertical: 16),
                ),
                child: const Text('Show Solution'),
              ),
            ],
          ),

          // Feedback message
          if (_feedbackMessage != null)
            Padding(
              padding: const EdgeInsets.only(top: 8.0),
              child: Text(
                _feedbackMessage!,
                style: TextStyle(
                  color: _isCorrect ? Colors.green : Colors.red,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ),
        ] else if (!_isAnimating) ...[
          // Animation controls
          Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              ElevatedButton.icon(
                onPressed: _startAnimation,
                icon: const Icon(Icons.replay),
                label: const Text('Replay Animation'),
                style: ElevatedButton.styleFrom(
                  backgroundColor: widget.secondaryColor,
                ),
              ),
            ],
          ),
        ],

        const Spacer(),

        // Navigation buttons
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            // Previous button
            ElevatedButton(
              onPressed: _currentEquationIndex > 0 ? _previousEquation : null,
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.grey.shade200,
                foregroundColor: Colors.black87,
              ),
              child: const Text('Previous'),
            ),

            // Next button
            ElevatedButton(
              onPressed: (_isCorrect || _showSolution) ? _nextEquation : null,
              style: ElevatedButton.styleFrom(
                backgroundColor: widget.primaryColor,
                foregroundColor: Colors.white,
              ),
              child: Text(_currentEquationIndex < _equations.length - 1 ? 'Next' : 'Finish'),
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildCompletionScreen() {
    return Column(
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        Icon(
          Icons.check_circle,
          size: 80,
          color: Colors.green,
        ),
        const SizedBox(height: 24),
        Text(
          'Congratulations!',
          style: TextStyle(
            fontSize: 24,
            fontWeight: FontWeight.bold,
            color: widget.primaryColor,
          ),
        ),
        const SizedBox(height: 16),
        Text(
          'You\'ve completed all the number line equation visualizations!',
          textAlign: TextAlign.center,
          style: TextStyle(
            fontSize: 16,
            color: widget.textColor,
          ),
        ),
        const SizedBox(height: 32),
        ElevatedButton.icon(
          onPressed: _resetWidget,
          icon: const Icon(Icons.refresh),
          label: const Text('Practice Again'),
          style: ElevatedButton.styleFrom(
            backgroundColor: widget.secondaryColor,
            padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
          ),
        ),
      ],
    );
  }
}

/// Custom painter for drawing the number line
class NumberLinePainter extends CustomPainter {
  final double minValue;
  final double maxValue;
  final double currentValue;
  final double targetValue;
  final Color lineColor;
  final Color markerColor;
  final Color textColor;
  final bool isAnimating;
  final double animationValue;

  NumberLinePainter({
    required this.minValue,
    required this.maxValue,
    required this.currentValue,
    required this.targetValue,
    required this.lineColor,
    required this.markerColor,
    required this.textColor,
    required this.isAnimating,
    required this.animationValue,
  });

  @override
  void paint(Canvas canvas, Size size) {
    final double width = size.width;
    final double height = size.height;
    final double lineY = height / 2;
    final double tickHeight = 10;
    final double markerRadius = 12;

    // Calculate the range and scale
    final double range = maxValue - minValue;
    final double pixelsPerUnit = width / range;

    // Draw the main line
    final Paint linePaint = Paint()
      ..color = lineColor
      ..strokeWidth = 2
      ..style = PaintingStyle.stroke;

    canvas.drawLine(
      Offset(0, lineY),
      Offset(width, lineY),
      linePaint,
    );

    // Draw tick marks and labels
    final TextStyle labelStyle = TextStyle(
      color: textColor,
      fontSize: 12,
    );
    final TextPainter textPainter = TextPainter(
      textDirection: TextDirection.ltr,
    );

    // Determine tick interval based on range
    int tickInterval = 1;
    if (range > 50) {
      tickInterval = 10;
    } else if (range > 20) {
      tickInterval = 5;
    } else if (range > 10) {
      tickInterval = 2;
    }

    for (int i = minValue.floor(); i <= maxValue.ceil(); i += tickInterval) {
      if (i < minValue || i > maxValue) continue;

      final double x = (i - minValue) * pixelsPerUnit;

      // Draw tick mark
      canvas.drawLine(
        Offset(x, lineY - tickHeight / 2),
        Offset(x, lineY + tickHeight / 2),
        linePaint,
      );

      // Draw label
      textPainter.text = TextSpan(
        text: i.toString(),
        style: labelStyle,
      );
      textPainter.layout();
      textPainter.paint(
        canvas,
        Offset(x - textPainter.width / 2, lineY + tickHeight),
      );
    }

    // Draw the current value marker
    final Paint markerPaint = Paint()
      ..color = markerColor
      ..style = PaintingStyle.fill;

    final double markerX = (currentValue - minValue) * pixelsPerUnit;
    canvas.drawCircle(
      Offset(markerX, lineY),
      markerRadius,
      markerPaint,
    );

    // Draw the current value label
    textPainter.text = TextSpan(
      text: currentValue.toStringAsFixed(1),
      style: TextStyle(
        color: Colors.white,
        fontSize: 10,
        fontWeight: FontWeight.bold,
      ),
    );
    textPainter.layout();
    textPainter.paint(
      canvas,
      Offset(markerX - textPainter.width / 2, lineY - textPainter.height / 2),
    );

    // Draw arrow to target if animating
    if (isAnimating) {
      final double targetX = (targetValue - minValue) * pixelsPerUnit;
      final double arrowLength = (targetX - markerX) * animationValue;

      // Draw arrow line
      final Paint arrowPaint = Paint()
        ..color = markerColor.withOpacity(0.7)
        ..strokeWidth = 2
        ..style = PaintingStyle.stroke;

      canvas.drawLine(
        Offset(markerX, lineY - 20),
        Offset(markerX + arrowLength, lineY - 20),
        arrowPaint,
      );

      // Draw arrow head
      if (arrowLength != 0) {
        final double arrowHeadSize = 6;
        final double direction = arrowLength > 0 ? 1 : -1;

        final Path arrowHeadPath = Path();
        arrowHeadPath.moveTo(markerX + arrowLength, lineY - 20);
        arrowHeadPath.lineTo(markerX + arrowLength - direction * arrowHeadSize, lineY - 20 - arrowHeadSize);
        arrowHeadPath.lineTo(markerX + arrowLength - direction * arrowHeadSize, lineY - 20 + arrowHeadSize);
        arrowHeadPath.close();

        canvas.drawPath(arrowHeadPath, Paint()..color = markerColor.withOpacity(0.7));
      }
    }
  }

  @override
  bool shouldRepaint(NumberLinePainter oldDelegate) {
    return oldDelegate.currentValue != currentValue ||
        oldDelegate.targetValue != targetValue ||
        oldDelegate.isAnimating != isAnimating ||
        oldDelegate.animationValue != animationValue;
  }
}

/// Data class for equation information
class EquationData {
  final String initialEquation;
  final String variableName;
  final String solution;
  final String operation;
  final List<EquationStep> steps;

  EquationData({
    required this.initialEquation,
    required this.variableName,
    required this.solution,
    required this.operation,
    required this.steps,
  });

  factory EquationData.fromJson(Map<String, dynamic> json) {
    final steps = <EquationStep>[];
    if (json.containsKey('steps') && json['steps'] is List) {
      for (final stepData in json['steps']) {
        if (stepData is Map<String, dynamic>) {
          steps.add(EquationStep.fromJson(stepData));
        }
      }
    }

    return EquationData(
      initialEquation: json['initialEquation'] ?? 'x + 5 = 10',
      variableName: json['variableName'] ?? 'x',
      solution: json['solution'] ?? '5',
      operation: json['operation'] ?? 'subtraction',
      steps: steps,
    );
  }
}

/// Data class for equation solution steps
class EquationStep {
  final String equation;
  final String explanation;
  final String operation;
  final double startValue;
  final double endValue;
  final double operationValue;

  EquationStep({
    required this.equation,
    required this.explanation,
    required this.operation,
    required this.startValue,
    required this.endValue,
    required this.operationValue,
  });

  factory EquationStep.fromJson(Map<String, dynamic> json) {
    return EquationStep(
      equation: json['equation'] ?? '',
      explanation: json['explanation'] ?? '',
      operation: json['operation'] ?? '',
      startValue: json['startValue'] ?? 0.0,
      endValue: json['endValue'] ?? 0.0,
      operationValue: json['operationValue'] ?? 0.0,
    );
  }
}
