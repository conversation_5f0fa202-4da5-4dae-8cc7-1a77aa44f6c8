import 'package:flutter/material.dart';
import 'dart:math' as math;

/// A widget that helps users translate word problems into algebraic equations
class InteractiveWordProblemTranslatorWidget extends StatefulWidget {
  /// The data for this widget
  final Map<String, dynamic> data;

  /// Optional callback when the widget state changes
  final Function(bool isCompleted)? onStateChanged;

  const InteractiveWordProblemTranslatorWidget({
    Key? key,
    required this.data,
    this.onStateChanged,
  }) : super(key: key);

  /// Factory constructor to create the widget from JSON data
  factory InteractiveWordProblemTranslatorWidget.fromData(Map<String, dynamic> data) {
    return InteractiveWordProblemTranslatorWidget(
      data: data,
    );
  }

  @override
  State<InteractiveWordProblemTranslatorWidget> createState() => _InteractiveWordProblemTranslatorWidgetState();
}

class _InteractiveWordProblemTranslatorWidgetState extends State<InteractiveWordProblemTranslatorWidget> {
  // Word problem data
  late List<WordProblem> _problems;
  
  // Current state
  int _currentProblemIndex = 0;
  bool _isCompleted = false;
  bool _showHint = false;
  bool _showSolution = false;
  String? _errorMessage;
  
  // User input
  final TextEditingController _equationController = TextEditingController();
  final TextEditingController _variableDefinitionController = TextEditingController();
  
  // UI customization
  late Color _primaryColor;
  late Color _successColor;
  late Color _errorColor;
  late Color _hintColor;
  late Color _neutralColor;

  @override
  void initState() {
    super.initState();
    
    // Parse problems
    _problems = [];
    final problemsData = widget.data['problems'] as List<dynamic>? ?? [];
    for (final problem in problemsData) {
      if (problem is Map<String, dynamic>) {
        _problems.add(WordProblem(
          problemText: problem['problem_text'] ?? '',
          variableDefinition: problem['variable_definition'] ?? '',
          equation: problem['equation'] ?? '',
          solution: problem['solution'] ?? '',
          hint: problem['hint'] ?? '',
          explanation: problem['explanation'] ?? '',
        ));
      }
    }
    
    // If no problems provided, create default problems
    if (_problems.isEmpty) {
      _problems = [
        WordProblem(
          problemText: 'A number plus 7 equals 15. Find the number.',
          variableDefinition: 'Let x = the unknown number',
          equation: 'x + 7 = 15',
          solution: '8',
          hint: 'Identify the unknown quantity and assign a variable to it. Then translate the word "plus" to the + operation.',
          explanation: 'We let x represent the unknown number. The phrase "a number plus 7" translates to "x + 7". The phrase "equals 15" translates to "= 15". So the equation is x + 7 = 15.',
        ),
        WordProblem(
          problemText: 'When 5 is subtracted from twice a number, the result is 13. Find the number.',
          variableDefinition: 'Let x = the unknown number',
          equation: '2x - 5 = 13',
          solution: '9',
          hint: 'Identify the unknown quantity and assign a variable to it. "Twice a number" means 2 times the variable.',
          explanation: 'We let x represent the unknown number. "Twice a number" translates to "2x". "5 is subtracted from" means "2x - 5". "The result is 13" translates to "= 13". So the equation is 2x - 5 = 13.',
        ),
        WordProblem(
          problemText: 'The sum of three consecutive integers is 42. Find the smallest integer.',
          variableDefinition: 'Let x = the smallest integer',
          equation: 'x + (x + 1) + (x + 2) = 42',
          solution: '13',
          hint: 'If x is the smallest integer, then the next consecutive integers are x + 1 and x + 2.',
          explanation: 'We let x represent the smallest integer. The next consecutive integers are x + 1 and x + 2. The sum of these three integers is x + (x + 1) + (x + 2) = 42.',
        ),
      ];
    }
    
    // Initialize colors
    _primaryColor = _parseColor(widget.data['primary_color'], Colors.blue);
    _successColor = _parseColor(widget.data['success_color'], Colors.green);
    _errorColor = _parseColor(widget.data['error_color'], Colors.red);
    _hintColor = _parseColor(widget.data['hint_color'], Colors.orange);
    _neutralColor = _parseColor(widget.data['neutral_color'], Colors.grey.shade200);
  }

  @override
  void dispose() {
    _equationController.dispose();
    _variableDefinitionController.dispose();
    super.dispose();
  }

  // Helper method to parse color from string
  Color _parseColor(dynamic colorValue, Color defaultColor) {
    if (colorValue == null) return defaultColor;
    if (colorValue is String) {
      try {
        return Color(int.parse(colorValue.replaceAll('#', '0xFF')));
      } catch (e) {
        return defaultColor;
      }
    }
    return defaultColor;
  }
  
  // Check the user's answer
  void _checkAnswer() {
    final currentProblem = _problems[_currentProblemIndex];
    
    // Check variable definition
    final variableDefinition = _variableDefinitionController.text.trim();
    if (variableDefinition.isEmpty) {
      setState(() {
        _errorMessage = 'Please define your variable(s)';
      });
      return;
    }
    
    // Check equation
    final equation = _equationController.text.trim();
    if (equation.isEmpty) {
      setState(() {
        _errorMessage = 'Please enter an equation';
      });
      return;
    }
    
    // Check if the equation is correct
    // This is a simplified check - in a real app, you would use a more sophisticated
    // equation parser and checker that can handle equivalent equations
    final isCorrect = _isEquivalentEquation(equation, currentProblem.equation);
    
    setState(() {
      if (isCorrect) {
        _showSolution = true;
        _errorMessage = null;
      } else {
        _errorMessage = 'Your equation doesn\'t match the expected solution. Try again or check the hint.';
      }
    });
  }
  
  // Check if two equations are equivalent
  // This is a simplified implementation - in a real app, you would use a proper
  // equation parser and solver to check equivalence
  bool _isEquivalentEquation(String userEquation, String correctEquation) {
    // Remove all spaces and convert to lowercase for comparison
    final normalizedUserEq = userEquation.replaceAll(' ', '').toLowerCase();
    final normalizedCorrectEq = correctEquation.replaceAll(' ', '').toLowerCase();
    
    // Direct match
    if (normalizedUserEq == normalizedCorrectEq) {
      return true;
    }
    
    // For demo purposes, we'll also accept some common variations
    // In a real implementation, you would use a proper equation parser
    
    // Check if equations are the same but with sides swapped
    // e.g., "x + 5 = 10" is equivalent to "10 = x + 5"
    final userParts = normalizedUserEq.split('=');
    final correctParts = normalizedCorrectEq.split('=');
    
    if (userParts.length == 2 && correctParts.length == 2) {
      if (userParts[0] == correctParts[1] && userParts[1] == correctParts[0]) {
        return true;
      }
    }
    
    // For demo purposes, we'll also accept if the user's equation is in the correct format
    // but with different variable names
    // e.g., if the correct equation is "x + 7 = 15", we'll also accept "n + 7 = 15"
    
    // In a real implementation, you would use a proper equation parser and solver
    // to check if the equations are mathematically equivalent
    
    return false;
  }
  
  // Move to the next problem
  void _nextProblem() {
    if (_currentProblemIndex < _problems.length - 1) {
      setState(() {
        _currentProblemIndex++;
        _resetProblemState();
      });
    } else {
      setState(() {
        _isCompleted = true;
      });
      
      // Notify parent about completion
      widget.onStateChanged?.call(true);
    }
  }
  
  // Reset the state for the current problem
  void _resetProblemState() {
    _equationController.clear();
    _variableDefinitionController.clear();
    _showHint = false;
    _showSolution = false;
    _errorMessage = null;
  }
  
  // Toggle hint visibility
  void _toggleHint() {
    setState(() {
      _showHint = !_showHint;
    });
  }
  
  // Reset the widget
  void _reset() {
    setState(() {
      _currentProblemIndex = 0;
      _isCompleted = false;
      _resetProblemState();
    });
    
    widget.onStateChanged?.call(false);
  }

  @override
  Widget build(BuildContext context) {
    final currentProblem = _problems[_currentProblemIndex];
    
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withOpacity(0.2),
            spreadRadius: 1,
            blurRadius: 3,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Title
          Text(
            'Word Problem Translator',
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
              color: _primaryColor,
            ),
          ),
          
          const SizedBox(height: 12),
          
          // Problem text
          Container(
            padding: const EdgeInsets.all(12),
            decoration: BoxDecoration(
              color: Colors.grey.shade50,
              borderRadius: BorderRadius.circular(8),
              border: Border.all(color: Colors.grey.shade300),
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'Problem ${_currentProblemIndex + 1} of ${_problems.length}:',
                  style: TextStyle(
                    fontWeight: FontWeight.bold,
                    color: _primaryColor,
                  ),
                ),
                const SizedBox(height: 8),
                Text(
                  currentProblem.problemText,
                  style: const TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
          ),
          
          const SizedBox(height: 16),
          
          if (_isCompleted) ...[
            // Completion message
            Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: _successColor.withOpacity(0.1),
                borderRadius: BorderRadius.circular(8),
                border: Border.all(color: _successColor),
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    children: [
                      Icon(Icons.check_circle, color: _successColor),
                      const SizedBox(width: 8),
                      Text(
                        'All Problems Completed!',
                        style: TextStyle(
                          fontWeight: FontWeight.bold,
                          color: _successColor,
                          fontSize: 16,
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 8),
                  const Text(
                    'You have successfully translated all the word problems into equations.',
                    style: TextStyle(fontSize: 14),
                  ),
                  const SizedBox(height: 12),
                  ElevatedButton(
                    onPressed: _reset,
                    style: ElevatedButton.styleFrom(
                      backgroundColor: _successColor,
                      foregroundColor: Colors.white,
                      padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(8),
                      ),
                    ),
                    child: const Text('Start Over'),
                  ),
                ],
              ),
            ),
          ] else ...[
            // Variable definition input
            Text(
              'Define your variable(s):',
              style: const TextStyle(fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 8),
            TextField(
              controller: _variableDefinitionController,
              decoration: InputDecoration(
                hintText: 'e.g., Let x = the unknown number',
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(8),
                ),
                focusedBorder: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(8),
                  borderSide: BorderSide(color: _primaryColor, width: 2),
                ),
              ),
            ),
            
            const SizedBox(height: 16),
            
            // Equation input
            Text(
              'Enter your equation:',
              style: const TextStyle(fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 8),
            TextField(
              controller: _equationController,
              decoration: InputDecoration(
                hintText: 'e.g., x + 7 = 15',
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(8),
                ),
                focusedBorder: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(8),
                  borderSide: BorderSide(color: _primaryColor, width: 2),
                ),
              ),
            ),
            
            if (_errorMessage != null) ...[
              const SizedBox(height: 8),
              Text(
                _errorMessage!,
                style: TextStyle(color: _errorColor, fontStyle: FontStyle.italic),
              ),
            ],
            
            const SizedBox(height: 16),
            
            // Action buttons
            Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                // Check button
                if (!_showSolution)
                  ElevatedButton(
                    onPressed: _checkAnswer,
                    style: ElevatedButton.styleFrom(
                      backgroundColor: _primaryColor,
                      foregroundColor: Colors.white,
                      padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(8),
                      ),
                    ),
                    child: const Text('Check'),
                  ),
                
                // Next button
                if (_showSolution)
                  ElevatedButton(
                    onPressed: _nextProblem,
                    style: ElevatedButton.styleFrom(
                      backgroundColor: _primaryColor,
                      foregroundColor: Colors.white,
                      padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(8),
                      ),
                    ),
                    child: const Text('Next Problem'),
                  ),
                
                const SizedBox(width: 16),
                
                // Hint button
                OutlinedButton(
                  onPressed: _toggleHint,
                  style: OutlinedButton.styleFrom(
                    foregroundColor: _hintColor,
                    side: BorderSide(color: _hintColor),
                    padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(8),
                    ),
                  ),
                  child: Text(_showHint ? 'Hide Hint' : 'Show Hint'),
                ),
              ],
            ),
            
            if (_showHint) ...[
              const SizedBox(height: 16),
              Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: _hintColor.withOpacity(0.1),
                  borderRadius: BorderRadius.circular(8),
                  border: Border.all(color: _hintColor),
                ),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      children: [
                        Icon(Icons.lightbulb, color: _hintColor),
                        const SizedBox(width: 8),
                        Text(
                          'Hint',
                          style: TextStyle(
                            fontWeight: FontWeight.bold,
                            color: _hintColor,
                            fontSize: 16,
                          ),
                        ),
                      ],
                    ),
                    const SizedBox(height: 8),
                    Text(
                      currentProblem.hint,
                      style: const TextStyle(fontSize: 14),
                    ),
                  ],
                ),
              ),
            ],
            
            if (_showSolution) ...[
              const SizedBox(height: 16),
              Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: _successColor.withOpacity(0.1),
                  borderRadius: BorderRadius.circular(8),
                  border: Border.all(color: _successColor),
                ),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      children: [
                        Icon(Icons.check_circle, color: _successColor),
                        const SizedBox(width: 8),
                        Text(
                          'Correct!',
                          style: TextStyle(
                            fontWeight: FontWeight.bold,
                            color: _successColor,
                            fontSize: 16,
                          ),
                        ),
                      ],
                    ),
                    const SizedBox(height: 8),
                    Text(
                      'Variable definition: ${currentProblem.variableDefinition}',
                      style: const TextStyle(
                        fontSize: 14,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const SizedBox(height: 4),
                    Text(
                      'Equation: ${currentProblem.equation}',
                      style: const TextStyle(
                        fontSize: 14,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const SizedBox(height: 4),
                    Text(
                      'Solution: ${currentProblem.solution}',
                      style: const TextStyle(
                        fontSize: 14,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const SizedBox(height: 8),
                    Text(
                      'Explanation: ${currentProblem.explanation}',
                      style: const TextStyle(fontSize: 14),
                    ),
                  ],
                ),
              ),
            ],
          ],
          
          // Widget name tag
          if (widget.data['showNameTag'] ?? true)
            Padding(
              padding: const EdgeInsets.only(top: 8),
              child: Text(
                'InteractiveWordProblemTranslatorWidget',
                style: TextStyle(
                  color: Colors.grey[600],
                  fontSize: 12,
                  fontStyle: FontStyle.italic,
                ),
              ),
            ),
        ],
      ),
    );
  }
}

/// Represents a word problem
class WordProblem {
  final String problemText;
  final String variableDefinition;
  final String equation;
  final String solution;
  final String hint;
  final String explanation;
  
  WordProblem({
    required this.problemText,
    required this.variableDefinition,
    required this.equation,
    required this.solution,
    required this.hint,
    required this.explanation,
  });
}
