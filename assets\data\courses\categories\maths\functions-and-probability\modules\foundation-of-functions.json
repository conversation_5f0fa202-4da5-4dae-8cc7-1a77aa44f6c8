{"id": "foundation-of-functions", "title": "THE FOUNDATION OF FUNCTIONS", "description": "Establish the core concept of functions as rules linking inputs and outputs.", "order": 1, "lessons": [{"id": "input-output-rules", "title": "Input, Output, and Rules", "description": "Visualize functions as machines transforming inputs.", "order": 1, "type": "interactive_lesson", "estimatedTimeMinutes": 10, "contentBlocks": [{"id": "ior-screen1-intro", "type": "lesson_screen", "order": 1, "estimatedTimeSeconds": 30, "content": {"headline": "Functions: The Magical Input-Output Machines", "body_md": "Think of a function as a magical machine that takes an input, applies a specific rule, and produces exactly one output. Like a toaster that turns bread (input) into toast (output), or a coffee machine that transforms water and beans into your morning brew!\n\nFunctions are the building blocks of mathematics and appear everywhere in our daily lives.", "visual": {"type": "giphy_search", "value": "function machine mathematics animated"}, "interactive_element": {"type": "button", "text": "Let's Explore the Magic of Functions!", "action": "next_screen"}}}, {"id": "ior-screen2-function-machine", "type": "lesson_screen", "order": 2, "estimatedTimeSeconds": 60, "content": {"headline": "The Function Machine: One Input, One Output", "body_md": "A function takes an input value (x), applies a specific rule, and produces exactly one output value (y). The key characteristic of a function is that each input must lead to exactly one output - no more, no less!\n\nLet's try a simple function: **f(x) = 2x + 3**\n\nThis means: 'Take the input, multiply it by 2, then add 3'. Try different inputs in our function machine below!", "visual": {"type": "giphy_search", "value": "function machine mathematics colorful"}, "interactive_element": {"type": "interactive_function_machine", "function_rule_display": "f(x) = 2x + 3", "function_logic": "return 2 * input + 3;"}}}, {"id": "ior-screen3-function-examples", "type": "lesson_screen", "order": 3, "estimatedTimeSeconds": 60, "content": {"headline": "Functions in the Real World", "body_md": "Functions are everywhere in our daily lives! Here are some fascinating real-world examples:\n\n- **Temperature conversion**: F = 1.8C + 32 (converts Celsius to Fahrenheit)\n- **Area of a circle**: A = πr² (calculates area from radius)\n- **Cost of apples**: Cost = $2.50 × (number of pounds)\n- **Distance traveled**: Distance = Speed × Time\n- **Smartphone battery life**: Time remaining = Battery% × (Full battery life ÷ 100)\n\nEach of these follows the same pattern: one input leads to exactly one output through a specific rule.", "visual": {"type": "unsplash_search", "value": "mathematics real world application colorful"}, "interactive_element": {"type": "multiple_choice_text", "question": "Which of these relationships is NOT a function?", "options": [{"id": "a", "text": "The relationship between a person's age and their height", "is_correct": false, "feedback_incorrect": "This is actually a function! Each person has exactly one height at a given age (even though different people of the same age may have different heights)."}, {"id": "b", "text": "The relationship between a number and its square root", "is_correct": false, "feedback_incorrect": "This is a function! Each positive number has exactly one principal square root (the positive one)."}, {"id": "c", "text": "The relationship between a person and their siblings", "is_correct": true, "feedback_correct": "Correct! This is not a function because one input (a person) can have multiple outputs (different siblings). Functions must have exactly one output for each input."}, {"id": "d", "text": "The relationship between temperature in Celsius and Fahrenheit", "is_correct": false, "feedback_incorrect": "This is a function! Each Celsius temperature converts to exactly one Fahrenheit temperature using the formula F = 1.8C + 32."}]}}}, {"id": "ior-screen4-function-machine-interactive", "type": "lesson_screen", "order": 4, "estimatedTimeSeconds": 90, "content": {"headline": "Your Turn: Be the Function Machine!", "body_md": "Now it's your turn to be the function machine! Apply the given rule to each input value. Remember the steps:\n\n1. Take the input value (x)\n2. Substitute it into the function rule\n3. Follow the order of operations (multiply/divide first, then add/subtract)\n4. Calculate the final output", "visual": {"type": "giphy_search", "value": "math calculation animated"}, "interactive_element": {"type": "interactive_expression_evaluator", "overall_prompt": "Calculate the output for each function and input value below:", "tasks": [{"expression_display": "f(x) = 3x - 1, x = 5", "correct_value": "14", "solution_steps": ["f(5) = 3(5) - 1", "f(5) = 15 - 1", "f(5) = 14"], "feedback_correct": "Excellent! You correctly calculated f(5) = 3(5) - 1 = 15 - 1 = 14", "feedback_incorrect": "Not quite. Remember to multiply first: 3(5) = 15, then subtract: 15 - 1 = 14"}, {"expression_display": "g(x) = x² + 2x, x = 3", "correct_value": "15", "solution_steps": ["g(3) = 3² + 2(3)", "g(3) = 9 + 6", "g(3) = 15"], "feedback_correct": "Perfect! You correctly calculated g(3) = 3² + 2(3) = 9 + 6 = 15", "feedback_incorrect": "Try again. First square 3 to get 9, then multiply 2(3) = 6, then add: 9 + 6 = 15"}, {"expression_display": "h(x) = 2x² - 5x + 3, x = 2", "correct_value": "5", "solution_steps": ["h(2) = 2(2)² - 5(2) + 3", "h(2) = 2(4) - 10 + 3", "h(2) = 8 - 10 + 3", "h(2) = 5"], "feedback_correct": "Excellent work! You correctly calculated h(2) = 2(2)² - 5(2) + 3 = 2(4) - 10 + 3 = 8 - 10 + 3 = 5", "feedback_incorrect": "Let's break it down: First calculate 2² = 4, then 2(4) = 8, then 5(2) = 10, then 8 - 10 + 3 = 5"}], "input_type": "text", "check_button_text": "Check My Answers", "show_solution_button_text": "Show Solutions"}}}, {"id": "ior-screen5-conclusion", "type": "lesson_screen", "order": 5, "estimatedTimeSeconds": 30, "content": {"headline": "Functions: The Building Blocks of Mathematics", "body_md": "Fantastic work! You've mastered the fundamental concept of functions as input-output machines. Let's recap what you've learned:\n\n- A function is a rule that assigns exactly one output to each input\n- Functions can be represented using function notation like f(x) = 2x + 3\n- Real-world relationships can often be modeled using functions\n- Not all relationships are functions (some have multiple outputs for one input)\n\nThis foundational knowledge will help us explore more complex mathematical relationships in the upcoming lessons. Get ready to discover how functions can be represented in different ways!", "visual": {"type": "unsplash_search", "value": "mathematics colorful success"}, "interactive_element": {"type": "button", "text": "Continue to 'Representing Relationships'", "action": "next_lesson"}}}]}, {"id": "representing-relationships", "title": "Representing Relationships", "description": "Explore functions through words, tables, and graphs.", "order": 2, "type": "interactive_lesson", "estimatedTimeMinutes": 10, "contentBlocks": [{"id": "rr-screen1-intro", "type": "lesson_screen", "order": 1, "estimatedTimeSeconds": 30, "content": {"headline": "The Many Faces of Functions", "body_md": "Just like a story can be told through words, pictures, or film, functions can be represented in multiple ways: words, equations, tables, and graphs. Each representation gives us a different perspective on the same relationship, highlighting different aspects of the function's behavior.\n\nThink of these different representations as different languages for expressing the same mathematical idea!", "visual": {"type": "giphy_search", "value": "function representation math colorful animated"}, "interactive_element": {"type": "button", "text": "Let's Explore the Different Faces of Functions!", "action": "next_screen"}}}, {"id": "rr-screen2-words-to-equation", "type": "lesson_screen", "order": 2, "estimatedTimeSeconds": 60, "content": {"headline": "From Words to Equations: Translating Real Life", "body_md": "Let's start with a verbal description from everyday life:\n\n**'The cost of a taxi ride is $3.00 plus $2.50 per mile.'**\n\nWe can translate this into a mathematical equation: **C(m) = 3 + 2.5m**\n\nWhere:\n- C is the cost function (in dollars)\n- m is the input variable (distance in miles)\n- 3 represents the fixed starting fee ($3.00)\n- 2.5 represents the rate per mile ($2.50)\n\nThis equation now lets us calculate the cost for any distance!", "visual": {"type": "unsplash_search", "value": "taxi meter night city"}, "interactive_element": {"type": "interactive_expression_evaluator", "overall_prompt": "Use the taxi cost function C(m) = 3 + 2.5m to calculate the following:", "tasks": [{"expression_display": "Cost for a 10-mile ride", "correct_value": "28", "solution_steps": ["C(10) = 3 + 2.5(10)", "C(10) = 3 + 25", "C(10) = $28"], "feedback_correct": "Perfect! C(10) = 3 + 2.5(10) = 3 + 25 = $28", "feedback_incorrect": "Try again. Substitute m = 10 into C(m) = 3 + 2.5m and calculate."}, {"expression_display": "Cost for a 4-mile ride", "correct_value": "13", "solution_steps": ["C(4) = 3 + 2.5(4)", "C(4) = 3 + 10", "C(4) = $13"], "feedback_correct": "Excellent! C(4) = 3 + 2.5(4) = 3 + 10 = $13", "feedback_incorrect": "Not quite. Calculate C(4) = 3 + 2.5(4) = 3 + 10 = $13"}], "input_type": "text", "check_button_text": "Check My Answers", "show_solution_button_text": "Show Solutions"}}}, {"id": "rr-screen3-tables", "type": "lesson_screen", "order": 3, "estimatedTimeSeconds": 60, "content": {"headline": "Representing Functions with Tables: Seeing the Pattern", "body_md": "Tables are a powerful way to represent functions by showing specific input-output pairs. They're especially useful for:\n\n- Spotting patterns and trends\n- Working with data from real-world measurements\n- Finding function values without using formulas\n\nLet's look at our taxi function C(m) = 3 + 2.5m in table form:\n\n| Miles (m) | Cost C(m) | Calculation |\n|-----------|----------|-------------|\n| 0 | $3.00 | 3 + 2.5(0) = 3 |\n| 1 | $5.50 | 3 + 2.5(1) = 5.50 |\n| 2 | $8.00 | 3 + 2.5(2) = 8 |\n| 5 | $15.50 | 3 + 2.5(5) = 15.50 |\n\nDo you notice the pattern? Each time we increase the distance by 1 mile, the cost increases by $2.50!", "visual": {"type": "giphy_search", "value": "data table pattern animated"}, "interactive_element": {"type": "multiple_choice_text", "question": "Based on the pattern in the table, what would be the cost for 8 miles?", "options": [{"id": "a", "text": "$20.00", "is_correct": false, "feedback_incorrect": "Not quite. Remember that the cost increases by $2.50 per mile, plus there's the initial $3.00 fee."}, {"id": "b", "text": "$23.00", "is_correct": true, "feedback_correct": "Excellent! C(8) = 3 + 2.5(8) = 3 + 20 = $23. You can also find this by continuing the pattern in the table, adding $2.50 for each mile after 5 miles."}, {"id": "c", "text": "$24.50", "is_correct": false, "feedback_incorrect": "Not correct. Check your calculation again. For 8 miles, we have C(8) = 3 + 2.5(8) = 3 + 20 = $23."}, {"id": "d", "text": "$27.00", "is_correct": false, "feedback_incorrect": "That's not right. Make sure you're using the correct rate of $2.50 per mile plus the $3.00 initial fee."}]}}}, {"id": "rr-screen4-multiple-representations", "type": "lesson_screen", "order": 4, "estimatedTimeSeconds": 90, "content": {"headline": "Exploring Multiple Representations of Functions", "body_md": "Let's explore how the same function can be represented in different ways! Each representation highlights different aspects of the function:\n\n- **Equation**: Shows the precise mathematical rule\n- **Table**: Shows specific input-output pairs\n- **Graph**: Provides a visual overview of the function's behavior\n- **Verbal**: Describes the function in everyday language\n\nUse the interactive tool below to switch between different representations of the same function. Try all the different functions to see how their representations compare!", "visual": {"type": "giphy_search", "value": "function representations math"}, "interactive_element": {"type": "interactive_function_representation_tool", "primary_color": "#2196F3", "secondary_color": "#FF9800", "accent_color": "#4CAF50", "background_color": "#FFFFFF", "text_color": "#212121", "predefined_functions": [{"name": "Linear Function", "equation": "f(x) = 2x + 3", "description": "A linear function with slope 2 and y-intercept 3. This type of function creates a straight line when graphed. Linear functions are used to model constant rates of change, like taxi fares or simple interest."}, {"name": "Quadratic Function", "equation": "f(x) = x² - 2x + 1", "description": "A quadratic function that forms a parabola when graphed. This particular function has a minimum value at x = 1. Quadratic functions model many real-world phenomena like projectile motion, profit optimization, and area relationships."}, {"name": "Absolute Value Function", "equation": "f(x) = |x|", "description": "The absolute value function returns the distance of x from zero, regardless of whether x is positive or negative. It creates a V-shaped graph and is used in applications involving distance, error margins, and tolerance ranges."}]}}}, {"id": "rr-screen5-graphs", "type": "lesson_screen", "order": 5, "estimatedTimeSeconds": 90, "content": {"headline": "Visualizing Functions with Graphs: The Complete Picture", "body_md": "Graphs are perhaps the most powerful way to represent functions because they give us a complete visual picture at a glance. They show the relationship between inputs (x-axis) and outputs (y-axis) across the entire domain.\n\nFor our taxi function C(m) = 3 + 2.5m, the graph is a straight line with:\n\n- **y-intercept of 3**: This is where the line crosses the y-axis (when m = 0), representing the initial $3.00 fee\n- **slope of 2.5**: This shows how steeply the line rises, representing the $2.50 rate per mile\n\nGraphs help us see important features like:\n- Overall trends (increasing/decreasing)\n- Rate of change (slope)\n- Starting values (y-intercept)\n- Maximum/minimum values\n- Points where the function equals zero (x-intercepts)", "visual": {"type": "giphy_search", "value": "linear function graph animated colorful"}, "interactive_element": {"type": "interactive_graph_selection_game", "prompt": "Which graph correctly represents our taxi function C(m) = 3 + 2.5m?", "options": [{"id": "a", "graph_description": "Line with y-intercept 3 and slope 2.5", "is_correct": true, "feedback": "Correct! This graph shows a line with y-intercept 3 (the initial fee) and slope 2.5 (the rate per mile)."}, {"id": "b", "graph_description": "Line with y-intercept 2.5 and slope 3", "is_correct": false, "feedback": "Not quite. This graph has the slope and y-intercept switched. The initial fee is $3.00 (y-intercept) and the rate is $2.50 per mile (slope)."}, {"id": "c", "graph_description": "Line with y-intercept 0 and slope 2.5", "is_correct": false, "feedback": "Incorrect. This graph shows no initial fee (y-intercept = 0), but our taxi function has a $3.00 initial fee."}, {"id": "d", "graph_description": "Curved parabola passing through (0,3)", "is_correct": false, "feedback": "Incorrect. Our taxi function is linear (straight line), not quadratic (curved). The equation C(m) = 3 + 2.5m creates a straight line."}], "follow_up_question": "What does the y-intercept of the taxi cost function represent?", "follow_up_options": [{"id": "a", "text": "The cost per mile", "is_correct": false, "feedback": "Not quite. The cost per mile is represented by the slope (2.5)."}, {"id": "b", "text": "The initial fee before any miles are driven", "is_correct": true, "feedback": "Correct! The y-intercept (3) represents the initial fee charged before any distance is traveled."}, {"id": "c", "text": "The total cost of the ride", "is_correct": false, "feedback": "Not correct. The total cost depends on the distance traveled."}, {"id": "d", "text": "The maximum distance the taxi can travel", "is_correct": false, "feedback": "Incorrect. The y-intercept doesn't represent distance at all."}]}}}, {"id": "rr-screen6-conclusion", "type": "lesson_screen", "order": 6, "estimatedTimeSeconds": 30, "content": {"headline": "Multiple Perspectives, One Function: The Complete Toolkit", "body_md": "Excellent work! You've mastered how the same function can be represented in multiple ways. Let's recap what you've learned:\n\n- **Words**: Describe functions in everyday language (\"$3.00 plus $2.50 per mile\")\n- **Equations**: Express functions precisely with mathematical symbols (C(m) = 3 + 2.5m)\n- **Tables**: Show specific input-output pairs to reveal patterns\n- **Graphs**: Provide a complete visual picture of the function's behavior\n\nEach representation has its strengths:\n- Words are accessible and connect to real life\n- Equations are precise and allow calculations\n- Tables show specific values and patterns\n- Graphs reveal overall trends and features\n\nMathematicians and scientists switch between these representations depending on what they need to understand or communicate about a function. You now have this powerful toolkit too!", "visual": {"type": "unsplash_search", "value": "mathematics multiple representations colorful"}, "interactive_element": {"type": "button", "text": "Continue to Next Lesson", "action": "next_lesson"}}}]}, {"id": "domain-and-range", "title": "Domain and Range", "description": "Explore the sets of valid inputs and outputs for functions.", "order": 3, "type": "interactive_lesson", "estimatedTimeMinutes": 10, "contentBlocks": [{"id": "dr-screen1-intro", "type": "lesson_screen", "order": 1, "estimatedTimeSeconds": 30, "content": {"headline": "Domain and Range: The Function's Territory", "body_md": "Every function has a specific territory where it operates:\n\n- **Domain**: The set of all valid input values (x-values)\n- **Range**: The set of all possible output values (y-values)\n\nThinking about domain and range helps us understand where a function makes sense and what outputs it can produce.", "visual": {"type": "giphy_search", "value": "function domain range mathematics animated"}, "interactive_element": {"type": "button", "text": "Let's Explore Domain and Range!", "action": "next_screen"}}}, {"id": "dr-screen2-domain", "type": "lesson_screen", "order": 2, "estimatedTimeSeconds": 60, "content": {"headline": "Domain: Where Functions Live", "body_md": "The **domain** is the set of all valid input values for a function. Sometimes the domain includes all real numbers, but often there are restrictions.\n\nCommon domain restrictions:\n\n1. **Division by zero**: In functions with fractions, we can't have a denominator of zero\n2. **Even roots of negative numbers**: We can't take the square root of a negative number in the real number system\n3. **Logarithms of non-positive numbers**: We can't take the logarithm of zero or negative numbers\n\nFor example, the function f(x) = 1/x has a domain of all real numbers *except* x = 0, because division by zero is undefined.", "visual": {"type": "unsplash_search", "value": "mathematics domain function"}, "interactive_element": {"type": "multiple_choice_text", "question": "What is the domain of the function f(x) = √x?", "options": [{"id": "a", "text": "All real numbers", "is_correct": false, "feedback_incorrect": "Not quite. We can't take the square root of negative numbers in the real number system."}, {"id": "b", "text": "All real numbers except x = 0", "is_correct": false, "feedback_incorrect": "Incorrect. We can actually calculate f(0) = √0 = 0, so 0 is in the domain."}, {"id": "c", "text": "All non-negative real numbers (x ≥ 0)", "is_correct": true, "feedback_correct": "Correct! The domain of f(x) = √x is all non-negative real numbers (x ≥ 0) because we can't take the square root of negative numbers in the real number system."}, {"id": "d", "text": "All positive real numbers (x > 0)", "is_correct": false, "feedback_incorrect": "Not quite. While we can't take the square root of negative numbers, we can calculate f(0) = √0 = 0, so 0 is in the domain."}]}}}, {"id": "dr-screen3-range", "type": "lesson_screen", "order": 3, "estimatedTimeSeconds": 60, "content": {"headline": "Range: The Function's Outputs", "body_md": "The **range** is the set of all possible output values that a function can produce. The range depends on the function's rule and its domain.\n\nFor example:\n- The function f(x) = x² has a range of [0, ∞) because squares are always non-negative\n- The function f(x) = sin(x) has a range of [-1, 1] because sine values are always between -1 and 1\n\nFinding the range often requires analyzing the function's behavior or graphing it.", "visual": {"type": "giphy_search", "value": "function range mathematics animated"}, "interactive_element": {"type": "multiple_choice_text", "question": "What is the range of the function f(x) = |x|?", "options": [{"id": "a", "text": "All real numbers", "is_correct": false, "feedback_incorrect": "Not quite. The absolute value function can't produce negative outputs."}, {"id": "b", "text": "All non-negative real numbers (y ≥ 0)", "is_correct": true, "feedback_correct": "Correct! The absolute value function always returns zero or positive values, so its range is [0, ∞)."}, {"id": "c", "text": "All positive real numbers (y > 0)", "is_correct": false, "feedback_incorrect": "Not quite. The absolute value of 0 is 0, so 0 is in the range."}, {"id": "d", "text": "[-1, 1]", "is_correct": false, "feedback_incorrect": "Incorrect. The absolute value function can produce values larger than 1 (for example, |2| = 2)."}]}}}, {"id": "dr-screen4-real-world", "type": "lesson_screen", "order": 4, "estimatedTimeSeconds": 90, "content": {"headline": "Domain and Range in the Real World", "body_md": "Domain and range restrictions often have real-world meanings:\n\n**Example 1: Area of a Circle**\nA = πr²\n- Domain: r ≥ 0 (radius can't be negative)\n- Range: A ≥ 0 (area can't be negative)\n\n**Example 2: Height of a Thrown Ball**\nh(t) = -16t² + 40t + 5\n- Domain: t ≥ 0 (time can't be negative)\n- Range: 0 ≤ h ≤ 30 (ball starts at 5 feet, reaches maximum height, then hits ground)\n\n**Example 3: Cost of Taxi Ride**\nC(m) = 3 + 2.5m\n- Domain: m ≥ 0 (distance can't be negative)\n- Range: C ≥ 3 (cost is at least the initial fee)", "visual": {"type": "unsplash_search", "value": "mathematics real world application"}, "interactive_element": {"type": "multiple_choice_text", "question": "A rectangular garden has a perimeter of 100 feet. If the length is x feet, then the width is (50 - x) feet. The area function is A(x) = x(50 - x). What is the domain of this function in this context?", "options": [{"id": "a", "text": "All real numbers", "is_correct": false, "feedback_incorrect": "Not quite. In a real-world context, the length can't be negative or greater than 50 feet."}, {"id": "b", "text": "0 < x < 50", "is_correct": true, "feedback_correct": "Correct! The length must be positive (x > 0) and less than 50 feet (to ensure the width is also positive)."}, {"id": "c", "text": "0 ≤ x ≤ 50", "is_correct": false, "feedback_incorrect": "Not quite. If x = 0 or x = 50, then one dimension would be 0, which isn't a valid rectangle."}, {"id": "d", "text": "x ≥ 0", "is_correct": false, "feedback_incorrect": "Not quite. While length must be positive, it also can't exceed 50 feet in this context."}]}}}, {"id": "dr-screen5-conclusion", "type": "lesson_screen", "order": 5, "estimatedTimeSeconds": 30, "content": {"headline": "Domain and Range: Setting Boundaries", "body_md": "Great job! You now understand domain and range - the boundaries that define where functions operate and what outputs they can produce. Let's recap:\n\n- **Domain**: The set of all valid input values (x-values)\n- **Range**: The set of all possible output values (y-values)\n\nDomain restrictions often come from:\n- Mathematical limitations (division by zero, square roots of negative numbers)\n- Real-world constraints (physical measurements can't be negative)\n\nUnderstanding domain and range helps us:\n- Avoid calculation errors\n- Interpret functions in real-world contexts\n- Analyze function behavior\n\nIn the next lesson, we'll explore function notation in more detail!", "visual": {"type": "unsplash_search", "value": "mathematics boundaries colorful"}, "interactive_element": {"type": "button", "text": "Continue to Next Lesson", "action": "next_lesson"}}}]}], "endOfModuleAssessment": {"id": "foundation-of-functions-test", "title": "Function Fundamentals", "description": "Test your understanding of function basics, domain and range, and function notation.", "type": "module_test_interactive", "estimatedTimeMinutes": 15, "passingScorePercentage": 70, "contentBlocks": [{"id": "fof-test-intro", "type": "test_screen_intro", "order": 1, "estimatedTimeSeconds": 30, "content": {"headline": "Function Fundamentals: Test Your Knowledge", "body_md": "Let's see how well you understand the core concepts of functions! This test will cover function basics, domain and range, and function notation.", "visual": {"type": "unsplash_search", "value": "mathematics test"}, "interactive_element": {"type": "button", "text": "Begin Test", "action": "next_screen"}}}, {"id": "fof-test-q1", "type": "test_screen_question", "order": 2, "estimatedTimeSeconds": 60, "content": {"headline": "Question 1: Function Definition", "body_md": "Which of the following best defines a function?", "interactive_element": {"type": "multiple_choice_text", "options": [{"id": "a", "text": "A relationship where each input has exactly one output", "is_correct": true, "feedback_correct": "Correct! A function maps each input to exactly one output."}, {"id": "b", "text": "A relationship where each output has exactly one input", "is_correct": false, "feedback_incorrect": "Not quite. This describes an injective (one-to-one) function, but not all functions have this property."}, {"id": "c", "text": "A relationship where inputs and outputs are always equal", "is_correct": false, "feedback_incorrect": "Incorrect. Functions can have different input and output values."}, {"id": "d", "text": "A relationship where multiple inputs can produce multiple outputs", "is_correct": false, "feedback_incorrect": "Incorrect. In a function, each input must have exactly one output."}]}}}, {"id": "fof-test-q2", "type": "test_screen_question", "order": 3, "estimatedTimeSeconds": 60, "content": {"headline": "Question 2: Domain and Range", "body_md": "For the function f(x) = 1/(x-2), what is the domain?", "interactive_element": {"type": "multiple_choice_text", "options": [{"id": "a", "text": "All real numbers", "is_correct": false, "feedback_incorrect": "Incorrect. The function is undefined at x = 2."}, {"id": "b", "text": "All real numbers except x = 2", "is_correct": true, "feedback_correct": "Correct! Since division by zero is undefined, x cannot equal 2."}, {"id": "c", "text": "All real numbers except x = 0", "is_correct": false, "feedback_incorrect": "Incorrect. The function is defined at x = 0, giving f(0) = -1/2."}, {"id": "d", "text": "All positive real numbers", "is_correct": false, "feedback_incorrect": "Incorrect. The function is defined for negative values of x as well, as long as x ≠ 2."}]}}}, {"id": "fof-test-q3", "type": "test_screen_question", "order": 4, "estimatedTimeSeconds": 60, "content": {"headline": "Question 3: Function Notation", "body_md": "If f(x) = 3x² - 2x + 5, what is f(2)?", "interactive_element": {"type": "text_input_quick", "correct_answer_regex": "^13$", "placeholder": "Enter your answer", "feedback_correct": "Correct! f(2) = 3(2)² - 2(2) + 5 = 3(4) - 4 + 5 = 12 - 4 + 5 = 13", "feedback_incorrect": "Incorrect. Try substituting x = 2 into the function and calculating step by step."}}}, {"id": "fof-test-q4", "type": "test_screen_question", "order": 5, "estimatedTimeSeconds": 60, "content": {"headline": "Question 4: Identifying Functions", "body_md": "Which of the following relations is NOT a function?", "interactive_element": {"type": "multiple_choice_text", "options": [{"id": "a", "text": "y = x²", "is_correct": false, "feedback_incorrect": "Incorrect. This is a function because each x-value maps to exactly one y-value."}, {"id": "b", "text": "y = √x", "is_correct": false, "feedback_incorrect": "Incorrect. This is a function (for x ≥ 0) because each x-value maps to exactly one y-value."}, {"id": "c", "text": "x = y²", "is_correct": true, "feedback_correct": "Correct! This is not a function because some x-values (like x = 4) correspond to multiple y-values (y = 2 and y = -2)."}, {"id": "d", "text": "y = |x|", "is_correct": false, "feedback_incorrect": "Incorrect. The absolute value relation is a function because each x-value maps to exactly one y-value."}]}}}, {"id": "fof-test-q5", "type": "test_screen_question", "order": 6, "estimatedTimeSeconds": 60, "content": {"headline": "Question 5: Function Representations", "body_md": "Which representation of a function shows the overall trend most clearly?", "interactive_element": {"type": "multiple_choice_text", "options": [{"id": "a", "text": "Verbal description", "is_correct": false, "feedback_incorrect": "Not the best choice. Verbal descriptions can be precise but don't visually show trends."}, {"id": "b", "text": "Equation", "is_correct": false, "feedback_incorrect": "Not the best choice. Equations are precise but require analysis to visualize trends."}, {"id": "c", "text": "Table of values", "is_correct": false, "feedback_incorrect": "Not the best choice. Tables show specific values but may not clearly reveal the overall pattern."}, {"id": "d", "text": "Graph", "is_correct": true, "feedback_correct": "Correct! Graphs provide a visual representation that makes it easy to see trends, intercepts, and the overall behavior of a function."}]}}}, {"id": "fof-test-conclusion", "type": "test_screen_conclusion", "order": 7, "estimatedTimeSeconds": 30, "content": {"headline": "Function Fundamentals: Test Complete", "body_md": "Great job completing the test! You've demonstrated your understanding of function basics, domain and range, and function notation.", "visual": {"type": "unsplash_search", "value": "mathematics success"}, "interactive_element": {"type": "button", "text": "Return to Module", "action": "return_to_module"}}}]}}