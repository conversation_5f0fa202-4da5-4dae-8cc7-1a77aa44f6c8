import 'package:flutter/material.dart';
import 'dart:math' as math;

/// A widget that allows users to graph points, lines, and functions on a coordinate plane
class InteractiveCoordinatePlaneGrapherWidget extends StatefulWidget {
  /// The data for this widget
  final Map<String, dynamic> data;

  /// Optional callback when the widget state changes
  final Function(bool isCompleted)? onStateChanged;

  const InteractiveCoordinatePlaneGrapherWidget({
    Key? key,
    required this.data,
    this.onStateChanged,
  }) : super(key: key);

  /// Factory constructor to create the widget from JSON data
  factory InteractiveCoordinatePlaneGrapherWidget.fromData(Map<String, dynamic> data) {
    return InteractiveCoordinatePlaneGrapherWidget(
      data: data,
    );
  }

  @override
  State<InteractiveCoordinatePlaneGrapherWidget> createState() => _InteractiveCoordinatePlaneGrapherWidgetState();
}

class _InteractiveCoordinatePlaneGrapherWidgetState extends State<InteractiveCoordinatePlaneGrapherWidget> {
  // State variables
  bool _isCompleted = false;
  List<Point> _points = [];
  List<Line> _lines = [];
  List<MathFunction> _functions = [];

  // Coordinate plane settings
  double _xMin = -10;
  double _xMax = 10;
  double _yMin = -10;
  double _yMax = 10;
  double _gridSpacing = 1.0;
  bool _showGrid = true;
  bool _showAxes = true;

  // Current mode
  String _currentMode = 'point'; // 'point', 'line', 'function'

  // Current function being edited
  String _currentFunction = '';
  String _functionError = '';

  // Current line being created
  Point? _lineStartPoint;

  // Colors
  late Color _gridColor;
  late Color _axisColor;
  late Color _pointColor;
  late Color _lineColor;
  late Color _functionColor;

  // Controller for function input
  final TextEditingController _functionController = TextEditingController();

  @override
  void initState() {
    super.initState();
    _initializeFromData();
  }

  @override
  void dispose() {
    _functionController.dispose();
    super.dispose();
  }

  void _initializeFromData() {
    // Initialize coordinate plane settings
    _xMin = widget.data['xMin'] ?? -10.0;
    _xMax = widget.data['xMax'] ?? 10.0;
    _yMin = widget.data['yMin'] ?? -10.0;
    _yMax = widget.data['yMax'] ?? 10.0;
    _gridSpacing = widget.data['gridSpacing'] ?? 1.0;
    _showGrid = widget.data['showGrid'] ?? true;
    _showAxes = widget.data['showAxes'] ?? true;

    // Initialize colors
    _gridColor = _parseColor(widget.data['gridColor'] ?? '#CCCCCC');
    _axisColor = _parseColor(widget.data['axisColor'] ?? '#000000');
    _pointColor = _parseColor(widget.data['pointColor'] ?? '#FF5722');
    _lineColor = _parseColor(widget.data['lineColor'] ?? '#2196F3');
    _functionColor = _parseColor(widget.data['functionColor'] ?? '#4CAF50');

    // Initialize current mode
    _currentMode = widget.data['initialMode'] ?? 'point';

    // Initialize pre-defined points if any
    if (widget.data['initialPoints'] != null) {
      for (var pointData in widget.data['initialPoints']) {
        _points.add(Point(
          x: pointData['x'].toDouble(),
          y: pointData['y'].toDouble(),
          label: pointData['label'],
        ));
      }
    }

    // Initialize pre-defined lines if any
    if (widget.data['initialLines'] != null) {
      for (var lineData in widget.data['initialLines']) {
        _lines.add(Line(
          start: Point(
            x: lineData['startX'].toDouble(),
            y: lineData['startY'].toDouble(),
          ),
          end: Point(
            x: lineData['endX'].toDouble(),
            y: lineData['endY'].toDouble(),
          ),
          label: lineData['label'],
        ));
      }
    }

    // Initialize pre-defined functions if any
    if (widget.data['initialFunctions'] != null) {
      for (var functionData in widget.data['initialFunctions']) {
        _functions.add(MathFunction(
          expression: functionData['expression'],
          color: _parseColor(functionData['color'] ?? '#4CAF50'),
        ));
      }
    }
  }

  Color _parseColor(String colorString) {
    if (colorString.startsWith('#')) {
      return Color(int.parse('FF${colorString.substring(1)}', radix: 16));
    }
    return Colors.black;
  }

  void _addPoint(double x, double y) {
    setState(() {
      _points.add(Point(x: x, y: y));
    });
  }

  void _startLine(double x, double y) {
    _lineStartPoint = Point(x: x, y: y);
  }

  void _completeLine(double x, double y) {
    if (_lineStartPoint != null) {
      setState(() {
        _lines.add(Line(
          start: _lineStartPoint!,
          end: Point(x: x, y: y),
        ));
        _lineStartPoint = null;
      });
    }
  }

  void _addFunction(String expression) {
    try {
      // Simple validation - this would be more complex in a real app
      if (expression.isEmpty) {
        setState(() {
          _functionError = 'Function expression cannot be empty';
        });
        return;
      }

      setState(() {
        _functions.add(MathFunction(expression: expression));
        _currentFunction = '';
        _functionError = '';
        _functionController.clear();
      });
    } catch (e) {
      setState(() {
        _functionError = 'Invalid function: $e';
      });
    }
  }

  void _clearAll() {
    setState(() {
      _points.clear();
      _lines.clear();
      _functions.clear();
      _lineStartPoint = null;
      _currentFunction = '';
      _functionError = '';
      _functionController.clear();
    });
  }

  void _setMode(String mode) {
    setState(() {
      _currentMode = mode;
      _lineStartPoint = null;
    });
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withOpacity(0.2),
            spreadRadius: 1,
            blurRadius: 3,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Title
          Text(
            widget.data['title'] ?? 'Coordinate Plane Grapher',
            style: const TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
            ),
          ),

          const SizedBox(height: 8),

          // Description
          if (widget.data['description'] != null)
            Text(
              widget.data['description'],
              style: const TextStyle(
                fontSize: 14,
                color: Colors.grey,
              ),
            ),

          const SizedBox(height: 16),

          // Mode selection buttons
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceEvenly,
            children: [
              _buildModeButton('Point', 'point', Icons.add_circle_outline),
              _buildModeButton('Line', 'line', Icons.timeline),
              _buildModeButton('Function', 'function', Icons.functions),
              IconButton(
                icon: const Icon(Icons.clear_all),
                onPressed: _clearAll,
                tooltip: 'Clear All',
                color: Colors.red,
              ),
            ],
          ),

          const SizedBox(height: 16),

          // Function input (only visible in function mode)
          if (_currentMode == 'function')
            Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                TextField(
                  controller: _functionController,
                  decoration: InputDecoration(
                    labelText: 'Enter function (e.g., y = 2x + 1)',
                    errorText: _functionError.isNotEmpty ? _functionError : null,
                    border: const OutlineInputBorder(),
                    suffixIcon: IconButton(
                      icon: const Icon(Icons.add),
                      onPressed: () => _addFunction(_functionController.text),
                      tooltip: 'Add Function',
                    ),
                  ),
                  onChanged: (value) {
                    setState(() {
                      _currentFunction = value;
                      _functionError = '';
                    });
                  },
                  onSubmitted: _addFunction,
                ),
                const SizedBox(height: 8),
                const Text(
                  'Supported format: y = mx + b or y = ax² + bx + c',
                  style: TextStyle(
                    fontSize: 12,
                    fontStyle: FontStyle.italic,
                  ),
                ),
              ],
            ),

          const SizedBox(height: 16),

          // Coordinate plane
          AspectRatio(
            aspectRatio: 1.0,
            child: GestureDetector(
              onTapDown: (details) {
                final RenderBox box = context.findRenderObject() as RenderBox;
                final localPosition = box.globalToLocal(details.globalPosition);
                final x = _mapToCoordinateX(localPosition.dx);
                final y = _mapToCoordinateY(localPosition.dy);

                if (_currentMode == 'point') {
                  _addPoint(x, y);
                } else if (_currentMode == 'line') {
                  if (_lineStartPoint == null) {
                    _startLine(x, y);
                  } else {
                    _completeLine(x, y);
                  }
                }
              },
              child: CustomPaint(
                painter: CoordinatePlanePainter(
                  xMin: _xMin,
                  xMax: _xMax,
                  yMin: _yMin,
                  yMax: _yMax,
                  gridSpacing: _gridSpacing,
                  showGrid: _showGrid,
                  showAxes: _showAxes,
                  gridColor: _gridColor,
                  axisColor: _axisColor,
                  points: _points,
                  lines: _lines,
                  functions: _functions,
                  pointColor: _pointColor,
                  lineColor: _lineColor,
                  functionColor: _functionColor,
                  lineStartPoint: _lineStartPoint,
                ),
                child: Container(),
              ),
            ),
          ),

          // Widget name tag
          if (widget.data['showNameTag'] ?? true)
            Padding(
              padding: const EdgeInsets.only(top: 8),
              child: Text(
                'InteractiveCoordinatePlaneGrapher',
                style: TextStyle(
                  color: Colors.grey[600],
                  fontSize: 12,
                  fontStyle: FontStyle.italic,
                ),
              ),
            ),
        ],
      ),
    );
  }

  Widget _buildModeButton(String label, String mode, IconData icon) {
    final isSelected = _currentMode == mode;

    return ElevatedButton.icon(
      icon: Icon(icon, size: 16),
      label: Text(label),
      style: ElevatedButton.styleFrom(
        backgroundColor: isSelected ? Theme.of(context).primaryColor : Colors.grey[200],
        foregroundColor: isSelected ? Colors.white : Colors.black87,
        padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
      ),
      onPressed: () => _setMode(mode),
    );
  }

  double _mapToCoordinateX(double pixelX) {
    final width = context.size?.width ?? 1;
    return _xMin + (pixelX / width) * (_xMax - _xMin);
  }

  double _mapToCoordinateY(double pixelY) {
    final height = context.size?.height ?? 1;
    // Invert Y because pixel coordinates increase downward
    return _yMax - (pixelY / height) * (_yMax - _yMin);
  }
}

class CoordinatePlanePainter extends CustomPainter {
  final double xMin;
  final double xMax;
  final double yMin;
  final double yMax;
  final double gridSpacing;
  final bool showGrid;
  final bool showAxes;
  final Color gridColor;
  final Color axisColor;
  final List<Point> points;
  final List<Line> lines;
  final List<MathFunction> functions;
  final Color pointColor;
  final Color lineColor;
  final Color functionColor;
  final Point? lineStartPoint;

  CoordinatePlanePainter({
    required this.xMin,
    required this.xMax,
    required this.yMin,
    required this.yMax,
    required this.gridSpacing,
    required this.showGrid,
    required this.showAxes,
    required this.gridColor,
    required this.axisColor,
    required this.points,
    required this.lines,
    required this.functions,
    required this.pointColor,
    required this.lineColor,
    required this.functionColor,
    this.lineStartPoint,
  });

  @override
  void paint(Canvas canvas, Size size) {
    final paint = Paint()
      ..color = gridColor
      ..strokeWidth = 1.0
      ..style = PaintingStyle.stroke;

    final axisPaint = Paint()
      ..color = axisColor
      ..strokeWidth = 2.0
      ..style = PaintingStyle.stroke;

    final pointPaint = Paint()
      ..color = pointColor
      ..strokeWidth = 1.0
      ..style = PaintingStyle.fill;

    final linePaint = Paint()
      ..color = lineColor
      ..strokeWidth = 2.0
      ..style = PaintingStyle.stroke;

    final functionPaint = Paint()
      ..color = functionColor
      ..strokeWidth = 2.0
      ..style = PaintingStyle.stroke;

    final tempLinePaint = Paint()
      ..color = lineColor.withOpacity(0.5)
      ..strokeWidth = 2.0
      ..style = PaintingStyle.stroke;

    // Draw grid
    if (showGrid) {
      _drawGrid(canvas, size, paint);
    }

    // Draw axes
    if (showAxes) {
      _drawAxes(canvas, size, axisPaint);
    }

    // Draw functions
    for (var function in functions) {
      _drawFunction(canvas, size, function, functionPaint);
    }

    // Draw lines
    for (var line in lines) {
      _drawLine(canvas, size, line, linePaint);
    }

    // Draw temporary line if in line mode and have start point
    if (lineStartPoint != null) {
      final startX = _mapToPixelX(lineStartPoint!.x, size.width);
      final startY = _mapToPixelY(lineStartPoint!.y, size.height);

      // Draw to current mouse position (not implemented here)
      // For now, just draw a short line
      canvas.drawLine(
        Offset(startX, startY),
        Offset(startX + 50, startY - 50),
        tempLinePaint,
      );
    }

    // Draw points
    for (var point in points) {
      _drawPoint(canvas, size, point, pointPaint);
    }
  }

  void _drawGrid(Canvas canvas, Size size, Paint paint) {
    // Draw vertical grid lines
    for (double x = xMin; x <= xMax; x += gridSpacing) {
      if (x == 0) continue; // Skip the axis
      final pixelX = _mapToPixelX(x, size.width);
      canvas.drawLine(
        Offset(pixelX, 0),
        Offset(pixelX, size.height),
        paint,
      );
    }

    // Draw horizontal grid lines
    for (double y = yMin; y <= yMax; y += gridSpacing) {
      if (y == 0) continue; // Skip the axis
      final pixelY = _mapToPixelY(y, size.height);
      canvas.drawLine(
        Offset(0, pixelY),
        Offset(size.width, pixelY),
        paint,
      );
    }
  }

  void _drawAxes(Canvas canvas, Size size, Paint paint) {
    // Draw x-axis
    final yAxisPixel = _mapToPixelY(0, size.height);
    canvas.drawLine(
      Offset(0, yAxisPixel),
      Offset(size.width, yAxisPixel),
      paint,
    );

    // Draw y-axis
    final xAxisPixel = _mapToPixelX(0, size.width);
    canvas.drawLine(
      Offset(xAxisPixel, 0),
      Offset(xAxisPixel, size.height),
      paint,
    );
  }

  void _drawPoint(Canvas canvas, Size size, Point point, Paint paint) {
    final x = _mapToPixelX(point.x, size.width);
    final y = _mapToPixelY(point.y, size.height);

    canvas.drawCircle(Offset(x, y), 5.0, paint);

    // Draw label if present
    if (point.label != null && point.label!.isNotEmpty) {
      final textSpan = TextSpan(
        text: point.label,
        style: const TextStyle(
          color: Colors.black,
          fontSize: 12,
        ),
      );
      final textPainter = TextPainter(
        text: textSpan,
        textDirection: TextDirection.ltr,
      );
      textPainter.layout();
      textPainter.paint(canvas, Offset(x + 8, y - 8));
    }
  }

  void _drawLine(Canvas canvas, Size size, Line line, Paint paint) {
    final startX = _mapToPixelX(line.start.x, size.width);
    final startY = _mapToPixelY(line.start.y, size.height);
    final endX = _mapToPixelX(line.end.x, size.width);
    final endY = _mapToPixelY(line.end.y, size.height);

    canvas.drawLine(
      Offset(startX, startY),
      Offset(endX, endY),
      paint,
    );

    // Draw label if present
    if (line.label != null && line.label!.isNotEmpty) {
      final midX = (startX + endX) / 2;
      final midY = (startY + endY) / 2;

      final textSpan = TextSpan(
        text: line.label,
        style: const TextStyle(
          color: Colors.black,
          fontSize: 12,
        ),
      );
      final textPainter = TextPainter(
        text: textSpan,
        textDirection: TextDirection.ltr,
      );
      textPainter.layout();
      textPainter.paint(canvas, Offset(midX + 8, midY - 8));
    }
  }

  void _drawFunction(Canvas canvas, Size size, MathFunction function, Paint paint) {
    // This is a simplified implementation
    // A real implementation would parse and evaluate the function expression

    // For demonstration, let's draw a simple parabola y = x^2
    final path = Path();
    bool isFirstPoint = true;

    for (double pixelX = 0; pixelX < size.width; pixelX++) {
      final x = _mapToCoordinateX(pixelX, size.width);

      // Evaluate function (simplified)
      double y;
      if (function.expression.contains('x²') || function.expression.contains('x^2')) {
        y = x * x; // Parabola
      } else {
        y = 2 * x; // Default to a line with slope 2
      }

      final pixelY = _mapToPixelY(y, size.height);

      if (isFirstPoint) {
        path.moveTo(pixelX, pixelY);
        isFirstPoint = false;
      } else {
        path.lineTo(pixelX, pixelY);
      }
    }

    canvas.drawPath(path, paint);
  }

  double _mapToPixelX(double x, double width) {
    return ((x - xMin) / (xMax - xMin)) * width;
  }

  double _mapToPixelY(double y, double height) {
    // Invert Y because pixel coordinates increase downward
    return height - ((y - yMin) / (yMax - yMin)) * height;
  }

  double _mapToCoordinateX(double pixelX, double width) {
    return xMin + (pixelX / width) * (xMax - xMin);
  }

  double _mapToCoordinateY(double pixelY, double height) {
    // Invert Y because pixel coordinates increase downward
    return yMax - (pixelY / height) * (yMax - yMin);
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) {
    return true;
  }
}

class Point {
  final double x;
  final double y;
  final String? label;

  Point({
    required this.x,
    required this.y,
    this.label,
  });
}

class Line {
  final Point start;
  final Point end;
  final String? label;

  Line({
    required this.start,
    required this.end,
    this.label,
  });
}

class MathFunction {
  final String expression;
  final Color color;

  MathFunction({
    required this.expression,
    this.color = Colors.green,
  });
}
