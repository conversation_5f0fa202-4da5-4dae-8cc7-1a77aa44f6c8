{"buildFiles": ["C:\\flutter\\packages\\flutter_tools\\gradle\\src\\main\\groovy\\CMakeLists.txt"], "cleanCommandsComponents": [["D:\\AndroidSDK\\cmake\\3.22.1\\bin\\ninja.exe", "-C", "D:\\AResonance\\rn\\android\\app\\.cxx\\Debug\\5e5t3o49\\x86_64", "clean"]], "buildTargetsCommandComponents": ["D:\\AndroidSDK\\cmake\\3.22.1\\bin\\ninja.exe", "-C", "D:\\AResonance\\rn\\android\\app\\.cxx\\Debug\\5e5t3o49\\x86_64", "{LIST_OF_TARGETS_TO_BUILD}"], "libraries": {}, "toolchains": {"toolchain": {"cCompilerExecutable": "D:\\AndroidSDK\\ndk\\29.0.13113456\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang.exe", "cppCompilerExecutable": "D:\\AndroidSDK\\ndk\\29.0.13113456\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe"}}, "cFileExtensions": [], "cppFileExtensions": []}