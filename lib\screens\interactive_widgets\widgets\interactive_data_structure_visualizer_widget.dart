import 'package:flutter/material.dart';
import 'dart:collection';

/// Data structure models

/// Array data structure
class ArrayStructure {
  List<int> elements;
  final int maxSize;

  ArrayStructure({
    required this.elements,
    required this.maxSize,
  });

  bool insert(int index, int value) {
    if (index < 0 || index > elements.length || elements.length >= maxSize) {
      return false;
    }
    elements.insert(index, value);
    return true;
  }

  int? removeAt(int index) {
    if (index < 0 || index >= elements.length) {
      return null;
    }
    return elements.removeAt(index);
  }

  int search(int value) {
    return elements.indexOf(value);
  }
}

/// Linked list node
class LinkedListNode {
  int value;
  LinkedListNode? next;

  LinkedListNode(this.value);
}

/// Linked list data structure
class LinkedListStructure {
  LinkedListNode? head;
  int length = 0;

  LinkedListStructure({
    required List<int> elements,
  }) {
    // Initialize linked list with elements
    for (final element in elements) {
      insertAtTail(element);
    }
  }

  void insertAtHead(int value) {
    final newNode = LinkedListNode(value);
    newNode.next = head;
    head = newNode;
    length++;
  }

  void insertAtTail(int value) {
    final newNode = LinkedListNode(value);
    if (head == null) {
      head = newNode;
    } else {
      var current = head;
      while (current!.next != null) {
        current = current.next;
      }
      current.next = newNode;
    }
    length++;
  }

  bool insertAt(int index, int value) {
    if (index < 0 || index > length) {
      return false;
    }
    if (index == 0) {
      insertAtHead(value);
      return true;
    }
    if (index == length) {
      insertAtTail(value);
      return true;
    }

    var current = head;
    for (var i = 0; i < index - 1; i++) {
      current = current!.next;
    }

    final newNode = LinkedListNode(value);
    newNode.next = current!.next;
    current.next = newNode;
    length++;
    return true;
  }

  int? removeHead() {
    if (head == null) {
      return null;
    }
    final value = head!.value;
    head = head!.next;
    length--;
    return value;
  }

  int? removeTail() {
    if (head == null) {
      return null;
    }
    if (head!.next == null) {
      final value = head!.value;
      head = null;
      length--;
      return value;
    }

    var current = head;
    while (current!.next!.next != null) {
      current = current.next;
    }
    final value = current.next!.value;
    current.next = null;
    length--;
    return value;
  }

  int search(int value) {
    var current = head;
    var index = 0;
    while (current != null) {
      if (current.value == value) {
        return index;
      }
      current = current.next;
      index++;
    }
    return -1;
  }

  List<int> toList() {
    final result = <int>[];
    var current = head;
    while (current != null) {
      result.add(current.value);
      current = current.next;
    }
    return result;
  }
}

/// Stack data structure
class StackStructure {
  List<int> elements;
  final int maxSize;

  StackStructure({
    required this.elements,
    required this.maxSize,
  });

  bool push(int value) {
    if (elements.length >= maxSize) {
      return false;
    }
    elements.add(value);
    return true;
  }

  int? pop() {
    if (elements.isEmpty) {
      return null;
    }
    return elements.removeLast();
  }

  int? peek() {
    if (elements.isEmpty) {
      return null;
    }
    return elements.last;
  }

  bool isEmpty() {
    return elements.isEmpty;
  }

  bool isFull() {
    return elements.length >= maxSize;
  }
}

/// Queue data structure
class QueueStructure {
  List<int> elements;
  final int maxSize;

  QueueStructure({
    required this.elements,
    required this.maxSize,
  });

  bool enqueue(int value) {
    if (elements.length >= maxSize) {
      return false;
    }
    elements.add(value);
    return true;
  }

  int? dequeue() {
    if (elements.isEmpty) {
      return null;
    }
    return elements.removeAt(0);
  }

  int? peek() {
    if (elements.isEmpty) {
      return null;
    }
    return elements.first;
  }

  bool isEmpty() {
    return elements.isEmpty;
  }

  bool isFull() {
    return elements.length >= maxSize;
  }
}

/// Tree node
class TreeNode {
  int value;
  int? left;
  int? right;

  TreeNode({
    required this.value,
    this.left,
    this.right,
  });
}

/// Binary search tree data structure
class TreeStructure {
  int rootValue;
  List<TreeNode> nodes;

  TreeStructure({
    required this.rootValue,
    required this.nodes,
  });

  bool insert(int value) {
    // Check if value already exists
    if (nodes.any((node) => node.value == value)) {
      return false;
    }

    // Find the parent node
    TreeNode? parent;
    for (final node in nodes) {
      if ((value < node.value && node.left == null) ||
          (value > node.value && node.right == null)) {
        parent = node;
        break;
      }
    }

    if (parent == null) {
      // If no parent found, this is the first node
      if (nodes.isEmpty) {
        nodes.add(TreeNode(value: value));
        rootValue = value;
        return true;
      }
      return false;
    }

    // Add the new node
    nodes.add(TreeNode(value: value));

    // Update the parent's reference
    if (value < parent.value) {
      parent.left = value;
    } else {
      parent.right = value;
    }

    return true;
  }

  bool search(int value) {
    return nodes.any((node) => node.value == value);
  }

  List<int> inOrderTraversal() {
    final result = <int>[];
    _inOrderTraversal(rootValue, result);
    return result;
  }

  void _inOrderTraversal(int? nodeValue, List<int> result) {
    if (nodeValue == null) return;

    final node = nodes.firstWhere((n) => n.value == nodeValue);
    _inOrderTraversal(node.left, result);
    result.add(node.value);
    _inOrderTraversal(node.right, result);
  }

  List<int> preOrderTraversal() {
    final result = <int>[];
    _preOrderTraversal(rootValue, result);
    return result;
  }

  void _preOrderTraversal(int? nodeValue, List<int> result) {
    if (nodeValue == null) return;

    final node = nodes.firstWhere((n) => n.value == nodeValue);
    result.add(node.value);
    _preOrderTraversal(node.left, result);
    _preOrderTraversal(node.right, result);
  }

  List<int> postOrderTraversal() {
    final result = <int>[];
    _postOrderTraversal(rootValue, result);
    return result;
  }

  void _postOrderTraversal(int? nodeValue, List<int> result) {
    if (nodeValue == null) return;

    final node = nodes.firstWhere((n) => n.value == nodeValue);
    _postOrderTraversal(node.left, result);
    _postOrderTraversal(node.right, result);
    result.add(node.value);
  }
}

/// Graph edge
class GraphEdge {
  int from;
  int to;

  GraphEdge({
    required this.from,
    required this.to,
  });
}

/// Graph data structure
class GraphStructure {
  List<int> nodes;
  List<GraphEdge> edges;

  GraphStructure({
    required this.nodes,
    required this.edges,
  });

  bool addNode(int value) {
    if (nodes.contains(value)) {
      return false;
    }
    nodes.add(value);
    return true;
  }

  bool addEdge(int from, int to) {
    if (!nodes.contains(from) || !nodes.contains(to)) {
      return false;
    }
    edges.add(GraphEdge(from: from, to: to));
    return true;
  }

  List<int> getNeighbors(int node) {
    return edges
        .where((edge) => edge.from == node)
        .map((edge) => edge.to)
        .toList();
  }

  List<int> bfsTraversal(int startNode) {
    if (!nodes.contains(startNode)) {
      return [];
    }

    final result = <int>[];
    final visited = <int>{};
    final queue = Queue<int>();

    queue.add(startNode);
    visited.add(startNode);

    while (queue.isNotEmpty) {
      final node = queue.removeFirst();
      result.add(node);

      for (final neighbor in getNeighbors(node)) {
        if (!visited.contains(neighbor)) {
          visited.add(neighbor);
          queue.add(neighbor);
        }
      }
    }

    return result;
  }

  List<int> dfsTraversal(int startNode) {
    if (!nodes.contains(startNode)) {
      return [];
    }

    final result = <int>[];
    final visited = <int>{};

    void dfs(int node) {
      visited.add(node);
      result.add(node);

      for (final neighbor in getNeighbors(node)) {
        if (!visited.contains(neighbor)) {
          dfs(neighbor);
        }
      }
    }

    dfs(startNode);
    return result;
  }
}

/// Custom painters for data structure visualization

/// Array painter
class ArrayPainter extends CustomPainter {
  final List<int> elements;
  final int? highlightIndex;
  final Color primaryColor;
  final Color highlightColor;
  final Color textColor;
  final double animationValue;

  ArrayPainter({
    required this.elements,
    this.highlightIndex,
    required this.primaryColor,
    required this.highlightColor,
    required this.textColor,
    required this.animationValue,
  });

  @override
  void paint(Canvas canvas, Size size) {
    final cellWidth = size.width / elements.length;
    final cellHeight = size.height;

    final textStyle = TextStyle(
      color: textColor,
      fontSize: 16,
      fontWeight: FontWeight.bold,
    );
    final indexStyle = TextStyle(
      color: textColor.withAlpha(179),
      fontSize: 12,
    );

    for (var i = 0; i < elements.length; i++) {
      final isHighlighted = i == highlightIndex;
      final cellColor = isHighlighted ? highlightColor : primaryColor;

      final rect = Rect.fromLTWH(
        i * cellWidth,
        0,
        cellWidth,
        cellHeight,
      );

      // Draw cell background
      final paint = Paint()
        ..color = cellColor.withAlpha((51 * animationValue).toInt())
        ..style = PaintingStyle.fill;
      canvas.drawRect(rect, paint);

      // Draw cell border
      final borderPaint = Paint()
        ..color = cellColor.withAlpha((179 * animationValue).toInt())
        ..style = PaintingStyle.stroke
        ..strokeWidth = 2.0;
      canvas.drawRect(rect, borderPaint);

      // Draw value
      final textSpan = TextSpan(
        text: elements[i].toString(),
        style: textStyle,
      );
      final textPainter = TextPainter(
        text: textSpan,
        textDirection: TextDirection.ltr,
      );
      textPainter.layout();
      textPainter.paint(
        canvas,
        Offset(
          i * cellWidth + (cellWidth - textPainter.width) / 2,
          (cellHeight - textPainter.height) / 2,
        ),
      );

      // Draw index
      final indexSpan = TextSpan(
        text: i.toString(),
        style: indexStyle,
      );
      final indexPainter = TextPainter(
        text: indexSpan,
        textDirection: TextDirection.ltr,
      );
      indexPainter.layout();
      indexPainter.paint(
        canvas,
        Offset(
          i * cellWidth + (cellWidth - indexPainter.width) / 2,
          cellHeight - indexPainter.height - 4,
        ),
      );
    }
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => true;
}

/// A widget that visualizes common data structures and their operations
class InteractiveDataStructureVisualizerWidget extends StatefulWidget {
  /// The data for this widget
  final Map<String, dynamic> data;

  /// Optional callback when the widget state changes
  final Function(bool isCompleted)? onStateChanged;

  const InteractiveDataStructureVisualizerWidget({
    super.key,
    required this.data,
    this.onStateChanged,
  });

  /// Factory constructor to create the widget from JSON data
  factory InteractiveDataStructureVisualizerWidget.fromData(
      Map<String, dynamic> data) {
    return InteractiveDataStructureVisualizerWidget(
      data: data,
    );
  }

  @override
  State<InteractiveDataStructureVisualizerWidget> createState() =>
      _InteractiveDataStructureVisualizerWidgetState();
}

class _InteractiveDataStructureVisualizerWidgetState
    extends State<InteractiveDataStructureVisualizerWidget>
    with SingleTickerProviderStateMixin {
  // Colors
  late Color _primaryColor;
  late Color _textColor;

  // Data structure types
  late List<String> _dataStructureTypes;
  late String _currentDataStructureType;

  // Animation controller
  late AnimationController _animationController;

  // Data structures
  late ArrayStructure _arrayStructure;
  late LinkedListStructure _linkedListStructure;
  late StackStructure _stackStructure;
  late QueueStructure _queueStructure;
  late TreeStructure _treeStructure;
  late GraphStructure _graphStructure;

  // UI state
  bool _isAnimating = false;
  bool _showExplanation = false;
  String _operationMessage = '';
  final TextEditingController _valueController = TextEditingController();
  final TextEditingController _indexController = TextEditingController();

  @override
  void initState() {
    super.initState();

    // Initialize colors from data or use defaults
    _primaryColor = _getColorFromHex(
        widget.data['primaryColor'] ?? '#2196F3'); // Blue
    _textColor =
        _getColorFromHex(widget.data['textColor'] ?? '#212121'); // Dark Grey

    // Initialize data structure types
    _dataStructureTypes = [
      'Array',
      'Linked List',
      'Stack',
      'Queue',
      'Binary Tree',
      'Graph'
    ];
    _currentDataStructureType = _dataStructureTypes[0];

    // Initialize animation controller
    _animationController = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 800),
    );
    _animationController.addStatusListener((status) {
      if (status == AnimationStatus.completed) {
        setState(() {
          _isAnimating = false;
        });
      }
    });

    // Initialize data structures
    _initializeDataStructures();
  }

  @override
  void dispose() {
    _animationController.dispose();
    _valueController.dispose();
    _indexController.dispose();
    super.dispose();
  }

  // Initialize data structures
  void _initializeDataStructures() {
    // Initialize with sample data
    _arrayStructure = ArrayStructure(
      elements: [5, 8, 2, 10, 7],
      maxSize: 10,
    );

    _linkedListStructure = LinkedListStructure(
      elements: [5, 8, 2, 10, 7],
    );

    _stackStructure = StackStructure(
      elements: [5, 8, 2, 10, 7],
      maxSize: 10,
    );

    _queueStructure = QueueStructure(
      elements: [5, 8, 2, 10, 7],
      maxSize: 10,
    );

    _treeStructure = TreeStructure(
      rootValue: 10,
      nodes: [
        TreeNode(value: 10, left: 5, right: 15),
        TreeNode(value: 5, left: 3, right: 7),
        TreeNode(value: 15, left: 12, right: 18),
        TreeNode(value: 3, left: null, right: null),
        TreeNode(value: 7, left: null, right: null),
        TreeNode(value: 12, left: null, right: null),
        TreeNode(value: 18, left: null, right: null),
      ],
    );

    _graphStructure = GraphStructure(
      nodes: [1, 2, 3, 4, 5],
      edges: [
        GraphEdge(from: 1, to: 2),
        GraphEdge(from: 1, to: 3),
        GraphEdge(from: 2, to: 4),
        GraphEdge(from: 3, to: 4),
        GraphEdge(from: 4, to: 5),
      ],
    );
  }

  // Convert hex color string to Color
  Color _getColorFromHex(String hexColor) {
    hexColor = hexColor.replaceAll('#', '');
    if (hexColor.length == 6) {
      hexColor = 'FF$hexColor';
    }
    return Color(int.parse(hexColor, radix: 16));
  }



  // Perform operation on the current data structure
  void _performOperation(String operation) {
    if (_isAnimating) return;

    setState(() {
      _isAnimating = true;
      _operationMessage = '';
    });

    _animationController.reset();
    _animationController.forward();

    // Get value and index from controllers
    int? value = int.tryParse(_valueController.text);
    int? index = int.tryParse(_indexController.text);

    // Perform operation based on data structure type
    switch (_currentDataStructureType) {
      case 'Array':
        _performArrayOperation(operation, value, index);
        break;
      case 'Linked List':
        _performLinkedListOperation(operation, value, index);
        break;
      case 'Stack':
        _performStackOperation(operation, value);
        break;
      case 'Queue':
        _performQueueOperation(operation, value);
        break;
      case 'Binary Tree':
        _performTreeOperation(operation, value);
        break;
      case 'Graph':
        _performGraphOperation(operation, value, index);
        break;
    }

    // Clear input fields
    _valueController.clear();
    _indexController.clear();
  }

  // Perform array operations
  void _performArrayOperation(String operation, int? value, int? index) {
    switch (operation) {
      case 'Insert':
        if (value != null && index != null) {
          if (_arrayStructure.insert(index, value)) {
            _operationMessage = 'Inserted $value at index $index';
          } else {
            _operationMessage = 'Failed to insert: Invalid index or array full';
          }
        } else {
          _operationMessage = 'Please provide both value and index';
        }
        break;
      case 'Remove':
        if (index != null) {
          final removed = _arrayStructure.removeAt(index);
          if (removed != null) {
            _operationMessage = 'Removed $removed from index $index';
          } else {
            _operationMessage = 'Failed to remove: Invalid index';
          }
        } else {
          _operationMessage = 'Please provide an index';
        }
        break;
      case 'Search':
        if (value != null) {
          final foundIndex = _arrayStructure.search(value);
          if (foundIndex != -1) {
            _operationMessage = 'Found $value at index $foundIndex';
          } else {
            _operationMessage = 'Value $value not found in the array';
          }
        } else {
          _operationMessage = 'Please provide a value to search';
        }
        break;
    }
  }

  // Perform linked list operations
  void _performLinkedListOperation(String operation, int? value, int? index) {
    switch (operation) {
      case 'Insert at Head':
        if (value != null) {
          _linkedListStructure.insertAtHead(value);
          _operationMessage = 'Inserted $value at the head';
        } else {
          _operationMessage = 'Please provide a value';
        }
        break;
      case 'Insert at Tail':
        if (value != null) {
          _linkedListStructure.insertAtTail(value);
          _operationMessage = 'Inserted $value at the tail';
        } else {
          _operationMessage = 'Please provide a value';
        }
        break;
      case 'Insert at Index':
        if (value != null && index != null) {
          if (_linkedListStructure.insertAt(index, value)) {
            _operationMessage = 'Inserted $value at index $index';
          } else {
            _operationMessage = 'Failed to insert: Invalid index';
          }
        } else {
          _operationMessage = 'Please provide both value and index';
        }
        break;
      case 'Remove Head':
        final removed = _linkedListStructure.removeHead();
        if (removed != null) {
          _operationMessage = 'Removed $removed from the head';
        } else {
          _operationMessage = 'Failed to remove: List is empty';
        }
        break;
      case 'Remove Tail':
        final removed = _linkedListStructure.removeTail();
        if (removed != null) {
          _operationMessage = 'Removed $removed from the tail';
        } else {
          _operationMessage = 'Failed to remove: List is empty';
        }
        break;
      case 'Search':
        if (value != null) {
          final foundIndex = _linkedListStructure.search(value);
          if (foundIndex != -1) {
            _operationMessage = 'Found $value at index $foundIndex';
          } else {
            _operationMessage = 'Value $value not found in the list';
          }
        } else {
          _operationMessage = 'Please provide a value to search';
        }
        break;
    }
  }

  // Perform stack operations
  void _performStackOperation(String operation, int? value) {
    switch (operation) {
      case 'Push':
        if (value != null) {
          if (_stackStructure.push(value)) {
            _operationMessage = 'Pushed $value onto the stack';
          } else {
            _operationMessage = 'Failed to push: Stack is full';
          }
        } else {
          _operationMessage = 'Please provide a value';
        }
        break;
      case 'Pop':
        final popped = _stackStructure.pop();
        if (popped != null) {
          _operationMessage = 'Popped $popped from the stack';
        } else {
          _operationMessage = 'Failed to pop: Stack is empty';
        }
        break;
      case 'Peek':
        final top = _stackStructure.peek();
        if (top != null) {
          _operationMessage = 'Top element is $top';
        } else {
          _operationMessage = 'Stack is empty';
        }
        break;
    }
  }

  // Perform queue operations
  void _performQueueOperation(String operation, int? value) {
    switch (operation) {
      case 'Enqueue':
        if (value != null) {
          if (_queueStructure.enqueue(value)) {
            _operationMessage = 'Enqueued $value to the queue';
          } else {
            _operationMessage = 'Failed to enqueue: Queue is full';
          }
        } else {
          _operationMessage = 'Please provide a value';
        }
        break;
      case 'Dequeue':
        final dequeued = _queueStructure.dequeue();
        if (dequeued != null) {
          _operationMessage = 'Dequeued $dequeued from the queue';
        } else {
          _operationMessage = 'Failed to dequeue: Queue is empty';
        }
        break;
      case 'Peek':
        final front = _queueStructure.peek();
        if (front != null) {
          _operationMessage = 'Front element is $front';
        } else {
          _operationMessage = 'Queue is empty';
        }
        break;
    }
  }

  // Perform tree operations
  void _performTreeOperation(String operation, int? value) {
    switch (operation) {
      case 'Insert':
        if (value != null) {
          if (_treeStructure.insert(value)) {
            _operationMessage = 'Inserted $value into the tree';
          } else {
            _operationMessage = 'Failed to insert: Value already exists';
          }
        } else {
          _operationMessage = 'Please provide a value';
        }
        break;
      case 'Search':
        if (value != null) {
          if (_treeStructure.search(value)) {
            _operationMessage = 'Found $value in the tree';
          } else {
            _operationMessage = 'Value $value not found in the tree';
          }
        } else {
          _operationMessage = 'Please provide a value to search';
        }
        break;
      case 'In-order Traversal':
        final result = _treeStructure.inOrderTraversal();
        _operationMessage = 'In-order traversal: ${result.join(', ')}';
        break;
      case 'Pre-order Traversal':
        final result = _treeStructure.preOrderTraversal();
        _operationMessage = 'Pre-order traversal: ${result.join(', ')}';
        break;
      case 'Post-order Traversal':
        final result = _treeStructure.postOrderTraversal();
        _operationMessage = 'Post-order traversal: ${result.join(', ')}';
        break;
    }
  }

  // Perform graph operations
  void _performGraphOperation(String operation, int? value, int? index) {
    switch (operation) {
      case 'Add Node':
        if (value != null) {
          if (_graphStructure.addNode(value)) {
            _operationMessage = 'Added node $value to the graph';
          } else {
            _operationMessage = 'Failed to add: Node already exists';
          }
        } else {
          _operationMessage = 'Please provide a value';
        }
        break;
      case 'Add Edge':
        if (value != null && index != null) {
          if (_graphStructure.addEdge(value, index)) {
            _operationMessage = 'Added edge from $value to $index';
          } else {
            _operationMessage = 'Failed to add edge: Invalid nodes';
          }
        } else {
          _operationMessage = 'Please provide both from and to node values';
        }
        break;
      case 'BFS Traversal':
        if (value != null) {
          final result = _graphStructure.bfsTraversal(value);
          if (result.isNotEmpty) {
            _operationMessage = 'BFS traversal from $value: ${result.join(', ')}';
          } else {
            _operationMessage = 'Failed: Invalid start node or empty graph';
          }
        } else {
          _operationMessage = 'Please provide a start node value';
        }
        break;
      case 'DFS Traversal':
        if (value != null) {
          final result = _graphStructure.dfsTraversal(value);
          if (result.isNotEmpty) {
            _operationMessage = 'DFS traversal from $value: ${result.join(', ')}';
          } else {
            _operationMessage = 'Failed: Invalid start node or empty graph';
          }
        } else {
          _operationMessage = 'Please provide a start node value';
        }
        break;
    }
  }

  // Build data structure type selector
  Widget _buildDataStructureTypeSelector() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Data Structure Type',
          style: TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.bold,
            color: _textColor,
          ),
        ),
        const SizedBox(height: 8),
        SingleChildScrollView(
          scrollDirection: Axis.horizontal,
          child: Row(
            children: _dataStructureTypes.map((type) {
              final isSelected = type == _currentDataStructureType;
              return Padding(
                padding: const EdgeInsets.only(right: 8),
                child: ChoiceChip(
                  label: Text(type),
                  selected: isSelected,
                  onSelected: (selected) {
                    if (selected) {
                      setState(() {
                        _currentDataStructureType = type;
                        _operationMessage = '';
                        _showExplanation = false;
                      });
                    }
                  },
                  backgroundColor: Colors.grey.withAlpha(50),
                  selectedColor: _primaryColor.withAlpha(100),
                ),
              );
            }).toList(),
          ),
        ),
      ],
    );
  }

  // Build operation controls based on the selected data structure
  Widget _buildOperationControls() {
    final operations = _getOperationsForDataStructure();

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Operations',
          style: TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.bold,
            color: _textColor,
          ),
        ),
        const SizedBox(height: 8),

        // Input fields
        Row(
          children: [
            Expanded(
              child: TextField(
                controller: _valueController,
                decoration: InputDecoration(
                  labelText: 'Value',
                  border: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(8),
                  ),
                  contentPadding: const EdgeInsets.symmetric(
                    horizontal: 12,
                    vertical: 8,
                  ),
                ),
                keyboardType: TextInputType.number,
              ),
            ),
            const SizedBox(width: 8),
            Expanded(
              child: TextField(
                controller: _indexController,
                decoration: InputDecoration(
                  labelText: 'Index/Position',
                  border: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(8),
                  ),
                  contentPadding: const EdgeInsets.symmetric(
                    horizontal: 12,
                    vertical: 8,
                  ),
                ),
                keyboardType: TextInputType.number,
              ),
            ),
          ],
        ),

        const SizedBox(height: 8),

        // Operation buttons
        Wrap(
          spacing: 8,
          runSpacing: 8,
          children: operations.map((operation) {
            return ElevatedButton(
              onPressed: _isAnimating
                  ? null
                  : () => _performOperation(operation),
              style: ElevatedButton.styleFrom(
                backgroundColor: _primaryColor,
                foregroundColor: Colors.white,
                padding: const EdgeInsets.symmetric(
                  horizontal: 12,
                  vertical: 8,
                ),
              ),
              child: Text(operation),
            );
          }).toList(),
        ),

        const SizedBox(height: 8),

        // Reset button
        OutlinedButton(
          onPressed: () {
            setState(() {
              _initializeDataStructures();
              _operationMessage = 'Data structures reset to initial state';
            });
          },
          style: OutlinedButton.styleFrom(
            foregroundColor: _primaryColor,
            side: BorderSide(color: _primaryColor),
          ),
          child: const Text('Reset'),
        ),
      ],
    );
  }

  // Get operations for the current data structure
  List<String> _getOperationsForDataStructure() {
    switch (_currentDataStructureType) {
      case 'Array':
        return ['Insert', 'Remove', 'Search'];
      case 'Linked List':
        return [
          'Insert at Head',
          'Insert at Tail',
          'Insert at Index',
          'Remove Head',
          'Remove Tail',
          'Search'
        ];
      case 'Stack':
        return ['Push', 'Pop', 'Peek'];
      case 'Queue':
        return ['Enqueue', 'Dequeue', 'Peek'];
      case 'Binary Tree':
        return [
          'Insert',
          'Search',
          'In-order Traversal',
          'Pre-order Traversal',
          'Post-order Traversal'
        ];
      case 'Graph':
        return ['Add Node', 'Add Edge', 'BFS Traversal', 'DFS Traversal'];
      default:
        return [];
    }
  }

  // Build visualization area based on the selected data structure
  Widget _buildVisualizationArea() {
    return Container(
      height: 200,
      width: double.infinity,
      decoration: BoxDecoration(
        color: Colors.grey.withAlpha(20),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: Colors.grey.withAlpha(100)),
      ),
      child: _buildVisualization(),
    );
  }

  // Build visualization based on the selected data structure
  Widget _buildVisualization() {
    switch (_currentDataStructureType) {
      case 'Array':
        return CustomPaint(
          painter: ArrayPainter(
            elements: _arrayStructure.elements,
            highlightIndex: null,
            primaryColor: _primaryColor,
            highlightColor: Colors.orange,
            textColor: _textColor,
            animationValue: _isAnimating ? _animationController.value : 1.0,
          ),
        );
      case 'Linked List':
        return Center(
          child: Text(
            'Linked List: ${_linkedListStructure.toList().join(' -> ')}',
            style: TextStyle(
              color: _textColor,
              fontSize: 16,
            ),
          ),
        );
      case 'Stack':
        return Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Text(
                'Stack (top to bottom):',
                style: TextStyle(
                  color: _textColor,
                  fontSize: 16,
                  fontWeight: FontWeight.bold,
                ),
              ),
              const SizedBox(height: 8),
              Text(
                _stackStructure.elements.reversed.join(', '),
                style: TextStyle(
                  color: _textColor,
                  fontSize: 16,
                ),
              ),
            ],
          ),
        );
      case 'Queue':
        return Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Text(
                'Queue (front to back):',
                style: TextStyle(
                  color: _textColor,
                  fontSize: 16,
                  fontWeight: FontWeight.bold,
                ),
              ),
              const SizedBox(height: 8),
              Text(
                _queueStructure.elements.join(', '),
                style: TextStyle(
                  color: _textColor,
                  fontSize: 16,
                ),
              ),
            ],
          ),
        );
      case 'Binary Tree':
        return Center(
          child: Text(
            'Binary Tree: In-order traversal: ${_treeStructure.inOrderTraversal().join(', ')}',
            style: TextStyle(
              color: _textColor,
              fontSize: 16,
            ),
          ),
        );
      case 'Graph':
        return Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Text(
                'Graph Nodes: ${_graphStructure.nodes.join(', ')}',
                style: TextStyle(
                  color: _textColor,
                  fontSize: 16,
                ),
              ),
              const SizedBox(height: 8),
              Text(
                'Edges: ${_graphStructure.edges.map((e) => "${e.from}->${e.to}").join(', ')}',
                style: TextStyle(
                  color: _textColor,
                  fontSize: 16,
                ),
              ),
            ],
          ),
        );
      default:
        return const Center(
          child: Text('Select a data structure type'),
        );
    }
  }

  // Build explanation panel
  Widget _buildExplanationPanel() {
    if (!_showExplanation) {
      return TextButton.icon(
        onPressed: () {
          setState(() {
            _showExplanation = true;
          });
        },
        icon: const Icon(Icons.info_outline),
        label: const Text('Show Explanation'),
      );
    }

    String explanation = '';
    switch (_currentDataStructureType) {
      case 'Array':
        explanation =
            'An array is a collection of elements stored at contiguous memory locations. '
            'It allows random access to elements using indices. Operations: '
            'Insert (O(n)), Remove (O(n)), Search (O(n)).';
        break;
      case 'Linked List':
        explanation =
            'A linked list is a linear data structure where elements are stored in nodes. '
            'Each node contains data and a reference to the next node. Operations: '
            'Insert at Head (O(1)), Insert at Tail (O(n)), Remove Head (O(1)), Remove Tail (O(n)), Search (O(n)).';
        break;
      case 'Stack':
        explanation =
            'A stack is a linear data structure that follows the Last In First Out (LIFO) principle. '
            'Operations: Push (O(1)), Pop (O(1)), Peek (O(1)).';
        break;
      case 'Queue':
        explanation =
            'A queue is a linear data structure that follows the First In First Out (FIFO) principle. '
            'Operations: Enqueue (O(1)), Dequeue (O(1)), Peek (O(1)).';
        break;
      case 'Binary Tree':
        explanation =
            'A binary tree is a hierarchical data structure where each node has at most two children. '
            'A binary search tree is a binary tree where nodes are ordered: left child < parent < right child. '
            'Operations: Insert (O(log n)), Search (O(log n)), Traversals (O(n)).';
        break;
      case 'Graph':
        explanation =
            'A graph is a non-linear data structure consisting of nodes (vertices) and edges. '
            'It represents relationships between pairs of objects. Operations: '
            'Add Node (O(1)), Add Edge (O(1)), BFS Traversal (O(V+E)), DFS Traversal (O(V+E)).';
        break;
    }

    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: Colors.grey.withAlpha(20),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: Colors.grey.withAlpha(100)),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                'Explanation',
                style: TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.bold,
                  color: _textColor,
                ),
              ),
              IconButton(
                icon: const Icon(Icons.close),
                onPressed: () {
                  setState(() {
                    _showExplanation = false;
                  });
                },
                padding: EdgeInsets.zero,
                constraints: const BoxConstraints(),
              ),
            ],
          ),
          const SizedBox(height: 8),
          Text(
            explanation,
            style: TextStyle(
              color: _textColor,
              fontSize: 14,
            ),
          ),
          const SizedBox(height: 8),
          Align(
            alignment: Alignment.centerRight,
            child: TextButton(
              onPressed: () {
                widget.onStateChanged?.call(true);
              },
              child: const Text('Mark as Completed'),
            ),
          ),
        ],
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
        side: BorderSide(color: _primaryColor.withAlpha(77)),
      ),
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Title and description
            Text(
              widget.data['title'] ?? 'Data Structure Visualizer',
              style: TextStyle(
                fontSize: 20,
                fontWeight: FontWeight.bold,
                color: _textColor,
              ),
            ),
            const SizedBox(height: 8),
            Text(
              widget.data['description'] ??
                  'Visualize and interact with common data structures',
              style: TextStyle(
                fontSize: 14,
                color: _textColor.withAlpha(179),
              ),
            ),
            const SizedBox(height: 16),

            // Data structure type selector
            _buildDataStructureTypeSelector(),

            const SizedBox(height: 16),

            // Operation controls
            _buildOperationControls(),

            const SizedBox(height: 16),

            // Visualization area
            _buildVisualizationArea(),

            const SizedBox(height: 16),

            // Operation message
            if (_operationMessage.isNotEmpty)
              Container(
                width: double.infinity,
                padding: const EdgeInsets.all(8),
                decoration: BoxDecoration(
                  color: _primaryColor.withAlpha(26),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Text(
                  _operationMessage,
                  style: TextStyle(
                    color: _primaryColor,
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ),

            const SizedBox(height: 16),

            // Explanation
            _buildExplanationPanel(),
          ],
        ),
      ),
    );
  }
}
