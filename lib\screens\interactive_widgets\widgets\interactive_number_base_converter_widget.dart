import 'package:flutter/material.dart';
import 'dart:math' as math;

/// A widget that allows users to convert numbers between different bases
class InteractiveNumberBaseConverterWidget extends StatefulWidget {
  /// The data for this widget
  final Map<String, dynamic> data;

  /// Optional callback when the widget state changes
  final Function(bool isCompleted)? onStateChanged;

  const InteractiveNumberBaseConverterWidget({
    Key? key,
    required this.data,
    this.onStateChanged,
  }) : super(key: key);

  /// Factory constructor to create the widget from JSON data
  factory InteractiveNumberBaseConverterWidget.fromData(
      Map<String, dynamic> data) {
    return InteractiveNumberBaseConverterWidget(
      data: data,
    );
  }

  @override
  State<InteractiveNumberBaseConverterWidget> createState() =>
      _InteractiveNumberBaseConverterWidgetState();
}

class _InteractiveNumberBaseConverterWidgetState
    extends State<InteractiveNumberBaseConverterWidget> {
  // State variables
  late String _title;
  late Color _primaryColor;
  late Color _secondaryColor;
  late Color _accentColor;
  late bool _showSteps;
  late bool _isCompleted;
  
  // Conversion state
  late String _inputValue;
  late int _fromBase;
  late int _toBase;
  String _result = '';
  List<String> _conversionSteps = [];
  bool _showResult = false;
  String _errorMessage = '';
  
  // Available bases
  final List<int> _availableBases = [2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16];
  
  // Controllers
  late TextEditingController _inputController;

  @override
  void initState() {
    super.initState();
    _initializeFromData();
    _inputController = TextEditingController(text: _inputValue);
  }

  @override
  void dispose() {
    _inputController.dispose();
    super.dispose();
  }

  void _initializeFromData() {
    _title = widget.data['title'] ?? 'Number Base Converter';
    _primaryColor = _colorFromHex(widget.data['primaryColor'] ?? '#2196F3');
    _secondaryColor = _colorFromHex(widget.data['secondaryColor'] ?? '#4CAF50');
    _accentColor = _colorFromHex(widget.data['accentColor'] ?? '#FF9800');
    _showSteps = widget.data['showSteps'] ?? true;
    _isCompleted = false;
    
    // Initialize conversion values
    _inputValue = widget.data['defaultInputValue'] ?? '101';
    _fromBase = widget.data['defaultFromBase'] ?? 2;
    _toBase = widget.data['defaultToBase'] ?? 10;
    
    // Perform initial conversion
    _performConversion();
  }

  Color _colorFromHex(String hexColor) {
    hexColor = hexColor.replaceAll('#', '');
    if (hexColor.length == 6) {
      hexColor = 'FF$hexColor';
    }
    return Color(int.parse(hexColor, radix: 16));
  }

  void _performConversion() {
    setState(() {
      _errorMessage = '';
      _conversionSteps = [];
      _showResult = false;
      
      try {
        // Validate input for the given base
        if (!_isValidForBase(_inputValue, _fromBase)) {
          _errorMessage = 'Invalid input for base $_fromBase';
          return;
        }
        
        // Convert to decimal first (if not already in decimal)
        int decimalValue;
        if (_fromBase == 10) {
          decimalValue = int.parse(_inputValue);
          _conversionSteps.add('Input is already in decimal: $_inputValue');
        } else {
          decimalValue = _convertToDecimal(_inputValue, _fromBase);
          _conversionSteps.add('Step 1: Convert $_inputValue (base $_fromBase) to decimal');
          
          // Add detailed steps for conversion to decimal
          _addToDecimalSteps(_inputValue, _fromBase);
          
          _conversionSteps.add('Decimal value: $decimalValue');
        }
        
        // Convert from decimal to target base (if not already in decimal)
        if (_toBase == 10) {
          _result = decimalValue.toString();
          _conversionSteps.add('Target base is decimal, so result is: $decimalValue');
        } else {
          _conversionSteps.add('Step 2: Convert $decimalValue (decimal) to base $_toBase');
          
          // Add detailed steps for conversion from decimal
          _result = _convertFromDecimal(decimalValue, _toBase);
          
          _conversionSteps.add('Final result in base $_toBase: $_result');
        }
        
        _showResult = true;
        
        // Notify parent of completion if needed
        if (widget.onStateChanged != null) {
          widget.onStateChanged!(true);
        }
      } catch (e) {
        _errorMessage = 'Error: $e';
      }
    });
  }

  bool _isValidForBase(String value, int base) {
    final validChars = '0123456789ABCDEF'.substring(0, base);
    final regex = RegExp('^[${validChars}]+\$', caseSensitive: false);
    return regex.hasMatch(value.toUpperCase());
  }

  int _convertToDecimal(String value, int fromBase) {
    value = value.toUpperCase();
    int result = 0;
    
    for (int i = 0; i < value.length; i++) {
      int digitValue = _getDigitValue(value[value.length - 1 - i]);
      result += digitValue * math.pow(fromBase, i).toInt();
    }
    
    return result;
  }

  void _addToDecimalSteps(String value, int fromBase) {
    value = value.toUpperCase();
    List<String> calculations = [];
    
    for (int i = 0; i < value.length; i++) {
      int position = value.length - 1 - i;
      int digitValue = _getDigitValue(value[position]);
      int placeValue = math.pow(fromBase, i).toInt();
      int contribution = digitValue * placeValue;
      
      calculations.add('${value[position]} × $_fromBase^$i = $digitValue × $placeValue = $contribution');
    }
    
    _conversionSteps.add(calculations.join('\n'));
    _conversionSteps.add('Sum all contributions: ${calculations.map((s) => s.split(' = ').last).join(' + ')}');
  }

  String _convertFromDecimal(int value, int toBase) {
    if (value == 0) return '0';
    
    List<String> digits = [];
    int remaining = value;
    List<String> steps = [];
    
    while (remaining > 0) {
      int remainder = remaining % toBase;
      steps.add('$remaining ÷ $_toBase = ${remaining ~/ toBase} remainder $remainder (${_getDigitChar(remainder)})');
      digits.insert(0, _getDigitChar(remainder));
      remaining = remaining ~/ toBase;
    }
    
    _conversionSteps.add(steps.join('\n'));
    return digits.join('');
  }

  int _getDigitValue(String digit) {
    if (digit.codeUnitAt(0) >= '0'.codeUnitAt(0) && 
        digit.codeUnitAt(0) <= '9'.codeUnitAt(0)) {
      return digit.codeUnitAt(0) - '0'.codeUnitAt(0);
    } else {
      return digit.codeUnitAt(0) - 'A'.codeUnitAt(0) + 10;
    }
  }

  String _getDigitChar(int value) {
    if (value < 10) {
      return value.toString();
    } else {
      return String.fromCharCode('A'.codeUnitAt(0) + value - 10);
    }
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withOpacity(0.2),
            spreadRadius: 1,
            blurRadius: 3,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            _title,
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
              color: _primaryColor,
            ),
          ),
          const SizedBox(height: 16),
          
          // Input section
          Row(
            children: [
              Expanded(
                flex: 2,
                child: TextField(
                  controller: _inputController,
                  decoration: InputDecoration(
                    labelText: 'Input Value',
                    border: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(8),
                    ),
                    contentPadding: const EdgeInsets.symmetric(
                      horizontal: 12,
                      vertical: 8,
                    ),
                  ),
                  onChanged: (value) {
                    _inputValue = value;
                  },
                ),
              ),
              const SizedBox(width: 8),
              Expanded(
                flex: 1,
                child: DropdownButtonFormField<int>(
                  value: _fromBase,
                  decoration: InputDecoration(
                    labelText: 'From Base',
                    border: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(8),
                    ),
                    contentPadding: const EdgeInsets.symmetric(
                      horizontal: 12,
                      vertical: 8,
                    ),
                  ),
                  items: _availableBases.map((base) {
                    return DropdownMenuItem<int>(
                      value: base,
                      child: Text('Base $base'),
                    );
                  }).toList(),
                  onChanged: (value) {
                    if (value != null) {
                      setState(() {
                        _fromBase = value;
                      });
                    }
                  },
                ),
              ),
              const SizedBox(width: 8),
              Expanded(
                flex: 1,
                child: DropdownButtonFormField<int>(
                  value: _toBase,
                  decoration: InputDecoration(
                    labelText: 'To Base',
                    border: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(8),
                    ),
                    contentPadding: const EdgeInsets.symmetric(
                      horizontal: 12,
                      vertical: 8,
                    ),
                  ),
                  items: _availableBases.map((base) {
                    return DropdownMenuItem<int>(
                      value: base,
                      child: Text('Base $base'),
                    );
                  }).toList(),
                  onChanged: (value) {
                    if (value != null) {
                      setState(() {
                        _toBase = value;
                      });
                    }
                  },
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),
          
          // Convert button
          Center(
            child: ElevatedButton(
              onPressed: () {
                _performConversion();
              },
              style: ElevatedButton.styleFrom(
                backgroundColor: _primaryColor,
                padding: const EdgeInsets.symmetric(
                  horizontal: 24,
                  vertical: 12,
                ),
              ),
              child: const Text('Convert'),
            ),
          ),
          const SizedBox(height: 16),
          
          // Error message
          if (_errorMessage.isNotEmpty)
            Container(
              padding: const EdgeInsets.all(8),
              decoration: BoxDecoration(
                color: Colors.red[50],
                borderRadius: BorderRadius.circular(4),
                border: Border.all(color: Colors.red[300]!),
              ),
              child: Text(
                _errorMessage,
                style: TextStyle(color: Colors.red[700]),
              ),
            ),
          
          // Conversion steps
          if (_showSteps && _conversionSteps.isNotEmpty && _errorMessage.isEmpty)
            Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                const SizedBox(height: 16),
                Text(
                  'Conversion Steps:',
                  style: TextStyle(
                    fontWeight: FontWeight.bold,
                    color: _secondaryColor,
                  ),
                ),
                const SizedBox(height: 8),
                Container(
                  padding: const EdgeInsets.all(8),
                  decoration: BoxDecoration(
                    color: Colors.grey[50],
                    borderRadius: BorderRadius.circular(4),
                    border: Border.all(color: Colors.grey[300]!),
                  ),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: _conversionSteps.map((step) {
                      return Padding(
                        padding: const EdgeInsets.symmetric(vertical: 4),
                        child: Text(step),
                      );
                    }).toList(),
                  ),
                ),
              ],
            ),
          
          // Result
          if (_showResult && _errorMessage.isEmpty)
            Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                const SizedBox(height: 16),
                Text(
                  'Result:',
                  style: TextStyle(
                    fontWeight: FontWeight.bold,
                    color: _accentColor,
                  ),
                ),
                const SizedBox(height: 8),
                Container(
                  padding: const EdgeInsets.all(12),
                  decoration: BoxDecoration(
                    color: _accentColor.withOpacity(0.1),
                    borderRadius: BorderRadius.circular(8),
                    border: Border.all(color: _accentColor.withOpacity(0.3)),
                  ),
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Text(
                        '$_inputValue (Base $_fromBase) = ',
                        style: const TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      Text(
                        '$_result (Base $_toBase)',
                        style: TextStyle(
                          fontSize: 18,
                          fontWeight: FontWeight.bold,
                          color: _accentColor,
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
          
          // Widget name tag
          if (widget.data['showNameTag'] ?? true)
            Padding(
              padding: const EdgeInsets.only(top: 8),
              child: Text(
                'InteractiveNumberBaseConverter',
                style: TextStyle(
                  color: Colors.grey[600],
                  fontSize: 12,
                  fontStyle: FontStyle.italic,
                ),
              ),
            ),
        ],
      ),
    );
  }
}
