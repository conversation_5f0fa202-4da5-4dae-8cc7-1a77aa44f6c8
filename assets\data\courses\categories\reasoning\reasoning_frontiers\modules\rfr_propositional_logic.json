{"id": "rfr_propositional_logic", "title": "Propositional Logic: Formal Deduction", "description": "Explore the formal system of propositional logic to analyze and construct valid deductive arguments.", "estimated_lesson_duration_minutes": 75, "lessons": [{"id": "rfr-pl-l1-language-prop-logic", "title": "The Language of Propositional Logic: Statements and Connectives", "description": "Learn the symbols and grammar of propositional logic.", "xp_reward": 100, "content_blocks": [{"type": "text", "content": "Propositional logic (also known as sentential logic) is a branch of formal logic that deals with propositions (statements that can be true or false) and how they can be combined using logical connectives."}, {"type": "heading", "content": "Atomic and Compound Propositions"}, {"type": "text", "content": "An **atomic proposition** is a simple statement that cannot be broken down further, e.g., 'It is raining' (P). A **compound proposition** is formed by combining one or more atomic propositions using logical connectives, e.g., 'It is raining (P) AND the wind is blowing (Q)'."}, {"type": "heading", "content": "Logical Connectives"}, {"type": "list", "items": ["**Negation (NOT):** Symbol: ¬ (or ~). Reverses the truth value of a proposition. '¬P' means 'It is not the case that P'.", "**Conjunction (AND):** Symbol: ∧ (or &). 'P ∧ Q' is true if and only if both P and Q are true.", "**Disjunction (OR):** Symbol: ∨. 'P ∨ Q' is true if at least one of P or Q (or both) is true (inclusive OR).", "**Conditional (IF...THEN):** Symbol: → (or ⇒). 'P → Q' is false if and only if P is true and Q is false. Otherwise, it's true. P is the antecedent, Q is the consequent.", "**Biconditional (IF AND ONLY IF):** Symbol: ↔ (or ⇔). 'P ↔ Q' is true if and only if P and Q have the same truth value."]}, {"type": "interactive_element_placeholder", "interactive_element": {"type": "interactive_symbol_connective_matcher", "prompt": "Match each logical symbol to its name and meaning.", "symbols": [{"id": "sym_not", "symbol_text": "¬"}, {"id": "sym_and", "symbol_text": "∧"}, {"id": "sym_or", "symbol_text": "∨"}, {"id": "sym_if", "symbol_text": "→"}, {"id": "sym_iff", "symbol_text": "↔"}], "meanings": [{"id": "mean_not", "name": "Negation", "meaning": "NOT (reverses truth value)"}, {"id": "mean_and", "name": "Conjunction", "meaning": "AND (true if both are true)"}, {"id": "mean_or", "name": "Disjunction", "meaning": "OR (true if at least one is true)"}, {"id": "mean_if", "name": "Conditional", "meaning": "IF...THEN (false only if antecedent true, consequent false)"}, {"id": "mean_iff", "name": "Biconditional", "meaning": "IF AND ONLY IF (true if both have same truth value)"}], "correct_pairs": [{"symbol_id": "sym_not", "meaning_id": "mean_not"}, {"symbol_id": "sym_and", "meaning_id": "mean_and"}, {"symbol_id": "sym_or", "meaning_id": "mean_or"}, {"symbol_id": "sym_if", "meaning_id": "mean_if"}, {"symbol_id": "sym_iff", "meaning_id": "mean_iff"}]}}]}, {"id": "rfr-pl-l2-truth-tables", "title": "Truth Tables: Evaluating Logical Statements", "description": "Determine the truth value of compound propositions.", "xp_reward": 100, "content_blocks": [{"type": "text", "content": "Truth tables are a systematic way to determine the truth value of a compound proposition for every possible combination of truth values of its atomic components."}, {"type": "heading", "content": "Constructing a Truth Table"}, {"type": "text", "content": "1. List all atomic propositions.\n2. Create columns for each atomic proposition and for the compound proposition.\n3. List all possible truth value combinations for the atomic propositions (2^n rows, where n is the number of atomic props).\n4. Fill in the truth values for the compound proposition based on the definitions of the connectives."}, {"type": "example", "content": "Truth table for P ∧ Q:\n| P | Q | P ∧ Q |\n|---|---|-------|\n| T | T |   T   |\n| T | F |   F   |\n| F | T |   F   |\n| F | F |   F   |"}, {"type": "interactive_element_placeholder", "interactive_element": {"type": "interactive_truth_table_generator", "prompt": "Complete the truth table for the proposition: P ∨ (¬Q)", "proposition_string": "P ∨ (¬Q)", "atomic_propositions": ["P", "Q"], "solution_truth_values": [true, true, false, true]}}]}, {"id": "rfr-pl-l3-logical-equivalence", "title": "Logical Equivalence and Tautologies", "description": "Understand when statements have the same truth value.", "xp_reward": 100, "content_blocks": [{"type": "text", "content": "Two propositions are **logically equivalent** if they have the same truth value for all possible truth value assignments of their atomic components. Their truth tables will have identical final columns."}, {"type": "example", "content": "<PERSON>'s Law: ¬(P ∧ Q) is logically equivalent to (¬P ∨ ¬Q)."}, {"type": "heading", "content": "Tautologies, Contradictions, and Contingencies"}, {"type": "list", "items": ["**Tautology:** A proposition that is always true, regardless of the truth values of its components (e.g., P ∨ ¬P).", "**Contradiction:** A proposition that is always false (e.g., P ∧ ¬P).", "**Contingency:** A proposition that is neither a tautology nor a contradiction."]}, {"type": "interactive_element_placeholder", "interactive_element": {"type": "interactive_equivalence_checker", "prompt": "Are the propositions 'P → Q' and '¬P ∨ Q' logically equivalent? Use a truth table to determine.", "proposition1_string": "P → Q", "proposition2_string": "¬P ∨ Q", "are_equivalent": true, "explanation": "Yes, they are logically equivalent. This is a fundamental equivalence known as Material Implication."}}]}, {"id": "rfr-pl-l4-rules-of-inference", "title": "Rules of Inference: Deriving Conclusions Logically", "description": "Apply formal rules like modus ponens and modus tollens.", "xp_reward": 100, "content_blocks": [{"type": "text", "content": "Rules of inference are simple, valid argument forms that can be used to derive new true statements from existing true statements in a formal proof."}, {"type": "heading", "content": "Common Rules of Inference"}, {"type": "list", "items": ["**<PERSON><PERSON> (MP):** From P → Q and P, infer Q.", "**<PERSON><PERSON> (MT):** From P → Q and ¬Q, infer ¬P.", "**Hypothetical Syllogism (HS):** From P → Q and Q → R, infer P → R.", "**Disjunctive Syllogism (DS):** From P ∨ Q and ¬P, infer Q (or from P ∨ Q and ¬Q, infer P).", "**Addition (Add):** From P, infer P ∨ Q.", "**Simplification (Simp):** From P ∧ Q, infer P (or infer Q)."]}, {"type": "interactive_element_placeholder", "interactive_element": {"type": "interactive_rule_of_inference_applier", "prompt": "Given the premises, what conclusion can be derived using the specified rule?", "scenarios": [{"premises": ["If it rains, the game is cancelled.", "It rains."], "rule": "<PERSON><PERSON>", "correct_conclusion": "The game is cancelled."}, {"premises": ["If the key fits, the door opens.", "The door does not open."], "rule": "<PERSON><PERSON>", "correct_conclusion": "The key does not fit."}]}}]}, {"id": "rfr-pl-l5-constructing-proofs", "title": "Constructing Formal Proofs", "description": "Derive conclusions from premises using rules of inference.", "xp_reward": 100, "content_blocks": [{"type": "text", "content": "A formal proof in propositional logic is a sequence of statements, where each statement is either a premise or follows from preceding statements by a rule of inference, ultimately leading to the desired conclusion."}, {"type": "heading", "content": "Steps in Constructing a Proof"}, {"type": "list", "items": ["List the premises.", "Apply rules of inference to the premises and any derived statements.", "Each new statement must be justified by citing the rule and the line numbers of the statements it's derived from.", "Continue until the conclusion is derived."]}, {"type": "interactive_element_placeholder", "interactive_element": {"type": "interactive_formal_proof_builder", "prompt": "Construct a formal proof for the following argument:\nPremises: 1. P → Q, 2. P\nConclusion: Q", "premises_list": ["P → Q", "P"], "target_conclusion": "Q", "available_rules": ["<PERSON><PERSON>", "<PERSON><PERSON>", "Addition", "Simplification"], "solution_steps": [{"line": 3, "statement": "Q", "justification": "<PERSON><PERSON> (1, 2)"}]}}]}], "module_test": {"id": "rfr-pl-mt1-propositional-prover", "title": "Propositional Prover", "description": "Construct formal proofs in propositional logic.", "estimated_duration_minutes": 30, "questions": [{"id": "rfr-pl-q1", "question_type": "test_formal_proof_construction", "problem_statement": "Given premises: 1. A → B, 2. B → C, 3. <PERSON>. Derive the conclusion: C.", "available_rules": ["<PERSON><PERSON>", "<PERSON><PERSON>", "Hypothetical Syllogism"], "max_steps": 3, "correct_proof_outline": [{"statement": "B", "justification_pattern": "Modus Ponens.*1.*3|Modus Ponens.*3.*1"}, {"statement": "C", "justification_pattern": "Modus Ponens.*2.*(previous_line_B)|Modus Ponens.*(previous_line_B).*2"}], "feedback_correct": "Excellent proof construction!", "feedback_incorrect": "Review the rules of inference and ensure each step is correctly justified."}, {"id": "rfr-pl-q2", "question_type": "multiple_choice_text", "question_text": "Which of the following is a tautology?", "options": [{"id": "q2opt1", "text": "P ∧ Q", "is_correct": false}, {"id": "q2opt2", "text": "P → (Q → P)", "is_correct": true}, {"id": "q2opt3", "text": "P ↔ ¬P", "is_correct": false}], "feedback_correct": "Correct! P → (Q → P) is always true, making it a tautology.", "feedback_incorrect": "A tautology is a statement that is true in every possible interpretation (i.e., for all truth values of its components)."}]}}