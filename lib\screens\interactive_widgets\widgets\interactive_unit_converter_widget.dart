import 'package:flutter/material.dart';
import 'dart:math' as math;

/// A widget that allows users to convert between different units of measurement
class InteractiveUnitConverterWidget extends StatefulWidget {
  /// The data for this widget
  final Map<String, dynamic> data;

  /// Optional callback when the widget state changes
  final Function(bool isCompleted)? onStateChanged;

  const InteractiveUnitConverterWidget({
    super.key,
    required this.data,
    this.onStateChanged,
  });

  /// Factory constructor to create the widget from JSON data
  factory InteractiveUnitConverterWidget.fromData(Map<String, dynamic> data) {
    return InteractiveUnitConverterWidget(
      data: data,
    );
  }

  @override
  State<InteractiveUnitConverterWidget> createState() => _InteractiveUnitConverterWidgetState();
}

class _InteractiveUnitConverterWidgetState extends State<InteractiveUnitConverterWidget> {
  // Current category and units
  String _currentCategory = 'length';
  String _fromUnit = 'm';
  String _toUnit = 'km';

  // Input and output values
  String _inputValue = '1';
  String _outputValue = '0.001';

  // UI parameters
  Color _primaryColor = Colors.blue;
  Color _secondaryColor = Colors.orange;
  Color _accentColor = Colors.green;
  Color _textColor = Colors.black87;

  // Challenge mode parameters
  bool _challengeMode = false;
  Map<String, dynamic>? _currentChallenge;
  bool _challengeCompleted = false;

  // Available unit categories and their units
  final Map<String, Map<String, double>> _unitCategories = {
    'length': {
      'nm': 1e-9,
      'μm': 1e-6,
      'mm': 1e-3,
      'cm': 1e-2,
      'dm': 1e-1,
      'm': 1.0,
      'km': 1e3,
      'in': 0.0254,
      'ft': 0.3048,
      'yd': 0.9144,
      'mi': 1609.344,
    },
    'mass': {
      'ng': 1e-12,
      'μg': 1e-9,
      'mg': 1e-6,
      'g': 1e-3,
      'kg': 1.0,
      'ton': 1000.0,
      'oz': 0.0283495,
      'lb': 0.453592,
      'st': 6.35029,
    },
    'volume': {
      'ml': 1e-6,
      'cl': 1e-5,
      'dl': 1e-4,
      'l': 1e-3,
      'm³': 1.0,
      'in³': 1.6387e-5,
      'ft³': 0.0283168,
      'gal (US)': 0.00378541,
      'gal (UK)': 0.00454609,
    },
    'time': {
      'ns': 1e-9,
      'μs': 1e-6,
      'ms': 1e-3,
      's': 1.0,
      'min': 60.0,
      'h': 3600.0,
      'day': 86400.0,
      'week': 604800.0,
      'month': 2.628e+6,
      'year': 3.154e+7,
    },
    'temperature': {
      '°C': 1.0,
      '°F': 1.0,
      'K': 1.0,
    },
    'area': {
      'mm²': 1e-6,
      'cm²': 1e-4,
      'm²': 1.0,
      'ha': 10000.0,
      'km²': 1e6,
      'in²': 0.00064516,
      'ft²': 0.092903,
      'yd²': 0.836127,
      'acre': 4046.86,
      'mi²': 2.59e+6,
    },
    'speed': {
      'm/s': 1.0,
      'km/h': 0.277778,
      'mph': 0.44704,
      'knot': 0.514444,
      'ft/s': 0.3048,
    },
    'pressure': {
      'Pa': 1.0,
      'kPa': 1000.0,
      'MPa': 1e6,
      'bar': 1e5,
      'atm': 101325.0,
      'mmHg': 133.322,
      'inHg': 3386.39,
      'psi': 6894.76,
    },
    'energy': {
      'J': 1.0,
      'kJ': 1000.0,
      'cal': 4.184,
      'kcal': 4184.0,
      'Wh': 3600.0,
      'kWh': 3.6e+6,
      'eV': 1.602e-19,
      'BTU': 1055.06,
    },
    'data': {
      'bit': 1.0,
      'byte': 8.0,
      'KB': 8192.0,
      'MB': 8.389e+6,
      'GB': 8.59e+9,
      'TB': 8.796e+12,
    },
  };

  @override
  void initState() {
    super.initState();
    _initializeFromData();
  }

  void _initializeFromData() {
    // Initialize parameters from widget data
    _currentCategory = widget.data['initialCategory'] ?? 'length';
    _fromUnit = widget.data['initialFromUnit'] ?? _getDefaultUnit(_currentCategory);
    _toUnit = widget.data['initialToUnit'] ?? _getDefaultUnit(_currentCategory);
    _inputValue = widget.data['initialInputValue']?.toString() ?? '1';

    _challengeMode = widget.data['challengeMode'] ?? false;
    if (_challengeMode && widget.data['challenges'] != null) {
      _currentChallenge = widget.data['challenges'][0];
    }

    // Initialize colors
    if (widget.data['primaryColor'] != null) {
      _primaryColor = _colorFromHex(widget.data['primaryColor']);
    }
    if (widget.data['secondaryColor'] != null) {
      _secondaryColor = _colorFromHex(widget.data['secondaryColor']);
    }
    if (widget.data['accentColor'] != null) {
      _accentColor = _colorFromHex(widget.data['accentColor']);
    }
    if (widget.data['textColor'] != null) {
      _textColor = _colorFromHex(widget.data['textColor']);
    }

    // Calculate initial output value
    _calculateConversion();
  }

  // Helper method to convert hex color string to Color
  Color _colorFromHex(String hexColor) {
    hexColor = hexColor.replaceAll('#', '');
    if (hexColor.length == 6) {
      hexColor = 'FF$hexColor';
    }
    return Color(int.parse(hexColor, radix: 16));
  }

  // Get the default unit for a category
  String _getDefaultUnit(String category) {
    if (_unitCategories.containsKey(category)) {
      return _unitCategories[category]!.keys.first;
    }
    return '';
  }

  // Calculate the conversion
  void _calculateConversion() {
    if (_inputValue.isEmpty) {
      setState(() {
        _outputValue = '';
      });
      return;
    }

    try {
      final double input = double.parse(_inputValue);
      double output = 0.0;

      // Special case for temperature
      if (_currentCategory == 'temperature') {
        output = _convertTemperature(input, _fromUnit, _toUnit);
      } else {
        // Standard conversion using conversion factors
        final double fromFactor = _unitCategories[_currentCategory]![_fromUnit]!;
        final double toFactor = _unitCategories[_currentCategory]![_toUnit]!;
        output = input * fromFactor / toFactor;
      }

      // Format the output based on its magnitude
      String formattedOutput;
      if (output.abs() < 0.000001 || output.abs() > 1000000) {
        formattedOutput = output.toStringAsExponential(6);
      } else {
        formattedOutput = output.toStringAsFixed(6);
        // Remove trailing zeros
        while (formattedOutput.contains('.') && formattedOutput.endsWith('0')) {
          formattedOutput = formattedOutput.substring(0, formattedOutput.length - 1);
        }
        if (formattedOutput.endsWith('.')) {
          formattedOutput = formattedOutput.substring(0, formattedOutput.length - 1);
        }
      }

      setState(() {
        _outputValue = formattedOutput;
      });

      // Check if challenge is completed
      if (_challengeMode && _currentChallenge != null) {
        _checkChallengeCompleted();
      }
    } catch (e) {
      setState(() {
        _outputValue = 'Error';
      });
    }
  }

  // Special conversion for temperature
  double _convertTemperature(double value, String fromUnit, String toUnit) {
    // Convert to Kelvin first
    double kelvin;

    switch (fromUnit) {
      case '°C':
        kelvin = value + 273.15;
        break;
      case '°F':
        kelvin = (value + 459.67) * 5 / 9;
        break;
      case 'K':
        kelvin = value;
        break;
      default:
        return 0.0;
    }

    // Convert from Kelvin to target unit
    switch (toUnit) {
      case '°C':
        return kelvin - 273.15;
      case '°F':
        return kelvin * 9 / 5 - 459.67;
      case 'K':
        return kelvin;
      default:
        return 0.0;
    }
  }

  void _checkChallengeCompleted() {
    if (!_challengeMode || _currentChallenge == null) return;

    final targetCategory = _currentChallenge!['targetCategory'];
    final targetFromUnit = _currentChallenge!['targetFromUnit'];
    final targetToUnit = _currentChallenge!['targetToUnit'];
    final targetInputValue = _currentChallenge!['targetInputValue']?.toString();

    final isCompleted = _currentCategory == targetCategory &&
                        _fromUnit == targetFromUnit &&
                        _toUnit == targetToUnit &&
                        _inputValue == targetInputValue;

    if (isCompleted != _challengeCompleted) {
      setState(() {
        _challengeCompleted = isCompleted;
      });

      if (_challengeCompleted && widget.onStateChanged != null) {
        widget.onStateChanged!(true);
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withOpacity(0.2),
            spreadRadius: 1,
            blurRadius: 3,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Title
          Text(
            widget.data['title'] ?? 'Unit Converter',
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
              color: _textColor,
            ),
          ),

          const SizedBox(height: 8),

          // Description
          Text(
            widget.data['description'] ?? 'Convert between different units of measurement.',
            style: TextStyle(
              fontSize: 14,
              color: _textColor.withOpacity(0.8),
            ),
          ),

          const SizedBox(height: 16),

          // Category selector
          _buildCategorySelector(),

          const SizedBox(height: 16),

          // Input section
          Row(
            children: [
              // Input value
              Expanded(
                flex: 3,
                child: TextField(
                  controller: TextEditingController(text: _inputValue),
                  keyboardType: TextInputType.numberWithOptions(decimal: true),
                  decoration: InputDecoration(
                    labelText: 'Value',
                    border: OutlineInputBorder(),
                    contentPadding: EdgeInsets.symmetric(horizontal: 12, vertical: 8),
                  ),
                  onChanged: (value) {
                    setState(() {
                      _inputValue = value;
                      _calculateConversion();
                    });
                  },
                ),
              ),

              const SizedBox(width: 8),

              // From unit selector
              Expanded(
                flex: 2,
                child: _buildUnitSelector(
                  _fromUnit,
                  (newValue) {
                    setState(() {
                      _fromUnit = newValue!;
                      _calculateConversion();
                    });
                  },
                ),
              ),
            ],
          ),

          // Swap button
          Center(
            child: IconButton(
              icon: Icon(Icons.swap_vert, color: _primaryColor),
              onPressed: () {
                setState(() {
                  final temp = _fromUnit;
                  _fromUnit = _toUnit;
                  _toUnit = temp;
                  _calculateConversion();
                });
              },
            ),
          ),

          // Output section
          Row(
            children: [
              // Output value
              Expanded(
                flex: 3,
                child: TextField(
                  controller: TextEditingController(text: _outputValue),
                  readOnly: true,
                  decoration: InputDecoration(
                    labelText: 'Result',
                    border: OutlineInputBorder(),
                    contentPadding: EdgeInsets.symmetric(horizontal: 12, vertical: 8),
                    fillColor: Colors.grey.withOpacity(0.1),
                    filled: true,
                  ),
                ),
              ),

              const SizedBox(width: 8),

              // To unit selector
              Expanded(
                flex: 2,
                child: _buildUnitSelector(
                  _toUnit,
                  (newValue) {
                    setState(() {
                      _toUnit = newValue!;
                      _calculateConversion();
                    });
                  },
                ),
              ),
            ],
          ),

          // Challenge feedback (if in challenge mode)
          if (_challengeMode && _currentChallenge != null)
            Container(
              margin: const EdgeInsets.only(top: 16),
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: _challengeCompleted
                    ? Colors.green.withOpacity(0.1)
                    : Colors.orange.withOpacity(0.1),
                borderRadius: BorderRadius.circular(8),
                border: Border.all(
                  color: _challengeCompleted
                      ? Colors.green.withOpacity(0.3)
                      : Colors.orange.withOpacity(0.3),
                ),
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    _currentChallenge!['description'] ?? 'Complete the conversion challenge.',
                    style: TextStyle(
                      fontSize: 14,
                      color: _textColor,
                    ),
                  ),
                  const SizedBox(height: 8),
                  Text(
                    _challengeCompleted
                        ? (_currentChallenge!['successMessage'] ?? 'Great job! You\'ve completed the challenge.')
                        : (_currentChallenge!['hint'] ?? 'Select the correct units and enter the correct value.'),
                    style: TextStyle(
                      fontSize: 14,
                      fontWeight: FontWeight.bold,
                      color: _challengeCompleted ? Colors.green : Colors.orange,
                    ),
                  ),
                ],
              ),
            ),

          // Widget name tag at the bottom
          if (widget.data['showNameTag'] ?? true)
            Padding(
              padding: const EdgeInsets.only(top: 8),
              child: Text(
                'InteractiveUnitConverterWidget',
                style: TextStyle(
                  color: Colors.grey[600],
                  fontSize: 12,
                  fontStyle: FontStyle.italic,
                ),
              ),
            ),
        ],
      ),
    );
  }

  Widget _buildCategorySelector() {
    return DropdownButtonFormField<String>(
      value: _currentCategory,
      decoration: InputDecoration(
        labelText: 'Category',
        border: OutlineInputBorder(),
        contentPadding: EdgeInsets.symmetric(horizontal: 12, vertical: 8),
      ),
      items: _unitCategories.keys.map((String category) {
        return DropdownMenuItem<String>(
          value: category,
          child: Text(category.substring(0, 1).toUpperCase() + category.substring(1)),
        );
      }).toList(),
      onChanged: (String? newValue) {
        if (newValue != null && newValue != _currentCategory) {
          setState(() {
            _currentCategory = newValue;
            _fromUnit = _getDefaultUnit(newValue);
            _toUnit = _unitCategories[newValue]!.keys.elementAt(1);
            _calculateConversion();
          });
        }
      },
    );
  }

  Widget _buildUnitSelector(String currentValue, Function(String?) onChanged) {
    return DropdownButtonFormField<String>(
      value: currentValue,
      decoration: InputDecoration(
        labelText: 'Unit',
        border: OutlineInputBorder(),
        contentPadding: EdgeInsets.symmetric(horizontal: 12, vertical: 8),
      ),
      items: _unitCategories[_currentCategory]!.keys.map((String unit) {
        return DropdownMenuItem<String>(
          value: unit,
          child: Text(unit),
        );
      }).toList(),
      onChanged: onChanged,
    );
  }
}
