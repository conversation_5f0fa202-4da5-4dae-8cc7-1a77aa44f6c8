import 'package:flutter/material.dart';
import 'dart:math' as math;
import 'dart:async';

/// A widget that simulates a pendulum with adjustable parameters
class InteractivePendulumSimulationWidget extends StatefulWidget {
  /// The data for this widget
  final Map<String, dynamic> data;

  /// Optional callback when the widget state changes
  final Function(bool isCompleted)? onStateChanged;

  const InteractivePendulumSimulationWidget({
    super.key,
    required this.data,
    this.onStateChanged,
  });

  /// Factory constructor to create the widget from JSON data
  factory InteractivePendulumSimulationWidget.fromData(Map<String, dynamic> data) {
    return InteractivePendulumSimulationWidget(
      data: data,
    );
  }

  @override
  State<InteractivePendulumSimulationWidget> createState() => _InteractivePendulumSimulationWidgetState();
}

class _InteractivePendulumSimulationWidgetState extends State<InteractivePendulumSimulationWidget> with SingleTickerProviderStateMixin {
  // Pendulum parameters
  double _length = 1.0; // Length of pendulum in meters
  double _gravity = 9.8; // Gravity in m/s²
  double _initialAngle = math.pi / 4; // Initial angle in radians (45 degrees)
  double _damping = 0.1; // Damping coefficient
  
  // Simulation parameters
  double _currentAngle = 0.0; // Current angle of the pendulum
  double _angularVelocity = 0.0; // Angular velocity
  bool _isRunning = false; // Is the simulation running?
  Timer? _simulationTimer;
  
  // Time step for simulation in seconds
  final double _timeStep = 0.016; // ~60 FPS
  
  // UI parameters
  Color _primaryColor = Colors.blue;
  Color _secondaryColor = Colors.orange;
  Color _accentColor = Colors.green;
  Color _textColor = Colors.black87;
  
  // Challenge mode parameters
  bool _challengeMode = false;
  Map<String, dynamic>? _currentChallenge;
  bool _challengeCompleted = false;
  String _targetPeriod = "";
  
  @override
  void initState() {
    super.initState();
    _initializeFromData();
    _resetSimulation();
  }
  
  @override
  void dispose() {
    _stopSimulation();
    super.dispose();
  }
  
  void _initializeFromData() {
    // Initialize parameters from widget data
    _length = widget.data['initialLength']?.toDouble() ?? 1.0;
    _gravity = widget.data['initialGravity']?.toDouble() ?? 9.8;
    _initialAngle = (widget.data['initialAngle']?.toDouble() ?? 45.0) * math.pi / 180.0; // Convert to radians
    _damping = widget.data['initialDamping']?.toDouble() ?? 0.1;
    
    _challengeMode = widget.data['challengeMode'] ?? false;
    if (_challengeMode && widget.data['challenges'] != null) {
      _currentChallenge = widget.data['challenges'][0];
      if (_currentChallenge != null) {
        _targetPeriod = _currentChallenge!['targetPeriod'] ?? "2.0";
      }
    }
    
    // Initialize colors
    if (widget.data['primaryColor'] != null) {
      _primaryColor = _colorFromHex(widget.data['primaryColor']);
    }
    if (widget.data['secondaryColor'] != null) {
      _secondaryColor = _colorFromHex(widget.data['secondaryColor']);
    }
    if (widget.data['accentColor'] != null) {
      _accentColor = _colorFromHex(widget.data['accentColor']);
    }
    if (widget.data['textColor'] != null) {
      _textColor = _colorFromHex(widget.data['textColor']);
    }
  }
  
  // Helper method to convert hex color string to Color
  Color _colorFromHex(String hexColor) {
    hexColor = hexColor.replaceAll('#', '');
    if (hexColor.length == 6) {
      hexColor = 'FF$hexColor';
    }
    return Color(int.parse(hexColor, radix: 16));
  }
  
  void _resetSimulation() {
    setState(() {
      _currentAngle = _initialAngle;
      _angularVelocity = 0.0;
    });
  }
  
  void _startSimulation() {
    if (_isRunning) return;
    
    setState(() {
      _isRunning = true;
    });
    
    _simulationTimer = Timer.periodic(Duration(milliseconds: (_timeStep * 1000).round()), (timer) {
      _updateSimulation();
    });
  }
  
  void _stopSimulation() {
    if (!_isRunning) return;
    
    _simulationTimer?.cancel();
    _simulationTimer = null;
    
    setState(() {
      _isRunning = false;
    });
  }
  
  void _toggleSimulation() {
    if (_isRunning) {
      _stopSimulation();
    } else {
      _startSimulation();
    }
  }
  
  void _updateSimulation() {
    // Simple pendulum physics: d²θ/dt² = -(g/L)sin(θ) - b(dθ/dt)
    // where θ is the angle, g is gravity, L is length, and b is damping
    
    // Calculate angular acceleration
    double angularAcceleration = -(_gravity / _length) * math.sin(_currentAngle) - _damping * _angularVelocity;
    
    // Update angular velocity using Euler method
    double newAngularVelocity = _angularVelocity + angularAcceleration * _timeStep;
    
    // Update angle using Euler method
    double newAngle = _currentAngle + newAngularVelocity * _timeStep;
    
    setState(() {
      _angularVelocity = newAngularVelocity;
      _currentAngle = newAngle;
    });
    
    // Check if challenge is completed
    if (_challengeMode && _currentChallenge != null) {
      _checkChallengeCompleted();
    }
  }
  
  // Calculate the period of the pendulum
  double _calculatePeriod() {
    // T = 2π√(L/g) for small angles
    return 2 * math.pi * math.sqrt(_length / _gravity);
  }
  
  void _checkChallengeCompleted() {
    if (!_challengeMode || _currentChallenge == null) return;
    
    final targetPeriod = double.tryParse(_targetPeriod) ?? 2.0;
    final currentPeriod = _calculatePeriod();
    
    // Allow for small floating-point differences
    const epsilon = 0.1;
    final isCompleted = (currentPeriod - targetPeriod).abs() < epsilon;
    
    if (isCompleted != _challengeCompleted) {
      setState(() {
        _challengeCompleted = isCompleted;
      });
      
      if (_challengeCompleted && widget.onStateChanged != null) {
        widget.onStateChanged!(true);
      }
    }
  }
  
  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withOpacity(0.2),
            spreadRadius: 1,
            blurRadius: 3,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Title
          Text(
            widget.data['title'] ?? 'Pendulum Simulation',
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
              color: _textColor,
            ),
          ),
          
          const SizedBox(height: 8),
          
          // Description
          Text(
            widget.data['description'] ?? 'Explore the properties of a pendulum by adjusting its parameters.',
            style: TextStyle(
              fontSize: 14,
              color: _textColor.withOpacity(0.8),
            ),
          ),
          
          const SizedBox(height: 16),
          
          // Pendulum period display
          Container(
            padding: const EdgeInsets.all(8),
            decoration: BoxDecoration(
              color: _primaryColor.withOpacity(0.1),
              borderRadius: BorderRadius.circular(8),
              border: Border.all(color: _primaryColor.withOpacity(0.3)),
            ),
            child: Text(
              'Period: ${_calculatePeriod().toStringAsFixed(2)} seconds',
              style: TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.bold,
                color: _primaryColor,
              ),
            ),
          ),
          
          const SizedBox(height: 16),
          
          // Pendulum visualization
          Container(
            height: 200,
            decoration: BoxDecoration(
              border: Border.all(color: Colors.grey.withOpacity(0.3)),
              borderRadius: BorderRadius.circular(8),
            ),
            child: ClipRRect(
              borderRadius: BorderRadius.circular(8),
              child: CustomPaint(
                size: const Size(double.infinity, 200),
                painter: PendulumPainter(
                  angle: _currentAngle,
                  length: _length,
                  primaryColor: _primaryColor,
                  secondaryColor: _secondaryColor,
                ),
              ),
            ),
          ),
          
          const SizedBox(height: 16),
          
          // Control buttons
          Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              ElevatedButton(
                onPressed: _toggleSimulation,
                style: ElevatedButton.styleFrom(
                  backgroundColor: _isRunning ? Colors.red : _accentColor,
                  foregroundColor: Colors.white,
                ),
                child: Text(_isRunning ? 'Stop' : 'Start'),
              ),
              const SizedBox(width: 16),
              ElevatedButton(
                onPressed: _resetSimulation,
                style: ElevatedButton.styleFrom(
                  backgroundColor: Colors.grey,
                  foregroundColor: Colors.white,
                ),
                child: const Text('Reset'),
              ),
            ],
          ),
          
          const SizedBox(height: 16),
          
          // Parameter sliders
          _buildParameterSlider(
            label: 'Length (m): ${_length.toStringAsFixed(2)}',
            value: _length,
            min: 0.1,
            max: 2.0,
            onChanged: (value) {
              setState(() {
                _length = value;
                if (_challengeMode) {
                  _checkChallengeCompleted();
                }
              });
            },
          ),
          
          _buildParameterSlider(
            label: 'Gravity (m/s²): ${_gravity.toStringAsFixed(2)}',
            value: _gravity,
            min: 1.0,
            max: 20.0,
            onChanged: (value) {
              setState(() {
                _gravity = value;
                if (_challengeMode) {
                  _checkChallengeCompleted();
                }
              });
            },
          ),
          
          _buildParameterSlider(
            label: 'Initial Angle (°): ${(_initialAngle * 180 / math.pi).toStringAsFixed(0)}',
            value: _initialAngle * 180 / math.pi, // Convert to degrees for display
            min: 0,
            max: 90,
            onChanged: (value) {
              setState(() {
                _initialAngle = value * math.pi / 180; // Convert back to radians
                _resetSimulation();
              });
            },
          ),
          
          _buildParameterSlider(
            label: 'Damping: ${_damping.toStringAsFixed(2)}',
            value: _damping,
            min: 0.0,
            max: 0.5,
            onChanged: (value) {
              setState(() {
                _damping = value;
              });
            },
          ),
          
          // Challenge feedback (if in challenge mode)
          if (_challengeMode && _currentChallenge != null)
            Container(
              margin: const EdgeInsets.only(top: 16),
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: _challengeCompleted
                    ? Colors.green.withOpacity(0.1)
                    : Colors.orange.withOpacity(0.1),
                borderRadius: BorderRadius.circular(8),
                border: Border.all(
                  color: _challengeCompleted
                      ? Colors.green.withOpacity(0.3)
                      : Colors.orange.withOpacity(0.3),
                ),
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    _currentChallenge!['description'] ?? 'Adjust the parameters to achieve the target period.',
                    style: TextStyle(
                      fontSize: 14,
                      color: _textColor,
                    ),
                  ),
                  const SizedBox(height: 8),
                  Text(
                    _challengeCompleted
                        ? (_currentChallenge!['successMessage'] ?? 'Great job! You\'ve achieved the target period.')
                        : (_currentChallenge!['hint'] ?? 'Remember that the period depends on the length and gravity.'),
                    style: TextStyle(
                      fontSize: 14,
                      fontWeight: FontWeight.bold,
                      color: _challengeCompleted ? Colors.green : Colors.orange,
                    ),
                  ),
                ],
              ),
            ),
          
          // Widget name tag at the bottom
          if (widget.data['showNameTag'] ?? true)
            Padding(
              padding: const EdgeInsets.only(top: 8),
              child: Text(
                'InteractivePendulumSimulationWidget',
                style: TextStyle(
                  color: Colors.grey[600],
                  fontSize: 12,
                  fontStyle: FontStyle.italic,
                ),
              ),
            ),
        ],
      ),
    );
  }
  
  Widget _buildParameterSlider({
    required String label,
    required double value,
    required double min,
    required double max,
    required ValueChanged<double> onChanged,
  }) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          label,
          style: TextStyle(
            fontSize: 14,
            color: _textColor,
          ),
        ),
        Slider(
          value: value,
          min: min,
          max: max,
          divisions: 100,
          onChanged: onChanged,
          activeColor: _primaryColor,
          inactiveColor: _primaryColor.withOpacity(0.3),
        ),
      ],
    );
  }
}

/// Custom painter for drawing the pendulum
class PendulumPainter extends CustomPainter {
  final double angle;
  final double length;
  final Color primaryColor;
  final Color secondaryColor;
  
  PendulumPainter({
    required this.angle,
    required this.length,
    required this.primaryColor,
    required this.secondaryColor,
  });
  
  @override
  void paint(Canvas canvas, Size size) {
    final center = Offset(size.width / 2, 20);
    final scale = (size.height - 40) / 2; // Scale to fit the container
    
    // Calculate pendulum bob position
    final bobX = center.dx + math.sin(angle) * scale * length;
    final bobY = center.dy + math.cos(angle) * scale * length;
    final bobPosition = Offset(bobX, bobY);
    
    // Draw pendulum string
    final stringPaint = Paint()
      ..color = primaryColor
      ..strokeWidth = 2.0
      ..style = PaintingStyle.stroke;
    
    canvas.drawLine(center, bobPosition, stringPaint);
    
    // Draw pivot point
    final pivotPaint = Paint()
      ..color = primaryColor
      ..style = PaintingStyle.fill;
    
    canvas.drawCircle(center, 5.0, pivotPaint);
    
    // Draw pendulum bob
    final bobPaint = Paint()
      ..color = secondaryColor
      ..style = PaintingStyle.fill;
    
    canvas.drawCircle(bobPosition, 15.0, bobPaint);
    
    // Draw bob border
    final bobBorderPaint = Paint()
      ..color = secondaryColor.withOpacity(0.7)
      ..strokeWidth = 2.0
      ..style = PaintingStyle.stroke;
    
    canvas.drawCircle(bobPosition, 15.0, bobBorderPaint);
  }
  
  @override
  bool shouldRepaint(PendulumPainter oldDelegate) {
    return oldDelegate.angle != angle ||
           oldDelegate.length != length ||
           oldDelegate.primaryColor != primaryColor ||
           oldDelegate.secondaryColor != secondaryColor;
  }
}
