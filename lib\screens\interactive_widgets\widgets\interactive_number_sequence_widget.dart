import 'package:flutter/material.dart';
import 'dart:async';
import '../../../models/interactive_widget_model.dart';

class InteractiveNumberSequenceWidget extends StatefulWidget {
  final List<dynamic> sequence;
  final bool showAnimation;
  final bool highlightDifferences;
  final bool highlightSpecial;
  final double fontSize;
  final Color primaryColor;
  final Color secondaryColor;
  final Color highlightColor;

  const InteractiveNumberSequenceWidget({
    Key? key,
    required this.sequence,
    this.showAnimation = true,
    this.highlightDifferences = false,
    this.highlightSpecial = false,
    this.fontSize = 24.0,
    this.primaryColor = Colors.blue,
    this.secondaryColor = Colors.orange,
    this.highlightColor = Colors.green,
  }) : super(key: key);

  factory InteractiveNumberSequenceWidget.fromData(Map<String, dynamic> data) {
    return InteractiveNumberSequenceWidget(
      sequence: List<dynamic>.from(data['sequence'] ?? []),
      showAnimation: data['show_animation'] ?? true,
      highlightDifferences: data['highlight_differences'] ?? false,
      highlightSpecial: data['highlight_special'] ?? false,
      fontSize: data['font_size']?.toDouble() ?? 24.0,
      primaryColor: _parseColor(data['primary_color'], Colors.blue),
      secondaryColor: _parseColor(data['secondary_color'], Colors.orange),
      highlightColor: _parseColor(data['highlight_color'], Colors.green),
    );
  }

  static Color _parseColor(String? colorString, Color defaultColor) {
    if (colorString == null) return defaultColor;
    try {
      return Color(int.parse(colorString.replaceAll('#', '0xFF')));
    } catch (e) {
      return defaultColor;
    }
  }

  @override
  State<InteractiveNumberSequenceWidget> createState() =>
      _InteractiveNumberSequenceWidgetState();
}

class _InteractiveNumberSequenceWidgetState
    extends State<InteractiveNumberSequenceWidget>
    with SingleTickerProviderStateMixin {
  late AnimationController _controller;
  late List<Animation<double>> _animations;
  List<double> _differences = [];
  bool _showDifferences = false;
  int _highlightIndex = -1;

  @override
  void initState() {
    super.initState();
    _controller = AnimationController(
      duration: const Duration(milliseconds: 1500),
      vsync: this,
    );

    _initializeAnimations();
    _calculateDifferences();

    if (widget.showAnimation) {
      _startAnimation();
    }
  }

  void _initializeAnimations() {
    _animations = List.generate(
      widget.sequence.length,
      (index) => Tween<double>(begin: 0.0, end: 1.0).animate(
        CurvedAnimation(
          parent: _controller,
          curve: Interval(
            index / widget.sequence.length,
            (index + 1) / widget.sequence.length,
            curve: Curves.easeInOut,
          ),
        ),
      ),
    );
  }

  void _calculateDifferences() {
    _differences = [];
    for (int i = 0; i < widget.sequence.length - 1; i++) {
      if (widget.sequence[i] is int && widget.sequence[i + 1] is int) {
        _differences.add(
          ((widget.sequence[i + 1] as int) - (widget.sequence[i] as int))
              .toDouble(),
        );
      } else {
        _differences.add(0);
      }
    }
  }

  void _startAnimation() {
    _controller.forward().then((_) {
      if (widget.highlightDifferences) {
        Future.delayed(const Duration(milliseconds: 500), () {
          setState(() {
            _showDifferences = true;
          });
        });
      }

      if (widget.highlightSpecial) {
        _startHighlightAnimation();
      }
    });
  }

  void _startHighlightAnimation() {
    int specialIndex = widget.sequence.indexWhere((element) => element == "?");
    if (specialIndex == -1) return;

    Future.delayed(const Duration(milliseconds: 500), () {
      setState(() {
        _highlightIndex = specialIndex;
      });
    });
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      mainAxisSize: MainAxisSize.min,
      children: [
        // Number sequence
        Container(
          height: 80,
          padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
          decoration: BoxDecoration(
            color: Colors.grey[100],
            borderRadius: BorderRadius.circular(12),
            border: Border.all(color: Colors.grey[300]!),
          ),
          child: Center(
            child: AnimatedBuilder(
              animation: _controller,
              builder: (context, child) {
                return Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: List.generate(
                    widget.sequence.length,
                    (index) => _buildNumberBox(index),
                  ),
                );
              },
            ),
          ),
        ),

        // Differences (if enabled)
        if (_showDifferences && widget.highlightDifferences)
          Padding(
            padding: const EdgeInsets.only(top: 16.0),
            child: _buildDifferencesRow(),
          ),
      ],
    );
  }

  Widget _buildNumberBox(int index) {
    final value = widget.sequence[index];
    final isSpecial = value == "?";
    final isHighlighted = index == _highlightIndex;

    // Determine opacity based on animation
    double opacity = widget.showAnimation ? _animations[index].value : 1.0;

    // Determine color based on highlighting
    Color boxColor =
        isHighlighted
            ? widget.highlightColor.withOpacity(0.3)
            : isSpecial
            ? widget.secondaryColor.withOpacity(0.3)
            : widget.primaryColor.withOpacity(0.1);

    Color borderColor =
        isHighlighted
            ? widget.highlightColor
            : isSpecial
            ? widget.secondaryColor
            : widget.primaryColor;

    return Opacity(
      opacity: opacity,
      child: Container(
        margin: const EdgeInsets.symmetric(horizontal: 4),
        width: 50,
        height: 50,
        decoration: BoxDecoration(
          color: boxColor,
          borderRadius: BorderRadius.circular(8),
          border: Border.all(color: borderColor, width: 2),
        ),
        child: Center(
          child: Text(
            value.toString(),
            style: TextStyle(
              fontSize: widget.fontSize,
              fontWeight: FontWeight.bold,
              color: isSpecial ? widget.secondaryColor : Colors.black87,
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildDifferencesRow() {
    return Row(
      mainAxisAlignment: MainAxisAlignment.center,
      children: List.generate(_differences.length, (index) {
        return Container(
          margin: const EdgeInsets.symmetric(horizontal: 4),
          width: 50,
          child: Column(
            children: [
              const Icon(Icons.arrow_downward, size: 16, color: Colors.grey),
              Container(
                padding: const EdgeInsets.all(4),
                decoration: BoxDecoration(
                  color: Colors.grey[200],
                  borderRadius: BorderRadius.circular(4),
                ),
                child: Text(
                  "+${_differences[index].toInt()}",
                  style: TextStyle(
                    fontSize: widget.fontSize * 0.6,
                    color: Colors.black87,
                  ),
                ),
              ),
            ],
          ),
        );
      }),
    );
  }
}
