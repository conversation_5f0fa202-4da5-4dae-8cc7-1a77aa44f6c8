import 'package:flutter/material.dart';
import 'dart:math';
import 'dart:math' as math;

/// A comprehensive module test for one-step equations.
class InteractiveOneStepSolverWidget extends StatefulWidget {
  final Map<String, dynamic> data;
  final Function(bool)? onStateChanged;
  final Color primaryColor;
  final Color secondaryColor;
  final Color backgroundColor;
  final Color textColor;

  const InteractiveOneStepSolverWidget({
    super.key,
    this.data = const {},
    this.onStateChanged,
    this.primaryColor = Colors.blue,
    this.secondaryColor = Colors.orange,
    this.backgroundColor = Colors.white,
    this.textColor = Colors.black87,
  });

  @override
  State<InteractiveOneStepSolverWidget> createState() =>
      _InteractiveOneStepSolverWidgetState();
}

class _InteractiveOneStepSolverWidgetState
    extends State<InteractiveOneStepSolverWidget> {
  // State variables
  bool _isCompleted = false;
  int _currentQuestionIndex = 0;
  int _score = 0;
  int _totalQuestions = 10;
  bool _showResults = false;
  String? _selectedAnswer;
  String? _feedbackMessage;
  List<Map<String, dynamic>> _questions = [];

  // Timer variables
  int _timeRemaining = 600; // 10 minutes in seconds
  bool _timerActive = false;
  late DateTime _timerStartTime;

  @override
  void initState() {
    super.initState();
    _generateQuestions();
  }

  void _generateQuestions() {
    // Clear existing questions
    _questions = [];

    // Generate a mix of different question types
    Random random = Random();

    // Ensure we have at least one of each type
    _addEquationSolvingQuestion('addition');
    _addEquationSolvingQuestion('subtraction');
    _addEquationSolvingQuestion('multiplication');
    _addEquationSolvingQuestion('division');
    _addWordProblemQuestion();
    _addOperationIdentificationQuestion();

    // Add more random questions to reach total
    while (_questions.length < _totalQuestions) {
      int questionType = random.nextInt(6);

      switch (questionType) {
        case 0:
          _addEquationSolvingQuestion('addition');
          break;
        case 1:
          _addEquationSolvingQuestion('subtraction');
          break;
        case 2:
          _addEquationSolvingQuestion('multiplication');
          break;
        case 3:
          _addEquationSolvingQuestion('division');
          break;
        case 4:
          _addWordProblemQuestion();
          break;
        case 5:
          _addOperationIdentificationQuestion();
          break;
      }
    }

    // Shuffle the questions
    _questions.shuffle();
  }

  void _addEquationSolvingQuestion(String operationType) {
    Random random = Random();
    List<String> variables = ['x', 'y', 'z', 'a', 'b', 'n', 'm'];
    String variable = variables[random.nextInt(variables.length)];

    String equation;
    String solution;
    String correctOperation;

    switch (operationType) {
      case 'addition':
        int value = random.nextInt(10) + 1;
        int result = random.nextInt(20) + 10;
        equation = '$variable - $value = ${result - value}';
        solution = result.toString();
        correctOperation = 'Add $value to both sides';
        break;
      case 'subtraction':
        int value = random.nextInt(10) + 1;
        int result = random.nextInt(20) + 1;
        equation = '$variable + $value = ${result + value}';
        solution = result.toString();
        correctOperation = 'Subtract $value from both sides';
        break;
      case 'multiplication':
        int value = random.nextInt(5) + 2;
        int result = random.nextInt(10) + 1;
        equation = '$variable/$value = $result';
        solution = (result * value).toString();
        correctOperation = 'Multiply both sides by $value';
        break;
      case 'division':
        int value = random.nextInt(5) + 2;
        int result = random.nextInt(10) + 1;
        equation = '${value}$variable = ${value * result}';
        solution = result.toString();
        correctOperation = 'Divide both sides by $value';
        break;
      default:
        int value = random.nextInt(10) + 1;
        int result = random.nextInt(20) + 10;
        equation = '$variable - $value = ${result - value}';
        solution = result.toString();
        correctOperation = 'Add $value to both sides';
    }

    // Generate wrong answers
    List<String> wrongAnswers = [];
    while (wrongAnswers.length < 3) {
      int offset = random.nextInt(5) + 1;
      if (random.nextBool()) offset = -offset;

      int wrongResult = int.parse(solution) + offset;
      String wrongAnswer = wrongResult.toString();

      if (!wrongAnswers.contains(wrongAnswer) && wrongAnswer != solution) {
        wrongAnswers.add(wrongAnswer);
      }
    }

    List<String> options = [solution, ...wrongAnswers];
    options.shuffle();

    Map<String, dynamic> question = {
      'type': 'equation_solving',
      'text': 'Solve for $variable: $equation',
      'correctAnswer': solution,
      'options': options,
      'explanation': 'To solve $equation, you need to $correctOperation to isolate the variable. This gives you $variable = $solution.',
      'operationType': operationType
    };

    _questions.add(question);
  }

  void _addWordProblemQuestion() {
    Random random = Random();
    List<Map<String, dynamic>> wordProblems = [
      {
        'problem': 'A number plus 7 equals 15. What is the number?',
        'equation': 'x + 7 = 15',
        'solution': '8',
        'operation': 'subtraction',
        'explanation': 'Let x be the unknown number. Then x + 7 = 15. Subtract 7 from both sides to get x = 8.'
      },
      {
        'problem': 'When 5 is subtracted from a number, the result is 12. What is the number?',
        'equation': 'x - 5 = 12',
        'solution': '17',
        'operation': 'addition',
        'explanation': 'Let x be the unknown number. Then x - 5 = 12. Add 5 to both sides to get x = 17.'
      },
      {
        'problem': 'Three times a number is 27. What is the number?',
        'equation': '3x = 27',
        'solution': '9',
        'operation': 'division',
        'explanation': 'Let x be the unknown number. Then 3x = 27. Divide both sides by 3 to get x = 9.'
      },
      {
        'problem': 'Half of a number is 14. What is the number?',
        'equation': 'x/2 = 14',
        'solution': '28',
        'operation': 'multiplication',
        'explanation': 'Let x be the unknown number. Then x/2 = 14. Multiply both sides by 2 to get x = 28.'
      },
      {
        'problem': 'A number decreased by 9 equals 11. What is the number?',
        'equation': 'x - 9 = 11',
        'solution': '20',
        'operation': 'addition',
        'explanation': 'Let x be the unknown number. Then x - 9 = 11. Add 9 to both sides to get x = 20.'
      },
      {
        'problem': 'Six more than a number is 23. What is the number?',
        'equation': 'x + 6 = 23',
        'solution': '17',
        'operation': 'subtraction',
        'explanation': 'Let x be the unknown number. Then x + 6 = 23. Subtract 6 from both sides to get x = 17.'
      },
    ];

    Map<String, dynamic> selectedProblem = wordProblems[random.nextInt(wordProblems.length)];

    // Generate wrong answers
    List<String> wrongAnswers = [];
    int solution = int.parse(selectedProblem['solution']);

    while (wrongAnswers.length < 3) {
      int offset = random.nextInt(5) + 1;
      if (random.nextBool()) offset = -offset;

      int wrongResult = solution + offset;
      String wrongAnswer = wrongResult.toString();

      if (!wrongAnswers.contains(wrongAnswer) && wrongAnswer != selectedProblem['solution']) {
        wrongAnswers.add(wrongAnswer);
      }
    }

    List<String> options = [selectedProblem['solution'], ...wrongAnswers];
    options.shuffle();

    Map<String, dynamic> question = {
      'type': 'word_problem',
      'text': selectedProblem['problem'],
      'correctAnswer': selectedProblem['solution'],
      'options': options,
      'explanation': selectedProblem['explanation'],
      'equation': selectedProblem['equation'],
      'operationType': selectedProblem['operation']
    };

    _questions.add(question);
  }

  void _addOperationIdentificationQuestion() {
    Random random = Random();
    List<String> variables = ['x', 'y', 'z', 'a', 'b', 'n', 'm'];
    String variable = variables[random.nextInt(variables.length)];

    List<Map<String, dynamic>> equationTypes = [
      {
        'equation': '$variable + 5 = 12',
        'operation': 'subtraction',
        'explanation': 'To isolate the variable in $variable + 5 = 12, you need to subtract 5 from both sides.'
      },
      {
        'equation': '$variable - 3 = 8',
        'operation': 'addition',
        'explanation': 'To isolate the variable in $variable - 3 = 8, you need to add 3 to both sides.'
      },
      {
        'equation': '4$variable = 20',
        'operation': 'division',
        'explanation': 'To isolate the variable in 4$variable = 20, you need to divide both sides by 4.'
      },
      {
        'equation': '$variable/5 = 3',
        'operation': 'multiplication',
        'explanation': 'To isolate the variable in $variable/5 = 3, you need to multiply both sides by 5.'
      },
      {
        'equation': '7 = $variable + 2',
        'operation': 'subtraction',
        'explanation': 'To isolate the variable in 7 = $variable + 2, you need to subtract 2 from both sides.'
      },
      {
        'equation': '9 = $variable - 4',
        'operation': 'addition',
        'explanation': 'To isolate the variable in 9 = $variable - 4, you need to add 4 to both sides.'
      },
    ];

    Map<String, dynamic> selectedEquation = equationTypes[random.nextInt(equationTypes.length)];

    List<String> operations = ['addition', 'subtraction', 'multiplication', 'division'];
    String correctOperation = selectedEquation['operation'];

    // Generate wrong answers
    List<String> wrongOperations = operations.where((op) => op != correctOperation).toList();
    wrongOperations.shuffle();
    wrongOperations = wrongOperations.take(3).toList();

    List<String> options = [correctOperation, ...wrongOperations];
    options.shuffle();

    // Make the options more readable
    List<String> readableOptions = options.map((op) {
      switch (op) {
        case 'addition':
          return 'Add to both sides';
        case 'subtraction':
          return 'Subtract from both sides';
        case 'multiplication':
          return 'Multiply both sides';
        case 'division':
          return 'Divide both sides';
        default:
          return op;
      }
    }).toList();

    Map<String, dynamic> question = {
      'type': 'operation_identification',
      'text': 'What operation should you perform first to solve this equation?\n${selectedEquation['equation']}',
      'correctAnswer': readableOptions[options.indexOf(correctOperation)],
      'options': readableOptions,
      'explanation': selectedEquation['explanation'],
      'equation': selectedEquation['equation'],
      'operationType': correctOperation
    };

    _questions.add(question);
  }

  void _startChallenge() {
    setState(() {
      _currentQuestionIndex = 0;
      _score = 0;
      _showResults = false;
      _selectedAnswer = null;
      _feedbackMessage = null;
      _isCompleted = false;
      _timerActive = true;
      _timeRemaining = 600; // Reset to 10 minutes
      _timerStartTime = DateTime.now();
    });

    // Start timer
    _startTimer();

    // Notify parent
    widget.onStateChanged?.call(false);
  }

  void _startTimer() {
    Future.delayed(const Duration(seconds: 1), () {
      if (!mounted) return;

      if (_timerActive) {
        setState(() {
          _timeRemaining = math.max(0, _timeRemaining - 1);

          // Check if time is up
          if (_timeRemaining <= 0) {
            _endChallenge();
          }
        });

        // Continue timer
        if (_timeRemaining > 0 && _timerActive) {
          _startTimer();
        }
      }
    });
  }

  void _checkAnswer(String answer) {
    final currentQuestion = _questions[_currentQuestionIndex];
    final bool isCorrect = answer == currentQuestion['correctAnswer'];

    setState(() {
      _selectedAnswer = answer;

      if (isCorrect) {
        _score++;
        _feedbackMessage = 'Correct! ${currentQuestion['explanation']}';
      } else {
        _feedbackMessage = 'Incorrect. ${currentQuestion['explanation']}';
      }
    });

    // Move to next question after a delay
    Future.delayed(const Duration(seconds: 2), () {
      if (!mounted) return;

      setState(() {
        if (_currentQuestionIndex < _questions.length - 1) {
          _currentQuestionIndex++;
          _selectedAnswer = null;
          _feedbackMessage = null;
        } else {
          _endChallenge();
        }
      });
    });
  }

  void _endChallenge() {
    setState(() {
      _timerActive = false;
      _showResults = true;

      // Calculate completion status (pass if score >= 70%)
      _isCompleted = (_score / _questions.length) >= 0.7;
    });

    // Notify parent of completion status
    widget.onStateChanged?.call(_isCompleted);
  }

  String _formatTime(int seconds) {
    int minutes = seconds ~/ 60;
    int remainingSeconds = seconds % 60;
    return '$minutes:${remainingSeconds.toString().padLeft(2, '0')}';
  }

  void _resetWidget() {
    setState(() {
      _generateQuestions();
      _currentQuestionIndex = 0;
      _score = 0;
      _showResults = false;
      _selectedAnswer = null;
      _feedbackMessage = null;
      _isCompleted = false;
      _timerActive = false;
    });
    widget.onStateChanged?.call(false);
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: widget.backgroundColor,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withAlpha(50),
            spreadRadius: 1,
            blurRadius: 3,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: _showResults ? _buildResultsScreen() :
             _timerActive ? _buildCurrentQuestion() : _buildStartScreen(),
    );
  }

  Widget _buildStartScreen() {
    return Column(
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        Text(
          'One-Step Equation Master',
          style: TextStyle(
            fontSize: 24,
            fontWeight: FontWeight.bold,
            color: widget.primaryColor,
          ),
        ),
        const SizedBox(height: 24),
        Container(
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            color: widget.primaryColor.withOpacity(0.1),
            borderRadius: BorderRadius.circular(12),
          ),
          child: Column(
            children: [
              Text(
                'Test your skills with one-step equations!',
                style: TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.w500,
                  color: widget.textColor,
                ),
                textAlign: TextAlign.center,
              ),
              const SizedBox(height: 16),
              Text(
                'This challenge includes:',
                style: TextStyle(
                  fontSize: 16,
                  color: widget.textColor,
                ),
              ),
              const SizedBox(height: 8),
              _buildFeatureItem('Equation solving questions'),
              _buildFeatureItem('Word problems'),
              _buildFeatureItem('Operation identification'),
              const SizedBox(height: 16),
              Text(
                'You\'ll have 10 minutes to answer $_totalQuestions questions.',
                style: TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.w500,
                  color: widget.textColor,
                ),
                textAlign: TextAlign.center,
              ),
            ],
          ),
        ),
        const SizedBox(height: 32),
        ElevatedButton.icon(
          onPressed: _startChallenge,
          icon: const Icon(Icons.play_arrow),
          label: const Text('Start Challenge'),
          style: ElevatedButton.styleFrom(
            backgroundColor: widget.primaryColor,
            padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
          ),
        ),
      ],
    );
  }

  Widget _buildFeatureItem(String text) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4),
      child: Row(
        children: [
          Icon(
            Icons.check_circle,
            color: widget.primaryColor,
            size: 20,
          ),
          const SizedBox(width: 8),
          Expanded(
            child: Text(
              text,
              style: TextStyle(
                fontSize: 14,
                color: widget.textColor,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildCurrentQuestion() {
    if (_currentQuestionIndex >= _questions.length) {
      return Container();
    }

    final question = _questions[_currentQuestionIndex];

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Question number and timer
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Text(
              'Question ${_currentQuestionIndex + 1} of ${_questions.length}',
              style: TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.bold,
                color: widget.textColor,
              ),
            ),
            Container(
              padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
              decoration: BoxDecoration(
                color: _timeRemaining < 60 ? Colors.red : widget.primaryColor,
                borderRadius: BorderRadius.circular(16),
              ),
              child: Row(
                children: [
                  Icon(
                    Icons.timer,
                    color: Colors.white,
                    size: 18,
                  ),
                  const SizedBox(width: 4),
                  Text(
                    _formatTime(_timeRemaining),
                    style: TextStyle(
                      color: Colors.white,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),

        const SizedBox(height: 16),

        // Question text
        Container(
          width: double.infinity,
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            color: widget.primaryColor.withOpacity(0.1),
            borderRadius: BorderRadius.circular(8),
            border: Border.all(color: widget.primaryColor.withOpacity(0.3)),
          ),
          child: Text(
            question['text'],
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
              color: widget.textColor,
            ),
          ),
        ),

        const SizedBox(height: 24),

        // Answer options
        ...question['options'].map<Widget>((option) {
          bool isSelected = _selectedAnswer == option;
          bool isCorrect = option == question['correctAnswer'];

          Color buttonColor = isSelected
              ? (isCorrect ? Colors.green : Colors.red)
              : widget.primaryColor;

          return Padding(
            padding: const EdgeInsets.only(bottom: 12.0),
            child: SizedBox(
              width: double.infinity,
              child: ElevatedButton(
                onPressed: _selectedAnswer == null ? () => _checkAnswer(option) : null,
                style: ElevatedButton.styleFrom(
                  backgroundColor: buttonColor,
                  padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
                  alignment: Alignment.centerLeft,
                ),
                child: Text(
                  option,
                  style: TextStyle(
                    fontSize: 16,
                    color: Colors.white,
                  ),
                ),
              ),
            ),
          );
        }).toList(),

        if (_feedbackMessage != null)
          Padding(
            padding: const EdgeInsets.only(top: 16.0),
            child: Text(
              _feedbackMessage!,
              style: TextStyle(
                color: _selectedAnswer == question['correctAnswer']
                    ? Colors.green
                    : Colors.red,
                fontWeight: FontWeight.bold,
                fontSize: 16,
              ),
            ),
          ),
      ],
    );
  }

  Widget _buildResultsScreen() {
    // Calculate percentage score
    final percentage = (_score / _questions.length * 100).round();
    final isPassing = percentage >= 70;

    return Column(
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        // Trophy icon for passing, or try again icon for failing
        Icon(
          isPassing ? Icons.emoji_events : Icons.refresh,
          size: 80,
          color: isPassing ? Colors.amber : Colors.grey,
        ),

        const SizedBox(height: 24),

        // Result title
        Text(
          isPassing ? 'Congratulations!' : 'Keep Practicing!',
          style: TextStyle(
            fontSize: 24,
            fontWeight: FontWeight.bold,
            color: isPassing ? widget.primaryColor : Colors.red,
          ),
        ),

        const SizedBox(height: 16),

        // Score
        Text(
          'Your Score: $_score out of ${_questions.length} ($percentage%)',
          style: TextStyle(
            fontSize: 18,
            fontWeight: FontWeight.bold,
            color: widget.textColor,
          ),
        ),

        const SizedBox(height: 8),

        // Time taken
        Text(
          'Time Taken: ${_formatTime(600 - _timeRemaining)}',
          style: TextStyle(
            fontSize: 16,
            color: widget.textColor,
          ),
        ),

        const SizedBox(height: 24),

        // Feedback message
        Text(
          isPassing
              ? 'Great job! You\'ve demonstrated a solid understanding of one-step equations.'
              : 'You need a score of at least 70% to pass. Review the concepts and try again!',
          textAlign: TextAlign.center,
          style: TextStyle(
            fontSize: 16,
            color: widget.textColor,
          ),
        ),

        const SizedBox(height: 32),

        // Try again button
        ElevatedButton.icon(
          onPressed: _resetWidget,
          icon: Icon(Icons.refresh),
          label: Text('Try Again'),
          style: ElevatedButton.styleFrom(
            backgroundColor: widget.secondaryColor,
            padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
          ),
        ),
      ],
    );
  }
}
