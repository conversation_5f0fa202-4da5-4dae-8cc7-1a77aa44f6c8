{"id": "computational-thinking-real-world", "title": "Computational Thinking in the Real World", "description": "Explore practical applications of computational thinking across various domains.", "order": 5, "lessons": [{"id": "ctrw-everyday-life", "title": "CT in Everyday Life", "description": "See how computational thinking principles apply to everyday tasks and decisions.", "order": 1, "type": "interactive_lesson", "estimatedTimeMinutes": 6, "contentBlocks": [{"id": "ctrw-edl-screen1", "type": "lesson_screen", "order": 1, "estimatedTimeSeconds": 40, "content": {"headline": "Beyond Computers!", "body_md": "Computational thinking isn't just for programmers! Its principles – deconstruction, pattern recognition, abstraction, and algorithms – are useful in many real-world scenarios.", "visual": {"type": "giphy_search", "value": "lightbulb idea daily life"}, "interactive_element": {"type": "button", "button_text": "Like what?"}}}, {"id": "ctrw-edl-screen2", "type": "lesson_screen", "order": 2, "estimatedTimeSeconds": 75, "content": {"headline": "Examples Around You", "body_md": "*   **Planning a Trip:** Deconstruct into flights, accommodation, activities. Recognize patterns in good travel deals. Abstract away complex logistics with a simple itinerary. Follow an algorithmic packing list.\n*   **Cooking a Meal:** Deconstruct the recipe. Recognize patterns in cooking times for similar ingredients. Abstract cooking techniques. Follow the recipe algorithm.\n*   **Organizing Your Room:** Deconstruct into areas (desk, closet). Recognize patterns of items that belong together. Abstract categories (books, clothes). Create an algorithm for cleaning.", "visual": {"type": "unsplash_search", "value": "everyday life collage"}, "interactive_element": {"type": "button", "button_text": "I see the connection!"}}}, {"id": "ctrw-edl-screen3", "type": "question_screen", "order": 3, "estimatedTimeSeconds": 60, "content": {"headline": "Real-World CT", "body_md": "When you follow a IKEA furniture assembly guide, which computational thinking concept are you most directly applying?", "visual": {"type": "static_text", "value": "🛠️📖"}, "interactive_element": {"type": "multiple_choice_text", "options": [{"text": "Abstraction", "is_correct": false, "feedback": "While the parts are abstracted, following the steps is more direct."}, {"text": "Pattern Recognition", "is_correct": false, "feedback": "You might recognize patterns in how parts fit, but the core activity is following instructions."}, {"text": "Algorithm Following", "is_correct": true, "feedback": "Correct! The assembly guide is essentially an algorithm."}, {"text": "Deconstruction (of the problem)", "is_correct": false, "feedback": "IKEA has already deconstructed the problem for you into steps."}], "correct_answer_feedback": "Exactly! Assembly instructions are a classic example of an algorithm.", "incorrect_answer_feedback": "Consider what the instruction manual represents in terms of CT."}}}]}, {"id": "ctrw-professions", "title": "CT in Different Professions", "description": "Explore how computational thinking is applied in various professional fields.", "order": 2, "type": "interactive_lesson", "estimatedTimeMinutes": 7, "contentBlocks": [{"id": "ctrw-prof-screen1", "type": "lesson_screen", "order": 1, "estimatedTimeSeconds": 45, "content": {"headline": "CT at Work", "body_md": "Computational thinking is a valuable skill in almost any career!\n\nLet's see how it's used in a few different fields.", "visual": {"type": "giphy_search", "value": "people working different jobs"}, "interactive_element": {"type": "button", "button_text": "Show me examples"}}}, {"id": "ctrw-prof-screen2", "type": "lesson_screen", "order": 2, "estimatedTimeSeconds": 80, "content": {"headline": "Examples in Professions", "body_md": "*   **Doctors:** Deconstruct symptoms, recognize patterns of illness, abstract patient data, follow diagnostic algorithms.\n*   **Detectives:** Deconstruct a crime scene, recognize patterns in evidence, abstract suspect profiles, build an algorithmic case.\n*   **Urban Planners:** Deconstruct city needs (housing, transport), recognize traffic patterns, abstract city zones, design algorithms for optimal routing or resource allocation.", "visual": {"type": "unsplash_search", "value": "professionals at work"}, "interactive_element": {"type": "button", "button_text": "Makes sense!"}}}, {"id": "ctrw-prof-screen3", "type": "question_screen", "order": 3, "estimatedTimeSeconds": 65, "content": {"headline": "Professional CT", "body_md": "A chef is trying to create a new signature dish. They experiment with different ingredients (deconstruction/pattern recognition of flavors), simplify the cooking process (abstraction), and then write down the final, repeatable steps (algorithm).\n\nThis entire process demonstrates:", "visual": {"type": "static_text", "value": "🧑‍🍳📝"}, "interactive_element": {"type": "multiple_choice_text", "options": [{"text": "Only Algorithm Design", "is_correct": false, "feedback": "While an algorithm is created, other CT skills are also used."}, {"text": "Only Abstraction", "is_correct": false, "feedback": "Abstraction is part of it, but not the whole process."}, {"text": "Computational Thinking as a whole", "is_correct": true, "feedback": "Correct! The chef is using multiple CT skills."}, {"text": "None of the above", "is_correct": false, "feedback": "The scenario clearly shows CT principles in action."}], "correct_answer_feedback": "Exactly! It's a great example of holistic computational thinking.", "incorrect_answer_feedback": "Consider if the chef is using more than one CT skill."}}}]}, {"id": "ctrw-module-test", "title": "Module Test: CT in the Real World", "description": "Test your understanding of how computational thinking applies in various contexts.", "order": 3, "type": "module_test_interactive", "estimatedTimeMinutes": 8, "contentBlocks": [{"id": "ctrw-test-q1", "type": "question_screen", "order": 1, "estimatedTimeSeconds": 70, "content": {"headline": "Question 1", "body_md": "When planning a large software project, a project manager breaks the project into phases (e.g., requirements, design, implementation, testing), then breaks those phases into smaller tasks. This is primarily an example of which CT skill?", "visual": {"type": "static_text", "value": "📊🗓️"}, "interactive_element": {"type": "multiple_choice_text", "options": [{"text": "Abstraction", "is_correct": false, "feedback": "While tasks are abstractions, the act of breaking down is more specific."}, {"text": "Deconstruction", "is_correct": true, "feedback": "Correct! Breaking a large project into smaller, manageable parts is deconstruction."}, {"text": "Pattern Recognition", "is_correct": false, "feedback": "Patterns might be found within tasks, but the breakdown itself is deconstruction."}, {"text": "Algorithm Design", "is_correct": false, "feedback": "The project plan itself might be an algorithm, but the breakdown is deconstruction."}], "correct_answer_feedback": "Spot on!", "incorrect_answer_feedback": "Consider which CT skill involves breaking large problems into smaller pieces."}}}, {"id": "ctrw-test-q2", "type": "question_screen", "order": 2, "estimatedTimeSeconds": 75, "content": {"headline": "Question 2", "body_md": "A data scientist analyzing customer purchase histories to identify groups of products frequently bought together is primarily using which CT skill?", "visual": {"type": "giphy_search", "value": "data analysis charts"}, "interactive_element": {"type": "multiple_choice_text", "options": [{"text": "Deconstruction", "is_correct": false, "feedback": "They are looking for relationships in existing data, not breaking down a single large problem."}, {"text": "Pattern Recognition", "is_correct": true, "feedback": "Correct! They are looking for recurring patterns (items bought together) in the data."}, {"text": "Abstraction", "is_correct": false, "feedback": "While customer segments might be an abstraction, the core activity is finding patterns."}, {"text": "Algorithm Evaluation", "is_correct": false, "feedback": "They might use algorithms, but the skill highlighted is identifying the patterns themselves."}], "correct_answer_feedback": "Excellent! That's a classic use of pattern recognition.", "incorrect_answer_feedback": "Think about what the data scientist is trying to find in the purchase histories."}}}, {"id": "ctrw-test-q3", "type": "question_screen", "order": 3, "estimatedTimeSeconds": 70, "content": {"headline": "Question 3", "body_md": "Using a GPS navigation system to get to a destination, where you only interact with a map interface and turn-by-turn directions without needing to know about satellite communication protocols or complex routing algorithms, is a good example of benefiting from:", "visual": {"type": "static_text", "value": "🛰️🗺️🚗"}, "interactive_element": {"type": "multiple_choice_text", "options": [{"text": "Algorithmic Inefficiency", "is_correct": false, "feedback": "Hopefully the GPS is efficient! The key here is what's hidden from you."}, {"text": "Detailed Deconstruction", "is_correct": false, "feedback": "You are not deconstructing the GPS system; you are using its simplified interface."}, {"text": "Abstraction", "is_correct": true, "feedback": "Correct! The complex details of how GPS works are abstracted away, providing you with a simple interface."}, {"text": "Pattern Overload", "is_correct": false, "feedback": "The GPS helps you navigate patterns (roads), but the benefit described is about hidden complexity."}], "correct_answer_feedback": "Precisely! Abstraction makes complex systems usable.", "incorrect_answer_feedback": "Consider what information is hidden from the user to make the system easy to use."}}}]}]}