import 'dart:async';
import 'package:flutter/material.dart';
import '../../../models/interactive_widget_model.dart';

class MiniGameWidget extends StatefulWidget {
  final InteractiveWidgetModel widget;

  const MiniGameWidget({super.key, required this.widget});

  @override
  State<MiniGameWidget> createState() => _MiniGameWidgetState();
}

class _MiniGameWidgetState extends State<MiniGameWidget> {
  bool _isPlaying = false;
  String _status = 'Ready to play';
  int _currentSequenceIndex = 0;
  String _userAnswer = '';
  bool _isCorrect = false;
  bool _hasSubmitted = false;
  int _score = 0;
  int _timeRemaining = 0;
  Timer? _gameTimer;
  final TextEditingController _answerController = TextEditingController();
  final FocusNode _answerFocusNode = FocusNode();

  @override
  void initState() {
    super.initState();
    _resetGame();
  }

  @override
  void dispose() {
    _gameTimer?.cancel();
    _answerController.dispose();
    _answerFocusNode.dispose();
    super.dispose();
  }

  void _resetGame() {
    final timeLimit = widget.widget.data['timeLimit'] as int? ?? 60;
    setState(() {
      _isPlaying = false;
      _status = 'Ready to play';
      _currentSequenceIndex = 0;
      _userAnswer = '';
      _isCorrect = false;
      _hasSubmitted = false;
      _score = 0;
      _timeRemaining = timeLimit;
      _answerController.clear();
    });
    _gameTimer?.cancel();
  }

  void _startGame() {
    setState(() {
      _isPlaying = true;
      _status = 'Game in progress...';
      _currentSequenceIndex = 0;
      _userAnswer = '';
      _isCorrect = false;
      _hasSubmitted = false;
      _score = 0;
      _answerController.clear();
    });

    // Start the timer
    _gameTimer = Timer.periodic(const Duration(seconds: 1), (timer) {
      setState(() {
        if (_timeRemaining > 0) {
          _timeRemaining--;
        } else {
          _endGame();
        }
      });
    });

    // Focus on the answer field
    _answerFocusNode.requestFocus();
  }

  void _endGame() {
    _gameTimer?.cancel();
    setState(() {
      _isPlaying = false;
      _status = 'Game over! Score: $_score';
    });
  }

  void _checkAnswer() {
    final sequences = List<Map<String, dynamic>>.from(
      widget.widget.data['sequences'] ?? [],
    );

    if (_currentSequenceIndex < sequences.length) {
      final currentSequence = sequences[_currentSequenceIndex];
      final correctAnswer = currentSequence['next'] as String? ?? '';

      setState(() {
        _hasSubmitted = true;
        _isCorrect = _userAnswer.trim() == correctAnswer.trim();

        if (_isCorrect) {
          _score += 10;
          _status = 'Correct! +10 points';
        } else {
          _status = 'Incorrect. The answer was $correctAnswer';
        }
      });
    }
  }

  void _nextSequence() {
    final sequences = List<Map<String, dynamic>>.from(
      widget.widget.data['sequences'] ?? [],
    );

    setState(() {
      _currentSequenceIndex++;
      _userAnswer = '';
      _hasSubmitted = false;
      _answerController.clear();

      if (_currentSequenceIndex >= sequences.length) {
        // End of game
        _endGame();
      } else {
        _status = 'Find the next number in the sequence';
        // Focus on the answer field for the next question
        _answerFocusNode.requestFocus();
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    final difficulty = widget.widget.data['difficulty'] as String? ?? 'medium';
    final timeLimit = widget.widget.data['timeLimit'] as int? ?? 60;
    final sequences = List<Map<String, dynamic>>.from(
      widget.widget.data['sequences'] ?? [],
    );

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Game header with score and timer
        Container(
          width: double.infinity,
          padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
          decoration: BoxDecoration(
            color: Colors.blue[50],
            borderRadius: BorderRadius.circular(8),
            border: Border.all(color: Colors.blue[200]!),
          ),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              // Score
              Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  const Text(
                    'Score',
                    style: TextStyle(fontSize: 12, fontWeight: FontWeight.bold),
                  ),
                  Text(
                    '$_score',
                    style: const TextStyle(
                      fontSize: 18,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ],
              ),

              // Difficulty
              Chip(
                label: Text(
                  difficulty.toUpperCase(),
                  style: TextStyle(
                    fontSize: 12,
                    color: _getColorForDifficulty(difficulty),
                    fontWeight: FontWeight.bold,
                  ),
                ),
                backgroundColor: _getColorForDifficulty(
                  difficulty,
                ).withOpacity(0.1),
              ),

              // Timer
              Column(
                crossAxisAlignment: CrossAxisAlignment.end,
                children: [
                  const Text(
                    'Time',
                    style: TextStyle(fontSize: 12, fontWeight: FontWeight.bold),
                  ),
                  Text(
                    '$_timeRemaining s',
                    style: TextStyle(
                      fontSize: 18,
                      fontWeight: FontWeight.bold,
                      color: _timeRemaining < 10 ? Colors.red : Colors.black,
                    ),
                  ),
                ],
              ),
            ],
          ),
        ),

        const SizedBox(height: 16),

        // Game content
        if (_isPlaying &&
            sequences.isNotEmpty &&
            _currentSequenceIndex < sequences.length)
          _buildGameContent(sequences[_currentSequenceIndex])
        else
          _buildGamePreview(difficulty, timeLimit),

        const SizedBox(height: 16),

        // Game details when not playing
        if (!_isPlaying)
          Padding(
            padding: const EdgeInsets.only(top: 8.0),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                const Text(
                  'Game Details:',
                  style: TextStyle(fontWeight: FontWeight.bold, fontSize: 16),
                ),
                const SizedBox(height: 8),
                _buildGameDetails(),
              ],
            ),
          ),
      ],
    );
  }

  Widget _buildGamePreview(String difficulty, int timeLimit) {
    return Container(
      height: 200,
      width: double.infinity,
      decoration: BoxDecoration(
        color: Colors.grey[200],
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: Colors.grey[300]!),
      ),
      child: Stack(
        children: [
          // Game preview
          Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Icon(
                  Icons.lightbulb,
                  size: 48,
                  color: _getColorForDifficulty(difficulty),
                ),
                const SizedBox(height: 16),
                Text(
                  widget.widget.name,
                  style: const TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const SizedBox(height: 8),
                Text(
                  'Difficulty: $difficulty | Time: $timeLimit seconds',
                  style: TextStyle(fontSize: 14, color: Colors.grey[700]),
                ),
                const SizedBox(height: 8),
                Text(
                  _status,
                  style: TextStyle(
                    fontSize: 14,
                    fontWeight: FontWeight.bold,
                    color: _isPlaying ? Colors.green : Colors.grey[700],
                  ),
                ),
              ],
            ),
          ),

          // Play button overlay
          Positioned(
            bottom: 16,
            left: 0,
            right: 0,
            child: Center(
              child: ElevatedButton.icon(
                onPressed: () {
                  if (_isPlaying) {
                    _endGame();
                  } else {
                    _startGame();
                  }
                },
                icon: Icon(_isPlaying ? Icons.pause : Icons.play_arrow),
                label: Text(_isPlaying ? 'End Game' : 'Start Game'),
                style: ElevatedButton.styleFrom(
                  backgroundColor: _isPlaying ? Colors.orange : Colors.green,
                  foregroundColor: Colors.white,
                  padding: const EdgeInsets.symmetric(
                    horizontal: 24,
                    vertical: 12,
                  ),
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildGameContent(Map<String, dynamic> sequence) {
    final numbers = List<dynamic>.from(sequence['sequence'] ?? []);
    final pattern = sequence['pattern'] as String? ?? 'Find the pattern';

    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: Colors.grey[300]!),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withOpacity(0.2),
            spreadRadius: 1,
            blurRadius: 3,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Sequence display
          Text(
            'Find the pattern and determine the next number:',
            style: const TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
          ),
          const SizedBox(height: 16),

          // Numbers display
          Container(
            width: double.infinity,
            padding: const EdgeInsets.symmetric(vertical: 16, horizontal: 8),
            decoration: BoxDecoration(
              color: Colors.blue[50],
              borderRadius: BorderRadius.circular(8),
            ),
            child: Center(
              child: Text(
                numbers.join(', ') + ', ?',
                style: const TextStyle(
                  fontSize: 24,
                  fontWeight: FontWeight.bold,
                  letterSpacing: 2,
                ),
              ),
            ),
          ),

          const SizedBox(height: 16),

          // Hint
          if (_hasSubmitted && !_isCorrect)
            Container(
              width: double.infinity,
              padding: const EdgeInsets.all(8),
              decoration: BoxDecoration(
                color: Colors.yellow[50],
                borderRadius: BorderRadius.circular(8),
                border: Border.all(color: Colors.yellow[300]!),
              ),
              child: Text(
                'Hint: $pattern',
                style: TextStyle(
                  fontSize: 14,
                  fontStyle: FontStyle.italic,
                  color: Colors.orange[800],
                ),
              ),
            ),

          const SizedBox(height: 16),

          // Answer input
          TextField(
            controller: _answerController,
            focusNode: _answerFocusNode,
            enabled: _isPlaying && !_hasSubmitted,
            decoration: InputDecoration(
              labelText: 'Your answer',
              hintText: 'Enter the next number in the sequence',
              border: OutlineInputBorder(
                borderRadius: BorderRadius.circular(8),
              ),
              suffixIcon: IconButton(
                icon: const Icon(Icons.send),
                onPressed:
                    _isPlaying &&
                            !_hasSubmitted &&
                            _answerController.text.isNotEmpty
                        ? () {
                          _userAnswer = _answerController.text;
                          _checkAnswer();
                        }
                        : null,
              ),
            ),
            keyboardType: TextInputType.number,
            onChanged: (value) {
              setState(() {
                _userAnswer = value;
              });
            },
            onSubmitted: (value) {
              if (_isPlaying && !_hasSubmitted) {
                _userAnswer = value;
                _checkAnswer();
              }
            },
          ),

          const SizedBox(height: 16),

          // Feedback and next button
          if (_hasSubmitted)
            Container(
              width: double.infinity,
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: _isCorrect ? Colors.green[50] : Colors.red[50],
                borderRadius: BorderRadius.circular(8),
                border: Border.all(
                  color: _isCorrect ? Colors.green[300]! : Colors.red[300]!,
                ),
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    children: [
                      Icon(
                        _isCorrect ? Icons.check_circle : Icons.cancel,
                        color: _isCorrect ? Colors.green : Colors.red,
                      ),
                      const SizedBox(width: 8),
                      Text(
                        _isCorrect ? 'Correct!' : 'Incorrect',
                        style: TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.bold,
                          color:
                              _isCorrect ? Colors.green[800] : Colors.red[800],
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 8),
                  Text(
                    _isCorrect
                        ? 'Great job! You found the pattern.'
                        : 'The correct answer was: ${sequence['next']}',
                    style: TextStyle(
                      fontSize: 14,
                      color: _isCorrect ? Colors.green[800] : Colors.red[800],
                    ),
                  ),
                  const SizedBox(height: 16),
                  SizedBox(
                    width: double.infinity,
                    child: ElevatedButton(
                      onPressed: _nextSequence,
                      style: ElevatedButton.styleFrom(
                        backgroundColor: Colors.blue,
                        foregroundColor: Colors.white,
                        padding: const EdgeInsets.symmetric(vertical: 12),
                      ),
                      child: const Text('Next Pattern'),
                    ),
                  ),
                ],
              ),
            ),
        ],
      ),
    );
  }

  Widget _buildGameDetails() {
    // Different details based on game type
    if (widget.widget.id.contains('pattern')) {
      final sequences = widget.widget.data['sequences'] as List<dynamic>? ?? [];

      return Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text('Number of patterns: ${sequences.length}'),
          const SizedBox(height: 4),
          const Text(
            'Find the pattern and determine the next number in the sequence.',
          ),
          const SizedBox(height: 8),
          if (sequences.isNotEmpty)
            Text('Example: ${_getExampleSequence(sequences[0])}'),
        ],
      );
    } else if (widget.widget.id.contains('puzzle')) {
      final puzzleTypes =
          widget.widget.data['puzzleTypes'] as List<dynamic>? ?? [];

      return Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text('Puzzle types: ${puzzleTypes.join(", ")}'),
          const SizedBox(height: 4),
          const Text('Solve various logic puzzles against the clock.'),
        ],
      );
    } else {
      return const Text(
        'This mini-game challenges your problem-solving skills.',
      );
    }
  }

  String _getExampleSequence(Map<String, dynamic> sequence) {
    final numbers = sequence['sequence'] as List<dynamic>? ?? [];
    final next = sequence['next'] as String? ?? '?';
    final pattern = sequence['pattern'] as String? ?? 'Find the pattern';

    return '${numbers.join(", ")}, ? (${pattern})';
  }

  Color _getColorForDifficulty(String difficulty) {
    switch (difficulty.toLowerCase()) {
      case 'easy':
        return Colors.green;
      case 'medium':
        return Colors.orange;
      case 'hard':
        return Colors.red;
      default:
        return Colors.blue;
    }
  }
}
