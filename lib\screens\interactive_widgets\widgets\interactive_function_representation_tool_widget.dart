import 'package:flutter/material.dart';
import 'dart:math' as math;

/// A simple point class to represent coordinates
class Point {
  final double x;
  final double y;

  Point(this.x, this.y);
}

/// A widget that allows users to explore different representations of functions
/// (equations, tables, graphs, and verbal descriptions)
class InteractiveFunctionRepresentationToolWidget extends StatefulWidget {
  /// The data for this widget
  final Map<String, dynamic> data;

  /// Optional callback when the widget state changes
  final Function(bool isCompleted)? onStateChanged;

  const InteractiveFunctionRepresentationToolWidget({
    Key? key,
    required this.data,
    this.onStateChanged,
  }) : super(key: key);

  /// Factory constructor to create the widget from JSON data
  factory InteractiveFunctionRepresentationToolWidget.fromData(
      Map<String, dynamic> data) {
    return InteractiveFunctionRepresentationToolWidget(
      data: data,
    );
  }

  @override
  State<InteractiveFunctionRepresentationToolWidget> createState() =>
      _InteractiveFunctionRepresentationToolWidgetState();
}

class _InteractiveFunctionRepresentationToolWidgetState
    extends State<InteractiveFunctionRepresentationToolWidget>
    with SingleTickerProviderStateMixin {
  // Current representation mode
  String _currentMode = 'equation';

  // Function data
  late String _functionName;
  late String _functionEquation;
  late String _functionDescription;
  late List<Map<String, dynamic>> _functionTable;
  late List<Map<String, dynamic>> _predefinedFunctions;
  late int _selectedFunctionIndex;

  // UI colors
  late Color _primaryColor;
  late Color _secondaryColor;
  late Color _accentColor;
  late Color _backgroundColor;
  late Color _textColor;

  @override
  void initState() {
    super.initState();

    // Initialize colors
    _primaryColor = _parseColor(widget.data['primary_color']) ?? Colors.blue;
    _secondaryColor =
        _parseColor(widget.data['secondary_color']) ?? Colors.orange;
    _accentColor = _parseColor(widget.data['accent_color']) ?? Colors.green;
    _backgroundColor =
        _parseColor(widget.data['background_color']) ?? Colors.white;
    _textColor = _parseColor(widget.data['text_color']) ?? Colors.black87;

    // Initialize predefined functions
    _predefinedFunctions = widget.data['predefined_functions'] != null
        ? List<Map<String, dynamic>>.from(widget.data['predefined_functions'])
        : _getDefaultFunctions();

    // Set initial function
    _selectedFunctionIndex = 0;
    _loadSelectedFunction();
  }

  // Parse color from string
  Color? _parseColor(String? colorStr) {
    if (colorStr == null) return null;
    if (colorStr.startsWith('#')) {
      return Color(int.parse('FF${colorStr.substring(1)}', radix: 16));
    }
    return null;
  }

  // Load the currently selected function
  void _loadSelectedFunction() {
    final function = _predefinedFunctions[_selectedFunctionIndex];
    _functionName = function['name'] ?? 'Unknown Function';
    _functionEquation = function['equation'] ?? 'f(x) = x';
    _functionDescription = function['description'] ??
        'This function takes an input and returns the same value.';
    _functionTable = function['table'] != null
        ? List<Map<String, dynamic>>.from(function['table'])
        : _generateTableFromEquation(_functionEquation);

    // Notify parent of state change
    widget.onStateChanged?.call(true);
  }

  // Generate a table of values from the equation
  List<Map<String, dynamic>> _generateTableFromEquation(String equation) {
    final List<Map<String, dynamic>> table = [];

    // Simple parser for basic functions
    // In a real app, you would use a more robust expression parser
    String logic = equation.toLowerCase();
    if (logic.contains('f(x) =')) {
      logic = logic.replaceAll('f(x) =', '').trim();
    } else if (logic.contains('y =')) {
      logic = logic.replaceAll('y =', '').trim();
    }

    // Generate table values from -5 to 5
    for (int i = -5; i <= 5; i++) {
      double x = i.toDouble();
      double y = _evaluateExpression(logic, x);
      table.add({'x': x, 'y': y});
    }

    return table;
  }

  // Simple expression evaluator
  double _evaluateExpression(String expression, double x) {
    // Replace x with the value
    String expr = expression.replaceAll('x', x.toString());

    // Handle basic operations
    try {
      // Handle power operations (x^2, x^3, etc.)
      RegExp powerRegex = RegExp(r'(\d+\.?\d*|\d*\.?\d+)\s*\^\s*(\d+\.?\d*|\d*\.?\d+)');
      while (powerRegex.hasMatch(expr)) {
        Match match = powerRegex.firstMatch(expr)!;
        double base = double.parse(match.group(1)!);
        double exponent = double.parse(match.group(2)!);
        double result = math.pow(base, exponent).toDouble();
        expr = expr.replaceRange(match.start, match.end, result.toString());
      }

      // This is a very simplified evaluator
      // For a real app, use a proper expression parser
      if (expr.contains('+')) {
        List<String> parts = expr.split('+');
        return double.parse(parts[0]) + double.parse(parts[1]);
      } else if (expr.contains('-')) {
        List<String> parts = expr.split('-');
        if (parts[0].isEmpty) {
          return -double.parse(parts[1]);
        }
        return double.parse(parts[0]) - double.parse(parts[1]);
      } else if (expr.contains('*')) {
        List<String> parts = expr.split('*');
        return double.parse(parts[0]) * double.parse(parts[1]);
      } else if (expr.contains('/')) {
        List<String> parts = expr.split('/');
        return double.parse(parts[0]) / double.parse(parts[1]);
      } else {
        return double.parse(expr);
      }
    } catch (e) {
      // Return a default value if evaluation fails
      return 0;
    }
  }

  // Get default functions if none provided
  List<Map<String, dynamic>> _getDefaultFunctions() {
    return [
      {
        'name': 'Linear Function',
        'equation': 'f(x) = 2x + 3',
        'description':
            'A linear function with slope 2 and y-intercept 3. This type of function creates a straight line when graphed.',
        'table': [
          {'x': -2, 'y': -1},
          {'x': -1, 'y': 1},
          {'x': 0, 'y': 3},
          {'x': 1, 'y': 5},
          {'x': 2, 'y': 7},
        ],
      },
      {
        'name': 'Quadratic Function',
        'equation': 'f(x) = x² - 2x + 1',
        'description':
            'A quadratic function that forms a parabola when graphed. This particular function has a minimum value at x = 1.',
        'table': [
          {'x': -2, 'y': 9},
          {'x': -1, 'y': 4},
          {'x': 0, 'y': 1},
          {'x': 1, 'y': 0},
          {'x': 2, 'y': 1},
        ],
      },
      {
        'name': 'Absolute Value Function',
        'equation': 'f(x) = |x|',
        'description':
            'The absolute value function returns the distance of x from zero, regardless of whether x is positive or negative.',
        'table': [
          {'x': -2, 'y': 2},
          {'x': -1, 'y': 1},
          {'x': 0, 'y': 0},
          {'x': 1, 'y': 1},
          {'x': 2, 'y': 2},
        ],
      },
    ];
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: _backgroundColor,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.1),
            blurRadius: 10,
            offset: const Offset(0, 5),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Function selector
          _buildFunctionSelector(),

          const SizedBox(height: 16),

          // Representation mode selector
          _buildModeSelector(),

          const SizedBox(height: 24),

          // Current representation display
          _buildCurrentRepresentation(),
        ],
      ),
    );
  }

  // Build the function selector dropdown
  Widget _buildFunctionSelector() {
    return Row(
      children: [
        Text(
          'Select Function:',
          style: TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.bold,
            color: _textColor,
          ),
        ),
        const SizedBox(width: 16),
        Expanded(
          child: Container(
            padding: const EdgeInsets.symmetric(horizontal: 12),
            decoration: BoxDecoration(
              border: Border.all(color: _primaryColor),
              borderRadius: BorderRadius.circular(8),
            ),
            child: DropdownButton<int>(
              value: _selectedFunctionIndex,
              isExpanded: true,
              underline: Container(),
              onChanged: (int? newValue) {
                if (newValue != null) {
                  setState(() {
                    _selectedFunctionIndex = newValue;
                    _loadSelectedFunction();
                  });
                }
              },
              items: List.generate(
                _predefinedFunctions.length,
                (index) => DropdownMenuItem<int>(
                  value: index,
                  child: Text(_predefinedFunctions[index]['name'] ?? 'Function ${index + 1}'),
                ),
              ),
            ),
          ),
        ),
      ],
    );
  }

  // Build the representation mode selector
  Widget _buildModeSelector() {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceEvenly,
      children: [
        _buildModeButton('equation', 'Equation'),
        _buildModeButton('table', 'Table'),
        _buildModeButton('graph', 'Graph'),
        _buildModeButton('verbal', 'Verbal'),
      ],
    );
  }

  // Build a mode selector button
  Widget _buildModeButton(String mode, String label) {
    bool isSelected = _currentMode == mode;

    return ElevatedButton(
      onPressed: () {
        setState(() {
          _currentMode = mode;
        });
      },
      style: ElevatedButton.styleFrom(
        backgroundColor: isSelected ? _primaryColor : Colors.grey[300],
        foregroundColor: isSelected ? Colors.white : _textColor,
        padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(8),
        ),
      ),
      child: Text(label),
    );
  }

  // Build the current representation based on selected mode
  Widget _buildCurrentRepresentation() {
    switch (_currentMode) {
      case 'equation':
        return _buildEquationView();
      case 'table':
        return _buildTableView();
      case 'graph':
        return _buildGraphView();
      case 'verbal':
        return _buildVerbalView();
      default:
        return _buildEquationView();
    }
  }

  // Build the equation representation view
  Widget _buildEquationView() {
    return Center(
      child: Container(
        padding: const EdgeInsets.all(24),
        decoration: BoxDecoration(
          color: _primaryColor.withOpacity(0.1),
          borderRadius: BorderRadius.circular(12),
          border: Border.all(color: _primaryColor),
        ),
        child: Text(
          _functionEquation,
          style: TextStyle(
            fontSize: 24,
            fontWeight: FontWeight.bold,
            color: _primaryColor,
          ),
        ),
      ),
    );
  }

  // Build the table representation view
  Widget _buildTableView() {
    return Container(
      height: 200,
      decoration: BoxDecoration(
        border: Border.all(color: Colors.grey[300]!),
        borderRadius: BorderRadius.circular(8),
      ),
      child: SingleChildScrollView(
        child: DataTable(
          columns: const [
            DataColumn(label: Text('x')),
            DataColumn(label: Text('f(x)')),
          ],
          rows: _functionTable.map((row) {
            return DataRow(
              cells: [
                DataCell(Text(row['x'].toString())),
                DataCell(Text(row['y'].toString())),
              ],
            );
          }).toList(),
        ),
      ),
    );
  }

  // Build the graph representation view
  Widget _buildGraphView() {
    return Container(
      height: 250,
      width: double.infinity,
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: Colors.grey[300]!),
      ),
      child: ClipRRect(
        borderRadius: BorderRadius.circular(8),
        child: CustomPaint(
          painter: FunctionGraphPainter(
            points: _functionTable.map((point) =>
              Point(point['x'].toDouble(), point['y'].toDouble())
            ).toList(),
            color: _primaryColor,
            minX: -5,
            maxX: 5,
            minY: _getMinY(),
            maxY: _getMaxY(),
          ),
          child: Container(),
        ),
      ),
    );
  }

  // Get minimum Y value for graph scaling
  double _getMinY() {
    double minY = _functionTable.isEmpty
        ? -5
        : _functionTable.map((p) => p['y'] as double).reduce(math.min);
    return math.min(minY, -1); // Ensure we show at least -1 on the y-axis
  }

  // Get maximum Y value for graph scaling
  double _getMaxY() {
    double maxY = _functionTable.isEmpty
        ? 5
        : _functionTable.map((p) => p['y'] as double).reduce(math.max);
    return math.max(maxY, 1); // Ensure we show at least 1 on the y-axis
  }

  // Build the verbal representation view
  Widget _buildVerbalView() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: _secondaryColor.withOpacity(0.1),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: _secondaryColor),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            _functionName,
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
              color: _secondaryColor,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            _functionDescription,
            style: TextStyle(
              fontSize: 16,
              color: _textColor,
            ),
          ),
        ],
      ),
    );
  }
}

/// Custom painter for drawing function graphs
class FunctionGraphPainter extends CustomPainter {
  final List<Point> points;
  final Color color;
  final double minX;
  final double maxX;
  final double minY;
  final double maxY;

  FunctionGraphPainter({
    required this.points,
    required this.color,
    required this.minX,
    required this.maxX,
    required this.minY,
    required this.maxY,
  });

  @override
  void paint(Canvas canvas, Size size) {
    final paint = Paint()
      ..color = color
      ..strokeWidth = 2.0
      ..style = PaintingStyle.stroke;

    final gridPaint = Paint()
      ..color = Colors.grey[300]!
      ..strokeWidth = 0.5
      ..style = PaintingStyle.stroke;

    final axisPaint = Paint()
      ..color = Colors.black87
      ..strokeWidth = 1.0
      ..style = PaintingStyle.stroke;

    // Draw grid
    _drawGrid(canvas, size, gridPaint);

    // Draw axes
    _drawAxes(canvas, size, axisPaint);

    // Draw function
    _drawFunction(canvas, size, paint);

    // Draw points
    _drawPoints(canvas, size, paint);
  }

  void _drawGrid(Canvas canvas, Size size, Paint paint) {
    // Draw vertical grid lines
    for (double x = minX; x <= maxX; x += 1) {
      final screenX = _mapXToScreen(x, size);
      canvas.drawLine(
        Offset(screenX, 0),
        Offset(screenX, size.height),
        paint,
      );
    }

    // Draw horizontal grid lines
    for (double y = minY; y <= maxY; y += 1) {
      final screenY = _mapYToScreen(y, size);
      canvas.drawLine(
        Offset(0, screenY),
        Offset(size.width, screenY),
        paint,
      );
    }
  }

  void _drawAxes(Canvas canvas, Size size, Paint paint) {
    // Draw x-axis
    final yZero = _mapYToScreen(0, size);
    canvas.drawLine(
      Offset(0, yZero),
      Offset(size.width, yZero),
      paint,
    );

    // Draw y-axis
    final xZero = _mapXToScreen(0, size);
    canvas.drawLine(
      Offset(xZero, 0),
      Offset(xZero, size.height),
      paint,
    );

    // Draw axis labels
    final textStyle = TextStyle(
      color: Colors.black87,
      fontSize: 10,
    );
    final textPainter = TextPainter(
      textDirection: TextDirection.ltr,
    );

    // X-axis labels
    for (int i = minX.toInt(); i <= maxX.toInt(); i++) {
      if (i == 0) continue; // Skip zero as it's the origin
      final x = i.toDouble();
      final screenX = _mapXToScreen(x, size);

      textPainter.text = TextSpan(
        text: i.toString(),
        style: textStyle,
      );
      textPainter.layout();
      textPainter.paint(
        canvas,
        Offset(screenX - textPainter.width / 2, yZero + 5),
      );
    }

    // Y-axis labels
    for (int i = minY.toInt(); i <= maxY.toInt(); i++) {
      if (i == 0) continue; // Skip zero as it's the origin
      final y = i.toDouble();
      final screenY = _mapYToScreen(y, size);

      textPainter.text = TextSpan(
        text: i.toString(),
        style: textStyle,
      );
      textPainter.layout();
      textPainter.paint(
        canvas,
        Offset(xZero + 5, screenY - textPainter.height / 2),
      );
    }

    // Origin label
    textPainter.text = TextSpan(
      text: "0",
      style: textStyle,
    );
    textPainter.layout();
    textPainter.paint(
      canvas,
      Offset(xZero + 5, yZero + 5),
    );
  }

  void _drawFunction(Canvas canvas, Size size, Paint paint) {
    if (points.isEmpty) return;

    final path = Path();
    bool started = false;

    // Sort points by x value
    final sortedPoints = List<Point>.from(points)
      ..sort((a, b) => a.x.compareTo(b.x));

    for (final point in sortedPoints) {
      final screenX = _mapXToScreen(point.x, size);
      final screenY = _mapYToScreen(point.y, size);

      // Skip points outside the visible area
      if (point.x < minX || point.x > maxX || point.y < minY || point.y > maxY) {
        continue;
      }

      if (!started) {
        path.moveTo(screenX, screenY);
        started = true;
      } else {
        path.lineTo(screenX, screenY);
      }
    }

    canvas.drawPath(path, paint);
  }

  void _drawPoints(Canvas canvas, Size size, Paint paint) {
    final pointPaint = Paint()
      ..color = color
      ..strokeWidth = 1.0
      ..style = PaintingStyle.fill;

    for (final point in points) {
      // Skip points outside the visible area
      if (point.x < minX || point.x > maxX || point.y < minY || point.y > maxY) {
        continue;
      }

      final screenX = _mapXToScreen(point.x, size);
      final screenY = _mapYToScreen(point.y, size);

      canvas.drawCircle(
        Offset(screenX, screenY),
        4,
        pointPaint,
      );
    }
  }

  // Map x coordinate from math space to screen space
  double _mapXToScreen(double x, Size size) {
    return size.width * (x - minX) / (maxX - minX);
  }

  // Map y coordinate from math space to screen space
  double _mapYToScreen(double y, Size size) {
    // Note: Screen coordinates have y increasing downward, math has y increasing upward
    return size.height * (1 - (y - minY) / (maxY - minY));
  }

  @override
  bool shouldRepaint(covariant FunctionGraphPainter oldDelegate) {
    return oldDelegate.points != points ||
        oldDelegate.color != color ||
        oldDelegate.minX != minX ||
        oldDelegate.maxX != maxX ||
        oldDelegate.minY != minY ||
        oldDelegate.maxY != maxY;
  }
}