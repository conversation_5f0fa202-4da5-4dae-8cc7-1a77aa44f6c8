import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import '../../../../models/course_models.dart';

class InteractiveExpressionEvaluatorWidget extends StatefulWidget {
  final InteractiveExpressionEvaluatorElement expressionEvaluatorElement;
  final VoidCallback onNextAction;
  final bool isLastSlideInLesson;

  const InteractiveExpressionEvaluatorWidget({
    Key? key,
    required this.expressionEvaluatorElement,
    required this.onNextAction,
    this.isLastSlideInLesson = false,
  }) : super(key: key);

  @override
  State<InteractiveExpressionEvaluatorWidget> createState() =>
      _InteractiveExpressionEvaluatorWidgetState();
}

class _InteractiveExpressionEvaluatorWidgetState
    extends State<InteractiveExpressionEvaluatorWidget> {
  int _currentTaskIndex = 0;
  String _userAnswer = '';
  bool _isAnswered = false;
  bool _isCorrect = false;
  bool _showSolution = false;
  final TextEditingController _answerController = TextEditingController();

  @override
  void dispose() {
    _answerController.dispose();
    super.dispose();
  }

  void _checkAnswer() {
    final currentTask = widget.expressionEvaluatorElement.tasks[_currentTaskIndex];
    
    // Parse user answer based on input type
    num? userAnswerNum;
    if (widget.expressionEvaluatorElement.input_type == 'number') {
      userAnswerNum = num.tryParse(_userAnswer);
    }
    
    // Check if answer is correct
    bool isCorrect = false;
    if (userAnswerNum != null) {
      isCorrect = userAnswerNum == currentTask.correct_answer;
    }
    
    setState(() {
      _isAnswered = true;
      _isCorrect = isCorrect;
    });
    
    // Provide haptic feedback
    HapticFeedback.mediumImpact();
    
    // Show feedback in a snackbar
    ScaffoldMessenger.of(context).clearSnackBars();
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(
          _isCorrect ? currentTask.feedback_correct : currentTask.feedback_incorrect,
        ),
        backgroundColor: _isCorrect ? Colors.green : Colors.redAccent,
        duration: const Duration(seconds: 3),
      ),
    );
  }

  void _showSolutionSteps() {
    setState(() {
      _showSolution = true;
    });
  }

  void _moveToNextTask() {
    if (_currentTaskIndex < widget.expressionEvaluatorElement.tasks.length - 1) {
      setState(() {
        _currentTaskIndex++;
        _isAnswered = false;
        _isCorrect = false;
        _showSolution = false;
        _userAnswer = '';
        _answerController.clear();
      });
    } else {
      // All tasks completed
      widget.onNextAction();
    }
  }

  @override
  Widget build(BuildContext context) {
    final currentTask = widget.expressionEvaluatorElement.tasks[_currentTaskIndex];
    
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withOpacity(0.2),
            spreadRadius: 1,
            blurRadius: 3,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Overall prompt
          Text(
            widget.expressionEvaluatorElement.overall_prompt,
            style: const TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 16),
          
          // Current task
          Container(
            padding: const EdgeInsets.all(12),
            decoration: BoxDecoration(
              color: Colors.blue.withOpacity(0.1),
              borderRadius: BorderRadius.circular(8),
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'Task ${_currentTaskIndex + 1} of ${widget.expressionEvaluatorElement.tasks.length}',
                  style: TextStyle(
                    fontSize: 14,
                    color: Colors.blue[700],
                    fontWeight: FontWeight.w500,
                  ),
                ),
                const SizedBox(height: 8),
                Text(
                  currentTask.prompt_with_values,
                  style: const TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.w500,
                  ),
                ),
                const SizedBox(height: 16),
                
                // Expression display
                Container(
                  width: double.infinity,
                  padding: const EdgeInsets.symmetric(vertical: 12, horizontal: 16),
                  decoration: BoxDecoration(
                    color: Colors.grey[100],
                    borderRadius: BorderRadius.circular(8),
                    border: Border.all(color: Colors.grey[300]!),
                  ),
                  child: Text(
                    currentTask.display_expression,
                    style: const TextStyle(
                      fontSize: 18,
                      fontWeight: FontWeight.bold,
                      fontFamily: 'monospace',
                    ),
                    textAlign: TextAlign.center,
                  ),
                ),
              ],
            ),
          ),
          const SizedBox(height: 24),
          
          // Answer input
          TextField(
            controller: _answerController,
            decoration: InputDecoration(
              labelText: 'Your Answer',
              hintText: 'Enter the value',
              border: OutlineInputBorder(
                borderRadius: BorderRadius.circular(8),
              ),
              suffixIcon: IconButton(
                icon: const Icon(Icons.check_circle),
                onPressed: _isAnswered ? null : () => _checkAnswer(),
              ),
              enabled: !_isAnswered,
            ),
            keyboardType: TextInputType.number,
            onChanged: (value) {
              setState(() {
                _userAnswer = value;
              });
            },
            onSubmitted: _isAnswered ? null : (value) => _checkAnswer(),
          ),
          const SizedBox(height: 16),
          
          // Action buttons
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              // Show solution button
              if (!_showSolution && _isAnswered)
                ElevatedButton.icon(
                  onPressed: _showSolutionSteps,
                  icon: const Icon(Icons.lightbulb_outline),
                  label: Text(
                    widget.expressionEvaluatorElement.show_solution_button_text ?? 'Show Steps',
                  ),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Colors.amber,
                    foregroundColor: Colors.black87,
                  ),
                ),
              
              // Check answer button
              if (!_isAnswered)
                ElevatedButton.icon(
                  onPressed: _userAnswer.isNotEmpty ? _checkAnswer : null,
                  icon: const Icon(Icons.check),
                  label: Text(
                    widget.expressionEvaluatorElement.check_button_text ?? 'Check Answer',
                  ),
                ),
              
              // Next button
              if (_isAnswered)
                ElevatedButton.icon(
                  onPressed: _moveToNextTask,
                  icon: const Icon(Icons.arrow_forward),
                  label: Text(
                    _currentTaskIndex < widget.expressionEvaluatorElement.tasks.length - 1
                        ? 'Next Task'
                        : widget.isLastSlideInLesson
                            ? 'Complete Lesson'
                            : 'Continue',
                  ),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: _isCorrect ? Colors.green : Colors.blue,
                    foregroundColor: Colors.white,
                  ),
                ),
            ],
          ),
          
          // Solution steps
          if (_showSolution) ...[
            const SizedBox(height: 24),
            Container(
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: Colors.amber.withOpacity(0.1),
                borderRadius: BorderRadius.circular(8),
                border: Border.all(color: Colors.amber[300]!),
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  const Text(
                    'Solution Steps:',
                    style: TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.bold,
                      color: Colors.amber,
                    ),
                  ),
                  const SizedBox(height: 8),
                  ...currentTask.solution_steps.map((step) => Padding(
                    padding: const EdgeInsets.symmetric(vertical: 4),
                    child: Text(step),
                  )),
                ],
              ),
            ),
          ],
        ],
      ),
    );
  }
}
