{"id": "core-electrical-components", "title": "Core Electrical Components", "description": "Deepen your understanding of fundamental electronic components and their behavior.", "order": 1, "lessons": [{"id": "resistors-controlling-current-flow", "title": "Resistors: Controlling Current Flow", "description": "Explore different types and their applications.", "order": 1, "type": "interactive_lesson", "estimatedTimeMinutes": 20, "contentBlocks": [{"id": "resistors-screen-1", "type": "lesson_screen", "order": 1, "estimatedTimeSeconds": 60, "content": {"headline": "What are Resistors?", "body_md": "Resistors are fundamental electronic components that **limit or regulate the flow of electrical current** in a circuit. Think of them like a narrow pipe restricting water flow, or friction slowing down an object.", "visual": {"type": "unsplash_search", "value": "water flowing through pipes"}, "interactive_element": {"type": "button", "button_text": "Got it! What's next?", "action": "next_screen"}}}, {"id": "resistors-screen-2", "type": "lesson_screen", "order": 2, "estimatedTimeSeconds": 120, "content": {"headline": "The Rule of Resistance: Oh<PERSON>'s Law", "body_md": "The relationship between voltage (V), current (I), and resistance (R) is described by **<PERSON><PERSON>'s Law**: `V = I * R`.\n\nThis means for a given voltage:\n- Higher resistance (R ↑) leads to lower current (I ↓).\n- Lower resistance (R ↓) leads to higher current (I ↑).", "visual": {"type": "static_text", "value": "V = I × R"}, "interactive_element": {"type": "multiple_choice_text", "question_text": "According to <PERSON><PERSON>'s Law, if you increase resistance while keeping voltage constant, what happens to the current?", "options": [{"id": "opt1", "text": "Increases"}, {"id": "opt2", "text": "Decreases"}, {"id": "opt3", "text": "Stays the same"}, {"id": "opt4", "text": "Becomes zero"}], "correct_option_id": "opt2", "feedback_correct": "Correct! Higher resistance restricts current flow.", "feedback_incorrect": "Not quite. Remember, resistance opposes current. Higher R means lower I for the same V."}}}, {"id": "resistors-screen-3", "type": "lesson_screen", "order": 3, "estimatedTimeSeconds": 75, "content": {"headline": "Fixed Resistors: Consistent Control", "body_md": "Most common resistors are **fixed resistors**. They have a specific, unchanging resistance value. They are made from materials like carbon film or metal film and come in various sizes and power ratings.", "visual": {"type": "unsplash_search", "value": "electronic resistors assortment"}, "interactive_element": {"type": "button", "button_text": "How do we know their value?", "action": "next_screen"}}}, {"id": "resistors-screen-4", "type": "lesson_screen", "order": 4, "estimatedTimeSeconds": 150, "content": {"headline": "Decoding Resistors: The Color Code", "body_md": "Fixed resistors use colored bands to indicate their resistance value and tolerance. It's a standard system. Let's see an example!\n\n**<PERSON>, <PERSON>, <PERSON>, Gold**\n- Brown: 1\n- Black: 0\n- Red: Multiplier x100 (or 2 zeros)\n- Gold: Tolerance +/- 5%\n\nSo, this resistor is 10 followed by 00 = 1000 Ohms (or 1kΩ) with 5% tolerance.", "visual": {"type": "local_asset", "value": "assets/images/course_specific/resistor_color_code_example.png"}, "interactive_element": {"type": "multiple_choice_text", "question_text": "What is the value of a resistor with bands: Yellow, Violet, Orange, Silver?", "options": [{"id": "opt1", "text": "470Ω, 10% tolerance"}, {"id": "opt2", "text": "4.7kΩ, 5% tolerance"}, {"id": "opt3", "text": "47kΩ, 10% tolerance"}, {"id": "opt4", "text": "470kΩ, 5% tolerance"}], "correct_option_id": "opt3", "feedback_correct": "Spot on! Yellow (4), <PERSON> (7), Orange (x1000) = 47,000Ω or 47kΩ. Silver is 10% tolerance.", "feedback_incorrect": "Almost! Check the color chart again. Yellow=4, <PERSON>=7, Orange=x1k, Silver=10%."}}}, {"id": "resistors-screen-5", "type": "lesson_screen", "order": 5, "estimatedTimeSeconds": 90, "content": {"headline": "Adjustable Resistance: Potentiometers", "body_md": "Not all resistors are fixed! **Potentiometers** (often called 'pots') are variable resistors. Their resistance can be changed, usually by turning a knob or sliding a lever. Think of a dimmer switch or a volume control on an old radio.", "visual": {"type": "unsplash_search", "value": "potentiometer electronic"}, "interactive_element": {"type": "multiple_choice_text", "question_text": "Which of these common devices likely uses a potentiometer?", "options": [{"id": "opt1", "text": "A simple LED"}, {"id": "opt2", "text": "A light dimmer switch"}, {"id": "opt3", "text": "A battery"}, {"id": "opt4", "text": "A fixed wire"}], "correct_option_id": "opt2", "feedback_correct": "Exactly! Light dimmers often use potentiometers to vary the current to the light.", "feedback_incorrect": "Consider which option allows for manual adjustment of a setting."}}}, {"id": "resistors-screen-6", "type": "lesson_screen", "order": 6, "estimatedTimeSeconds": 60, "content": {"headline": "Resistors Recap!", "body_md": "Great job! You've learned:\n\n- What resistors do (control current).\n- <PERSON><PERSON>'s Law (`V = I * R`) is key.\n- About fixed resistors and their color codes.\n- About variable resistors (potentiometers) and their uses.", "visual": {"type": "giphy_search", "value": "electronics circuit success"}, "interactive_element": {"type": "button", "button_text": "Finish Lesson", "action": "complete_lesson"}}}]}, {"id": "capacitors-storing-electrical-energy", "title": "Capacitors: Storing Electrical Energy", "description": "Understand capacitance and charging/discharging.", "order": 2, "type": "interactive_lesson", "estimatedTimeMinutes": 25, "contentBlocks": [{"id": "capacitors-screen-1", "type": "lesson_screen", "order": 1, "estimatedTimeSeconds": 60, "content": {"headline": "Meet the Capacitor!", "body_md": "Capacitors are like tiny, rechargeable batteries. They **store electrical energy** in an electric field. Think of a capacitor as a small water tank that can quickly fill up and then release its water when needed.", "visual": {"type": "unsplash_search", "value": "water tank"}, "interactive_element": {"type": "button", "button_text": "How do they work?", "action": "next_screen"}}}, {"id": "capacitors-screen-2", "type": "lesson_screen", "order": 2, "estimatedTimeSeconds": 120, "content": {"headline": "Inside a Capacitor", "body_md": "A simple capacitor consists of two conductive **plates** separated by an insulating material called a **dielectric**.\n\nWhen a voltage is applied, positive charge builds up on one plate and negative charge on the other. The dielectric prevents the charge from flowing directly between the plates.", "visual": {"type": "local_asset", "value": "assets/images/course_specific/capacitor_diagram.png"}, "interactive_element": {"type": "multiple_choice_text", "question_text": "What is the role of the dielectric in a capacitor?", "options": [{"id": "opt1", "text": "To conduct current between plates"}, {"id": "opt2", "text": "To store magnetic energy"}, {"id": "opt3", "text": "To insulate the plates and increase capacitance"}, {"id": "opt4", "text": "To generate voltage"}], "correct_option_id": "opt3", "feedback_correct": "Correct! The dielectric is an insulator that separates the plates.", "feedback_incorrect": "Not quite. The dielectric is an insulator. Think about what insulators do."}}}, {"id": "capacitors-screen-3", "type": "lesson_screen", "order": 3, "estimatedTimeSeconds": 90, "content": {"headline": "Measuring Storage: Capacitance", "body_md": "**Capacitance (C)** is the ability of a capacitor to store charge. It's measured in **Farads (F)**.\n\nA larger capacitance means the capacitor can store more charge for a given voltage. It's like having a bigger water tank!\n\nThe formula is: `C = Q / V` (Capacitance = Charge / Voltage)", "visual": {"type": "static_text", "value": "C = Q / V"}, "interactive_element": {"type": "button", "button_text": "How do they charge and discharge?", "action": "next_screen"}}}, {"id": "capacitors-screen-4", "type": "lesson_screen", "order": 4, "estimatedTimeSeconds": 100, "content": {"headline": "Charging Up!", "body_md": "When a capacitor is connected to a voltage source (like a battery), current flows, and charge accumulates on its plates. Initially, current is high, but it decreases as the capacitor charges. Once fully charged, current stops flowing in a DC circuit.", "visual": {"type": "giphy_search", "value": "battery charging animation"}, "interactive_element": {"type": "multiple_choice_text", "question_text": "When a capacitor first starts charging in a DC circuit, is the current flow high or low?", "options": [{"id": "opt1", "text": "High, then decreases"}, {"id": "opt2", "text": "Low, then increases"}, {"id": "opt3", "text": "Zero"}, {"id": "opt4", "text": "Constant"}], "correct_option_id": "opt1", "feedback_correct": "Exactly! Current is initially high as charge rushes onto the plates.", "feedback_incorrect": "Think about the initial rush of charge. It's like opening a dam."}}}, {"id": "capacitors-screen-5", "type": "lesson_screen", "order": 5, "estimatedTimeSeconds": 100, "content": {"headline": "Releasing Energy: Discharging", "body_md": "Once charged, a capacitor can release its stored energy. If connected to a load (like an LED), current will flow from the capacitor through the load until the capacitor is discharged. This is how camera flashes work – quick release of stored energy!", "visual": {"type": "giphy_search", "value": "camera flash"}, "interactive_element": {"type": "multiple_choice_text", "question_text": "What happens to the stored energy when a capacitor discharges through a load?", "options": [{"id": "opt1", "text": "It disappears"}, {"id": "opt2", "text": "It converts to magnetic energy"}, {"id": "opt3", "text": "It is transferred to the load (e.g., as light or heat)"}, {"id": "opt4", "text": "It returns to the battery"}], "correct_option_id": "opt3", "feedback_correct": "Correct! The energy powers the load.", "feedback_incorrect": "The energy doesn't just vanish; it gets used!"}}}, {"id": "capacitors-screen-6", "type": "lesson_screen", "order": 6, "estimatedTimeSeconds": 60, "content": {"headline": "Capacitors Uncovered!", "body_md": "Well done! You've learned:\n\n- Capacitors store electrical energy.\n- They consist of plates and a dielectric.\n- Capacitance (Farads) measures their storage ability.\n- How they charge and discharge.", "visual": {"type": "giphy_search", "value": "electric spark success"}, "interactive_element": {"type": "button", "button_text": "Finish Lesson", "action": "complete_lesson"}}}]}, {"id": "inductors-resisting-changes-in-current", "title": "Inductors: Resisting Changes in Current", "description": "Explore inductance and magnetic fields.", "order": 3, "type": "interactive_lesson", "estimatedTimeMinutes": 20, "contentBlocks": [{"id": "inductors-screen-1", "type": "lesson_screen", "order": 1, "estimatedTimeSeconds": 60, "content": {"headline": "Introducing Inductors!", "body_md": "Inductors are components that **store energy in a magnetic field** when electric current flows through them. They are typically a coil of wire. Their main characteristic is to **resist changes in current**.", "visual": {"type": "unsplash_search", "value": "copper coil"}, "interactive_element": {"type": "button", "button_text": "How do they resist change?", "action": "next_screen"}}}, {"id": "inductors-screen-2", "type": "lesson_screen", "order": 2, "estimatedTimeSeconds": 120, "content": {"headline": "Magnetic Fields and Inductance", "body_md": "When current flows through a wire, it creates a magnetic field around it. Coiling the wire concentrates this field.\n\n**Inductance (L)** is the property of an inductor to oppose changes in current. It's measured in **Henrys (H)**.\n\nIf current tries to change quickly, the inductor generates a voltage (called back EMF) that opposes this change.", "visual": {"type": "local_asset", "value": "assets/images/course_specific/inductor_magnetic_field.png"}, "interactive_element": {"type": "multiple_choice_text", "question_text": "What does an inductor primarily store energy as?", "options": [{"id": "opt1", "text": "Electric field"}, {"id": "opt2", "text": "Magnetic field"}, {"id": "opt3", "text": "Heat energy"}, {"id": "opt4", "text": "Kinetic energy"}], "correct_option_id": "opt2", "feedback_correct": "Correct! Inductors store energy in their magnetic field.", "feedback_incorrect": "Think about the coil of wire and what it generates when current flows."}}}, {"id": "inductors-screen-3", "type": "lesson_screen", "order": 3, "estimatedTimeSeconds": 90, "content": {"headline": "<PERSON>z's Law in Action", "body_md": "The inductor's opposition to current change is a consequence of **<PERSON><PERSON>'s Law**. It states that the induced voltage (back EMF) will always act to oppose the change in current that created it.\n\n- If current tries to **increase**, the inductor pushes back to slow the increase.\n- If current tries to **decrease**, the inductor pushes to keep it flowing.", "visual": {"type": "giphy_search", "value": "pushing against force"}, "interactive_element": {"type": "button", "button_text": "What are they used for?", "action": "next_screen"}}}, {"id": "inductors-screen-4", "type": "lesson_screen", "order": 4, "estimatedTimeSeconds": 100, "content": {"headline": "Applications: Smoothing and Filtering", "body_md": "Because they resist changes in current, inductors are excellent for:\n\n- **Smoothing out fluctuating DC currents** in power supplies.\n- **Filtering out unwanted frequencies** in AC circuits (often with capacitors in LC circuits).\n- Storing energy temporarily in switching power supplies.", "visual": {"type": "unsplash_search", "value": "electronic circuit board components"}, "interactive_element": {"type": "multiple_choice_text", "question_text": "If you have a DC power supply with some ripple (fluctuation), what might an inductor help with?", "options": [{"id": "opt1", "text": "Increase the voltage"}, {"id": "opt2", "text": "Smooth out the current"}, {"id": "opt3", "text": "Block all current"}, {"id": "opt4", "text": "Amplify the ripple"}], "correct_option_id": "opt2", "feedback_correct": "Exactly! Inductors resist changes, helping to smooth the current.", "feedback_incorrect": "Remember, inductors oppose *changes* in current."}}}, {"id": "inductors-screen-5", "type": "lesson_screen", "order": 5, "estimatedTimeSeconds": 75, "content": {"headline": "Types of Inductors", "body_md": "Inductors come in various forms:\n\n- **Air Core:** Just a coil of wire.\n- **Iron Core:** Wire wrapped around an iron core to greatly increase inductance.\n- **Ferrite Core:** Uses a ferrite material, good for high-frequency applications.\n- **Toroidal:** Wire wrapped around a donut-shaped core, helps contain the magnetic field.", "visual": {"type": "unsplash_search", "value": "inductor assortment"}, "interactive_element": {"type": "button", "button_text": "Let's wrap up!", "action": "next_screen"}}}, {"id": "inductors-screen-6", "type": "lesson_screen", "order": 6, "estimatedTimeSeconds": 60, "content": {"headline": "Inductors Demystified!", "body_md": "Fantastic! You've learned:\n\n- Inductors store energy in magnetic fields.\n- They resist changes in current (Inductance, <PERSON><PERSON>).\n- <PERSON><PERSON>'s Law explains their behavior.\n- They are used for smoothing, filtering, and energy storage.", "visual": {"type": "giphy_search", "value": "magnetism science success"}, "interactive_element": {"type": "button", "button_text": "Finish Lesson", "action": "complete_lesson"}}}]}, {"id": "diodes-one-way-current-flow", "title": "Diodes: One-Way Current Flow", "description": "Understand their behavior and applications like rectification.", "order": 4, "type": "interactive_lesson", "estimatedTimeMinutes": 20, "contentBlocks": [{"id": "diodes-screen-1", "type": "lesson_screen", "order": 1, "estimatedTimeSeconds": 60, "content": {"headline": "Diodes: The One-Way Street for Current", "body_md": "Diodes are semiconductor devices that act like a **one-way valve for electrical current**. They allow current to flow easily in one direction but block it almost completely in the other.", "visual": {"type": "unsplash_search", "value": "one way street sign"}, "interactive_element": {"type": "button", "button_text": "How do they achieve this?", "action": "next_screen"}}}, {"id": "diodes-screen-2", "type": "lesson_screen", "order": 2, "estimatedTimeSeconds": 120, "content": {"headline": "Anode and Cathode: The Two Terminals", "body_md": "A diode has two terminals:\n\n- **Anode (+):** Where conventional current enters.\n- **Cathode (-):** Where conventional current leaves.\n\nThe arrow symbol for a diode points in the direction of conventional current flow (from anode to cathode).", "visual": {"type": "local_asset", "value": "assets/images/course_specific/diode_symbol_anode_cathode.png"}, "interactive_element": {"type": "multiple_choice_text", "question_text": "In which direction does the arrow in a diode symbol point?", "options": [{"id": "opt1", "text": "Opposite to current flow"}, {"id": "opt2", "text": "In the direction of conventional current flow"}, {"id": "opt3", "text": "Towards the negative terminal always"}, {"id": "opt4", "text": "It's arbitrary"}], "correct_option_id": "opt2", "feedback_correct": "Correct! The arrow shows the easy path for conventional current.", "feedback_incorrect": "The arrow indicates the direction current *can* flow easily."}}}, {"id": "diodes-screen-3", "type": "lesson_screen", "order": 3, "estimatedTimeSeconds": 100, "content": {"headline": "Forward Bias: Current Flows!", "body_md": "When the anode is more positive than the cathode (called **forward bias**), the diode conducts current with only a small voltage drop across it (typically ~0.7V for silicon diodes). The 'valve' is open!", "visual": {"type": "giphy_search", "value": "open door"}, "interactive_element": {"type": "button", "button_text": "What if it's the other way?", "action": "next_screen"}}}, {"id": "diodes-screen-4", "type": "lesson_screen", "order": 4, "estimatedTimeSeconds": 100, "content": {"headline": "Reverse Bias: Current Blocked!", "body_md": "When the cathode is more positive than the anode (called **reverse bias**), the diode blocks current flow. Only a very tiny leakage current might pass. The 'valve' is closed!\n\nHowever, if the reverse voltage is too high (breakdown voltage), the diode can be damaged and conduct heavily.", "visual": {"type": "giphy_search", "value": "closed door"}, "interactive_element": {"type": "multiple_choice_text", "question_text": "What happens if a diode is reverse-biased beyond its breakdown voltage?", "options": [{"id": "opt1", "text": "It operates normally"}, {"id": "opt2", "text": "It stops all current perfectly"}, {"id": "opt3", "text": "It can be damaged and conduct heavily"}, {"id": "opt4", "text": "It turns into a capacitor"}], "correct_option_id": "opt3", "feedback_correct": "Correct! Exceeding breakdown voltage can damage the diode.", "feedback_incorrect": "Think about what 'breakdown' implies for a component."}}}, {"id": "diodes-screen-5", "type": "lesson_screen", "order": 5, "estimatedTimeSeconds": 90, "content": {"headline": "Key Application: Rectification", "body_md": "One of the most common uses for diodes is **rectification**: converting Alternating Current (AC) to Direct Current (DC). Since diodes only allow current in one direction, they can 'chop off' half of the AC waveform, or redirect it to create a pulsating DC.", "visual": {"type": "local_asset", "value": "assets/images/course_specific/half_wave_rectification.gif"}, "interactive_element": {"type": "button", "button_text": "Let's summarize!", "action": "next_screen"}}}, {"id": "diodes-screen-6", "type": "lesson_screen", "order": 6, "estimatedTimeSeconds": 60, "content": {"headline": "Diodes Decoded!", "body_md": "Excellent work! You now know:\n\n- Diodes allow current in one direction (anode to cathode).\n- Forward bias = conducts, Reverse bias = blocks.\n- A key application is AC to DC rectification.\n- They have a small forward voltage drop.", "visual": {"type": "giphy_search", "value": "arrow direction success"}, "interactive_element": {"type": "button", "button_text": "Finish Lesson", "action": "complete_lesson"}}}]}, {"id": "transistors-electronic-switches-amplifiers", "title": "Transistors: Electronic Switches and Amplifiers (Conceptual)", "description": "Introduce the basic functionality of transistors.", "order": 5, "type": "interactive_lesson", "estimatedTimeMinutes": 15, "contentBlocks": [{"id": "transistors-screen-1", "type": "lesson_screen", "order": 1, "estimatedTimeSeconds": 75, "content": {"headline": "Transistors: The Powerhouses!", "body_md": "Transistors are arguably one of the most important inventions of the 20th century! They are semiconductor devices that can **amplify** electrical signals or act as **electronic switches**.\n\nThink of them as a tiny, controllable gate for electricity.", "visual": {"type": "unsplash_search", "value": "microchip close up"}, "interactive_element": {"type": "button", "button_text": "Switches and Amplifiers? Tell me more!", "action": "next_screen"}}}, {"id": "transistors-screen-2", "type": "lesson_screen", "order": 2, "estimatedTimeSeconds": 100, "content": {"headline": "Transistors as Switches", "body_md": "As a switch, a transistor can turn a current ON or OFF. A small current or voltage applied to one terminal (the **base** or **gate**) can control a much larger current flowing through the other two terminals (the **collector** and **emitter**, or **drain** and **source**).\n\nThis is the fundamental principle behind digital logic and computers!", "visual": {"type": "giphy_search", "value": "light switch on off"}, "interactive_element": {"type": "multiple_choice_text", "question_text": "When a transistor acts as a switch, what does a small input signal control?", "options": [{"id": "opt1", "text": "The transistor's color"}, {"id": "opt2", "text": "A much larger current flow"}, {"id": "opt3", "text": "The room temperature"}, {"id": "opt4", "text": "The speed of light"}], "correct_option_id": "opt2", "feedback_correct": "Precisely! A small control signal manages a larger current.", "feedback_incorrect": "The control signal at the base/gate influences the main current path."}}}, {"id": "transistors-screen-3", "type": "lesson_screen", "order": 3, "estimatedTimeSeconds": 100, "content": {"headline": "Transistors as Amplifiers", "body_md": "As an amplifier, a transistor can take a small, varying input signal (like from a microphone) and produce a much larger, but identically shaped, output signal. This makes weak signals strong enough to drive speakers or other devices.", "visual": {"type": "unsplash_search", "value": "audio sound waves"}, "interactive_element": {"type": "multiple_choice_text", "question_text": "What is the primary function of a transistor when used as an amplifier?", "options": [{"id": "opt1", "text": "To block the signal"}, {"id": "opt2", "text": "To make a weak signal stronger"}, {"id": "opt3", "text": "To change the signal's frequency"}, {"id": "opt4", "text": "To store the signal"}], "correct_option_id": "opt2", "feedback_correct": "Correct! It boosts the signal's strength.", "feedback_incorrect": "Amplifiers increase the amplitude (strength) of a signal."}}}, {"id": "transistors-screen-4", "type": "lesson_screen", "order": 4, "estimatedTimeSeconds": 90, "content": {"headline": "Basic Types (Conceptual)", "body_md": "There are two main families of transistors:\n\n- **Bipolar Junction Transistors (BJTs):** Current-controlled devices (NPN and PNP types).\n- **Field-Effect Transistors (FETs):** Voltage-controlled devices (like MOSFETs).\n\nWe'll dive deeper into these in more advanced courses!", "visual": {"type": "local_asset", "value": "assets/images/course_specific/bjt_fet_symbols.png"}, "interactive_element": {"type": "button", "button_text": "Understood! Let's recap.", "action": "next_screen"}}}, {"id": "transistors-screen-5", "type": "lesson_screen", "order": 5, "estimatedTimeSeconds": 60, "content": {"headline": "Transistors: A Glimpse!", "body_md": "Great start! You've got the basic idea:\n\n- Transistors can act as electronic switches or amplifiers.\n- A small signal can control a larger current.\n- They are fundamental to modern electronics.\n\nThis is just a conceptual introduction; there's much more to learn!", "visual": {"type": "giphy_search", "value": "brain expanding electronics"}, "interactive_element": {"type": "button", "button_text": "Finish Lesson", "action": "complete_lesson"}}}]}, {"id": "module-test-component-commander", "title": "Module Test: Component Commander", "description": "Understand the characteristics and applications of fundamental electronic components.", "order": 6, "type": "module_test_interactive", "estimatedTimeMinutes": 30, "passingScorePercentage": 70, "contentBlocks": [{"id": "test-component-commander-q1", "type": "question_screen", "order": 1, "estimatedTimeSeconds": 60, "content": {"headline": "Question 1: <PERSON><PERSON>'s Law", "body_md": "A circuit has a 12V power supply and a resistor. If the current measured is 2 Amperes, what is the resistance of the resistor?", "visual": {"type": "static_text", "value": "V = I × R"}, "interactive_element": {"type": "multiple_choice_text", "question_text": "Calculate the resistance.", "options": [{"id": "opt1", "text": "24 Ohms"}, {"id": "opt2", "text": "6 Ohms"}, {"id": "opt3", "text": "0.167 Ohms"}, {"id": "opt4", "text": "12 Ohms"}], "correct_option_id": "opt2", "feedback_correct": "Correct! R = V / I = 12V / 2A = 6Ω.", "feedback_incorrect": "Remember <PERSON><PERSON>'s Law: V = I * R. You need to solve for R."}}}, {"id": "test-component-commander-q2", "type": "question_screen", "order": 2, "estimatedTimeSeconds": 75, "content": {"headline": "Question 2: Capacitor Function", "body_md": "What is the primary function of a capacitor in an electronic circuit?", "visual": {"type": "unsplash_search", "value": "capacitors electronics"}, "interactive_element": {"type": "multiple_choice_text", "question_text": "Choose the best description.", "options": [{"id": "opt1", "text": "To resist the flow of current"}, {"id": "opt2", "text": "To store electrical energy in an electric field"}, {"id": "opt3", "text": "To store energy in a magnetic field"}, {"id": "opt4", "text": "To allow current flow in only one direction"}], "correct_option_id": "opt2", "feedback_correct": "Excellent! Capacitors are all about storing energy in an electric field.", "feedback_incorrect": "Think about what makes a capacitor unique. Resistors resist, inductors use magnetic fields, diodes control direction."}}}, {"id": "test-component-commander-q3", "type": "question_screen", "order": 3, "estimatedTimeSeconds": 75, "content": {"headline": "Question 3: Inductor Behavior", "body_md": "How does an inductor react to a sudden attempt to change the current flowing through it?", "visual": {"type": "unsplash_search", "value": "electromagnet coil"}, "interactive_element": {"type": "multiple_choice_text", "question_text": "Select the correct behavior.", "options": [{"id": "opt1", "text": "It allows the change to happen instantly"}, {"id": "opt2", "text": "It generates a voltage that opposes the change"}, {"id": "opt3", "text": "It shorts the circuit"}, {"id": "opt4", "text": "Its resistance drops to zero"}], "correct_option_id": "opt2", "feedback_correct": "Spot on! Inductors resist changes in current by inducing an opposing voltage (back EMF).", "feedback_incorrect": "Inductors are known for their opposition to changes in current. How do they do this?"}}}, {"id": "test-component-commander-q4", "type": "question_screen", "order": 4, "estimatedTimeSeconds": 90, "content": {"headline": "Question 4: Diode Application", "body_md": "Which of the following is a primary application of a diode due to its one-way current characteristic?", "visual": {"type": "local_asset", "value": "assets/images/course_specific/diode_symbols_various.png"}, "interactive_element": {"type": "multiple_choice_text", "question_text": "Identify the main application.", "options": [{"id": "opt1", "text": "Amplifying signals"}, {"id": "opt2", "text": "Storing charge"}, {"id": "opt3", "text": "Converting AC to DC (Rectification)"}, {"id": "opt4", "text": "Varying resistance"}], "correct_option_id": "opt3", "feedback_correct": "Correct! Rectification is a key use of diodes.", "feedback_incorrect": "Consider what a one-way valve for current would be most useful for among the options."}}}, {"id": "test-component-commander-q5", "type": "question_screen", "order": 5, "estimatedTimeSeconds": 60, "content": {"headline": "Question 5: Transistor Role", "body_md": "A transistor can be used to control a large current between its collector and emitter (or drain and source) using a small current or voltage at its base (or gate). This describes its function as a...?", "visual": {"type": "unsplash_search", "value": "transistor on circuit board"}, "interactive_element": {"type": "multiple_choice_text", "question_text": "What function is this?", "options": [{"id": "opt1", "text": "Resistor"}, {"id": "opt2", "text": "Capacitor"}, {"id": "opt3", "text": "Switch or Amplifier"}, {"id": "opt4", "text": "<PERSON><PERSON>"}], "correct_option_id": "opt3", "feedback_correct": "Yes! This describes the fundamental operation of a transistor as both a switch and an amplifier.", "feedback_incorrect": "Think about which component is known for controlling a large current with a small signal."}}}]}]}