import 'dart:async';
import 'dart:math' as math;
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';

class InteractiveEnergyTransformationVisualizerWidget extends StatefulWidget {
  final Map<String, dynamic> data;
  final Function(bool)? onStateChanged;

  const InteractiveEnergyTransformationVisualizerWidget({
    Key? key,
    required this.data,
    this.onStateChanged,
  }) : super(key: key);

  @override
  State<InteractiveEnergyTransformationVisualizerWidget> createState() =>
      _InteractiveEnergyTransformationVisualizerWidgetState();
}

class _InteractiveEnergyTransformationVisualizerWidgetState
    extends State<InteractiveEnergyTransformationVisualizerWidget>
    with SingleTickerProviderStateMixin {
  // Animation controller
  late AnimationController _animationController;
  late Animation<double> _animation;

  // Simulation parameters
  String _currentScenario = 'Pendulum';
  bool _isSimulating = false;
  Timer? _simulationTimer;
  double _time = 0.0;
  double _timeStep = 0.016; // ~60fps

  // Energy values
  double _potentialEnergy = 100.0;
  double _kineticEnergy = 0.0;
  double _totalEnergy = 100.0;
  double _energyLoss = 0.0; // For scenarios with energy loss

  // Pendulum specific
  double _pendulumAngle = math.pi / 4; // Initial angle
  double _pendulumAngularVelocity = 0.0;
  double _pendulumLength = 1.0;
  double _pendulumMass = 1.0;
  double _pendulumDamping = 0.05;
  double _gravity = 9.8;
  List<Offset> _pendulumTrajectory = [];

  // Roller coaster specific
  double _rollerCoasterPosition = 0.0;
  double _rollerCoasterVelocity = 0.0;
  double _rollerCoasterMass = 1.0;
  double _rollerCoasterFriction = 0.05;
  List<Offset> _rollerCoasterTrajectory = [];

  // Spring specific
  double _springPosition = 0.0;
  double _springVelocity = 0.0;
  double _springMass = 1.0;
  double _springConstant = 10.0;
  double _springDamping = 0.1;
  List<Offset> _springTrajectory = [];

  // UI colors
  final Color _primaryColor = Colors.blue;
  final Color _secondaryColor = Colors.red;
  final Color _tertiaryColor = Colors.green;
  final Color _textColor = Colors.black87;
  final Color _backgroundColor = Colors.white;

  // Scenario options
  final List<String> _scenarioOptions = [
    'Pendulum',
    'Roller Coaster',
    'Spring',
    'Bouncing Ball',
  ];

  @override
  void initState() {
    super.initState();

    // Initialize animation controller
    _animationController = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 1000),
    );

    _animation = CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeInOut,
    );

    // Initialize trajectories
    _pendulumTrajectory = [];
    _rollerCoasterTrajectory = [];
    _springTrajectory = [];

    // Start with pendulum scenario
    _resetSimulation();
  }

  @override
  void dispose() {
    _animationController.dispose();
    _simulationTimer?.cancel();
    super.dispose();
  }

  void _resetSimulation() {
    _simulationTimer?.cancel();
    _isSimulating = false;
    _time = 0.0;

    setState(() {
      switch (_currentScenario) {
        case 'Pendulum':
          _pendulumAngle = math.pi / 4;
          _pendulumAngularVelocity = 0.0;
          _pendulumTrajectory = [];
          _potentialEnergy = _calculatePendulumPotentialEnergy();
          _kineticEnergy = _calculatePendulumKineticEnergy();
          _totalEnergy = _potentialEnergy + _kineticEnergy;
          break;
        case 'Roller Coaster':
          _rollerCoasterPosition = 0.0;
          _rollerCoasterVelocity = 0.0;
          _rollerCoasterTrajectory = [];
          _potentialEnergy = _calculateRollerCoasterPotentialEnergy();
          _kineticEnergy = _calculateRollerCoasterKineticEnergy();
          _totalEnergy = _potentialEnergy + _kineticEnergy;
          break;
        case 'Spring':
          _springPosition = -0.5; // Compressed spring
          _springVelocity = 0.0;
          _springTrajectory = [];
          _potentialEnergy = _calculateSpringPotentialEnergy();
          _kineticEnergy = _calculateSpringKineticEnergy();
          _totalEnergy = _potentialEnergy + _kineticEnergy;
          break;
        case 'Bouncing Ball':
          // TODO: Implement bouncing ball scenario
          break;
      }
    });
  }

  void _startSimulation() {
    if (_isSimulating) return;

    setState(() {
      _isSimulating = true;
    });

    _simulationTimer = Timer.periodic(Duration(milliseconds: (_timeStep * 1000).round()), (timer) {
      _updateSimulation();
    });
  }

  void _stopSimulation() {
    _simulationTimer?.cancel();
    setState(() {
      _isSimulating = false;
    });
  }

  void _updateSimulation() {
    setState(() {
      _time += _timeStep;

      switch (_currentScenario) {
        case 'Pendulum':
          _updatePendulum();
          break;
        case 'Roller Coaster':
          _updateRollerCoaster();
          break;
        case 'Spring':
          _updateSpring();
          break;
        case 'Bouncing Ball':
          // TODO: Implement bouncing ball update
          break;
      }
    });
  }

  // Pendulum physics
  void _updatePendulum() {
    // Calculate angular acceleration
    double angularAcceleration = -(_gravity / _pendulumLength) * math.sin(_pendulumAngle) - 
                                _pendulumDamping * _pendulumAngularVelocity;
    
    // Update angular velocity
    _pendulumAngularVelocity += angularAcceleration * _timeStep;
    
    // Update angle
    _pendulumAngle += _pendulumAngularVelocity * _timeStep;
    
    // Calculate energies
    _potentialEnergy = _calculatePendulumPotentialEnergy();
    _kineticEnergy = _calculatePendulumKineticEnergy();
    _energyLoss = _totalEnergy - (_potentialEnergy + _kineticEnergy);
    
    // Update trajectory
    if (_pendulumTrajectory.length > 100) {
      _pendulumTrajectory.removeAt(0);
    }
    
    double x = _pendulumLength * math.sin(_pendulumAngle);
    double y = _pendulumLength * math.cos(_pendulumAngle);
    _pendulumTrajectory.add(Offset(x, y));
  }

  double _calculatePendulumPotentialEnergy() {
    // U = mgh = mg(L - L*cos(θ))
    return _pendulumMass * _gravity * _pendulumLength * (1 - math.cos(_pendulumAngle));
  }

  double _calculatePendulumKineticEnergy() {
    // K = (1/2)mv² = (1/2)m(Lω)²
    return 0.5 * _pendulumMass * math.pow(_pendulumLength * _pendulumAngularVelocity, 2);
  }

  // Roller coaster physics
  void _updateRollerCoaster() {
    // TODO: Implement roller coaster physics
  }

  double _calculateRollerCoasterPotentialEnergy() {
    // TODO: Implement roller coaster potential energy calculation
    return 0.0;
  }

  double _calculateRollerCoasterKineticEnergy() {
    // TODO: Implement roller coaster kinetic energy calculation
    return 0.0;
  }

  // Spring physics
  void _updateSpring() {
    // TODO: Implement spring physics
  }

  double _calculateSpringPotentialEnergy() {
    // U = (1/2)kx²
    return 0.5 * _springConstant * math.pow(_springPosition, 2);
  }

  double _calculateSpringKineticEnergy() {
    // K = (1/2)mv²
    return 0.5 * _springMass * math.pow(_springVelocity, 2);
  }

  @override
  Widget build(BuildContext context) {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
      ),
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Energy Transformation Visualizer',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
                color: _textColor,
              ),
            ),
            const SizedBox(height: 16),
            
            // Scenario selector
            DropdownButton<String>(
              value: _currentScenario,
              onChanged: (String? newValue) {
                if (newValue != null && newValue != _currentScenario) {
                  setState(() {
                    _currentScenario = newValue;
                    _resetSimulation();
                  });
                }
              },
              items: _scenarioOptions.map<DropdownMenuItem<String>>((String value) {
                return DropdownMenuItem<String>(
                  value: value,
                  child: Text(value),
                );
              }).toList(),
            ),
            
            const SizedBox(height: 16),
            
            // Visualization area
            Container(
              height: 200,
              width: double.infinity,
              decoration: BoxDecoration(
                color: Colors.grey.withOpacity(0.1),
                borderRadius: BorderRadius.circular(8),
              ),
              child: _buildVisualization(),
            ),
            
            const SizedBox(height: 16),
            
            // Energy bar chart
            Container(
              height: 100,
              width: double.infinity,
              decoration: BoxDecoration(
                color: Colors.grey.withOpacity(0.1),
                borderRadius: BorderRadius.circular(8),
              ),
              child: CustomPaint(
                painter: EnergyBarChartPainter(
                  potentialEnergy: _potentialEnergy,
                  kineticEnergy: _kineticEnergy,
                  totalEnergy: _totalEnergy,
                  energyLoss: _energyLoss,
                  primaryColor: _primaryColor,
                  secondaryColor: _secondaryColor,
                  tertiaryColor: _tertiaryColor,
                ),
              ),
            ),
            
            const SizedBox(height: 16),
            
            // Control buttons
            Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                ElevatedButton(
                  onPressed: _isSimulating ? _stopSimulation : _startSimulation,
                  style: ElevatedButton.styleFrom(
                    backgroundColor: _isSimulating ? Colors.red : _primaryColor,
                  ),
                  child: Text(_isSimulating ? 'Stop' : 'Start'),
                ),
                const SizedBox(width: 16),
                ElevatedButton(
                  onPressed: _resetSimulation,
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Colors.grey,
                  ),
                  child: const Text('Reset'),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildVisualization() {
    switch (_currentScenario) {
      case 'Pendulum':
        return CustomPaint(
          painter: PendulumPainter(
            angle: _pendulumAngle,
            length: _pendulumLength,
            trajectory: _pendulumTrajectory,
            primaryColor: _primaryColor,
            secondaryColor: _secondaryColor,
            textColor: _textColor,
          ),
        );
      case 'Roller Coaster':
        return CustomPaint(
          painter: RollerCoasterPainter(
            position: _rollerCoasterPosition,
            velocity: _rollerCoasterVelocity,
            trajectory: _rollerCoasterTrajectory,
            primaryColor: _primaryColor,
            secondaryColor: _secondaryColor,
            textColor: _textColor,
          ),
        );
      case 'Spring':
        return CustomPaint(
          painter: SpringPainter(
            position: _springPosition,
            velocity: _springVelocity,
            trajectory: _springTrajectory,
            primaryColor: _primaryColor,
            secondaryColor: _secondaryColor,
            textColor: _textColor,
          ),
        );
      case 'Bouncing Ball':
        return const Center(
          child: Text('Bouncing Ball Visualization (Coming Soon)'),
        );
      default:
        return const Center(
          child: Text('Select a scenario'),
        );
    }
  }
}

// Painters for different scenarios
class PendulumPainter extends CustomPainter {
  final double angle;
  final double length;
  final List<Offset> trajectory;
  final Color primaryColor;
  final Color secondaryColor;
  final Color textColor;

  PendulumPainter({
    required this.angle,
    required this.length,
    required this.trajectory,
    required this.primaryColor,
    required this.secondaryColor,
    required this.textColor,
  });

  @override
  void paint(Canvas canvas, Size size) {
    final center = Offset(size.width / 2, size.height / 4);
    final scale = size.height / 3;
    
    // Draw pivot point
    final pivotPaint = Paint()
      ..color = Colors.black
      ..style = PaintingStyle.fill;
    canvas.drawCircle(center, 5, pivotPaint);
    
    // Calculate pendulum bob position
    final bobX = center.dx + math.sin(angle) * length * scale;
    final bobY = center.dy + math.cos(angle) * length * scale;
    final bobPosition = Offset(bobX, bobY);
    
    // Draw pendulum string
    final stringPaint = Paint()
      ..color = Colors.black
      ..strokeWidth = 2;
    canvas.drawLine(center, bobPosition, stringPaint);
    
    // Draw pendulum bob
    final bobPaint = Paint()
      ..color = primaryColor
      ..style = PaintingStyle.fill;
    canvas.drawCircle(bobPosition, 15, bobPaint);
    
    // Draw trajectory
    if (trajectory.isNotEmpty) {
      final trajectoryPaint = Paint()
        ..color = secondaryColor.withOpacity(0.5)
        ..strokeWidth = 1;
      
      final path = Path();
      
      // Transform trajectory points to canvas coordinates
      final firstPoint = Offset(
        center.dx + trajectory.first.dx * scale,
        center.dy + trajectory.first.dy * scale,
      );
      
      path.moveTo(firstPoint.dx, firstPoint.dy);
      
      for (int i = 1; i < trajectory.length; i++) {
        final point = Offset(
          center.dx + trajectory[i].dx * scale,
          center.dy + trajectory[i].dy * scale,
        );
        path.lineTo(point.dx, point.dy);
      }
      
      canvas.drawPath(path, trajectoryPaint);
    }
  }

  @override
  bool shouldRepaint(covariant PendulumPainter oldDelegate) {
    return angle != oldDelegate.angle || 
           trajectory.length != oldDelegate.trajectory.length;
  }
}

class RollerCoasterPainter extends CustomPainter {
  final double position;
  final double velocity;
  final List<Offset> trajectory;
  final Color primaryColor;
  final Color secondaryColor;
  final Color textColor;

  RollerCoasterPainter({
    required this.position,
    required this.velocity,
    required this.trajectory,
    required this.primaryColor,
    required this.secondaryColor,
    required this.textColor,
  });

  @override
  void paint(Canvas canvas, Size size) {
    // TODO: Implement roller coaster visualization
    final textSpan = TextSpan(
      text: 'Roller Coaster Visualization (Coming Soon)',
      style: TextStyle(color: textColor, fontSize: 14),
    );
    final textPainter = TextPainter(
      text: textSpan,
      textDirection: TextDirection.ltr,
    );
    textPainter.layout(minWidth: 0, maxWidth: size.width);
    textPainter.paint(canvas, Offset(size.width / 2 - textPainter.width / 2, size.height / 2));
  }

  @override
  bool shouldRepaint(covariant RollerCoasterPainter oldDelegate) {
    return position != oldDelegate.position || 
           velocity != oldDelegate.velocity;
  }
}

class SpringPainter extends CustomPainter {
  final double position;
  final double velocity;
  final List<Offset> trajectory;
  final Color primaryColor;
  final Color secondaryColor;
  final Color textColor;

  SpringPainter({
    required this.position,
    required this.velocity,
    required this.trajectory,
    required this.primaryColor,
    required this.secondaryColor,
    required this.textColor,
  });

  @override
  void paint(Canvas canvas, Size size) {
    // TODO: Implement spring visualization
    final textSpan = TextSpan(
      text: 'Spring Visualization (Coming Soon)',
      style: TextStyle(color: textColor, fontSize: 14),
    );
    final textPainter = TextPainter(
      text: textSpan,
      textDirection: TextDirection.ltr,
    );
    textPainter.layout(minWidth: 0, maxWidth: size.width);
    textPainter.paint(canvas, Offset(size.width / 2 - textPainter.width / 2, size.height / 2));
  }

  @override
  bool shouldRepaint(covariant SpringPainter oldDelegate) {
    return position != oldDelegate.position || 
           velocity != oldDelegate.velocity;
  }
}

class EnergyBarChartPainter extends CustomPainter {
  final double potentialEnergy;
  final double kineticEnergy;
  final double totalEnergy;
  final double energyLoss;
  final Color primaryColor;
  final Color secondaryColor;
  final Color tertiaryColor;

  EnergyBarChartPainter({
    required this.potentialEnergy,
    required this.kineticEnergy,
    required this.totalEnergy,
    required this.energyLoss,
    required this.primaryColor,
    required this.secondaryColor,
    required this.tertiaryColor,
  });

  @override
  void paint(Canvas canvas, Size size) {
    final barWidth = size.width / 4;
    final maxHeight = size.height * 0.8;
    final bottomY = size.height * 0.9;
    
    // Calculate bar heights
    final potentialBarHeight = (potentialEnergy / totalEnergy) * maxHeight;
    final kineticBarHeight = (kineticEnergy / totalEnergy) * maxHeight;
    final totalBarHeight = maxHeight;
    final currentTotalBarHeight = ((potentialEnergy + kineticEnergy) / totalEnergy) * maxHeight;
    
    // Draw potential energy bar
    final potentialPaint = Paint()
      ..color = primaryColor
      ..style = PaintingStyle.fill;
    canvas.drawRect(
      Rect.fromLTWH(barWidth * 0.5, bottomY - potentialBarHeight, barWidth * 0.8, potentialBarHeight),
      potentialPaint,
    );
    
    // Draw kinetic energy bar
    final kineticPaint = Paint()
      ..color = secondaryColor
      ..style = PaintingStyle.fill;
    canvas.drawRect(
      Rect.fromLTWH(barWidth * 1.5, bottomY - kineticBarHeight, barWidth * 0.8, kineticBarHeight),
      kineticPaint,
    );
    
    // Draw total energy bar (initial)
    final totalPaint = Paint()
      ..color = tertiaryColor.withOpacity(0.5)
      ..style = PaintingStyle.fill;
    canvas.drawRect(
      Rect.fromLTWH(barWidth * 2.5, bottomY - totalBarHeight, barWidth * 0.8, totalBarHeight),
      totalPaint,
    );
    
    // Draw current total energy bar
    final currentTotalPaint = Paint()
      ..color = tertiaryColor
      ..style = PaintingStyle.fill;
    canvas.drawRect(
      Rect.fromLTWH(barWidth * 2.5, bottomY - currentTotalBarHeight, barWidth * 0.8, currentTotalBarHeight),
      currentTotalPaint,
    );
    
    // Draw labels
    final textStyle = TextStyle(color: Colors.black, fontSize: 12);
    
    _drawCenteredText(canvas, 'Potential', Offset(barWidth * 0.9, bottomY + 10), textStyle);
    _drawCenteredText(canvas, 'Kinetic', Offset(barWidth * 1.9, bottomY + 10), textStyle);
    _drawCenteredText(canvas, 'Total', Offset(barWidth * 2.9, bottomY + 10), textStyle);
  }
  
  void _drawCenteredText(Canvas canvas, String text, Offset position, TextStyle style) {
    final textSpan = TextSpan(text: text, style: style);
    final textPainter = TextPainter(
      text: textSpan,
      textDirection: TextDirection.ltr,
    );
    textPainter.layout();
    textPainter.paint(canvas, Offset(position.dx - textPainter.width / 2, position.dy));
  }

  @override
  bool shouldRepaint(covariant EnergyBarChartPainter oldDelegate) {
    return potentialEnergy != oldDelegate.potentialEnergy || 
           kineticEnergy != oldDelegate.kineticEnergy;
  }
}
