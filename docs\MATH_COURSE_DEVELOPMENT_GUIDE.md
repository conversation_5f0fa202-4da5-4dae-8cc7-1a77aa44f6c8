# Resonance Mathematics Course Development Guide

## Overview
This guide provides comprehensive instructions for developing mathematics courses in the Resonance platform, with a focus on interactive elements, asset management, and content structure.

## Interactive Widget Implementation Priority

### Mathematical Thinking Course
| Widget Type | Status | Description | Priority |
|-------------|--------|-------------|----------|
| interactive_number_sequence | ❌ Not Implemented | Allows exploration of number sequences and patterns | High |
| interactive_pattern_gallery | ❌ Not Implemented | Visual pattern recognition and completion | High |
| interactive_logical_chain_constructor | ❌ Not Implemented | Building logical arguments step by step | High |
| interactive_fallacy_identification | ❌ Not Implemented | Identifying logical fallacies in arguments | Medium |
| interactive_logic_puzzle | ❌ Not Implemented | Interactive puzzles requiring logical thinking | Medium |
| truth_table_explorer | ❌ Not Implemented | Exploration of logical truth tables | Medium |

### Calculus Course
| Widget Type | Status | Description | Priority |
|-------------|--------|-------------|----------|
| interactive_function_plotter | ✅ Implemented | Plots mathematical functions with interactive controls | High |
| interactive_derivative_visualizer | ⚠️ Partial | Shows derivatives of functions | High |
| interactive_limit_explorer | ❌ Not Implemented | Visualizes limits of functions | High |
| interactive_integral_visualizer | ❌ Not Implemented | Visualizes integrals as areas | High |
| interactive_taylor_series | ❌ Not Implemented | Demonstrates Taylor series approximations | Medium |
| interactive_differential_equation_solver | ❌ Not Implemented | Solves and visualizes differential equations | Medium |

### Equations and Algebra Course
| Widget Type | Status | Description | Priority |
|-------------|--------|-------------|----------|
| interactive_step_by_step_equation_solver | ❌ Not Implemented | Solves equations step by step | High |
| interactive_number_base_converter | ❌ Not Implemented | Converts between different number bases | Medium |
| interactive_inequality_visualizer | ❌ Not Implemented | Visualizes inequalities on number lines | High |
| interactive_coordinate_plane_grapher | ✅ Implemented | Plots points and lines on coordinate plane | High |
| interactive_system_of_equations_solver | ❌ Not Implemented | Solves systems of equations | Medium |
| interactive_polynomial_factorizer | ❌ Not Implemented | Factors polynomials | Medium |

### Functions and Probability Course
| Widget Type | Status | Description | Priority |
|-------------|--------|-------------|----------|
| interactive_function_machine | ❌ Not Implemented | Input-output machine for functions | High |
| interactive_function_transformer | ❌ Not Implemented | Shows transformations of functions | High |
| interactive_probability_simulator | ❌ Not Implemented | Simulates probability experiments | High |
| interactive_distribution_explorer | ❌ Not Implemented | Explores probability distributions | Medium |
| interactive_conditional_probability_calculator | ❌ Not Implemented | Calculates conditional probabilities | Medium |
| interactive_expected_value_calculator | ❌ Not Implemented | Calculates expected values | Medium |

## Asset Management Strategy

### Required Asset Types
1. **Mathematical Diagrams**: SVG-based coordinate systems, geometric shapes, graphs
2. **Conceptual Illustrations**: Visual metaphors for mathematical concepts
3. **UI Elements**: Buttons, sliders, input fields styled for mathematical context
4. **Animated Demonstrations**: Replacing GIFs with programmatic animations

### AI-Generated Asset Implementation (REQUIRED)
We must implement a local AI image generation system to create:

1. **Conceptual Illustrations**: Visual representations of mathematical concepts
   - Example: Visualization of "function" as a machine with inputs and outputs
   - Example: Representation of "limit" as approaching a boundary

2. **Scenario-based Problem Images**: Visual contexts for word problems
   - Example: Geometric scenarios for calculus problems
   - Example: Visual representations of probability scenarios

3. **Pattern Recognition Images**: Visual patterns for sequence problems
   - Example: Geometric patterns that follow mathematical sequences
   - Example: Visual representations of recursive patterns

### Implementation Requirements for AI Image Generation
1. **Local Generation**: Must run on-device to avoid API dependencies
2. **Style Consistency**: Maintain consistent visual style across all generated images
3. **Mathematical Accuracy**: Ensure precise representation of mathematical concepts
4. **Fallback System**: Default to programmatically generated diagrams when precision is critical

### Asset Naming Conventions
- Format: `[course_id]_[module_id]_[concept]_[type].[extension]`
- Example: `calculus_limits_approaching_infinity_illustration.svg`

## Interactive Widget Development Guidelines

### Core Principles
1. **Educational Value**: Each widget must clearly demonstrate a specific mathematical concept
2. **Interactivity**: Allow users to manipulate variables and see immediate results
3. **Scaffolded Learning**: Provide guidance that can be progressively removed
4. **Visual Feedback**: Clear visual indication of mathematical relationships

### Implementation Process
1. Define educational objectives for the widget
2. Create wireframes showing key interactive elements
3. Implement core mathematical functionality
4. Add UI controls and visual feedback
5. Implement step-by-step guidance
6. Add challenge mode for testing understanding

### Testing Requirements
Each widget must be tested for:
1. Mathematical accuracy across input ranges
2. Edge cases (e.g., asymptotes, undefined values)
3. Usability with different input methods
4. Performance with complex calculations
5. Accessibility features

## Content Integration

### Widget Integration in Lessons
```json
{
  "type": "interactive_element",
  "interactive_type": "interactive_function_plotter",
  "data": {
    "initial_functions": ["x^2", "2*x+1"],
    "x_range": [-10, 10],
    "y_range": [-10, 10],
    "grid_step": 1,
    "allow_user_functions": true
  }
}
```

### Progressive Disclosure Pattern
Structure content to follow this pattern:
1. Concept introduction (text + static illustration)
2. Guided interactive exploration (widget with preset values)
3. Free exploration (widget with user-defined values)
4. Challenge (widget with goals to achieve)
5. Application (widget in context of a problem)

## Replacing GIFs with Interactive Elements

### Current GIF Usage Analysis
Many lessons currently use GIFs to demonstrate:
- Function transformations
- Limit behavior
- Derivative development
- Integral accumulation

### Replacement Strategy
1. Identify mathematical concept in each GIF
2. Map to appropriate interactive widget type
3. Implement widget with equivalent demonstration capability
4. Add ability for user to control the demonstration pace
5. Extend with user-modifiable parameters

## Documentation for Other AIs

When implementing a new interactive widget, document:

1. **Widget Purpose**: Clear statement of educational objective
2. **Required Features**: List of interactions and capabilities
3. **Mathematical Domain**: Concepts covered and their boundaries
4. **Asset Dependencies**: Required images, SVGs, or programmatically generated elements
5. **Integration Examples**: JSON snippets showing how to use in lessons

## Next Steps Priority List

1. Complete implementation of high-priority widgets for Mathematical Thinking
2. Develop local AI image generation system for conceptual illustrations
3. Replace GIFs in Calculus course with interactive elements
4. Create comprehensive test suite for mathematical accuracy
5. Document all implemented widgets for other AI developers
