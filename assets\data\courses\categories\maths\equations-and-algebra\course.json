{"id": "equations-and-algebra", "title": "Equations and Algebra", "description": "Master the fundamentals of algebraic expressions, equations, inequalities, and their relationships through interactive explorations and problem-solving.", "categoryId": "maths", "thumbnailPath": "assets/images/algebra_banner.svg", "difficulty": "<PERSON><PERSON><PERSON>", "modules": [{"id": "language-of-variables", "title": "The Language of Variables", "description": "Introduce the power of symbols to represent unknowns and build mathematical expressions.", "order": 1}, {"id": "solving-one-step-equations", "title": "Solving One-Step Equations", "description": "Master the fundamental techniques for isolating variables in simple equations.", "order": 2}, {"id": "tackling-two-step-equations", "title": "Tackling Two-Step Equations", "description": "Extend your solving skills to equations requiring multiple operations.", "order": 3}, {"id": "introduction-to-inequalities", "title": "Introduction to Inequalities", "description": "Explore mathematical statements that compare values rather than equating them.", "order": 4}, {"id": "exploring-algebraic-relationships", "title": "Exploring Algebraic Relationships", "description": "Discover how equations can represent connections between different quantities.", "order": 5}]}