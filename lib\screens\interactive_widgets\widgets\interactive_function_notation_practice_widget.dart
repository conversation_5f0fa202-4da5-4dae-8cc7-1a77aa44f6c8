import 'package:flutter/material.dart';
import 'dart:math' as math;

/// A widget that helps users practice function notation by evaluating functions at specific values
class InteractiveFunctionNotationPracticeWidget extends StatefulWidget {
  /// The data for this widget
  final Map<String, dynamic> data;

  /// Optional callback when the widget state changes
  final Function(bool isCompleted)? onStateChanged;

  const InteractiveFunctionNotationPracticeWidget({
    Key? key,
    required this.data,
    this.onStateChanged,
  }) : super(key: key);

  /// Factory constructor to create the widget from JSON data
  factory InteractiveFunctionNotationPracticeWidget.fromData(Map<String, dynamic> data) {
    return InteractiveFunctionNotationPracticeWidget(
      data: data,
    );
  }

  @override
  State<InteractiveFunctionNotationPracticeWidget> createState() => _InteractiveFunctionNotationPracticeWidgetState();
}

class _InteractiveFunctionNotationPracticeWidgetState extends State<InteractiveFunctionNotationPracticeWidget> with SingleTickerProviderStateMixin {
  // Current function index
  int _currentFunctionIndex = 0;

  // List of functions to practice
  late List<Map<String, dynamic>> _functions;

  // Current input value
  String _inputValue = '';

  // Current answer
  String _userAnswer = '';

  // Whether feedback is shown
  bool _showFeedback = false;

  // Whether the widget is completed
  bool _isCompleted = false;

  // Whether to show explanation
  bool _showExplanation = false;

  // Whether to show step-by-step solution
  bool _showSteps = false;

  // Colors
  late Color _primaryColor;
  late Color _secondaryColor;
  late Color _accentColor;
  late Color _backgroundColor;
  late Color _textColor;

  // Animation controller for transitions
  late AnimationController _animationController;
  late Animation<double> _animation;

  // Text editing controller for user input
  final TextEditingController _answerController = TextEditingController();

  // Focus node for the answer text field
  final FocusNode _answerFocusNode = FocusNode();

  // Error message
  String? _errorMessage;

  // Whether the answer is correct
  bool? _isCorrect;

  // Number of correct answers
  int _correctAnswers = 0;

  // Total number of attempts
  int _totalAttempts = 0;

  @override
  void initState() {
    super.initState();

    // Initialize colors
    _primaryColor = _parseColor(widget.data['primary_color']) ?? Colors.blue;
    _secondaryColor = _parseColor(widget.data['secondary_color']) ?? Colors.orange;
    _accentColor = _parseColor(widget.data['accent_color']) ?? Colors.green;
    _backgroundColor = _parseColor(widget.data['background_color']) ?? Colors.white;
    _textColor = _parseColor(widget.data['text_color']) ?? Colors.black87;

    // Initialize animation controller
    _animationController = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 500),
    );

    _animation = CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeInOut,
    );

    // Initialize functions
    _functions = widget.data['functions'] != null
        ? List<Map<String, dynamic>>.from(widget.data['functions'])
        : _getDefaultFunctions();

    // Set initial input value
    _generateNewInputValue();
  }

  @override
  void dispose() {
    _animationController.dispose();
    _answerController.dispose();
    _answerFocusNode.dispose();
    super.dispose();
  }

  // Parse color from hex string
  Color? _parseColor(String? hexString) {
    if (hexString == null) return null;

    hexString = hexString.replaceAll('#', '');
    if (hexString.length == 6) {
      hexString = 'FF$hexString';
    }

    return Color(int.parse(hexString, radix: 16));
  }

  // Get default functions if none provided
  List<Map<String, dynamic>> _getDefaultFunctions() {
    return [
      {
        'name': 'Linear Function',
        'notation': 'f(x) = 2x + 3',
        'expression': '2 * x + 3',
        'variable': 'x',
        'type': 'linear',
        'difficulty': 'easy',
        'explanation': 'To evaluate f(x) = 2x + 3, multiply the input value by 2 and then add 3.',
        'steps': [
          'Substitute the input value for x',
          'Multiply the input by 2',
          'Add 3 to the result',
        ],
      },
      {
        'name': 'Quadratic Function',
        'notation': 'g(x) = x² - 4',
        'expression': 'x * x - 4',
        'variable': 'x',
        'type': 'quadratic',
        'difficulty': 'medium',
        'explanation': 'To evaluate g(x) = x² - 4, square the input value and then subtract 4.',
        'steps': [
          'Substitute the input value for x',
          'Square the input value',
          'Subtract 4 from the result',
        ],
      },
      {
        'name': 'Absolute Value Function',
        'notation': 'h(x) = |x - 2|',
        'expression': '(x - 2).abs()',
        'variable': 'x',
        'type': 'absolute',
        'difficulty': 'medium',
        'explanation': 'To evaluate h(x) = |x - 2|, subtract 2 from the input value and then take the absolute value.',
        'steps': [
          'Substitute the input value for x',
          'Subtract 2 from the input value',
          'Take the absolute value of the result',
        ],
      },
      {
        'name': 'Composite Function',
        'notation': 'j(x) = (x + 1)² - 3',
        'expression': '(x + 1) * (x + 1) - 3',
        'variable': 'x',
        'type': 'composite',
        'difficulty': 'hard',
        'explanation': 'To evaluate j(x) = (x + 1)² - 3, add 1 to the input value, square the result, and then subtract 3.',
        'steps': [
          'Substitute the input value for x',
          'Add 1 to the input value',
          'Square the result',
          'Subtract 3 from the squared value',
        ],
      },
      {
        'name': 'Rational Function',
        'notation': 'k(x) = (2x + 1) / (x - 3)',
        'expression': '(2 * x + 1) / (x - 3)',
        'variable': 'x',
        'type': 'rational',
        'difficulty': 'hard',
        'explanation': 'To evaluate k(x) = (2x + 1) / (x - 3), multiply the input by 2 and add 1 for the numerator, subtract 3 from the input for the denominator, then divide.',
        'steps': [
          'Substitute the input value for x',
          'Calculate the numerator: 2x + 1',
          'Calculate the denominator: x - 3',
          'Divide the numerator by the denominator',
        ],
        'domain_restriction': 'x ≠ 3',
      },
    ];
  }

  // Generate a new random input value
  void _generateNewInputValue() {
    final random = math.Random();
    final function = _functions[_currentFunctionIndex];

    // Generate a random integer between -10 and 10
    int value = random.nextInt(21) - 10;

    // For rational functions, avoid domain restrictions
    if (function['type'] == 'rational' && function['domain_restriction'] != null) {
      // For k(x) = (2x + 1) / (x - 3), avoid x = 3
      if (function['domain_restriction'] == 'x ≠ 3' && value == 3) {
        value = 4; // Use 4 instead of 3
      }
    }

    setState(() {
      _inputValue = value.toString();
      _userAnswer = '';
      _answerController.text = '';
      _showFeedback = false;
      _showExplanation = false;
      _showSteps = false;
      _errorMessage = null;
      _isCorrect = null;
    });
  }

  // Evaluate the function with the given input value
  double _evaluateFunction(String expression, String variable, String inputValue) {
    // Parse the input value
    double x = double.parse(inputValue);

    // Evaluate the expression based on the function type
    switch (_functions[_currentFunctionIndex]['type']) {
      case 'linear':
        return 2 * x + 3;
      case 'quadratic':
        return x * x - 4;
      case 'absolute':
        return (x - 2).abs();
      case 'composite':
        return (x + 1) * (x + 1) - 3;
      case 'rational':
        if (x == 3) {
          throw Exception('Division by zero');
        }
        return (2 * x + 1) / (x - 3);
      default:
        return 0;
    }
  }

  // Check the user's answer
  void _checkAnswer() {
    if (_userAnswer.isEmpty) {
      setState(() {
        _errorMessage = 'Please enter an answer';
      });
      return;
    }

    try {
      // Parse the user's answer
      double userAnswerValue = double.parse(_userAnswer);

      // Get the current function
      final function = _functions[_currentFunctionIndex];

      // Evaluate the function
      double correctAnswer = _evaluateFunction(
        function['expression'],
        function['variable'],
        _inputValue,
      );

      // Check if the answer is correct (with a small tolerance for floating point errors)
      bool isCorrect = (userAnswerValue - correctAnswer).abs() < 0.001;

      setState(() {
        _isCorrect = isCorrect;
        _showFeedback = true;
        _totalAttempts++;

        if (isCorrect) {
          _correctAnswers++;

          // If this is the last function and the answer is correct, mark as completed
          if (_currentFunctionIndex == _functions.length - 1 && _correctAnswers >= 5) {
            _isCompleted = true;
          }
        }
      });

      // Notify parent of state change
      widget.onStateChanged?.call(_isCompleted);
    } catch (e) {
      setState(() {
        _errorMessage = 'Please enter a valid number';
      });
    }
  }

  // Move to the next function
  void _nextFunction() {
    if (_currentFunctionIndex < _functions.length - 1) {
      // Start animation
      _animationController.forward().then((_) {
        setState(() {
          _currentFunctionIndex++;
          _generateNewInputValue();
        });
        _animationController.reverse();
      });
    } else {
      // Reset to the first function if completed
      _animationController.forward().then((_) {
        setState(() {
          _currentFunctionIndex = 0;
          _generateNewInputValue();
        });
        _animationController.reverse();
      });
    }
  }

  // Try another example with the same function
  void _tryAnotherExample() {
    setState(() {
      _generateNewInputValue();
    });
  }

  // Toggle explanation visibility
  void _toggleExplanation() {
    setState(() {
      _showExplanation = !_showExplanation;
    });
  }

  // Toggle step-by-step solution visibility
  void _toggleSteps() {
    setState(() {
      _showSteps = !_showSteps;
    });
  }

  // Build function information
  Widget _buildFunctionInfo(Map<String, dynamic> function) {
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: _primaryColor.withOpacity(0.1),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: _primaryColor),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Function: ${function['name']}',
            style: TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.bold,
              color: _primaryColor,
            ),
          ),
          const SizedBox(height: 4),
          Text(
            'Notation: ${function['notation']}',
            style: TextStyle(
              fontSize: 16,
              color: _textColor,
            ),
          ),
          if (function['domain_restriction'] != null)
            Padding(
              padding: const EdgeInsets.only(top: 4),
              child: Text(
                'Domain Restriction: ${function['domain_restriction']}',
                style: TextStyle(
                  fontSize: 14,
                  color: _secondaryColor,
                  fontStyle: FontStyle.italic,
                ),
              ),
            ),
        ],
      ),
    );
  }

  // Build practice area
  Widget _buildPracticeArea(Map<String, dynamic> function) {
    final notation = function['notation'].split('=')[0].trim();

    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: Colors.grey[100],
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: Colors.grey[300]!),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Evaluate the function:',
            style: TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.bold,
              color: _textColor,
            ),
          ),
          const SizedBox(height: 12),
          Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Text(
                '$notation($_inputValue) = ',
                style: TextStyle(
                  fontSize: 20,
                  fontWeight: FontWeight.bold,
                  color: _primaryColor,
                ),
              ),
              SizedBox(
                width: 100,
                child: TextField(
                  controller: _answerController,
                  focusNode: _answerFocusNode,
                  keyboardType: TextInputType.numberWithOptions(decimal: true, signed: true),
                  decoration: InputDecoration(
                    hintText: 'Answer',
                    border: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(8),
                    ),
                    contentPadding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
                    errorText: _errorMessage,
                  ),
                  enabled: !_showFeedback,
                  onChanged: (value) {
                    setState(() {
                      _userAnswer = value;
                      _errorMessage = null;
                    });
                  },
                  onSubmitted: (_) {
                    if (!_showFeedback) {
                      _checkAnswer();
                    }
                  },
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),
          if (!_showFeedback)
            Center(
              child: ElevatedButton(
                onPressed: _checkAnswer,
                style: ElevatedButton.styleFrom(
                  backgroundColor: _primaryColor,
                  foregroundColor: Colors.white,
                  padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
                ),
                child: const Text('Check Answer'),
              ),
            ),
          const SizedBox(height: 8),
          Center(
            child: Text(
              'Score: $_correctAnswers / $_totalAttempts',
              style: TextStyle(
                fontSize: 14,
                color: _textColor,
              ),
            ),
          ),
        ],
      ),
    );
  }

  // Build feedback
  Widget _buildFeedback(Map<String, dynamic> function) {
    final notation = function['notation'].split('=')[0].trim();
    final correctAnswer = _evaluateFunction(
      function['expression'],
      function['variable'],
      _inputValue,
    );

    return Container(
      margin: const EdgeInsets.only(top: 16),
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: _isCorrect! ? _accentColor.withOpacity(0.1) : _secondaryColor.withOpacity(0.1),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: _isCorrect! ? _accentColor : _secondaryColor),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(
                _isCorrect! ? Icons.check_circle : Icons.cancel,
                color: _isCorrect! ? _accentColor : _secondaryColor,
              ),
              const SizedBox(width: 8),
              Text(
                _isCorrect! ? 'Correct!' : 'Incorrect!',
                style: TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.bold,
                  color: _isCorrect! ? _accentColor : _secondaryColor,
                ),
              ),
            ],
          ),
          const SizedBox(height: 8),
          Text(
            '$notation($_inputValue) = $correctAnswer',
            style: TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.bold,
              color: _textColor,
            ),
          ),
          if (_showExplanation)
            Padding(
              padding: const EdgeInsets.only(top: 8),
              child: Text(
                function['explanation'] as String,
                style: TextStyle(
                  fontSize: 14,
                  fontStyle: FontStyle.italic,
                  color: _textColor,
                ),
              ),
            ),
          if (_showSteps)
            Padding(
              padding: const EdgeInsets.only(top: 8),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'Step-by-step solution:',
                    style: TextStyle(
                      fontSize: 14,
                      fontWeight: FontWeight.bold,
                      color: _textColor,
                    ),
                  ),
                  const SizedBox(height: 4),
                  ...List<Widget>.from(
                    (function['steps'] as List).map(
                      (step) => Padding(
                        padding: const EdgeInsets.only(left: 16, top: 4),
                        child: Row(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              '• ',
                              style: TextStyle(
                                fontSize: 14,
                                color: _textColor,
                              ),
                            ),
                            Expanded(
                              child: Text(
                                step,
                                style: TextStyle(
                                  fontSize: 14,
                                  color: _textColor,
                                ),
                              ),
                            ),
                          ],
                        ),
                      ),
                    ),
                  ),
                ],
              ),
            ),
          const SizedBox(height: 12),
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceEvenly,
            children: [
              ElevatedButton(
                onPressed: _toggleExplanation,
                style: ElevatedButton.styleFrom(
                  backgroundColor: _primaryColor,
                  foregroundColor: Colors.white,
                ),
                child: Text(_showExplanation ? 'Hide Explanation' : 'Show Explanation'),
              ),
              ElevatedButton(
                onPressed: _toggleSteps,
                style: ElevatedButton.styleFrom(
                  backgroundColor: _primaryColor,
                  foregroundColor: Colors.white,
                ),
                child: Text(_showSteps ? 'Hide Steps' : 'Show Steps'),
              ),
            ],
          ),
          const SizedBox(height: 8),
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceEvenly,
            children: [
              ElevatedButton(
                onPressed: _tryAnotherExample,
                style: ElevatedButton.styleFrom(
                  backgroundColor: _accentColor,
                  foregroundColor: Colors.white,
                ),
                child: const Text('Try Another Example'),
              ),
              ElevatedButton(
                onPressed: _nextFunction,
                style: ElevatedButton.styleFrom(
                  backgroundColor: _secondaryColor,
                  foregroundColor: Colors.white,
                ),
                child: const Text('Next Function'),
              ),
            ],
          ),
        ],
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    final currentFunction = _functions[_currentFunctionIndex];

    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: _backgroundColor,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withOpacity(0.2),
            spreadRadius: 1,
            blurRadius: 3,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Title
          Text(
            widget.data['title'] ?? 'Function Notation Practice',
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
              color: _textColor,
            ),
          ),

          const SizedBox(height: 8),

          // Description
          if (widget.data['description'] != null)
            Text(
              widget.data['description'],
              style: TextStyle(
                fontSize: 14,
                color: _textColor.withOpacity(0.8),
              ),
            ),

          const SizedBox(height: 16),

          // Function information
          _buildFunctionInfo(currentFunction),

          const SizedBox(height: 16),

          // Practice area
          _buildPracticeArea(currentFunction),

          // Feedback
          if (_showFeedback)
            _buildFeedback(currentFunction),

          // Widget name tag
          if (widget.data['showNameTag'] ?? true)
            Padding(
              padding: const EdgeInsets.only(top: 8),
              child: Text(
                'InteractiveFunctionNotationPractice',
                style: TextStyle(
                  color: Colors.grey[600],
                  fontSize: 12,
                  fontStyle: FontStyle.italic,
                ),
              ),
            ),
        ],
      ),
    );
  }
}
