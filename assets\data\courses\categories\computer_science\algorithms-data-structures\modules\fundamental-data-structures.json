{"id": "fundamental-data-structures", "title": "Fundamental Data Structures", "description": "Explore arrays, linked lists, stacks, and queues as the building blocks for complex software.", "order": 1, "lessons": [{"id": "intro-arrays", "title": "Introduction to <PERSON><PERSON><PERSON>", "description": "Understand the structure, operations, and use cases of arrays.", "order": 1, "type": "interactive_lesson", "estimatedTimeMinutes": 10, "contentBlocks": [{"id": "arrays_screen1_what_is_array", "type": "lesson_screen", "order": 1, "estimatedTimeSeconds": 60, "content": {"headline": "Arrays: Ordered Collections", "body_md": "An array is a fundamental data structure that stores a collection of elements of the same type in a contiguous block of memory. Each element is identified by an index or a key.\n\nThink of a row of mailboxes, each with a number (index) and holding a letter (element).", "visual": {"type": "giphy_search", "value": "row of boxes"}, "interactive_element": {"type": "button", "button_text": "Key Properties?"}, "audio_narration_url": null}}, {"id": "arrays_screen2_properties", "type": "lesson_screen", "order": 2, "estimatedTimeSeconds": 90, "content": {"headline": "Key Properties of Arrays", "body_md": "*   **Fixed Size (often):** In many languages, arrays have a fixed size determined at creation.\n*   **Homogeneous Elements:** Typically store elements of the same data type.\n*   **Direct Access:** Elements can be accessed directly using their index (e.g., `myArray[3]`). This is very fast (O(1) time complexity).\n*   **Contiguous Memory:** Elements are stored next to each other in memory.\n\nWhich property allows for fast access to any element?", "visual": {"type": "unsplash_search", "value": "numbered list"}, "interactive_element": {"type": "multiple_choice_text", "question_text": "Allows fast access?", "options": [{"text": "Fixed Size", "is_correct": false, "feedback": "Fixed size relates to capacity, not directly to access speed of an individual element."}, {"text": "Direct Access via Index", "is_correct": true, "feedback": "Correct! Knowing the index allows the computer to calculate the memory location directly."}, {"text": "Homogeneous Elements", "is_correct": false, "feedback": "Storing same-type elements simplifies memory management but isn't the primary reason for fast access."}]}, "audio_narration_url": null}}, {"id": "arrays_screen3_operations", "type": "lesson_screen", "order": 3, "estimatedTimeSeconds": 75, "content": {"headline": "Common Array Operations", "body_md": "*   **Access:** Read an element at a given index.\n*   **Search:** Find an element in the array.\n*   **Insert:** Add an element (can be slow if array is full or requires shifting elements).\n*   **Delete:** Remove an element (can be slow if it requires shifting elements).\n*   **Traversal:** Visit each element in the array.\n\nConsider inserting an element at the beginning of a full array. Why might this be slow?", "visual": {"type": "giphy_search", "value": "gears working tools"}, "interactive_element": {"type": "text_input", "question_text": "Why is insertion at beginning slow?", "placeholder_text": "e.g., All elements must shift", "correct_answer_regex": ".+", "feedback_correct": "Exactly! All existing elements need to be shifted one position to the right to make space."}, "audio_narration_url": null}}, {"id": "arrays_screen4_use_cases", "type": "lesson_screen", "order": 4, "estimatedTimeSeconds": 60, "content": {"headline": "When to Use Arrays", "body_md": "Arrays are suitable when:\n\n*   You know the approximate number of elements beforehand.\n*   You need fast access to elements by their position.\n*   You are storing a collection of similar items.\n\nExamples: Storing scores for students in a class, representing a game board, pixels in an image.", "visual": {"type": "unsplash_search", "value": "grid pattern"}, "interactive_element": {"type": "button", "button_text": "Lesson Summary"}, "audio_narration_url": null}}, {"id": "arrays_screen5_summary", "type": "lesson_screen", "order": 5, "estimatedTimeSeconds": 60, "content": {"headline": "<PERSON><PERSON><PERSON>", "body_md": "We've learned:\n\n*   Arrays store ordered collections in contiguous memory.\n*   They offer fast indexed access (O(1)).\n*   Insertions/deletions can be slow due to element shifting.\n*   Best for fixed-size collections needing quick positional access.\n\nNext up: Linked Lists!", "visual": {"type": "giphy_search", "value": "checklist done"}, "interactive_element": {"type": "button", "button_text": "On to Linked Lists!"}, "audio_narration_url": null}}]}, {"id": "intro-linked-lists", "title": "Introduction to Linked Lists", "description": "Explore the dynamic nature of linked lists, their types, and operations.", "order": 2, "type": "interactive_lesson", "estimatedTimeMinutes": 12, "contentBlocks": [{"id": "ll_screen1_what_is_ll", "type": "lesson_screen", "order": 1, "estimatedTimeSeconds": 75, "content": {"headline": "Linked Lists: Chains of Data", "body_md": "A linked list is a linear data structure where elements are not stored at contiguous memory locations. Instead, elements are linked using pointers.\n\nEach element (called a **node**) contains:\n1.  The data itself.\n2.  A pointer (or link) to the next node in the sequence.\n\nThink of a treasure hunt where each clue (node) tells you where to find the next clue.", "visual": {"type": "giphy_search", "value": "chain links"}, "interactive_element": {"type": "button", "button_text": "How do they differ from Arrays?"}, "audio_narration_url": null}}, {"id": "ll_screen2_vs_arrays", "type": "lesson_screen", "order": 2, "estimatedTimeSeconds": 90, "content": {"headline": "Linked Lists vs. <PERSON><PERSON><PERSON>", "body_md": "*   **Memory:** Arrays need contiguous memory; linked lists don't.\n*   **Size:** Arrays are often fixed-size; linked lists are dynamic (can grow/shrink easily).\n*   **Access:** Arrays allow O(1) direct access by index; linked lists require O(n) sequential access (traverse from head).\n*   **Insertion/Deletion:** Can be O(1) in linked lists if pointer to previous node is known (vs. O(n) for arrays due to shifting).\n\nWhich is better for frequent insertions/deletions in the middle?", "visual": {"type": "unsplash_search", "value": "comparing two things"}, "interactive_element": {"type": "multiple_choice_text", "question_text": "Better for middle insertions/deletions?", "options": [{"text": "<PERSON><PERSON><PERSON>", "is_correct": false, "feedback": "Arrays require shifting elements, which is slow for middle operations."}, {"text": "Linked Lists", "is_correct": true, "feedback": "Correct! Linked lists excel here by just updating pointers."}]}, "audio_narration_url": null}}, {"id": "ll_screen3_types", "type": "lesson_screen", "order": 3, "estimatedTimeSeconds": 90, "content": {"headline": "Types of Linked Lists", "body_md": "*   **Singly Linked List:** Each node points only to the next node. Traversal is one-way.\n*   **Doubly Linked List:** Each node points to both the next AND previous nodes. Allows two-way traversal.\n*   **Circular Linked List:** The last node points back to the first node, forming a circle.\n\nWhat's an advantage of a doubly linked list over a singly linked list?", "visual": {"type": "giphy_search", "value": "arrows directions"}, "interactive_element": {"type": "text_input", "question_text": "Advantage of doubly linked list?", "placeholder_text": "e.g., Easier reverse traversal", "correct_answer_regex": ".+", "feedback_correct": "Exactly! Reverse traversal is easy, and deletion can be more efficient if you only have a pointer to the node to be deleted."}, "audio_narration_url": null}}, {"id": "ll_screen4_operations", "type": "lesson_screen", "order": 4, "estimatedTimeSeconds": 75, "content": {"headline": "Common Linked List Operations", "body_md": "*   **Traversal:** Visiting each node (usually starting from head).\n*   **Search:** Finding a node with specific data.\n*   **Insertion:** Adding a new node (at beginning, end, or middle).\n*   **Deletion:** Removing a node.\n\nThe 'head' pointer is crucial as it's the entry point to the list.", "visual": {"type": "unsplash_search", "value": "connecting dots"}, "interactive_element": {"type": "button", "button_text": "Lesson Summary"}, "audio_narration_url": null}}, {"id": "ll_screen5_summary", "type": "lesson_screen", "order": 5, "estimatedTimeSeconds": 60, "content": {"headline": "Linked Lists Recap", "body_md": "We've learned:\n\n*   Linked lists are dynamic chains of nodes connected by pointers.\n*   They excel at insertions/deletions but have slower O(n) access.\n*   Types include singly, doubly, and circular linked lists.\n\nNext: Stacks and Queues!", "visual": {"type": "giphy_search", "value": "train connection"}, "interactive_element": {"type": "button", "button_text": "Stacks & Queues!"}, "audio_narration_url": null}}]}, {"id": "intro-stacks-queues", "title": "Stacks and Queues", "description": "Understand LIFO (Last-In, First-Out) and FIFO (First-In, First-Out) principles.", "order": 3, "type": "interactive_lesson", "estimatedTimeMinutes": 10, "contentBlocks": [{"id": "sq_screen1_abstract_data_types", "type": "lesson_screen", "order": 1, "estimatedTimeSeconds": 75, "content": {"headline": "Abstract Data Types (ADTs)", "body_md": "Stacks and Queues are often implemented using arrays or linked lists, but they are best understood as **Abstract Data Types (ADTs)**. An ADT defines a set of operations (a logical description) without specifying how these operations are implemented.\n\nThis means we focus on *what* they do, not *how* they do it internally.", "visual": {"type": "giphy_search", "value": "interface buttons"}, "interactive_element": {"type": "button", "button_text": "Tell me about Stacks"}, "audio_narration_url": null}}, {"id": "sq_screen2_stacks_lifo", "type": "lesson_screen", "order": 2, "estimatedTimeSeconds": 90, "content": {"headline": "Stacks: Last-In, First-Out (LIFO)", "body_md": "A stack follows the LIFO principle: the last element added is the first one to be removed.\n\nThink of a stack of plates: you add (push) a plate to the top, and you remove (pop) a plate from the top.\n\n**Common Operations:**\n*   `push(item)`: Add an item to the top.\n*   `pop()`: Remove and return the item from the top.\n*   `peek()` or `top()`: Return the top item without removing it.\n*   `isEmpty()`: Check if the stack is empty.", "visual": {"type": "unsplash_search", "value": "stack of books"}, "interactive_element": {"type": "multiple_choice_text", "question_text": "If you push A, then B, then C onto a stack, what's the first item popped?", "options": [{"text": "A", "is_correct": false, "feedback": "A was the first in, so it will be the last out."}, {"text": "B", "is_correct": false, "feedback": "B is in the middle."}, {"text": "C", "is_correct": true, "feedback": "Correct! C was the last one in, so it's the first one out (LIFO)."}]}, "audio_narration_url": null}}, {"id": "sq_screen3_stack_applications", "type": "lesson_screen", "order": 3, "estimatedTimeSeconds": 75, "content": {"headline": "Applications of Stacks", "body_md": "*   Function call management (call stack) in programming.\n*   Undo/Redo functionality in applications.\n*   Expression evaluation (e.g., converting infix to postfix).\n*   Backtracking in algorithms (e.g., maze solving).\n\nStacks are surprisingly versatile!", "visual": {"type": "giphy_search", "value": "undo button"}, "interactive_element": {"type": "button", "button_text": "Now for Queues!"}, "audio_narration_url": null}}, {"id": "sq_screen4_queues_fifo", "type": "lesson_screen", "order": 4, "estimatedTimeSeconds": 90, "content": {"headline": "Queues: First-In, First-Out (FIFO)", "body_md": "A queue follows the FIFO principle: the first element added is the first one to be removed.\n\nThink of a line at a checkout counter: the first person in line is the first person served.\n\n**Common Operations:**\n*   `enqueue(item)` or `add(item)`: Add an item to the rear (end) of the queue.\n*   `dequeue()` or `remove()`: Remove and return the item from the front of the queue.\n*   `peek()` or `front()`: Return the front item without removing it.\n*   `isEmpty()`: Check if the queue is empty.", "visual": {"type": "unsplash_search", "value": "people waiting in line"}, "interactive_element": {"type": "multiple_choice_text", "question_text": "If you enqueue X, then Y, then Z into a queue, what's the first item dequeued?", "options": [{"text": "X", "is_correct": true, "feedback": "Correct! X was the first one in, so it's the first one out (FIFO)."}, {"text": "Y", "is_correct": false, "feedback": "Y is in the middle."}, {"text": "Z", "is_correct": false, "feedback": "<PERSON> was the last one in, so it will be the last out."}]}, "audio_narration_url": null}}, {"id": "sq_screen5_queue_applications", "type": "lesson_screen", "order": 5, "estimatedTimeSeconds": 75, "content": {"headline": "Applications of Queues", "body_md": "*   Task scheduling in operating systems (e.g., print queues).\n*   Breadth-First Search (BFS) algorithm in graphs.\n*   Buffering data (e.g., streaming video).\n*   Handling requests on a server.\n\nQueues help manage order and fairness.", "visual": {"type": "giphy_search", "value": "printer printing documents"}, "interactive_element": {"type": "button", "button_text": "Lesson Summary"}, "audio_narration_url": null}}, {"id": "sq_screen6_summary", "type": "lesson_screen", "order": 6, "estimatedTimeSeconds": 60, "content": {"headline": "Stacks & Queues Recap", "body_md": "We've learned:\n\n*   Stacks are LIFO (Last-In, First-Out) - like a stack of plates.\n*   Queues are FIFO (First-In, First-Out) - like a waiting line.\n*   Both are ADTs with specific operations and many real-world applications.\n\nThese are essential tools in a programmer's toolkit!", "visual": {"type": "giphy_search", "value": "tools working"}, "interactive_element": {"type": "button", "button_text": "Module Test Time!"}, "audio_narration_url": null}}]}], "moduleTest": {"id": "fundamental-data-structures-test", "title": "Module Test: Fundamental Data Structures", "description": "Test your knowledge of arrays, linked lists, stacks, and queues.", "type": "module_test_interactive", "estimatedTimeMinutes": 8, "contentBlocks": [{"id": "fds_test_q1_array_access", "type": "question_screen", "order": 1, "estimatedTimeSeconds": 60, "content": {"headline": "Question 1: Array Access", "body_md": "What is the typical time complexity for accessing an element in an array by its index?", "visual": {"type": "giphy_search", "value": "fast speed"}, "interactive_element": {"type": "multiple_choice_text", "question_text": "Array access time complexity?", "options": [{"text": "O(1) - Constant Time", "is_correct": true, "feedback": "Correct! Direct indexed access is very fast."}, {"text": "O(log n) - Logarithmic Time", "is_correct": false, "feedback": "Logarithmic time is typical for structures like binary search trees."}, {"text": "O(n) - Linear Time", "is_correct": false, "feedback": "Linear time would mean scanning part of the array, which isn't needed for direct index access."}]}, "audio_narration_url": null}}, {"id": "fds_test_q2_linkedlist_advantage", "type": "question_screen", "order": 2, "estimatedTimeSeconds": 75, "content": {"headline": "Question 2: Linked List Advantage", "body_md": "Compared to arrays, what is a primary advantage of linked lists?", "visual": {"type": "unsplash_search", "value": "flexible chain"}, "interactive_element": {"type": "multiple_choice_text", "question_text": "Primary advantage of linked lists?", "options": [{"text": "Faster access to elements by index.", "is_correct": false, "feedback": "Arrays have faster indexed access (O(1)) than linked lists (O(n))."}, {"text": "More efficient use of cache memory.", "is_correct": false, "feedback": "Arrays often have better cache locality due to contiguous memory."}, {"text": "Dynamic size and efficient insertions/deletions.", "is_correct": true, "feedback": "Correct! Linked lists can easily grow or shrink, and insertions/deletions (especially in the middle) are generally faster than in arrays."}]}, "audio_narration_url": null}}, {"id": "fds_test_q3_stack_principle", "type": "question_screen", "order": 3, "estimatedTimeSeconds": 75, "content": {"headline": "Question 3: <PERSON><PERSON>", "body_md": "A stack data structure operates on which principle?", "visual": {"type": "giphy_search", "value": "stack of items"}, "interactive_element": {"type": "multiple_choice_text", "question_text": "Stack operating principle?", "options": [{"text": "FIFO (First-In, First-Out)", "is_correct": false, "feedback": "FIFO describes queues."}, {"text": "LIFO (Last-In, First-Out)", "is_correct": true, "feedback": "Correct! The last item pushed onto a stack is the first one popped off."}, {"text": "Random Access", "is_correct": false, "feedback": "Random access is a characteristic of arrays, not the defining principle of stacks."}]}, "audio_narration_url": null}}, {"id": "fds_test_q4_queue_application", "type": "question_screen", "order": 4, "estimatedTimeSeconds": 60, "content": {"headline": "Question 4: Queue Application", "body_md": "Which of the following is a common application of a queue data structure?", "visual": {"type": "unsplash_search", "value": "people in line"}, "interactive_element": {"type": "multiple_choice_text", "question_text": "Common queue application?", "options": [{"text": "Managing function calls in a program.", "is_correct": false, "feedback": "Function calls are typically managed using a stack (the call stack)."}, {"text": "Implementing an Undo feature.", "is_correct": false, "feedback": "Undo features often use a stack to keep track of previous states."}, {"text": "Handling print jobs in a printer spooler.", "is_correct": true, "feedback": "Correct! Print jobs are usually processed in the order they are received (FIFO), which is ideal for a queue."}]}, "audio_narration_url": null}}]}}