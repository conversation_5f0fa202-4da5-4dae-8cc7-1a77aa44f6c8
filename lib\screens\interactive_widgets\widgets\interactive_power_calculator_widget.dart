import 'package:flutter/material.dart';
import 'dart:math' as math;

class InteractivePowerCalculatorWidget extends StatefulWidget {
  final Map<String, dynamic>? data;

  const InteractivePowerCalculatorWidget({
    super.key,
    this.data,
  });

  factory InteractivePowerCalculatorWidget.fromData(
      Map<String, dynamic> data) {
    return InteractivePowerCalculatorWidget(
      data: data,
    );
  }

  @override
  State<InteractivePowerCalculatorWidget> createState() =>
      _InteractivePowerCalculatorWidgetState();
}

class _InteractivePowerCalculatorWidgetState
    extends State<InteractivePowerCalculatorWidget>
    with SingleTickerProviderStateMixin {
  // UI parameters
  late Color _primaryColor;
  late Color _secondaryColor;
  late Color _tertiaryColor;
  late Color _textColor;
  late Color _backgroundColor;

  // Power calculation parameters
  String _calculationMode = 'work_time'; // work_time, force_velocity, energy_time

  // Work and time parameters
  double _work = 100.0; // Joules
  double _time = 10.0; // Seconds

  // Force and velocity parameters
  double _force = 20.0; // Newtons
  double _velocity = 5.0; // m/s

  // Energy and time parameters
  double _energy = 100.0; // Joules
  double _energyTime = 10.0; // Seconds

  // Result
  double _power = 0.0; // Watts

  // Sliders
  double _minWork = 0.0;
  double _maxWork = 1000.0;
  double _minTime = 0.1;
  double _maxTime = 60.0;
  double _minForce = 0.0;
  double _maxForce = 100.0;
  double _minVelocity = 0.0;
  double _maxVelocity = 20.0;
  double _minEnergy = 0.0;
  double _maxEnergy = 1000.0;
  double _minEnergyTime = 0.1;
  double _maxEnergyTime = 60.0;

  // Real-world examples
  final List<Map<String, dynamic>> _examples = [
    {
      'name': 'Human Walking',
      'power': 100, // Watts
      'description': 'Average power output of a person walking at moderate pace',
    },
    {
      'name': 'Human Running',
      'power': 500, // Watts
      'description': 'Average power output of a person running',
    },
    {
      'name': 'Cycling (Tour de France)',
      'power': 400, // Watts
      'description': 'Professional cyclist during a race',
    },
    {
      'name': 'Light Bulb',
      'power': 60, // Watts
      'description': 'Standard incandescent light bulb',
    },
    {
      'name': 'Microwave Oven',
      'power': 1000, // Watts
      'description': 'Standard kitchen microwave',
    },
    {
      'name': 'Car Engine',
      'power': 100000, // Watts (100 kW)
      'description': 'Average mid-size car engine',
    },
    {
      'name': 'Wind Turbine',
      'power': 2000000, // Watts (2 MW)
      'description': 'Modern utility-scale wind turbine',
    },
  ];

  // Animation controller
  late AnimationController _animationController;
  late Animation<double> _animation;

  @override
  void initState() {
    super.initState();
    _initializeFromData();
    _animationController = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 500),
    );
    _animation = CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeInOut,
    );
    _animationController.forward();
    _calculatePower();
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  void _initializeFromData() {
    final data = widget.data;
    if (data != null) {
      _primaryColor = Color(data['primary_color'] ?? 0xFF2196F3);
      _secondaryColor = Color(data['secondary_color'] ?? 0xFFFFA000);
      _tertiaryColor = Color(data['tertiary_color'] ?? 0xFF4CAF50);
      _textColor = Color(data['text_color'] ?? 0xFF333333);
      _backgroundColor = Color(data['background_color'] ?? 0xFFF5F5F5);

      if (data['calculation_mode'] != null) {
        _calculationMode = data['calculation_mode'];
      }

      if (data['work'] != null) {
        _work = data['work'].toDouble();
      }

      if (data['time'] != null) {
        _time = data['time'].toDouble();
      }

      if (data['force'] != null) {
        _force = data['force'].toDouble();
      }

      if (data['velocity'] != null) {
        _velocity = data['velocity'].toDouble();
      }

      if (data['energy'] != null) {
        _energy = data['energy'].toDouble();
      }

      if (data['energy_time'] != null) {
        _energyTime = data['energy_time'].toDouble();
      }
    } else {
      _primaryColor = Colors.blue;
      _secondaryColor = Colors.orange;
      _tertiaryColor = Colors.green;
      _textColor = Colors.black87;
      _backgroundColor = Colors.grey.shade100;
    }
  }

  void _calculatePower() {
    setState(() {
      switch (_calculationMode) {
        case 'work_time':
          _power = _work / _time;
          break;
        case 'force_velocity':
          _power = _force * _velocity;
          break;
        case 'energy_time':
          _power = _energy / _energyTime;
          break;
      }
    });
  }

  String _formatPower(double power) {
    if (power >= 1000000) {
      return '${(power / 1000000).toStringAsFixed(2)} MW';
    } else if (power >= 1000) {
      return '${(power / 1000).toStringAsFixed(2)} kW';
    } else {
      return '${power.toStringAsFixed(2)} W';
    }
  }

  String _getPowerComparison() {
    // Find the closest example to the current power
    Map<String, dynamic>? closestExample;
    double smallestDifference = double.infinity;

    for (var example in _examples) {
      double difference = (example['power'] - _power).abs();
      if (difference < smallestDifference) {
        smallestDifference = difference;
        closestExample = example;
      }
    }

    if (closestExample != null) {
      double ratio = _power / closestExample['power'];
      String comparison;

      if (ratio > 1.1) {
        comparison = '${ratio.toStringAsFixed(1)} times more than';
      } else if (ratio < 0.9) {
        comparison = '${(1 / ratio).toStringAsFixed(1)} times less than';
      } else {
        comparison = 'about the same as';
      }

      return '$comparison ${closestExample['name']} (${_formatPower(closestExample['power'].toDouble())})';
    }

    return '';
  }

  Widget _buildInputParameters() {
    switch (_calculationMode) {
      case 'work_time':
        return Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Work input
            Row(
              children: [
                Text(
                  'Work:',
                  style: TextStyle(
                    fontSize: 14,
                    fontWeight: FontWeight.bold,
                    color: _textColor,
                  ),
                ),
                const SizedBox(width: 8),
                Text(
                  '${_work.toStringAsFixed(1)} J',
                  style: TextStyle(
                    fontSize: 14,
                    color: _primaryColor,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
            Slider(
              value: _work,
              min: _minWork,
              max: _maxWork,
              divisions: 100,
              label: _work.toStringAsFixed(1),
              onChanged: (double value) {
                setState(() {
                  _work = value;
                  _calculatePower();
                });
              },
              activeColor: _primaryColor,
              inactiveColor: _primaryColor.withOpacity(0.2),
            ),

            // Time input
            Row(
              children: [
                Text(
                  'Time:',
                  style: TextStyle(
                    fontSize: 14,
                    fontWeight: FontWeight.bold,
                    color: _textColor,
                  ),
                ),
                const SizedBox(width: 8),
                Text(
                  '${_time.toStringAsFixed(1)} s',
                  style: TextStyle(
                    fontSize: 14,
                    color: _primaryColor,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
            Slider(
              value: _time,
              min: _minTime,
              max: _maxTime,
              divisions: 100,
              label: _time.toStringAsFixed(1),
              onChanged: (double value) {
                setState(() {
                  _time = value;
                  _calculatePower();
                });
              },
              activeColor: _primaryColor,
              inactiveColor: _primaryColor.withOpacity(0.2),
            ),

            // Formula
            Container(
              padding: const EdgeInsets.all(8),
              decoration: BoxDecoration(
                color: _secondaryColor.withOpacity(0.1),
                borderRadius: BorderRadius.circular(4),
              ),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Text(
                    'P = W / t = ${_work.toStringAsFixed(1)} J / ${_time.toStringAsFixed(1)} s = ${_power.toStringAsFixed(2)} W',
                    style: TextStyle(
                      fontSize: 14,
                      fontWeight: FontWeight.bold,
                      color: _secondaryColor,
                    ),
                  ),
                ],
              ),
            ),
          ],
        );

      case 'force_velocity':
        return Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Force input
            Row(
              children: [
                Text(
                  'Force:',
                  style: TextStyle(
                    fontSize: 14,
                    fontWeight: FontWeight.bold,
                    color: _textColor,
                  ),
                ),
                const SizedBox(width: 8),
                Text(
                  '${_force.toStringAsFixed(1)} N',
                  style: TextStyle(
                    fontSize: 14,
                    color: _primaryColor,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
            Slider(
              value: _force,
              min: _minForce,
              max: _maxForce,
              divisions: 100,
              label: _force.toStringAsFixed(1),
              onChanged: (double value) {
                setState(() {
                  _force = value;
                  _calculatePower();
                });
              },
              activeColor: _primaryColor,
              inactiveColor: _primaryColor.withOpacity(0.2),
            ),

            // Velocity input
            Row(
              children: [
                Text(
                  'Velocity:',
                  style: TextStyle(
                    fontSize: 14,
                    fontWeight: FontWeight.bold,
                    color: _textColor,
                  ),
                ),
                const SizedBox(width: 8),
                Text(
                  '${_velocity.toStringAsFixed(1)} m/s',
                  style: TextStyle(
                    fontSize: 14,
                    color: _primaryColor,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
            Slider(
              value: _velocity,
              min: _minVelocity,
              max: _maxVelocity,
              divisions: 100,
              label: _velocity.toStringAsFixed(1),
              onChanged: (double value) {
                setState(() {
                  _velocity = value;
                  _calculatePower();
                });
              },
              activeColor: _primaryColor,
              inactiveColor: _primaryColor.withOpacity(0.2),
            ),

            // Formula
            Container(
              padding: const EdgeInsets.all(8),
              decoration: BoxDecoration(
                color: _secondaryColor.withOpacity(0.1),
                borderRadius: BorderRadius.circular(4),
              ),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Text(
                    'P = F × v = ${_force.toStringAsFixed(1)} N × ${_velocity.toStringAsFixed(1)} m/s = ${_power.toStringAsFixed(2)} W',
                    style: TextStyle(
                      fontSize: 14,
                      fontWeight: FontWeight.bold,
                      color: _secondaryColor,
                    ),
                  ),
                ],
              ),
            ),
          ],
        );

      case 'energy_time':
        return Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Energy input
            Row(
              children: [
                Text(
                  'Energy:',
                  style: TextStyle(
                    fontSize: 14,
                    fontWeight: FontWeight.bold,
                    color: _textColor,
                  ),
                ),
                const SizedBox(width: 8),
                Text(
                  '${_energy.toStringAsFixed(1)} J',
                  style: TextStyle(
                    fontSize: 14,
                    color: _primaryColor,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
            Slider(
              value: _energy,
              min: _minEnergy,
              max: _maxEnergy,
              divisions: 100,
              label: _energy.toStringAsFixed(1),
              onChanged: (double value) {
                setState(() {
                  _energy = value;
                  _calculatePower();
                });
              },
              activeColor: _primaryColor,
              inactiveColor: _primaryColor.withOpacity(0.2),
            ),

            // Time input
            Row(
              children: [
                Text(
                  'Time:',
                  style: TextStyle(
                    fontSize: 14,
                    fontWeight: FontWeight.bold,
                    color: _textColor,
                  ),
                ),
                const SizedBox(width: 8),
                Text(
                  '${_energyTime.toStringAsFixed(1)} s',
                  style: TextStyle(
                    fontSize: 14,
                    color: _primaryColor,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
            Slider(
              value: _energyTime,
              min: _minEnergyTime,
              max: _maxEnergyTime,
              divisions: 100,
              label: _energyTime.toStringAsFixed(1),
              onChanged: (double value) {
                setState(() {
                  _energyTime = value;
                  _calculatePower();
                });
              },
              activeColor: _primaryColor,
              inactiveColor: _primaryColor.withOpacity(0.2),
            ),

            // Formula
            Container(
              padding: const EdgeInsets.all(8),
              decoration: BoxDecoration(
                color: _secondaryColor.withOpacity(0.1),
                borderRadius: BorderRadius.circular(4),
              ),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Text(
                    'P = E / t = ${_energy.toStringAsFixed(1)} J / ${_energyTime.toStringAsFixed(1)} s = ${_power.toStringAsFixed(2)} W',
                    style: TextStyle(
                      fontSize: 14,
                      fontWeight: FontWeight.bold,
                      color: _secondaryColor,
                    ),
                  ),
                ],
              ),
            ),
          ],
        );

      default:
        return const SizedBox.shrink();
    }
  }

  @override
  Widget build(BuildContext context) {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
      ),
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Title and description
            Text(
              'Power Calculator',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
                color: _textColor,
              ),
            ),
            const SizedBox(height: 8),
            Text(
              'Calculate power using different physical quantities',
              style: TextStyle(
                fontSize: 14,
                color: _textColor.withOpacity(0.7),
              ),
            ),
            const SizedBox(height: 16),

            // Calculation mode selector
            Text(
              'Calculation Method:',
              style: TextStyle(
                fontSize: 14,
                fontWeight: FontWeight.bold,
                color: _textColor,
              ),
            ),
            const SizedBox(height: 8),
            SegmentedButton<String>(
              segments: const [
                ButtonSegment(
                  value: 'work_time',
                  label: Text('Work/Time'),
                  icon: Icon(Icons.access_time),
                ),
                ButtonSegment(
                  value: 'force_velocity',
                  label: Text('Force×Velocity'),
                  icon: Icon(Icons.speed),
                ),
                ButtonSegment(
                  value: 'energy_time',
                  label: Text('Energy/Time'),
                  icon: Icon(Icons.bolt),
                ),
              ],
              selected: {_calculationMode},
              onSelectionChanged: (Set<String> selection) {
                setState(() {
                  _calculationMode = selection.first;
                  _calculatePower();
                });
              },
            ),
            const SizedBox(height: 16),

            // Input parameters based on calculation mode
            _buildInputParameters(),
            const SizedBox(height: 16),

            // Power result
            Container(
              width: double.infinity,
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: _primaryColor.withOpacity(0.1),
                borderRadius: BorderRadius.circular(8),
                border: Border.all(color: _primaryColor.withOpacity(0.3)),
              ),
              child: Column(
                children: [
                  Text(
                    'Power',
                    style: TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.bold,
                      color: _textColor,
                    ),
                  ),
                  const SizedBox(height: 8),
                  Text(
                    _formatPower(_power),
                    style: TextStyle(
                      fontSize: 24,
                      fontWeight: FontWeight.bold,
                      color: _primaryColor,
                    ),
                  ),
                  const SizedBox(height: 8),
                  Text(
                    _getPowerComparison(),
                    textAlign: TextAlign.center,
                    style: TextStyle(
                      fontSize: 14,
                      fontStyle: FontStyle.italic,
                      color: _textColor.withOpacity(0.7),
                    ),
                  ),
                ],
              ),
            ),
            const SizedBox(height: 16),

            // Power visualization
            Container(
              height: 100,
              width: double.infinity,
              decoration: BoxDecoration(
                color: Colors.grey.withOpacity(0.1),
                borderRadius: BorderRadius.circular(8),
              ),
              child: CustomPaint(
                painter: PowerVisualizationPainter(
                  power: _power,
                  examples: _examples,
                  primaryColor: _primaryColor,
                  secondaryColor: _secondaryColor,
                  textColor: _textColor,
                  animationValue: _animation.value,
                ),
              ),
            ),
            const SizedBox(height: 16),

            // Power explanation
            ExpansionTile(
              title: Text(
                'Understanding Power in Physics',
                style: TextStyle(
                  fontSize: 14,
                  fontWeight: FontWeight.bold,
                  color: _textColor,
                ),
              ),
              children: [
                Padding(
                  padding: const EdgeInsets.all(16.0),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'Power is the rate at which work is done or energy is transferred. The SI unit of power is the watt (W), which is equal to one joule per second (J/s).',
                        style: TextStyle(
                          fontSize: 14,
                          color: _textColor,
                        ),
                      ),
                      const SizedBox(height: 8),
                      Text(
                        'Power can be calculated using different formulas:',
                        style: TextStyle(
                          fontSize: 14,
                          fontWeight: FontWeight.bold,
                          color: _textColor,
                        ),
                      ),
                      const SizedBox(height: 4),
                      Text(
                        '• P = W/t (Work divided by time)',
                        style: TextStyle(
                          fontSize: 14,
                          color: _textColor,
                        ),
                      ),
                      Text(
                        '• P = F×v (Force multiplied by velocity)',
                        style: TextStyle(
                          fontSize: 14,
                          color: _textColor,
                        ),
                      ),
                      Text(
                        '• P = E/t (Energy divided by time)',
                        style: TextStyle(
                          fontSize: 14,
                          color: _textColor,
                        ),
                      ),
                      const SizedBox(height: 8),
                      Text(
                        'Power is an important concept in physics and engineering, as it helps us understand how quickly energy is being used or transferred in a system.',
                        style: TextStyle(
                          fontSize: 14,
                          color: _textColor,
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }
}

class PowerVisualizationPainter extends CustomPainter {
  final double power;
  final List<Map<String, dynamic>> examples;
  final Color primaryColor;
  final Color secondaryColor;
  final Color textColor;
  final double animationValue;

  PowerVisualizationPainter({
    required this.power,
    required this.examples,
    required this.primaryColor,
    required this.secondaryColor,
    required this.textColor,
    required this.animationValue,
  });

  @override
  void paint(Canvas canvas, Size size) {
    // Sort examples by power
    final sortedExamples = List<Map<String, dynamic>>.from(examples)
      ..sort((a, b) => (a['power'] as num).compareTo(b['power'] as num));

    // Find min and max power values for scaling
    double minPower = sortedExamples.first['power'].toDouble();
    double maxPower = sortedExamples.last['power'].toDouble();

    // Ensure our current power is within the range
    if (power < minPower) minPower = power;
    if (power > maxPower) maxPower = power;

    // Use logarithmic scale for better visualization
    minPower = math.log(minPower) / math.log(10);
    maxPower = math.log(maxPower) / math.log(10);
    double logPower = math.log(power) / math.log(10);

    // Draw scale
    final scalePaint = Paint()
      ..color = textColor.withOpacity(0.2)
      ..style = PaintingStyle.stroke
      ..strokeWidth = 1.0;

    // Draw horizontal line
    canvas.drawLine(
      Offset(20, size.height / 2),
      Offset(size.width - 20, size.height / 2),
      scalePaint,
    );

    // Draw tick marks for powers of 10
    final tickPaint = Paint()
      ..color = textColor.withOpacity(0.5)
      ..style = PaintingStyle.stroke
      ..strokeWidth = 1.0;

    final textStyle = TextStyle(
      color: textColor.withOpacity(0.7),
      fontSize: 10,
    );

    for (int i = minPower.floor(); i <= maxPower.ceil(); i++) {
      // Calculate x position
      double x = 20 + (i - minPower) / (maxPower - minPower) * (size.width - 40);

      // Draw tick
      canvas.drawLine(
        Offset(x, size.height / 2 - 5),
        Offset(x, size.height / 2 + 5),
        tickPaint,
      );

      // Draw label
      final powerValue = math.pow(10, i).toInt();
      final label = _formatPower(powerValue.toDouble());

      final textSpan = TextSpan(
        text: label,
        style: textStyle,
      );
      final textPainter = TextPainter(
        text: textSpan,
        textDirection: TextDirection.ltr,
      );
      textPainter.layout();
      textPainter.paint(
        canvas,
        Offset(x - textPainter.width / 2, size.height / 2 + 10),
      );
    }

    // Draw examples
    for (var example in sortedExamples) {
      double examplePower = example['power'].toDouble();
      double logExamplePower = math.log(examplePower) / math.log(10);

      // Calculate x position
      double x = 20 + (logExamplePower - minPower) / (maxPower - minPower) * (size.width - 40);

      // Draw dot
      final dotPaint = Paint()
        ..color = secondaryColor.withOpacity(0.7)
        ..style = PaintingStyle.fill;

      canvas.drawCircle(
        Offset(x, size.height / 2),
        3.0,
        dotPaint,
      );

      // Draw label for selected examples
      if (examplePower == 100 || examplePower == 1000 || examplePower == 100000 || examplePower == 2000000) {
        final exampleLabel = example['name'] as String;

        final textSpan = TextSpan(
          text: exampleLabel,
          style: TextStyle(
            color: secondaryColor,
            fontSize: 8,
            fontWeight: FontWeight.bold,
          ),
        );
        final textPainter = TextPainter(
          text: textSpan,
          textDirection: TextDirection.ltr,
        );
        textPainter.layout();

        // Alternate between top and bottom placement to avoid overlap
        double y = (examplePower == 100 || examplePower == 100000)
            ? size.height / 2 - 15
            : size.height / 2 + 25;

        textPainter.paint(
          canvas,
          Offset(x - textPainter.width / 2, y),
        );
      }
    }

    // Draw current power
    double x = 20 + (logPower - minPower) / (maxPower - minPower) * (size.width - 40);

    // Draw animated dot
    final currentPaint = Paint()
      ..color = primaryColor
      ..style = PaintingStyle.fill;

    canvas.drawCircle(
      Offset(x, size.height / 2),
      5.0 * animationValue,
      currentPaint,
    );

    // Draw ripple effect
    final ripplePaint = Paint()
      ..color = primaryColor.withOpacity(0.3 * (1 - animationValue))
      ..style = PaintingStyle.stroke
      ..strokeWidth = 2.0;

    canvas.drawCircle(
      Offset(x, size.height / 2),
      10.0 * animationValue,
      ripplePaint,
    );

    // Draw current power label
    final powerLabel = _formatPower(power);

    final textSpan = TextSpan(
      text: powerLabel,
      style: TextStyle(
        color: primaryColor,
        fontSize: 12,
        fontWeight: FontWeight.bold,
      ),
    );
    final textPainter = TextPainter(
      text: textSpan,
      textDirection: TextDirection.ltr,
    );
    textPainter.layout();
    textPainter.paint(
      canvas,
      Offset(x - textPainter.width / 2, size.height / 2 - 25),
    );
  }

  String _formatPower(double power) {
    if (power >= 1000000) {
      return '${(power / 1000000).toStringAsFixed(2)} MW';
    } else if (power >= 1000) {
      return '${(power / 1000).toStringAsFixed(2)} kW';
    } else {
      return '${power.toStringAsFixed(2)} W';
    }
  }

  @override
  bool shouldRepaint(covariant PowerVisualizationPainter oldDelegate) {
    return oldDelegate.power != power ||
        oldDelegate.animationValue != animationValue;
  }
}
