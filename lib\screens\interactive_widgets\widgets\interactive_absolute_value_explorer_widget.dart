import 'package:flutter/material.dart';
import 'dart:math' as math;

/// A widget that allows users to explore absolute value functions and their properties
class InteractiveAbsoluteValueExplorerWidget extends StatefulWidget {
  /// The data for this widget
  final Map<String, dynamic> data;

  /// Optional callback when the widget state changes
  final Function(bool isCompleted)? onStateChanged;

  const InteractiveAbsoluteValueExplorerWidget({
    Key? key,
    required this.data,
    this.onStateChanged,
  }) : super(key: key);

  /// Factory constructor to create the widget from JSON data
  factory InteractiveAbsoluteValueExplorerWidget.fromData(Map<String, dynamic> data) {
    return InteractiveAbsoluteValueExplorerWidget(
      data: data,
    );
  }

  @override
  State<InteractiveAbsoluteValueExplorerWidget> createState() => _InteractiveAbsoluteValueExplorerWidgetState();
}

class _InteractiveAbsoluteValueExplorerWidgetState extends State<InteractiveAbsoluteValueExplorerWidget> {
  // Parameters for the absolute value function: f(x) = a|x - h| + k
  double _a = 1.0; // Vertical stretch/compression
  double _h = 0.0; // Horizontal shift
  double _k = 0.0; // Vertical shift
  
  // Graph display parameters
  double _minX = -10.0;
  double _maxX = 10.0;
  double _minY = -10.0;
  double _maxY = 10.0;
  
  // Selected points for exploration
  double _selectedX = 2.0;
  
  // Challenge mode parameters
  bool _challengeMode = false;
  Map<String, dynamic>? _currentChallenge;
  bool _challengeCompleted = false;
  
  // Colors
  Color _primaryColor = Colors.blue;
  Color _secondaryColor = Colors.orange;
  Color _accentColor = Colors.green;
  Color _textColor = Colors.black87;
  
  @override
  void initState() {
    super.initState();
    _initializeFromData();
  }
  
  void _initializeFromData() {
    // Initialize parameters from widget data
    _a = widget.data['initialA']?.toDouble() ?? 1.0;
    _h = widget.data['initialH']?.toDouble() ?? 0.0;
    _k = widget.data['initialK']?.toDouble() ?? 0.0;
    
    _minX = widget.data['minX']?.toDouble() ?? -10.0;
    _maxX = widget.data['maxX']?.toDouble() ?? 10.0;
    _minY = widget.data['minY']?.toDouble() ?? -10.0;
    _maxY = widget.data['maxY']?.toDouble() ?? 10.0;
    
    _selectedX = widget.data['initialSelectedX']?.toDouble() ?? 2.0;
    
    _challengeMode = widget.data['challengeMode'] ?? false;
    if (_challengeMode && widget.data['challenges'] != null) {
      _currentChallenge = widget.data['challenges'][0];
    }
    
    // Initialize colors
    if (widget.data['primaryColor'] != null) {
      _primaryColor = _colorFromHex(widget.data['primaryColor']);
    }
    if (widget.data['secondaryColor'] != null) {
      _secondaryColor = _colorFromHex(widget.data['secondaryColor']);
    }
    if (widget.data['accentColor'] != null) {
      _accentColor = _colorFromHex(widget.data['accentColor']);
    }
    if (widget.data['textColor'] != null) {
      _textColor = _colorFromHex(widget.data['textColor']);
    }
  }
  
  // Helper method to convert hex color string to Color
  Color _colorFromHex(String hexColor) {
    hexColor = hexColor.replaceAll('#', '');
    if (hexColor.length == 6) {
      hexColor = 'FF' + hexColor;
    }
    return Color(int.parse(hexColor, radix: 16));
  }
  
  // Calculate the absolute value function value for a given x
  double _calculateFunction(double x) {
    return _a * (x - _h).abs() + _k;
  }
  
  // Check if the current parameters match the challenge target
  bool _checkChallengeCompleted() {
    if (!_challengeMode || _currentChallenge == null) return false;
    
    final targetA = _currentChallenge!['targetA']?.toDouble() ?? 1.0;
    final targetH = _currentChallenge!['targetH']?.toDouble() ?? 0.0;
    final targetK = _currentChallenge!['targetK']?.toDouble() ?? 0.0;
    
    // Allow for small floating-point differences
    const epsilon = 0.1;
    return ((_a - targetA).abs() < epsilon) &&
           ((_h - targetH).abs() < epsilon) &&
           ((_k - targetK).abs() < epsilon);
  }
  
  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withOpacity(0.2),
            spreadRadius: 1,
            blurRadius: 3,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Title
          Text(
            widget.data['title'] ?? 'Absolute Value Explorer',
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
              color: _textColor,
            ),
          ),
          
          const SizedBox(height: 8),
          
          // Description
          Text(
            widget.data['description'] ?? 'Explore the properties of absolute value functions by adjusting the parameters.',
            style: TextStyle(
              fontSize: 14,
              color: _textColor.withOpacity(0.8),
            ),
          ),
          
          const SizedBox(height: 16),
          
          // Function display
          Container(
            padding: const EdgeInsets.all(8),
            decoration: BoxDecoration(
              color: _primaryColor.withOpacity(0.1),
              borderRadius: BorderRadius.circular(8),
              border: Border.all(color: _primaryColor.withOpacity(0.3)),
            ),
            child: Text(
              'f(x) = $_a|x - $_h| + $_k',
              style: TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.bold,
                color: _primaryColor,
              ),
            ),
          ),
          
          const SizedBox(height: 16),
          
          // Graph area
          Container(
            height: 200,
            decoration: BoxDecoration(
              border: Border.all(color: Colors.grey.withOpacity(0.3)),
              borderRadius: BorderRadius.circular(8),
            ),
            child: ClipRRect(
              borderRadius: BorderRadius.circular(8),
              child: CustomPaint(
                size: const Size(double.infinity, 200),
                painter: AbsoluteValueGraphPainter(
                  a: _a,
                  h: _h,
                  k: _k,
                  minX: _minX,
                  maxX: _maxX,
                  minY: _minY,
                  maxY: _maxY,
                  selectedX: _selectedX,
                  primaryColor: _primaryColor,
                  secondaryColor: _secondaryColor,
                  accentColor: _accentColor,
                ),
              ),
            ),
          ),
          
          const SizedBox(height: 16),
          
          // Parameter sliders
          _buildParameterSlider(
            label: 'a (Vertical Stretch/Compression)',
            value: _a,
            min: -5.0,
            max: 5.0,
            onChanged: (value) {
              setState(() {
                _a = value;
                if (_challengeMode) {
                  _challengeCompleted = _checkChallengeCompleted();
                  if (_challengeCompleted && widget.onStateChanged != null) {
                    widget.onStateChanged!(true);
                  }
                }
              });
            },
          ),
          
          _buildParameterSlider(
            label: 'h (Horizontal Shift)',
            value: _h,
            min: -5.0,
            max: 5.0,
            onChanged: (value) {
              setState(() {
                _h = value;
                if (_challengeMode) {
                  _challengeCompleted = _checkChallengeCompleted();
                  if (_challengeCompleted && widget.onStateChanged != null) {
                    widget.onStateChanged!(true);
                  }
                }
              });
            },
          ),
          
          _buildParameterSlider(
            label: 'k (Vertical Shift)',
            value: _k,
            min: -5.0,
            max: 5.0,
            onChanged: (value) {
              setState(() {
                _k = value;
                if (_challengeMode) {
                  _challengeCompleted = _checkChallengeCompleted();
                  if (_challengeCompleted && widget.onStateChanged != null) {
                    widget.onStateChanged!(true);
                  }
                }
              });
            },
          ),
          
          // X value slider for exploration
          _buildParameterSlider(
            label: 'Explore at x = $_selectedX',
            value: _selectedX,
            min: _minX,
            max: _maxX,
            onChanged: (value) {
              setState(() {
                _selectedX = value;
              });
            },
          ),
          
          const SizedBox(height: 16),
          
          // Value at selected point
          Container(
            padding: const EdgeInsets.all(8),
            decoration: BoxDecoration(
              color: _secondaryColor.withOpacity(0.1),
              borderRadius: BorderRadius.circular(8),
              border: Border.all(color: _secondaryColor.withOpacity(0.3)),
            ),
            child: Text(
              'f($_selectedX) = ${_calculateFunction(_selectedX).toStringAsFixed(2)}',
              style: TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.bold,
                color: _secondaryColor,
              ),
            ),
          ),
          
          // Challenge feedback (if in challenge mode)
          if (_challengeMode && _currentChallenge != null)
            Container(
              margin: const EdgeInsets.only(top: 16),
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: _challengeCompleted
                    ? Colors.green.withOpacity(0.1)
                    : Colors.orange.withOpacity(0.1),
                borderRadius: BorderRadius.circular(8),
                border: Border.all(
                  color: _challengeCompleted
                      ? Colors.green.withOpacity(0.3)
                      : Colors.orange.withOpacity(0.3),
                ),
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    _currentChallenge!['description'] ?? 'Adjust the parameters to match the target function.',
                    style: TextStyle(
                      fontSize: 14,
                      color: _textColor,
                    ),
                  ),
                  const SizedBox(height: 8),
                  Text(
                    _challengeCompleted
                        ? (_currentChallenge!['successMessage'] ?? 'Great job! You matched the target function.')
                        : (_currentChallenge!['hint'] ?? 'Keep adjusting the parameters to match the target.'),
                    style: TextStyle(
                      fontSize: 14,
                      fontWeight: FontWeight.bold,
                      color: _challengeCompleted ? Colors.green : Colors.orange,
                    ),
                  ),
                ],
              ),
            ),
          
          // Widget name tag at the bottom
          if (widget.data['showNameTag'] ?? true)
            Padding(
              padding: const EdgeInsets.only(top: 8),
              child: Text(
                'InteractiveAbsoluteValueExplorerWidget',
                style: TextStyle(
                  color: Colors.grey[600],
                  fontSize: 12,
                  fontStyle: FontStyle.italic,
                ),
              ),
            ),
        ],
      ),
    );
  }
  
  Widget _buildParameterSlider({
    required String label,
    required double value,
    required double min,
    required double max,
    required ValueChanged<double> onChanged,
  }) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          label,
          style: TextStyle(
            fontSize: 14,
            color: _textColor,
          ),
        ),
        Slider(
          value: value,
          min: min,
          max: max,
          divisions: 100,
          label: value.toStringAsFixed(1),
          onChanged: onChanged,
          activeColor: _primaryColor,
          inactiveColor: _primaryColor.withOpacity(0.3),
        ),
      ],
    );
  }
}

/// Custom painter for drawing the absolute value function graph
class AbsoluteValueGraphPainter extends CustomPainter {
  final double a;
  final double h;
  final double k;
  final double minX;
  final double maxX;
  final double minY;
  final double maxY;
  final double selectedX;
  final Color primaryColor;
  final Color secondaryColor;
  final Color accentColor;
  
  AbsoluteValueGraphPainter({
    required this.a,
    required this.h,
    required this.k,
    required this.minX,
    required this.maxX,
    required this.minY,
    required this.maxY,
    required this.selectedX,
    required this.primaryColor,
    required this.secondaryColor,
    required this.accentColor,
  });
  
  @override
  void paint(Canvas canvas, Size size) {
    final width = size.width;
    final height = size.height;
    
    // Set up coordinate transformation
    final scaleX = width / (maxX - minX);
    final scaleY = height / (maxY - minY);
    
    // Transform from math coordinates to canvas coordinates
    double transformX(double x) => (x - minX) * scaleX;
    double transformY(double y) => height - (y - minY) * scaleY;
    
    // Draw grid
    _drawGrid(canvas, size, transformX, transformY);
    
    // Draw axes
    _drawAxes(canvas, size, transformX, transformY);
    
    // Draw the absolute value function
    _drawFunction(canvas, size, transformX, transformY);
    
    // Draw the selected point
    _drawSelectedPoint(canvas, transformX, transformY);
  }
  
  void _drawGrid(Canvas canvas, Size size, Function transformX, Function transformY) {
    final gridPaint = Paint()
      ..color = Colors.grey.withOpacity(0.2)
      ..strokeWidth = 0.5;
    
    // Draw vertical grid lines
    for (double x = minX.ceil().toDouble(); x <= maxX; x += 1.0) {
      final startPoint = Offset(transformX(x), 0);
      final endPoint = Offset(transformX(x), size.height);
      canvas.drawLine(startPoint, endPoint, gridPaint);
    }
    
    // Draw horizontal grid lines
    for (double y = minY.ceil().toDouble(); y <= maxY; y += 1.0) {
      final startPoint = Offset(0, transformY(y));
      final endPoint = Offset(size.width, transformY(y));
      canvas.drawLine(startPoint, endPoint, gridPaint);
    }
  }
  
  void _drawAxes(Canvas canvas, Size size, Function transformX, Function transformY) {
    final axesPaint = Paint()
      ..color = Colors.black.withOpacity(0.5)
      ..strokeWidth = 1.0;
    
    // Draw x-axis if it's in the visible range
    if (minY <= 0 && 0 <= maxY) {
      final y0 = transformY(0);
      canvas.drawLine(
        Offset(0, y0),
        Offset(size.width, y0),
        axesPaint,
      );
    }
    
    // Draw y-axis if it's in the visible range
    if (minX <= 0 && 0 <= maxX) {
      final x0 = transformX(0);
      canvas.drawLine(
        Offset(x0, 0),
        Offset(x0, size.height),
        axesPaint,
      );
    }
  }
  
  void _drawFunction(Canvas canvas, Size size, Function transformX, Function transformY) {
    final functionPaint = Paint()
      ..color = primaryColor
      ..strokeWidth = 2.0
      ..style = PaintingStyle.stroke;
    
    final path = Path();
    bool pathStarted = false;
    
    // Draw the function with many small line segments
    for (double x = minX; x <= maxX; x += 0.1) {
      final y = a * (x - h).abs() + k;
      
      // Skip points outside the visible y range
      if (y < minY || y > maxY) {
        pathStarted = false;
        continue;
      }
      
      final point = Offset(transformX(x), transformY(y));
      
      if (!pathStarted) {
        path.moveTo(point.dx, point.dy);
        pathStarted = true;
      } else {
        path.lineTo(point.dx, point.dy);
      }
    }
    
    canvas.drawPath(path, functionPaint);
  }
  
  void _drawSelectedPoint(Canvas canvas, Function transformX, Function transformY) {
    // Calculate the y value at the selected x
    final y = a * (selectedX - h).abs() + k;
    
    // Only draw if the point is within the visible range
    if (minX <= selectedX && selectedX <= maxX && minY <= y && y <= maxY) {
      final pointPaint = Paint()
        ..color = secondaryColor
        ..strokeWidth = 2.0
        ..style = PaintingStyle.fill;
      
      final point = Offset(transformX(selectedX), transformY(y));
      
      // Draw a circle at the point
      canvas.drawCircle(point, 6.0, pointPaint);
      
      // Draw lines to the axes
      final linePaint = Paint()
        ..color = secondaryColor.withOpacity(0.5)
        ..strokeWidth = 1.0
        ..style = PaintingStyle.stroke;
      
      // Line to y-axis
      canvas.drawLine(
        point,
        Offset(transformX(0), point.dy),
        linePaint,
      );
      
      // Line to x-axis
      canvas.drawLine(
        point,
        Offset(point.dx, transformY(0)),
        linePaint,
      );
    }
  }
  
  @override
  bool shouldRepaint(AbsoluteValueGraphPainter oldDelegate) {
    return oldDelegate.a != a ||
           oldDelegate.h != h ||
           oldDelegate.k != k ||
           oldDelegate.selectedX != selectedX ||
           oldDelegate.minX != minX ||
           oldDelegate.maxX != maxX ||
           oldDelegate.minY != minY ||
           oldDelegate.maxY != maxY;
  }
}
