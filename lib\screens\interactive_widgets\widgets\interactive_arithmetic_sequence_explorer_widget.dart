import 'package:flutter/material.dart';
import 'dart:math' as math;

/// A widget that allows users to explore arithmetic sequences
class InteractiveArithmeticSequenceExplorerWidget extends StatefulWidget {
  /// The data for this widget
  final Map<String, dynamic> data;

  /// Optional callback when the widget state changes
  final Function(bool isCompleted)? onStateChanged;

  const InteractiveArithmeticSequenceExplorerWidget({
    Key? key,
    required this.data,
    this.onStateChanged,
  }) : super(key: key);

  /// Factory constructor to create the widget from JSON data
  factory InteractiveArithmeticSequenceExplorerWidget.fromData(Map<String, dynamic> data) {
    return InteractiveArithmeticSequenceExplorerWidget(
      data: data,
    );
  }

  @override
  State<InteractiveArithmeticSequenceExplorerWidget> createState() => _InteractiveArithmeticSequenceExplorerWidgetState();
}

class _InteractiveArithmeticSequenceExplorerWidgetState extends State<InteractiveArithmeticSequenceExplorerWidget> with SingleTickerProviderStateMixin {
  // Sequence parameters
  late double _firstTerm;
  late double _commonDifference;
  late int _numberOfTerms;
  
  // Sequence values
  late List<double> _sequenceValues;
  
  // Animation controller
  late AnimationController _animationController;
  late Animation<double> _animation;
  
  // Colors
  late Color _primaryColor;
  late Color _secondaryColor;
  late Color _tertiaryColor;
  late Color _backgroundColor;
  late Color _textColor;
  
  // Display options
  late bool _showFormula;
  late bool _showDifferences;
  late bool _showGraph;
  late bool _showTable;
  
  // Sliders
  late double _minFirstTerm;
  late double _maxFirstTerm;
  late double _minCommonDifference;
  late double _maxCommonDifference;
  
  // Animation state
  bool _isAnimating = false;

  @override
  void initState() {
    super.initState();
    
    // Initialize sequence parameters
    _firstTerm = widget.data['first_term']?.toDouble() ?? 1.0;
    _commonDifference = widget.data['common_difference']?.toDouble() ?? 2.0;
    _numberOfTerms = widget.data['number_of_terms'] ?? 10;
    
    // Initialize colors
    _primaryColor = _parseColor(widget.data['primary_color']) ?? Colors.blue;
    _secondaryColor = _parseColor(widget.data['secondary_color']) ?? Colors.orange;
    _tertiaryColor = _parseColor(widget.data['tertiary_color']) ?? Colors.green;
    _backgroundColor = _parseColor(widget.data['background_color']) ?? Colors.white;
    _textColor = _parseColor(widget.data['text_color']) ?? Colors.black87;
    
    // Initialize display options
    _showFormula = widget.data['show_formula'] ?? true;
    _showDifferences = widget.data['show_differences'] ?? true;
    _showGraph = widget.data['show_graph'] ?? true;
    _showTable = widget.data['show_table'] ?? true;
    
    // Initialize slider ranges
    _minFirstTerm = widget.data['min_first_term']?.toDouble() ?? -10.0;
    _maxFirstTerm = widget.data['max_first_term']?.toDouble() ?? 10.0;
    _minCommonDifference = widget.data['min_common_difference']?.toDouble() ?? -10.0;
    _maxCommonDifference = widget.data['max_common_difference']?.toDouble() ?? 10.0;
    
    // Calculate sequence values
    _calculateSequence();
    
    // Initialize animation controller
    _animationController = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 1500),
    );
    
    _animation = CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeInOut,
    );
    
    // Add listener to animation controller
    _animationController.addStatusListener((status) {
      if (status == AnimationStatus.completed) {
        setState(() {
          _isAnimating = false;
        });
      }
    });
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }
  
  // Parse color from hex string
  Color? _parseColor(String? hexString) {
    if (hexString == null) return null;
    
    hexString = hexString.replaceAll('#', '');
    if (hexString.length == 6) {
      hexString = 'FF$hexString';
    }
    
    try {
      return Color(int.parse(hexString, radix: 16));
    } catch (e) {
      // Log the error or handle it as appropriate
      // For now, return null to use the default color
      return null;
    }
  }
  
  // Calculate sequence values
  void _calculateSequence() {
    _sequenceValues = List.generate(
      _numberOfTerms,
      (index) => _firstTerm + index * _commonDifference,
    );
  }
  
  // Start animation
  void _startAnimation() {
    if (_isAnimating) return;
    
    setState(() {
      _isAnimating = true;
    });
    
    _animationController.reset();
    _animationController.forward();
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: _backgroundColor,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withOpacity(0.2),
            spreadRadius: 1,
            blurRadius: 3,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Title
          Text(
            widget.data['title'] ?? 'Arithmetic Sequence Explorer',
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
              color: _textColor,
            ),
          ),
          
          const SizedBox(height: 8),
          
          // Description
          if (widget.data['description'] != null)
            Text(
              widget.data['description'],
              style: TextStyle(
                fontSize: 14,
                color: _textColor.withOpacity(0.8),
              ),
            ),
          
          const SizedBox(height: 16),
          
          // Formula display
          if (_showFormula)
            Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: _primaryColor.withOpacity(0.1),
                borderRadius: BorderRadius.circular(8),
                border: Border.all(color: _primaryColor),
              ),
              child: Column(
                children: [
                  Text(
                    'Arithmetic Sequence Formula:',
                    style: TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.bold,
                      color: _textColor,
                    ),
                  ),
                  const SizedBox(height: 8),
                  Text(
                    'a_n = a₁ + (n - 1) × d',
                    style: TextStyle(
                      fontSize: 18,
                      fontWeight: FontWeight.bold,
                      color: _primaryColor,
                    ),
                  ),
                  const SizedBox(height: 8),
                  Text(
                    'Where a₁ = $_firstTerm and d = $_commonDifference',
                    style: TextStyle(
                      fontSize: 14,
                      color: _textColor.withOpacity(0.8),
                    ),
                  ),
                ],
              ),
            ),
          
          const SizedBox(height: 16),
          
          // Sliders for adjusting parameters
          Text(
            'First Term (a₁):',
            style: TextStyle(
              fontSize: 14,
              fontWeight: FontWeight.bold,
              color: _textColor,
            ),
          ),
          Row(
            children: [
              Expanded(
                child: Slider(
                  value: _firstTerm,
                  min: _minFirstTerm,
                  max: _maxFirstTerm,
                  divisions: (_maxFirstTerm - _minFirstTerm).toInt() * 2,
                  label: _firstTerm.toStringAsFixed(1),
                  activeColor: _primaryColor,
                  onChanged: (value) {
                    setState(() {
                      _firstTerm = value;
                      _calculateSequence();
                    });
                  },
                ),
              ),
              SizedBox(
                width: 50,
                child: Text(
                  _firstTerm.toStringAsFixed(1),
                  style: TextStyle(
                    color: _textColor,
                  ),
                ),
              ),
            ],
          ),
          
          Text(
            'Common Difference (d):',
            style: TextStyle(
              fontSize: 14,
              fontWeight: FontWeight.bold,
              color: _textColor,
            ),
          ),
          Row(
            children: [
              Expanded(
                child: Slider(
                  value: _commonDifference,
                  min: _minCommonDifference,
                  max: _maxCommonDifference,
                  divisions: (_maxCommonDifference - _minCommonDifference).toInt() * 2,
                  label: _commonDifference.toStringAsFixed(1),
                  activeColor: _secondaryColor,
                  onChanged: (value) {
                    setState(() {
                      _commonDifference = value;
                      _calculateSequence();
                    });
                  },
                ),
              ),
              SizedBox(
                width: 50,
                child: Text(
                  _commonDifference.toStringAsFixed(1),
                  style: TextStyle(
                    color: _textColor,
                  ),
                ),
              ),
            ],
          ),
          
          const SizedBox(height: 16),

          Text(
            'Number of Terms:',
            style: TextStyle(
              fontSize: 14,
              fontWeight: FontWeight.bold,
              color: _textColor,
            ),
          ),
          Row(
            children: [
              Expanded(
                child: Slider(
                  value: _numberOfTerms.toDouble(),
                  min: 2,
                  max: 20,
                  divisions: 18, // (20 - 2)
                  label: _numberOfTerms.toString(),
                  activeColor: _tertiaryColor,
                  onChanged: (value) {
                    setState(() {
                      _numberOfTerms = value.toInt();
                      _calculateSequence();
                    });
                  },
                ),
              ),
              SizedBox(
                width: 50,
                child: Text(
                  _numberOfTerms.toString(),
                  style: TextStyle(
                    color: _textColor,
                  ),
                ),
              ),
            ],
          ),
          
          const SizedBox(height: 16),
          
          // Sequence visualization
          if (_showGraph)
            Container(
              height: 200,
              decoration: BoxDecoration(
                color: Colors.grey.withOpacity(0.1),
                borderRadius: BorderRadius.circular(8),
              ),
              child: CustomPaint(
                painter: ArithmeticSequencePainter(
                  sequenceValues: _sequenceValues,
                  primaryColor: _primaryColor,
                  secondaryColor: _secondaryColor,
                  tertiaryColor: _tertiaryColor,
                  showDifferences: _showDifferences,
                  animationValue: _isAnimating ? _animation.value : 1.0,
                ),
                child: Container(),
              ),
            ),
          
          const SizedBox(height: 16),
          
          // Sequence table
          if (_showTable)
            Container(
              height: 150,
              decoration: BoxDecoration(
                color: Colors.grey.withOpacity(0.1),
                borderRadius: BorderRadius.circular(8),
              ),
              child: ListView(
                scrollDirection: Axis.horizontal,
                children: [
                  DataTable(
                    columns: [
                      DataColumn(
                        label: Text(
                          'n',
                          style: TextStyle(
                            fontWeight: FontWeight.bold,
                            color: _textColor,
                          ),
                        ),
                      ),
                      DataColumn(
                        label: Text(
                          'a_n',
                          style: TextStyle(
                            fontWeight: FontWeight.bold,
                            color: _textColor,
                          ),
                        ),
                      ),
                      if (_showDifferences)
                        DataColumn(
                          label: Text(
                            'Difference',
                            style: TextStyle(
                              fontWeight: FontWeight.bold,
                              color: _textColor,
                            ),
                          ),
                        ),
                    ],
                    rows: List.generate(
                      _numberOfTerms,
                      (index) => DataRow(
                        cells: [
                          DataCell(
                            Text(
                              (index + 1).toString(),
                              style: TextStyle(
                                color: _textColor,
                              ),
                            ),
                          ),
                          DataCell(
                            Text(
                              _sequenceValues[index].toStringAsFixed(1),
                              style: TextStyle(
                                color: _primaryColor,
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                          ),
                          if (_showDifferences)
                            DataCell(
                              index > 0
                                  ? Text(
                                      (_sequenceValues[index] - _sequenceValues[index - 1]).toStringAsFixed(1),
                                      style: TextStyle(
                                        color: _secondaryColor,
                                      ),
                                    )
                                  : const Text('-'),
                            ),
                        ],
                      ),
                    ),
                  ),
                ],
              ),
            ),
          
          const SizedBox(height: 16),
          
          // Animation button
          Center(
            child: ElevatedButton.icon(
              onPressed: _isAnimating ? null : _startAnimation,
              icon: const Icon(Icons.play_arrow),
              label: const Text('Animate Sequence'),
              style: ElevatedButton.styleFrom(
                backgroundColor: _primaryColor,
                padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
              ),
            ),
          ),
          
          // Widget name tag
          if (widget.data['showNameTag'] ?? true)
            Padding(
              padding: const EdgeInsets.only(top: 8),
              child: Text(
                'InteractiveArithmeticSequenceExplorer',
                style: TextStyle(
                  color: Colors.grey[600],
                  fontSize: 12,
                  fontStyle: FontStyle.italic,
                ),
              ),
            ),
        ],
      ),
    );
  }
}

/// Custom painter for arithmetic sequence visualization
class ArithmeticSequencePainter extends CustomPainter {
  final List<double> sequenceValues;
  final Color primaryColor;
  final Color secondaryColor;
  final Color tertiaryColor;
  final bool showDifferences;
  final double animationValue;
  
  ArithmeticSequencePainter({
    required this.sequenceValues,
    required this.primaryColor,
    required this.secondaryColor,
    required this.tertiaryColor,
    required this.showDifferences,
    required this.animationValue,
  });
  
  @override
  void paint(Canvas canvas, Size size) {
    // Calculate min and max values for scaling
    double minValue = sequenceValues.reduce(math.min);
    double maxValue = sequenceValues.reduce(math.max);
    
    // Add some padding
    minValue = minValue - (maxValue - minValue) * 0.1;
    maxValue = maxValue + (maxValue - minValue) * 0.1;
    
    // Ensure min and max are different
    if (minValue == maxValue) {
      minValue -= 1;
      maxValue += 1;
    }
    
    // Calculate visible terms based on animation
    int visibleTerms = (sequenceValues.length * animationValue).ceil();
    
    // Draw axes
    final axesPaint = Paint()
      ..color = Colors.grey
      ..style = PaintingStyle.stroke
      ..strokeWidth = 1;
    
    // X-axis
    canvas.drawLine(
      Offset(0, size.height / 2),
      Offset(size.width, size.height / 2),
      axesPaint,
    );
    
    // Y-axis
    canvas.drawLine(
      Offset(50, 0),
      Offset(50, size.height),
      axesPaint,
    );
    
    // Draw points and lines
    final pointPaint = Paint()
      ..color = primaryColor
      ..style = PaintingStyle.fill;
    
    final linePaint = Paint()
      ..color = primaryColor
      ..style = PaintingStyle.stroke
      ..strokeWidth = 2;
    
    final differencePaint = Paint()
      ..color = secondaryColor
      ..style = PaintingStyle.stroke
      ..strokeWidth = 1
      ..strokeCap = StrokeCap.round;
    
    // Calculate x and y spacing
    final xSpacing = (size.width - 60) / (sequenceValues.length - 1);
    
    // Draw points and lines
    final points = <Offset>[];
    
    for (int i = 0; i < visibleTerms; i++) {
      final x = 50 + i * xSpacing;
      final y = size.height / 2 - (sequenceValues[i] - minValue) / (maxValue - minValue) * (size.height - 40) + 20;
      
      points.add(Offset(x, y));
      
      // Draw point
      canvas.drawCircle(
        Offset(x, y),
        5,
        pointPaint,
      );
      
      // Draw term number
      final textSpan = TextSpan(
        text: (i + 1).toString(),
        style: TextStyle(
          color: Colors.grey[600],
          fontSize: 12,
        ),
      );
      
      final textPainter = TextPainter(
        text: textSpan,
        textDirection: TextDirection.ltr,
      );
      
      textPainter.layout();
      textPainter.paint(
        canvas,
        Offset(x - textPainter.width / 2, size.height / 2 + 10),
      );
      
      // Draw term value
      final valueSpan = TextSpan(
        text: sequenceValues[i].toStringAsFixed(1),
        style: TextStyle(
          color: primaryColor,
          fontSize: 12,
          fontWeight: FontWeight.bold,
        ),
      );
      
      final valuePainter = TextPainter(
        text: valueSpan,
        textDirection: TextDirection.ltr,
      );
      
      valuePainter.layout();
      valuePainter.paint(
        canvas,
        Offset(x - valuePainter.width / 2, y - 20),
      );
    }
    
    // Draw lines connecting points
    if (points.length > 1) {
      final path = Path();
      path.moveTo(points[0].dx, points[0].dy);
      
      for (int i = 1; i < points.length; i++) {
        path.lineTo(points[i].dx, points[i].dy);
      }
      
      canvas.drawPath(path, linePaint);
    }
    
    // Draw differences
    if (showDifferences && points.length > 1) {
      for (int i = 0; i < points.length - 1; i++) {
        final startPoint = points[i];
        final endPoint = points[i + 1];
        
        // Draw difference arrow
        final midX = (startPoint.dx + endPoint.dx) / 2;
        final midY = (startPoint.dy + endPoint.dy) / 2;
        
        canvas.drawLine(
          Offset(startPoint.dx, startPoint.dy + 15),
          Offset(endPoint.dx, startPoint.dy + 15),
          differencePaint,
        );
        
        // Draw difference value
        final diffValue = sequenceValues[i + 1] - sequenceValues[i];
        final diffSpan = TextSpan(
          text: diffValue.toStringAsFixed(1),
          style: TextStyle(
            color: secondaryColor,
            fontSize: 12,
            fontWeight: FontWeight.bold,
          ),
        );
        
        final diffPainter = TextPainter(
          text: diffSpan,
          textDirection: TextDirection.ltr,
        );
        
        diffPainter.layout();
        diffPainter.paint(
          canvas,
          Offset(midX - diffPainter.width / 2, startPoint.dy + 25),
        );
      }
    }
  }
  
  @override
  bool shouldRepaint(covariant ArithmeticSequencePainter oldDelegate) {
    return oldDelegate.sequenceValues != sequenceValues ||
           oldDelegate.animationValue != animationValue ||
           oldDelegate.showDifferences != showDifferences;
  }
}
