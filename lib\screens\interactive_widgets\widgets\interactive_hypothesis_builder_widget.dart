import 'package:flutter/material.dart';

/// A widget that helps users build and test scientific hypotheses
/// Users can create hypotheses, identify variables, and evaluate testability
class InteractiveHypothesisBuilderWidget extends StatefulWidget {
  /// The data for this widget
  final Map<String, dynamic> data;

  /// Optional callback when the widget state changes
  final Function(bool isCompleted)? onStateChanged;

  const InteractiveHypothesisBuilderWidget({
    Key? key,
    required this.data,
    this.onStateChanged,
  }) : super(key: key);

  /// Factory constructor to create the widget from JSON data
  factory InteractiveHypothesisBuilderWidget.fromData(Map<String, dynamic> data) {
    return InteractiveHypothesisBuilderWidget(
      data: data,
    );
  }

  @override
  State<InteractiveHypothesisBuilderWidget> createState() => _InteractiveHypothesisBuilderWidgetState();
}

class _InteractiveHypothesisBuilderWidgetState extends State<InteractiveHypothesisBuilderWidget> {
  // Hypothesis components
  late List<String> _ifPhrases;
  late List<String> _thenPhrases;
  late List<String> _becausePhrases;
  late List<String> _variables;
  late List<String> _controlledVariables;
  
  // Selected components
  late String _selectedIf;
  late String _selectedThen;
  late String _selectedBecause;
  late List<String> _selectedVariables;
  late List<String> _selectedControlledVariables;
  
  // Custom input
  late TextEditingController _customIfController;
  late TextEditingController _customThenController;
  late TextEditingController _customBecauseController;
  late TextEditingController _customVariableController;
  late TextEditingController _customControlledVariableController;
  
  // UI state
  late int _currentStep;
  late bool _isCompleted;
  late bool _showFeedback;
  late bool _isHypothesisValid;
  late String _feedbackMessage;
  
  // Colors
  late Color _primaryColor;
  late Color _secondaryColor;
  late Color _accentColor;
  late Color _backgroundColor;
  late Color _textColor;

  @override
  void initState() {
    super.initState();
    
    // Initialize colors
    _primaryColor = _getColorFromHex(widget.data['primaryColor'] ?? '#2196F3');
    _secondaryColor = _getColorFromHex(widget.data['secondaryColor'] ?? '#FF9800');
    _accentColor = _getColorFromHex(widget.data['accentColor'] ?? '#4CAF50');
    _backgroundColor = _getColorFromHex(widget.data['backgroundColor'] ?? '#FFFFFF');
    _textColor = _getColorFromHex(widget.data['textColor'] ?? '#212121');
    
    // Initialize hypothesis components
    _ifPhrases = List<String>.from(widget.data['ifPhrases'] ?? _getDefaultIfPhrases());
    _thenPhrases = List<String>.from(widget.data['thenPhrases'] ?? _getDefaultThenPhrases());
    _becausePhrases = List<String>.from(widget.data['becausePhrases'] ?? _getDefaultBecausePhrases());
    _variables = List<String>.from(widget.data['variables'] ?? _getDefaultVariables());
    _controlledVariables = List<String>.from(widget.data['controlledVariables'] ?? _getDefaultControlledVariables());
    
    // Initialize selected components
    _selectedIf = '';
    _selectedThen = '';
    _selectedBecause = '';
    _selectedVariables = [];
    _selectedControlledVariables = [];
    
    // Initialize text controllers
    _customIfController = TextEditingController();
    _customThenController = TextEditingController();
    _customBecauseController = TextEditingController();
    _customVariableController = TextEditingController();
    _customControlledVariableController = TextEditingController();
    
    // Initialize UI state
    _currentStep = 0;
    _isCompleted = false;
    _showFeedback = false;
    _isHypothesisValid = false;
    _feedbackMessage = '';
  }

  @override
  void dispose() {
    _customIfController.dispose();
    _customThenController.dispose();
    _customBecauseController.dispose();
    _customVariableController.dispose();
    _customControlledVariableController.dispose();
    super.dispose();
  }

  // Get default "if" phrases
  List<String> _getDefaultIfPhrases() {
    return [
      'If plants receive more sunlight',
      'If temperature increases',
      'If students study for longer periods',
      'If water is heated to 100°C',
      'If exercise frequency increases',
    ];
  }

  // Get default "then" phrases
  List<String> _getDefaultThenPhrases() {
    return [
      'then they will grow taller',
      'then ice will melt faster',
      'then test scores will improve',
      'then it will boil',
      'then weight loss will occur',
    ];
  }

  // Get default "because" phrases
  List<String> _getDefaultBecausePhrases() {
    return [
      'because photosynthesis increases',
      'because molecular motion increases',
      'because more information is retained',
      'because water molecules gain enough energy to change state',
      'because more calories are burned',
    ];
  }

  // Get default variables
  List<String> _getDefaultVariables() {
    return [
      'Amount of sunlight',
      'Temperature',
      'Study time',
      'Water temperature',
      'Exercise frequency',
    ];
  }

  // Get default controlled variables
  List<String> _getDefaultControlledVariables() {
    return [
      'Type of plant',
      'Type of ice',
      'Study material',
      'Atmospheric pressure',
      'Diet',
    ];
  }

  // Get color from hex string
  Color _getColorFromHex(String hexColor) {
    hexColor = hexColor.replaceAll('#', '');
    if (hexColor.length == 6) {
      hexColor = 'FF$hexColor';
    }
    return Color(int.parse(hexColor, radix: 16));
  }

  // Add a custom "if" phrase
  void _addCustomIf() {
    if (_customIfController.text.isNotEmpty) {
      setState(() {
        _ifPhrases.add(_customIfController.text);
        _selectedIf = _customIfController.text;
        _customIfController.clear();
      });
    }
  }

  // Add a custom "then" phrase
  void _addCustomThen() {
    if (_customThenController.text.isNotEmpty) {
      setState(() {
        _thenPhrases.add(_customThenController.text);
        _selectedThen = _customThenController.text;
        _customThenController.clear();
      });
    }
  }

  // Add a custom "because" phrase
  void _addCustomBecause() {
    if (_customBecauseController.text.isNotEmpty) {
      setState(() {
        _becausePhrases.add(_customBecauseController.text);
        _selectedBecause = _customBecauseController.text;
        _customBecauseController.clear();
      });
    }
  }

  // Add a custom variable
  void _addCustomVariable() {
    if (_customVariableController.text.isNotEmpty) {
      setState(() {
        _variables.add(_customVariableController.text);
        _selectedVariables.add(_customVariableController.text);
        _customVariableController.clear();
      });
    }
  }

  // Add a custom controlled variable
  void _addCustomControlledVariable() {
    if (_customControlledVariableController.text.isNotEmpty) {
      setState(() {
        _controlledVariables.add(_customControlledVariableController.text);
        _selectedControlledVariables.add(_customControlledVariableController.text);
        _customControlledVariableController.clear();
      });
    }
  }

  // Select an "if" phrase
  void _selectIf(String phrase) {
    setState(() {
      _selectedIf = phrase;
    });
  }

  // Select a "then" phrase
  void _selectThen(String phrase) {
    setState(() {
      _selectedThen = phrase;
    });
  }

  // Select a "because" phrase
  void _selectBecause(String phrase) {
    setState(() {
      _selectedBecause = phrase;
    });
  }

  // Toggle a variable selection
  void _toggleVariable(String variable) {
    setState(() {
      if (_selectedVariables.contains(variable)) {
        _selectedVariables.remove(variable);
      } else {
        _selectedVariables.add(variable);
      }
    });
  }

  // Toggle a controlled variable selection
  void _toggleControlledVariable(String variable) {
    setState(() {
      if (_selectedControlledVariables.contains(variable)) {
        _selectedControlledVariables.remove(variable);
      } else {
        _selectedControlledVariables.add(variable);
      }
    });
  }

  // Go to the next step
  void _nextStep() {
    if (_currentStep < 3) {
      setState(() {
        _currentStep++;
      });
    } else {
      _evaluateHypothesis();
    }
  }

  // Go to the previous step
  void _previousStep() {
    if (_currentStep > 0) {
      setState(() {
        _currentStep--;
      });
    }
  }

  // Evaluate the hypothesis
  void _evaluateHypothesis() {
    // Check if all components are selected
    bool hasIf = _selectedIf.isNotEmpty;
    bool hasThen = _selectedThen.isNotEmpty;
    bool hasBecause = _selectedBecause.isNotEmpty;
    bool hasVariables = _selectedVariables.isNotEmpty;
    bool hasControlledVariables = _selectedControlledVariables.isNotEmpty;
    
    // Evaluate the hypothesis
    _isHypothesisValid = hasIf && hasThen && hasBecause && hasVariables && hasControlledVariables;
    
    // Generate feedback
    if (_isHypothesisValid) {
      _feedbackMessage = 'Great job! Your hypothesis is well-structured and testable.';
    } else {
      List<String> missingComponents = [];
      if (!hasIf) missingComponents.add('"If" statement');
      if (!hasThen) missingComponents.add('"Then" statement');
      if (!hasBecause) missingComponents.add('"Because" explanation');
      if (!hasVariables) missingComponents.add('Variables');
      if (!hasControlledVariables) missingComponents.add('Controlled variables');
      
      _feedbackMessage = 'Your hypothesis needs improvement. Missing: ${missingComponents.join(', ')}.';
    }
    
    setState(() {
      _showFeedback = true;
      _isCompleted = true;
    });
    
    // Notify parent of completion
    widget.onStateChanged?.call(true);
  }

  // Reset the hypothesis builder
  void _resetBuilder() {
    setState(() {
      _selectedIf = '';
      _selectedThen = '';
      _selectedBecause = '';
      _selectedVariables = [];
      _selectedControlledVariables = [];
      _currentStep = 0;
      _isCompleted = false;
      _showFeedback = false;
    });
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: _backgroundColor,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withOpacity(0.2),
            spreadRadius: 1,
            blurRadius: 3,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Title
          Text(
            widget.data['title'] ?? 'Hypothesis Builder',
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
              color: _textColor,
            ),
          ),
          
          const SizedBox(height: 16),
          
          // Stepper
          LinearProgressIndicator(
            value: (_currentStep + 1) / 4,
            backgroundColor: Colors.grey[200],
            valueColor: AlwaysStoppedAnimation<Color>(_primaryColor),
          ),
          
          Padding(
            padding: const EdgeInsets.symmetric(vertical: 8.0),
            child: Text(
              'Step ${_currentStep + 1} of 4: ${_getStepTitle(_currentStep)}',
              style: TextStyle(
                fontSize: 14,
                fontWeight: FontWeight.bold,
                color: _primaryColor,
              ),
            ),
          ),
          
          const SizedBox(height: 8),
          
          // Current step content
          _buildStepContent(),
          
          const SizedBox(height: 16),
          
          // Navigation buttons
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              ElevatedButton(
                onPressed: _currentStep > 0 ? _previousStep : null,
                style: ElevatedButton.styleFrom(
                  backgroundColor: _secondaryColor,
                  foregroundColor: Colors.white,
                ),
                child: const Text('Previous'),
              ),
              if (_showFeedback)
                ElevatedButton(
                  onPressed: _resetBuilder,
                  style: ElevatedButton.styleFrom(
                    backgroundColor: _accentColor,
                    foregroundColor: Colors.white,
                  ),
                  child: const Text('Start Over'),
                )
              else
                ElevatedButton(
                  onPressed: _nextStep,
                  style: ElevatedButton.styleFrom(
                    backgroundColor: _primaryColor,
                    foregroundColor: Colors.white,
                  ),
                  child: Text(_currentStep < 3 ? 'Next' : 'Evaluate'),
                ),
            ],
          ),
          
          // Widget name tag at the bottom
          if (widget.data['showNameTag'] ?? true)
            Padding(
              padding: const EdgeInsets.only(top: 8),
              child: Text(
                'InteractiveHypothesisBuilderWidget',
                style: TextStyle(
                  color: Colors.grey[600],
                  fontSize: 12,
                  fontStyle: FontStyle.italic,
                ),
              ),
            ),
        ],
      ),
    );
  }

  // Get the title for the current step
  String _getStepTitle(int step) {
    switch (step) {
      case 0:
        return 'Create Your Hypothesis Statement';
      case 1:
        return 'Add Explanation';
      case 2:
        return 'Identify Variables';
      case 3:
        return 'Identify Controlled Variables';
      default:
        return '';
    }
  }

  // Build the content for the current step
  Widget _buildStepContent() {
    switch (_currentStep) {
      case 0:
        return _buildHypothesisStatementStep();
      case 1:
        return _buildExplanationStep();
      case 2:
        return _buildVariablesStep();
      case 3:
        return _buildControlledVariablesStep();
      default:
        return Container();
    }
  }

  // Build the hypothesis statement step
  Widget _buildHypothesisStatementStep() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Select or create an "If" statement:',
          style: TextStyle(
            fontSize: 14,
            fontWeight: FontWeight.bold,
            color: _textColor,
          ),
        ),
        const SizedBox(height: 8),
        Wrap(
          spacing: 8,
          runSpacing: 8,
          children: _ifPhrases.map((phrase) {
            return ChoiceChip(
              label: Text(phrase),
              selected: _selectedIf == phrase,
              onSelected: (selected) {
                if (selected) _selectIf(phrase);
              },
            );
          }).toList(),
        ),
        const SizedBox(height: 8),
        Row(
          children: [
            Expanded(
              child: TextField(
                controller: _customIfController,
                decoration: const InputDecoration(
                  hintText: 'Create your own "If" statement',
                  border: OutlineInputBorder(),
                ),
              ),
            ),
            const SizedBox(width: 8),
            ElevatedButton(
              onPressed: _addCustomIf,
              child: const Text('Add'),
            ),
          ],
        ),
        const SizedBox(height: 16),
        Text(
          'Select or create a "Then" statement:',
          style: TextStyle(
            fontSize: 14,
            fontWeight: FontWeight.bold,
            color: _textColor,
          ),
        ),
        const SizedBox(height: 8),
        Wrap(
          spacing: 8,
          runSpacing: 8,
          children: _thenPhrases.map((phrase) {
            return ChoiceChip(
              label: Text(phrase),
              selected: _selectedThen == phrase,
              onSelected: (selected) {
                if (selected) _selectThen(phrase);
              },
            );
          }).toList(),
        ),
        const SizedBox(height: 8),
        Row(
          children: [
            Expanded(
              child: TextField(
                controller: _customThenController,
                decoration: const InputDecoration(
                  hintText: 'Create your own "Then" statement',
                  border: OutlineInputBorder(),
                ),
              ),
            ),
            const SizedBox(width: 8),
            ElevatedButton(
              onPressed: _addCustomThen,
              child: const Text('Add'),
            ),
          ],
        ),
        const SizedBox(height: 16),
        if (_selectedIf.isNotEmpty && _selectedThen.isNotEmpty)
          Container(
            padding: const EdgeInsets.all(12),
            decoration: BoxDecoration(
              color: _primaryColor.withOpacity(0.1),
              borderRadius: BorderRadius.circular(8),
              border: Border.all(color: _primaryColor.withOpacity(0.3)),
            ),
            child: Text(
              '$_selectedIf, $_selectedThen.',
              style: TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.bold,
                color: _textColor,
              ),
            ),
          ),
      ],
    );
  }

  // Build the explanation step
  Widget _buildExplanationStep() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Select or create a "Because" explanation:',
          style: TextStyle(
            fontSize: 14,
            fontWeight: FontWeight.bold,
            color: _textColor,
          ),
        ),
        const SizedBox(height: 8),
        Wrap(
          spacing: 8,
          runSpacing: 8,
          children: _becausePhrases.map((phrase) {
            return ChoiceChip(
              label: Text(phrase),
              selected: _selectedBecause == phrase,
              onSelected: (selected) {
                if (selected) _selectBecause(phrase);
              },
            );
          }).toList(),
        ),
        const SizedBox(height: 8),
        Row(
          children: [
            Expanded(
              child: TextField(
                controller: _customBecauseController,
                decoration: const InputDecoration(
                  hintText: 'Create your own "Because" explanation',
                  border: OutlineInputBorder(),
                ),
              ),
            ),
            const SizedBox(width: 8),
            ElevatedButton(
              onPressed: _addCustomBecause,
              child: const Text('Add'),
            ),
          ],
        ),
        const SizedBox(height: 16),
        if (_selectedIf.isNotEmpty && _selectedThen.isNotEmpty && _selectedBecause.isNotEmpty)
          Container(
            padding: const EdgeInsets.all(12),
            decoration: BoxDecoration(
              color: _primaryColor.withOpacity(0.1),
              borderRadius: BorderRadius.circular(8),
              border: Border.all(color: _primaryColor.withOpacity(0.3)),
            ),
            child: Text(
              '$_selectedIf, $_selectedThen $_selectedBecause.',
              style: TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.bold,
                color: _textColor,
              ),
            ),
          ),
      ],
    );
  }

  // Build the variables step
  Widget _buildVariablesStep() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Select or create variables to test:',
          style: TextStyle(
            fontSize: 14,
            fontWeight: FontWeight.bold,
            color: _textColor,
          ),
        ),
        const SizedBox(height: 4),
        Text(
          'Variables are factors that you will change or measure in your experiment.',
          style: TextStyle(
            fontSize: 12,
            fontStyle: FontStyle.italic,
            color: _textColor.withOpacity(0.7),
          ),
        ),
        const SizedBox(height: 8),
        Wrap(
          spacing: 8,
          runSpacing: 8,
          children: _variables.map((variable) {
            return FilterChip(
              label: Text(variable),
              selected: _selectedVariables.contains(variable),
              onSelected: (selected) {
                _toggleVariable(variable);
              },
            );
          }).toList(),
        ),
        const SizedBox(height: 8),
        Row(
          children: [
            Expanded(
              child: TextField(
                controller: _customVariableController,
                decoration: const InputDecoration(
                  hintText: 'Add a new variable',
                  border: OutlineInputBorder(),
                ),
              ),
            ),
            const SizedBox(width: 8),
            ElevatedButton(
              onPressed: _addCustomVariable,
              child: const Text('Add'),
            ),
          ],
        ),
        const SizedBox(height: 16),
        if (_selectedVariables.isNotEmpty)
          Container(
            padding: const EdgeInsets.all(12),
            decoration: BoxDecoration(
              color: _primaryColor.withOpacity(0.1),
              borderRadius: BorderRadius.circular(8),
              border: Border.all(color: _primaryColor.withOpacity(0.3)),
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'Selected Variables:',
                  style: TextStyle(
                    fontSize: 14,
                    fontWeight: FontWeight.bold,
                    color: _textColor,
                  ),
                ),
                const SizedBox(height: 4),
                ...(_selectedVariables.map((variable) => Padding(
                  padding: const EdgeInsets.only(bottom: 4),
                  child: Row(
                    children: [
                      Icon(Icons.check_circle, color: _accentColor, size: 16),
                      const SizedBox(width: 4),
                      Text(variable),
                    ],
                  ),
                ))),
              ],
            ),
          ),
      ],
    );
  }

  // Build the controlled variables step
  Widget _buildControlledVariablesStep() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Select or create controlled variables:',
          style: TextStyle(
            fontSize: 14,
            fontWeight: FontWeight.bold,
            color: _textColor,
          ),
        ),
        const SizedBox(height: 4),
        Text(
          'Controlled variables are factors that you will keep constant during your experiment.',
          style: TextStyle(
            fontSize: 12,
            fontStyle: FontStyle.italic,
            color: _textColor.withOpacity(0.7),
          ),
        ),
        const SizedBox(height: 8),
        Wrap(
          spacing: 8,
          runSpacing: 8,
          children: _controlledVariables.map((variable) {
            return FilterChip(
              label: Text(variable),
              selected: _selectedControlledVariables.contains(variable),
              onSelected: (selected) {
                _toggleControlledVariable(variable);
              },
            );
          }).toList(),
        ),
        const SizedBox(height: 8),
        Row(
          children: [
            Expanded(
              child: TextField(
                controller: _customControlledVariableController,
                decoration: const InputDecoration(
                  hintText: 'Add a new controlled variable',
                  border: OutlineInputBorder(),
                ),
              ),
            ),
            const SizedBox(width: 8),
            ElevatedButton(
              onPressed: _addCustomControlledVariable,
              child: const Text('Add'),
            ),
          ],
        ),
        const SizedBox(height: 16),
        if (_selectedControlledVariables.isNotEmpty)
          Container(
            padding: const EdgeInsets.all(12),
            decoration: BoxDecoration(
              color: _primaryColor.withOpacity(0.1),
              borderRadius: BorderRadius.circular(8),
              border: Border.all(color: _primaryColor.withOpacity(0.3)),
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'Selected Controlled Variables:',
                  style: TextStyle(
                    fontSize: 14,
                    fontWeight: FontWeight.bold,
                    color: _textColor,
                  ),
                ),
                const SizedBox(height: 4),
                ...(_selectedControlledVariables.map((variable) => Padding(
                  padding: const EdgeInsets.only(bottom: 4),
                  child: Row(
                    children: [
                      Icon(Icons.check_circle, color: _accentColor, size: 16),
                      const SizedBox(width: 4),
                      Text(variable),
                    ],
                  ),
                ))),
              ],
            ),
          ),
        const SizedBox(height: 16),
        if (_showFeedback)
          Container(
            padding: const EdgeInsets.all(12),
            decoration: BoxDecoration(
              color: _isHypothesisValid ? _accentColor.withOpacity(0.1) : _secondaryColor.withOpacity(0.1),
              borderRadius: BorderRadius.circular(8),
              border: Border.all(
                color: _isHypothesisValid ? _accentColor.withOpacity(0.3) : _secondaryColor.withOpacity(0.3),
              ),
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'Feedback:',
                  style: TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                    color: _isHypothesisValid ? _accentColor : _secondaryColor,
                  ),
                ),
                const SizedBox(height: 8),
                Text(
                  _feedbackMessage,
                  style: TextStyle(
                    fontSize: 14,
                    color: _textColor,
                  ),
                ),
                const SizedBox(height: 12),
                Text(
                  'Complete Hypothesis:',
                  style: TextStyle(
                    fontSize: 14,
                    fontWeight: FontWeight.bold,
                    color: _textColor,
                  ),
                ),
                const SizedBox(height: 4),
                Text(
                  '$_selectedIf, $_selectedThen $_selectedBecause.',
                  style: TextStyle(
                    fontSize: 14,
                    fontStyle: FontStyle.italic,
                    color: _textColor,
                  ),
                ),
              ],
            ),
          ),
      ],
    );
  }
}
