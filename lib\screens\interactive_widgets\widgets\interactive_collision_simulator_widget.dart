import 'package:flutter/material.dart';
import 'dart:math' as math;
import 'dart:async';

class PhysicsObject {
  Offset position;
  Offset velocity;
  final double mass;
  final double radius;
  final Color color;

  PhysicsObject({
    required this.position,
    required this.velocity,
    required this.mass,
    required this.radius,
    required this.color,
  });
}

class InteractiveCollisionSimulatorWidget extends StatefulWidget {
  final Map<String, dynamic>? data;

  const InteractiveCollisionSimulatorWidget({
    super.key,
    this.data,
  });

  factory InteractiveCollisionSimulatorWidget.fromData(
      Map<String, dynamic> data) {
    return InteractiveCollisionSimulatorWidget(
      data: data,
    );
  }

  @override
  State<InteractiveCollisionSimulatorWidget> createState() =>
      _InteractiveCollisionSimulatorWidgetState();
}

class _InteractiveCollisionSimulatorWidgetState
    extends State<InteractiveCollisionSimulatorWidget>
    with SingleTickerProviderStateMixin {
  // UI parameters
  late Color _primaryColor;
  late Color _secondaryColor;
  late Color _tertiaryColor;
  late Color _textColor;
  late Color _backgroundColor;

  // Simulation parameters
  String _collisionType = 'elastic'; // elastic, inelastic, perfectly_inelastic
  bool _isSimulating = false;
  bool _showVelocityVectors = true;
  bool _showMomentumVectors = true;
  bool _showKineticEnergy = true;
  Timer? _simulationTimer;
  double _timeStep = 0.016; // 60 FPS
  double _elapsedTime = 0.0;
  double _simulationSpeed = 1.0;
  double _coefficientOfRestitution = 1.0; // 1.0 for elastic, 0.0 for perfectly inelastic

  // Physics objects
  late List<PhysicsObject> _objects;

  // Boundaries
  late double _minX;
  late double _maxX;
  late double _minY;
  late double _maxY;

  // Tracking
  List<double> _totalMomentumHistory = [];
  List<double> _totalKineticEnergyHistory = [];

  // Animation controller
  late AnimationController _animationController;
  late Animation<double> _animation;

  @override
  void initState() {
    super.initState();
    _initializeFromData();
    _animationController = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 500),
    );
    _animation = CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeInOut,
    );
    _animationController.forward();
    _initializeSimulation();
  }

  @override
  void dispose() {
    _simulationTimer?.cancel();
    _animationController.dispose();
    super.dispose();
  }

  void _initializeFromData() {
    final data = widget.data;
    if (data != null) {
      _primaryColor = Color(data['primary_color'] ?? 0xFF2196F3);
      _secondaryColor = Color(data['secondary_color'] ?? 0xFFFFA000);
      _tertiaryColor = Color(data['tertiary_color'] ?? 0xFF4CAF50);
      _textColor = Color(data['text_color'] ?? 0xFF333333);
      _backgroundColor = Color(data['background_color'] ?? 0xFFF5F5F5);

      if (data['collision_type'] != null) {
        _collisionType = data['collision_type'];
      }

      if (data['coefficient_of_restitution'] != null) {
        _coefficientOfRestitution = data['coefficient_of_restitution'].toDouble();
      } else {
        _updateCoefficientOfRestitution();
      }
    } else {
      _primaryColor = Colors.blue;
      _secondaryColor = Colors.orange;
      _tertiaryColor = Colors.green;
      _textColor = Colors.black87;
      _backgroundColor = Colors.grey.shade100;
      _updateCoefficientOfRestitution();
    }
  }

  void _updateCoefficientOfRestitution() {
    switch (_collisionType) {
      case 'elastic':
        _coefficientOfRestitution = 1.0;
        break;
      case 'inelastic':
        _coefficientOfRestitution = 0.5;
        break;
      case 'perfectly_inelastic':
        _coefficientOfRestitution = 0.0;
        break;
    }
  }

  void _initializeSimulation() {
    // Set boundaries (will be scaled to actual size in the paint method)
    _minX = 0;
    _maxX = 100;
    _minY = 0;
    _maxY = 100;

    // Create physics objects
    _objects = [
      PhysicsObject(
        position: Offset(30, 50),
        velocity: Offset(10, 0),
        mass: 10.0,
        radius: 5.0,
        color: _primaryColor,
      ),
      PhysicsObject(
        position: Offset(70, 50),
        velocity: Offset(-5, 0),
        mass: 5.0,
        radius: 3.0,
        color: _secondaryColor,
      ),
    ];

    // Reset tracking
    _totalMomentumHistory = [];
    _totalKineticEnergyHistory = [];
    _elapsedTime = 0.0;

    // Calculate initial values
    _calculateTotalMomentum();
    _calculateTotalKineticEnergy();
  }

  void _resetSimulation() {
    _stopSimulation();
    _initializeSimulation();
    setState(() {});
  }

  void _toggleSimulation() {
    if (_isSimulating) {
      _stopSimulation();
    } else {
      _startSimulation();
    }
  }

  void _startSimulation() {
    if (_isSimulating) return;

    setState(() {
      _isSimulating = true;
    });

    _simulationTimer = Timer.periodic(Duration(milliseconds: (_timeStep * 1000 ~/ _simulationSpeed)), (timer) {
      _updateSimulation();
    });
  }

  void _stopSimulation() {
    _simulationTimer?.cancel();
    _simulationTimer = null;

    setState(() {
      _isSimulating = false;
    });
  }

  void _updateSimulation() {
    setState(() {
      // Update elapsed time
      _elapsedTime += _timeStep;

      // Move objects
      for (var object in _objects) {
        object.position = Offset(
          object.position.dx + object.velocity.dx * _timeStep,
          object.position.dy + object.velocity.dy * _timeStep,
        );
      }

      // Check for collisions with boundaries
      for (var object in _objects) {
        // X boundaries
        if (object.position.dx - object.radius < _minX) {
          object.position = Offset(_minX + object.radius, object.position.dy);
          object.velocity = Offset(-object.velocity.dx, object.velocity.dy);
        } else if (object.position.dx + object.radius > _maxX) {
          object.position = Offset(_maxX - object.radius, object.position.dy);
          object.velocity = Offset(-object.velocity.dx, object.velocity.dy);
        }

        // Y boundaries
        if (object.position.dy - object.radius < _minY) {
          object.position = Offset(object.position.dx, _minY + object.radius);
          object.velocity = Offset(object.velocity.dx, -object.velocity.dy);
        } else if (object.position.dy + object.radius > _maxY) {
          object.position = Offset(object.position.dx, _maxY - object.radius);
          object.velocity = Offset(object.velocity.dx, -object.velocity.dy);
        }
      }

      // Check for collisions between objects
      for (int i = 0; i < _objects.length; i++) {
        for (int j = i + 1; j < _objects.length; j++) {
          if (_checkCollision(_objects[i], _objects[j])) {
            _resolveCollision(_objects[i], _objects[j]);
          }
        }
      }

      // Update tracking
      _calculateTotalMomentum();
      _calculateTotalKineticEnergy();
    });
  }

  bool _checkCollision(PhysicsObject a, PhysicsObject b) {
    final distance = (a.position - b.position).distance;
    return distance <= a.radius + b.radius;
  }

  void _resolveCollision(PhysicsObject a, PhysicsObject b) {
    // Calculate normal vector
    final normalVector = b.position - a.position;
    final distance = normalVector.distance;
    // Normalize the vector manually
    final normal = Offset(
      normalVector.dx / distance,
      normalVector.dy / distance,
    );

    // Calculate relative velocity
    final relativeVelocity = b.velocity - a.velocity;

    // Calculate relative velocity along normal
    final velocityAlongNormal = relativeVelocity.dx * normal.dx + relativeVelocity.dy * normal.dy;

    // Do not resolve if objects are moving away from each other
    if (velocityAlongNormal > 0) return;

    // Calculate impulse scalar
    final impulseScalar = -(1 + _coefficientOfRestitution) * velocityAlongNormal /
        (1 / a.mass + 1 / b.mass);

    // Apply impulse
    final impulse = Offset(normal.dx * impulseScalar, normal.dy * impulseScalar);

    a.velocity = Offset(
      a.velocity.dx - impulse.dx / a.mass,
      a.velocity.dy - impulse.dy / a.mass,
    );

    b.velocity = Offset(
      b.velocity.dx + impulse.dx / b.mass,
      b.velocity.dy + impulse.dy / b.mass,
    );

    // If perfectly inelastic, make objects stick together
    if (_collisionType == 'perfectly_inelastic') {
      // Calculate center of mass
      final totalMass = a.mass + b.mass;
      final centerOfMassX = (a.mass * a.position.dx + b.mass * b.position.dx) / totalMass;
      final centerOfMassY = (a.mass * a.position.dy + b.mass * b.position.dy) / totalMass;
      final centerOfMass = Offset(centerOfMassX, centerOfMassY);

      // Calculate velocity of center of mass
      final velocityOfMassX = (a.mass * a.velocity.dx + b.mass * b.velocity.dx) / totalMass;
      final velocityOfMassY = (a.mass * a.velocity.dy + b.mass * b.velocity.dy) / totalMass;
      final velocityOfMass = Offset(velocityOfMassX, velocityOfMassY);

      // Set both objects to center of mass and same velocity
      a.position = centerOfMass;
      b.position = centerOfMass;
      a.velocity = velocityOfMass;
      b.velocity = velocityOfMass;
    } else {
      // Separate objects to prevent sticking
      final overlap = a.radius + b.radius - (a.position - b.position).distance;
      final separationVector = Offset(normal.dx * overlap / 2, normal.dy * overlap / 2);

      a.position = Offset(
        a.position.dx - separationVector.dx,
        a.position.dy - separationVector.dy,
      );

      b.position = Offset(
        b.position.dx + separationVector.dx,
        b.position.dy + separationVector.dy,
      );
    }
  }

  void _calculateTotalMomentum() {
    double totalMomentumX = 0;
    double totalMomentumY = 0;

    for (var object in _objects) {
      totalMomentumX += object.mass * object.velocity.dx;
      totalMomentumY += object.mass * object.velocity.dy;
    }

    final totalMomentum = math.sqrt(totalMomentumX * totalMomentumX + totalMomentumY * totalMomentumY);
    _totalMomentumHistory.add(totalMomentum);

    // Keep history at a reasonable size
    if (_totalMomentumHistory.length > 100) {
      _totalMomentumHistory.removeAt(0);
    }
  }

  void _calculateTotalKineticEnergy() {
    double totalKineticEnergy = 0;

    for (var object in _objects) {
      final speed = math.sqrt(object.velocity.dx * object.velocity.dx + object.velocity.dy * object.velocity.dy);
      totalKineticEnergy += 0.5 * object.mass * speed * speed;
    }

    _totalKineticEnergyHistory.add(totalKineticEnergy);

    // Keep history at a reasonable size
    if (_totalKineticEnergyHistory.length > 100) {
      _totalKineticEnergyHistory.removeAt(0);
    }
  }

  @override
  Widget build(BuildContext context) {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
      ),
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Title and description
            Text(
              'Collision Simulator',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
                color: _textColor,
              ),
            ),
            const SizedBox(height: 8),
            Text(
              'Explore momentum conservation in different types of collisions',
              style: TextStyle(
                fontSize: 14,
                color: _textColor.withOpacity(0.7),
              ),
            ),
            const SizedBox(height: 16),

            // Collision type selector
            Text(
              'Collision Type:',
              style: TextStyle(
                fontSize: 14,
                fontWeight: FontWeight.bold,
                color: _textColor,
              ),
            ),
            const SizedBox(height: 8),
            SegmentedButton<String>(
              segments: const [
                ButtonSegment(
                  value: 'elastic',
                  label: Text('Elastic'),
                  icon: Icon(Icons.refresh),
                ),
                ButtonSegment(
                  value: 'inelastic',
                  label: Text('Inelastic'),
                  icon: Icon(Icons.compare_arrows),
                ),
                ButtonSegment(
                  value: 'perfectly_inelastic',
                  label: Text('Perfectly Inelastic'),
                  icon: Icon(Icons.merge_type),
                ),
              ],
              selected: {_collisionType},
              onSelectionChanged: (Set<String> selection) {
                setState(() {
                  _collisionType = selection.first;
                  _updateCoefficientOfRestitution();
                  _resetSimulation();
                });
              },
            ),
            const SizedBox(height: 16),

            // Simulation controls
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                // Start/Stop button
                ElevatedButton.icon(
                  onPressed: _toggleSimulation,
                  icon: Icon(_isSimulating ? Icons.pause : Icons.play_arrow),
                  label: Text(_isSimulating ? 'Pause' : 'Start'),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: _primaryColor,
                    foregroundColor: Colors.white,
                  ),
                ),

                // Reset button
                ElevatedButton.icon(
                  onPressed: _resetSimulation,
                  icon: const Icon(Icons.refresh),
                  label: const Text('Reset'),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: _secondaryColor,
                    foregroundColor: Colors.white,
                  ),
                ),

                // Simulation speed
                Row(
                  children: [
                    const Text('Speed:'),
                    const SizedBox(width: 8),
                    DropdownButton<double>(
                      value: _simulationSpeed,
                      items: [0.5, 1.0, 2.0, 4.0].map((speed) {
                        return DropdownMenuItem<double>(
                          value: speed,
                          child: Text('${speed}x'),
                        );
                      }).toList(),
                      onChanged: (value) {
                        if (value != null) {
                          setState(() {
                            _simulationSpeed = value;
                            if (_isSimulating) {
                              _stopSimulation();
                              _startSimulation();
                            }
                          });
                        }
                      },
                    ),
                  ],
                ),
              ],
            ),
            const SizedBox(height: 16),

            // Display options
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceAround,
              children: [
                // Velocity vectors
                Row(
                  children: [
                    Checkbox(
                      value: _showVelocityVectors,
                      onChanged: (value) {
                        setState(() {
                          _showVelocityVectors = value ?? true;
                        });
                      },
                      activeColor: _primaryColor,
                    ),
                    const Text('Velocity'),
                  ],
                ),

                // Momentum vectors
                Row(
                  children: [
                    Checkbox(
                      value: _showMomentumVectors,
                      onChanged: (value) {
                        setState(() {
                          _showMomentumVectors = value ?? true;
                        });
                      },
                      activeColor: _secondaryColor,
                    ),
                    const Text('Momentum'),
                  ],
                ),

                // Kinetic energy
                Row(
                  children: [
                    Checkbox(
                      value: _showKineticEnergy,
                      onChanged: (value) {
                        setState(() {
                          _showKineticEnergy = value ?? true;
                        });
                      },
                      activeColor: _tertiaryColor,
                    ),
                    const Text('Energy'),
                  ],
                ),
              ],
            ),
            const SizedBox(height: 16),

            // Simulation area
            Container(
              height: 200,
              width: double.infinity,
              decoration: BoxDecoration(
                color: Colors.grey.withOpacity(0.1),
                borderRadius: BorderRadius.circular(8),
                border: Border.all(color: Colors.grey.withOpacity(0.3)),
              ),
              child: ClipRRect(
                borderRadius: BorderRadius.circular(8),
                child: CustomPaint(
                  painter: CollisionSimulatorPainter(
                    objects: _objects,
                    minX: _minX,
                    maxX: _maxX,
                    minY: _minY,
                    maxY: _maxY,
                    showVelocityVectors: _showVelocityVectors,
                    showMomentumVectors: _showMomentumVectors,
                    primaryColor: _primaryColor,
                    secondaryColor: _secondaryColor,
                    textColor: _textColor,
                    animationValue: _animation.value,
                  ),
                ),
              ),
            ),
            const SizedBox(height: 16),

            // Conservation graphs
            if (_showKineticEnergy || _showMomentumVectors)
              Container(
                height: 100,
                width: double.infinity,
                decoration: BoxDecoration(
                  color: Colors.grey.withOpacity(0.1),
                  borderRadius: BorderRadius.circular(8),
                  border: Border.all(color: Colors.grey.withOpacity(0.3)),
                ),
                child: CustomPaint(
                  painter: ConservationGraphPainter(
                    momentumHistory: _totalMomentumHistory,
                    kineticEnergyHistory: _totalKineticEnergyHistory,
                    showMomentum: _showMomentumVectors,
                    showKineticEnergy: _showKineticEnergy,
                    primaryColor: _primaryColor,
                    secondaryColor: _secondaryColor,
                    tertiaryColor: _tertiaryColor,
                    textColor: _textColor,
                    collisionType: _collisionType,
                  ),
                ),
              ),
            const SizedBox(height: 16),

            // Physics explanation
            ExpansionTile(
              title: Text(
                'Understanding Collisions and Momentum',
                style: TextStyle(
                  fontSize: 14,
                  fontWeight: FontWeight.bold,
                  color: _textColor,
                ),
              ),
              children: [
                Padding(
                  padding: const EdgeInsets.all(16.0),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'Types of Collisions:',
                        style: TextStyle(
                          fontSize: 14,
                          fontWeight: FontWeight.bold,
                          color: _textColor,
                        ),
                      ),
                      const SizedBox(height: 4),
                      Text(
                        '• Elastic: Both momentum and kinetic energy are conserved',
                        style: TextStyle(
                          fontSize: 14,
                          color: _textColor,
                        ),
                      ),
                      Text(
                        '• Inelastic: Momentum is conserved, but some kinetic energy is lost',
                        style: TextStyle(
                          fontSize: 14,
                          color: _textColor,
                        ),
                      ),
                      Text(
                        '• Perfectly Inelastic: Objects stick together after collision, momentum is conserved but maximum kinetic energy is lost',
                        style: TextStyle(
                          fontSize: 14,
                          color: _textColor,
                        ),
                      ),
                      const SizedBox(height: 8),
                      Text(
                        'Conservation of Momentum:',
                        style: TextStyle(
                          fontSize: 14,
                          fontWeight: FontWeight.bold,
                          color: _textColor,
                        ),
                      ),
                      const SizedBox(height: 4),
                      Text(
                        'The total momentum of a closed system remains constant: m₁v₁ + m₂v₂ = m₁v₁\' + m₂v₂\'',
                        style: TextStyle(
                          fontSize: 14,
                          color: _textColor,
                        ),
                      ),
                      const SizedBox(height: 8),
                      Text(
                        'Coefficient of Restitution (e):',
                        style: TextStyle(
                          fontSize: 14,
                          fontWeight: FontWeight.bold,
                          color: _textColor,
                        ),
                      ),
                      const SizedBox(height: 4),
                      Text(
                        'e = 1: Perfectly elastic collision',
                        style: TextStyle(
                          fontSize: 14,
                          color: _textColor,
                        ),
                      ),
                      Text(
                        '0 < e < 1: Inelastic collision',
                        style: TextStyle(
                          fontSize: 14,
                          color: _textColor,
                        ),
                      ),
                      Text(
                        'e = 0: Perfectly inelastic collision',
                        style: TextStyle(
                          fontSize: 14,
                          color: _textColor,
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }
}

class CollisionSimulatorPainter extends CustomPainter {
  final List<PhysicsObject> objects;
  final double minX;
  final double maxX;
  final double minY;
  final double maxY;
  final bool showVelocityVectors;
  final bool showMomentumVectors;
  final Color primaryColor;
  final Color secondaryColor;
  final Color textColor;
  final double animationValue;

  CollisionSimulatorPainter({
    required this.objects,
    required this.minX,
    required this.maxX,
    required this.minY,
    required this.maxY,
    required this.showVelocityVectors,
    required this.showMomentumVectors,
    required this.primaryColor,
    required this.secondaryColor,
    required this.textColor,
    required this.animationValue,
  });

  @override
  void paint(Canvas canvas, Size size) {
    // Draw background grid
    _drawGrid(canvas, size);

    // Scale factors to map simulation coordinates to canvas coordinates
    final scaleX = size.width / (maxX - minX);
    final scaleY = size.height / (maxY - minY);

    // Draw objects
    for (var object in objects) {
      // Map object position to canvas coordinates
      final canvasX = (object.position.dx - minX) * scaleX;
      final canvasY = (object.position.dy - minY) * scaleY;
      final canvasPosition = Offset(canvasX, canvasY);
      final canvasRadius = object.radius * scaleX;

      // Draw object
      final paint = Paint()
        ..color = object.color
        ..style = PaintingStyle.fill;

      canvas.drawCircle(
        canvasPosition,
        canvasRadius,
        paint,
      );

      // Draw border
      final borderPaint = Paint()
        ..color = Colors.black.withOpacity(0.3)
        ..style = PaintingStyle.stroke
        ..strokeWidth = 1.0;

      canvas.drawCircle(
        canvasPosition,
        canvasRadius,
        borderPaint,
      );

      // Draw velocity vector if enabled
      if (showVelocityVectors) {
        final velocityMagnitude = math.sqrt(
          object.velocity.dx * object.velocity.dx +
          object.velocity.dy * object.velocity.dy,
        );

        if (velocityMagnitude > 0.1) {
          final velocityDirection = Offset(
            object.velocity.dx / velocityMagnitude,
            object.velocity.dy / velocityMagnitude,
          );

          final vectorLength = math.min(velocityMagnitude * 2, 20.0);
          final endPoint = Offset(
            canvasPosition.dx + velocityDirection.dx * vectorLength,
            canvasPosition.dy + velocityDirection.dy * vectorLength,
          );

          final velocityPaint = Paint()
            ..color = primaryColor
            ..strokeWidth = 2.0
            ..style = PaintingStyle.stroke;

          // Draw line
          canvas.drawLine(
            canvasPosition,
            endPoint,
            velocityPaint,
          );

          // Draw arrowhead
          final arrowSize = 5.0;
          final angle = math.atan2(
            endPoint.dy - canvasPosition.dy,
            endPoint.dx - canvasPosition.dx,
          );

          final arrowPoint1 = Offset(
            endPoint.dx - arrowSize * math.cos(angle - math.pi / 6),
            endPoint.dy - arrowSize * math.sin(angle - math.pi / 6),
          );

          final arrowPoint2 = Offset(
            endPoint.dx - arrowSize * math.cos(angle + math.pi / 6),
            endPoint.dy - arrowSize * math.sin(angle + math.pi / 6),
          );

          final arrowPath = Path()
            ..moveTo(endPoint.dx, endPoint.dy)
            ..lineTo(arrowPoint1.dx, arrowPoint1.dy)
            ..lineTo(arrowPoint2.dx, arrowPoint2.dy)
            ..close();

          canvas.drawPath(arrowPath, velocityPaint..style = PaintingStyle.fill);
        }
      }

      // Draw momentum vector if enabled
      if (showMomentumVectors) {
        final momentumX = object.mass * object.velocity.dx;
        final momentumY = object.mass * object.velocity.dy;
        final momentumMagnitude = math.sqrt(
          momentumX * momentumX + momentumY * momentumY,
        );

        if (momentumMagnitude > 0.1) {
          final momentumDirection = Offset(
            momentumX / momentumMagnitude,
            momentumY / momentumMagnitude,
          );

          final vectorLength = math.min(momentumMagnitude / 2, 30.0);
          final endPoint = Offset(
            canvasPosition.dx + momentumDirection.dx * vectorLength,
            canvasPosition.dy + momentumDirection.dy * vectorLength,
          );

          final momentumPaint = Paint()
            ..color = secondaryColor
            ..strokeWidth = 2.0
            ..style = PaintingStyle.stroke;

          // Draw dashed line
          final dashLength = 4.0;
          final gapLength = 2.0;
          final dx = endPoint.dx - canvasPosition.dx;
          final dy = endPoint.dy - canvasPosition.dy;
          final distance = math.sqrt(dx * dx + dy * dy);
          final unitX = dx / distance;
          final unitY = dy / distance;

          var currentDistance = 0.0;
          var isDrawing = true;

          while (currentDistance < distance) {
            final remainingDistance = distance - currentDistance;
            final segmentLength = math.min(
              isDrawing ? dashLength : gapLength,
              remainingDistance,
            );

            if (isDrawing) {
              final startX = canvasPosition.dx + unitX * currentDistance;
              final startY = canvasPosition.dy + unitY * currentDistance;
              final endX = canvasPosition.dx + unitX * (currentDistance + segmentLength);
              final endY = canvasPosition.dy + unitY * (currentDistance + segmentLength);

              canvas.drawLine(
                Offset(startX, startY),
                Offset(endX, endY),
                momentumPaint,
              );
            }

            currentDistance += segmentLength;
            isDrawing = !isDrawing;
          }

          // Draw arrowhead
          final arrowSize = 5.0;
          final angle = math.atan2(
            endPoint.dy - canvasPosition.dy,
            endPoint.dx - canvasPosition.dx,
          );

          final arrowPoint1 = Offset(
            endPoint.dx - arrowSize * math.cos(angle - math.pi / 6),
            endPoint.dy - arrowSize * math.sin(angle - math.pi / 6),
          );

          final arrowPoint2 = Offset(
            endPoint.dx - arrowSize * math.cos(angle + math.pi / 6),
            endPoint.dy - arrowSize * math.sin(angle + math.pi / 6),
          );

          final arrowPath = Path()
            ..moveTo(endPoint.dx, endPoint.dy)
            ..lineTo(arrowPoint1.dx, arrowPoint1.dy)
            ..lineTo(arrowPoint2.dx, arrowPoint2.dy)
            ..close();

          canvas.drawPath(arrowPath, momentumPaint..style = PaintingStyle.fill);
        }
      }

      // Draw mass label
      final textStyle = TextStyle(
        color: textColor,
        fontSize: 10,
        fontWeight: FontWeight.bold,
      );

      final textSpan = TextSpan(
        text: '${object.mass.toStringAsFixed(0)} kg',
        style: textStyle,
      );

      final textPainter = TextPainter(
        text: textSpan,
        textDirection: TextDirection.ltr,
      );

      textPainter.layout();
      textPainter.paint(
        canvas,
        Offset(
          canvasPosition.dx - textPainter.width / 2,
          canvasPosition.dy - canvasRadius - textPainter.height - 2,
        ),
      );
    }
  }

  void _drawGrid(Canvas canvas, Size size) {
    final gridPaint = Paint()
      ..color = Colors.grey.withOpacity(0.2)
      ..strokeWidth = 0.5;

    // Draw horizontal grid lines
    final horizontalSpacing = size.height / 10;
    for (int i = 0; i <= 10; i++) {
      final y = i * horizontalSpacing;
      canvas.drawLine(
        Offset(0, y),
        Offset(size.width, y),
        gridPaint,
      );
    }

    // Draw vertical grid lines
    final verticalSpacing = size.width / 10;
    for (int i = 0; i <= 10; i++) {
      final x = i * verticalSpacing;
      canvas.drawLine(
        Offset(x, 0),
        Offset(x, size.height),
        gridPaint,
      );
    }
  }

  @override
  bool shouldRepaint(covariant CollisionSimulatorPainter oldDelegate) {
    return oldDelegate.objects != objects ||
        oldDelegate.showVelocityVectors != showVelocityVectors ||
        oldDelegate.showMomentumVectors != showMomentumVectors ||
        oldDelegate.animationValue != animationValue;
  }
}

class ConservationGraphPainter extends CustomPainter {
  final List<double> momentumHistory;
  final List<double> kineticEnergyHistory;
  final bool showMomentum;
  final bool showKineticEnergy;
  final Color primaryColor;
  final Color secondaryColor;
  final Color tertiaryColor;
  final Color textColor;
  final String collisionType;

  ConservationGraphPainter({
    required this.momentumHistory,
    required this.kineticEnergyHistory,
    required this.showMomentum,
    required this.showKineticEnergy,
    required this.primaryColor,
    required this.secondaryColor,
    required this.tertiaryColor,
    required this.textColor,
    required this.collisionType,
  });

  @override
  void paint(Canvas canvas, Size size) {
    // Draw axes
    final axesPaint = Paint()
      ..color = textColor.withOpacity(0.5)
      ..strokeWidth = 1.0;

    // X-axis (time)
    canvas.drawLine(
      Offset(0, size.height - 20),
      Offset(size.width, size.height - 20),
      axesPaint,
    );

    // Y-axis (value)
    canvas.drawLine(
      Offset(20, 0),
      Offset(20, size.height - 20),
      axesPaint,
    );

    // Draw labels
    final labelStyle = TextStyle(
      color: textColor,
      fontSize: 10,
    );

    // X-axis label
    final xLabelSpan = TextSpan(
      text: 'Time',
      style: labelStyle,
    );

    final xLabelPainter = TextPainter(
      text: xLabelSpan,
      textDirection: TextDirection.ltr,
    );

    xLabelPainter.layout();
    xLabelPainter.paint(
      canvas,
      Offset(
        size.width - xLabelPainter.width - 5,
        size.height - xLabelPainter.height - 2,
      ),
    );

    // Y-axis label
    final yLabelSpan = TextSpan(
      text: 'Value',
      style: labelStyle,
    );

    final yLabelPainter = TextPainter(
      text: yLabelSpan,
      textDirection: TextDirection.ltr,
    );

    yLabelPainter.layout();
    yLabelPainter.paint(
      canvas,
      Offset(
        5,
        5,
      ),
    );

    // Draw momentum graph if enabled
    if (showMomentum && momentumHistory.isNotEmpty) {
      _drawGraph(
        canvas,
        size,
        momentumHistory,
        secondaryColor,
        'Momentum',
        20,
      );
    }

    // Draw kinetic energy graph if enabled
    if (showKineticEnergy && kineticEnergyHistory.isNotEmpty) {
      _drawGraph(
        canvas,
        size,
        kineticEnergyHistory,
        tertiaryColor,
        'Kinetic Energy',
        40,
      );

      // Add note about energy conservation for different collision types
      final noteStyle = TextStyle(
        color: textColor.withOpacity(0.7),
        fontSize: 9,
        fontStyle: FontStyle.italic,
      );

      String noteText;
      switch (collisionType) {
        case 'elastic':
          noteText = 'In elastic collisions, both momentum and kinetic energy are conserved';
          break;
        case 'inelastic':
          noteText = 'In inelastic collisions, momentum is conserved but some kinetic energy is lost';
          break;
        case 'perfectly_inelastic':
          noteText = 'In perfectly inelastic collisions, momentum is conserved but maximum kinetic energy is lost';
          break;
        default:
          noteText = '';
      }

      final noteSpan = TextSpan(
        text: noteText,
        style: noteStyle,
      );

      final notePainter = TextPainter(
        text: noteSpan,
        textDirection: TextDirection.ltr,
      );

      notePainter.layout(maxWidth: size.width - 40);
      notePainter.paint(
        canvas,
        Offset(
          30,
          size.height - 15,
        ),
      );
    }
  }

  void _drawGraph(
    Canvas canvas,
    Size size,
    List<double> data,
    Color color,
    String label,
    double labelY,
  ) {
    if (data.isEmpty) return;

    // Find max value for scaling
    final maxValue = data.reduce((a, b) => math.max(a, b));

    // Calculate scale factors
    final graphWidth = size.width - 30;
    final graphHeight = size.height - 30;
    final xScale = graphWidth / (data.length - 1 > 0 ? data.length - 1 : 1);
    final yScale = maxValue > 0 ? (graphHeight - 20) / maxValue : 1;

    // Draw graph line
    final path = Path();
    final graphPaint = Paint()
      ..color = color
      ..style = PaintingStyle.stroke
      ..strokeWidth = 2.0;

    for (int i = 0; i < data.length; i++) {
      final x = 20 + i * xScale;
      final y = size.height - 20 - data[i] * yScale;

      if (i == 0) {
        path.moveTo(x, y);
      } else {
        path.lineTo(x, y);
      }
    }

    canvas.drawPath(path, graphPaint);

    // Draw label
    final labelStyle = TextStyle(
      color: color,
      fontSize: 10,
      fontWeight: FontWeight.bold,
    );

    final labelSpan = TextSpan(
      text: '$label: ${data.last.toStringAsFixed(1)}',
      style: labelStyle,
    );

    final labelPainter = TextPainter(
      text: labelSpan,
      textDirection: TextDirection.ltr,
    );

    labelPainter.layout();
    labelPainter.paint(
      canvas,
      Offset(
        25,
        labelY,
      ),
    );
  }

  @override
  bool shouldRepaint(covariant ConservationGraphPainter oldDelegate) {
    return oldDelegate.momentumHistory != momentumHistory ||
        oldDelegate.kineticEnergyHistory != kineticEnergyHistory ||
        oldDelegate.showMomentum != showMomentum ||
        oldDelegate.showKineticEnergy != showKineticEnergy ||
        oldDelegate.collisionType != collisionType;
  }
}
