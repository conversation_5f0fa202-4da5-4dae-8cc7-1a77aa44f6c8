import 'package:flutter/material.dart';

/// A widget that provides an interactive logic gate simulator
class InteractiveLogicGateSimulatorWidget extends StatefulWidget {
  /// The data for this widget
  final Map<String, dynamic> data;

  /// Optional callback when the widget state changes
  final Function(bool isCompleted)? onStateChanged;

  const InteractiveLogicGateSimulatorWidget({
    super.key,
    required this.data,
    this.onStateChanged,
  });

  /// Factory constructor to create the widget from JSON data
  factory InteractiveLogicGateSimulatorWidget.fromData(
      Map<String, dynamic> data) {
    return InteractiveLogicGateSimulatorWidget(
      data: data,
    );
  }

  @override
  State<InteractiveLogicGateSimulatorWidget> createState() =>
      _InteractiveLogicGateSimulatorWidgetState();
}

/// Logic gate model
class LogicGate {
  final String type;
  final String name;
  final String symbol;
  final String description;
  final List<bool> inputs;
  final int maxInputs;
  final Color color;

  LogicGate({
    required this.type,
    required this.name,
    required this.symbol,
    required this.description,
    required this.inputs,
    required this.maxInputs,
    required this.color,
  });

  /// Create a copy of the gate with new inputs
  LogicGate copyWith({List<bool>? inputs}) {
    return LogicGate(
      type: type,
      name: name,
      symbol: symbol,
      description: description,
      inputs: inputs ?? this.inputs,
      maxInputs: maxInputs,
      color: color,
    );
  }

  /// Calculate the output of the gate based on its type and inputs
  bool calculateOutput() {
    switch (type) {
      case 'AND':
        return inputs.every((input) => input);
      case 'OR':
        return inputs.any((input) => input);
      case 'NOT':
        return !inputs[0];
      case 'NAND':
        return !inputs.every((input) => input);
      case 'NOR':
        return !inputs.any((input) => input);
      case 'XOR':
        return inputs.where((input) => input).length % 2 == 1;
      case 'XNOR':
        return inputs.where((input) => input).length % 2 == 0;
      default:
        return false;
    }
  }

  /// Get the truth table for the gate
  List<List<bool>> getTruthTable() {
    final result = <List<bool>>[];

    // For NOT gate, only need 2 rows
    if (type == 'NOT') {
      result.add([false, true]);
      result.add([true, false]);
      return result;
    }

    // For 2-input gates, generate all combinations
    if (maxInputs == 2) {
      result.add([false, false, calculateOutputForInputs([false, false])]);
      result.add([false, true, calculateOutputForInputs([false, true])]);
      result.add([true, false, calculateOutputForInputs([true, false])]);
      result.add([true, true, calculateOutputForInputs([true, true])]);
    } else if (maxInputs == 3) {
      // For 3-input gates, generate all combinations
      result.add([false, false, false, calculateOutputForInputs([false, false, false])]);
      result.add([false, false, true, calculateOutputForInputs([false, false, true])]);
      result.add([false, true, false, calculateOutputForInputs([false, true, false])]);
      result.add([false, true, true, calculateOutputForInputs([false, true, true])]);
      result.add([true, false, false, calculateOutputForInputs([true, false, false])]);
      result.add([true, false, true, calculateOutputForInputs([true, false, true])]);
      result.add([true, true, false, calculateOutputForInputs([true, true, false])]);
      result.add([true, true, true, calculateOutputForInputs([true, true, true])]);
    }

    return result;
  }

  /// Calculate output for specific inputs
  bool calculateOutputForInputs(List<bool> specificInputs) {
    switch (type) {
      case 'AND':
        return specificInputs.every((input) => input);
      case 'OR':
        return specificInputs.any((input) => input);
      case 'NOT':
        return !specificInputs[0];
      case 'NAND':
        return !specificInputs.every((input) => input);
      case 'NOR':
        return !specificInputs.any((input) => input);
      case 'XOR':
        return specificInputs.where((input) => input).length % 2 == 1;
      case 'XNOR':
        return specificInputs.where((input) => input).length % 2 == 0;
      default:
        return false;
    }
  }
}

class _InteractiveLogicGateSimulatorWidgetState
    extends State<InteractiveLogicGateSimulatorWidget> {
  // Colors
  late Color _primaryColor;
  late Color _textColor;
  late Color _backgroundColor;

  // Logic gates
  late List<LogicGate> _gates;
  late int _currentGateIndex;

  // UI state
  bool _showTruthTable = true;
  bool _showDescription = true;

  @override
  void initState() {
    super.initState();

    // Initialize colors
    _primaryColor = _getColorFromHex(
        widget.data['primaryColor'] ?? '#2196F3'); // Blue
    _textColor =
        _getColorFromHex(widget.data['textColor'] ?? '#212121'); // Dark Grey
    _backgroundColor = _getColorFromHex(
        widget.data['backgroundColor'] ?? '#FFFFFF'); // White

    // Initialize logic gates
    _gates = _createLogicGates();
    _currentGateIndex = 0;
  }

  // Convert hex color string to Color
  Color _getColorFromHex(String hexColor) {
    hexColor = hexColor.replaceAll('#', '');
    if (hexColor.length == 6) {
      hexColor = 'FF$hexColor';
    }
    return Color(int.parse(hexColor, radix: 16));
  }

  // Create predefined logic gates
  List<LogicGate> _createLogicGates() {
    return [
      LogicGate(
        type: 'AND',
        name: 'AND Gate',
        symbol: '∧',
        description:
            'The AND gate outputs true only when all inputs are true. '
            'It performs logical conjunction.',
        inputs: [false, false],
        maxInputs: 3,
        color: Colors.blue,
      ),
      LogicGate(
        type: 'OR',
        name: 'OR Gate',
        symbol: '∨',
        description:
            'The OR gate outputs true when at least one input is true. '
            'It performs logical disjunction.',
        inputs: [false, false],
        maxInputs: 3,
        color: Colors.green,
      ),
      LogicGate(
        type: 'NOT',
        name: 'NOT Gate',
        symbol: '¬',
        description:
            'The NOT gate outputs the inverse of its input. '
            'It performs logical negation.',
        inputs: [false],
        maxInputs: 1,
        color: Colors.red,
      ),
      LogicGate(
        type: 'NAND',
        name: 'NAND Gate',
        symbol: '⊼',
        description:
            'The NAND gate outputs false only when all inputs are true. '
            'It is the negation of the AND gate.',
        inputs: [false, false],
        maxInputs: 3,
        color: Colors.purple,
      ),
      LogicGate(
        type: 'NOR',
        name: 'NOR Gate',
        symbol: '⊽',
        description:
            'The NOR gate outputs true only when all inputs are false. '
            'It is the negation of the OR gate.',
        inputs: [false, false],
        maxInputs: 3,
        color: Colors.orange,
      ),
      LogicGate(
        type: 'XOR',
        name: 'XOR Gate',
        symbol: '⊕',
        description:
            'The XOR gate outputs true when an odd number of inputs are true. '
            'It performs exclusive disjunction.',
        inputs: [false, false],
        maxInputs: 2,
        color: Colors.teal,
      ),
      LogicGate(
        type: 'XNOR',
        name: 'XNOR Gate',
        symbol: '⊙',
        description:
            'The XNOR gate outputs true when an even number of inputs are true. '
            'It is the negation of the XOR gate.',
        inputs: [false, false],
        maxInputs: 2,
        color: Colors.amber,
      ),
    ];
  }

  // Toggle an input value
  void _toggleInput(int inputIndex) {
    if (inputIndex < _gates[_currentGateIndex].inputs.length) {
      setState(() {
        final currentGate = _gates[_currentGateIndex];
        final newInputs = List<bool>.from(currentGate.inputs);
        newInputs[inputIndex] = !newInputs[inputIndex];
        _gates[_currentGateIndex] = currentGate.copyWith(inputs: newInputs);
      });
    }
  }

  // Add an input to the current gate
  void _addInput() {
    final currentGate = _gates[_currentGateIndex];
    if (currentGate.inputs.length < currentGate.maxInputs) {
      setState(() {
        final newInputs = List<bool>.from(currentGate.inputs)..add(false);
        _gates[_currentGateIndex] = currentGate.copyWith(inputs: newInputs);
      });
    }
  }

  // Remove an input from the current gate
  void _removeInput() {
    final currentGate = _gates[_currentGateIndex];
    if (currentGate.inputs.length > 1 &&
        (currentGate.type != 'NOT' || currentGate.inputs.length > 1)) {
      setState(() {
        final newInputs = List<bool>.from(currentGate.inputs)..removeLast();
        _gates[_currentGateIndex] = currentGate.copyWith(inputs: newInputs);
      });
    }
  }

  // Build gate selector
  Widget _buildGateSelector() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Select Gate',
          style: TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.bold,
            color: _textColor,
          ),
        ),
        const SizedBox(height: 8),
        SingleChildScrollView(
          scrollDirection: Axis.horizontal,
          child: Row(
            children: List.generate(_gates.length, (index) {
              final isSelected = index == _currentGateIndex;
              return Padding(
                padding: const EdgeInsets.only(right: 8),
                child: ChoiceChip(
                  label: Text(_gates[index].name),
                  selected: isSelected,
                  onSelected: (selected) {
                    if (selected) {
                      setState(() {
                        _currentGateIndex = index;
                      });
                    }
                  },
                  backgroundColor: Colors.grey.withAlpha(50),
                  selectedColor: _gates[index].color.withAlpha(100),
                ),
              );
            }),
          ),
        ),
      ],
    );
  }

  // Build gate simulator
  Widget _buildGateSimulator(LogicGate gate, bool output) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: _backgroundColor,
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: Colors.grey.withAlpha(100)),
      ),
      child: Column(
        children: [
          // Gate symbol and name
          Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: gate.color.withAlpha(50),
                  borderRadius: BorderRadius.circular(8),
                  border: Border.all(color: gate.color),
                ),
                child: Text(
                  gate.symbol,
                  style: TextStyle(
                    fontSize: 24,
                    fontWeight: FontWeight.bold,
                    color: gate.color,
                  ),
                ),
              ),
              const SizedBox(width: 16),
              Text(
                gate.name,
                style: TextStyle(
                  fontSize: 20,
                  fontWeight: FontWeight.bold,
                  color: _textColor,
                ),
              ),
            ],
          ),
          const SizedBox(height: 24),

          // Inputs and output
          Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              // Inputs
              Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'Inputs',
                    style: TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.bold,
                      color: _textColor,
                    ),
                  ),
                  const SizedBox(height: 8),
                  ...List.generate(gate.inputs.length, (index) {
                    return Padding(
                      padding: const EdgeInsets.only(bottom: 8),
                      child: Row(
                        children: [
                          Text(
                            'Input ${index + 1}:',
                            style: TextStyle(
                              color: _textColor,
                            ),
                          ),
                          const SizedBox(width: 8),
                          Switch(
                            value: gate.inputs[index],
                            onChanged: (value) => _toggleInput(index),
                            activeColor: gate.color,
                          ),
                          const SizedBox(width: 8),
                          Container(
                            padding: const EdgeInsets.symmetric(
                              horizontal: 12,
                              vertical: 6,
                            ),
                            decoration: BoxDecoration(
                              color: gate.inputs[index]
                                  ? Colors.green.withAlpha(50)
                                  : Colors.red.withAlpha(50),
                              borderRadius: BorderRadius.circular(4),
                              border: Border.all(
                                color: gate.inputs[index]
                                    ? Colors.green
                                    : Colors.red,
                              ),
                            ),
                            child: Text(
                              gate.inputs[index] ? '1' : '0',
                              style: TextStyle(
                                fontWeight: FontWeight.bold,
                                color: gate.inputs[index]
                                    ? Colors.green
                                    : Colors.red,
                              ),
                            ),
                          ),
                        ],
                      ),
                    );
                  }),
                  // Input controls
                  if (gate.type != 'NOT')
                    Row(
                      children: [
                        IconButton(
                          onPressed: gate.inputs.length < gate.maxInputs
                              ? _addInput
                              : null,
                          icon: const Icon(Icons.add_circle),
                          tooltip: 'Add input',
                          color: _primaryColor,
                        ),
                        IconButton(
                          onPressed: gate.inputs.length > 1 ? _removeInput : null,
                          icon: const Icon(Icons.remove_circle),
                          tooltip: 'Remove input',
                          color: Colors.red,
                        ),
                      ],
                    ),
                ],
              ),

              // Arrow
              Padding(
                padding: const EdgeInsets.symmetric(horizontal: 24),
                child: Icon(
                  Icons.arrow_forward,
                  color: _textColor,
                  size: 32,
                ),
              ),

              // Output
              Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'Output',
                    style: TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.bold,
                      color: _textColor,
                    ),
                  ),
                  const SizedBox(height: 8),
                  Container(
                    padding: const EdgeInsets.all(16),
                    decoration: BoxDecoration(
                      color: output
                          ? Colors.green.withAlpha(50)
                          : Colors.red.withAlpha(50),
                      borderRadius: BorderRadius.circular(8),
                      border: Border.all(
                        color: output ? Colors.green : Colors.red,
                      ),
                    ),
                    child: Text(
                      output ? '1' : '0',
                      style: TextStyle(
                        fontSize: 24,
                        fontWeight: FontWeight.bold,
                        color: output ? Colors.green : Colors.red,
                      ),
                    ),
                  ),
                ],
              ),
            ],
          ),
        ],
      ),
    );
  }

  // Build controls
  Widget _buildControls() {
    return Row(
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        // Toggle truth table button
        OutlinedButton.icon(
          onPressed: () {
            setState(() {
              _showTruthTable = !_showTruthTable;
            });
          },
          icon: Icon(_showTruthTable ? Icons.visibility_off : Icons.visibility),
          label: Text(_showTruthTable ? 'Hide Truth Table' : 'Show Truth Table'),
          style: OutlinedButton.styleFrom(
            foregroundColor: _primaryColor,
          ),
        ),
        const SizedBox(width: 16),
        // Toggle description button
        OutlinedButton.icon(
          onPressed: () {
            setState(() {
              _showDescription = !_showDescription;
            });
          },
          icon: Icon(_showDescription ? Icons.visibility_off : Icons.visibility),
          label: Text(_showDescription ? 'Hide Description' : 'Show Description'),
          style: OutlinedButton.styleFrom(
            foregroundColor: _primaryColor,
          ),
        ),
      ],
    );
  }

  // Build truth table
  Widget _buildTruthTable(LogicGate gate) {
    final truthTable = gate.getTruthTable();

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Truth Table',
          style: TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.bold,
            color: _textColor,
          ),
        ),
        const SizedBox(height: 8),
        Container(
          decoration: BoxDecoration(
            color: Colors.grey.withAlpha(20),
            borderRadius: BorderRadius.circular(8),
            border: Border.all(color: Colors.grey.withAlpha(100)),
          ),
          child: SingleChildScrollView(
            scrollDirection: Axis.horizontal,
            child: DataTable(
              columns: [
                ...List.generate(
                  gate.type == 'NOT' ? 1 : truthTable[0].length - 1,
                  (index) => DataColumn(
                    label: Text(
                      'Input ${index + 1}',
                      style: TextStyle(
                        fontWeight: FontWeight.bold,
                        color: _textColor,
                      ),
                    ),
                  ),
                ),
                DataColumn(
                  label: Text(
                    'Output',
                    style: TextStyle(
                      fontWeight: FontWeight.bold,
                      color: _textColor,
                    ),
                  ),
                ),
              ],
              rows: truthTable.map((row) {
                return DataRow(
                  cells: [
                    ...List.generate(
                      row.length,
                      (index) => DataCell(
                        Container(
                          padding: const EdgeInsets.symmetric(
                            horizontal: 12,
                            vertical: 6,
                          ),
                          decoration: BoxDecoration(
                            color: row[index]
                                ? Colors.green.withAlpha(50)
                                : Colors.red.withAlpha(50),
                            borderRadius: BorderRadius.circular(4),
                            border: Border.all(
                              color: row[index] ? Colors.green : Colors.red,
                            ),
                          ),
                          child: Text(
                            row[index] ? '1' : '0',
                            style: TextStyle(
                              fontWeight: FontWeight.bold,
                              color: row[index] ? Colors.green : Colors.red,
                            ),
                          ),
                        ),
                      ),
                    ),
                  ],
                );
              }).toList(),
            ),
          ),
        ),
      ],
    );
  }

  // Build gate information
  Widget _buildGateInfo(LogicGate gate) {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: gate.color.withAlpha(20),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: gate.color.withAlpha(100)),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            gate.name,
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
              color: _textColor,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            gate.description,
            style: TextStyle(
              color: _textColor,
              fontSize: 14,
            ),
          ),
          const SizedBox(height: 8),
          Align(
            alignment: Alignment.centerRight,
            child: TextButton(
              onPressed: () {
                widget.onStateChanged?.call(true);
              },
              child: const Text('Mark as Completed'),
            ),
          ),
        ],
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    final currentGate = _gates[_currentGateIndex];
    final output = currentGate.calculateOutput();

    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
        side: BorderSide(color: _primaryColor.withAlpha(77)),
      ),
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Title and description
            Text(
              widget.data['title'] ?? 'Logic Gate Simulator',
              style: TextStyle(
                fontSize: 20,
                fontWeight: FontWeight.bold,
                color: _textColor,
              ),
            ),
            const SizedBox(height: 8),
            Text(
              widget.data['description'] ??
                  'Explore logic gates and their behavior',
              style: TextStyle(
                fontSize: 14,
                color: _textColor.withAlpha(179),
              ),
            ),
            const SizedBox(height: 16),

            // Gate selector
            _buildGateSelector(),

            const SizedBox(height: 16),

            // Gate simulator
            _buildGateSimulator(currentGate, output),

            const SizedBox(height: 16),

            // Controls
            _buildControls(),

            const SizedBox(height: 16),

            // Truth table
            if (_showTruthTable) _buildTruthTable(currentGate),

            if (_showTruthTable) const SizedBox(height: 16),

            // Gate information
            if (_showDescription) _buildGateInfo(currentGate),
          ],
        ),
      ),
    );
  }
}
