import 'package:flutter/material.dart';

/// Utility class for standardized widget animations
/// Provides consistent animation behaviors across all interactive widgets
class WidgetAnimations {
  // Private constructor to prevent instantiation
  WidgetAnimations._();

  // ============================================================================
  // ANIMATION DURATIONS
  // ============================================================================
  
  static const Duration fast = Duration(milliseconds: 150);
  static const Duration medium = Duration(milliseconds: 300);
  static const Duration slow = Duration(milliseconds: 500);
  static const Duration extraSlow = Duration(milliseconds: 800);

  // ============================================================================
  // ANIMATION CURVES
  // ============================================================================
  
  static const Curve easeInOut = Curves.easeInOut;
  static const Curve easeOut = Curves.easeOut;
  static const Curve bounceOut = Curves.bounceOut;
  static const Curve elasticOut = Curves.elasticOut;

  // ============================================================================
  // BUTTON ANIMATIONS
  // ============================================================================
  
  /// Creates a scale animation for button press feedback
  static Widget buttonPressAnimation({
    required Widget child,
    required VoidCallback onPressed,
    double scaleFactor = 0.95,
    Duration duration = fast,
  }) {
    return _ScaleAnimationWrapper(
      scaleFactor: scaleFactor,
      duration: duration,
      onPressed: onPressed,
      child: child,
    );
  }

  /// Creates a ripple effect for button interactions
  static Widget buttonRippleAnimation({
    required Widget child,
    required VoidCallback onPressed,
    Color? rippleColor,
    BorderRadius? borderRadius,
  }) {
    return Material(
      color: Colors.transparent,
      child: InkWell(
        onTap: onPressed,
        splashColor: rippleColor?.withOpacity(0.3),
        highlightColor: rippleColor?.withOpacity(0.1),
        borderRadius: borderRadius ?? BorderRadius.circular(8),
        child: child,
      ),
    );
  }

  // ============================================================================
  // FEEDBACK ANIMATIONS
  // ============================================================================
  
  /// Creates a success animation with scale and color transition
  static Widget successAnimation({
    required Widget child,
    required bool isSuccess,
    Duration duration = medium,
  }) {
    return AnimatedContainer(
      duration: duration,
      curve: easeInOut,
      transform: Matrix4.identity()..scale(isSuccess ? 1.05 : 1.0),
      child: AnimatedOpacity(
        duration: duration,
        opacity: isSuccess ? 1.0 : 0.8,
        child: child,
      ),
    );
  }

  /// Creates an error shake animation
  static Widget errorShakeAnimation({
    required Widget child,
    required bool triggerShake,
    Duration duration = fast,
  }) {
    return _ShakeAnimationWrapper(
      triggerShake: triggerShake,
      duration: duration,
      child: child,
    );
  }

  // ============================================================================
  // REVEAL ANIMATIONS
  // ============================================================================
  
  /// Creates a fade-in animation for content reveal
  static Widget fadeInAnimation({
    required Widget child,
    required bool isVisible,
    Duration duration = medium,
    Curve curve = easeInOut,
  }) {
    return AnimatedOpacity(
      duration: duration,
      curve: curve,
      opacity: isVisible ? 1.0 : 0.0,
      child: child,
    );
  }

  /// Creates a slide-in animation from bottom
  static Widget slideInFromBottom({
    required Widget child,
    required bool isVisible,
    Duration duration = medium,
    Curve curve = easeOut,
  }) {
    return AnimatedSlide(
      duration: duration,
      curve: curve,
      offset: isVisible ? Offset.zero : const Offset(0, 1),
      child: child,
    );
  }

  /// Creates a slide-in animation from right
  static Widget slideInFromRight({
    required Widget child,
    required bool isVisible,
    Duration duration = medium,
    Curve curve = easeOut,
  }) {
    return AnimatedSlide(
      duration: duration,
      curve: curve,
      offset: isVisible ? Offset.zero : const Offset(1, 0),
      child: child,
    );
  }

  // ============================================================================
  // PROGRESS ANIMATIONS
  // ============================================================================
  
  /// Creates an animated progress bar
  static Widget progressBarAnimation({
    required double progress,
    required Color color,
    Color? backgroundColor,
    Duration duration = medium,
    double height = 4,
    BorderRadius? borderRadius,
  }) {
    return Container(
      height: height,
      decoration: BoxDecoration(
        color: backgroundColor ?? Colors.grey[200],
        borderRadius: borderRadius ?? BorderRadius.circular(height / 2),
      ),
      child: AnimatedFractionallySizedBox(
        duration: duration,
        curve: easeInOut,
        alignment: Alignment.centerLeft,
        widthFactor: progress.clamp(0.0, 1.0),
        child: Container(
          decoration: BoxDecoration(
            color: color,
            borderRadius: borderRadius ?? BorderRadius.circular(height / 2),
          ),
        ),
      ),
    );
  }

  // ============================================================================
  // LOADING ANIMATIONS
  // ============================================================================
  
  /// Creates a pulsing animation for loading states
  static Widget pulseAnimation({
    required Widget child,
    Duration duration = slow,
    double minOpacity = 0.5,
    double maxOpacity = 1.0,
  }) {
    return _PulseAnimationWrapper(
      duration: duration,
      minOpacity: minOpacity,
      maxOpacity: maxOpacity,
      child: child,
    );
  }

  /// Creates a rotating animation for loading indicators
  static Widget rotateAnimation({
    required Widget child,
    Duration duration = const Duration(seconds: 2),
  }) {
    return _RotateAnimationWrapper(
      duration: duration,
      child: child,
    );
  }

  // ============================================================================
  // TRANSITION ANIMATIONS
  // ============================================================================
  
  /// Creates a page transition animation
  static PageRouteBuilder pageTransition({
    required Widget page,
    TransitionType type = TransitionType.slideFromRight,
    Duration duration = medium,
  }) {
    return PageRouteBuilder(
      pageBuilder: (context, animation, secondaryAnimation) => page,
      transitionDuration: duration,
      transitionsBuilder: (context, animation, secondaryAnimation, child) {
        switch (type) {
          case TransitionType.slideFromRight:
            return SlideTransition(
              position: animation.drive(
                Tween(begin: const Offset(1.0, 0.0), end: Offset.zero),
              ),
              child: child,
            );
          case TransitionType.slideFromBottom:
            return SlideTransition(
              position: animation.drive(
                Tween(begin: const Offset(0.0, 1.0), end: Offset.zero),
              ),
              child: child,
            );
          case TransitionType.fade:
            return FadeTransition(opacity: animation, child: child);
          case TransitionType.scale:
            return ScaleTransition(scale: animation, child: child);
        }
      },
    );
  }
}

/// Enum for different transition types
enum TransitionType {
  slideFromRight,
  slideFromBottom,
  fade,
  scale,
}

// ============================================================================
// PRIVATE ANIMATION WRAPPER CLASSES
// ============================================================================

class _ScaleAnimationWrapper extends StatefulWidget {
  final Widget child;
  final VoidCallback onPressed;
  final double scaleFactor;
  final Duration duration;

  const _ScaleAnimationWrapper({
    required this.child,
    required this.onPressed,
    required this.scaleFactor,
    required this.duration,
  });

  @override
  State<_ScaleAnimationWrapper> createState() => _ScaleAnimationWrapperState();
}

class _ScaleAnimationWrapperState extends State<_ScaleAnimationWrapper>
    with SingleTickerProviderStateMixin {
  late AnimationController _controller;
  late Animation<double> _scaleAnimation;

  @override
  void initState() {
    super.initState();
    _controller = AnimationController(duration: widget.duration, vsync: this);
    _scaleAnimation = Tween<double>(
      begin: 1.0,
      end: widget.scaleFactor,
    ).animate(CurvedAnimation(parent: _controller, curve: Curves.easeInOut));
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTapDown: (_) => _controller.forward(),
      onTapUp: (_) {
        _controller.reverse();
        widget.onPressed();
      },
      onTapCancel: () => _controller.reverse(),
      child: AnimatedBuilder(
        animation: _scaleAnimation,
        builder: (context, child) {
          return Transform.scale(
            scale: _scaleAnimation.value,
            child: widget.child,
          );
        },
      ),
    );
  }
}

class _ShakeAnimationWrapper extends StatefulWidget {
  final Widget child;
  final bool triggerShake;
  final Duration duration;

  const _ShakeAnimationWrapper({
    required this.child,
    required this.triggerShake,
    required this.duration,
  });

  @override
  State<_ShakeAnimationWrapper> createState() => _ShakeAnimationWrapperState();
}

class _ShakeAnimationWrapperState extends State<_ShakeAnimationWrapper>
    with SingleTickerProviderStateMixin {
  late AnimationController _controller;
  late Animation<double> _shakeAnimation;

  @override
  void initState() {
    super.initState();
    _controller = AnimationController(duration: widget.duration, vsync: this);
    _shakeAnimation = Tween<double>(begin: 0, end: 1).animate(_controller);
  }

  @override
  void didUpdateWidget(_ShakeAnimationWrapper oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (widget.triggerShake && !oldWidget.triggerShake) {
      _controller.forward().then((_) => _controller.reset());
    }
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return AnimatedBuilder(
      animation: _shakeAnimation,
      builder: (context, child) {
        return Transform.translate(
          offset: Offset(
            10 * _shakeAnimation.value * (1 - _shakeAnimation.value) * 4,
            0,
          ),
          child: widget.child,
        );
      },
    );
  }
}

class _PulseAnimationWrapper extends StatefulWidget {
  final Widget child;
  final Duration duration;
  final double minOpacity;
  final double maxOpacity;

  const _PulseAnimationWrapper({
    required this.child,
    required this.duration,
    required this.minOpacity,
    required this.maxOpacity,
  });

  @override
  State<_PulseAnimationWrapper> createState() => _PulseAnimationWrapperState();
}

class _PulseAnimationWrapperState extends State<_PulseAnimationWrapper>
    with SingleTickerProviderStateMixin {
  late AnimationController _controller;
  late Animation<double> _opacityAnimation;

  @override
  void initState() {
    super.initState();
    _controller = AnimationController(duration: widget.duration, vsync: this);
    _opacityAnimation = Tween<double>(
      begin: widget.minOpacity,
      end: widget.maxOpacity,
    ).animate(CurvedAnimation(parent: _controller, curve: Curves.easeInOut));
    _controller.repeat(reverse: true);
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return AnimatedBuilder(
      animation: _opacityAnimation,
      builder: (context, child) {
        return Opacity(
          opacity: _opacityAnimation.value,
          child: widget.child,
        );
      },
    );
  }
}

class _RotateAnimationWrapper extends StatefulWidget {
  final Widget child;
  final Duration duration;

  const _RotateAnimationWrapper({
    required this.child,
    required this.duration,
  });

  @override
  State<_RotateAnimationWrapper> createState() => _RotateAnimationWrapperState();
}

class _RotateAnimationWrapperState extends State<_RotateAnimationWrapper>
    with SingleTickerProviderStateMixin {
  late AnimationController _controller;

  @override
  void initState() {
    super.initState();
    _controller = AnimationController(duration: widget.duration, vsync: this);
    _controller.repeat();
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return AnimatedBuilder(
      animation: _controller,
      builder: (context, child) {
        return Transform.rotate(
          angle: _controller.value * 2 * 3.14159,
          child: widget.child,
        );
      },
    );
  }
}
