import 'package:flutter/material.dart';
import 'dart:math' as math;

/// A widget that presents a game to identify geometric transformations
class InteractiveTransformationIdentificationGameWidget extends StatefulWidget {
  final Map<String, dynamic> data;
  final Function(bool)? onStateChanged;

  const InteractiveTransformationIdentificationGameWidget({
    Key? key,
    required this.data,
    this.onStateChanged,
  }) : super(key: key);

  /// Creates the widget from JSON data
  static InteractiveTransformationIdentificationGameWidget fromData(Map<String, dynamic> data) {
    return InteractiveTransformationIdentificationGameWidget(
      data: data,
    );
  }

  @override
  State<InteractiveTransformationIdentificationGameWidget> createState() => _InteractiveTransformationIdentificationGameWidgetState();
}

class _InteractiveTransformationIdentificationGameWidgetState extends State<InteractiveTransformationIdentificationGameWidget> {
  // Game state
  late String _correctTransformation;
  String? _selectedTransformation;
  bool _showFeedback = false;
  bool _isCorrect = false;
  
  // Figure images
  late String _figureASrc;
  late String _figureBSrc;
  
  // Colors
  late Color _primaryColor;
  late Color _correctColor;
  late Color _incorrectColor;
  late Color _textColor;
  
  // Transformation options
  late List<String> _transformationOptions;
  
  // Feedback text
  late String _correctFeedback;
  late String _incorrectFeedback;

  @override
  void initState() {
    super.initState();
    _initializeWidget();
  }

  void _initializeWidget() {
    // Set figure images
    _figureASrc = widget.data['figure_a_src'] ?? 'assets/images/geo_puzzles/default_figure_a.svg';
    _figureBSrc = widget.data['figure_b_src'] ?? 'assets/images/geo_puzzles/default_figure_b.svg';
    
    // Set correct transformation
    _correctTransformation = widget.data['correct_transformation'] ?? 'translation';
    
    // Set transformation options
    _transformationOptions = List<String>.from(widget.data['transformation_options'] ?? [
      'translation',
      'rotation',
      'reflection',
      'dilation',
    ]);
    
    // Set colors
    _primaryColor = _parseColor(widget.data['primaryColor']) ?? Colors.blue;
    _correctColor = _parseColor(widget.data['correctColor']) ?? Colors.green;
    _incorrectColor = _parseColor(widget.data['incorrectColor']) ?? Colors.red;
    _textColor = _parseColor(widget.data['textColor']) ?? Colors.black87;
    
    // Set feedback text
    _correctFeedback = widget.data['correctFeedback'] ?? 'Correct! The transformation is $_correctTransformation.';
    _incorrectFeedback = widget.data['incorrectFeedback'] ?? 'Not quite. Try again!';
  }

  // Parse color from hex string
  Color? _parseColor(String? hexString) {
    if (hexString == null) return null;
    
    hexString = hexString.replaceAll('#', '');
    if (hexString.length == 6) {
      hexString = 'FF$hexString';
    }
    
    return Color(int.parse(hexString, radix: 16));
  }

  // Handle transformation selection
  void _selectTransformation(String transformation) {
    setState(() {
      _selectedTransformation = transformation;
      _showFeedback = true;
      _isCorrect = transformation == _correctTransformation;
      
      // Notify parent about completion if correct
      if (_isCorrect) {
        widget.onStateChanged?.call(true);
      }
    });
  }

  // Reset the game
  void _resetGame() {
    setState(() {
      _selectedTransformation = null;
      _showFeedback = false;
    });
  }

  // Get transformation display name
  String _getTransformationDisplayName(String transformation) {
    switch (transformation) {
      case 'translation':
        return 'Translation (Slide)';
      case 'rotation':
        return 'Rotation (Turn)';
      case 'reflection':
        return 'Reflection (Flip)';
      case 'dilation':
        return 'Dilation (Resize)';
      default:
        return transformation;
    }
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withOpacity(0.2),
            spreadRadius: 1,
            blurRadius: 3,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Title
          Text(
            widget.data['title'] ?? 'Transformation Identification Game',
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
              color: _textColor,
            ),
          ),
          
          const SizedBox(height: 8),
          
          // Description
          if (widget.data['description'] != null)
            Text(
              widget.data['description'],
              style: TextStyle(
                fontSize: 14,
                color: _textColor.withOpacity(0.8),
              ),
            ),
          
          const SizedBox(height: 16),
          
          // Figures display
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceEvenly,
            children: [
              // Figure A
              Column(
                children: [
                  Text(
                    'Figure A',
                    style: TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.bold,
                      color: _textColor,
                    ),
                  ),
                  const SizedBox(height: 8),
                  Container(
                    width: 120,
                    height: 120,
                    decoration: BoxDecoration(
                      border: Border.all(color: Colors.grey),
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: Image.asset(
                      _figureASrc,
                      fit: BoxFit.contain,
                      errorBuilder: (context, error, stackTrace) {
                        return Center(
                          child: Icon(
                            Icons.image_not_supported,
                            color: Colors.grey,
                            size: 40,
                          ),
                        );
                      },
                    ),
                  ),
                ],
              ),
              
              // Arrow
              Icon(
                Icons.arrow_forward,
                size: 40,
                color: _primaryColor,
              ),
              
              // Figure B
              Column(
                children: [
                  Text(
                    'Figure B',
                    style: TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.bold,
                      color: _textColor,
                    ),
                  ),
                  const SizedBox(height: 8),
                  Container(
                    width: 120,
                    height: 120,
                    decoration: BoxDecoration(
                      border: Border.all(color: Colors.grey),
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: Image.asset(
                      _figureBSrc,
                      fit: BoxFit.contain,
                      errorBuilder: (context, error, stackTrace) {
                        return Center(
                          child: Icon(
                            Icons.image_not_supported,
                            color: Colors.grey,
                            size: 40,
                          ),
                        );
                      },
                    ),
                  ),
                ],
              ),
            ],
          ),
          
          const SizedBox(height: 24),
          
          // Question
          Text(
            widget.data['question'] ?? 'What single transformation maps Figure A to Figure B?',
            style: TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.bold,
              color: _textColor,
            ),
          ),
          
          const SizedBox(height: 16),
          
          // Transformation options
          ...List.generate(_transformationOptions.length, (index) {
            final transformation = _transformationOptions[index];
            final isSelected = _selectedTransformation == transformation;
            final isCorrect = transformation == _correctTransformation;
            
            Color buttonColor = _primaryColor;
            if (_showFeedback) {
              if (isSelected) {
                buttonColor = isCorrect ? _correctColor : _incorrectColor;
              } else if (isCorrect) {
                buttonColor = _correctColor;
              }
            }
            
            return Padding(
              padding: const EdgeInsets.only(bottom: 8),
              child: ElevatedButton(
                onPressed: _showFeedback ? null : () => _selectTransformation(transformation),
                style: ElevatedButton.styleFrom(
                  backgroundColor: buttonColor,
                  padding: const EdgeInsets.symmetric(vertical: 12, horizontal: 16),
                  minimumSize: const Size(double.infinity, 0),
                ),
                child: Row(
                  children: [
                    if (_showFeedback && isCorrect)
                      const Icon(Icons.check_circle, color: Colors.white),
                    if (_showFeedback && isSelected && !isCorrect)
                      const Icon(Icons.cancel, color: Colors.white),
                    const SizedBox(width: 8),
                    Text(
                      _getTransformationDisplayName(transformation),
                      style: const TextStyle(
                        fontSize: 16,
                        color: Colors.white,
                      ),
                    ),
                  ],
                ),
              ),
            );
          }),
          
          const SizedBox(height: 16),
          
          // Feedback
          if (_showFeedback)
            Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: _isCorrect ? _correctColor.withOpacity(0.1) : _incorrectColor.withOpacity(0.1),
                borderRadius: BorderRadius.circular(8),
                border: Border.all(
                  color: _isCorrect ? _correctColor : _incorrectColor,
                ),
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    children: [
                      Icon(
                        _isCorrect ? Icons.check_circle : Icons.cancel,
                        color: _isCorrect ? _correctColor : _incorrectColor,
                      ),
                      const SizedBox(width: 8),
                      Text(
                        _isCorrect ? 'Correct!' : 'Incorrect',
                        style: TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.bold,
                          color: _isCorrect ? _correctColor : _incorrectColor,
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 8),
                  Text(
                    _isCorrect ? _correctFeedback : _incorrectFeedback,
                    style: TextStyle(
                      color: _textColor,
                    ),
                  ),
                  if (!_isCorrect)
                    Padding(
                      padding: const EdgeInsets.only(top: 8),
                      child: ElevatedButton(
                        onPressed: _resetGame,
                        style: ElevatedButton.styleFrom(
                          backgroundColor: _primaryColor,
                        ),
                        child: const Text('Try Again'),
                      ),
                    ),
                ],
              ),
            ),
          
          // Widget name tag
          if (widget.data['showNameTag'] ?? true)
            Padding(
              padding: const EdgeInsets.only(top: 8),
              child: Text(
                'InteractiveTransformationIdentificationGameWidget',
                style: TextStyle(
                  color: Colors.grey[600],
                  fontSize: 12,
                  fontStyle: FontStyle.italic,
                ),
              ),
            ),
        ],
      ),
    );
  }
}

/// A simple widget to display transformation examples
class TransformationExampleWidget extends StatelessWidget {
  final String transformationType;
  final Color color;
  
  const TransformationExampleWidget({
    Key? key,
    required this.transformationType,
    required this.color,
  }) : super(key: key);
  
  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(8),
      decoration: BoxDecoration(
        color: color.withOpacity(0.1),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: color),
      ),
      child: Column(
        children: [
          Text(
            transformationType,
            style: TextStyle(
              fontWeight: FontWeight.bold,
              color: color,
            ),
          ),
          const SizedBox(height: 8),
          CustomPaint(
            size: const Size(100, 60),
            painter: TransformationExamplePainter(
              transformationType: transformationType,
              color: color,
            ),
          ),
        ],
      ),
    );
  }
}

/// Custom painter for transformation examples
class TransformationExamplePainter extends CustomPainter {
  final String transformationType;
  final Color color;
  
  TransformationExamplePainter({
    required this.transformationType,
    required this.color,
  });
  
  @override
  void paint(Canvas canvas, Size size) {
    final paint = Paint()
      ..color = color
      ..style = PaintingStyle.stroke
      ..strokeWidth = 2;
    
    final fillPaint = Paint()
      ..color = color.withOpacity(0.2)
      ..style = PaintingStyle.fill;
    
    // Draw the original shape (a triangle)
    final originalPath = Path()
      ..moveTo(20, 40)
      ..lineTo(40, 10)
      ..lineTo(60, 40)
      ..close();
    
    canvas.drawPath(originalPath, fillPaint);
    canvas.drawPath(originalPath, paint);
    
    // Draw the transformed shape based on transformation type
    switch (transformationType.toLowerCase()) {
      case 'translation':
        // Draw translated triangle (moved right)
        final translatedPath = Path()
          ..moveTo(40, 40)
          ..lineTo(60, 10)
          ..lineTo(80, 40)
          ..close();
        
        canvas.drawPath(translatedPath, fillPaint..color = color.withOpacity(0.5));
        canvas.drawPath(translatedPath, paint..color = color.withOpacity(0.8));
        
        // Draw arrow
        canvas.drawLine(
          const Offset(60, 25),
          const Offset(70, 25),
          paint..color = color,
        );
        canvas.drawLine(
          const Offset(70, 25),
          const Offset(65, 20),
          paint..color = color,
        );
        canvas.drawLine(
          const Offset(70, 25),
          const Offset(65, 30),
          paint..color = color,
        );
        break;
        
      case 'rotation':
        // Save canvas state
        canvas.save();
        
        // Translate to the center of rotation
        canvas.translate(40, 25);
        
        // Rotate canvas
        canvas.rotate(math.pi / 4); // 45 degrees
        
        // Draw rotated triangle
        final rotatedPath = Path()
          ..moveTo(-20, 15)
          ..lineTo(0, -15)
          ..lineTo(20, 15)
          ..close();
        
        canvas.drawPath(rotatedPath, fillPaint..color = color.withOpacity(0.5));
        canvas.drawPath(rotatedPath, paint..color = color.withOpacity(0.8));
        
        // Restore canvas state
        canvas.restore();
        
        // Draw rotation arc
        final arcRect = Rect.fromCircle(center: const Offset(40, 25), radius: 15);
        canvas.drawArc(
          arcRect,
          -math.pi / 2,
          math.pi / 4,
          false,
          paint..color = color,
        );
        break;
        
      case 'reflection':
        // Draw reflected triangle (flipped horizontally)
        final reflectedPath = Path()
          ..moveTo(80, 40)
          ..lineTo(60, 10)
          ..lineTo(40, 40)
          ..close();
        
        canvas.drawPath(reflectedPath, fillPaint..color = color.withOpacity(0.5));
        canvas.drawPath(reflectedPath, paint..color = color.withOpacity(0.8));
        
        // Draw reflection line
        canvas.drawLine(
          const Offset(60, 5),
          const Offset(60, 45),
          paint..color = color..strokeWidth = 1..strokeCap = StrokeCap.round,
        );
        break;
        
      case 'dilation':
        // Draw dilated triangle (scaled up)
        final dilatedPath = Path()
          ..moveTo(10, 50)
          ..lineTo(40, 5)
          ..lineTo(70, 50)
          ..close();
        
        canvas.drawPath(dilatedPath, fillPaint..color = color.withOpacity(0.5));
        canvas.drawPath(dilatedPath, paint..color = color.withOpacity(0.8));
        
        // Draw scaling arrows
        canvas.drawLine(
          const Offset(40, 25),
          const Offset(30, 15),
          paint..color = color,
        );
        canvas.drawLine(
          const Offset(40, 25),
          const Offset(50, 15),
          paint..color = color,
        );
        canvas.drawLine(
          const Offset(40, 25),
          const Offset(30, 35),
          paint..color = color,
        );
        canvas.drawLine(
          const Offset(40, 25),
          const Offset(50, 35),
          paint..color = color,
        );
        break;
    }
  }
  
  @override
  bool shouldRepaint(covariant TransformationExamplePainter oldDelegate) {
    return oldDelegate.transformationType != transformationType ||
           oldDelegate.color != color;
  }
}
