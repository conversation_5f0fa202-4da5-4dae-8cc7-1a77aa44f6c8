import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../../../../services/service_provider.dart';
import '../../../../utils/page_transitions.dart';

class InteractiveDataPacketTracer extends StatefulWidget {
  const InteractiveDataPacketTracer({super.key});

  @override
  State<InteractiveDataPacketTracer> createState() => _InteractiveDataPacketTracerState();
}

class _InteractiveDataPacketTracerState extends State<InteractiveDataPacketTracer> with SingleTickerProviderStateMixin {
  late AnimationController _controller;

  @override
  void initState() {
    super.initState();
    _controller = AnimationController(
      vsync: this,
      duration: const Duration(seconds: 1),
    )..repeat(reverse: true);
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Interactive Data Packet Tracer'),
        backgroundColor: Theme.of(context).primaryColor,
      ),
      body: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Trace the path of data packets across the internet!',
              style: Theme.of(context).textTheme.headlineSmall,
            ),
            const SizedBox(height: 20),
            Expanded(
              child: Center(
                child: FadeTransition(
                  opacity: _controller,
                  child: const Text(
                    'Placeholder for interactive data packet tracing.',
                    textAlign: TextAlign.center,
                    style: TextStyle(fontSize: 18, fontStyle: FontStyle.italic),
                  ),
                ),
              ),
            ),
            const SizedBox(height: 20),
            ElevatedButton(
              onPressed: () {
                Navigator.of(context).pop();
              },
              child: const Text('Go Back'),
            ),
          ],
        ),
      ),
    );
  }
}
