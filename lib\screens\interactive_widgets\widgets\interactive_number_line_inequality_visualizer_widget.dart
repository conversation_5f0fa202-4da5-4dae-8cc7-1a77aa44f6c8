import 'package:flutter/material.dart';
import 'dart:math' as math;

/// A widget that visualizes inequalities on a number line.
class InteractiveNumberLineInequalityVisualizerWidget extends StatefulWidget {
  final Map<String, dynamic> data;
  final Function(bool)? onStateChanged;
  final Color primaryColor;
  final Color secondaryColor;
  final Color backgroundColor;
  final Color textColor;

  const InteractiveNumberLineInequalityVisualizerWidget({
    super.key,
    this.data = const {},
    this.onStateChanged,
    this.primaryColor = Colors.blue,
    this.secondaryColor = Colors.orange,
    this.backgroundColor = Colors.white,
    this.textColor = Colors.black87,
  });

  @override
  State<InteractiveNumberLineInequalityVisualizerWidget> createState() =>
      _InteractiveNumberLineInequalityVisualizerWidgetState();
}

class _InteractiveNumberLineInequalityVisualizerWidgetState
    extends State<InteractiveNumberLineInequalityVisualizerWidget> {
  // State variables
  bool _isCompleted = false;
  int _currentInequalityIndex = 0;
  List<InequalityData> _inequalities = [];
  late InequalityData _currentInequality;

  // Interactive variables
  double _sliderValue = 0;
  bool _isValueInSolution = false;
  String? _feedbackMessage;

  // Quiz variables
  bool _showQuiz = false;
  int _quizScore = 0;
  int _currentQuestionIndex = 0;
  List<Map<String, dynamic>> _quizQuestions = [];
  String? _selectedAnswer;
  bool? _isCorrect;

  @override
  void initState() {
    super.initState();
    _initializeInequalities();
    _currentInequality = _inequalities[_currentInequalityIndex];
    _updateSliderValue();
    _initializeQuizQuestions();
  }

  void _initializeInequalities() {
    // Check if inequalities are provided in the data
    if (widget.data.containsKey('inequalities') &&
        widget.data['inequalities'] is List &&
        widget.data['inequalities'].isNotEmpty) {

      final inequalitiesData = widget.data['inequalities'] as List;
      for (final inequalityData in inequalitiesData) {
        if (inequalityData is Map<String, dynamic>) {
          final inequality = InequalityData.fromJson(inequalityData);
          _inequalities.add(inequality);
        }
      }
    }

    // If no inequalities were provided, create default ones
    if (_inequalities.isEmpty) {
      _inequalities = [
        InequalityData(
          inequality: 'x < 5',
          description: 'All values of x that are less than 5',
          boundaryValue: 5,
          boundaryIncluded: false,
          direction: 'left',
          minValue: -10,
          maxValue: 10,
        ),
        InequalityData(
          inequality: 'x ≤ 3',
          description: 'All values of x that are less than or equal to 3',
          boundaryValue: 3,
          boundaryIncluded: true,
          direction: 'left',
          minValue: -10,
          maxValue: 10,
        ),
        InequalityData(
          inequality: 'x > 0',
          description: 'All values of x that are greater than 0',
          boundaryValue: 0,
          boundaryIncluded: false,
          direction: 'right',
          minValue: -10,
          maxValue: 10,
        ),
        InequalityData(
          inequality: 'x ≥ -2',
          description: 'All values of x that are greater than or equal to -2',
          boundaryValue: -2,
          boundaryIncluded: true,
          direction: 'right',
          minValue: -10,
          maxValue: 10,
        ),
        InequalityData(
          inequality: '-3 < x < 4',
          description: 'All values of x that are greater than -3 and less than 4',
          boundaryValue: 0.5, // Midpoint
          boundaryIncluded: false,
          direction: 'both',
          lowerBound: -3,
          upperBound: 4,
          lowerBoundIncluded: false,
          upperBoundIncluded: false,
          minValue: -10,
          maxValue: 10,
        ),
        InequalityData(
          inequality: '1 ≤ x ≤ 6',
          description: 'All values of x that are greater than or equal to 1 and less than or equal to 6',
          boundaryValue: 3.5, // Midpoint
          boundaryIncluded: true,
          direction: 'both',
          lowerBound: 1,
          upperBound: 6,
          lowerBoundIncluded: true,
          upperBoundIncluded: true,
          minValue: -10,
          maxValue: 10,
        ),
      ];
    }
  }

  void _initializeQuizQuestions() {
    _quizQuestions = [
      {
        'question': 'Which inequality is represented by the number line?',
        'numberLine': {
          'boundaryValue': 3,
          'boundaryIncluded': false,
          'direction': 'left',
        },
        'options': ['x < 3', 'x ≤ 3', 'x > 3', 'x ≥ 3'],
        'correctAnswer': 'x < 3',
        'explanation': 'The number line shows an open circle at 3 with shading to the left, which represents x < 3.',
      },
      {
        'question': 'Which inequality is represented by the number line?',
        'numberLine': {
          'boundaryValue': 2,
          'boundaryIncluded': true,
          'direction': 'left',
        },
        'options': ['x < 2', 'x ≤ 2', 'x > 2', 'x ≥ 2'],
        'correctAnswer': 'x ≤ 2',
        'explanation': 'The number line shows a closed circle at 2 with shading to the left, which represents x ≤ 2.',
      },
      {
        'question': 'Which inequality is represented by the number line?',
        'numberLine': {
          'boundaryValue': 0,
          'boundaryIncluded': false,
          'direction': 'right',
        },
        'options': ['x < 0', 'x ≤ 0', 'x > 0', 'x ≥ 0'],
        'correctAnswer': 'x > 0',
        'explanation': 'The number line shows an open circle at 0 with shading to the right, which represents x > 0.',
      },
      {
        'question': 'Which inequality is represented by the number line?',
        'numberLine': {
          'boundaryValue': -1,
          'boundaryIncluded': true,
          'direction': 'right',
        },
        'options': ['x < -1', 'x ≤ -1', 'x > -1', 'x ≥ -1'],
        'correctAnswer': 'x ≥ -1',
        'explanation': 'The number line shows a closed circle at -1 with shading to the right, which represents x ≥ -1.',
      },
      {
        'question': 'Which inequality is represented by the number line?',
        'numberLine': {
          'direction': 'both',
          'lowerBound': -2,
          'upperBound': 5,
          'lowerBoundIncluded': false,
          'upperBoundIncluded': false,
        },
        'options': ['-2 < x < 5', '-2 ≤ x < 5', '-2 < x ≤ 5', '-2 ≤ x ≤ 5'],
        'correctAnswer': '-2 < x < 5',
        'explanation': 'The number line shows open circles at -2 and 5 with shading between them, which represents -2 < x < 5.',
      },
      {
        'question': 'Which inequality is represented by the number line?',
        'numberLine': {
          'direction': 'both',
          'lowerBound': 0,
          'upperBound': 4,
          'lowerBoundIncluded': true,
          'upperBoundIncluded': true,
        },
        'options': ['0 < x < 4', '0 ≤ x < 4', '0 < x ≤ 4', '0 ≤ x ≤ 4'],
        'correctAnswer': '0 ≤ x ≤ 4',
        'explanation': 'The number line shows closed circles at 0 and 4 with shading between them, which represents 0 ≤ x ≤ 4.',
      },
      {
        'question': 'Which number line represents the inequality x > 2?',
        'options': [
          'Open circle at 2, shading to the right',
          'Closed circle at 2, shading to the right',
          'Open circle at 2, shading to the left',
          'Closed circle at 2, shading to the left',
        ],
        'correctAnswer': 'Open circle at 2, shading to the right',
        'explanation': 'The inequality x > 2 is represented by an open circle at 2 with shading to the right.',
      },
      {
        'question': 'Which number line represents the inequality x ≤ -3?',
        'options': [
          'Open circle at -3, shading to the right',
          'Closed circle at -3, shading to the right',
          'Open circle at -3, shading to the left',
          'Closed circle at -3, shading to the left',
        ],
        'correctAnswer': 'Closed circle at -3, shading to the left',
        'explanation': 'The inequality x ≤ -3 is represented by a closed circle at -3 with shading to the left.',
      },
      {
        'question': 'Which number line represents the inequality -1 < x ≤ 4?',
        'options': [
          'Open circles at -1 and 4, shading between',
          'Closed circles at -1 and 4, shading between',
          'Open circle at -1, closed circle at 4, shading between',
          'Closed circle at -1, open circle at 4, shading between',
        ],
        'correctAnswer': 'Open circle at -1, closed circle at 4, shading between',
        'explanation': 'The inequality -1 < x ≤ 4 is represented by an open circle at -1, a closed circle at 4, with shading between them.',
      },
      {
        'question': 'For the inequality x < 5, which value of x is in the solution set?',
        'options': ['x = 5', 'x = 6', 'x = 4', 'x = 7'],
        'correctAnswer': 'x = 4',
        'explanation': 'For x < 5, any value less than 5 is in the solution set. Of the options, only x = 4 is less than 5.',
      },
    ];
  }

  void _updateSliderValue() {
    // Set slider to a random value within the range
    final random = math.Random();
    double min = _currentInequality.minValue;
    double max = _currentInequality.maxValue;
    _sliderValue = min + random.nextDouble() * (max - min);

    // Check if the value is in the solution set
    _checkValueInSolution();
  }

  void _checkValueInSolution() {
    bool isInSolution = false;

    if (_currentInequality.direction == 'both') {
      // Compound inequality
      bool isAboveLower = _currentInequality.lowerBoundIncluded
          ? _sliderValue >= _currentInequality.lowerBound
          : _sliderValue > _currentInequality.lowerBound;

      bool isBelowUpper = _currentInequality.upperBoundIncluded
          ? _sliderValue <= _currentInequality.upperBound
          : _sliderValue < _currentInequality.upperBound;

      isInSolution = isAboveLower && isBelowUpper;
    } else if (_currentInequality.direction == 'left') {
      // Less than inequality
      isInSolution = _currentInequality.boundaryIncluded
          ? _sliderValue <= _currentInequality.boundaryValue
          : _sliderValue < _currentInequality.boundaryValue;
    } else {
      // Greater than inequality
      isInSolution = _currentInequality.boundaryIncluded
          ? _sliderValue >= _currentInequality.boundaryValue
          : _sliderValue > _currentInequality.boundaryValue;
    }

    setState(() {
      _isValueInSolution = isInSolution;
      _feedbackMessage = isInSolution
          ? 'Yes! ${_sliderValue.toStringAsFixed(1)} is in the solution set.'
          : 'No, ${_sliderValue.toStringAsFixed(1)} is not in the solution set.';
    });
  }

  void _nextInequality() {
    if (_currentInequalityIndex < _inequalities.length - 1) {
      setState(() {
        _currentInequalityIndex++;
        _currentInequality = _inequalities[_currentInequalityIndex];
        _updateSliderValue();
      });
    } else {
      // All inequalities explored
      setState(() {
        _showQuiz = true;
        _currentQuestionIndex = 0;
        _selectedAnswer = null;
        _isCorrect = null;
      });
    }
  }

  void _previousInequality() {
    if (_currentInequalityIndex > 0) {
      setState(() {
        _currentInequalityIndex--;
        _currentInequality = _inequalities[_currentInequalityIndex];
        _updateSliderValue();
      });
    }
  }

  void _checkAnswer(String answer) {
    if (_currentQuestionIndex >= _quizQuestions.length) return;

    final currentQuestion = _quizQuestions[_currentQuestionIndex];
    final bool isCorrect = answer == currentQuestion['correctAnswer'];

    setState(() {
      _selectedAnswer = answer;
      _isCorrect = isCorrect;

      if (isCorrect) {
        _quizScore++;
      }
    });

    // Move to next question after a delay
    Future.delayed(const Duration(seconds: 2), () {
      if (!mounted) return;

      setState(() {
        if (_currentQuestionIndex < _quizQuestions.length - 1) {
          _currentQuestionIndex++;
          _selectedAnswer = null;
          _isCorrect = null;
        } else {
          // Quiz completed
          _isCompleted = true;
          widget.onStateChanged?.call(true);
        }
      });
    });
  }

  void _resetWidget() {
    setState(() {
      _currentInequalityIndex = 0;
      _currentInequality = _inequalities[_currentInequalityIndex];
      _updateSliderValue();
      _showQuiz = false;
      _quizScore = 0;
      _currentQuestionIndex = 0;
      _selectedAnswer = null;
      _isCorrect = null;
      _isCompleted = false;
    });
    widget.onStateChanged?.call(false);
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: widget.backgroundColor,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withOpacity(0.2),
            spreadRadius: 1,
            blurRadius: 3,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: _isCompleted
          ? _buildCompletionScreen()
          : (_showQuiz ? _buildQuizScreen() : _buildVisualizerScreen()),
    );
  }

  Widget _buildVisualizerScreen() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Title and navigation
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Text(
              'Inequality Visualizer',
              style: TextStyle(
                fontSize: 20,
                fontWeight: FontWeight.bold,
                color: widget.primaryColor,
              ),
            ),
            Text(
              'Example ${_currentInequalityIndex + 1} of ${_inequalities.length}',
              style: TextStyle(
                fontSize: 14,
                color: widget.textColor.withOpacity(0.7),
              ),
            ),
          ],
        ),

        const SizedBox(height: 24),

        // Current inequality
        Container(
          width: double.infinity,
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            color: widget.primaryColor.withOpacity(0.1),
            borderRadius: BorderRadius.circular(8),
            border: Border.all(color: widget.primaryColor.withOpacity(0.3)),
          ),
          child: Column(
            children: [
              Text(
                _currentInequality.inequality,
                style: TextStyle(
                  fontSize: 28,
                  fontWeight: FontWeight.bold,
                  color: widget.primaryColor,
                ),
                textAlign: TextAlign.center,
              ),
              const SizedBox(height: 8),
              Text(
                _currentInequality.description,
                style: TextStyle(
                  fontSize: 16,
                  color: widget.textColor,
                ),
                textAlign: TextAlign.center,
              ),
            ],
          ),
        ),

        const SizedBox(height: 32),

        // Number line visualization
        Text(
          'Number Line Representation:',
          style: TextStyle(
            fontSize: 18,
            fontWeight: FontWeight.bold,
            color: widget.textColor,
          ),
        ),

        const SizedBox(height: 16),

        Container(
          height: 100,
          width: double.infinity,
          child: CustomPaint(
            painter: NumberLinePainter(
              inequality: _currentInequality,
              primaryColor: widget.primaryColor,
              secondaryColor: widget.secondaryColor,
              textColor: widget.textColor,
            ),
          ),
        ),

        const SizedBox(height: 32),

        // Interactive test
        Text(
          'Test a Value:',
          style: TextStyle(
            fontSize: 18,
            fontWeight: FontWeight.bold,
            color: widget.textColor,
          ),
        ),

        const SizedBox(height: 8),

        Text(
          'Drag the slider to test if a value is in the solution set:',
          style: TextStyle(
            fontSize: 14,
            color: widget.textColor.withOpacity(0.7),
          ),
        ),

        const SizedBox(height: 16),

        // Slider
        Row(
          children: [
            Text(
              _currentInequality.minValue.toInt().toString(),
              style: TextStyle(
                fontSize: 14,
                color: widget.textColor,
              ),
            ),
            Expanded(
              child: Slider(
                value: _sliderValue,
                min: _currentInequality.minValue,
                max: _currentInequality.maxValue,
                divisions: (_currentInequality.maxValue - _currentInequality.minValue).toInt() * 2,
                label: _sliderValue.toStringAsFixed(1),
                activeColor: widget.primaryColor,
                onChanged: (value) {
                  setState(() {
                    _sliderValue = value;
                    _checkValueInSolution();
                  });
                },
              ),
            ),
            Text(
              _currentInequality.maxValue.toInt().toString(),
              style: TextStyle(
                fontSize: 14,
                color: widget.textColor,
              ),
            ),
          ],
        ),

        const SizedBox(height: 16),

        // Current value and feedback
        Container(
          width: double.infinity,
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            color: _isValueInSolution
                ? Colors.green.withOpacity(0.1)
                : Colors.red.withOpacity(0.1),
            borderRadius: BorderRadius.circular(8),
            border: Border.all(
              color: _isValueInSolution
                  ? Colors.green.withOpacity(0.3)
                  : Colors.red.withOpacity(0.3),
            ),
          ),
          child: Column(
            children: [
              Text(
                'Is x = ${_sliderValue.toStringAsFixed(1)} in the solution set?',
                style: TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.bold,
                  color: widget.textColor,
                ),
              ),
              const SizedBox(height: 8),
              Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Icon(
                    _isValueInSolution ? Icons.check_circle : Icons.cancel,
                    color: _isValueInSolution ? Colors.green : Colors.red,
                    size: 24,
                  ),
                  const SizedBox(width: 8),
                  Text(
                    _feedbackMessage ?? '',
                    style: TextStyle(
                      fontSize: 16,
                      color: _isValueInSolution ? Colors.green : Colors.red,
                    ),
                  ),
                ],
              ),
            ],
          ),
        ),

        const Spacer(),

        // Navigation buttons
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            ElevatedButton(
              onPressed: _currentInequalityIndex > 0 ? _previousInequality : null,
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.grey.shade200,
                foregroundColor: Colors.black87,
              ),
              child: const Text('Previous'),
            ),
            ElevatedButton(
              onPressed: _nextInequality,
              style: ElevatedButton.styleFrom(
                backgroundColor: widget.primaryColor,
                foregroundColor: Colors.white,
              ),
              child: Text(_currentInequalityIndex < _inequalities.length - 1 ? 'Next' : 'Take Quiz'),
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildQuizScreen() {
    if (_currentQuestionIndex >= _quizQuestions.length) {
      return Container();
    }

    final currentQuestion = _quizQuestions[_currentQuestionIndex];

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Title and progress
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Text(
              'Inequality Quiz',
              style: TextStyle(
                fontSize: 20,
                fontWeight: FontWeight.bold,
                color: widget.primaryColor,
              ),
            ),
            Text(
              'Question ${_currentQuestionIndex + 1} of ${_quizQuestions.length}',
              style: TextStyle(
                fontSize: 14,
                color: widget.textColor.withOpacity(0.7),
              ),
            ),
          ],
        ),

        const SizedBox(height: 24),

        // Question
        Container(
          width: double.infinity,
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            color: widget.primaryColor.withOpacity(0.1),
            borderRadius: BorderRadius.circular(8),
            border: Border.all(color: widget.primaryColor.withOpacity(0.3)),
          ),
          child: Text(
            currentQuestion['question'],
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
              color: widget.textColor,
            ),
          ),
        ),

        const SizedBox(height: 24),

        // Number line visualization for the question (if applicable)
        if (currentQuestion.containsKey('numberLine')) ...[
          Container(
            height: 100,
            width: double.infinity,
            child: CustomPaint(
              painter: NumberLinePainter(
                inequality: _createInequalityFromQuiz(currentQuestion['numberLine']),
                primaryColor: widget.primaryColor,
                secondaryColor: widget.secondaryColor,
                textColor: widget.textColor,
              ),
            ),
          ),

          const SizedBox(height: 24),
        ],

        // Answer options
        ...(currentQuestion['options'] as List<String>).map((option) {
          final bool isSelected = _selectedAnswer == option;
          final bool isCorrect = option == currentQuestion['correctAnswer'];

          return Padding(
            padding: const EdgeInsets.only(bottom: 12),
            child: InkWell(
              onTap: _selectedAnswer == null ? () => _checkAnswer(option) : null,
              child: Container(
                width: double.infinity,
                padding: const EdgeInsets.all(16),
                decoration: BoxDecoration(
                  color: isSelected
                      ? (isCorrect ? Colors.green.withOpacity(0.1) : Colors.red.withOpacity(0.1))
                      : Colors.grey.shade50,
                  borderRadius: BorderRadius.circular(8),
                  border: Border.all(
                    color: isSelected
                        ? (isCorrect ? Colors.green : Colors.red)
                        : Colors.grey.shade300,
                  ),
                ),
                child: Row(
                  children: [
                    Expanded(
                      child: Text(
                        option,
                        style: TextStyle(
                          fontSize: 16,
                          fontWeight: isSelected ? FontWeight.bold : FontWeight.normal,
                          color: widget.textColor,
                        ),
                      ),
                    ),
                    if (isSelected)
                      Icon(
                        isCorrect ? Icons.check_circle : Icons.cancel,
                        color: isCorrect ? Colors.green : Colors.red,
                      ),
                  ],
                ),
              ),
            ),
          );
        }).toList(),

        const Spacer(),

        // Explanation (shown after answering)
        if (_selectedAnswer != null)
          Container(
            width: double.infinity,
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: _isCorrect == true
                  ? Colors.green.withOpacity(0.1)
                  : Colors.red.withOpacity(0.1),
              borderRadius: BorderRadius.circular(8),
              border: Border.all(
                color: _isCorrect == true ? Colors.green : Colors.red,
              ),
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  _isCorrect == true ? 'Correct!' : 'Incorrect!',
                  style: TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                    color: _isCorrect == true ? Colors.green : Colors.red,
                  ),
                ),
                const SizedBox(height: 8),
                Text(
                  currentQuestion['explanation'],
                  style: TextStyle(
                    fontSize: 14,
                    color: widget.textColor,
                  ),
                ),
              ],
            ),
          ),
      ],
    );
  }

  InequalityData _createInequalityFromQuiz(Map<String, dynamic> numberLineData) {
    if (numberLineData['direction'] == 'both') {
      return InequalityData(
        inequality: '',
        description: '',
        boundaryValue: 0,
        boundaryIncluded: false,
        direction: 'both',
        lowerBound: numberLineData['lowerBound'] ?? -5,
        upperBound: numberLineData['upperBound'] ?? 5,
        lowerBoundIncluded: numberLineData['lowerBoundIncluded'] ?? false,
        upperBoundIncluded: numberLineData['upperBoundIncluded'] ?? false,
        minValue: -10,
        maxValue: 10,
      );
    } else {
      return InequalityData(
        inequality: '',
        description: '',
        boundaryValue: numberLineData['boundaryValue'] ?? 0,
        boundaryIncluded: numberLineData['boundaryIncluded'] ?? false,
        direction: numberLineData['direction'] ?? 'left',
        minValue: -10,
        maxValue: 10,
      );
    }
  }

  Widget _buildCompletionScreen() {
    // Calculate percentage score
    final percentage = (_quizScore / _quizQuestions.length * 100).round();
    final isPassing = percentage >= 70;

    return Column(
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        // Trophy icon for passing, or try again icon for failing
        Icon(
          isPassing ? Icons.emoji_events : Icons.refresh,
          size: 80,
          color: isPassing ? Colors.amber : Colors.grey,
        ),

        const SizedBox(height: 24),

        // Result title
        Text(
          isPassing ? 'Congratulations!' : 'Keep Practicing!',
          style: TextStyle(
            fontSize: 24,
            fontWeight: FontWeight.bold,
            color: isPassing ? widget.primaryColor : Colors.red,
          ),
        ),

        const SizedBox(height: 16),

        // Score
        Text(
          'Your Score: $_quizScore out of ${_quizQuestions.length} ($percentage%)',
          style: TextStyle(
            fontSize: 18,
            fontWeight: FontWeight.bold,
            color: widget.textColor,
          ),
        ),

        const SizedBox(height: 24),

        // Feedback message
        Text(
          isPassing
              ? 'Great job! You\'ve demonstrated a solid understanding of inequalities on number lines.'
              : 'You need a score of at least 70% to pass. Review the concepts and try again!',
          textAlign: TextAlign.center,
          style: TextStyle(
            fontSize: 16,
            color: widget.textColor,
          ),
        ),

        const SizedBox(height: 32),

        // Try again button
        ElevatedButton.icon(
          onPressed: _resetWidget,
          icon: Icon(Icons.refresh),
          label: Text('Start Over'),
          style: ElevatedButton.styleFrom(
            backgroundColor: widget.secondaryColor,
            padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
          ),
        ),
      ],
    );
  }
}

/// Custom painter for drawing the number line
class NumberLinePainter extends CustomPainter {
  final InequalityData inequality;
  final Color primaryColor;
  final Color secondaryColor;
  final Color textColor;

  NumberLinePainter({
    required this.inequality,
    required this.primaryColor,
    required this.secondaryColor,
    required this.textColor,
  });

  @override
  void paint(Canvas canvas, Size size) {
    final double centerY = size.height / 2;
    final double lineStartX = 20;
    final double lineEndX = size.width - 20;
    final double lineLength = lineEndX - lineStartX;

    // Calculate the scale
    final double valueRange = inequality.maxValue - inequality.minValue;
    final double pixelsPerUnit = lineLength / valueRange;

    // Draw the main line
    final Paint linePaint = Paint()
      ..color = textColor
      ..strokeWidth = 2
      ..style = PaintingStyle.stroke;

    canvas.drawLine(
      Offset(lineStartX, centerY),
      Offset(lineEndX, centerY),
      linePaint,
    );

    // Draw tick marks and labels
    final int tickCount = valueRange.toInt() + 1;
    final double tickSpacing = lineLength / valueRange;

    for (int i = 0; i <= tickCount; i++) {
      final double value = inequality.minValue + i;
      final double x = lineStartX + (value - inequality.minValue) * pixelsPerUnit;

      // Draw tick mark
      canvas.drawLine(
        Offset(x, centerY - 5),
        Offset(x, centerY + 5),
        linePaint,
      );

      // Draw label
      final TextPainter textPainter = TextPainter(
        text: TextSpan(
          text: value.toInt().toString(),
          style: TextStyle(
            color: textColor,
            fontSize: 12,
          ),
        ),
        textDirection: TextDirection.ltr,
      );

      textPainter.layout();
      textPainter.paint(
        canvas,
        Offset(x - textPainter.width / 2, centerY + 10),
      );
    }

    // Draw the shaded region based on the inequality
    final Paint shadePaint = Paint()
      ..color = secondaryColor.withOpacity(0.3)
      ..style = PaintingStyle.fill;

    if (inequality.direction == 'both') {
      // Compound inequality
      final double lowerX = lineStartX + (inequality.lowerBound - inequality.minValue) * pixelsPerUnit;
      final double upperX = lineStartX + (inequality.upperBound - inequality.minValue) * pixelsPerUnit;

      // Draw shaded region between bounds
      final Rect shadeRect = Rect.fromPoints(
        Offset(lowerX, centerY - 10),
        Offset(upperX, centerY + 10),
      );
      canvas.drawRect(shadeRect, shadePaint);

      // Draw boundary points
      final Paint boundaryPaint = Paint()
        ..color = primaryColor
        ..style = inequality.lowerBoundIncluded ? PaintingStyle.fill : PaintingStyle.stroke
        ..strokeWidth = 2;

      canvas.drawCircle(
        Offset(lowerX, centerY),
        6,
        boundaryPaint,
      );

      boundaryPaint.style = inequality.upperBoundIncluded ? PaintingStyle.fill : PaintingStyle.stroke;

      canvas.drawCircle(
        Offset(upperX, centerY),
        6,
        boundaryPaint,
      );
    } else {
      // Single inequality
      final double boundaryX = lineStartX + (inequality.boundaryValue - inequality.minValue) * pixelsPerUnit;

      if (inequality.direction == 'left') {
        // Less than inequality
        final Rect shadeRect = Rect.fromPoints(
          Offset(lineStartX, centerY - 10),
          Offset(boundaryX, centerY + 10),
        );
        canvas.drawRect(shadeRect, shadePaint);
      } else {
        // Greater than inequality
        final Rect shadeRect = Rect.fromPoints(
          Offset(boundaryX, centerY - 10),
          Offset(lineEndX, centerY + 10),
        );
        canvas.drawRect(shadeRect, shadePaint);
      }

      // Draw boundary point
      final Paint boundaryPaint = Paint()
        ..color = primaryColor
        ..style = inequality.boundaryIncluded ? PaintingStyle.fill : PaintingStyle.stroke
        ..strokeWidth = 2;

      canvas.drawCircle(
        Offset(boundaryX, centerY),
        6,
        boundaryPaint,
      );
    }
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) {
    return true;
  }
}

/// Data class for inequality information
class InequalityData {
  final String inequality;
  final String description;
  final double boundaryValue;
  final bool boundaryIncluded;
  final String direction; // 'left', 'right', or 'both'
  final double lowerBound;
  final double upperBound;
  final bool lowerBoundIncluded;
  final bool upperBoundIncluded;
  final double minValue;
  final double maxValue;

  InequalityData({
    required this.inequality,
    required this.description,
    required this.boundaryValue,
    required this.boundaryIncluded,
    required this.direction,
    this.lowerBound = 0,
    this.upperBound = 0,
    this.lowerBoundIncluded = false,
    this.upperBoundIncluded = false,
    this.minValue = -10,
    this.maxValue = 10,
  });

  factory InequalityData.fromJson(Map<String, dynamic> json) {
    return InequalityData(
      inequality: json['inequality'] ?? 'x < 5',
      description: json['description'] ?? 'All values of x that are less than 5',
      boundaryValue: json['boundaryValue']?.toDouble() ?? 5,
      boundaryIncluded: json['boundaryIncluded'] ?? false,
      direction: json['direction'] ?? 'left',
      lowerBound: json['lowerBound']?.toDouble() ?? 0,
      upperBound: json['upperBound']?.toDouble() ?? 0,
      lowerBoundIncluded: json['lowerBoundIncluded'] ?? false,
      upperBoundIncluded: json['upperBoundIncluded'] ?? false,
      minValue: json['minValue']?.toDouble() ?? -10,
      maxValue: json['maxValue']?.toDouble() ?? 10,
    );
  }
}
