import 'package:flutter/material.dart';
import 'dart:math' as math;

/// A widget that helps users explore exponential functions by visualizing how changes to parameters affect the graph
class InteractiveExponentialFunctionExplorerWidget extends StatefulWidget {
  /// The data for this widget
  final Map<String, dynamic> data;

  /// Optional callback when the widget state changes
  final Function(bool isCompleted)? onStateChanged;

  const InteractiveExponentialFunctionExplorerWidget({
    Key? key,
    required this.data,
    this.onStateChanged,
  }) : super(key: key);

  /// Factory constructor to create the widget from JSON data
  factory InteractiveExponentialFunctionExplorerWidget.fromData(Map<String, dynamic> data) {
    return InteractiveExponentialFunctionExplorerWidget(
      data: data,
    );
  }

  @override
  State<InteractiveExponentialFunctionExplorerWidget> createState() => _InteractiveExponentialFunctionExplorerWidgetState();
}

class _InteractiveExponentialFunctionExplorerWidgetState extends State<InteractiveExponentialFunctionExplorerWidget> {
  // Parameters of the exponential function f(x) = a * b^x + c
  double _a = 1.0;
  double _b = 2.0;
  double _c = 0.0;

  // Whether the widget is completed
  bool _isCompleted = false;

  // Current form of the exponential function
  String _currentForm = 'standard';

  // Current example
  int _currentExampleIndex = 0;

  // Colors
  late Color _primaryColor;
  late Color _secondaryColor;
  late Color _accentColor;
  late Color _backgroundColor;
  late Color _textColor;

  // List of real-world examples
  late List<Map<String, dynamic>> _examples;

  // Whether to show key points
  bool _showKeyPoints = true;

  // Whether to show grid
  bool _showGrid = true;

  // Whether to show asymptote
  bool _showAsymptote = true;

  // Whether to show the equation
  bool _showEquation = true;

  // Whether to show the example
  bool _showExample = false;

  // Whether to show the form selector
  bool _showFormSelector = true;

  // Natural exponential form parameter (k)
  double _k = 0.0;

  @override
  void initState() {
    super.initState();

    // Initialize colors
    _primaryColor = _parseColor(widget.data['primary_color']) ?? Colors.blue;
    _secondaryColor = _parseColor(widget.data['secondary_color']) ?? Colors.orange;
    _accentColor = _parseColor(widget.data['accent_color']) ?? Colors.green;
    _backgroundColor = _parseColor(widget.data['background_color']) ?? Colors.white;
    _textColor = _parseColor(widget.data['text_color']) ?? Colors.black87;

    // Initialize examples
    _examples = widget.data['examples'] != null
        ? List<Map<String, dynamic>>.from(widget.data['examples'])
        : _getDefaultExamples();

    // Calculate k for natural exponential form
    _updateNaturalExponentialParameter();
  }

  // Parse color from hex string
  Color? _parseColor(String? hexString) {
    if (hexString == null) return null;

    hexString = hexString.replaceAll('#', '');
    if (hexString.length == 6) {
      hexString = 'FF$hexString';
    }

    return Color(int.parse(hexString, radix: 16));
  }

  // Get default examples if none provided
  List<Map<String, dynamic>> _getDefaultExamples() {
    return [
      {
        'title': 'Population Growth',
        'description': 'A bacteria population starts with 100 cells and doubles every hour. The population P(t) after t hours is given by P(t) = 100 * 2^t.',
        'a': 100.0,
        'b': 2.0,
        'c': 0.0,
        'a_meaning': 'The coefficient 100 represents the initial population.',
        'b_meaning': 'The base 2 means the population doubles each time period.',
        'c_meaning': 'The constant 0 means there is no vertical shift.',
        'x_axis': 'Time (hours)',
        'y_axis': 'Population (cells)',
        'type': 'growth',
      },
      {
        'title': 'Radioactive Decay',
        'description': 'A radioactive substance has an initial mass of 50 grams and decays by 20% each day. The remaining mass M(t) after t days is given by M(t) = 50 * (0.8)^t.',
        'a': 50.0,
        'b': 0.8,
        'c': 0.0,
        'a_meaning': 'The coefficient 50 represents the initial mass in grams.',
        'b_meaning': 'The base 0.8 means 80% of the mass remains after each day (20% decay rate).',
        'c_meaning': 'The constant 0 means there is no vertical shift.',
        'x_axis': 'Time (days)',
        'y_axis': 'Mass (grams)',
        'type': 'decay',
      },
      {
        'title': 'Compound Interest',
        'description': 'An investment of \$1000 earns 5% interest compounded continuously. The value V(t) after t years is given by V(t) = 1000 * e^(0.05t).',
        'a': 1000.0,
        'b': math.e,
        'c': 0.0,
        'k': 0.05,
        'a_meaning': 'The coefficient 1000 represents the initial investment in dollars.',
        'b_meaning': 'The base e is used for continuous compounding.',
        'k_meaning': 'The exponent 0.05 represents the annual interest rate (5%).',
        'c_meaning': 'The constant 0 means there is no vertical shift.',
        'x_axis': 'Time (years)',
        'y_axis': 'Value (\$)',
        'type': 'growth',
      },
      {
        'title': 'Temperature Cooling',
        'description': 'A hot object with temperature 100°C is placed in a room with temperature 20°C. The object\'s temperature T(t) after t minutes is given by T(t) = 20 + 80 * (0.9)^t.',
        'a': 80.0,
        'b': 0.9,
        'c': 20.0,
        'a_meaning': 'The coefficient 80 represents the initial temperature difference (100°C - 20°C).',
        'b_meaning': 'The base 0.9 represents the cooling rate.',
        'c_meaning': 'The constant 20 represents the ambient room temperature (asymptote).',
        'x_axis': 'Time (minutes)',
        'y_axis': 'Temperature (°C)',
        'type': 'decay',
      },
    ];
  }

  // Update natural exponential parameter k (when b = e)
  void _updateNaturalExponentialParameter() {
    if (_b != math.e) {
      _k = math.log(_b);
    }
  }

  // Update base b from natural exponential parameter k
  void _updateBaseFromNaturalParameter() {
    _b = math.exp(_k);
  }

  // Load an example
  void _loadExample(int index) {
    if (index >= 0 && index < _examples.length) {
      setState(() {
        _currentExampleIndex = index;
        _a = _examples[index]['a'];
        _b = _examples[index]['b'];
        _c = _examples[index]['c'];

        // If example has k parameter (for natural exponential form)
        if (_examples[index]['k'] != null) {
          _k = _examples[index]['k'];
        } else {
          _updateNaturalExponentialParameter();
        }

        _showExample = true;
      });
    }
  }

  // Reset to default values
  void _resetToDefault() {
    setState(() {
      _a = 1.0;
      _b = 2.0;
      _c = 0.0;
      _updateNaturalExponentialParameter();
      _showExample = false;
    });
  }

  // Toggle key points visibility
  void _toggleKeyPoints() {
    setState(() {
      _showKeyPoints = !_showKeyPoints;
    });
  }

  // Toggle grid visibility
  void _toggleGrid() {
    setState(() {
      _showGrid = !_showGrid;
    });
  }

  // Toggle asymptote visibility
  void _toggleAsymptote() {
    setState(() {
      _showAsymptote = !_showAsymptote;
    });
  }

  // Toggle equation visibility
  void _toggleEquation() {
    setState(() {
      _showEquation = !_showEquation;
    });
  }

  // Change the form of the exponential function
  void _changeForm(String form) {
    setState(() {
      _currentForm = form;
    });
  }

  // Get the equation text based on the current form
  String _getEquationText() {
    switch (_currentForm) {
      case 'standard':
        String aText = _a == 1 ? '' : '${_a.toStringAsFixed(1)} * ';
        String cSign = _c >= 0 ? '+' : '';
        String cText = _c == 0 ? '' : ' $cSign ${_c.toStringAsFixed(1)}';
        return 'f(x) = ${aText}${_b.toStringAsFixed(2)}^x$cText';
      case 'natural':
        String aText = _a == 1 ? '' : '${_a.toStringAsFixed(1)} * ';
        String cSign = _c >= 0 ? '+' : '';
        String cText = _c == 0 ? '' : ' $cSign ${_c.toStringAsFixed(1)}';
        return 'f(x) = ${aText}e^(${_k.toStringAsFixed(3)}x)$cText';
      default:
        return 'f(x) = ${_a.toStringAsFixed(1)} * ${_b.toStringAsFixed(2)}^x ${_c >= 0 ? '+' : ''} ${_c.toStringAsFixed(1)}';
    }
  }

  // Mark the widget as completed
  void _markAsCompleted() {
    if (!_isCompleted) {
      setState(() {
        _isCompleted = true;
      });

      // Notify parent of state change
      widget.onStateChanged?.call(true);
    }
  }

  // Build the graph and controls section
  Widget _buildGraphAndControls() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Function form selector
        if (_showFormSelector)
          Container(
            padding: const EdgeInsets.all(8),
            decoration: BoxDecoration(
              color: Colors.grey[100],
              borderRadius: BorderRadius.circular(8),
              border: Border.all(color: Colors.grey[300]!),
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'Function Form:',
                  style: TextStyle(
                    fontSize: 14,
                    fontWeight: FontWeight.bold,
                    color: _textColor,
                  ),
                ),
                const SizedBox(height: 8),
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                  children: [
                    _buildFormButton('standard', 'Standard'),
                    _buildFormButton('natural', 'Natural (e)'),
                  ],
                ),
              ],
            ),
          ),

        const SizedBox(height: 16),

        // Equation display
        if (_showEquation)
          Container(
            width: double.infinity,
            padding: const EdgeInsets.all(12),
            decoration: BoxDecoration(
              color: _primaryColor.withOpacity(0.1),
              borderRadius: BorderRadius.circular(8),
              border: Border.all(color: _primaryColor),
            ),
            child: Text(
              _getEquationText(),
              textAlign: TextAlign.center,
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
                color: _primaryColor,
              ),
            ),
          ),

        const SizedBox(height: 16),

        // Graph
        Container(
          height: 250,
          width: double.infinity,
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(8),
            border: Border.all(color: Colors.grey[300]!),
          ),
          child: ClipRRect(
            borderRadius: BorderRadius.circular(8),
            child: CustomPaint(
              painter: ExponentialFunctionGraphPainter(
                a: _a,
                b: _b,
                c: _c,
                showGrid: _showGrid,
                showKeyPoints: _showKeyPoints,
                showAsymptote: _showAsymptote,
                primaryColor: _primaryColor,
                secondaryColor: _secondaryColor,
                accentColor: _accentColor,
              ),
            ),
          ),
        ),

        const SizedBox(height: 16),

        // Graph controls
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceEvenly,
          children: [
            _buildControlButton(
              icon: Icons.grid_on,
              label: 'Grid',
              isActive: _showGrid,
              onPressed: _toggleGrid,
            ),
            _buildControlButton(
              icon: Icons.location_on,
              label: 'Key Points',
              isActive: _showKeyPoints,
              onPressed: _toggleKeyPoints,
            ),
            _buildControlButton(
              icon: Icons.show_chart,
              label: 'Asymptote',
              isActive: _showAsymptote,
              onPressed: _toggleAsymptote,
            ),
            _buildControlButton(
              icon: Icons.functions,
              label: 'Equation',
              isActive: _showEquation,
              onPressed: _toggleEquation,
            ),
          ],
        ),

        const SizedBox(height: 16),

        // Parameter a slider
        Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Coefficient a: ${_a.toStringAsFixed(1)}',
              style: TextStyle(
                fontSize: 14,
                fontWeight: FontWeight.bold,
                color: _textColor,
              ),
            ),
            Slider(
              value: _a,
              min: -5.0,
              max: 5.0,
              divisions: 100,
              activeColor: _primaryColor,
              inactiveColor: _primaryColor.withOpacity(0.3),
              onChanged: (value) {
                setState(() {
                  _a = value;
                });
              },
            ),
          ],
        ),

        // Parameter b or k slider (depending on form)
        Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              _currentForm == 'natural'
                  ? 'Rate k: ${_k.toStringAsFixed(3)}'
                  : 'Base b: ${_b.toStringAsFixed(2)}',
              style: TextStyle(
                fontSize: 14,
                fontWeight: FontWeight.bold,
                color: _textColor,
              ),
            ),
            Slider(
              value: _currentForm == 'natural' ? _k : _b,
              min: _currentForm == 'natural' ? -2.0 : 0.1,
              max: _currentForm == 'natural' ? 2.0 : 5.0,
              divisions: 100,
              activeColor: _secondaryColor,
              inactiveColor: _secondaryColor.withOpacity(0.3),
              onChanged: (value) {
                setState(() {
                  if (_currentForm == 'natural') {
                    _k = value;
                    _updateBaseFromNaturalParameter();
                  } else {
                    _b = value;
                    _updateNaturalExponentialParameter();
                  }
                });
              },
            ),
          ],
        ),

        // Parameter c slider
        Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Vertical shift c: ${_c.toStringAsFixed(1)}',
              style: TextStyle(
                fontSize: 14,
                fontWeight: FontWeight.bold,
                color: _textColor,
              ),
            ),
            Slider(
              value: _c,
              min: -10.0,
              max: 10.0,
              divisions: 100,
              activeColor: _accentColor,
              inactiveColor: _accentColor.withOpacity(0.3),
              onChanged: (value) {
                setState(() {
                  _c = value;
                });
              },
            ),
          ],
        ),

        const SizedBox(height: 16),

        // Key points information
        Container(
          padding: const EdgeInsets.all(8),
          decoration: BoxDecoration(
            color: Colors.grey[100],
            borderRadius: BorderRadius.circular(8),
            border: Border.all(color: Colors.grey[300]!),
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                'Key Points:',
                style: TextStyle(
                  fontSize: 14,
                  fontWeight: FontWeight.bold,
                  color: _textColor,
                ),
              ),
              const SizedBox(height: 4),
              Text(
                'Y-intercept: (0, ${(_a + _c).toStringAsFixed(2)})',
                style: TextStyle(
                  fontSize: 14,
                  color: _secondaryColor,
                ),
              ),
              Text(
                'Horizontal Asymptote: y = ${_c.toStringAsFixed(2)}',
                style: TextStyle(
                  fontSize: 14,
                  color: _accentColor,
                ),
              ),
              Text(
                _b > 1
                    ? 'Growth Type: Exponential Growth (b > 1)'
                    : _b == 1
                        ? 'Growth Type: Constant (b = 1)'
                        : 'Growth Type: Exponential Decay (0 < b < 1)',
                style: TextStyle(
                  fontSize: 14,
                  fontWeight: FontWeight.bold,
                  color: _b > 1 ? Colors.green : _b < 1 ? Colors.red : Colors.blue,
                ),
              ),
            ],
          ),
        ),

        const SizedBox(height: 16),

        // Examples and reset buttons
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            ElevatedButton(
              onPressed: _resetToDefault,
              style: ElevatedButton.styleFrom(
                backgroundColor: _secondaryColor,
                foregroundColor: Colors.white,
              ),
              child: const Text('Reset'),
            ),
            ElevatedButton(
              onPressed: () => _showExamplesDialog(context),
              style: ElevatedButton.styleFrom(
                backgroundColor: _primaryColor,
                foregroundColor: Colors.white,
              ),
              child: const Text('Examples'),
            ),
            ElevatedButton(
              onPressed: _markAsCompleted,
              style: ElevatedButton.styleFrom(
                backgroundColor: _accentColor,
                foregroundColor: Colors.white,
              ),
              child: const Text('Complete'),
            ),
          ],
        ),
      ],
    );
  }

  // Build a form selection button
  Widget _buildFormButton(String form, String label) {
    final isSelected = _currentForm == form;

    return ElevatedButton(
      onPressed: () => _changeForm(form),
      style: ElevatedButton.styleFrom(
        backgroundColor: isSelected ? _primaryColor : Colors.grey[300],
        foregroundColor: isSelected ? Colors.white : _textColor,
        padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      ),
      child: Text(label),
    );
  }

  // Build a control button
  Widget _buildControlButton({
    required IconData icon,
    required String label,
    required bool isActive,
    required VoidCallback onPressed,
  }) {
    return Column(
      children: [
        IconButton(
          icon: Icon(icon),
          color: isActive ? _primaryColor : Colors.grey,
          onPressed: onPressed,
        ),
        Text(
          label,
          style: TextStyle(
            fontSize: 12,
            color: isActive ? _primaryColor : Colors.grey,
          ),
        ),
      ],
    );
  }

  // Build the example section
  Widget _buildExampleSection() {
    final example = _examples[_currentExampleIndex];

    return Container(
      margin: const EdgeInsets.only(top: 16),
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: _accentColor.withOpacity(0.1),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: _accentColor),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Example: ${example['title']}',
            style: TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.bold,
              color: _accentColor,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            example['description'],
            style: TextStyle(
              fontSize: 14,
              color: _textColor,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            'Coefficient a meaning: ${example['a_meaning']}',
            style: TextStyle(
              fontSize: 14,
              color: _textColor,
            ),
          ),
          const SizedBox(height: 4),
          Text(
            'Base b meaning: ${example['b_meaning']}',
            style: TextStyle(
              fontSize: 14,
              color: _textColor,
            ),
          ),
          if (example['k_meaning'] != null)
            Padding(
              padding: const EdgeInsets.only(top: 4),
              child: Text(
                'Rate k meaning: ${example['k_meaning']}',
                style: TextStyle(
                  fontSize: 14,
                  color: _textColor,
                ),
              ),
            ),
          const SizedBox(height: 4),
          Text(
            'Vertical shift c meaning: ${example['c_meaning']}',
            style: TextStyle(
              fontSize: 14,
              color: _textColor,
            ),
          ),
          const SizedBox(height: 8),
          Row(
            children: [
              Text(
                'X-axis: ${example['x_axis']}',
                style: TextStyle(
                  fontSize: 14,
                  fontStyle: FontStyle.italic,
                  color: _textColor,
                ),
              ),
              const SizedBox(width: 16),
              Text(
                'Y-axis: ${example['y_axis']}',
                style: TextStyle(
                  fontSize: 14,
                  fontStyle: FontStyle.italic,
                  color: _textColor,
                ),
              ),
            ],
          ),
          const SizedBox(height: 8),
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
            decoration: BoxDecoration(
              color: example['type'] == 'growth' ? Colors.green.withOpacity(0.2) : Colors.red.withOpacity(0.2),
              borderRadius: BorderRadius.circular(16),
            ),
            child: Text(
              example['type'] == 'growth' ? 'Exponential Growth' : 'Exponential Decay',
              style: TextStyle(
                fontSize: 14,
                fontWeight: FontWeight.bold,
                color: example['type'] == 'growth' ? Colors.green[700] : Colors.red[700],
              ),
            ),
          ),
        ],
      ),
    );
  }

  // Show examples dialog
  void _showExamplesDialog(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(
          'Real-World Examples',
          style: TextStyle(
            color: _primaryColor,
            fontWeight: FontWeight.bold,
          ),
        ),
        content: Container(
          width: double.maxFinite,
          child: ListView.builder(
            shrinkWrap: true,
            itemCount: _examples.length,
            itemBuilder: (context, index) {
              final example = _examples[index];
              return ListTile(
                title: Text(example['title']),
                subtitle: Text(
                  example['description'],
                  maxLines: 2,
                  overflow: TextOverflow.ellipsis,
                ),
                trailing: Container(
                  padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                  decoration: BoxDecoration(
                    color: example['type'] == 'growth' ? Colors.green.withOpacity(0.2) : Colors.red.withOpacity(0.2),
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: Text(
                    example['type'] == 'growth' ? 'Growth' : 'Decay',
                    style: TextStyle(
                      fontSize: 12,
                      color: example['type'] == 'growth' ? Colors.green[700] : Colors.red[700],
                    ),
                  ),
                ),
                onTap: () {
                  Navigator.of(context).pop();
                  _loadExample(index);
                },
              );
            },
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Cancel'),
          ),
        ],
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: _backgroundColor,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withOpacity(0.2),
            spreadRadius: 1,
            blurRadius: 3,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Title
          Text(
            widget.data['title'] ?? 'Exponential Function Explorer',
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
              color: _textColor,
            ),
          ),

          const SizedBox(height: 8),

          // Description
          if (widget.data['description'] != null)
            Text(
              widget.data['description'],
              style: TextStyle(
                fontSize: 14,
                color: _textColor.withOpacity(0.8),
              ),
            ),

          const SizedBox(height: 16),

          // Graph and controls
          _buildGraphAndControls(),

          // Example section
          if (_showExample)
            _buildExampleSection(),

          // Widget name tag
          if (widget.data['showNameTag'] ?? true)
            Padding(
              padding: const EdgeInsets.only(top: 8),
              child: Text(
                'InteractiveExponentialFunctionExplorer',
                style: TextStyle(
                  color: Colors.grey[600],
                  fontSize: 12,
                  fontStyle: FontStyle.italic,
                ),
              ),
            ),
        ],
      ),
    );
  }
}

/// Custom painter for drawing exponential function graphs
class ExponentialFunctionGraphPainter extends CustomPainter {
  final double a;
  final double b;
  final double c;
  final bool showGrid;
  final bool showKeyPoints;
  final bool showAsymptote;
  final Color primaryColor;
  final Color secondaryColor;
  final Color accentColor;

  // Constants for graph scaling
  final double minX = -5.0;
  final double maxX = 5.0;
  final double minY = -5.0;
  final double maxY = 15.0;

  ExponentialFunctionGraphPainter({
    required this.a,
    required this.b,
    required this.c,
    required this.showGrid,
    required this.showKeyPoints,
    required this.showAsymptote,
    required this.primaryColor,
    required this.secondaryColor,
    required this.accentColor,
  });

  @override
  void paint(Canvas canvas, Size size) {
    final paint = Paint()
      ..color = primaryColor
      ..strokeWidth = 2.0
      ..style = PaintingStyle.stroke;

    final gridPaint = Paint()
      ..color = Colors.grey[300]!
      ..strokeWidth = 0.5
      ..style = PaintingStyle.stroke;

    final axisPaint = Paint()
      ..color = Colors.black87
      ..strokeWidth = 1.0
      ..style = PaintingStyle.stroke;

    final pointPaint = Paint()
      ..color = secondaryColor
      ..strokeWidth = 4.0
      ..style = PaintingStyle.fill;

    final asymptotePaint = Paint()
      ..color = accentColor
      ..strokeWidth = 1.5
      ..style = PaintingStyle.stroke
      ..strokeCap = StrokeCap.round;

    // Set up dashed pattern for asymptote
    final dashPattern = [5.0, 5.0];
    final dashOffset = 0.0;

    // Draw grid
    if (showGrid) {
      _drawGrid(canvas, size, gridPaint);
    }

    // Draw axes
    _drawAxes(canvas, size, axisPaint);

    // Draw horizontal asymptote
    if (showAsymptote) {
      final asymptoteY = _mapYToScreen(c, size);

      // Draw dashed line for asymptote
      final path = Path();
      path.moveTo(0, asymptoteY);

      double distance = 0;
      double dashLength = dashPattern[0];
      double gapLength = dashPattern[1];
      double x = 0;
      bool isDash = true;

      while (x < size.width) {
        if (isDash) {
          path.lineTo(x + dashLength, asymptoteY);
          distance += dashLength;
        } else {
          path.moveTo(x + gapLength, asymptoteY);
          distance += gapLength;
        }

        x = distance;
        isDash = !isDash;
      }

      canvas.drawPath(path, asymptotePaint);
    }

    // Draw function
    _drawFunction(canvas, size, paint);

    // Draw key points
    if (showKeyPoints) {
      // Draw y-intercept (0, a + c)
      final yInterceptX = _mapXToScreen(0, size);
      final yInterceptY = _mapYToScreen(a + c, size);

      canvas.drawCircle(
        Offset(yInterceptX, yInterceptY),
        6,
        pointPaint,
      );
    }

    // Draw labels
    _drawLabels(canvas, size);
  }

  void _drawGrid(Canvas canvas, Size size, Paint paint) {
    // Draw vertical grid lines
    for (double x = minX; x <= maxX; x += 1) {
      final screenX = _mapXToScreen(x, size);
      canvas.drawLine(
        Offset(screenX, 0),
        Offset(screenX, size.height),
        paint,
      );
    }

    // Draw horizontal grid lines
    for (double y = minY; y <= maxY; y += 1) {
      final screenY = _mapYToScreen(y, size);
      canvas.drawLine(
        Offset(0, screenY),
        Offset(size.width, screenY),
        paint,
      );
    }
  }

  void _drawAxes(Canvas canvas, Size size, Paint paint) {
    // Draw x-axis
    final yZero = _mapYToScreen(0, size);
    canvas.drawLine(
      Offset(0, yZero),
      Offset(size.width, yZero),
      paint,
    );

    // Draw y-axis
    final xZero = _mapXToScreen(0, size);
    canvas.drawLine(
      Offset(xZero, 0),
      Offset(xZero, size.height),
      paint,
    );

    // Draw axis ticks
    for (double x = minX; x <= maxX; x += 1) {
      if (x == 0) continue; // Skip origin
      final screenX = _mapXToScreen(x, size);
      canvas.drawLine(
        Offset(screenX, yZero - 5),
        Offset(screenX, yZero + 5),
        paint,
      );
    }

    for (double y = minY; y <= maxY; y += 1) {
      if (y == 0) continue; // Skip origin
      final screenY = _mapYToScreen(y, size);
      canvas.drawLine(
        Offset(xZero - 5, screenY),
        Offset(xZero + 5, screenY),
        paint,
      );
    }
  }

  void _drawFunction(Canvas canvas, Size size, Paint paint) {
    final path = Path();
    bool pathStarted = false;

    // Draw the exponential function as a smooth curve
    for (double x = minX; x <= maxX; x += 0.1) {
      final y = a * math.pow(b, x) + c;

      // Skip points outside the visible y range
      if (y < minY || y > maxY) {
        pathStarted = false;
        continue;
      }

      final screenX = _mapXToScreen(x, size);
      final screenY = _mapYToScreen(y, size);

      if (!pathStarted) {
        path.moveTo(screenX, screenY);
        pathStarted = true;
      } else {
        path.lineTo(screenX, screenY);
      }
    }

    canvas.drawPath(path, paint);
  }

  void _drawLabels(Canvas canvas, Size size) {
    final textStyle = TextStyle(
      color: Colors.black87,
      fontSize: 10,
    );
    final textPainter = TextPainter(
      textDirection: TextDirection.ltr,
    );

    // Draw axis labels
    final yZero = _mapYToScreen(0, size);
    final xZero = _mapXToScreen(0, size);

    // X-axis labels
    for (int i = minX.toInt(); i <= maxX.toInt(); i++) {
      if (i == 0) continue; // Skip zero as it's the origin
      final x = i.toDouble();
      final screenX = _mapXToScreen(x, size);

      textPainter.text = TextSpan(
        text: i.toString(),
        style: textStyle,
      );
      textPainter.layout();
      textPainter.paint(
        canvas,
        Offset(screenX - textPainter.width / 2, yZero + 10),
      );
    }

    // Y-axis labels
    for (int i = minY.toInt(); i <= maxY.toInt(); i++) {
      if (i == 0) continue; // Skip zero as it's the origin
      if (i % 2 != 0) continue; // Only show even numbers to avoid crowding
      final y = i.toDouble();
      final screenY = _mapYToScreen(y, size);

      textPainter.text = TextSpan(
        text: i.toString(),
        style: textStyle,
      );
      textPainter.layout();
      textPainter.paint(
        canvas,
        Offset(xZero + 10, screenY - textPainter.height / 2),
      );
    }

    // Origin label
    textPainter.text = TextSpan(
      text: "0",
      style: textStyle,
    );
    textPainter.layout();
    textPainter.paint(
      canvas,
      Offset(xZero + 5, yZero + 5),
    );

    // Draw key point labels if showing key points
    if (showKeyPoints) {
      // Y-intercept label
      textPainter.text = TextSpan(
        text: "(0, ${(a + c).toStringAsFixed(1)})",
        style: TextStyle(
          color: secondaryColor,
          fontSize: 10,
          fontWeight: FontWeight.bold,
        ),
      );
      textPainter.layout();
      textPainter.paint(
        canvas,
        Offset(
          _mapXToScreen(0, size) + 10,
          _mapYToScreen(a + c, size) - 15,
        ),
      );
    }

    // Draw asymptote label if showing asymptote
    if (showAsymptote) {
      textPainter.text = TextSpan(
        text: "y = ${c.toStringAsFixed(1)}",
        style: TextStyle(
          color: accentColor,
          fontSize: 10,
          fontWeight: FontWeight.bold,
        ),
      );
      textPainter.layout();
      textPainter.paint(
        canvas,
        Offset(
          size.width - textPainter.width - 10,
          _mapYToScreen(c, size) - 15,
        ),
      );
    }
  }

  // Map x coordinate from math space to screen space
  double _mapXToScreen(double x, Size size) {
    return size.width * (x - minX) / (maxX - minX);
  }

  // Map y coordinate from math space to screen space
  double _mapYToScreen(double y, Size size) {
    // Note: Screen coordinates have y increasing downward, math has y increasing upward
    return size.height * (1 - (y - minY) / (maxY - minY));
  }

  @override
  bool shouldRepaint(covariant ExponentialFunctionGraphPainter oldDelegate) {
    return oldDelegate.a != a ||
        oldDelegate.b != b ||
        oldDelegate.c != c ||
        oldDelegate.showGrid != showGrid ||
        oldDelegate.showKeyPoints != showKeyPoints ||
        oldDelegate.showAsymptote != showAsymptote ||
        oldDelegate.primaryColor != primaryColor ||
        oldDelegate.secondaryColor != secondaryColor ||
        oldDelegate.accentColor != accentColor;
  }
}