{"id": "supervised-learning-classification", "title": "Supervised Learning: Classification", "description": "Discover techniques for categorizing data into distinct classes.", "order": 3, "lessons": [{"id": "introduction-to-classification", "title": "Introduction to Classification", "description": "Understand what classification is and how it differs from regression.", "order": 1, "type": "interactive_lesson", "estimatedTimeMinutes": 10, "contentBlocks": [{"id": "slc_l1_s1_what_is_classification", "type": "lesson_screen", "order": 1, "estimatedTimeSeconds": 75, "content": {"headline": "Welcome to Classification!", "body_md": "In supervised learning, we've seen regression predicts continuous values (like house prices).\n\n**Classification**, on the other hand, is about predicting a **discrete category or class label**.\n\nThink: Is this email spam or not spam? Does this image show a cat, a dog, or a bird? Is this transaction fraudulent or legitimate?", "visual": {"type": "giphy_search", "value": "sorting categories"}, "interactive_element": {"type": "button", "button_text": "Examples?"}, "audio_narration_url": null}}, {"id": "slc_l1_s2_classification_examples", "type": "lesson_screen", "order": 2, "estimatedTimeSeconds": 90, "content": {"headline": "Real-World Classification", "body_md": "Classification is used everywhere!\n\n*   **Spam Detection:** Classifying emails as 'spam' or 'not spam'.\n*   **Image Recognition:** Identifying objects in images (e.g., 'cat', 'dog', 'car').\n*   **Medical Diagnosis:** Predicting if a tumor is 'benign' or 'malignant'.\n*   **Sentiment Analysis:** Determining if a movie review is 'positive', 'negative', or 'neutral'.\n\nWhich of these involves predicting from more than two categories?", "visual": {"type": "unsplash_search", "value": "different categories"}, "interactive_element": {"type": "multiple_choice_text", "question_text": "Which example is multi-class classification?", "options": [{"text": "Spam Detection (spam/not spam)", "is_correct": false, "feedback": "This is binary classification (two classes)."}, {"text": "Sentiment Analysis (positive/negative/neutral)", "is_correct": true, "feedback": "Correct! This involves predicting one of three possible categories."}, {"text": "Medical Diagnosis (benign/malignant)", "is_correct": false, "feedback": "This is also binary classification."}]}, "audio_narration_url": null}}, {"id": "slc_l1_s3_classification_vs_regression", "type": "lesson_screen", "order": 3, "estimatedTimeSeconds": 75, "content": {"headline": "Classification vs. Regression", "body_md": "The key difference:\n\n*   **Regression:** Predicts a continuous quantity (e.g., temperature, price, height).\n*   **Classification:** Predicts a discrete category (e.g., spam/not spam, cat/dog, red/green/blue).\n\nIf you're predicting *how much* something will be, it's likely regression. If you're predicting *what kind* of thing it is, it's likely classification.", "visual": {"type": "giphy_search", "value": "compare contrast"}, "interactive_element": {"type": "button", "button_text": "How do we build classifiers?"}, "audio_narration_url": null}}, {"id": "slc_l1_s4_binary_vs_multiclass", "type": "lesson_screen", "order": 4, "estimatedTimeSeconds": 90, "content": {"headline": "Binary vs. Multi-Class", "body_md": "Classification problems can be:\n\n*   **Binary Classification:** Two possible outcome categories (e.g., spam/not spam, yes/no, true/false).\n*   **Multi-Class Classification:** More than two possible outcome categories (e.g., classifying an animal as 'cat', 'dog', 'bird', or 'fish'; or a handwritten digit from 0-9).\n\nDifferent algorithms might be preferred for binary vs. multi-class problems, but the core idea of assigning to a category remains.", "visual": {"type": "unsplash_search", "value": "two options vs many options"}, "interactive_element": {"type": "multiple_choice_text", "question_text": "Classifying a fruit as 'apple', 'banana', or 'orange' is:", "options": [{"text": "Binary Classification", "is_correct": false, "feedback": "Binary means two classes. This example has three."}, {"text": "Multi-Class Classification", "is_correct": true, "feedback": "Correct! There are more than two possible fruit categories."}, {"text": "Regression", "is_correct": false, "feedback": "Regression predicts a continuous value, not a category."}]}, "audio_narration_url": null}}, {"id": "slc_l1_s5_summary", "type": "lesson_screen", "order": 5, "estimatedTimeSeconds": 60, "content": {"headline": "Classification Intro: Recap", "body_md": "Key points about Classification:\n\n*   Predicts a **discrete category**.\n*   Can be **binary** (two classes) or **multi-class** (more than two classes).\n*   Many real-world applications rely on it!\n\nNext, we'll look at some popular classification algorithms.", "visual": {"type": "giphy_search", "value": "sorting complete"}, "interactive_element": {"type": "button", "button_text": "Explore Algorithms"}, "audio_narration_url": null}}]}, {"id": "common-classification-algorithms", "title": "Common Classification Algorithms", "description": "Get an overview of popular algorithms used for classification tasks.", "order": 2, "type": "interactive_lesson", "estimatedTimeMinutes": 12, "contentBlocks": [{"id": "slc_l2_s1_algo_intro", "type": "lesson_screen", "order": 1, "estimatedTimeSeconds": 75, "content": {"headline": "Tools of the Trade: Classification Algorithms", "body_md": "There are many algorithms designed to solve classification problems. Each has its strengths and weaknesses.\n\nLet's briefly touch upon a few common ones:\n\n*   Logistic Regression\n*   K-Nearest Neighbors (KNN)\n*   Support Vector Machines (SVM)\n*   Decision Trees & Random Forests\n*   Naive Bayes", "visual": {"type": "giphy_search", "value": "toolbox tools"}, "interactive_element": {"type": "button", "button_text": "Tell Me About Logistic Regression"}, "audio_narration_url": null}}, {"id": "slc_l2_s2_logistic_regression", "type": "lesson_screen", "order": 2, "estimatedTimeSeconds": 90, "content": {"headline": "Logistic Regression", "body_md": "Despite its name, **Logistic Regression** is used for **classification**, not regression!\n\nIt models the probability that a given input point belongs to a particular class. It's widely used for binary classification problems (e.g., yes/no, spam/not spam).\n\nIt uses a logistic function (or sigmoid function) to squash the output probability between 0 and 1.", "visual": {"type": "unsplash_search", "value": "s-curve graph"}, "interactive_element": {"type": "multiple_choice_text", "question_text": "Logistic Regression is primarily used for:", "options": [{"text": "Predicting continuous stock prices.", "is_correct": false, "feedback": "That's a regression task. Logistic Regression is for classification."}, {"text": "Classifying an email as spam or not spam.", "is_correct": true, "feedback": "Correct! This is a classic binary classification problem well-suited for Logistic Regression."}, {"text": "Finding hidden groups in unlabeled data.", "is_correct": false, "feedback": "That's unsupervised learning (e.g., clustering)."}]}, "audio_narration_url": null}}, {"id": "slc_l2_s3_knn", "type": "lesson_screen", "order": 3, "estimatedTimeSeconds": 100, "content": {"headline": "K-Nearest Neighbors (KNN)", "body_md": "**K-Nearest Neighbors (KNN)** is a simple, instance-based learning algorithm.\n\nTo classify a new data point, it looks at the 'K' closest labeled data points (its neighbors) in the training set. The new point is then assigned to the class that is most common among its K neighbors.\n\nImagine asking your K closest friends for their opinion to make a decision!", "visual": {"type": "giphy_search", "value": "neighbors group"}, "interactive_element": {"type": "button", "button_text": "What about Decision Trees?"}, "audio_narration_url": null}}, {"id": "slc_l2_s4_decision_trees", "type": "lesson_screen", "order": 4, "estimatedTimeSeconds": 100, "content": {"headline": "Decision Trees", "body_md": "**Decision Trees** are flowchart-like structures where each internal node represents a 'test' on an attribute (e.g., 'Is Outlook Sunny?'), each branch represents the outcome of the test, and each leaf node represents a class label (decision taken).\n\nThey are intuitive to understand and visualize. They work by splitting the data into subsets based on feature values, aiming to create 'pure' subsets that mostly belong to one class.", "visual": {"type": "unsplash_search", "value": "flowchart decision tree"}, "interactive_element": {"type": "button", "button_text": "And Random Forests?"}, "audio_narration_url": null}}, {"id": "slc_l2_s5_random_forests", "type": "lesson_screen", "order": 5, "estimatedTimeSeconds": 90, "content": {"headline": "Random Forests: The Power of Many Trees", "body_md": "A **Random Forest** is an **ensemble learning** method. It builds multiple decision trees during training and outputs the class that is the mode of the classes output by individual trees.\n\nThink of it as asking many different 'experts' (decision trees) and going with the majority opinion. This often leads to better accuracy and helps prevent overfitting (where a single tree might learn the training data too specifically).", "visual": {"type": "giphy_search", "value": "forest of trees"}, "interactive_element": {"type": "multiple_choice_text", "question_text": "What is a key advantage of Random Forests over a single Decision Tree?", "options": [{"text": "They are much simpler to understand.", "is_correct": false, "feedback": "Individual decision trees are simpler, but a forest is more complex."}, {"text": "They generally have better performance and are less prone to overfitting.", "is_correct": true, "feedback": "Correct! The ensemble nature of Random Forests often leads to more robust models."}, {"text": "They require significantly less data to train.", "is_correct": false, "feedback": "Both can require substantial data for good performance."}]}, "audio_narration_url": null}}, {"id": "slc_l2_s6_algo_summary", "type": "lesson_screen", "order": 6, "estimatedTimeSeconds": 60, "content": {"headline": "Algorithm Overview: Recap", "body_md": "We've briefly looked at:\n\n*   **Logistic Regression:** Good for binary classification.\n*   **K-Nearest Neighbors (KNN):** Simple, based on proximity.\n*   **Decision Trees:** Intuitive, flowchart-like.\n*   **Random Forests:** Ensemble of trees, often more robust.\n\nMany other algorithms exist (like SVMs, Naive Bayes), each with its own use cases!", "visual": {"type": "unsplash_search", "value": "gears algorithm"}, "interactive_element": {"type": "button", "button_text": "Next Lesson"}, "audio_narration_url": null}}]}, {"id": "evaluating-classification-models", "title": "Evaluating Classification Models", "description": "Learn how to measure the performance of classification models using various metrics.", "order": 3, "type": "interactive_lesson", "estimatedTimeMinutes": 12, "contentBlocks": [{"id": "slc_l3_s1_intro_eval", "type": "lesson_screen", "order": 1, "estimatedTimeSeconds": 75, "content": {"headline": "How Good is Your Classifier?", "body_md": "Just like with regression, we need to evaluate how well our classification models perform. But the metrics are different because we're dealing with categories, not continuous values.\n\nCommon metrics include Accuracy, Precision, Recall, F1-Score, and the Confusion Matrix.", "visual": {"type": "giphy_search", "value": "judge scorecard"}, "interactive_element": {"type": "button", "button_text": "Let's Start with Accuracy"}, "audio_narration_url": null}}, {"id": "slc_l3_s2_accuracy", "type": "lesson_screen", "order": 2, "estimatedTimeSeconds": 90, "content": {"headline": "Accuracy: The Simplest Metric", "body_md": "**Accuracy** is the proportion of predictions that the model got right.\n\n`Accuracy = (Number of Correct Predictions) / (Total Number of Predictions)`\n\nWhile simple, accuracy can be misleading, especially if you have **imbalanced classes** (e.g., 99% not spam, 1% spam). A model that always predicts 'not spam' would be 99% accurate but useless for catching spam!", "visual": {"type": "unsplash_search", "value": "target bullseye"}, "interactive_element": {"type": "multiple_choice_text", "question_text": "If a model correctly identifies 90 out of 100 emails, its accuracy is:", "options": [{"text": "80%", "is_correct": false, "feedback": "Accuracy = (Correct Predictions / Total Predictions) * 100"}, {"text": "90%", "is_correct": true, "feedback": "Correct! (90/100) * 100 = 90%"}, {"text": "10%", "is_correct": false, "feedback": "This would be the error rate, not accuracy."}]}, "audio_narration_url": null}}, {"id": "slc_l3_s3_confusion_matrix_intro", "type": "lesson_screen", "order": 3, "estimatedTimeSeconds": 100, "content": {"headline": "The Confusion Matrix: A Deeper Look", "body_md": "A **Confusion Matrix** gives a more detailed breakdown of a classifier's performance. For a binary classifier (e.g., spam/not spam), it looks like this:\n\n|                   | Predicted: Not Spam | Predicted: Spam |\n|-------------------|---------------------|-----------------|\n| **Actual: Not Spam** | True Negative (TN)  | False Positive (FP) |\n| **Actual: Spam**    | False Negative (FN) | True Positive (TP)  |\n\n*   **TP:** Correctly identified as spam.\n*   **TN:** Correctly identified as not spam.\n*   **FP (Type I Error):** Incorrectly identified as spam (a legitimate email flagged!).\n*   **FN (Type II Error):** Incorrectly identified as not spam (a spam email missed!).", "visual": {"type": "static_text", "value": "TN | FP\nFN | TP"}, "interactive_element": {"type": "button", "button_text": "What are Precision & Recall?"}, "audio_narration_url": null}}, {"id": "slc_l3_s4_precision_recall", "type": "lesson_screen", "order": 4, "estimatedTimeSeconds": 120, "content": {"headline": "Precision and Recall", "body_md": "**Precision:** Out of all the times the model predicted 'Spam', how many were actually spam?\n`Precision = TP / (TP + FP)`\n*   High precision means fewer false positives (less legitimate email marked as spam).\n\n**Recall (Sensitivity):** Out of all the actual 'Spam' emails, how many did the model correctly identify?\n`Recall = TP / (TP + FN)`\n*   High recall means fewer false negatives (less spam missed).\n\nOften, there's a trade-off between precision and recall.", "visual": {"type": "unsplash_search", "value": "balance scale"}, "interactive_element": {"type": "multiple_choice_text", "question_text": "If you want to minimize missing actual spam emails, you should optimize for:", "options": [{"text": "Higher Precision", "is_correct": false, "feedback": "Higher precision minimizes legitimate emails being marked as spam."}, {"text": "Higher Recall", "is_correct": true, "feedback": "Correct! Higher recall means you're catching more of the actual spam."}, {"text": "Lower Accuracy", "is_correct": false, "feedback": "We generally want higher accuracy, but recall is more specific here."}]}, "audio_narration_url": null}}, {"id": "slc_l3_s5_f1_score", "type": "lesson_screen", "order": 5, "estimatedTimeSeconds": 90, "content": {"headline": "F1-Score: The Harmonic Mean", "body_md": "The **F1-Score** is the harmonic mean of Precision and Recall. It tries to find a balance between the two.\n\n`F1-Score = 2 * (Precision * Recall) / (Precision + Recall)`\n\n*   Ranges from 0 to 1. Higher is better.\n*   It's useful when you want a single number to represent model performance, especially if classes are imbalanced.", "visual": {"type": "giphy_search", "value": "balance harmony"}, "interactive_element": {"type": "button", "button_text": "Let's Summarize"}, "audio_narration_url": null}}, {"id": "slc_l3_s6_eval_summary", "type": "lesson_screen", "order": 6, "estimatedTimeSeconds": 75, "content": {"headline": "Classification Evaluation: Recap", "body_md": "Key metrics for classification models:\n\n*   **Accuracy:** Overall correctness (can be misleading).\n*   **Confusion Matrix:** Detailed breakdown (TP, TN, FP, FN).\n*   **Precision:** How many positive predictions were correct.\n*   **Recall:** How many actual positives were found.\n*   **F1-Score:** Harmonic mean of Precision and Recall.\n\nUnderstanding these helps you choose and fine-tune the best model!", "visual": {"type": "unsplash_search", "value": "dashboard metrics"}, "interactive_element": {"type": "button", "button_text": "Finish Lesson"}, "audio_narration_url": null}}]}], "moduleTest": {"id": "supervised-learning-classification-test", "title": "Module Test: Supervised Learning - Classification", "description": "Test your understanding of classification techniques in supervised learning.", "type": "module_test_interactive", "estimatedTimeMinutes": 15, "contentBlocks": [{"id": "slc_test_q1_classification_def", "type": "question_screen", "order": 1, "estimatedTimeSeconds": 60, "content": {"headline": "Question 1: What is Classification?", "body_md": "Which of the following best describes a classification problem in machine learning?", "visual": {"type": "giphy_search", "value": "sorting objects"}, "interactive_element": {"type": "multiple_choice_text", "question_text": "Best description of a classification problem?", "options": [{"text": "Predicting the exact temperature for tomorrow.", "is_correct": false, "feedback": "Predicting an exact numerical value is a regression problem."}, {"text": "Assigning an email to one of several predefined categories (e.g., 'inbox', 'spam', 'promotions').", "is_correct": true, "feedback": "Correct! Classification assigns items to discrete categories."}, {"text": "Finding natural groupings in a dataset without predefined labels.", "is_correct": false, "feedback": "This describes clustering, an unsupervised learning task."}]}, "audio_narration_url": null}}, {"id": "slc_test_q2_binary_vs_multiclass", "type": "question_screen", "order": 2, "estimatedTimeSeconds": 75, "content": {"headline": "Question 2: Binary vs. Multi-Class", "body_md": "A model that predicts whether a loan application should be 'Approved' or 'Rejected' is an example of:", "visual": {"type": "unsplash_search", "value": "approved rejected stamp"}, "interactive_element": {"type": "multiple_choice_text", "question_text": "'Approved' or 'Rejected' loan application is:", "options": [{"text": "Binary Classification", "is_correct": true, "feedback": "Correct! There are two distinct outcome categories."}, {"text": "Multi-Class Classification", "is_correct": false, "feedback": "Multi-class involves more than two categories."}, {"text": "Regression", "is_correct": false, "feedback": "Regression would predict a continuous value, like the loan amount."}]}, "audio_narration_url": null}}, {"id": "slc_test_q3_logistic_regression_use", "type": "question_screen", "order": 3, "estimatedTimeSeconds": 75, "content": {"headline": "Question 3: Logistic Regression", "body_md": "Which statement is TRUE about Logistic Regression?", "visual": {"type": "giphy_search", "value": "s-curve"}, "interactive_element": {"type": "multiple_choice_text", "question_text": "True statement about Logistic Regression?", "options": [{"text": "It is primarily used for predicting continuous numerical values.", "is_correct": false, "feedback": "Despite its name, Logistic Regression is a classification algorithm."}, {"text": "It outputs a probability that an input belongs to a particular class.", "is_correct": true, "feedback": "Correct! It uses a logistic function to output probabilities, typically for binary classification."}, {"text": "It is an unsupervised learning algorithm.", "is_correct": false, "feedback": "Logistic Regression is a supervised learning algorithm as it requires labeled data."}]}, "audio_narration_url": null}}, {"id": "slc_test_q4_accuracy_pitfall", "type": "question_screen", "order": 4, "estimatedTimeSeconds": 90, "content": {"headline": "Question 4: A<PERSON><PERSON><PERSON><PERSON>", "body_md": "When can accuracy be a misleading metric for evaluating a classification model?", "visual": {"type": "unsplash_search", "value": "warning sign"}, "interactive_element": {"type": "multiple_choice_text", "question_text": "When is accuracy misleading?", "options": [{"text": "When the number of features is very large.", "is_correct": false, "feedback": "While many features can pose challenges, it doesn't inherently make accuracy misleading."}, {"text": "When the dataset has imbalanced classes (e.g., one class is much more frequent than others).", "is_correct": true, "feedback": "Correct! A model can achieve high accuracy by simply predicting the majority class, even if it performs poorly on the minority class."}, {"text": "When the model is a Decision Tree.", "is_correct": false, "feedback": "The choice of algorithm doesn't inherently make accuracy misleading; class imbalance is the key here."}]}, "audio_narration_url": null}}, {"id": "slc_test_q5_precision_recall_tradeoff", "type": "question_screen", "order": 5, "estimatedTimeSeconds": 75, "content": {"headline": "Question 5: Precision vs. <PERSON><PERSON><PERSON>", "body_md": "In a medical diagnosis scenario where failing to detect a disease (a False Negative) is very costly, which metric would you prioritize?", "visual": {"type": "giphy_search", "value": "doctor thinking"}, "interactive_element": {"type": "multiple_choice_text", "question_text": "Prioritize which metric to minimize False Negatives?", "options": [{"text": "Precision", "is_correct": false, "feedback": "High precision minimizes False Positives (e.g., wrongly diagnosing a healthy person)."}, {"text": "Recall", "is_correct": true, "feedback": "Correct! High recall (sensitivity) ensures that most actual positive cases (diseased patients) are identified, minimizing False Negatives."}, {"text": "Accuracy", "is_correct": false, "feedback": "Accuracy might be high even if many sick patients are missed, especially if the disease is rare."}]}, "audio_narration_url": null}}]}}