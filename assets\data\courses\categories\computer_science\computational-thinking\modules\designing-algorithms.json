{"id": "designing-algorithms", "title": "Designing Algorithms", "description": "Learn to create step-by-step instructions to solve problems efficiently.", "order": 3, "lessons": [{"id": "algorithm-flowcharts", "title": "Algorithm Flowcharts", "description": "Learn to visualize algorithms using flowcharts to understand their structure and flow.", "order": 1, "type": "interactive_lesson", "estimatedTimeMinutes": 15, "contentBlocks": [{"id": "af-screen1-intro", "type": "lesson_screen", "order": 1, "estimatedTimeSeconds": 60, "content": {"headline": "Visualizing Algorithms with Flowcharts", "body_md": "Algorithms are step-by-step procedures for solving problems. Flowcharts provide a visual way to represent these procedures, making them easier to understand, communicate, and analyze.\n\nA flowchart uses different shapes to represent different types of steps in an algorithm, with arrows showing the flow of execution.", "visual": {"type": "image", "url": "assets/images/cs/flowchart_example.png", "alt": "A simple flowchart showing the steps to make a cup of tea", "caption": "A flowchart showing the algorithm for making a cup of tea"}, "interactive_element": {"type": "none"}}}, {"id": "af-screen2-symbols", "type": "lesson_screen", "order": 2, "estimatedTimeSeconds": 90, "content": {"headline": "Flowchart Symbols and Their Meanings", "body_md": "Each shape in a flowchart has a specific meaning:\n\n- **Oval**: Start/End points\n- **Rectangle**: Process or action step\n- **Parallelogram**: Input/Output operations\n- **Diamond**: Decision point (Yes/No or True/False)\n- **Arrows**: Flow direction\n\nLearning these symbols is the first step to creating clear and effective flowcharts.", "visual": {"type": "image", "url": "assets/images/cs/flowchart_symbols.png", "alt": "Common flowchart symbols and their meanings", "caption": "Standard flowchart symbols used in algorithm design"}, "interactive_element": {"type": "multiple_choice", "question": "Which flowchart symbol is used for decision points?", "options": ["Rectangle", "Oval", "Diamond", "Parallelogram"], "correctAnswer": "Diamond", "feedback": {"correct": "Correct! Diamonds represent decision points where the algorithm can take different paths based on a condition.", "incorrect": "Not quite. Decision points that lead to different paths based on conditions (like if-then statements) are represented by diamond shapes."}}}}, {"id": "af-screen3-example", "type": "lesson_screen", "order": 3, "estimatedTimeSeconds": 120, "content": {"headline": "A Simple Algorithm Example", "body_md": "Let's look at a simple algorithm for finding the maximum of two numbers, and how we can represent it as a flowchart:\n\n1. Start\n2. Input two numbers A and B\n3. If A > B, set Max = A\n4. Otherwise, set Max = B\n5. Output Max\n6. End\n\nThis straightforward algorithm can be clearly visualized using a flowchart.", "visual": {"type": "image", "url": "assets/images/cs/max_flowchart.png", "alt": "Flowchart for finding the maximum of two numbers", "caption": "Flowchart representation of the maximum-finding algorithm"}, "interactive_element": {"type": "text_input", "question": "If we input A=15 and B=7 into this algorithm, what will be the output?", "correctAnswer": "15", "caseSensitive": false, "feedback": {"correct": "Correct! Since 15 > 7, the algorithm sets Max = 15 and outputs it.", "incorrect": "Let's trace through the algorithm: We input A=15 and B=7. Since A > B (15 > 7), we set Max = A, which is 15. Then we output Max, which is 15."}}}}, {"id": "af-screen4-benefits", "type": "lesson_screen", "order": 4, "estimatedTimeSeconds": 90, "content": {"headline": "Benefits of Using Flowcharts", "body_md": "Flowcharts offer several advantages when designing algorithms:\n\n- **Clarity**: They provide a clear visual representation of the algorithm's logic\n- **Communication**: They make it easier to explain algorithms to others\n- **Analysis**: They help identify potential issues or inefficiencies\n- **Planning**: They assist in planning before writing actual code\n- **Documentation**: They serve as valuable documentation for future reference\n\nMany programmers sketch flowcharts before writing code to organize their thoughts.", "visual": {"type": "image", "url": "assets/images/cs/flowchart_benefits.png", "alt": "A programmer sketching a flowchart before coding", "caption": "Planning with flowcharts helps create more efficient code"}, "interactive_element": {"type": "multiple_choice", "question": "Which of the following is NOT a benefit of using flowcharts?", "options": ["They help identify potential issues in algorithms", "They automatically generate code in any programming language", "They make it easier to communicate algorithms to others", "They provide visual clarity for complex logic"], "correctAnswer": "They automatically generate code in any programming language", "feedback": {"correct": "Correct! While flowcharts help with planning and understanding algorithms, they don't automatically generate code. You still need to translate the flowchart into actual programming code.", "incorrect": "Incorrect. Flowcharts are helpful planning and communication tools, but they don't automatically generate code in programming languages. That requires a separate step of translation from the flowchart to actual code."}}}}, {"id": "af-screen5-interactive", "type": "lesson_screen", "order": 5, "estimatedTimeSeconds": 300, "content": {"headline": "Build Your Own Algorithm Flowchart", "body_md": "Now it's your turn to create a flowchart! In this interactive exercise, you'll build a flowchart for a bubble sort algorithm, which is used to sort a list of numbers in ascending order.\n\nDrag the flowchart elements from the palette on the left to the canvas, and connect them to create a complete algorithm. The challenge is to arrange them in the correct order to implement the bubble sort algorithm.", "visual": {"type": "none"}, "interactive_element": {"type": "custom", "widget_type": "interactive_algorithm_flowchart_builder", "widget_data": {"id": "interactive_algorithm_flowchart_builder_1"}}}}, {"id": "af-screen6-conclusion", "type": "lesson_screen", "order": 6, "estimatedTimeSeconds": 60, "content": {"headline": "From Flowcharts to Code", "body_md": "Once you've designed an algorithm using a flowchart, the next step is to translate it into actual code. Each shape in your flowchart corresponds to specific programming constructs:\n\n- **Process** (rectangle) → Assignment statements, function calls\n- **Decision** (diamond) → if-else statements, switch-case, loops\n- **Input/Output** (parallelogram) → read/write operations, print statements\n\nWith practice, you'll be able to move smoothly between flowcharts and code, using each where it's most appropriate.", "visual": {"type": "image", "url": "assets/images/cs/flowchart_to_code.png", "alt": "Comparison of flowchart elements to programming code", "caption": "Mapping flowchart elements to programming constructs"}, "interactive_element": {"type": "multiple_choice", "question": "Which programming construct would you use to implement a diamond (decision) shape from a flowchart?", "options": ["A simple assignment statement (x = 5)", "A function definition (def calculate())", "An if-else statement or loop", "A print statement"], "correctAnswer": "An if-else statement or loop", "feedback": {"correct": "Correct! Decision diamonds in flowcharts represent branching logic, which is implemented in code using if-else statements, switch-case statements, or loops that have conditional checks.", "incorrect": "Not quite. The diamond shape in flowcharts represents a decision point where the flow can take different paths based on a condition. This is implemented in code using conditional statements like if-else or loops with conditions."}}}}, {"id": "af-screen7-summary", "type": "lesson_screen", "order": 7, "estimatedTimeSeconds": 60, "content": {"headline": "Key Takeaways", "body_md": "In this lesson, you've learned:\n\n- Flowcharts are visual representations of algorithms\n- Different shapes represent different types of steps (process, decision, input/output)\n- Flowcharts help with clarity, communication, and planning\n- Flowcharts can be translated into code using corresponding programming constructs\n\nIn the next lesson, we'll explore common algorithm patterns that appear across many different problems.", "visual": {"type": "image", "url": "assets/images/cs/flowchart_summary.png", "alt": "Summary of flowchart concepts", "caption": "Flowcharts bridge the gap between problem-solving and coding"}, "interactive_element": {"type": "none"}}}]}, {"id": "what-is-an-algorithm", "title": "What is an Algorithm?", "description": "Understand the definition and characteristics of algorithms.", "order": 2, "type": "interactive_lesson", "estimatedTimeMinutes": 10, "contentBlocks": [{"id": "screen1_intro_algorithms", "type": "lesson_screen", "order": 1, "estimatedTimeSeconds": 60, "content": {"headline": "The Recipe for Problem Solving", "body_md": "An algorithm is a finite sequence of well-defined, computer-implementable instructions, typically to solve a class of problems or to perform a computation. Think of it as a recipe: a set of steps to achieve a specific outcome.\n\nWhy are algorithms so central to computer science?", "visual": {"type": "giphy_search", "value": "recipe cooking"}, "interactive_element": {"type": "button", "button_text": "Key Characteristics"}, "audio_narration_url": null}}, {"id": "screen2_characteristics_of_algorithms", "type": "lesson_screen", "order": 2, "estimatedTimeSeconds": 90, "content": {"headline": "Characteristics of a Good Algorithm", "body_md": "Good algorithms generally have these properties:\n\n*   **Input:** An algorithm has zero or more well-defined inputs.\n*   **Output:** An algorithm has one or more well-defined outputs, and should match the desired outcome.\n*   **Finiteness:** An algorithm must terminate after a finite number of steps.\n*   **Definiteness:** Each step must be precisely defined; actions to be carried out must be rigorously and unambiguously specified.\n*   **Effectiveness:** Every instruction must be basic enough to be carried out, in principle, by a person using only pencil and paper.\n\nWhich characteristic ensures an algorithm doesn't run forever?", "visual": {"type": "unsplash_search", "value": "checklist"}, "interactive_element": {"type": "multiple_choice_text", "question_text": "Ensures algorithm termination?", "options": [{"text": "Input", "is_correct": false, "feedback": "Input is what the algorithm works on."}, {"text": "Finiteness", "is_correct": true, "feedback": "Correct! Finiteness guarantees the algorithm will eventually stop."}, {"text": "Definiteness", "is_correct": false, "feedback": "Definiteness ensures clarity of each step, not termination."}, {"text": "Effectiveness", "is_correct": false, "feedback": "Effectiveness ensures each step is doable."}]}, "audio_narration_url": null}}, {"id": "screen3_algorithms_everyday", "type": "lesson_screen", "order": 3, "estimatedTimeSeconds": 75, "content": {"headline": "Algorithms in Daily Life", "body_md": "You follow algorithms all the time!\n\n*   A recipe for baking a cake.\n*   Instructions for assembling furniture.\n*   The process of doing laundry.\n*   Your morning routine to get ready.\n\nCan you describe a simple algorithm you use?", "visual": {"type": "giphy_search", "value": "following instructions"}, "interactive_element": {"type": "text_input", "question_text": "Describe a simple algorithm you use (2-3 steps):", "placeholder_text": "e.g., Making tea: Boil water, add tea bag, steep.", "correct_answer_regex": ".+", "feedback_correct": "That's a great example of an everyday algorithm!"}, "audio_narration_url": null}}, {"id": "screen4_algorithms_vs_programs", "type": "lesson_screen", "order": 4, "estimatedTimeSeconds": 90, "content": {"headline": "Algorithm vs. Program", "body_md": "An **algorithm** is the logical idea or method for solving a problem.\nA **program** is the implementation of an algorithm in a specific programming language.\n\nThink of an algorithm as the blueprint, and the program as the actual building constructed from that blueprint. One algorithm can be implemented in many different programming languages.", "visual": {"type": "unsplash_search", "value": "blueprint and building"}, "interactive_element": {"type": "button", "button_text": "How to Represent Them?"}, "audio_narration_url": null}}, {"id": "screen5_lesson1_summary", "type": "lesson_screen", "order": 5, "estimatedTimeSeconds": 60, "content": {"headline": "Lesson Recap: What is an Algorithm?", "body_md": "We've learned:\n\n*   An algorithm is a step-by-step procedure for solving a problem.\n*   Key characteristics: Input, Output, Finiteness, Definiteness, Effectiveness.\n*   Algorithms are common in daily life.\n*   An algorithm is the logic; a program is its implementation.\n\nNext, let's see how to represent algorithms.", "visual": {"type": "giphy_search", "value": "thinking process"}, "interactive_element": {"type": "button", "button_text": "Representing Algorithms"}, "audio_narration_url": null}}]}, {"id": "representing-algorithms", "title": "Representing Algorithms: Pseudocode & Flowcharts", "description": "Learn common ways to describe and visualize algorithms before coding.", "order": 3, "type": "interactive_lesson", "estimatedTimeMinutes": 12, "contentBlocks": [{"id": "screen1_why_represent", "type": "lesson_screen", "order": 1, "estimatedTimeSeconds": 60, "content": {"headline": "Planning Before Coding", "body_md": "Before writing a program, it's crucial to design and represent the algorithm. This helps clarify the logic and catch errors early. Two common methods are pseudocode and flowcharts.", "visual": {"type": "giphy_search", "value": "planning board"}, "interactive_element": {"type": "button", "button_text": "Explore Pseudocode"}, "audio_narration_url": null}}, {"id": "screen2_pseudocode", "type": "lesson_screen", "order": 2, "estimatedTimeSeconds": 100, "content": {"headline": "Pseudocode: Informal High-Level Description", "body_md": "Pseudocode is a plain language description of the steps in an algorithm. It's not tied to any specific programming language syntax but uses common programming structures (like IF-THEN-ELSE, WHILE, FOR).\n\n**Example: Algorithm to find the largest of two numbers (a, b)**\n```\nIF a > b THEN\n  largest = a\nELSE\n  largest = b\nENDIF\nDISPLAY largest\n```\nIt's easy to read and understand.", "visual": {"type": "unsplash_search", "value": "writing notes"}, "interactive_element": {"type": "multiple_choice_text", "question_text": "Main advantage of pseudocode?", "options": [{"text": "It can be directly compiled and run.", "is_correct": false, "feedback": "Pseudocode is for human understanding, not direct execution."}, {"text": "It focuses on logic without worrying about specific language syntax.", "is_correct": true, "feedback": "Correct! This makes it a great tool for algorithm design."}, {"text": "It is highly visual.", "is_correct": false, "feedback": "Pseudocode is text-based. Flowcharts are visual."}]}, "audio_narration_url": null}}, {"id": "screen3_flowcharts", "type": "lesson_screen", "order": 3, "estimatedTimeSeconds": 100, "content": {"headline": "Flowcharts: Visual Representation", "body_md": "A flowchart is a diagrammatic representation of an algorithm. It uses standard symbols to represent different types of instructions and shows the flow of control.\n\n*   **Ovals:** Start/End\n*   **Rectangles:** Process/Instruction\n*   **Diamonds:** Decision (e.g., IF condition)\n*   **Parallelograms:** Input/Output\n*   **Arrows:** Flow of logic\n\nFlowcharts are great for visualizing complex logic.", "visual": {"type": "giphy_search", "value": "flowchart diagram"}, "interactive_element": {"type": "button", "button_text": "Flowchart Example"}, "audio_narration_url": null}}, {"id": "screen4_flowchart_example", "type": "lesson_screen", "order": 4, "estimatedTimeSeconds": 90, "content": {"headline": "Flowchart: Largest of Two Numbers", "body_md": "Imagine a flowchart for finding the largest of two numbers:\n\n1.  **Start** (Oval)\n2.  Input A, B (Parallelogram)\n3.  Is A > B? (Diamond)\n    *   Yes: Largest = A (Rectangle) -> Go to step 5\n    *   No: Largest = B (Rectangle) -> Go to step 5\n4.  (Flow merges here)\n5.  Display Largest (Parallelogram)\n6.  **End** (Oval)\n\nThis visually maps out the decision and process.", "visual": {"type": "local_asset", "value": "placeholder_flowchart_image.png"}, "interactive_element": {"type": "multiple_choice_text", "question_text": "In a flowchart, what shape represents a decision?", "options": [{"text": "Rectangle", "is_correct": false, "feedback": "Rectangles usually represent a process or instruction."}, {"text": "Oval", "is_correct": false, "feedback": "Ovals represent the start or end points."}, {"text": "Diamond", "is_correct": true, "feedback": "Correct! Diamonds are used for decision points (like IF statements)."}]}, "audio_narration_url": null}}, {"id": "screen5_lesson2_summary", "type": "lesson_screen", "order": 5, "estimatedTimeSeconds": 60, "content": {"headline": "Lesson Recap: Representing Algorithms", "body_md": "We've learned about:\n\n*   **Pseudocode:** Informal, text-based description of algorithm logic.\n*   **Flowcharts:** Visual, diagrammatic representation using standard symbols.\n\nBoth are valuable tools for designing and communicating algorithms before implementation.", "visual": {"type": "giphy_search", "value": "blueprint design"}, "interactive_element": {"type": "button", "button_text": "Next: Common Structures"}, "audio_narration_url": null}}]}, {"id": "basic-algorithmic-structures", "title": "Basic Algorithmic Structures", "description": "Understand sequence, selection, and iteration – the building blocks of algorithms.", "order": 4, "type": "interactive_lesson", "estimatedTimeMinutes": 10, "contentBlocks": [{"id": "screen1_building_blocks_intro", "type": "lesson_screen", "order": 1, "estimatedTimeSeconds": 60, "content": {"headline": "The Building Blocks", "body_md": "Most algorithms, no matter how complex, are built from three basic control structures:\n\n1.  **Sequence:** Steps are performed one after another.\n2.  **Selection (Decision):** A choice is made between different paths (e.g., IF-THEN-ELSE).\n3.  **Iteration (Loop):** A group of steps is repeated (e.g., WHILE, FOR).\n\nLet's look at each.", "visual": {"type": "giphy_search", "value": "building blocks"}, "interactive_element": {"type": "button", "button_text": "Explore Sequence"}, "audio_narration_url": null}}, {"id": "screen2_sequence", "type": "lesson_screen", "order": 2, "estimatedTimeSeconds": 75, "content": {"headline": "Sequence Structure", "body_md": "This is the simplest structure. Instructions are executed in the order they are written, one after the other.\n\n**Example: Making a Sandwich**\n1.  Get two slices of bread.\n2.  Spread butter on one slice.\n3.  Add filling.\n4.  Place other slice on top.\n\nMost lines of code in a simple script run in sequence.", "visual": {"type": "unsplash_search", "value": "dominoes falling in line"}, "interactive_element": {"type": "button", "button_text": "Next: Selection"}, "audio_narration_url": null}}, {"id": "screen3_selection", "type": "lesson_screen", "order": 3, "estimatedTimeSeconds": 90, "content": {"headline": "Selection (Decision) Structure", "body_md": "This structure allows the algorithm to choose a path based on a condition. The `IF-THEN-ELSE` statement is a common example.\n\n**Example: Deciding what to wear**\n```\nIF it is raining THEN\n  Wear a raincoat\nELSE\n  Wear a t-shirt\nENDIF\n```\nThis allows algorithms to adapt to different situations.", "visual": {"type": "giphy_search", "value": "fork in road"}, "interactive_element": {"type": "multiple_choice_text", "question_text": "Selection allows an algorithm to:", "options": [{"text": "Repeat steps multiple times.", "is_correct": false, "feedback": "That's iteration."}, {"text": "Perform steps in a fixed order.", "is_correct": false, "feedback": "That's sequence."}, {"text": "Choose between different actions based on a condition.", "is_correct": true, "feedback": "Correct! Selection is about making choices."}]}, "audio_narration_url": null}}, {"id": "screen4_iteration", "type": "lesson_screen", "order": 4, "estimatedTimeSeconds": 90, "content": {"headline": "Iteration (Loop) Structure", "body_md": "Iteration allows a set of instructions to be repeated multiple times. Common loop types are `WHILE` loops (repeat as long as a condition is true) and `FOR` loops (repeat a specific number of times).\n\n**Example: Watering 5 plants**\n```\nFOR each plant from 1 to 5:\n  Get watering can\n  Pour water on plant\nENDFOR\n```\nLoops are essential for processing collections of data or performing repetitive tasks.", "visual": {"type": "giphy_search", "value": "loop repeat"}, "interactive_element": {"type": "button", "button_text": "Combining Them"}, "audio_narration_url": null}}, {"id": "screen5_combining_structures", "type": "lesson_screen", "order": 5, "estimatedTimeSeconds": 75, "content": {"headline": "Combining Structures", "body_md": "Real algorithms combine these structures. You might have a sequence of steps, where one step is a selection, and inside that selection, there's an iteration.\n\nThis ability to nest and combine these three basic structures is what gives algorithms their power and flexibility to solve complex problems.", "visual": {"type": "unsplash_search", "value": "complex gears mechanism"}, "interactive_element": {"type": "button", "button_text": "Lesson Summary"}, "audio_narration_url": null}}, {"id": "screen6_lesson3_summary", "type": "lesson_screen", "order": 6, "estimatedTimeSeconds": 60, "content": {"headline": "Lesson Recap: Basic Structures", "body_md": "The three fundamental building blocks of algorithms are:\n\n*   **Sequence:** Executing steps in order.\n*   **Selection:** Making decisions (IF-THEN-ELSE).\n*   **Iteration:** Repeating steps (loops).\n\nUnderstanding these is crucial for designing any algorithm!", "visual": {"type": "giphy_search", "value": "lego building"}, "interactive_element": {"type": "button", "button_text": "Module Test Time!"}, "audio_narration_url": null}}]}], "moduleTest": {"id": "designing-algorithms-test", "title": "Module Test: Designing Algorithms", "description": "Test your understanding of algorithm design.", "type": "module_test_interactive", "estimatedTimeMinutes": 8, "contentBlocks": [{"id": "test_q1_algorithm_definition", "type": "question_screen", "order": 1, "estimatedTimeSeconds": 60, "content": {"headline": "Question 1: Algorithm Definition", "body_md": "Which characteristic ensures that each step of an algorithm is precisely defined and unambiguous?", "visual": {"type": "giphy_search", "value": "clear instructions"}, "interactive_element": {"type": "multiple_choice_text", "question_text": "Ensures clarity of each step?", "options": [{"text": "Finiteness", "is_correct": false, "feedback": "Finiteness means the algorithm must end."}, {"text": "Definiteness", "is_correct": true, "feedback": "Correct! Definiteness means each step is clear and unambiguous."}, {"text": "Effectiveness", "is_correct": false, "feedback": "Effectiveness means each step is performable."}, {"text": "Input", "is_correct": false, "feedback": "Input is what the algorithm processes."}]}, "audio_narration_url": null}}, {"id": "test_q2_pseudocode_purpose", "type": "question_screen", "order": 2, "estimatedTimeSeconds": 75, "content": {"headline": "Question 2: Pseudocode", "body_md": "What is the primary purpose of using pseudocode in algorithm design?", "visual": {"type": "unsplash_search", "value": "writing plan"}, "interactive_element": {"type": "multiple_choice_text", "question_text": "Primary purpose of pseudocode?", "options": [{"text": "To create a visually appealing diagram of the algorithm.", "is_correct": false, "feedback": "That's more characteristic of flowcharts."}, {"text": "To write code that can be directly executed by a computer.", "is_correct": false, "feedback": "Pseudocode is not directly executable; it's a design tool."}, {"text": "To describe the algorithm's logic in a human-readable, language-independent way.", "is_correct": true, "feedback": "Correct! It helps plan the logic before coding."}]}, "audio_narration_url": null}}, {"id": "test_q3_flowchart_symbol", "type": "question_screen", "order": 3, "estimatedTimeSeconds": 75, "content": {"headline": "Question 3: Flowchart Symbols", "body_md": "In a standard flowchart, what does a diamond shape typically represent?", "visual": {"type": "giphy_search", "value": "diamond shape"}, "interactive_element": {"type": "multiple_choice_text", "question_text": "Diamond shape in a flowchart?", "options": [{"text": "A process or instruction", "is_correct": false, "feedback": "Processes are usually represented by rectangles."}, {"text": "Input or output operation", "is_correct": false, "feedback": "Input/Output is often shown with parallelograms."}, {"text": "A decision point (e.g., IF condition)", "is_correct": true, "feedback": "Correct! Diamonds are used for decisions."}]}, "audio_narration_url": null}}, {"id": "test_q4_control_structures", "type": "question_screen", "order": 4, "estimatedTimeSeconds": 60, "content": {"headline": "Question 4: Control Structures", "body_md": "Which basic algorithmic structure is used to repeat a block of instructions multiple times?", "visual": {"type": "unsplash_search", "value": "loop arrow"}, "interactive_element": {"type": "multiple_choice_text", "question_text": "Structure for repeating instructions?", "options": [{"text": "Sequence", "is_correct": false, "feedback": "Sequence executes steps one after another."}, {"text": "Selection", "is_correct": false, "feedback": "Selection makes choices between paths."}, {"text": "Iteration", "is_correct": true, "feedback": "Correct! Iteration (looping) is used for repetition."}]}, "audio_narration_url": null}}]}}