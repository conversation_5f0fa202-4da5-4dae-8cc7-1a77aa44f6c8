# This is the CMakeCache file.
# For build in directory: d:/AResonance/rn/android/app/.cxx/Debug/11o49124/arm64-v8a
# It was generated by CMake: D:/AndroidSDK/cmake/3.22.1/bin/cmake.exe
# You can edit this file to change values found and used by cmake.
# If you do not want to change any of the values, simply exit the editor.
# If you do want to change a value, simply edit, save, and exit the editor.
# The syntax for the file is as follows:
# KEY:TYPE=VALUE
# KEY is the name of a variable in the cache.
# TYPE is a hint to GUIs for the type of VALUE, DO NOT EDIT TYPE!.
# VALUE is the current value for the KEY.

########################
# EXTERNAL cache entries
########################

//No help, variable specified on the command line.
ANDROID_ABI:UNINITIALIZED=arm64-v8a

//No help, variable specified on the command line.
ANDROID_NDK:UNINITIALIZED=D:\AndroidSDK\ndk\27.0.12077973

//No help, variable specified on the command line.
ANDROID_PLATFORM:UNINITIALIZED=android-22

//No help, variable specified on the command line.
CMAKE_ANDROID_ARCH_ABI:UNINITIALIZED=arm64-v8a

//No help, variable specified on the command line.
CMAKE_ANDROID_NDK:UNINITIALIZED=D:\AndroidSDK\ndk\27.0.12077973

//No help, variable specified on the command line.
CMAKE_BUILD_TYPE:UNINITIALIZED=Debug

//No help, variable specified on the command line.
CMAKE_EXPORT_COMPILE_COMMANDS:UNINITIALIZED=ON

//No help, variable specified on the command line.
CMAKE_LIBRARY_OUTPUT_DIRECTORY:UNINITIALIZED=D:\AResonance\rn\build\app\intermediates\cxx\Debug\11o49124\obj\arm64-v8a

//No help, variable specified on the command line.
CMAKE_MAKE_PROGRAM:UNINITIALIZED=D:\AndroidSDK\cmake\3.22.1\bin\ninja.exe

//Value Computed by CMake
CMAKE_PROJECT_DESCRIPTION:STATIC=

//Value Computed by CMake
CMAKE_PROJECT_HOMEPAGE_URL:STATIC=

//Value Computed by CMake
CMAKE_PROJECT_NAME:STATIC=Project

//No help, variable specified on the command line.
CMAKE_RUNTIME_OUTPUT_DIRECTORY:UNINITIALIZED=D:\AResonance\rn\build\app\intermediates\cxx\Debug\11o49124\obj\arm64-v8a

//No help, variable specified on the command line.
CMAKE_SYSTEM_NAME:UNINITIALIZED=Android

//No help, variable specified on the command line.
CMAKE_SYSTEM_VERSION:UNINITIALIZED=22

//No help, variable specified on the command line.
CMAKE_TOOLCHAIN_FILE:UNINITIALIZED=D:\AndroidSDK\ndk\27.0.12077973\build\cmake\android.toolchain.cmake

//Value Computed by CMake
Project_BINARY_DIR:STATIC=D:/AResonance/rn/android/app/.cxx/Debug/11o49124/arm64-v8a

//Value Computed by CMake
Project_IS_TOP_LEVEL:STATIC=ON

//Value Computed by CMake
Project_SOURCE_DIR:STATIC=C:/flutter/packages/flutter_tools/gradle/src/main/groovy


########################
# INTERNAL cache entries
########################

//This is the directory where this CMakeCache.txt was created
CMAKE_CACHEFILE_DIR:INTERNAL=d:/AResonance/rn/android/app/.cxx/Debug/11o49124/arm64-v8a
//Major version of cmake used to create the current loaded cache
CMAKE_CACHE_MAJOR_VERSION:INTERNAL=3
//Minor version of cmake used to create the current loaded cache
CMAKE_CACHE_MINOR_VERSION:INTERNAL=22
//Patch version of cmake used to create the current loaded cache
CMAKE_CACHE_PATCH_VERSION:INTERNAL=1
//Path to CMake executable.
CMAKE_COMMAND:INTERNAL=D:/AndroidSDK/cmake/3.22.1/bin/cmake.exe
//Path to cpack program executable.
CMAKE_CPACK_COMMAND:INTERNAL=D:/AndroidSDK/cmake/3.22.1/bin/cpack.exe
//Path to ctest program executable.
CMAKE_CTEST_COMMAND:INTERNAL=D:/AndroidSDK/cmake/3.22.1/bin/ctest.exe
//Whether to issue deprecation errors for macros and functions.
CMAKE_ERROR_DEPRECATED:INTERNAL=FALSE
//Name of external makefile project generator.
CMAKE_EXTRA_GENERATOR:INTERNAL=
//Name of generator.
CMAKE_GENERATOR:INTERNAL=Ninja
//Generator instance identifier.
CMAKE_GENERATOR_INSTANCE:INTERNAL=
//Name of generator platform.
CMAKE_GENERATOR_PLATFORM:INTERNAL=
//Name of generator toolset.
CMAKE_GENERATOR_TOOLSET:INTERNAL=
//Source directory with the top level CMakeLists.txt file for this
// project
CMAKE_HOME_DIRECTORY:INTERNAL=C:/flutter/packages/flutter_tools/gradle/src/main/groovy
//number of local generators
CMAKE_NUMBER_OF_MAKEFILES:INTERNAL=1
//Platform information initialized
CMAKE_PLATFORM_INFO_INITIALIZED:INTERNAL=1
//Path to CMake installation.
CMAKE_ROOT:INTERNAL=D:/AndroidSDK/cmake/3.22.1/share/cmake-3.22
//Suppress errors that are meant for the author of the CMakeLists.txt
// files.
CMAKE_SUPPRESS_DEVELOPER_ERRORS:INTERNAL=TRUE
//Suppress Warnings that are meant for the author of the CMakeLists.txt
// files.
CMAKE_SUPPRESS_DEVELOPER_WARNINGS:INTERNAL=TRUE
//Whether to issue warnings for deprecated functionality.
CMAKE_WARN_DEPRECATED:INTERNAL=FALSE

