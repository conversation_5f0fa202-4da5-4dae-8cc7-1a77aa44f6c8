import 'package:flutter/material.dart';
import 'dart:math' as math;

/// A widget that visualizes ratios and proportions
class InteractiveRatioVisualizerWidget extends StatefulWidget {
  /// The data for this widget
  final Map<String, dynamic> data;

  /// Optional callback when the widget state changes
  final Function(bool isCompleted)? onStateChanged;

  const InteractiveRatioVisualizerWidget({
    Key? key,
    required this.data,
    this.onStateChanged,
  }) : super(key: key);

  /// Factory constructor to create the widget from JSON data
  factory InteractiveRatioVisualizerWidget.fromData(Map<String, dynamic> data) {
    return InteractiveRatioVisualizerWidget(
      data: data,
    );
  }

  @override
  State<InteractiveRatioVisualizerWidget> createState() => _InteractiveRatioVisualizerWidgetState();
}

class _InteractiveRatioVisualizerWidgetState extends State<InteractiveRatioVisualizerWidget> with SingleTickerProviderStateMixin {
  // Ratio parameters
  late int _numerator;
  late int _denominator;
  
  // Visualization type
  late String _visualizationType;
  
  // Animation controller
  late AnimationController _animationController;
  late Animation<double> _animation;
  
  // Colors
  late Color _primaryColor;
  late Color _secondaryColor;
  late Color _backgroundColor;
  late Color _textColor;
  
  // Display options
  late bool _showDecimal;
  late bool _showPercentage;
  late bool _showFraction;
  
  // Animation state
  bool _isAnimating = false;
  
  // Available visualization types
  final List<String> _visualizationTypes = [
    'Blocks',
    'Pie Chart',
    'Bar Chart',
    'Grid',
  ];

  @override
  void initState() {
    super.initState();
    
    // Initialize ratio parameters
    _numerator = widget.data['numerator'] ?? 3;
    _denominator = widget.data['denominator'] ?? 4;
    
    // Initialize visualization type
    _visualizationType = widget.data['visualization_type'] ?? 'Blocks';
    
    // Initialize colors
    _primaryColor = _parseColor(widget.data['primary_color']) ?? Colors.blue;
    _secondaryColor = _parseColor(widget.data['secondary_color']) ?? Colors.grey;
    _backgroundColor = _parseColor(widget.data['background_color']) ?? Colors.white;
    _textColor = _parseColor(widget.data['text_color']) ?? Colors.black87;
    
    // Initialize display options
    _showDecimal = widget.data['show_decimal'] ?? true;
    _showPercentage = widget.data['show_percentage'] ?? true;
    _showFraction = widget.data['show_fraction'] ?? true;
    
    // Initialize animation controller
    _animationController = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 1500),
    );
    
    _animation = CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeInOut,
    );
    
    // Add listener to animation controller
    _animationController.addStatusListener((status) {
      if (status == AnimationStatus.completed) {
        setState(() {
          _isAnimating = false;
        });
      }
    });
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }
  
  // Parse color from hex string
  Color? _parseColor(String? hexString) {
    if (hexString == null) return null;
    
    hexString = hexString.replaceAll('#', '');
    if (hexString.length == 6) {
      hexString = 'FF$hexString';
    }
    
    return Color(int.parse(hexString, radix: 16));
  }
  
  // Start animation
  void _startAnimation() {
    if (_isAnimating) return;
    
    setState(() {
      _isAnimating = true;
    });
    
    _animationController.reset();
    _animationController.forward();
  }
  
  // Change visualization type
  void _changeVisualizationType(String type) {
    setState(() {
      _visualizationType = type;
    });
    
    _startAnimation();
  }
  
  // Increase numerator
  void _increaseNumerator() {
    if (_numerator < _denominator) {
      setState(() {
        _numerator++;
      });
      
      _startAnimation();
    }
  }
  
  // Decrease numerator
  void _decreaseNumerator() {
    if (_numerator > 0) {
      setState(() {
        _numerator--;
      });
      
      _startAnimation();
    }
  }
  
  // Increase denominator
  void _increaseDenominator() {
    if (_denominator < 20) {
      setState(() {
        _denominator++;
      });
      
      _startAnimation();
    }
  }
  
  // Decrease denominator
  void _decreaseDenominator() {
    if (_denominator > _numerator && _denominator > 1) {
      setState(() {
        _denominator--;
      });
      
      _startAnimation();
    }
  }
  
  // Calculate greatest common divisor (GCD)
  int _gcd(int a, int b) {
    while (b != 0) {
      final t = b;
      b = a % b;
      a = t;
    }
    return a;
  }
  
  // Simplify fraction
  String _simplifyFraction() {
    if (_numerator == 0) return '0';
    
    final gcd = _gcd(_numerator, _denominator);
    final simplifiedNumerator = _numerator ~/ gcd;
    final simplifiedDenominator = _denominator ~/ gcd;
    
    if (simplifiedDenominator == 1) {
      return simplifiedNumerator.toString();
    }
    
    return '$simplifiedNumerator/$simplifiedDenominator';
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: _backgroundColor,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withOpacity(0.2),
            spreadRadius: 1,
            blurRadius: 3,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Title
          Text(
            widget.data['title'] ?? 'Ratio Visualizer',
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
              color: _textColor,
            ),
          ),
          
          const SizedBox(height: 8),
          
          // Description
          if (widget.data['description'] != null)
            Text(
              widget.data['description'],
              style: TextStyle(
                fontSize: 14,
                color: _textColor.withOpacity(0.8),
              ),
            ),
          
          const SizedBox(height: 16),
          
          // Ratio controls
          Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              // Numerator controls
              Column(
                children: [
                  IconButton(
                    onPressed: _increaseNumerator,
                    icon: const Icon(Icons.arrow_upward),
                    color: _primaryColor,
                  ),
                  Container(
                    width: 60,
                    height: 60,
                    decoration: BoxDecoration(
                      color: _primaryColor.withOpacity(0.1),
                      borderRadius: BorderRadius.circular(8),
                      border: Border.all(color: _primaryColor),
                    ),
                    child: Center(
                      child: Text(
                        _numerator.toString(),
                        style: TextStyle(
                          fontSize: 24,
                          fontWeight: FontWeight.bold,
                          color: _primaryColor,
                        ),
                      ),
                    ),
                  ),
                  IconButton(
                    onPressed: _decreaseNumerator,
                    icon: const Icon(Icons.arrow_downward),
                    color: _primaryColor,
                  ),
                ],
              ),
              
              // Fraction line
              Container(
                width: 40,
                height: 4,
                color: _textColor,
                margin: const EdgeInsets.symmetric(horizontal: 16),
              ),
              
              // Denominator controls
              Column(
                children: [
                  IconButton(
                    onPressed: _increaseDenominator,
                    icon: const Icon(Icons.arrow_upward),
                    color: _primaryColor,
                  ),
                  Container(
                    width: 60,
                    height: 60,
                    decoration: BoxDecoration(
                      color: _primaryColor.withOpacity(0.1),
                      borderRadius: BorderRadius.circular(8),
                      border: Border.all(color: _primaryColor),
                    ),
                    child: Center(
                      child: Text(
                        _denominator.toString(),
                        style: TextStyle(
                          fontSize: 24,
                          fontWeight: FontWeight.bold,
                          color: _primaryColor,
                        ),
                      ),
                    ),
                  ),
                  IconButton(
                    onPressed: _decreaseDenominator,
                    icon: const Icon(Icons.arrow_downward),
                    color: _primaryColor,
                  ),
                ],
              ),
              
              // Equals sign
              Container(
                margin: const EdgeInsets.symmetric(horizontal: 16),
                child: Text(
                  '=',
                  style: TextStyle(
                    fontSize: 24,
                    fontWeight: FontWeight.bold,
                    color: _textColor,
                  ),
                ),
              ),
              
              // Ratio representations
              Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  if (_showFraction)
                    Text(
                      _simplifyFraction(),
                      style: TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                        color: _textColor,
                      ),
                    ),
                  if (_showDecimal)
                    Text(
                      'Decimal: ${(_numerator / _denominator).toStringAsFixed(2)}',
                      style: TextStyle(
                        fontSize: 14,
                        color: _textColor,
                      ),
                    ),
                  if (_showPercentage)
                    Text(
                      'Percentage: ${((_numerator / _denominator) * 100).toStringAsFixed(1)}%',
                      style: TextStyle(
                        fontSize: 14,
                        color: _textColor,
                      ),
                    ),
                ],
              ),
            ],
          ),
          
          const SizedBox(height: 16),
          
          // Visualization type selector
          Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: _visualizationTypes.map((type) {
              return Padding(
                padding: const EdgeInsets.symmetric(horizontal: 4),
                child: ChoiceChip(
                  label: Text(type),
                  selected: _visualizationType == type,
                  onSelected: (_) => _changeVisualizationType(type),
                ),
              );
            }).toList(),
          ),
          
          const SizedBox(height: 16),
          
          // Ratio visualization
          Container(
            height: 200,
            decoration: BoxDecoration(
              color: Colors.grey.withOpacity(0.1),
              borderRadius: BorderRadius.circular(8),
            ),
            child: AnimatedBuilder(
              animation: _animation,
              builder: (context, child) {
                return CustomPaint(
                  painter: RatioVisualizationPainter(
                    numerator: _numerator,
                    denominator: _denominator,
                    visualizationType: _visualizationType,
                    primaryColor: _primaryColor,
                    secondaryColor: _secondaryColor,
                    animationValue: _isAnimating ? _animation.value : 1.0,
                  ),
                  child: Container(),
                );
              },
            ),
          ),
          
          const SizedBox(height: 16),
          
          // Animation button
          Center(
            child: ElevatedButton.icon(
              onPressed: _isAnimating ? null : _startAnimation,
              icon: const Icon(Icons.play_arrow),
              label: const Text('Animate Ratio'),
              style: ElevatedButton.styleFrom(
                backgroundColor: _primaryColor,
                padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
              ),
            ),
          ),
          
          // Widget name tag
          if (widget.data['showNameTag'] ?? true)
            Padding(
              padding: const EdgeInsets.only(top: 8),
              child: Text(
                'InteractiveRatioVisualizer',
                style: TextStyle(
                  color: Colors.grey[600],
                  fontSize: 12,
                  fontStyle: FontStyle.italic,
                ),
              ),
            ),
        ],
      ),
    );
  }
}

/// Custom painter for ratio visualization
class RatioVisualizationPainter extends CustomPainter {
  final int numerator;
  final int denominator;
  final String visualizationType;
  final Color primaryColor;
  final Color secondaryColor;
  final double animationValue;
  
  RatioVisualizationPainter({
    required this.numerator,
    required this.denominator,
    required this.visualizationType,
    required this.primaryColor,
    required this.secondaryColor,
    required this.animationValue,
  });
  
  @override
  void paint(Canvas canvas, Size size) {
    switch (visualizationType) {
      case 'Blocks':
        _drawBlocks(canvas, size);
        break;
      case 'Pie Chart':
        _drawPieChart(canvas, size);
        break;
      case 'Bar Chart':
        _drawBarChart(canvas, size);
        break;
      case 'Grid':
        _drawGrid(canvas, size);
        break;
      default:
        _drawBlocks(canvas, size);
    }
  }
  
  // Draw blocks visualization
  void _drawBlocks(Canvas canvas, Size size) {
    final blockWidth = size.width / denominator;
    final blockHeight = size.height * 0.6;
    final startY = (size.height - blockHeight) / 2;
    
    // Draw all blocks
    for (int i = 0; i < denominator; i++) {
      final blockRect = Rect.fromLTWH(
        i * blockWidth,
        startY,
        blockWidth,
        blockHeight,
      );
      
      final paint = Paint()
        ..color = i < (numerator * animationValue)
            ? primaryColor
            : secondaryColor
        ..style = PaintingStyle.fill;
      
      canvas.drawRect(blockRect, paint);
      
      // Draw border
      final borderPaint = Paint()
        ..color = Colors.white
        ..style = PaintingStyle.stroke
        ..strokeWidth = 2;
      
      canvas.drawRect(blockRect, borderPaint);
    }
    
    // Draw ratio text
    final textSpan = TextSpan(
      text: '$numerator/$denominator',
      style: TextStyle(
        color: Colors.black87,
        fontSize: 16,
        fontWeight: FontWeight.bold,
      ),
    );
    
    final textPainter = TextPainter(
      text: textSpan,
      textDirection: TextDirection.ltr,
    );
    
    textPainter.layout();
    textPainter.paint(
      canvas,
      Offset(
        (size.width - textPainter.width) / 2,
        startY + blockHeight + 10,
      ),
    );
  }
  
  // Draw pie chart visualization
  void _drawPieChart(Canvas canvas, Size size) {
    final center = Offset(size.width / 2, size.height / 2);
    final radius = math.min(size.width, size.height) * 0.4;
    
    // Draw background circle
    final backgroundPaint = Paint()
      ..color = secondaryColor
      ..style = PaintingStyle.fill;
    
    canvas.drawCircle(center, radius, backgroundPaint);
    
    // Draw ratio segment
    final ratioPaint = Paint()
      ..color = primaryColor
      ..style = PaintingStyle.fill;
    
    final sweepAngle = 2 * math.pi * (numerator / denominator) * animationValue;
    
    canvas.drawArc(
      Rect.fromCircle(center: center, radius: radius),
      -math.pi / 2, // Start from top
      sweepAngle,
      true,
      ratioPaint,
    );
    
    // Draw center circle
    final centerPaint = Paint()
      ..color = Colors.white
      ..style = PaintingStyle.fill;
    
    canvas.drawCircle(center, radius * 0.3, centerPaint);
    
    // Draw ratio text
    final textSpan = TextSpan(
      text: '$numerator/$denominator',
      style: TextStyle(
        color: Colors.black87,
        fontSize: 16,
        fontWeight: FontWeight.bold,
      ),
    );
    
    final textPainter = TextPainter(
      text: textSpan,
      textDirection: TextDirection.ltr,
    );
    
    textPainter.layout();
    textPainter.paint(
      canvas,
      Offset(
        center.dx - textPainter.width / 2,
        center.dy - textPainter.height / 2,
      ),
    );
  }
  
  // Draw bar chart visualization
  void _drawBarChart(Canvas canvas, Size size) {
    final barWidth = size.width * 0.4;
    final barHeight = size.height * 0.7;
    final startY = (size.height - barHeight) / 2;
    final startX = (size.width - barWidth) / 2;
    
    // Draw background bar
    final backgroundRect = Rect.fromLTWH(
      startX,
      startY,
      barWidth,
      barHeight,
    );
    
    final backgroundPaint = Paint()
      ..color = secondaryColor
      ..style = PaintingStyle.fill;
    
    canvas.drawRect(backgroundRect, backgroundPaint);
    
    // Draw ratio bar
    final ratioHeight = barHeight * (numerator / denominator) * animationValue;
    final ratioRect = Rect.fromLTWH(
      startX,
      startY + barHeight - ratioHeight,
      barWidth,
      ratioHeight,
    );
    
    final ratioPaint = Paint()
      ..color = primaryColor
      ..style = PaintingStyle.fill;
    
    canvas.drawRect(ratioRect, ratioPaint);
    
    // Draw border
    final borderPaint = Paint()
      ..color = Colors.white
      ..style = PaintingStyle.stroke
      ..strokeWidth = 2;
    
    canvas.drawRect(backgroundRect, borderPaint);
    
    // Draw ratio text
    final textSpan = TextSpan(
      text: '$numerator/$denominator',
      style: TextStyle(
        color: Colors.black87,
        fontSize: 16,
        fontWeight: FontWeight.bold,
      ),
    );
    
    final textPainter = TextPainter(
      text: textSpan,
      textDirection: TextDirection.ltr,
    );
    
    textPainter.layout();
    textPainter.paint(
      canvas,
      Offset(
        startX + (barWidth - textPainter.width) / 2,
        startY + barHeight + 10,
      ),
    );
  }
  
  // Draw grid visualization
  void _drawGrid(Canvas canvas, Size size) {
    // Calculate grid dimensions
    int rows = 1;
    int cols = denominator;
    
    // Try to make the grid more square-like
    if (denominator > 12) {
      rows = 2;
      cols = (denominator + 1) ~/ 2;
    }
    
    if (denominator > 24) {
      rows = 3;
      cols = (denominator + 2) ~/ 3;
    }
    
    if (denominator > 36) {
      rows = 4;
      cols = (denominator + 3) ~/ 4;
    }
    
    final cellWidth = size.width / cols;
    final cellHeight = size.height * 0.8 / rows;
    final startY = (size.height - cellHeight * rows) / 2;
    
    // Draw all cells
    int cellCount = 0;
    for (int row = 0; row < rows; row++) {
      for (int col = 0; col < cols; col++) {
        if (cellCount < denominator) {
          final cellRect = Rect.fromLTWH(
            col * cellWidth,
            startY + row * cellHeight,
            cellWidth,
            cellHeight,
          );
          
          final paint = Paint()
            ..color = cellCount < (numerator * animationValue)
                ? primaryColor
                : secondaryColor
            ..style = PaintingStyle.fill;
          
          canvas.drawRect(cellRect, paint);
          
          // Draw border
          final borderPaint = Paint()
            ..color = Colors.white
            ..style = PaintingStyle.stroke
            ..strokeWidth = 1;
          
          canvas.drawRect(cellRect, borderPaint);
          
          cellCount++;
        }
      }
    }
    
    // Draw ratio text
    final textSpan = TextSpan(
      text: '$numerator/$denominator',
      style: TextStyle(
        color: Colors.black87,
        fontSize: 16,
        fontWeight: FontWeight.bold,
      ),
    );
    
    final textPainter = TextPainter(
      text: textSpan,
      textDirection: TextDirection.ltr,
    );
    
    textPainter.layout();
    textPainter.paint(
      canvas,
      Offset(
        (size.width - textPainter.width) / 2,
        startY + rows * cellHeight + 10,
      ),
    );
  }
  
  @override
  bool shouldRepaint(covariant RatioVisualizationPainter oldDelegate) {
    return oldDelegate.numerator != numerator ||
           oldDelegate.denominator != denominator ||
           oldDelegate.visualizationType != visualizationType ||
           oldDelegate.animationValue != animationValue;
  }
}
