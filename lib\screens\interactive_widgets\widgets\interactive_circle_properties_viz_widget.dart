import 'package:flutter/material.dart';
import 'dart:math' as math;

/// A widget that visualizes circle properties (radius, diameter, circumference)
class InteractiveCirclePropertiesVizWidget extends StatefulWidget {
  final Map<String, dynamic> data;
  final Function(bool)? onStateChanged;

  const InteractiveCirclePropertiesVizWidget({
    Key? key,
    required this.data,
    this.onStateChanged,
  }) : super(key: key);

  /// Creates the widget from JSON data
  static InteractiveCirclePropertiesVizWidget fromData(Map<String, dynamic> data) {
    return InteractiveCirclePropertiesVizWidget(
      data: data,
    );
  }

  @override
  State<InteractiveCirclePropertiesVizWidget> createState() => _InteractiveCirclePropertiesVizWidgetState();
}

class _InteractiveCirclePropertiesVizWidgetState extends State<InteractiveCirclePropertiesVizWidget> {
  // Circle properties
  double _radius = 50.0;
  Offset _center = const Offset(150, 150);
  
  // Colors
  late Color _circleColor;
  late Color _radiusColor;
  late Color _diameterColor;
  late Color _circumferenceColor;
  late Color _textColor;
  
  // Display options
  bool _showRadius = true;
  bool _showDiameter = true;
  bool _showCircumference = true;
  bool _showLabels = true;
  bool _showValues = true;
  bool _showPi = true;
  
  // Interaction state
  bool _isDraggingRadius = false;
  bool _isDraggingCenter = false;
  
  // Min/max radius
  double _minRadius = 20.0;
  double _maxRadius = 120.0;

  @override
  void initState() {
    super.initState();
    _initializeWidget();
  }

  void _initializeWidget() {
    // Set initial radius
    _radius = widget.data['initialRadius']?.toDouble() ?? 50.0;
    
    // Set min/max radius
    _minRadius = widget.data['minRadius']?.toDouble() ?? 20.0;
    _maxRadius = widget.data['maxRadius']?.toDouble() ?? 120.0;
    
    // Set colors
    _circleColor = _parseColor(widget.data['circleColor']) ?? Colors.blue.withOpacity(0.2);
    _radiusColor = _parseColor(widget.data['radiusColor']) ?? Colors.red;
    _diameterColor = _parseColor(widget.data['diameterColor']) ?? Colors.green;
    _circumferenceColor = _parseColor(widget.data['circumferenceColor']) ?? Colors.purple;
    _textColor = _parseColor(widget.data['textColor']) ?? Colors.black87;
    
    // Set display options
    _showRadius = widget.data['showRadius'] ?? true;
    _showDiameter = widget.data['showDiameter'] ?? true;
    _showCircumference = widget.data['showCircumference'] ?? true;
    _showLabels = widget.data['showLabels'] ?? true;
    _showValues = widget.data['showValues'] ?? true;
    _showPi = widget.data['showPi'] ?? true;
  }

  // Parse color from hex string
  Color? _parseColor(String? hexString) {
    if (hexString == null) return null;
    
    hexString = hexString.replaceAll('#', '');
    if (hexString.length == 6) {
      hexString = 'FF$hexString';
    }
    
    return Color(int.parse(hexString, radix: 16));
  }

  // Calculate circle properties
  double get _diameter => _radius * 2;
  double get _circumference => 2 * math.pi * _radius;
  double get _area => math.pi * _radius * _radius;
  double get _piRatio => _circumference / _diameter; // Should be approximately pi

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withOpacity(0.2),
            spreadRadius: 1,
            blurRadius: 3,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Title
          Text(
            widget.data['title'] ?? 'Circle Properties Visualizer',
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
              color: _textColor,
            ),
          ),
          
          const SizedBox(height: 8),
          
          // Description
          if (widget.data['description'] != null)
            Text(
              widget.data['description'],
              style: TextStyle(
                fontSize: 14,
                color: _textColor.withOpacity(0.8),
              ),
            ),
          
          const SizedBox(height: 16),
          
          // Controls
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceEvenly,
            children: [
              ElevatedButton.icon(
                onPressed: () {
                  setState(() {
                    _showRadius = !_showRadius;
                  });
                },
                icon: Icon(_showRadius ? Icons.visibility : Icons.visibility_off),
                label: const Text('Radius'),
                style: ElevatedButton.styleFrom(
                  backgroundColor: _showRadius ? Colors.red : Colors.grey,
                ),
              ),
              ElevatedButton.icon(
                onPressed: () {
                  setState(() {
                    _showDiameter = !_showDiameter;
                  });
                },
                icon: Icon(_showDiameter ? Icons.visibility : Icons.visibility_off),
                label: const Text('Diameter'),
                style: ElevatedButton.styleFrom(
                  backgroundColor: _showDiameter ? Colors.green : Colors.grey,
                ),
              ),
              ElevatedButton.icon(
                onPressed: () {
                  setState(() {
                    _showCircumference = !_showCircumference;
                  });
                },
                icon: Icon(_showCircumference ? Icons.visibility : Icons.visibility_off),
                label: const Text('Circumference'),
                style: ElevatedButton.styleFrom(
                  backgroundColor: _showCircumference ? Colors.purple : Colors.grey,
                ),
              ),
            ],
          ),
          
          const SizedBox(height: 8),
          
          // Radius slider
          Row(
            children: [
              const Text('Radius: '),
              Expanded(
                child: Slider(
                  value: _radius,
                  min: _minRadius,
                  max: _maxRadius,
                  divisions: 100,
                  label: _radius.toStringAsFixed(1),
                  activeColor: _radiusColor,
                  onChanged: (value) {
                    setState(() {
                      _radius = value;
                    });
                  },
                ),
              ),
              Text('${_radius.toStringAsFixed(1)} units'),
            ],
          ),
          
          const SizedBox(height: 16),
          
          // Circle visualization
          SizedBox(
            height: 300,
            child: GestureDetector(
              onPanDown: (details) {
                final localPosition = details.localPosition;
                final distanceFromCenter = (localPosition - _center).distance;
                
                // Check if dragging the radius handle
                if ((_radius - distanceFromCenter).abs() < 20) {
                  setState(() {
                    _isDraggingRadius = true;
                  });
                }
                // Check if dragging the center
                else if (distanceFromCenter < 20) {
                  setState(() {
                    _isDraggingCenter = true;
                  });
                }
              },
              onPanUpdate: (details) {
                if (_isDraggingRadius) {
                  final localPosition = details.localPosition;
                  final newRadius = (localPosition - _center).distance;
                  
                  setState(() {
                    _radius = newRadius.clamp(_minRadius, _maxRadius);
                  });
                } else if (_isDraggingCenter) {
                  setState(() {
                    _center += details.delta;
                  });
                }
              },
              onPanEnd: (details) {
                setState(() {
                  _isDraggingRadius = false;
                  _isDraggingCenter = false;
                });
              },
              child: CustomPaint(
                painter: CirclePropertiesPainter(
                  center: _center,
                  radius: _radius,
                  circleColor: _circleColor,
                  radiusColor: _radiusColor,
                  diameterColor: _diameterColor,
                  circumferenceColor: _circumferenceColor,
                  textColor: _textColor,
                  showRadius: _showRadius,
                  showDiameter: _showDiameter,
                  showCircumference: _showCircumference,
                  showLabels: _showLabels,
                ),
                child: Container(),
              ),
            ),
          ),
          
          const SizedBox(height: 16),
          
          // Properties display
          Container(
            padding: const EdgeInsets.all(12),
            decoration: BoxDecoration(
              color: Colors.grey.withOpacity(0.1),
              borderRadius: BorderRadius.circular(8),
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'Circle Properties:',
                  style: TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                    color: _textColor,
                  ),
                ),
                const SizedBox(height: 8),
                Row(
                  children: [
                    Container(
                      width: 12,
                      height: 12,
                      color: _radiusColor,
                    ),
                    const SizedBox(width: 8),
                    Text(
                      'Radius (r): ${_radius.toStringAsFixed(1)} units',
                      style: TextStyle(
                        color: _textColor,
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 4),
                Row(
                  children: [
                    Container(
                      width: 12,
                      height: 12,
                      color: _diameterColor,
                    ),
                    const SizedBox(width: 8),
                    Text(
                      'Diameter (d): ${_diameter.toStringAsFixed(1)} units',
                      style: TextStyle(
                        color: _textColor,
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 4),
                Row(
                  children: [
                    Container(
                      width: 12,
                      height: 12,
                      color: _circumferenceColor,
                    ),
                    const SizedBox(width: 8),
                    Text(
                      'Circumference (C): ${_circumference.toStringAsFixed(1)} units',
                      style: TextStyle(
                        color: _textColor,
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 4),
                Text(
                  'Area (A): ${_area.toStringAsFixed(1)} square units',
                  style: TextStyle(
                    color: _textColor,
                  ),
                ),
                if (_showPi)
                  Padding(
                    padding: const EdgeInsets.only(top: 8),
                    child: Text(
                      'C/d = π ≈ ${_piRatio.toStringAsFixed(6)}',
                      style: TextStyle(
                        fontWeight: FontWeight.bold,
                        color: _textColor,
                      ),
                    ),
                  ),
              ],
            ),
          ),
          
          // Widget name tag
          if (widget.data['showNameTag'] ?? true)
            Padding(
              padding: const EdgeInsets.only(top: 8),
              child: Text(
                'InteractiveCirclePropertiesVizWidget',
                style: TextStyle(
                  color: Colors.grey[600],
                  fontSize: 12,
                  fontStyle: FontStyle.italic,
                ),
              ),
            ),
        ],
      ),
    );
  }
}

/// Custom painter for the circle properties visualization
class CirclePropertiesPainter extends CustomPainter {
  final Offset center;
  final double radius;
  final Color circleColor;
  final Color radiusColor;
  final Color diameterColor;
  final Color circumferenceColor;
  final Color textColor;
  final bool showRadius;
  final bool showDiameter;
  final bool showCircumference;
  final bool showLabels;
  
  CirclePropertiesPainter({
    required this.center,
    required this.radius,
    required this.circleColor,
    required this.radiusColor,
    required this.diameterColor,
    required this.circumferenceColor,
    required this.textColor,
    required this.showRadius,
    required this.showDiameter,
    required this.showCircumference,
    required this.showLabels,
  });
  
  @override
  void paint(Canvas canvas, Size size) {
    // Draw the circle
    final circlePaint = Paint()
      ..color = circleColor
      ..style = PaintingStyle.fill;
    
    final circleBorderPaint = Paint()
      ..color = circumferenceColor
      ..style = PaintingStyle.stroke
      ..strokeWidth = 2;
    
    canvas.drawCircle(center, radius, circlePaint);
    
    // Draw circumference
    if (showCircumference) {
      canvas.drawCircle(center, radius, circleBorderPaint);
      
      if (showLabels) {
        final textSpan = TextSpan(
          text: 'C = 2πr',
          style: TextStyle(
            color: circumferenceColor,
            fontSize: 14,
            fontWeight: FontWeight.bold,
          ),
        );
        
        final textPainter = TextPainter(
          text: textSpan,
          textDirection: TextDirection.ltr,
        );
        
        textPainter.layout();
        textPainter.paint(
          canvas,
          Offset(center.dx - textPainter.width / 2, center.dy - radius - 20),
        );
      }
    }
    
    // Draw diameter
    if (showDiameter) {
      final diameterPaint = Paint()
        ..color = diameterColor
        ..style = PaintingStyle.stroke
        ..strokeWidth = 2;
      
      canvas.drawLine(
        Offset(center.dx - radius, center.dy),
        Offset(center.dx + radius, center.dy),
        diameterPaint,
      );
      
      if (showLabels) {
        final textSpan = TextSpan(
          text: 'd = 2r',
          style: TextStyle(
            color: diameterColor,
            fontSize: 14,
            fontWeight: FontWeight.bold,
          ),
        );
        
        final textPainter = TextPainter(
          text: textSpan,
          textDirection: TextDirection.ltr,
        );
        
        textPainter.layout();
        textPainter.paint(
          canvas,
          Offset(center.dx - textPainter.width / 2, center.dy + 10),
        );
      }
    }
    
    // Draw radius
    if (showRadius) {
      final radiusPaint = Paint()
        ..color = radiusColor
        ..style = PaintingStyle.stroke
        ..strokeWidth = 2;
      
      canvas.drawLine(
        center,
        Offset(center.dx + radius, center.dy),
        radiusPaint,
      );
      
      // Draw radius handle
      final handlePaint = Paint()
        ..color = radiusColor
        ..style = PaintingStyle.fill;
      
      canvas.drawCircle(
        Offset(center.dx + radius, center.dy),
        5,
        handlePaint,
      );
      
      if (showLabels) {
        final textSpan = TextSpan(
          text: 'r',
          style: TextStyle(
            color: radiusColor,
            fontSize: 14,
            fontWeight: FontWeight.bold,
          ),
        );
        
        final textPainter = TextPainter(
          text: textSpan,
          textDirection: TextDirection.ltr,
        );
        
        textPainter.layout();
        textPainter.paint(
          canvas,
          Offset(center.dx + radius / 2, center.dy - 20),
        );
      }
    }
    
    // Draw center point
    final centerPaint = Paint()
      ..color = textColor
      ..style = PaintingStyle.fill;
    
    canvas.drawCircle(center, 5, centerPaint);
    
    if (showLabels) {
      final textSpan = TextSpan(
        text: 'O',
        style: TextStyle(
          color: textColor,
          fontSize: 14,
          fontWeight: FontWeight.bold,
        ),
      );
      
      final textPainter = TextPainter(
        text: textSpan,
        textDirection: TextDirection.ltr,
      );
      
      textPainter.layout();
      textPainter.paint(
        canvas,
        center - Offset(textPainter.width / 2, textPainter.height + 10),
      );
    }
  }
  
  @override
  bool shouldRepaint(covariant CirclePropertiesPainter oldDelegate) {
    return oldDelegate.center != center ||
           oldDelegate.radius != radius ||
           oldDelegate.showRadius != showRadius ||
           oldDelegate.showDiameter != showDiameter ||
           oldDelegate.showCircumference != showCircumference;
  }
}
