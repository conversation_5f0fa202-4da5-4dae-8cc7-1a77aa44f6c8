{"id": "exploring-algebraic-relationships", "title": "Exploring Algebraic Relationships", "description": "Discover how equations can represent connections between different quantities.", "order": 5, "lessons": [{"id": "ear-l1-representing-relationships", "title": "Equations as Storytellers: Representing Relationships", "description": "Learn how to translate described relationships between quantities into algebraic equations, capturing the connection between variables.", "order": 1, "type": "interactive_lesson", "estimatedTimeMinutes": 8, "contentBlocks": [{"id": "ear-l1-s1", "type": "lesson_screen", "order": 1, "estimatedTimeSeconds": 60, "content": {"headline": "Equations Tell Stories", "body_md": "Algebra isn't just about solving for 'x'! A huge part of its power lies in **representing relationships** between different quantities. Equations are like mathematical sentences that tell the story of how these quantities are connected.", "visual": {"type": "giphy_search", "value": "storytelling book"}, "hook": "Prepare to see equations in a new light - as storytellers!", "interactive_element": {"type": "button", "text": "How do equations tell stories?", "action": "next_screen"}}}, {"id": "ear-l1-s2", "type": "lesson_screen", "order": 2, "estimatedTimeSeconds": 90, "content": {"headline": "Example: y = x + 3", "body_md": "If we say 'the value of y is always 3 more than the value of x', this describes a clear relationship. We capture this with: \n`y = x + 3`\n\nThis means:\n- If x = 1, then y = 1 + 3 = 4.\n- If x = 5, then y = 5 + 3 = 8.\n- If x = -2, then y = -2 + 3 = 1.\n\nThe variables 'x' and 'y' change, but their relationship (y is x plus 3) is constant.", "visual": {"type": "static_text", "value": "y = x + 3"}, "hook": "This simple equation describes a consistent pattern.", "interactive_element": {"type": "button", "text": "Let me try to write a story (equation)!", "action": "next_screen"}}}, {"id": "ear-l1-s3", "type": "lesson_screen", "order": 3, "estimatedTimeSeconds": 120, "content": {"headline": "Interactive: Bicycles and Wheels", "body_md": "Imagine counting wheels (w) at a bicycle shop. The total wheels depends on the number of bicycles (b). Specifically, wheels (w) is always **twice** the number of bicycles (b).", "visual": {"type": "giphy_search", "value": "bicycle wheels"}, "interactive_element": {"type": "interactive_relationship_to_equation", "scenario_title": "Bicycles and Wheels", "scenario_text_md": "Wheels (w) is twice the number of bicycles (b).", "prompt_equation": "Write an equation for 'w' in terms of 'b'.", "equation_placeholder": "e.g., w = 2b", "correct_equation_regex": "^\\s*w\\s*=\\s*2\\s*\\*?\\s*b\\s*$|^\\s*2\\s*\\*?\\s*b\\s*=\\s*w\\s*$", "feedback_correct": "Excellent! `w = 2b` is perfect.", "feedback_incorrect": "If b=1, w=? If b=3, w=? Your equation should work.", "action_button_text": "Any more examples?"}, "hook": "Your turn to be the equation author!"}}, {"id": "ear-l1-s4", "type": "lesson_screen", "order": 4, "estimatedTimeSeconds": 60, "content": {"headline": "More Examples", "body_md": "Another example: 'The perimeter (P) of a square is four times the length of one of its sides (s).' \nEquation: `P = 4s`.\n\nTranslating descriptions into algebraic equations is fundamental for modeling and problem-solving.", "visual": {"type": "static_text", "value": "P = 4s"}, "hook": "The more you practice, the easier it becomes to translate words to math!", "interactive_element": {"type": "button", "text": "How can we visualize these relationships?", "action": "next_lesson"}}}]}, {"id": "ear-l2-intro-graphing-linear-equations", "title": "Lines on a Plane: Graphing Linear Equations", "description": "Learn to visualize linear equations (like y = mx + c) as straight lines on a coordinate plane by plotting points.", "order": 2, "type": "interactive_lesson", "estimatedTimeMinutes": 9, "contentBlocks": [{"id": "ear-l2-s1", "type": "lesson_screen", "order": 1, "estimatedTimeSeconds": 70, "content": {"headline": "Graphing Linear Equations", "body_md": "Equations describing a consistent relationship between two variables often form straight lines when graphed. These are **linear equations** (e.g., `y = mx + c`).\n\nTo graph them, we use a **coordinate plane** (x-axis horizontal, y-axis vertical, meeting at origin (0,0)).", "visual": {"type": "local_asset", "value": "assets/images/algebra/coordinate_plane_axes.svg", "alt_text": "Coordinate plane."}, "hook": "Let's turn these equations into pictures!", "interactive_element": {"type": "button", "text": "How do we graph these lines?", "action": "next_screen"}}}, {"id": "ear-l2-s2", "type": "lesson_screen", "order": 2, "estimatedTimeSeconds": 90, "content": {"headline": "Steps to Graph a Line", "body_md": "1.  **Find Solution Pairs (Points):** Choose 'x' values, substitute into the equation, calculate 'y'. Each (x, y) is a point.\n2.  **Plot Points:** Mark (x, y) pairs on the coordinate plane.\n3.  **Draw Line:** Connect points with a straight line.\n\nExample: `y = x - 1`\n- If x = 0, y = -1. Point: (0, -1)\n- If x = 2, y = 1. Point: (2, 1)\n- If x = 4, y = 3. Point: (4, 3)", "visual": {"type": "giphy_search", "value": "drawing line graph"}, "hook": "Just a few points, and you've got a line!", "interactive_element": {"type": "button", "text": "Let me try graphing one!", "action": "next_screen"}}}, {"id": "ear-l2-s3", "type": "lesson_screen", "order": 3, "estimatedTimeSeconds": 150, "content": {"headline": "Interactive: Graph y = x + 2", "body_md": "Let's graph `y = x + 2`.\nFind at least two (x, y) pairs. Click on the graph to plot them, then draw the line.", "visual": {"type": "static_text", "value": "Interactive: Graph y = x + 2"}, "interactive_element": {"type": "interactive_linear_equation_grapher", "equation_string": "y = x + 2", "target_points_examples_for_user": [{"x": 0, "y": 2}, {"x": 1, "y": 3}, {"x": -2, "y": 0}], "prompt_plot_points": "Plot points for `y = x + 2`.", "prompt_draw_line": "Draw the line through your points.", "feedback_correct_points": "Points are correct!", "feedback_correct_line": "Perfect line for `y = x + 2`!", "feedback_incorrect_points": "Check your (x,y) pairs for `y = x + 2`.", "feedback_incorrect_line": "Line doesn't match `y = x + 2`. Ensure points are accurate.", "action_button_text": "Why is this visual so useful?", "x_min": -5, "x_max": 5, "y_min": -5, "y_max": 5}, "hook": "Time to plot your way to understanding!"}}, {"id": "ear-l2-s4", "type": "lesson_screen", "order": 4, "estimatedTimeSeconds": 40, "content": {"headline": "Visualizing Relationships", "body_md": "Graphing linear equations helps visualize variable relationships and is key for complex algebra.", "visual": {"type": "giphy_search", "value": "connected dots"}, "hook": "You're now a line artist!", "interactive_element": {"type": "button", "text": "What's the deal with 'slope'?", "action": "next_lesson"}}}]}, {"id": "ear-l3-understanding-slope", "title": "The Ups and Downs: Understanding Slope", "description": "Explore the concept of slope as the rate of change of a line, how to calculate it, and what positive, negative, zero, or undefined slopes mean visually.", "order": 3, "type": "interactive_lesson", "estimatedTimeMinutes": 9, "contentBlocks": [{"id": "ear-l3-s1", "type": "lesson_screen", "order": 1, "estimatedTimeSeconds": 70, "content": {"headline": "What is <PERSON><PERSON><PERSON>?", "body_md": "Slope (m) describes a line's steepness/direction: how much y changes per unit change in x. It's the **rate of change**.\n\nFormula: `m = (change in y) / (change in x) = (y₂ - y₁) / (x₂ - x₁)`\n(Remembered as **\"rise over run\"**)", "visual": {"type": "local_asset", "value": "assets/images/algebra/slope_rise_over_run.svg", "alt_text": "Rise over run diagram."}, "hook": "Ever wondered how we measure the 'tilt' of a line? That's slope!", "interactive_element": {"type": "button", "text": "What do different slopes look like?", "action": "next_screen"}}}, {"id": "ear-l3-s2", "type": "lesson_screen", "order": 2, "estimatedTimeSeconds": 90, "content": {"headline": "Interpreting Slope Values", "body_md": "-   **Positive Slope (m > 0):** Line goes **upward** left to right.\n-   **Negative Slope (m < 0):** Line goes **downward** left to right.\n-   **Zero Slope (m = 0):** Line is **horizontal** (no change in y).\n-   **Undefined Slope:** Line is **vertical** (no change in x, division by zero).", "visual": {"type": "giphy_search", "value": "mountain slope ski"}, "hook": "Positive, negative, zero, or even undefined – slope tells a story.", "interactive_element": {"type": "button", "text": "Let me play with slopes!", "action": "next_screen"}}}, {"id": "ear-l3-s3", "type": "lesson_screen", "order": 3, "estimatedTimeSeconds": 150, "content": {"headline": "Interactive Slope Explorer", "body_md": "Use sliders to adjust slope 'm' and y-intercept 'c' in `y = mx + c`.\nObserve how the line tilts and moves. What happens when 'm' is positive? Negative? Zero? How does 'c' affect position?", "visual": {"type": "static_text", "value": "Interactive: Slope Explorer for y = mx + c"}, "interactive_element": {"type": "interactive_slope_explorer", "line_equation_template": "y = mx + c", "initial_m": 1, "initial_c": 0, "m_min": -5, "m_max": 5, "m_step": 0.5, "c_min": -3, "c_max": 3, "c_step": 0.5, "prompt": "Adjust 'm' and 'c' to see their effect.", "x_axis_min": -5, "x_axis_max": 5, "y_axis_min": -5, "y_axis_max": 5, "show_slope_value": true, "show_intercept_value": true, "action_button_text": "How does slope relate to real life?"}, "hook": "Time to become a slope architect!"}}, {"id": "ear-l3-s4", "type": "lesson_screen", "order": 4, "estimatedTimeSeconds": 40, "content": {"headline": "Slope in Real Life", "body_md": "Understanding slope is crucial as it describes how one quantity changes relative to another (e.g., speed, steepness of a hill).", "visual": {"type": "giphy_search", "value": "road trip speed"}, "hook": "From ski hills to road grades, slope is everywhere!", "interactive_element": {"type": "button", "text": "How do we use slope to define a line's equation?", "action": "next_lesson"}}}]}, {"id": "ear-l4-equation-of-a-line", "title": "Decoding Lines: The Slope-Intercept Form (y = mx + c)", "description": "Master the slope-intercept form (y = mx + c) to quickly identify a line's slope and y-intercept, and to write its equation from a graph.", "order": 4, "type": "interactive_lesson", "estimatedTimeMinutes": 9, "contentBlocks": [{"id": "ear-l4-s1", "type": "lesson_screen", "order": 1, "estimatedTimeSeconds": 70, "content": {"headline": "Slope-Intercept Form: y = mx + c", "body_md": "A common way to write linear equations is the **slope-intercept form**: `y = mx + c` (or `y = mx + b`).\n\n-   **`m` is the slope** (steepness).\n-   **`c` (or `b`) is the y-intercept** (where line crosses y-axis; point (0, c)).\n\nExample: `y = 2x + 3` => slope m=2, y-intercept c=3 (crosses at (0,3)).\n`y = -x - 4` => `y = (-1)x + (-4)` => slope m=-1, y-intercept c=-4 (crosses at (0,-4)).", "visual": {"type": "static_text", "value": "y = mx + c"}, "hook": "This famous equation is like a secret code for lines!", "interactive_element": {"type": "button", "text": "Let's decode a graph!", "action": "next_screen"}}}, {"id": "ear-l4-s2", "type": "lesson_screen", "order": 2, "estimatedTimeSeconds": 180, "content": {"headline": "Interactive: Graph to Equation", "body_md": "Analyze the graph to find its equation in `y = mx + c` form.", "visual": {"type": "local_asset", "value": "assets/images/algebra/graph_slope2_intercept1.png", "alt_text": "Graph of a line with slope 2 and y-intercept 1."}, "interactive_element": {"type": "interactive_graph_to_equation_matcher", "graph_image_path": "assets/images/algebra/graph_slope2_intercept1.png", "introduction_text": "Find the equation for the graphed line.", "prompt_m": "1. Determine slope (m) ('rise over run').", "input_m_placeholder": "Enter slope (m)", "correct_m": 2, "feedback_correct_m": "Correct! Slope (m) is 2.", "feedback_incorrect_m": "Slope = (y₂-y₁)/(x₂-x₁).", "prompt_c": "2. Identify y-intercept (c) (where line crosses y-axis).", "input_c_placeholder": "Enter y-intercept (c)", "correct_c": 1, "feedback_correct_c": "Spot on! Y-intercept (c) is 1.", "feedback_incorrect_c": "Where does it cross the y-axis?", "prompt_equation": "3. Write the equation: `y = mx + c`.", "equation_placeholder": "e.g., y = 2x + 1", "correct_equation_regex": "^\\s*y\\s*=\\s*2\\s*\\*?\\s*x\\s*\\+\\s*1\\s*$", "feedback_correct_equation": "Perfect! Equation is `y = 2x + 1`.", "feedback_incorrect_equation": "Use your m and c in `y = mx + c`.", "action_button_text": "Why is this form so handy?"}, "hook": "Put on your detective hat and find 'm' and 'c'!"}}, {"id": "ear-l4-s3", "type": "lesson_screen", "order": 3, "estimatedTimeSeconds": 40, "content": {"headline": "Sketching Lines", "body_md": "Slope-intercept form makes sketching easy: plot y-intercept (0,c), use slope (rise/run) for another point, connect!", "visual": {"type": "giphy_search", "value": "drawing sketch easy"}, "hook": "With m and c, you can sketch any line in a flash!", "interactive_element": {"type": "button", "text": "What happens when lines meet?", "action": "next_lesson"}}}]}, {"id": "ear-l5-solving-simple-systems-visually", "title": "Intersection Point: Solving Systems of Equations Visually", "description": "Understand that the solution to a system of two linear equations is the point where their graphs intersect, and practice finding this point visually.", "order": 5, "type": "interactive_lesson", "estimatedTimeMinutes": 9, "contentBlocks": [{"id": "ear-l5-s1", "type": "lesson_screen", "order": 1, "estimatedTimeSeconds": 70, "content": {"headline": "Systems of Equations", "body_md": "A **system of equations** is multiple equations with the same variables. Ex:\nEq 1: `y = x + 1`\nEq 2: `y = -x + 3`\n\nA **solution** is an (x, y) pair making *both* true. Graphically, it's where their lines **intersect**.", "visual": {"type": "local_asset", "value": "assets/images/algebra/system_intersection_point.svg", "alt_text": "Two lines intersecting."}, "hook": "What happens when two lines cross paths? That's a system solution!", "interactive_element": {"type": "button", "text": "Let's find where they meet!", "action": "next_screen"}}}, {"id": "ear-l5-s2", "type": "lesson_screen", "order": 2, "estimatedTimeSeconds": 180, "content": {"headline": "Interactive: Solve System Visually", "body_md": "System:\n1. `y = x + 1`\n2. `y = -x + 3`\nGraph both lines. Then click their intersection point.", "visual": {"type": "static_text", "value": "Interactive: Graph y=x+1 and y=-x+3"}, "interactive_element": {"type": "interactive_system_of_equations_grapher", "equation1_string": "y = x + 1", "equation2_string": "y = -x + 3", "introduction_text": "Graph `y = x + 1` and `y = -x + 3`.", "prompt_graph_lines": "Graph both lines.", "prompt_find_intersection": "Click the intersection point.", "correct_intersection_point": {"x": 1, "y": 2}, "feedback_correct_intersection": "Exactly! Intersection at (1, 2). This solves both equations.", "feedback_incorrect_intersection": "Check your graphs. The intersection must be on *both* lines.", "action_button_text": "Why is this intersection point special?", "x_min": -5, "x_max": 5, "y_min": -5, "y_max": 5}, "hook": "Graph both lines and pinpoint their meeting spot!"}}, {"id": "ear-l5-s3", "type": "lesson_screen", "order": 3, "estimatedTimeSeconds": 50, "content": {"headline": "Visual Solutions", "body_md": "Solving systems by graphing is a great visual way to understand solutions. Later, you'll learn algebraic methods (substitution, elimination) for more precision.", "visual": {"type": "giphy_search", "value": "puzzle solved"}, "hook": "You've seen how graphs can solve multiple equations at once!", "interactive_element": {"type": "button", "text": "Ready for the Relationship Resolver Test!", "action": "next_lesson"}}}]}], "moduleTest": {"id": "ear-mt1-relationship-resolver", "title": "Module Test: Relationship Resolver", "description": "Analyze and represent algebraic relationships through equations and graphs.", "order": 6, "type": "module_test_interactive", "estimatedTimeMinutes": 7, "contentBlocks": [{"id": "ear-mt1-s0-intro", "type": "test_screen_intro", "order": 1, "estimatedTimeSeconds": 20, "content": {"headline": "Module Test: Relationship Resolver", "body_md": "Let's test your skills on algebraic relationships!", "visual": {"type": "giphy_search", "value": "connections puzzle"}, "interactive_element": {"type": "button", "text": "Start!", "action": "next_screen"}}}, {"id": "ear-mt1-s1-q1", "type": "test_screen_intro", "order": 2, "estimatedTimeSeconds": 70, "content": {"headline": "Question 1: Recipe Equation", "body_md": "A recipe needs 3 eggs for every cake. If 'e' is eggs and 'c' is cakes, write an equation for 'e' in terms of 'c'.", "interactive_element": {"type": "text_input", "placeholder": "Enter equation (e.g., e = 3c)", "correct_answer_regex": "^\\s*e\\s*=\\s*3\\s*\\*?\\s*c\\s*$", "feedback_correct": "Correct! e = 3c.", "feedback_incorrect": "If c=1, e=3. If c=2, e=6. How are e and c related?", "action_button_text": "Next Question"}}}, {"id": "ear-mt1-s2-q2", "type": "test_screen_intro", "order": 3, "estimatedTimeSeconds": 60, "content": {"headline": "Question 2: Y-Intercept", "body_md": "What is the y-intercept of the line `y = -4x + 5`?", "interactive_element": {"type": "multiple_choice_text", "options": [{"id": "opt1", "text": "-4", "is_correct": false}, {"id": "opt2", "text": "5", "is_correct": true, "feedback_correct": "Correct!"}, {"id": "opt3", "text": "x", "is_correct": false}, {"id": "opt4", "text": "1", "is_correct": false}], "action_button_text": "Next Question"}}}, {"id": "ear-mt1-s3-q3", "type": "test_screen_intro", "order": 4, "estimatedTimeSeconds": 70, "content": {"headline": "Question 3: Slope Calculation", "body_md": "What is the slope of the line passing through points (1, 2) and (3, 8)?", "interactive_element": {"type": "text_input", "placeholder": "Enter slope value", "correct_answer_regex": "^3$", "feedback_correct": "Correct! Slope is 3.", "feedback_incorrect": "Slope = (y₂ - y₁) / (x₂ - x₁).", "action_button_text": "Next Question"}}}, {"id": "ear-mt1-s4-q4", "type": "test_screen_intro", "order": 5, "estimatedTimeSeconds": 90, "content": {"headline": "Question 4: Intersection Point", "body_md": "At what point (x,y) do the lines `y = 2x` and `y = x + 2` intersect? Enter as 'x,y'.", "interactive_element": {"type": "text_input", "placeholder": "Enter coordinates e.g., 2,4", "correct_answer_regex": "^\\s*2\\s*,\\s*4\\s*$", "feedback_correct": "Correct! They intersect at (2,4).", "feedback_incorrect": "Set 2x = x + 2. Solve for x, then find y.", "action_button_text": "Finish Test"}}}, {"id": "ear-mt1-s5-conclusion", "type": "lesson_screen", "order": 6, "estimatedTimeSeconds": 30, "content": {"headline": "Test Complete!", "body_md": "Well done resolving those relationships!", "visual": {"type": "giphy_search", "value": "connections complete"}, "interactive_element": {"type": "button", "text": "Back to Course", "action": "module_complete"}}}]}}