{"id": "materials-science-engineering", "title": "Materials Science and Engineering", "description": "Explore the properties, behavior, and selection of materials for engineering applications.", "order": 3, "lessons": [{"id": "material-structure-atomic-bonding", "title": "Material Structure: Atomic Bonding and Microstructure", "description": "Understand the basis of material properties.", "order": 1, "type": "interactive_lesson", "estimatedTimeMinutes": 20, "contentBlocks": [{"id": "material-structure-screen-1", "type": "lesson_screen", "order": 1, "estimatedTimeSeconds": 60, "content": {"headline": "The Building Blocks of Everything", "body_md": "**Materials science** is the study of the properties, structure, and performance of materials. At its core, it explores a fundamental question: **Why do materials behave the way they do?**\n\nThe answer begins at the atomic level, where the arrangement and bonding of atoms determine virtually every property we can observe.", "visual": {"type": "unsplash_search", "value": "atomic structure material science"}, "interactive_element": {"type": "button", "button_text": "Explore Atomic Bonding", "action": "next_screen"}}}, {"id": "material-structure-screen-2", "type": "lesson_screen", "order": 2, "estimatedTimeSeconds": 90, "content": {"headline": "Atomic Bonding: How Atoms Stick Together", "body_md": "The way atoms connect to each other creates four primary types of bonds:\n\n- **Metallic bonds**: Electrons are shared among many atoms, creating a \"sea\" of electrons (found in metals)\n- **Ionic bonds**: Electrons are transferred between atoms, creating positive and negative ions (found in salts)\n- **Covalent bonds**: Electrons are shared between specific atoms (found in polymers, ceramics)\n- **Secondary bonds**: Weaker attractions between molecules (like hydrogen bonds)\n\nThese bonds determine fundamental properties like strength, melting point, and electrical conductivity.", "visual": {"type": "giphy_search", "value": "atomic bonding animation"}, "interactive_element": {"type": "multiple_choice_text", "question": "Which type of bonding is responsible for the high electrical conductivity of metals?", "options": [{"id": "opt1", "text": "Ionic bonding", "is_correct": false, "feedback": "Ionic compounds typically have poor electrical conductivity in solid form."}, {"id": "opt2", "text": "Covalent bonding", "is_correct": false, "feedback": "Covalent bonds typically create materials that are poor conductors of electricity."}, {"id": "opt3", "text": "Metallic bonding", "is_correct": true, "feedback": "Correct! The 'sea' of delocalized electrons in metallic bonding allows electrons to move freely, creating high electrical conductivity."}, {"id": "opt4", "text": "Hydrogen bonding", "is_correct": false, "feedback": "Hydrogen bonding is a type of secondary bond and doesn't contribute to electrical conductivity."}]}}}, {"id": "material-structure-screen-3", "type": "lesson_screen", "order": 3, "estimatedTimeSeconds": 90, "content": {"headline": "Crystal Structures: Patterns in Solids", "body_md": "Most solid materials form **crystalline structures** - regular, repeating 3D arrangements of atoms. Common crystal structures include:\n\n- **Body-Centered Cubic (BCC)**: Found in iron, chromium, tungsten\n- **Face-Centered Cubic (FCC)**: Found in aluminum, copper, gold\n- **Hexagonal Close-Packed (HCP)**: Found in zinc, magnesium, titanium\n\nThese arrangements determine how atoms can move relative to each other, affecting properties like ductility, strength, and thermal expansion.", "visual": {"type": "giphy_search", "value": "crystal structure animation"}, "interactive_element": {"type": "button", "button_text": "Beyond Perfect Crystals", "action": "next_screen"}}}, {"id": "material-structure-screen-4", "type": "lesson_screen", "order": 4, "estimatedTimeSeconds": 120, "content": {"headline": "Crystal Defects: Imperfections Matter", "body_md": "Real materials are never perfect crystals. They contain **defects** that significantly influence properties:\n\n- **Point defects**: Missing atoms (vacancies) or extra atoms (interstitials)\n- **Line defects (dislocations)**: Entire rows of atoms out of place\n- **Planar defects**: Grain boundaries, twin boundaries\n- **Bulk defects**: Voids, cracks, inclusions\n\nSurprisingly, these \"imperfections\" are often beneficial. For example, dislocations allow metals to deform without breaking, making them ductile.", "visual": {"type": "unsplash_search", "value": "crystal defect material science"}, "interactive_element": {"type": "multiple_choice_text", "question": "Why are dislocations (line defects) important in metals?", "options": [{"id": "opt1", "text": "They make metals more brittle", "is_correct": false, "feedback": "Actually, dislocations typically make metals less brittle (more ductile)."}, {"id": "opt2", "text": "They allow planes of atoms to slip past each other, enabling ductility", "is_correct": true, "feedback": "Correct! Dislocations allow metals to deform plastically without breaking, which is why metals can be bent and shaped."}, {"id": "opt3", "text": "They increase electrical resistance", "is_correct": false, "feedback": "While dislocations can slightly affect electrical properties, this isn't their primary importance."}, {"id": "opt4", "text": "They prevent corrosion", "is_correct": false, "feedback": "Dislocations don't prevent corrosion; in fact, they can sometimes be sites where corrosion begins."}]}}}, {"id": "material-structure-screen-5", "type": "lesson_screen", "order": 5, "estimatedTimeSeconds": 120, "content": {"headline": "Microstructure: The Mesoscale View", "body_md": "Between the atomic scale and what we can see with our eyes lies the **microstructure** - features visible under a microscope:\n\n- **Grains**: Individual crystals in a polycrystalline material\n- **Phases**: Regions with distinct composition and structure\n- **Precipitates**: Particles of one phase within another\n- **Domains**: Regions with aligned properties (like magnetic domains)\n\nMicrostructure is often the most important factor in determining a material's properties and can be controlled through processing.", "visual": {"type": "unsplash_search", "value": "metal microstructure microscope"}, "interactive_element": {"type": "multiple_choice_text", "question": "What happens to a metal's strength when its grain size is reduced?", "options": [{"id": "opt1", "text": "The strength decreases", "is_correct": false, "feedback": "Smaller grains typically lead to higher strength, not lower."}, {"id": "opt2", "text": "The strength increases", "is_correct": true, "feedback": "Correct! Smaller grains create more grain boundaries, which impede dislocation movement. This is known as the <PERSON>-<PERSON><PERSON> relationship."}, {"id": "opt3", "text": "The strength remains unchanged", "is_correct": false, "feedback": "Grain size has a significant effect on strength; it doesn't remain unchanged."}, {"id": "opt4", "text": "The relationship is unpredictable", "is_correct": false, "feedback": "The relationship is actually quite predictable - smaller grains generally mean higher strength."}]}}}, {"id": "material-structure-screen-6", "type": "lesson_screen", "order": 6, "estimatedTimeSeconds": 90, "content": {"headline": "Amorphous Materials: Order vs. Disorder", "body_md": "Not all materials form crystals. **Amorphous materials** lack long-range atomic order:\n\n- **Glasses**: Silica-based (window glass), metallic, polymeric\n- **Many polymers**: Random molecular arrangements\n- **Some ceramics**: Rapid cooling prevents crystal formation\n\nWithout crystal planes to slip along, these materials often behave differently under stress - they may be brittle (like window glass) or viscoelastic (like many polymers).", "visual": {"type": "giphy_search", "value": "glass material amorphous"}, "interactive_element": {"type": "button", "button_text": "Structure-Property Relationships", "action": "next_screen"}}}, {"id": "material-structure-screen-7", "type": "lesson_screen", "order": 7, "estimatedTimeSeconds": 90, "content": {"headline": "Connecting Structure to Properties", "body_md": "The central theme of materials science is the **structure-property relationship**:\n\n- **Electrical properties** depend on electron mobility (metallic bonds = good conductors)\n- **Mechanical strength** depends on resistance to atomic movement (strong bonds = high strength)\n- **Thermal properties** depend on atomic vibrations (rigid structures = low thermal expansion)\n- **Optical properties** depend on electron energy transitions (band gap = color)\n\nBy understanding these relationships, engineers can select or design materials with specific properties for particular applications.", "visual": {"type": "unsplash_search", "value": "material properties engineering"}, "interactive_element": {"type": "multiple_choice_text", "question": "Why do ceramics typically have higher melting points than metals?", "options": [{"id": "opt1", "text": "Ceramics have lower atomic weights", "is_correct": false, "feedback": "Atomic weight isn't directly related to melting point in this way."}, {"id": "opt2", "text": "Ceramics have stronger directional bonds that require more energy to break", "is_correct": true, "feedback": "Correct! Ceramics typically have strong ionic or covalent bonds that are directional, requiring more thermal energy to disrupt."}, {"id": "opt3", "text": "Ceramics have more crystal defects", "is_correct": false, "feedback": "Ceramics typically have fewer defects than metals, and defects often lower melting points rather than raise them."}, {"id": "opt4", "text": "Ceramics have higher thermal conductivity", "is_correct": false, "feedback": "Most ceramics actually have lower thermal conductivity than metals."}]}}}, {"id": "material-structure-screen-8", "type": "lesson_screen", "order": 8, "estimatedTimeSeconds": 60, "content": {"headline": "Material Structure: Foundation of Properties", "body_md": "You've now explored the fundamental aspects of material structure:\n\n- Atomic bonding determines basic material properties\n- Crystal structures create ordered arrangements of atoms\n- Defects and microstructure significantly influence behavior\n- Amorphous materials lack long-range order\n- Structure directly connects to observable properties\n\nThis understanding forms the foundation for all materials selection and design in engineering.", "visual": {"type": "giphy_search", "value": "engineering materials future"}, "interactive_element": {"type": "button", "button_text": "Finish Lesson", "action": "complete_lesson"}}}]}, {"id": "mechanical-properties-stress-strain", "title": "Mechanical Properties: <PERSON>ress, Strain, Strength, Toughness", "description": "Analyze how materials respond to loads.", "order": 2, "type": "interactive_lesson", "estimatedTimeMinutes": 25, "contentBlocks": [{"id": "mechanical-properties-screen-1", "type": "lesson_screen", "order": 1, "estimatedTimeSeconds": 60, "content": {"headline": "When Materials Meet Forces", "body_md": "**Mechanical properties** describe how materials respond to applied forces. Understanding these properties is crucial for engineers to:\n\n- Select appropriate materials for specific applications\n- Predict how structures will behave under load\n- Design components that won't fail during use\n- Optimize material usage for cost and performance", "visual": {"type": "unsplash_search", "value": "material testing machine"}, "interactive_element": {"type": "button", "button_text": "Stress & Strain Basics", "action": "next_screen"}}}, {"id": "mechanical-properties-screen-2", "type": "lesson_screen", "order": 2, "estimatedTimeSeconds": 90, "content": {"headline": "Stress & Strain: The Fundamentals", "body_md": "Two fundamental concepts form the basis of mechanical properties:\n\n- **Stress (σ)**: Force per unit area (N/m² or Pa)\n  - Measures the internal resistance to deformation\n  - Independent of object size\n\n- **Strain (ε)**: Deformation relative to original dimensions (dimensionless)\n  - Measures how much a material stretches or compresses\n  - Expressed as a ratio or percentage\n\nThese concepts allow us to compare materials regardless of their size or shape.", "visual": {"type": "giphy_search", "value": "stress strain animation"}, "interactive_element": {"type": "multiple_choice_text", "question": "Why do engineers use stress instead of force when analyzing materials?", "options": [{"id": "opt1", "text": "Stress is easier to measure directly", "is_correct": false, "feedback": "Actually, force is typically measured directly, and stress is calculated from force and area."}, {"id": "opt2", "text": "Stress accounts for the size of the object, making comparisons between different sized components possible", "is_correct": true, "feedback": "Correct! Stress (force/area) normalizes for size, allowing direct comparison of material properties regardless of dimensions."}, {"id": "opt3", "text": "Stress is always constant throughout a material", "is_correct": false, "feedback": "Stress can vary throughout a material, especially in complex geometries or loading conditions."}, {"id": "opt4", "text": "Stress is always proportional to strain", "is_correct": false, "feedback": "Stress is only proportional to strain in the elastic region (<PERSON><PERSON>'s Law), not throughout all loading conditions."}]}}}, {"id": "mechanical-properties-screen-3", "type": "lesson_screen", "order": 3, "estimatedTimeSeconds": 90, "content": {"headline": "Types of Stress & Deformation", "body_md": "Materials experience different types of stress:\n\n- **Tensile stress**: Pulling forces (stretching)\n- **Compressive stress**: Pushing forces (squeezing)\n- **Shear stress**: Forces acting parallel to a surface (sliding)\n- **Torsional stress**: Twisting forces\n- **Bending stress**: Combination of tension and compression\n\nEach type of stress creates corresponding strains and may affect materials differently.", "visual": {"type": "giphy_search", "value": "types of stress deformation"}, "interactive_element": {"type": "button", "button_text": "The Stress-Strain Curve", "action": "next_screen"}}}, {"id": "mechanical-properties-screen-4", "type": "lesson_screen", "order": 4, "estimatedTimeSeconds": 120, "content": {"headline": "The Stress-Strain Curve: Material's Story", "body_md": "A **stress-strain curve** reveals a material's mechanical behavior:\n\n1. **Elastic region**: Material returns to original shape when force is removed\n   - Linear portion follows <PERSON><PERSON>'s Law (stress ∝ strain)\n   - Slope = <PERSON>'s modulus (E) = stiffness\n\n2. **Yield point**: Transition from elastic to plastic behavior\n\n3. **Plastic region**: Permanent deformation occurs\n   - Material won't return to original shape\n\n4. **Ultimate tensile strength**: Maximum stress before failure\n\n5. **Fracture point**: Complete failure", "visual": {"type": "unsplash_search", "value": "stress strain curve graph"}, "interactive_element": {"type": "multiple_choice_text", "question": "What does a steep slope in the elastic region of a stress-strain curve indicate?", "options": [{"id": "opt1", "text": "The material is very ductile", "is_correct": false, "feedback": "Ductility is related to the extent of the plastic region, not the slope of the elastic region."}, {"id": "opt2", "text": "The material has high stiffness (requires more force to deform)", "is_correct": true, "feedback": "Correct! A steep slope indicates a high Young's modulus (E), meaning the material is stiff and resists elastic deformation."}, {"id": "opt3", "text": "The material will break easily", "is_correct": false, "feedback": "The slope doesn't directly indicate how easily a material will break; that's more related to strength and toughness."}, {"id": "opt4", "text": "The material has a low yield strength", "is_correct": false, "feedback": "The slope (<PERSON>'s modulus) and yield strength are separate properties; a steep slope doesn't imply low yield strength."}]}}}, {"id": "mechanical-properties-screen-5", "type": "lesson_screen", "order": 5, "estimatedTimeSeconds": 120, "content": {"headline": "Key Mechanical Properties", "body_md": "From the stress-strain relationship, we define several important properties:\n\n- **<PERSON>'s modulus (E)**: Measure of stiffness (resistance to elastic deformation)\n- **Yield strength**: Stress at which plastic deformation begins\n- **Ultimate tensile strength**: Maximum stress before failure\n- **Ductility**: Ability to deform plastically before fracture\n- **Resilience**: Ability to absorb energy elastically (area under elastic portion)\n- **Toughness**: Total energy absorbed before fracture (entire area under curve)\n- **Hardness**: Resistance to surface indentation or scratching", "visual": {"type": "giphy_search", "value": "material testing strength"}, "interactive_element": {"type": "multiple_choice_text", "question": "Which property best describes a material's ability to absorb energy without permanent deformation?", "options": [{"id": "opt1", "text": "<PERSON><PERSON>ness", "is_correct": false, "feedback": "Toughness includes both elastic and plastic energy absorption before fracture."}, {"id": "opt2", "text": "Ductility", "is_correct": false, "feedback": "Ductility refers to plastic deformation before fracture, not elastic energy absorption."}, {"id": "opt3", "text": "Resilience", "is_correct": true, "feedback": "Correct! Resilience specifically measures a material's ability to absorb energy elastically and return to its original shape."}, {"id": "opt4", "text": "Hardness", "is_correct": false, "feedback": "Hardness measures resistance to surface deformation, not energy absorption."}]}}}, {"id": "mechanical-properties-screen-6", "type": "lesson_screen", "order": 6, "estimatedTimeSeconds": 90, "content": {"headline": "Material Behavior Categories", "body_md": "Materials exhibit different mechanical behaviors:\n\n- **Brittle materials** (ceramics, glass):\n  - Little/no plastic deformation\n  - Fracture suddenly without warning\n  - High strength but low toughness\n\n- **Ductile materials** (most metals):\n  - Significant plastic deformation before failure\n  - Visible necking provides warning before failure\n  - Good toughness and energy absorption\n\n- **Elastomers** (rubber):\n  - Extreme elastic deformation\n  - Return to original shape after large strains", "visual": {"type": "unsplash_search", "value": "brittle ductile materials"}, "interactive_element": {"type": "button", "button_text": "Factors Affecting Properties", "action": "next_screen"}}}, {"id": "mechanical-properties-screen-7", "type": "lesson_screen", "order": 7, "estimatedTimeSeconds": 90, "content": {"headline": "Factors Affecting Mechanical Properties", "body_md": "Mechanical properties can be modified by various factors:\n\n- **Temperature**: Higher temperatures typically reduce strength and increase ductility\n- **Strain rate**: Faster loading often increases apparent strength but reduces ductility\n- **Microstructure**: Grain size, phases, defects significantly affect properties\n- **Processing history**: Heat treatment, work hardening, alloying\n- **Environment**: Corrosion, radiation, chemical exposure can degrade properties\n\nEngineers must consider these factors when selecting materials for specific applications.", "visual": {"type": "giphy_search", "value": "metal heat treatment"}, "interactive_element": {"type": "multiple_choice_text", "question": "How does decreasing temperature typically affect a metal's mechanical properties?", "options": [{"id": "opt1", "text": "Increases ductility, decreases strength", "is_correct": false, "feedback": "Lower temperatures typically have the opposite effect."}, {"id": "opt2", "text": "Increases both strength and ductility", "is_correct": false, "feedback": "Strength and ductility usually have an inverse relationship with temperature changes."}, {"id": "opt3", "text": "Decreases both strength and ductility", "is_correct": false, "feedback": "Lower temperatures typically increase strength."}, {"id": "opt4", "text": "Increases strength, decreases ductility", "is_correct": true, "feedback": "Correct! Lower temperatures typically make metals stronger but more brittle (less ductile), which is why some metals can become dangerously brittle in extremely cold environments."}]}}}, {"id": "mechanical-properties-screen-8", "type": "lesson_screen", "order": 8, "estimatedTimeSeconds": 60, "content": {"headline": "Mechanical Properties: Engineering Essentials", "body_md": "You've now explored the fundamental mechanical properties of materials:\n\n- Stress and strain describe forces and deformations\n- The stress-strain curve reveals a material's mechanical story\n- Key properties include modulus, strength, ductility, and toughness\n- Materials can be brittle, ductile, or elastic\n- Properties change with temperature, processing, and environment\n\nThese concepts are essential for material selection and structural design in virtually every engineering discipline.", "visual": {"type": "giphy_search", "value": "engineering design success"}, "interactive_element": {"type": "button", "button_text": "Finish Lesson", "action": "complete_lesson"}}}]}, {"id": "thermal-properties-heat-capacity", "title": "Thermal Properties: Heat Capacity, Thermal Expansion", "description": "Understand material behavior with temperature changes.", "order": 3, "type": "interactive_lesson", "estimatedTimeMinutes": 20, "contentBlocks": []}, {"id": "electrical-properties-conductivity", "title": "Electrical Properties: Conductivity, Resistivity", "description": "Explore how materials conduct electricity.", "order": 4, "type": "interactive_lesson", "estimatedTimeMinutes": 20, "contentBlocks": []}, {"id": "material-selection-for-design", "title": "Material Selection for Design", "description": "Choose appropriate materials based on application requirements.", "order": 5, "type": "interactive_lesson", "estimatedTimeMinutes": 25, "contentBlocks": []}, {"id": "module-test-materials-maven", "title": "Module Test: The Materials Maven", "description": "Understand the fundamental properties of engineering materials and apply them in design considerations.", "order": 6, "type": "module_test_interactive", "estimatedTimeMinutes": 35, "passingScorePercentage": 70, "contentBlocks": []}]}