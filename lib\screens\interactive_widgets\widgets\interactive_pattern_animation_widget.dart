import 'package:flutter/material.dart';
import 'dart:math' as math;
import 'dart:async';

class InteractivePatternAnimationWidget extends StatefulWidget {
  final String patternType;
  final bool autoPlay;
  final bool loop;
  final double width;
  final double height;
  final Color primaryColor;
  final Color secondaryColor;
  final Color backgroundColor;
  final Duration animationDuration;

  const InteractivePatternAnimationWidget({
    Key? key,
    required this.patternType,
    this.autoPlay = true,
    this.loop = true,
    this.width = double.infinity,
    this.height = 200,
    this.primaryColor = Colors.purple,
    this.secondaryColor = Colors.amber,
    this.backgroundColor = Colors.white,
    this.animationDuration = const Duration(seconds: 5),
  }) : super(key: key);

  factory InteractivePatternAnimationWidget.fromData(
    Map<String, dynamic> data,
  ) {
    return InteractivePatternAnimationWidget(
      patternType: data['pattern_type'] ?? 'fibonacci_spiral',
      autoPlay: data['auto_play'] ?? true,
      loop: data['loop'] ?? true,
      width: data['width']?.toDouble() ?? double.infinity,
      height: data['height']?.toDouble() ?? 200,
      primaryColor: _parseColor(data['primary_color'], Colors.purple),
      secondaryColor: _parseColor(data['secondary_color'], Colors.amber),
      backgroundColor: _parseColor(data['background_color'], Colors.white),
      animationDuration: Duration(
        milliseconds: data['animation_duration_ms'] ?? 5000,
      ),
    );
  }

  static Color _parseColor(String? colorString, Color defaultColor) {
    if (colorString == null) return defaultColor;
    try {
      return Color(int.parse(colorString.replaceAll('#', '0xFF')));
    } catch (e) {
      return defaultColor;
    }
  }

  @override
  State<InteractivePatternAnimationWidget> createState() =>
      _InteractivePatternAnimationWidgetState();
}

class _InteractivePatternAnimationWidgetState
    extends State<InteractivePatternAnimationWidget>
    with SingleTickerProviderStateMixin {
  late AnimationController _controller;
  bool _isPlaying = false;
  Timer? _loopTimer;

  @override
  void initState() {
    super.initState();
    _controller = AnimationController(
      vsync: this,
      duration: widget.animationDuration,
    );

    _controller.addStatusListener((status) {
      if (status == AnimationStatus.completed) {
        if (widget.loop) {
          _loopTimer = Timer(const Duration(milliseconds: 500), () {
            if (mounted) {
              _controller.reset();
              _controller.forward();
            }
          });
        } else {
          setState(() {
            _isPlaying = false;
          });
        }
      }
    });

    if (widget.autoPlay) {
      _isPlaying = true;
      _controller.forward();
    }
  }

  @override
  void dispose() {
    _controller.dispose();
    _loopTimer?.cancel();
    super.dispose();
  }

  void _togglePlayPause() {
    setState(() {
      _isPlaying = !_isPlaying;
      if (_isPlaying) {
        _controller.forward();
      } else {
        _controller.stop();
      }
    });
  }

  void _restart() {
    setState(() {
      _isPlaying = true;
      _controller.reset();
      _controller.forward();
    });
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      mainAxisSize: MainAxisSize.min,
      children: [
        // Animation container
        Container(
          width: widget.width,
          height: widget.height,
          decoration: BoxDecoration(
            color: widget.backgroundColor,
            borderRadius: BorderRadius.circular(12),
            border: Border.all(color: Colors.grey[300]!),
          ),
          child: ClipRRect(
            borderRadius: BorderRadius.circular(12),
            child: AnimatedBuilder(
              animation: _controller,
              builder: (context, child) {
                return CustomPaint(
                  painter: _getPatternPainter(
                    widget.patternType,
                    _controller.value,
                    widget.primaryColor,
                    widget.secondaryColor,
                  ),
                  child: Container(),
                );
              },
            ),
          ),
        ),

        // Controls
        Padding(
          padding: const EdgeInsets.only(top: 8.0),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              IconButton(
                icon: Icon(_isPlaying ? Icons.pause : Icons.play_arrow),
                onPressed: _togglePlayPause,
                color: widget.primaryColor,
              ),
              IconButton(
                icon: const Icon(Icons.replay),
                onPressed: _restart,
                color: widget.primaryColor,
              ),
            ],
          ),
        ),
      ],
    );
  }

  CustomPainter _getPatternPainter(
    String patternType,
    double animationValue,
    Color primaryColor,
    Color secondaryColor,
  ) {
    switch (patternType) {
      case 'fibonacci_spiral':
        return FibonacciSpiralPainter(
          animationValue: animationValue,
          primaryColor: primaryColor,
          secondaryColor: secondaryColor,
        );
      case 'fractal_tree':
        return FractalTreePainter(
          animationValue: animationValue,
          primaryColor: primaryColor,
          secondaryColor: secondaryColor,
        );
      case 'sierpinski_triangle':
        return SierpinskiTrianglePainter(
          animationValue: animationValue,
          primaryColor: primaryColor,
          secondaryColor: secondaryColor,
        );
      default:
        return FibonacciSpiralPainter(
          animationValue: animationValue,
          primaryColor: primaryColor,
          secondaryColor: secondaryColor,
        );
    }
  }
}

class FibonacciSpiralPainter extends CustomPainter {
  final double animationValue;
  final Color primaryColor;
  final Color secondaryColor;

  FibonacciSpiralPainter({
    required this.animationValue,
    required this.primaryColor,
    required this.secondaryColor,
  });

  @override
  void paint(Canvas canvas, Size size) {
    final center = Offset(size.width / 2, size.height / 2);
    final maxRadius = math.min(size.width, size.height) * 0.45;

    // Draw Fibonacci spiral
    final paint =
        Paint()
          ..color = primaryColor
          ..style = PaintingStyle.stroke
          ..strokeWidth = 2.0;

    final squarePaint =
        Paint()
          ..color = secondaryColor.withOpacity(0.2)
          ..style = PaintingStyle.fill;

    final borderPaint =
        Paint()
          ..color = secondaryColor
          ..style = PaintingStyle.stroke
          ..strokeWidth = 1.0;

    // Fibonacci sequence
    List<int> fibonacci = [1, 1, 2, 3, 5, 8, 13, 21, 34, 55, 89];

    // Scale factor to fit the spiral within the canvas
    double scaleFactor = maxRadius / fibonacci.last;

    // Starting position and direction
    double x = center.dx;
    double y = center.dy;
    int direction = 0; // 0: right, 1: down, 2: left, 3: up

    // Draw squares and spiral
    Path spiralPath = Path();
    spiralPath.moveTo(x, y);

    int maxSteps = (fibonacci.length * animationValue).floor();
    maxSteps = math.max(1, maxSteps); // Ensure at least one step

    for (int i = 0; i < maxSteps; i++) {
      double size = fibonacci[i] * scaleFactor;

      // Draw square
      Rect square;
      switch (direction) {
        case 0: // right
          square = Rect.fromLTWH(x, y - size, size, size);
          x += size;
          break;
        case 1: // down
          square = Rect.fromLTWH(x - size, y, size, size);
          y += size;
          break;
        case 2: // left
          square = Rect.fromLTWH(x - size, y - size, size, size);
          x -= size;
          break;
        case 3: // up
          square = Rect.fromLTWH(x, y - size, size, size);
          y -= size;
          break;
        default:
          square = Rect.zero;
      }

      canvas.drawRect(square, squarePaint);
      canvas.drawRect(square, borderPaint);

      // Draw arc for spiral
      Rect arcRect;
      double startAngle, sweepAngle;

      switch (direction) {
        case 0: // right to down
          arcRect = Rect.fromLTWH(x - size, y - size, size * 2, size * 2);
          startAngle = math.pi * 1.0;
          sweepAngle = math.pi * 0.5;
          break;
        case 1: // down to left
          arcRect = Rect.fromLTWH(x - size, y - size, size * 2, size * 2);
          startAngle = math.pi * 1.5;
          sweepAngle = math.pi * 0.5;
          break;
        case 2: // left to up
          arcRect = Rect.fromLTWH(x - size, y - size, size * 2, size * 2);
          startAngle = 0;
          sweepAngle = math.pi * 0.5;
          break;
        case 3: // up to right
          arcRect = Rect.fromLTWH(x - size, y - size, size * 2, size * 2);
          startAngle = math.pi * 0.5;
          sweepAngle = math.pi * 0.5;
          break;
        default:
          arcRect = Rect.zero;
          startAngle = 0;
          sweepAngle = 0;
      }

      spiralPath.addArc(arcRect, startAngle, sweepAngle);

      // Change direction (clockwise: right -> down -> left -> up)
      direction = (direction + 1) % 4;
    }

    canvas.drawPath(spiralPath, paint);
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => true;
}

class FractalTreePainter extends CustomPainter {
  final double animationValue;
  final Color primaryColor;
  final Color secondaryColor;
  final int maxDepth = 9;

  FractalTreePainter({
    required this.animationValue,
    required this.primaryColor,
    required this.secondaryColor,
  });

  @override
  void paint(Canvas canvas, Size size) {
    final paint =
        Paint()
          ..color = primaryColor
          ..style = PaintingStyle.stroke
          ..strokeWidth = 2.0
          ..strokeCap = StrokeCap.round;

    // Starting point at the bottom center
    final startX = size.width / 2;
    final startY = size.height * 0.9;

    // Initial branch length
    final branchLength = size.height * 0.3;

    // Calculate current depth based on animation
    final currentDepth = (maxDepth * animationValue).ceil();

    // Draw the tree
    _drawBranch(
      canvas,
      Offset(startX, startY),
      -math.pi / 2, // Start pointing up
      branchLength,
      0,
      currentDepth,
      paint,
    );
  }

  void _drawBranch(
    Canvas canvas,
    Offset start,
    double angle,
    double length,
    int depth,
    int maxDepth,
    Paint paint,
  ) {
    if (depth >= maxDepth) return;

    // Calculate end point
    final endX = start.dx + math.cos(angle) * length;
    final endY = start.dy + math.sin(angle) * length;
    final end = Offset(endX, endY);

    // Adjust color and thickness based on depth
    final depthRatio = depth / maxDepth;
    final color = Color.lerp(primaryColor, secondaryColor, depthRatio)!;
    final thickness = math.max(0.5, 2.0 * (1 - depthRatio));

    paint.color = color;
    paint.strokeWidth = thickness;

    // Draw the branch
    canvas.drawLine(start, end, paint);

    // Calculate new length for child branches
    final newLength = length * 0.7;

    // Draw right branch (smaller angle change)
    _drawBranch(
      canvas,
      end,
      angle - math.pi / 6, // 30 degrees to the right
      newLength,
      depth + 1,
      maxDepth,
      paint,
    );

    // Draw left branch (larger angle change)
    _drawBranch(
      canvas,
      end,
      angle + math.pi / 4, // 45 degrees to the left
      newLength,
      depth + 1,
      maxDepth,
      paint,
    );
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => true;
}

class SierpinskiTrianglePainter extends CustomPainter {
  final double animationValue;
  final Color primaryColor;
  final Color secondaryColor;
  final int maxDepth = 6;

  SierpinskiTrianglePainter({
    required this.animationValue,
    required this.primaryColor,
    required this.secondaryColor,
  });

  @override
  void paint(Canvas canvas, Size size) {
    final paint =
        Paint()
          ..color = primaryColor
          ..style = PaintingStyle.fill;

    // Calculate current depth based on animation
    final currentDepth = (maxDepth * animationValue).ceil();

    // Define the initial triangle
    final p1 = Offset(size.width / 2, size.height * 0.1); // Top
    final p2 = Offset(size.width * 0.1, size.height * 0.9); // Bottom left
    final p3 = Offset(size.width * 0.9, size.height * 0.9); // Bottom right

    // Draw the Sierpinski triangle
    _drawSierpinskiTriangle(canvas, p1, p2, p3, currentDepth, paint);
  }

  void _drawSierpinskiTriangle(
    Canvas canvas,
    Offset p1,
    Offset p2,
    Offset p3,
    int depth,
    Paint paint,
  ) {
    if (depth <= 0) {
      // Draw a filled triangle
      final path =
          Path()
            ..moveTo(p1.dx, p1.dy)
            ..lineTo(p2.dx, p2.dy)
            ..lineTo(p3.dx, p3.dy)
            ..close();

      canvas.drawPath(path, paint);
      return;
    }

    // Calculate midpoints
    final mid1 = Offset(
      (p1.dx + p2.dx) / 2,
      (p1.dy + p2.dy) / 2,
    ); // Between p1 and p2
    final mid2 = Offset(
      (p2.dx + p3.dx) / 2,
      (p2.dy + p3.dy) / 2,
    ); // Between p2 and p3
    final mid3 = Offset(
      (p3.dx + p1.dx) / 2,
      (p3.dy + p1.dy) / 2,
    ); // Between p3 and p1

    // Recursively draw three smaller triangles
    _drawSierpinskiTriangle(canvas, p1, mid1, mid3, depth - 1, paint);
    _drawSierpinskiTriangle(canvas, mid1, p2, mid2, depth - 1, paint);
    _drawSierpinskiTriangle(canvas, mid3, mid2, p3, depth - 1, paint);
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => true;
}
