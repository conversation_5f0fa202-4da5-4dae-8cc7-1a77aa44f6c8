{"id": "information-gathering-reconnaissance", "title": "Information Gathering and Reconnaissance", "description": "Master the techniques for passively and actively collecting information about target systems.", "order": 2, "lessons": [{"id": "passive-reconnaissance-osint", "title": "Passive Reconnaissance: Open Source Intelligence (OSINT)", "description": "Learn to gather publicly available information without directly interacting with the target.", "order": 1, "type": "interactive_lesson", "estimatedTimeMinutes": 12, "contentBlocks": [{"id": "pro-screen1-intro", "type": "lesson_screen", "order": 1, "estimatedTimeSeconds": 60, "content": {"headline": "The Art of Information Gathering: Passive Reconnaissance", "body_md": "Before any ethical hacking engagement, thorough reconnaissance is essential. Passive reconnaissance allows you to gather valuable information about a target without directly interacting with their systems - leaving no traces and raising no alarms.", "visual": {"type": "giphy_search", "value": "research investigation"}, "interactive_element": {"type": "button", "text": "What is passive reconnaissance?"}}}, {"id": "pro-screen2-definition", "type": "lesson_screen", "order": 2, "estimatedTimeSeconds": 90, "content": {"headline": "Passive Reconnaissance Defined", "body_md": "**Passive reconnaissance** is the process of collecting information about a target without directly interacting with their systems or networks.\n\nKey characteristics:\n\n• **Non-intrusive**: Leaves no traces on target systems\n• **Uses public sources**: Relies on publicly available information\n• **Legal**: Generally doesn't violate laws (though ethical authorization is still required)\n• **Undetectable**: Target is unaware of the information gathering\n• **Foundation phase**: Typically the first step in the ethical hacking process\n\nPassive reconnaissance is also known as Open Source Intelligence (OSINT) gathering.", "visual": {"type": "unsplash_search", "value": "research information"}, "interactive_element": {"type": "button", "text": "Types of information gathered"}}}, {"id": "pro-screen3-information-types", "type": "lesson_screen", "order": 3, "estimatedTimeSeconds": 90, "content": {"headline": "What Information Can Be Gathered Passively?", "body_md": "Passive reconnaissance can reveal a surprising amount of information:\n\n• **Domain information**: Domain names, registrar details, expiration dates\n• **Network information**: IP addresses, network blocks, ASNs\n• **Technical details**: Technologies used, server types, CMS platforms\n• **Organizational structure**: Employee information, email formats, job postings\n• **Digital footprint**: Social media presence, public documents, press releases\n• **Security posture**: Security policies, certificates, public security incidents\n• **Business relationships**: Partners, vendors, clients, acquisitions\n\nThis information helps ethical hackers understand the target's attack surface and potential vulnerabilities.", "visual": {"type": "static_text", "value": "Passive Reconnaissance Information Types"}, "interactive_element": {"type": "button", "text": "OSINT techniques and tools"}}}, {"id": "pro-screen4-techniques-tools", "type": "lesson_screen", "order": 4, "estimatedTimeSeconds": 90, "content": {"headline": "OSINT Techniques and Tools", "body_md": "Several techniques and tools can be used for passive reconnaissance:\n\n• **WHOIS lookups**: Domain registration information (registrant, dates, nameservers)\n  - Tools: whois, DomainTools, ICANN Lookup\n\n• **DNS reconnaissance**: Domain records, subdomains, mail servers\n  - Tools: nslookup, dig, DNSdumpster, SecurityTrails\n\n• **Search engine research**: Advanced search operators to find specific information\n  - Tools: Google, Bing, DuckDuckGo with advanced operators\n\n• **Social media analysis**: Employee information, organizational details\n  - Tools: LinkedIn, Twitter, Facebook, Instagram\n\n• **Website analysis**: Technologies, frameworks, CMS platforms\n  - Tools: Wappalyzer, BuiltWith, Netcraft\n\n• **Document metadata**: Information embedded in public documents\n  - Tools: ExifTool, metagoofil\n\n• **Public code repositories**: Source code, API keys, credentials\n  - Tools: GitHub, GitLab, Bitbucket searches", "visual": {"type": "giphy_search", "value": "research tools"}, "interactive_element": {"type": "button", "text": "Example: Domain reconnaissance"}}}, {"id": "pro-screen5-example", "type": "lesson_screen", "order": 5, "estimatedTimeSeconds": 90, "content": {"headline": "Example: Domain Reconnaissance Process", "body_md": "Let's walk through a basic domain reconnaissance process:\n\n1. **Start with WHOIS lookup**:\n   - Identify domain registrar, registration/expiration dates\n   - Note registrant information (if not private)\n   - Identify nameservers\n\n2. **Perform DNS enumeration**:\n   - Identify A records (IP addresses)\n   - Check MX records (mail servers)\n   - Look for TXT records (SPF, DKIM, verification records)\n   - Enumerate subdomains\n\n3. **Analyze website technology**:\n   - Identify web server type and version\n   - Detect CMS platforms and versions\n   - Note JavaScript frameworks and libraries\n   - Check for third-party services and integrations\n\n4. **Search for related information**:\n   - Use Google dorking techniques (e.g., site:domain.com filetype:pdf)\n   - Check for leaked credentials on breach databases\n   - Look for employee information on LinkedIn\n\nAll this information helps build a comprehensive profile of the target.", "visual": {"type": "unsplash_search", "value": "domain website analysis"}, "interactive_element": {"type": "button", "text": "Ethical and legal considerations"}}}, {"id": "pro-screen6-ethical-legal", "type": "lesson_screen", "order": 6, "estimatedTimeSeconds": 90, "content": {"headline": "Ethical and Legal Considerations", "body_md": "Even though passive reconnaissance uses publicly available information, ethical and legal considerations still apply:\n\n• **Authorization**: Always ensure you have explicit permission for the target\n\n• **Scope boundaries**: Stick to the agreed scope of the assessment\n\n• **Privacy respect**: Be mindful of personal information you discover\n\n• **Data handling**: Treat gathered information as confidential\n\n• **Legal compliance**: Some countries have restrictions on certain OSINT activities\n\n• **Documentation**: Keep detailed records of your reconnaissance activities\n\n• **Reporting**: Include only relevant information in your final report\n\nRemember: The goal is to improve security, not to invade privacy or cause harm.", "visual": {"type": "static_text", "value": "Ethical OSINT Guidelines"}, "interactive_element": {"type": "button", "text": "Test your knowledge"}}}, {"id": "pro-screen7-quiz", "type": "lesson_screen", "order": 7, "estimatedTimeSeconds": 60, "content": {"headline": "Passive Reconnaissance Quiz", "body_md": "Let's test your understanding of passive reconnaissance:", "visual": {"type": "static_text", "value": "OSINT Quiz"}, "interactive_element": {"type": "multiple_choice_text", "question_text": "Which of the following is NOT an example of passive reconnaissance?", "options": [{"id": "opt1", "text": "Looking up domain registration information using WHOIS"}, {"id": "opt2", "text": "Scanning a company's network ports using Nmap"}, {"id": "opt3", "text": "Reviewing a company's job postings for technology information"}, {"id": "opt4", "text": "Analyzing the source code of a public website"}], "correct_option_id": "opt2", "feedback_correct": "Correct! Scanning a network with Nmap is active reconnaissance because it involves directly interacting with the target's systems, which could be detected.", "feedback_incorrect": "Scanning a network with Nmap is active reconnaissance because it involves directly interacting with the target's systems, which could be detected. The other options are passive techniques that don't interact with the target's systems."}}}]}, {"id": "active-reconnaissance-network-scanning", "title": "Active Reconnaissance: Network Scanning (Nmap Basics)", "description": "Explore tools for identifying live hosts and services on a network.", "order": 2, "type": "interactive_lesson", "estimatedTimeMinutes": 15, "contentBlocks": [{"id": "arns-screen1-intro", "type": "lesson_screen", "order": 1, "estimatedTimeSeconds": 60, "content": {"headline": "Direct Interaction: Active Reconnaissance", "body_md": "After gathering information passively, ethical hackers move to active reconnaissance - directly interacting with target systems to discover more detailed information about network structure, running services, and potential entry points.", "visual": {"type": "giphy_search", "value": "network scan"}, "interactive_element": {"type": "button", "text": "What is active reconnaissance?"}}}, {"id": "arns-screen2-definition", "type": "lesson_screen", "order": 2, "estimatedTimeSeconds": 90, "content": {"headline": "Active Reconnaissance Defined", "body_md": "**Active reconnaissance** involves directly interacting with target systems to gather information about their structure, services, and potential vulnerabilities.\n\nKey characteristics:\n\n• **Direct interaction**: Sends packets or requests to target systems\n• **Detectable**: Can be logged or detected by security systems\n• **More detailed**: Provides specific technical information\n• **Authorization required**: Must have explicit permission\n• **Potential impact**: Can potentially disrupt services if not careful\n\nActive reconnaissance is a critical phase that bridges information gathering and vulnerability assessment.", "visual": {"type": "unsplash_search", "value": "network security scan"}, "interactive_element": {"type": "button", "text": "Network scanning basics"}}}, {"id": "arns-screen3-network-scanning", "type": "lesson_screen", "order": 3, "estimatedTimeSeconds": 90, "content": {"headline": "Network Scanning: The Foundation of Active Reconnaissance", "body_md": "Network scanning is a fundamental active reconnaissance technique that helps identify:\n\n• **Live hosts**: Which IP addresses have active systems\n• **Open ports**: Which communication ports are accepting connections\n• **Services**: What applications are running on those ports\n• **Operating systems**: What OS the target systems are running\n• **Network topology**: How systems are connected and structured\n• **Firewalls/filters**: What security measures are in place\n\nThis information is essential for understanding the attack surface and identifying potential vulnerabilities.", "visual": {"type": "static_text", "value": "Network Scanning Process"}, "interactive_element": {"type": "button", "text": "Introduction to Nmap"}}}, {"id": "arns-screen4-nmap-intro", "type": "lesson_screen", "order": 4, "estimatedTimeSeconds": 90, "content": {"headline": "Nmap: The Network Mapper", "body_md": "**Nmap** (Network Mapper) is the most popular and powerful network scanning tool in the ethical hacker's toolkit.\n\nKey features of Nmap:\n\n• **Host discovery**: Identify live hosts on a network\n• **Port scanning**: Determine open, closed, and filtered ports\n• **Service detection**: Identify applications running on ports\n• **OS fingerprinting**: Determine operating systems\n• **Scriptable**: Extend functionality with NSE (Nmap Scripting Engine)\n• **Versatile**: Works across all major platforms\n• **Free and open-source**: Continuously improved by the community\n\nNmap has been featured in movies and is considered the \"Swiss Army knife\" of network scanning.", "visual": {"type": "giphy_search", "value": "network scan"}, "interactive_element": {"type": "button", "text": "Basic Nmap scanning techniques"}}}, {"id": "arns-screen5-nmap-techniques", "type": "lesson_screen", "order": 5, "estimatedTimeSeconds": 90, "content": {"headline": "Basic Nmap Scanning Techniques", "body_md": "Here are some fundamental Nmap scanning commands:\n\n• **Basic scan**: `nmap target_ip`\n  - Scans 1000 common ports\n\n• **Scan specific ports**: `nmap -p 80,443,8080 target_ip`\n  - Checks only the specified ports\n\n• **Scan all ports**: `nmap -p- target_ip`\n  - Checks all 65535 ports (time-consuming)\n\n• **Service version detection**: `nmap -sV target_ip`\n  - Identifies service versions running on open ports\n\n• **OS detection**: `nmap -O target_ip`\n  - Attempts to determine the operating system\n\n• **Comprehensive scan**: `nmap -A target_ip`\n  - Enables OS detection, version detection, script scanning, and traceroute\n\n• **Stealthy scan**: `nmap -sS target_ip`\n  - SYN scan, doesn't complete TCP connections\n\nRemember: Only use these techniques on systems you have permission to scan!", "visual": {"type": "static_text", "value": "Nmap Scanning Commands"}, "interactive_element": {"type": "button", "text": "Interpreting Nmap results"}}}, {"id": "arns-screen6-interpreting", "type": "lesson_screen", "order": 6, "estimatedTimeSeconds": 90, "content": {"headline": "Interpreting Nmap Results", "body_md": "Nmap provides detailed output that requires proper interpretation:\n\n• **Port states**:\n  - **Open**: Service is accepting connections\n  - **Closed**: Port is accessible but no service is listening\n  - **Filtered**: Firewall or filter is blocking the port\n  - **Unfiltered**: Port is accessible but state can't be determined\n  - **Open|Filtered**: Nmap can't determine if port is open or filtered\n\n• **Service information**:\n  - Service name and version when using -sV\n  - Potential vulnerabilities based on version\n\n• **OS detection**:\n  - Possible operating systems with confidence levels\n  - System uptime estimates\n\n• **Network information**:\n  - MAC addresses (when scanning locally)\n  - Network hops (with traceroute)\n\nCareful analysis of these results guides the next steps in the ethical hacking process.", "visual": {"type": "unsplash_search", "value": "computer code analysis"}, "interactive_element": {"type": "button", "text": "Ethical considerations"}}}, {"id": "arns-screen7-ethical", "type": "lesson_screen", "order": 7, "estimatedTimeSeconds": 90, "content": {"headline": "Ethical Considerations in Active Scanning", "body_md": "Active scanning requires careful ethical consideration:\n\n• **Explicit authorization**: Always have written permission before scanning\n\n• **Scope limitations**: Only scan IP ranges and systems within scope\n\n• **Timing and rate limiting**: Use slower scans to avoid disruption\n  - Nmap options: `-T0` (slowest) to `-T5` (fastest)\n\n• **Avoid dangerous scans**: Some scan types can crash unstable services\n\n• **Working hours**: Consider scanning during maintenance windows\n\n• **Documentation**: Keep detailed logs of all scanning activities\n\n• **Reporting**: Document all findings professionally\n\nRemember: The goal is to improve security, not to cause disruption or damage.", "visual": {"type": "static_text", "value": "Ethical Scanning Guidelines"}, "interactive_element": {"type": "button", "text": "Test your knowledge"}}}, {"id": "arns-screen8-quiz", "type": "lesson_screen", "order": 8, "estimatedTimeSeconds": 60, "content": {"headline": "Active Reconnaissance Quiz", "body_md": "Let's test your understanding of active reconnaissance:", "visual": {"type": "static_text", "value": "Network Scanning Quiz"}, "interactive_element": {"type": "multiple_choice_text", "question_text": "What does an 'open' port state in Nmap results indicate?", "options": [{"id": "opt1", "text": "The port is blocked by a firewall"}, {"id": "opt2", "text": "The port is accessible but no service is running"}, {"id": "opt3", "text": "The port has a service accepting connections"}, {"id": "opt4", "text": "The port state could not be determined"}], "correct_option_id": "opt3", "feedback_correct": "Correct! An 'open' port state indicates that a service is running on that port and is accepting connections.", "feedback_incorrect": "An 'open' port state indicates that a service is running on that port and is accepting connections. A 'closed' port is accessible but has no service, a 'filtered' port is blocked by a firewall, and 'unfiltered' means the port is accessible but the state can't be determined."}}}]}], "moduleTest": {"id": "information-intelligence-officer-test", "title": "Information Intelligence Officer", "description": "Apply various reconnaissance techniques to gather information about a target.", "type": "interactive_test", "estimatedTimeMinutes": 15, "contentBlocks": [{"id": "iiot-intro", "type": "test_screen_intro", "order": 1, "estimatedTimeSeconds": 30, "content": {"headline": "Test Your Reconnaissance Skills", "body_md": "In this test, you'll demonstrate your understanding of passive and active reconnaissance techniques, tools, and ethical considerations.", "visual": {"type": "unsplash_search", "value": "information gathering"}}}, {"id": "iiot-q1", "type": "question_screen", "order": 2, "estimatedTimeSeconds": 60, "content": {"headline": "Question 1: Passive vs. Active Reconnaissance", "body_md": "Understanding the difference between passive and active reconnaissance is fundamental for ethical hackers.", "visual": {"type": "giphy_search", "value": "research investigation"}, "interactive_element": {"type": "multiple_choice_text", "question_text": "Which of the following activities is considered passive reconnaissance?", "options": [{"id": "opt1", "text": "Using Nmap to scan for open ports"}, {"id": "opt2", "text": "Sending ping requests to determine live hosts"}, {"id": "opt3", "text": "Using WHOIS to look up domain registration information"}, {"id": "opt4", "text": "Running a vulnerability scanner against a web application"}], "correct_option_id": "opt3", "feedback_correct": "Correct! Using WHOIS to look up domain registration information is passive reconnaissance because it doesn't involve direct interaction with the target's systems.", "feedback_incorrect": "Using WHOIS to look up domain registration information is passive reconnaissance because it doesn't involve direct interaction with the target's systems. The other options all involve sending packets directly to the target, making them active reconnaissance."}}}, {"id": "iiot-q2", "type": "question_screen", "order": 3, "estimatedTimeSeconds": 60, "content": {"headline": "Question 2: Nmap Scanning", "body_md": "Nmap is a powerful tool for network reconnaissance with various scanning options.", "visual": {"type": "unsplash_search", "value": "network scan"}, "interactive_element": {"type": "multiple_choice_text", "question_text": "Which Nmap command would you use to perform a comprehensive scan that includes OS detection, version detection, and script scanning?", "options": [{"id": "opt1", "text": "nmap -sS target_ip"}, {"id": "opt2", "text": "nmap -p- target_ip"}, {"id": "opt3", "text": "nmap -A target_ip"}, {"id": "opt4", "text": "nmap -sV target_ip"}], "correct_option_id": "opt3", "feedback_correct": "Correct! The -A option in Nmap enables aggressive scanning, which includes OS detection (-O), version detection (-sV), script scanning (-sC), and traceroute.", "feedback_incorrect": "The -A option in Nmap enables aggressive scanning, which includes OS detection (-O), version detection (-sV), script scanning (-sC), and traceroute. The other options perform more limited scans."}}}, {"id": "iiot-q3", "type": "question_screen", "order": 4, "estimatedTimeSeconds": 60, "content": {"headline": "Question 3: Ethical Considerations", "body_md": "Ethical considerations are paramount when conducting reconnaissance activities.", "visual": {"type": "giphy_search", "value": "ethics security"}, "interactive_element": {"type": "multiple_choice_text", "question_text": "What is the most important requirement before conducting active reconnaissance on a target?", "options": [{"id": "opt1", "text": "Having advanced scanning tools"}, {"id": "opt2", "text": "Ensuring your IP address is hidden"}, {"id": "opt3", "text": "Having explicit written authorization from the target owner"}, {"id": "opt4", "text": "Testing your tools on your own systems first"}], "correct_option_id": "opt3", "feedback_correct": "Correct! Having explicit written authorization from the target owner is the most important ethical and legal requirement before conducting any active reconnaissance.", "feedback_incorrect": "Having explicit written authorization from the target owner is the most important ethical and legal requirement before conducting any active reconnaissance. Without proper authorization, such activities could be illegal and unethical."}}}]}}