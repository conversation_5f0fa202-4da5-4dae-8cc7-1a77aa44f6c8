class InteractiveWidgetModel {
  final String id;
  final String name;
  final String type;
  final String category;
  final String description;
  final Map<String, dynamic> data;
  final bool isImplemented;

  InteractiveWidgetModel({
    required this.id,
    required this.name,
    required this.type,
    required this.category,
    required this.description,
    required this.data,
    this.isImplemented = false,
  });

  factory InteractiveWidgetModel.fromJson(Map<String, dynamic> json) {
    return InteractiveWidgetModel(
      id: json['id'],
      name: json['name'],
      type: json['type'],
      category: json['category'],
      description: json['description'],
      data: json['data'] ?? {},
      isImplemented: json['isImplemented'] ?? false,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'type': type,
      'category': category,
      'description': description,
      'data': data,
      'isImplemented': isImplemented,
    };
  }
}
