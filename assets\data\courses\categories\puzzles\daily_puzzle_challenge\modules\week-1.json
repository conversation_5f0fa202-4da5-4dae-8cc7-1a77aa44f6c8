{"id": "week-1", "title": "Week 1: Logic & Pattern Puzzles", "description": "Dive into a collection of logic grid puzzles, lateral thinking challenges, mathematical brain teasers, and visual pattern puzzles.", "order": 1, "lessons": [{"id": "logic-grid-puzzle", "title": "Logic Grid Puzzle: Who Owns the Fish?", "description": "Solve a classic logic grid puzzle using deductive reasoning.", "order": 1, "type": "interactive_lesson", "estimatedTimeMinutes": 15, "contentBlocks": [{"id": "lgp-screen1-intro", "type": "lesson_screen", "order": 1, "estimatedTimeSeconds": 45, "content": {"headline": "The Einstein Puzzle Challenge", "body_md": "Legend has it that <PERSON> created this puzzle and claimed that 98% of the world's population couldn't solve it. Let's see if you can!\n\nIn this puzzle, you'll use clues to determine who lives in which house, what they drink, what pet they own, and more.", "visual": {"type": "giphy_search", "value": "<PERSON><PERSON>tein thinking puzzle"}, "interactive_element": {"type": "button", "text": "I'm Ready to Solve It!", "action": "next_screen"}}}, {"id": "lgp-screen2-setup", "type": "lesson_screen", "order": 2, "estimatedTimeSeconds": 90, "content": {"headline": "The Setup", "body_md": "There are 5 houses in 5 different colors. In each house lives a person of a different nationality. These 5 owners drink a certain beverage, smoke a certain brand of cigar, and keep a certain pet. **No owner has the same pet, smokes the same brand of cigar, or drinks the same beverage as any other owner.**\n\nThe question is: **Who owns the fish?**", "visual": {"type": "unsplash_search", "value": "colorful houses in a row"}, "interactive_element": {"type": "button", "text": "Show Me the Clues", "action": "next_screen"}}}, {"id": "lgp-screen3-clues", "type": "lesson_screen", "order": 3, "estimatedTimeSeconds": 120, "content": {"headline": "The Clues", "body_md": "1. The <PERSON>rit lives in the red house.\n2. The Swede keeps dogs as pets.\n3. The Dane drinks tea.\n4. The green house is on the left of the white house.\n5. The green house owner drinks coffee.\n6. The person who smokes Pall Mall keeps birds.\n7. The owner of the yellow house smokes Dunhill.\n8. The man living in the center house drinks milk.\n9. The Norwegian lives in the first house.\n10. The man who smokes <PERSON><PERSON><PERSON> lives next to the one who keeps cats.\n11. The man who keeps horses lives next to the man who smokes Dunhill.\n12. The owner who smokes Blue Master drinks beer.\n13. The German smokes Prince.\n14. The Norwegian lives next to the blue house.\n15. The man who smokes <PERSON><PERSON><PERSON> has a neighbor who drinks water.", "interactive_element": {"type": "button", "text": "Let's Start Solving", "action": "next_screen"}}}, {"id": "lgp-screen4-interactive", "type": "lesson_screen", "order": 4, "estimatedTimeSeconds": 300, "content": {"headline": "Solve the Puzzle", "body_md": "Use the clues to fill in the grid. Think step by step and eliminate possibilities as you go.\n\nHint: Start with the Norwegian living in the first house and work from there.", "interactive_element": {"type": "deductionPuzzle", "grid_categories": [{"name": "House", "values": ["1 (First)", "2", "3 (Center)", "4", "5 (Last)"]}, {"name": "Color", "values": ["Red", "Green", "Yellow", "Blue", "White"]}, {"name": "Nationality", "values": ["<PERSON><PERSON>", "Swede", "<PERSON>", "Norwegian", "German"]}, {"name": "Drink", "values": ["Tea", "Coffee", "Milk", "Beer", "Water"]}, {"name": "Cigar", "values": ["Pall Mall", "Dunhill", "Blends", "Blue Master", "Prince"]}, {"name": "Pet", "values": ["Dogs", "Birds", "Cats", "Horses", "Fish"]}], "solution": {"1": {"House": "1 (First)", "Color": "Yellow", "Nationality": "Norwegian", "Drink": "Water", "Cigar": "Dunhill", "Pet": "Cats"}, "2": {"House": "2", "Color": "Blue", "Nationality": "<PERSON>", "Drink": "Tea", "Cigar": "Blends", "Pet": "Horses"}, "3": {"House": "3 (Center)", "Color": "Red", "Nationality": "<PERSON><PERSON>", "Drink": "Milk", "Cigar": "Pall Mall", "Pet": "Birds"}, "4": {"House": "4", "Color": "Green", "Nationality": "German", "Drink": "Coffee", "Cigar": "Prince", "Pet": "Fish"}, "5": {"House": "5 (Last)", "Color": "White", "Nationality": "Swede", "Drink": "Beer", "Cigar": "Blue Master", "Pet": "Dogs"}}, "feedback_correct": "Excellent deduction! You've solved <PERSON>'s puzzle!", "feedback_incorrect": "Not quite right. Try reviewing the clues again and think step by step.", "action_button_text": "Check My Solution"}}}, {"id": "lgp-screen5-solution", "type": "lesson_screen", "order": 5, "estimatedTimeSeconds": 90, "content": {"headline": "The Solution", "body_md": "Here's the complete solution:\n\n**House 1:** Norwegian, Yellow, Water, Dunhill, Cats\n**House 2:** <PERSON>, <PERSON>, Tea, Blends, Horses\n**House 3:** <PERSON>rit, Red, Milk, Pall Mall, Birds\n**House 4:** German, Green, Coffee, Prince, Fish\n**House 5:** Swede, <PERSON>, <PERSON>, Blue Master, Dogs\n\nSo, the **German** owns the fish!", "visual": {"type": "giphy_search", "value": "goldfish bowl"}, "interactive_element": {"type": "button", "text": "Next Puzzle", "action": "next_lesson"}}}]}, {"id": "lateral-thinking-puzzle", "title": "Lateral Thinking Puzzle: The Hanging Man", "description": "Think outside the box to solve this intriguing scenario.", "order": 2, "type": "interactive_lesson", "estimatedTimeMinutes": 10, "contentBlocks": [{"id": "ltp-screen1-intro", "type": "lesson_screen", "order": 1, "estimatedTimeSeconds": 45, "content": {"headline": "Lateral Thinking: Beyond the Obvious", "body_md": "Lateral thinking puzzles require you to think creatively and consider explanations that aren't immediately obvious. These puzzles often present a strange scenario with seemingly missing information, and you need to figure out what happened.", "visual": {"type": "unsplash_search", "value": "thinking outside box creativity"}, "interactive_element": {"type": "button", "text": "Show Me a Puzzle!", "action": "next_screen"}}}, {"id": "ltp-screen2-puzzle", "type": "lesson_screen", "order": 2, "estimatedTimeSeconds": 120, "content": {"headline": "The Hanging Man Puzzle", "body_md": "A man is found hanging in a locked room with no furniture except for a puddle of water beneath his feet. The room has no windows and only one door, which was locked from the inside. How did he die?", "visual": {"type": "giphy_search", "value": "mystery puzzle"}, "interactive_element": {"type": "multiple_choice_text", "question_text": "What's your theory about what happened?", "options": [{"id": "ltp2opt1", "text": "He was murdered and the killer escaped through a secret passage.", "is_correct": false, "feedback_incorrect": "There's no evidence of a secret passage, and the room was locked from the inside."}, {"id": "ltp2opt2", "text": "He stood on a block of ice which melted, leaving the puddle of water.", "is_correct": true, "feedback_correct": "Correct! He stood on a block of ice to hang himself. After he died, the ice melted, leaving only the puddle of water.", "feedback_incorrect": "Think about what the puddle of water could have been before it was water."}, {"id": "ltp2opt3", "text": "He was already dead when placed in the room by someone else.", "is_correct": false, "feedback_incorrect": "But how would the door be locked from the inside?"}, {"id": "ltp2opt4", "text": "He died of natural causes that made it look like hanging.", "is_correct": false, "feedback_incorrect": "This doesn't explain the hanging position or the puddle of water."}], "action_button_text": "Next Puzzle"}}}, {"id": "ltp-screen3-explanation", "type": "lesson_screen", "order": 3, "estimatedTimeSeconds": 60, "content": {"headline": "The Solution Explained", "body_md": "The man committed suicide by standing on a block of ice and hanging himself. After he died, the ice melted, leaving only the puddle of water beneath his feet.\n\nThis puzzle requires you to think about what the puddle of water could have been before it melted - a key insight that isn't directly stated in the puzzle.", "visual": {"type": "giphy_search", "value": "melting ice block"}, "interactive_element": {"type": "button", "text": "Next Puzzle", "action": "next_lesson"}}}]}, {"id": "mathematical-brain-teaser", "title": "Mathematical Brain Teaser: The Missing Dollar", "description": "Solve a classic mathematical puzzle that challenges your logical thinking.", "order": 3, "type": "interactive_lesson", "estimatedTimeMinutes": 10, "contentBlocks": [{"id": "mbt-screen1-intro", "type": "lesson_screen", "order": 1, "estimatedTimeSeconds": 45, "content": {"headline": "Mathematical Brain Teasers", "body_md": "Mathematical brain teasers often involve scenarios where the math seems to work out incorrectly. These puzzles test your ability to spot logical fallacies and think critically about mathematical operations.", "visual": {"type": "unsplash_search", "value": "math puzzle numbers"}, "interactive_element": {"type": "button", "text": "Show Me the Puzzle!", "action": "next_screen"}}}, {"id": "mbt-screen2-puzzle", "type": "lesson_screen", "order": 2, "estimatedTimeSeconds": 120, "content": {"headline": "The Missing Dollar Puzzle", "body_md": "Three friends check into a hotel. The clerk says the bill is $30, so each guest pays $10. Later, the clerk realizes the bill should only have been $25. The clerk gives $5 to the bellhop to return to the guests.\n\nOn the way, the bellhop realizes that $5 would be difficult to split among three people, so he pockets $2 and gives $1 back to each guest.\n\nNow, each guest has paid $9, for a total of $27. The bellhop has $2. $27 + $2 = $29. Where is the missing dollar?", "visual": {"type": "giphy_search", "value": "money puzzle"}, "interactive_element": {"type": "multiple_choice_text", "question_text": "What's the explanation for the 'missing' dollar?", "options": [{"id": "mbt2opt1", "text": "The bellhop stole it.", "is_correct": false, "feedback_incorrect": "The bellhop took $2, which is accounted for in the $29."}, {"id": "mbt2opt2", "text": "It's a mathematical error in how the problem is framed.", "is_correct": true, "feedback_correct": "Correct! The calculation mixes up what was paid and what was returned. The guests paid $27 total, of which $25 went to the hotel and $2 to the bellhop. There is no missing dollar.", "feedback_incorrect": "Think about whether the addition of $27 and $2 is the right calculation to make."}, {"id": "mbt2opt3", "text": "The clerk kept it.", "is_correct": false, "feedback_incorrect": "The clerk gave all $5 to the bellhop."}, {"id": "mbt2opt4", "text": "It was never there - the original bill was miscalculated.", "is_correct": false, "feedback_incorrect": "The original bill was indeed wrong, but that doesn't explain the 'missing' dollar in the final calculation."}], "action_button_text": "Explain It To Me"}}}, {"id": "mbt-screen3-explanation", "type": "lesson_screen", "order": 3, "estimatedTimeSeconds": 90, "content": {"headline": "The Solution Explained", "body_md": "This puzzle is tricky because it frames the problem incorrectly. Let's break it down properly:\n\n1. Guests initially paid: $30\n2. Actual hotel bill: $25\n3. Refund: $5 (given to bellhop)\n4. Bellhop keeps: $2\n5. Bellhop returns to guests: $3 ($1 each)\n\nSo the guests paid $27 total ($30 - $3). Of this $27:\n- $25 went to the hotel\n- $2 went to the bellhop\n\n$25 + $2 = $27. Everything adds up!\n\nThe mistake in the puzzle is adding what the guests paid AFTER the refund ($27) to what the bellhop kept ($2). These aren't separate amounts to be added - the $2 is already part of the $27!", "visual": {"type": "giphy_search", "value": "math solution"}, "interactive_element": {"type": "button", "text": "Next Puzzle", "action": "next_lesson"}}}]}, {"id": "visual-pattern-puzzle", "title": "Visual Pattern Puzzle: What Comes Next?", "description": "Test your pattern recognition skills with visual sequences.", "order": 4, "type": "interactive_lesson", "estimatedTimeMinutes": 12, "contentBlocks": [{"id": "vpp-screen1-intro", "type": "lesson_screen", "order": 1, "estimatedTimeSeconds": 45, "content": {"headline": "Visual Pattern Recognition", "body_md": "Visual pattern puzzles test your ability to identify rules and relationships in sequences of shapes, symbols, or figures. These puzzles are excellent for developing your pattern recognition skills, which are crucial for many areas of problem-solving.", "visual": {"type": "unsplash_search", "value": "pattern geometric sequence"}, "interactive_element": {"type": "button", "text": "Show Me the Puzzles!", "action": "next_screen"}}}, {"id": "vpp-screen2-puzzle1", "type": "lesson_screen", "order": 2, "estimatedTimeSeconds": 90, "content": {"headline": "Puzzle 1: <PERSON><PERSON><PERSON> S<PERSON><PERSON>", "body_md": "Look at the sequence of shapes below. What comes next in the pattern?", "visual": {"type": "local_asset", "value": "assets/images/puzzles/rotating_triangle_sequence.png"}, "interactive_element": {"type": "multiple_choice_image_element", "question_text": "Which shape should come next?", "options": [{"id": "vpp2opt1", "image_path": "assets/images/puzzles/triangle_option_a.png", "is_correct": false}, {"id": "vpp2opt2", "image_path": "assets/images/puzzles/triangle_option_b.png", "is_correct": true}, {"id": "vpp2opt3", "image_path": "assets/images/puzzles/triangle_option_c.png", "is_correct": false}, {"id": "vpp2opt4", "image_path": "assets/images/puzzles/triangle_option_d.png", "is_correct": false}], "feedback_correct": "Correct! The triangle rotates 45 degrees clockwise in each step.", "feedback_incorrect": "Look carefully at how the triangle rotates from one image to the next.", "action_button_text": "Next Pattern"}}}, {"id": "vpp-screen3-puzzle2", "type": "lesson_screen", "order": 3, "estimatedTimeSeconds": 90, "content": {"headline": "Puzzle 2: Growing Dots", "body_md": "Observe the pattern of dots below. How many dots should be in the next figure?", "visual": {"type": "local_asset", "value": "assets/images/puzzles/dot_pattern_sequence.png"}, "interactive_element": {"type": "text_input", "question_text": "How many dots should be in the next figure?", "correct_answer_regex": "^16$", "feedback_correct": "Correct! The number of dots follows the pattern of perfect squares: 1, 4, 9, 16...", "feedback_incorrect": "Look at the total number of dots in each figure. What mathematical sequence might they follow?", "action_button_text": "One More Pattern"}}}, {"id": "vpp-screen4-puzzle3", "type": "lesson_screen", "order": 4, "estimatedTimeSeconds": 120, "content": {"headline": "Puzzle 3: Symbol Sequence", "body_md": "Study the sequence of symbols below. What symbol comes next?", "visual": {"type": "local_asset", "value": "assets/images/puzzles/symbol_sequence.png"}, "interactive_element": {"type": "multiple_choice_image_element", "question_text": "Which symbol should come next?", "options": [{"id": "vpp4opt1", "image_path": "assets/images/puzzles/symbol_option_a.png", "is_correct": false}, {"id": "vpp4opt2", "image_path": "assets/images/puzzles/symbol_option_b.png", "is_correct": false}, {"id": "vpp4opt3", "image_path": "assets/images/puzzles/symbol_option_c.png", "is_correct": true}, {"id": "vpp4opt4", "image_path": "assets/images/puzzles/symbol_option_d.png", "is_correct": false}], "feedback_correct": "Correct! The pattern combines two alternating sequences: one for the outer shape (circle, square, triangle, repeat) and one for the inner symbol (star, cross, dot, repeat).", "feedback_incorrect": "There are two patterns happening simultaneously - one for the outer shape and one for the inner symbol.", "action_button_text": "Complete Week 1!"}}}, {"id": "vpp-screen5-conclusion", "type": "lesson_screen", "order": 5, "estimatedTimeSeconds": 45, "content": {"headline": "Pattern Recognition Mastered!", "body_md": "Great job working through these visual pattern puzzles! Pattern recognition is a fundamental skill that helps with problem-solving in many areas, from mathematics to science to everyday life.\n\nYou've completed all the puzzles for Week 1. Check back soon for Week 2's challenges!", "visual": {"type": "giphy_search", "value": "celebration puzzle solved"}, "interactive_element": {"type": "button", "text": "Back to Course Overview", "action": "next_lesson"}}}]}]}