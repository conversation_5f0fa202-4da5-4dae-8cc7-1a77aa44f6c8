# Interactive Widgets

This directory contains the implementation of all interactive widgets used in the app. These widgets are designed to be reusable components that can be integrated into courses and lessons.

## Directory Structure

- `interactive_widgets_showcase.dart`: Main screen to showcase all interactive widgets
- `interactive_widgets_tracking.md`: Tracking file for implementation status
- `widgets/`: Directory containing individual widget implementations
  - `gif_player_widget.dart`: GIF player widget
  - `multiple_choice_widget.dart`: Multiple choice quiz widget
  - `interactive_diagram_widget.dart`: Interactive diagram widget
  - (more to be added)

## How to Add a New Widget

1. Create a new widget file in the `widgets/` directory
2. Implement the widget using the `InteractiveWidgetModel` for configuration
3. Add the widget to the `_buildWidgetPreview` method in `interactive_widgets_showcase.dart`
4. Add sample data for the widget in `interactive_widget_service.dart`
5. Update the tracking file `interactive_widgets_tracking.md`

## Widget Guidelines

- All widgets should be self-contained and not depend on external state
- Widgets should be configurable through the `InteractiveWidgetModel.data` map
- Widgets should handle loading states and errors gracefully
- Widgets should provide clear feedback to the user
- Widgets should be accessible and work on different screen sizes

## Testing Widgets

The `interactive_widgets_showcase.dart` screen provides a way to test all widgets in isolation before integrating them into courses. Use this screen to verify that widgets work as expected and to showcase them to stakeholders.
