import 'package:flutter/material.dart';

/// A widget that allows users to build and validate syllogisms
class InteractiveSyllogismBuilderWidget extends StatefulWidget {
  /// The data for this widget
  final Map<String, dynamic> data;

  /// Optional callback when the widget state changes
  final Function(bool isCompleted)? onStateChanged;

  const InteractiveSyllogismBuilderWidget({
    Key? key,
    required this.data,
    this.onStateChanged,
  }) : super(key: key);

  /// Factory constructor to create the widget from JSON data
  factory InteractiveSyllogismBuilderWidget.fromData(Map<String, dynamic> data) {
    return InteractiveSyllogismBuilderWidget(
      data: data,
    );
  }

  @override
  State<InteractiveSyllogismBuilderWidget> createState() => _InteractiveSyllogismBuilderWidgetState();
}

class _InteractiveSyllogismBuilderWidgetState extends State<InteractiveSyllogismBuilderWidget> {
  // State variables
  bool _isCompleted = false;
  late String _title;
  late List<SyllogismPart> _majorPremiseOptions;
  late List<SyllogismPart> _minorPremiseOptions;
  late List<SyllogismPart> _conclusionOptions;
  
  int? _selectedMajorPremiseIndex;
  int? _selectedMinorPremiseIndex;
  int? _selectedConclusionIndex;
  
  bool _showFeedback = false;
  bool _isValid = false;
  late String _validFeedback;
  late String _invalidFeedback;
  late List<int> _correctCombination;

  @override
  void initState() {
    super.initState();
    
    // Initialize from data
    _title = widget.data['title'] ?? 'Syllogism Builder';
    _validFeedback = widget.data['valid_feedback'] ?? 'Correct! This is a valid syllogism.';
    _invalidFeedback = widget.data['invalid_feedback'] ?? 'This syllogism is not valid. Try again!';
    _correctCombination = List<int>.from(widget.data['correct_combination'] ?? [0, 0, 0]);
    
    // Load syllogism parts
    _majorPremiseOptions = _loadSyllogismParts(widget.data['major_premise_options'] ?? []);
    _minorPremiseOptions = _loadSyllogismParts(widget.data['minor_premise_options'] ?? []);
    _conclusionOptions = _loadSyllogismParts(widget.data['conclusion_options'] ?? []);
  }

  /// Loads syllogism parts from JSON data
  List<SyllogismPart> _loadSyllogismParts(List<dynamic> parts) {
    return parts.map((part) => SyllogismPart.fromJson(part)).toList();
  }

  /// Checks if the current syllogism is valid
  void _checkSyllogism() {
    // Ensure all parts are selected
    if (_selectedMajorPremiseIndex == null || 
        _selectedMinorPremiseIndex == null || 
        _selectedConclusionIndex == null) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('Please select all parts of the syllogism'),
          backgroundColor: Colors.orange,
        ),
      );
      return;
    }
    
    // Check if the selected combination matches the correct one
    _isValid = _selectedMajorPremiseIndex == _correctCombination[0] &&
               _selectedMinorPremiseIndex == _correctCombination[1] &&
               _selectedConclusionIndex == _correctCombination[2];
    
    setState(() {
      _showFeedback = true;
      _isCompleted = _isValid;
    });
    
    if (widget.onStateChanged != null) {
      widget.onStateChanged!(_isCompleted);
    }
  }

  /// Resets the syllogism builder
  void _resetSyllogism() {
    setState(() {
      _selectedMajorPremiseIndex = null;
      _selectedMinorPremiseIndex = null;
      _selectedConclusionIndex = null;
      _showFeedback = false;
      _isCompleted = false;
    });
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withOpacity(0.2),
            spreadRadius: 1,
            blurRadius: 3,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Title
          Text(
            _title,
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
              color: Colors.blue[800],
            ),
          ),
          const SizedBox(height: 16),
          
          // Instructions
          Container(
            padding: const EdgeInsets.all(12),
            decoration: BoxDecoration(
              color: Colors.blue[50],
              borderRadius: BorderRadius.circular(8),
              border: Border.all(color: Colors.blue[200]!),
            ),
            child: const Text(
              'Build a valid syllogism by selecting a major premise, minor premise, and conclusion.',
              style: TextStyle(
                fontSize: 14,
              ),
            ),
          ),
          const SizedBox(height: 24),
          
          // Major Premise
          _buildSyllogismPartSelector(
            'Major Premise:',
            _majorPremiseOptions,
            _selectedMajorPremiseIndex,
            (index) {
              if (!_showFeedback) {
                setState(() {
                  _selectedMajorPremiseIndex = index;
                });
              }
            },
          ),
          const SizedBox(height: 16),
          
          // Minor Premise
          _buildSyllogismPartSelector(
            'Minor Premise:',
            _minorPremiseOptions,
            _selectedMinorPremiseIndex,
            (index) {
              if (!_showFeedback) {
                setState(() {
                  _selectedMinorPremiseIndex = index;
                });
              }
            },
          ),
          const SizedBox(height: 16),
          
          // Conclusion
          _buildSyllogismPartSelector(
            'Conclusion:',
            _conclusionOptions,
            _selectedConclusionIndex,
            (index) {
              if (!_showFeedback) {
                setState(() {
                  _selectedConclusionIndex = index;
                });
              }
            },
          ),
          const SizedBox(height: 24),
          
          // Action buttons
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              // Reset button
              ElevatedButton.icon(
                onPressed: _resetSyllogism,
                icon: const Icon(Icons.refresh),
                label: const Text('Reset'),
                style: ElevatedButton.styleFrom(
                  backgroundColor: Colors.grey[300],
                  foregroundColor: Colors.black87,
                ),
              ),
              
              // Check button
              if (!_showFeedback)
                ElevatedButton.icon(
                  onPressed: _checkSyllogism,
                  icon: const Icon(Icons.check),
                  label: const Text('Check Syllogism'),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Colors.blue,
                    foregroundColor: Colors.white,
                  ),
                ),
            ],
          ),
          
          // Feedback
          if (_showFeedback) ...[
            const SizedBox(height: 24),
            Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: _isValid ? Colors.green[50] : Colors.red[50],
                borderRadius: BorderRadius.circular(8),
                border: Border.all(
                  color: _isValid ? Colors.green[300]! : Colors.red[300]!,
                ),
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    _isValid ? 'Valid Syllogism!' : 'Invalid Syllogism',
                    style: TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.bold,
                      color: _isValid ? Colors.green[700] : Colors.red[700],
                    ),
                  ),
                  const SizedBox(height: 8),
                  Text(
                    _isValid ? _validFeedback : _invalidFeedback,
                  ),
                  if (_isValid) ...[
                    const SizedBox(height: 16),
                    const Text(
                      'This syllogism follows the correct logical form:',
                      style: TextStyle(fontWeight: FontWeight.bold),
                    ),
                    const SizedBox(height: 8),
                    Text(
                      '• Major Premise: ${_majorPremiseOptions[_selectedMajorPremiseIndex!].text}',
                      style: const TextStyle(fontStyle: FontStyle.italic),
                    ),
                    const SizedBox(height: 4),
                    Text(
                      '• Minor Premise: ${_minorPremiseOptions[_selectedMinorPremiseIndex!].text}',
                      style: const TextStyle(fontStyle: FontStyle.italic),
                    ),
                    const SizedBox(height: 4),
                    Text(
                      '• Conclusion: ${_conclusionOptions[_selectedConclusionIndex!].text}',
                      style: const TextStyle(fontStyle: FontStyle.italic),
                    ),
                  ],
                ],
              ),
            ),
          ],
          
          // Widget name tag at the bottom
          if (widget.data['showNameTag'] ?? true)
            Padding(
              padding: const EdgeInsets.only(top: 8),
              child: Text(
                'InteractiveSyllogismBuilderWidget',
                style: TextStyle(
                  color: Colors.grey[600],
                  fontSize: 12,
                  fontStyle: FontStyle.italic,
                ),
              ),
            ),
        ],
      ),
    );
  }

  /// Builds a selector for a syllogism part
  Widget _buildSyllogismPartSelector(
    String label,
    List<SyllogismPart> options,
    int? selectedIndex,
    Function(int) onSelect,
  ) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          label,
          style: const TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.bold,
          ),
        ),
        const SizedBox(height: 8),
        Container(
          decoration: BoxDecoration(
            border: Border.all(color: Colors.grey[300]!),
            borderRadius: BorderRadius.circular(8),
          ),
          child: Column(
            children: options.asMap().entries.map((entry) {
              final index = entry.key;
              final option = entry.value;
              final isSelected = selectedIndex == index;
              
              return InkWell(
                onTap: () => onSelect(index),
                child: Container(
                  padding: const EdgeInsets.symmetric(vertical: 12, horizontal: 16),
                  decoration: BoxDecoration(
                    color: isSelected ? Colors.blue[50] : Colors.white,
                    border: Border(
                      bottom: index < options.length - 1
                          ? BorderSide(color: Colors.grey[300]!)
                          : BorderSide.none,
                    ),
                  ),
                  child: Row(
                    children: [
                      Icon(
                        isSelected ? Icons.radio_button_checked : Icons.radio_button_unchecked,
                        color: isSelected ? Colors.blue : Colors.grey,
                        size: 20,
                      ),
                      const SizedBox(width: 12),
                      Expanded(
                        child: Text(
                          option.text,
                          style: TextStyle(
                            fontWeight: isSelected ? FontWeight.bold : FontWeight.normal,
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
              );
            }).toList(),
          ),
        ),
      ],
    );
  }
}

/// Represents a part of a syllogism (major premise, minor premise, or conclusion)
class SyllogismPart {
  final String text;
  final String? explanation;

  SyllogismPart({
    required this.text,
    this.explanation,
  });

  factory SyllogismPart.fromJson(Map<String, dynamic> json) {
    return SyllogismPart(
      text: json['text'] ?? '',
      explanation: json['explanation'],
    );
  }
}
