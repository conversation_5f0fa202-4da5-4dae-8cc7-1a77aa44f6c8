{"id": "number-sense-and-intuition", "title": "Number Sense and Intuition", "description": "Develop a deep understanding of numbers, their properties, and relationships.", "order": 2, "lessons": [{"id": "playing-with-primes", "title": "Playing with Primes", "description": "Investigate the building blocks of numbers and their unique behavior.", "order": 1, "type": "interactive_lesson", "estimatedTimeMinutes": 10, "contentBlocks": [{"id": "pwp-screen1-intro", "type": "lesson_screen", "order": 1, "estimatedTimeSeconds": 30, "content": {"headline": "The Building Blocks: Prime Numbers!", "body_md": "What makes the number 7 special? Or 13? Or 29? They're prime numbers – the fundamental atoms of the number world! These special numbers have fascinated mathematicians for thousands of years.", "visual": {"type": "giphy_search", "value": "building blocks numbers"}, "interactive_element": {"type": "interactive_pattern_gallery", "data": {"title": "Prime Numbers Pattern", "description": "Tap on the prime numbers below:", "numbers": [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15], "correctPattern": [2, 3, 5, 7, 11, 13], "feedbackCorrect": "Great job! You found all the primes!", "feedbackIncorrect": "Not quite. Remember, prime numbers have exactly two factors: 1 and themselves.", "showNameTag": false}, "action_button_text": "What are primes?"}}}, {"id": "pwp-screen2-definition", "type": "lesson_screen", "order": 2, "estimatedTimeSeconds": 60, "content": {"headline": "Defining Primes", "body_md": "A **prime number** is a whole number greater than 1 that has exactly two distinct positive divisors: 1 and itself.\n\nExamples: 2, 3, 5, 7, 11...\n\nNumbers that have more than two divisors are called **composite numbers** (e.g., 4, 6, 8, 9...). The number 1 is neither prime nor composite.", "visual": {"type": "unsplash_search", "value": "prime numbers"}, "interactive_element": {"type": "multiple_choice_text", "question_text": "Is the number 4 prime or composite?", "options": [{"id": "pwp2opt1", "text": "Prime", "is_correct": false, "feedback_incorrect": "Think about the divisors of 4. Besides 1 and 4, is there anything else?"}, {"id": "pwp2opt2", "text": "Composite", "is_correct": true, "feedback_correct": "Correct! 4 can be divided by 1, 2, and 4, so it has more than two divisors.", "feedback_incorrect": "A prime has *only* 1 and itself as divisors."}], "action_button_text": "Continue"}}}, {"id": "pwp-screen3-prime-factorization", "type": "lesson_screen", "order": 3, "estimatedTimeSeconds": 60, "content": {"headline": "Prime Factorization", "body_md": "Every composite number can be broken down into a unique combination of prime numbers multiplied together. This is called **prime factorization**.\n\nFor example:\n- 6 = 2 × 3\n- 12 = 2 × 2 × 3\n- 15 = 3 × 5", "interactive_element": {"type": "interactive_number_sequence", "data": {"title": "Prime Factorization Challenge", "description": "Find the prime factorization of 18:", "options": ["2 × 3 × 3", "3 × 6", "2 × 9", "3 × 3 × 2"], "correctAnswer": "2 × 3 × 3", "feedbackCorrect": "Excellent! 18 = 2 × 9 = 2 × 3 × 3", "feedbackIncorrect": "Try again. Break it down step by step: 18 = 2 × 9, and 9 = 3 × 3", "showNameTag": false}, "action_button_text": "Finding Primes"}}}, {"id": "pwp-screen4-sieve-intro", "type": "lesson_screen", "order": 4, "estimatedTimeSeconds": 40, "content": {"headline": "Finding Primes: The Sieve", "body_md": "How do we find prime numbers efficiently? An ancient Greek mathematician named <PERSON><PERSON><PERSON><PERSON> came up with a clever method called the **Sieve of Eratosthenes** around 240 BCE. It's still one of the most efficient ways to find all primes up to a certain limit!", "visual": {"type": "unsplash_search", "value": "ancient greek mathematician"}, "interactive_element": {"type": "button", "text": "Show me the Sieve!", "action": "next_screen"}}}, {"id": "pwp-screen5-sieve-interactive", "type": "lesson_screen", "order": 5, "estimatedTimeSeconds": 120, "content": {"headline": "Sieve of Eratosthenes", "body_md": "Let's find primes up to 30!\n1. Start with 2 (it's prime!). Cross out all multiples of 2 (4, 6, 8...). \n2. Move to the next uncrossed number (3, it's prime!). Cross out all multiples of 3 (6, 9, 12...). \n3. Continue this process with each new prime you find.", "interactive_element": {"type": "mini_game_widget", "data": {"title": "Sieve of Eratosthenes", "description": "Click on numbers to cross them out following the sieve method. Start with crossing out multiples of 2, then 3, and so on.", "gameType": "sieve", "numbers": [2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30], "primes": [2, 3, 5, 7, 11, 13, 17, 19, 23, 29], "feedbackCorrect": "Well done! You've sieved out the primes: 2, 3, 5, 7, 11, 13, 17, 19, 23, 29", "feedbackIncorrect": "Not quite right. Remember to cross out ALL multiples of each prime.", "showNameTag": false}, "action_button_text": "Fundamental Theorem"}}}, {"id": "pwp-screen6-fundamental-theorem", "type": "lesson_screen", "order": 6, "estimatedTimeSeconds": 60, "content": {"headline": "The Fundamental Theorem of Arithmetic", "body_md": "This is a BIG one! The Fundamental Theorem of Arithmetic states that every integer greater than 1 is either:\n\n1. A prime number itself, OR\n2. Can be represented as a **unique product of prime numbers** (ignoring the order of factors).\n\nExample: 12 = 2² × 3. No other combination of primes will multiply to 12.", "visual": {"type": "giphy_search", "value": "important discovery"}, "interactive_element": {"type": "multiple_choice_text", "question_text": "What is the prime factorization of 20?", "options": [{"id": "pwp6opt1", "text": "2 × 10", "is_correct": false, "feedback_incorrect": "That's not fully factored into primes. What is 10 in terms of primes?"}, {"id": "pwp6opt2", "text": "2² × 5", "is_correct": true, "feedback_correct": "Correct! 20 = 2 × 2 × 5 = 2² × 5", "feedback_incorrect": "Break it down completely into prime factors."}, {"id": "pwp6opt3", "text": "4 × 5", "is_correct": false, "feedback_incorrect": "4 is not a prime number. What is 4 in terms of primes?"}, {"id": "pwp6opt4", "text": "2 × 2 × 2 × 5", "is_correct": false, "feedback_incorrect": "Check your multiplication. Does 2³ × 5 = 20?"}], "action_button_text": "Why is this important?"}}}, {"id": "pwp-screen7-why-primes-matter", "type": "lesson_screen", "order": 7, "estimatedTimeSeconds": 70, "content": {"headline": "Primes in the Wild", "body_md": "Prime numbers are crucial in many areas:\n\n*   **Cryptography:** Modern encryption (like RSA) relies on the difficulty of factoring large numbers into primes. This keeps your online information secure!\n\n*   **Computer Science:** Hash functions, random number generation, and data structures all use properties of prime numbers.\n\n*   **Nature:** Cicadas emerge in prime-numbered year cycles (13 or 17 years) to minimize encounters with predators that have more regular cycles.", "visual": {"type": "unsplash_search", "value": "encryption security"}, "interactive_element": {"type": "interactive_diagram_widget", "data": {"title": "RSA Encryption (Simplified)", "description": "Modern encryption uses the product of two large primes (p×q=n). While multiplying is easy, factoring n back into p and q is extremely difficult for very large numbers.", "imageUrl": "assets/images/rsa_encryption_diagram.png", "hotspots": [{"x": 0.2, "y": 0.3, "label": "Large Prime p", "description": "A prime number with hundreds of digits"}, {"x": 0.2, "y": 0.7, "label": "Large Prime q", "description": "Another prime number with hundreds of digits"}, {"x": 0.8, "y": 0.5, "label": "Product n = p×q", "description": "Easy to calculate, but nearly impossible to factor back when p and q are very large"}], "showNameTag": false}, "action_button_text": "Fascinating!"}}}, {"id": "pwp-screen8-twin-primes", "type": "lesson_screen", "order": 8, "estimatedTimeSeconds": 60, "content": {"headline": "Twin Primes and Unsolved Mysteries", "body_md": "Prime numbers still hold many mysteries! **Twin primes** are pairs of primes that differ by 2, like (3,5), (5,7), (11,13), (17,19).\n\nThe Twin Prime Conjecture states that there are infinitely many twin primes, but no one has proven this yet!\n\nAnother mystery: Is every even number greater than 2 the sum of two primes? This is called <PERSON><PERSON>'s Conjecture, and it remains unproven despite being tested for numbers up to 4 × 10¹⁸!", "visual": {"type": "giphy_search", "value": "mystery mathematics"}, "interactive_element": {"type": "multiple_choice_text", "question_text": "Which of these is a pair of twin primes?", "options": [{"id": "pwp8opt1", "text": "(13, 15)", "is_correct": false, "feedback_incorrect": "15 is not prime (3×5=15)."}, {"id": "pwp8opt2", "text": "(17, 19)", "is_correct": true, "feedback_correct": "Correct! Both 17 and 19 are prime, and they differ by 2.", "feedback_incorrect": "Twin primes are two prime numbers that differ by exactly 2."}, {"id": "pwp8opt3", "text": "(19, 23)", "is_correct": false, "feedback_incorrect": "Both are prime, but they differ by 4, not 2."}, {"id": "pwp8opt4", "text": "(21, 23)", "is_correct": false, "feedback_incorrect": "21 is not prime (3×7=21)."}], "action_button_text": "Recap"}}}, {"id": "pwp-screen9-recap", "type": "lesson_screen", "order": 9, "estimatedTimeSeconds": 40, "content": {"headline": "Recap: Playing with Primes", "body_md": "*   **Prime numbers** are divisible only by 1 and themselves.\n*   **Composite numbers** can be uniquely factored into primes (Fundamental Theorem of Arithmetic).\n*   The **Sieve of Eratosthenes** is an efficient method to find primes.\n*   Primes are essential in cryptography, computer science, and appear in nature.\n*   Many fascinating conjectures about primes remain unsolved!", "interactive_element": {"type": "interactive_sequence", "data": {"title": "Prime Number Sequence", "description": "What are the next two prime numbers in this sequence?", "sequence": [2, 3, 5, 7, 11, 13, 17, 19, 23], "correctAnswers": [29, 31], "feedbackCorrect": "Excellent! The next two primes are indeed 29 and 31.", "feedbackIncorrect": "Not quite. Remember to check if each number is divisible only by 1 and itself.", "showNameTag": false}, "action_button_text": "Next Lesson"}}}]}, {"id": "beauty-of-divisibility", "title": "The Beauty of Divisibility", "description": "Uncover elegant rules and patterns in how numbers divide.", "order": 2, "type": "interactive_lesson", "estimatedTimeMinutes": 9, "contentBlocks": [{"id": "bod-screen1-intro", "type": "lesson_screen", "order": 1, "estimatedTimeSeconds": 30, "content": {"headline": "Secrets of Sharing: Divisibility Rules!", "body_md": "Can you tell if 12345 is divisible by 5 without actually dividing? Or if 987 is divisible by 3? Divisibility rules are like secret codes that reveal hidden patterns in numbers!", "visual": {"type": "giphy_search", "value": "secret code numbers"}, "interactive_element": {"type": "interactive_pattern_gallery", "data": {"title": "Divisibility Patterns", "description": "Tap on all numbers that are divisible by 3:", "numbers": [12, 15, 17, 18, 21, 23, 27, 30, 32, 33], "correctPattern": [12, 15, 18, 21, 27, 30, 33], "feedbackCorrect": "Excellent! All these numbers have digits that sum to a multiple of 3.", "feedbackIncorrect": "Not quite. Remember, a number is divisible by 3 if the sum of its digits is divisible by 3.", "showNameTag": false}, "action_button_text": "Unlock the Secrets!"}}}, {"id": "bod-screen2-divisibility-overview", "type": "lesson_screen", "order": 2, "estimatedTimeSeconds": 40, "content": {"headline": "Why Divisibility Rules Matter", "body_md": "Divisibility rules help us quickly determine if one number divides another without performing long division. These patterns reveal the beautiful structure hidden within our number system!\n\nThey're useful for:\n- Simplifying fractions\n- Finding common factors\n- Testing if a number is prime\n- Mental math shortcuts", "visual": {"type": "unsplash_search", "value": "mathematical patterns"}, "interactive_element": {"type": "button", "text": "Show Me the Rules!", "action": "next_screen"}}}, {"id": "bod-screen3-rule-for-2", "type": "lesson_screen", "order": 3, "estimatedTimeSeconds": 50, "content": {"headline": "Rule for 2: <PERSON>", "body_md": "A number is divisible by 2 if its last digit is even (0, 2, 4, 6, or 8).\n\nThis works because every power of 10 is divisible by 2, so only the last digit determines divisibility by 2.", "visual": {"type": "unsplash_search", "value": "even numbers"}, "interactive_element": {"type": "interactive_number_sequence", "data": {"title": "Divisibility by 2", "description": "Which of these numbers is divisible by 2?", "options": ["12,345", "9,876", "54,321", "10,001"], "correctAnswer": "9,876", "feedbackCorrect": "Correct! 9,876 ends in 6, which is even, so it's divisible by 2.", "feedbackIncorrect": "Look at the last digit of each number. Is it 0, 2, 4, 6, or 8?", "showNameTag": false}, "action_button_text": "Rules for 5 and 10"}}}, {"id": "bod-screen4-rule-for-5-10", "type": "lesson_screen", "order": 4, "estimatedTimeSeconds": 60, "content": {"headline": "Rules for 5 and 10: <PERSON><PERSON> and <PERSON>s", "body_md": "*   A number is divisible by 5 if its last digit is 0 or 5.\n*   A number is divisible by 10 if its last digit is 0.\n\nLike the rule for 2, these work because they depend on the properties of our base-10 number system.", "visual": {"type": "unsplash_search", "value": "target bullseye"}, "interactive_element": {"type": "multiple_choice_text", "question_text": "Which of these numbers is divisible by both 5 and 10?", "options": [{"id": "bod4opt1", "text": "125", "is_correct": false, "feedback_incorrect": "125 is divisible by 5 (ends in 5), but not by 10."}, {"id": "bod4opt2", "text": "270", "is_correct": true, "feedback_correct": "Correct! 270 ends in 0, so it's divisible by both 5 and 10.", "feedback_incorrect": "For divisibility by 10, the number must end in 0."}, {"id": "bod4opt3", "text": "357", "is_correct": false, "feedback_incorrect": "357 doesn't end in 0 or 5, so it's not divisible by 5 or 10."}, {"id": "bod4opt4", "text": "895", "is_correct": false, "feedback_incorrect": "895 is divisible by 5 (ends in 5), but not by 10."}], "action_button_text": "Rule for 3!"}}}, {"id": "bod-screen5-rule-for-3", "type": "lesson_screen", "order": 5, "estimatedTimeSeconds": 90, "content": {"headline": "Rule for 3: Sum of Digits", "body_md": "A number is divisible by 3 if the sum of its digits is divisible by 3.\n\nExample: 567. Sum of digits = 5 + 6 + 7 = 18.\nSince 18 is divisible by 3 (18 ÷ 3 = 6), then 567 is divisible by 3.\n\nThis works because 10 ≡ 1 (mod 3), so each place value contributes just the digit itself to the remainder when divided by 3.", "visual": {"type": "giphy_search", "value": "adding numbers"}, "interactive_element": {"type": "interactive_calculator_widget", "data": {"title": "Divisibility by 3 Calculator", "description": "Enter a number to check if it's divisible by 3:", "type": "divisibility", "divisor": 3, "placeholder": "Enter a number", "showSteps": true, "feedbackDivisible": "Yes! The sum of digits is divisible by 3, so the number is divisible by 3.", "feedbackNotDivisible": "No. The sum of digits is not divisible by 3, so the number is not divisible by 3.", "showNameTag": false}, "action_button_text": "Rule for 9?"}}}, {"id": "bod-screen6-rule-for-9", "type": "lesson_screen", "order": 6, "estimatedTimeSeconds": 70, "content": {"headline": "Rule for 9: Sum of Digits (Again!)", "body_md": "Similar to 3! A number is divisible by 9 if the sum of its digits is divisible by 9.\n\nExample: 729. Sum of digits = 7 + 2 + 9 = 18.\nSince 18 is divisible by 9 (18 ÷ 9 = 2), then 729 is divisible by 9.\n\nThis works for the same mathematical reason as the rule for 3, since 10 ≡ 1 (mod 9).", "visual": {"type": "giphy_search", "value": "magic trick"}, "interactive_element": {"type": "multiple_choice_text", "question_text": "Which number is divisible by 9?", "options": [{"id": "bod6opt1", "text": "234", "is_correct": false, "feedback_incorrect": "Sum of digits: 2+3+4=9, which is divisible by 9. So 234 is divisible by 9."}, {"id": "bod6opt2", "text": "567", "is_correct": false, "feedback_incorrect": "Sum of digits: 5+6+7=18, which is divisible by 9. So 567 is divisible by 9."}, {"id": "bod6opt3", "text": "999", "is_correct": true, "feedback_correct": "Correct! Sum of digits: 9+9+9=27, which is divisible by 9. So 999 is divisible by 9.", "feedback_incorrect": "Add up all the digits and check if the sum is divisible by 9."}, {"id": "bod6opt4", "text": "123", "is_correct": false, "feedback_incorrect": "Sum of digits: 1+2+3=6, which is not divisible by 9. So 123 is not divisible by 9."}], "action_button_text": "More Rules!"}}}, {"id": "bod-screen7-rule-for-4-8", "type": "lesson_screen", "order": 7, "estimatedTimeSeconds": 80, "content": {"headline": "Rules for 4 and 8: Last Digits", "body_md": "*   **Rule for 4:** A number is divisible by 4 if the number formed by its last two digits is divisible by 4.\n    Example: 1324 is divisible by 4 because 24 is divisible by 4.\n\n*   **Rule for 8:** A number is divisible by 8 if the number formed by its last three digits is divisible by 8.\n    Example: 9,624 is divisible by 8 because 624 is divisible by 8.", "visual": {"type": "unsplash_search", "value": "digital numbers"}, "interactive_element": {"type": "interactive_number_sequence", "data": {"title": "Divisibility by 4", "description": "Which of these numbers is divisible by 4?", "options": ["1,234", "5,678", "9,102", "3,457"], "correctAnswer": "5,678", "feedbackCorrect": "Correct! The last two digits of 5,678 are 78, and 78 is divisible by 4 (78 ÷ 4 = 19 with remainder 2).", "feedbackIncorrect": "Look at the last two digits of each number. Is that two-digit number divisible by 4?", "showNameTag": false}, "action_button_text": "Combining Rules"}}}, {"id": "bod-screen8-rule-for-6-12", "type": "lesson_screen", "order": 8, "estimatedTimeSeconds": 90, "content": {"headline": "Combining Rules: 6 and 12", "body_md": "Some divisibility rules combine other rules:\n\n*   **Rule for 6:** A number is divisible by 6 if it is divisible by BOTH 2 AND 3.\n    Example: 738 is divisible by 6 because it's even (divisible by 2) and 7+3+8=18 is divisible by 3.\n\n*   **Rule for 12:** A number is divisible by 12 if it is divisible by BOTH 3 AND 4.\n    Example: 1,236 is divisible by 12 because the sum of its digits (12) is divisible by 3, and the last two digits (36) are divisible by 4.", "interactive_element": {"type": "interactive_conditional_flow", "data": {"title": "Divisibility by 6 Flowchart", "description": "Follow the flowchart to determine if a number is divisible by 6:", "startNode": "start", "nodes": {"start": {"text": "Is the number even?", "options": [{"text": "Yes", "nextNode": "check_div_3"}, {"text": "No", "nextNode": "not_div_6"}]}, "check_div_3": {"text": "Is the sum of digits divisible by 3?", "options": [{"text": "Yes", "nextNode": "div_6"}, {"text": "No", "nextNode": "not_div_6"}]}, "div_6": {"text": "The number is divisible by 6!", "options": []}, "not_div_6": {"text": "The number is NOT divisible by 6.", "options": []}}, "showNameTag": false}, "action_button_text": "Rule for 11"}}}, {"id": "bod-screen9-rule-for-11", "type": "lesson_screen", "order": 9, "estimatedTimeSeconds": 70, "content": {"headline": "Rule for 11: Alternating Sum", "body_md": "A number is divisible by 11 if the alternating sum of its digits is divisible by 11.\n\nExample: Is 8,591 divisible by 11?\nAlternating sum: 8 - 5 + 9 - 1 = 11\nSince 11 is divisible by 11, 8,591 is divisible by 11.\n\nThis works because 10 ≡ -1 (mod 11), so each place value alternates between +1 and -1 when divided by 11.", "visual": {"type": "giphy_search", "value": "alternating pattern"}, "interactive_element": {"type": "text_input", "question_text": "Is 2,728 divisible by 11? (Type Yes or No)", "correct_answer_regex": "^[Yy][Ee][Ss]$", "feedback_correct": "Correct! Alternating sum: 2 - 7 + 2 - 8 = -11, which is divisible by 11. So 2,728 is divisible by 11.", "feedback_incorrect": "Calculate the alternating sum: 2 - 7 + 2 - 8 = -11, which is divisible by 11. So 2,728 is divisible by 11.", "action_button_text": "Practical Applications"}}}, {"id": "bod-screen10-applications", "type": "lesson_screen", "order": 10, "estimatedTimeSeconds": 60, "content": {"headline": "Real-World Applications", "body_md": "Divisibility rules have many practical applications:\n\n*   **Simplifying fractions:** Quickly identify common factors.\n*   **Checking calculations:** Verify results without redoing the entire calculation.\n*   **Computer science:** Used in hash functions, error detection codes, and cryptography.\n*   **Mental math:** Perform complex calculations quickly in your head.", "visual": {"type": "unsplash_search", "value": "calculator mathematics"}, "interactive_element": {"type": "multiple_choice_text", "question_text": "Which of these fractions can be simplified using divisibility rules?", "options": [{"id": "bod10opt1", "text": "17/51", "is_correct": true, "feedback_correct": "Correct! Both 17 and 51 are divisible by 17, so this simplifies to 1/3.", "feedback_incorrect": "Check if both numbers share a common factor using divisibility rules."}, {"id": "bod10opt2", "text": "23/46", "is_correct": true, "feedback_correct": "Correct! Both 23 and 46 are divisible by 23, so this simplifies to 1/2.", "feedback_incorrect": "Check if both numbers share a common factor using divisibility rules."}, {"id": "bod10opt3", "text": "19/57", "is_correct": true, "feedback_correct": "Correct! Both 19 and 57 are divisible by 19, so this simplifies to 1/3.", "feedback_incorrect": "Check if both numbers share a common factor using divisibility rules."}, {"id": "bod10opt4", "text": "All of the above", "is_correct": true, "feedback_correct": "Correct! All these fractions can be simplified using divisibility rules.", "feedback_incorrect": "Check each fraction individually using divisibility rules."}], "action_button_text": "Recap"}}}, {"id": "bod-screen11-recap", "type": "lesson_screen", "order": 11, "estimatedTimeSeconds": 40, "content": {"headline": "Recap: Divisibility Power!", "body_md": "You've learned powerful shortcuts for determining divisibility:\n\n*   **Rule for 2:** Last digit is even (0, 2, 4, 6, 8)\n*   **Rule for 3:** Sum of digits is divisible by 3\n*   **Rule for 4:** Last two digits divisible by 4\n*   **Rule for 5:** Last digit is 0 or 5\n*   **Rule for 6:** Divisible by both 2 and 3\n*   **Rule for 8:** Last three digits divisible by 8\n*   **Rule for 9:** Sum of digits is divisible by 9\n*   **Rule for 10:** Last digit is 0\n*   **Rule for 11:** Alternating sum of digits divisible by 11", "interactive_element": {"type": "interactive_sequence", "data": {"title": "Divisibility Challenge", "description": "Which numbers are divisible by 9?", "sequence": [153, 246, 372, 481, 567, 639, 724, 855, 918, 999], "correctAnswers": [153, 567, 639, 918, 999], "feedbackCorrect": "Excellent! For each of these numbers, the sum of digits is divisible by 9.", "feedbackIncorrect": "Check your work. Remember, a number is divisible by 9 if the sum of its digits is divisible by 9.", "showNameTag": false}, "action_button_text": "Next Lesson"}}}]}, {"id": "mastering-mental-math", "title": "Mastering Mental Math", "description": "Learn clever shortcuts and estimation techniques for quick calculations.", "order": 3, "type": "interactive_lesson", "estimatedTimeMinutes": 10, "contentBlocks": [{"id": "mmm-screen1-intro", "type": "lesson_screen", "order": 1, "estimatedTimeSeconds": 30, "content": {"headline": "Math Whiz in Your Head!", "body_md": "Calculators are great, but imagine doing quick math in your head! Mental math boosts your number sense, improves your cognitive abilities, and makes everyday calculations a breeze. Let's discover the secrets of mental math masters!", "visual": {"type": "giphy_search", "value": "brain power thinking fast"}, "interactive_element": {"type": "multiple_choice_text", "question_text": "Which of these is NOT a benefit of practicing mental math?", "options": [{"id": "mmm1opt1", "text": "Improved number sense", "is_correct": false, "feedback_incorrect": "Mental math definitely improves your number sense and understanding of numerical relationships."}, {"id": "mmm1opt2", "text": "Faster calculations in daily life", "is_correct": false, "feedback_incorrect": "Mental math helps you calculate things quickly in everyday situations."}, {"id": "mmm1opt3", "text": "Enhanced memory and concentration", "is_correct": false, "feedback_incorrect": "Mental math exercises your brain and improves both memory and concentration."}, {"id": "mmm1opt4", "text": "Eliminating the need to understand concepts", "is_correct": true, "feedback_correct": "Correct! Mental math actually deepens your understanding of mathematical concepts rather than eliminating the need for it.", "feedback_incorrect": "Mental math enhances understanding rather than replacing it."}], "action_button_text": "Teach Me the Tricks!"}}}, {"id": "mmm-screen2-why-mental-math", "type": "lesson_screen", "order": 2, "estimatedTimeSeconds": 40, "content": {"headline": "Why Mental Math Matters", "body_md": "Mental math isn't just about impressing friends—it has real-world benefits:\n\n* **Practical Utility**: Calculate tips, discounts, and budgets on the fly\n* **Cognitive Benefits**: Improves working memory, concentration, and problem-solving\n* **Mathematical Fluency**: Builds stronger number sense and mathematical intuition\n* **Confidence**: Reduces math anxiety and builds mathematical self-assurance", "visual": {"type": "unsplash_search", "value": "brain training"}, "interactive_element": {"type": "interactive_diagram_widget", "data": {"title": "Mental Math Benefits", "description": "Mental math strengthens connections between different brain regions", "imageUrl": "assets/images/brain_regions_math.png", "hotspots": [{"x": 0.3, "y": 0.2, "label": "Prefrontal Cortex", "description": "Working memory and decision making"}, {"x": 0.7, "y": 0.4, "label": "Parietal <PERSON>", "description": "Number processing and calculations"}, {"x": 0.5, "y": 0.7, "label": "Hippocampus", "description": "Memory formation and recall"}], "showNameTag": false}, "action_button_text": "Let's Learn Strategies!"}}}, {"id": "mmm-screen3-add-subtract-left-to-right", "type": "lesson_screen", "order": 3, "estimatedTimeSeconds": 70, "content": {"headline": "Adding/Subtracting: Left to Right", "body_md": "Instead of right-to-left (like on paper), try left-to-right for mental addition/subtraction. This works with how our brain naturally processes numbers!\n\nExample: 345 + 123\n1. Add hundreds: 300 + 100 = 400\n2. Add tens: 40 + 20 = 60 (Total so far: 400 + 60 = 460)\n3. Add units: 5 + 3 = 8 (Total: 460 + 8 = 468)", "visual": {"type": "giphy_search", "value": "adding numbers animation"}, "interactive_element": {"type": "interactive_calculator_widget", "data": {"title": "Left-to-Right Addition", "description": "Try adding these numbers left-to-right", "type": "step_by_step", "problem": "642 + 257", "steps": ["Add hundreds: 600 + 200 = 800", "Add tens: 40 + 50 = 90 (Total so far: 800 + 90 = 890)", "Add units: 2 + 7 = 9 (Final total: 890 + 9 = 899)"], "solution": "899", "showNameTag": false}, "action_button_text": "Compensation Method"}}}, {"id": "mmm-screen4-compensation", "type": "lesson_screen", "order": 4, "estimatedTimeSeconds": 80, "content": {"headline": "Compensation: Making Numbers Friendly", "body_md": "Adjust numbers to make them easier to work with, then compensate for the adjustment.\n\n**Addition Example**: 49 + 27\n*   49 is close to 50. So, 50 + 27 = 77.\n*   We added 1 to 49 to make it 50, so subtract 1 from the result: 77 - 1 = 76.\n\n**Subtraction Example**: 63 - 18\n*   18 is close to 20. So, 63 - 20 = 43.\n*   We subtracted 2 extra (20 instead of 18), so add 2 back: 43 + 2 = 45.", "visual": {"type": "unsplash_search", "value": "balancing scale"}, "interactive_element": {"type": "multiple_choice_text", "question_text": "Calculate 398 + 156 using compensation:", "options": [{"id": "mmm4opt1", "text": "554", "is_correct": true, "feedback_correct": "Correct! 398 is close to 400, so 400 + 156 = 556, then 556 - 2 = 554", "feedback_incorrect": "Try rounding 398 up to 400, adding 156, then compensating."}, {"id": "mmm4opt2", "text": "556", "is_correct": false, "feedback_incorrect": "You rounded 398 to 400, but forgot to compensate by subtracting 2."}, {"id": "mmm4opt3", "text": "550", "is_correct": false, "feedback_incorrect": "Check your calculation. 398 rounds to 400, then 400 + 156 = 556, then compensate."}, {"id": "mmm4opt4", "text": "560", "is_correct": false, "feedback_incorrect": "You may have rounded both numbers. Try rounding just 398 to 400."}], "action_button_text": "Multiplication Tricks"}}}, {"id": "mmm-screen5-multiplying-by-5-and-9", "type": "lesson_screen", "order": 5, "estimatedTimeSeconds": 70, "content": {"headline": "Multiplication Shortcuts: 5 and 9", "body_md": "**Multiplying by 5**:\n1. Multiply by 10 (add a zero).\n2. Then, take half of that result.\n\nExample: 68 × 5\n1. 68 × 10 = 680\n2. Half of 680 = 340. So, 68 × 5 = 340.\n\n**Multiplying by 9**:\n1. Multiply by 10 (add a zero).\n2. Subtract the original number.\n\nExample: 37 × 9\n1. 37 × 10 = 370\n2. 370 - 37 = 333. So, 37 × 9 = 333.", "visual": {"type": "giphy_search", "value": "multiplication trick"}, "interactive_element": {"type": "interactive_number_sequence", "data": {"title": "Multiplication Shortcuts", "description": "Which calculation uses the correct shortcut?", "options": ["76 × 5 = 760 ÷ 2 = 380", "76 × 5 = 76 × 10 ÷ 2 = 380", "76 × 9 = 76 × 10 - 76 = 684", "76 × 9 = 76 × 10 + 76 = 836"], "correctAnswer": "76 × 5 = 76 × 10 ÷ 2 = 380", "feedbackCorrect": "Correct! For multiplying by 5, we multiply by 10 and then divide by 2.", "feedbackIncorrect": "For multiplying by 5, multiply by 10 then divide by 2. For multiplying by 9, multiply by 10 then subtract the original number.", "showNameTag": false}, "action_button_text": "More Multiplication!"}}}, {"id": "mmm-screen6-multiplying-by-11-and-25", "type": "lesson_screen", "order": 6, "estimatedTimeSeconds": 70, "content": {"headline": "More Multiplication Magic: 11 and 25", "body_md": "**Multiplying by 11** (2-digit numbers):\nAdd the two digits, and place the result between the original digits.\nIf the sum exceeds 9, carry the 1 to the left digit.\n\nExample: 45 × 11\n4 + 5 = 9, so 45 × 11 = 495\n\n**Multiplying by 25**:\nDivide by 4, then multiply by 100.\n\nExample: 48 × 25\n48 ÷ 4 = 12, then 12 × 100 = 1,200\nSo 48 × 25 = 1,200", "visual": {"type": "unsplash_search", "value": "magic numbers"}, "interactive_element": {"type": "text_input", "question_text": "Calculate 36 × 25 using the shortcut:", "correct_answer_regex": "^900$", "feedback_correct": "Perfect! 36 ÷ 4 = 9, then 9 × 100 = 900.", "feedback_incorrect": "Divide 36 by 4 (which is 9), then multiply by 100.", "action_button_text": "Squaring Tricks"}}}, {"id": "mmm-screen7-squaring-numbers", "type": "lesson_screen", "order": 7, "estimatedTimeSeconds": 80, "content": {"headline": "Squaring Numbers Near 100", "body_md": "To square a number near 100:\n1. Find the difference from 100 (call it d)\n2. Square result = (100 + d) × (100 - d) + d²\n\nExample: 97²\n1. d = 3 (difference from 100)\n2. 97² = (100 - 3) × (100 + 3) + 3²\n   = 97 × 103 + 9\n   = 9,991 + 9\n   = 9,400 + 591 + 9\n   = 10,000 - 600 + 591 + 9\n   = 10,000 - 600 + 600\n   = 10,000 - 0\n   = 10,000\n\nWait, that's not right! Let's try again:\n97² = (100 - 3)²\n    = 100² - 2(100)(3) + 3²\n    = 10,000 - 600 + 9\n    = 9,409", "visual": {"type": "giphy_search", "value": "squaring numbers"}, "interactive_element": {"type": "interactive_calculator_widget", "data": {"title": "Squaring Numbers", "description": "Calculate 96² using the difference from 100 method", "type": "step_by_step", "problem": "96²", "steps": ["Find the difference from 100: d = 4", "Calculate 96² = 100² - 2(100)(4) + 4²", "= 10,000 - 800 + 16", "= 9,216"], "solution": "9,216", "showNameTag": false}, "action_button_text": "Estimation Skills"}}}, {"id": "mmm-screen8-estimation", "type": "lesson_screen", "order": 8, "estimatedTimeSeconds": 70, "content": {"headline": "Estimation: The Art of 'About'", "body_md": "Sometimes an exact answer isn't needed, just a good estimate. Rounding numbers to make them easier to work with can save time and mental effort.\n\n**Example**: What's roughly 29 × 18?\n*   Round 29 to 30\n*   Round 18 to 20\n*   30 × 20 = 600 (Actual answer is 522, so this is a reasonable estimate)\n\n**When to use estimation**:\n* Shopping and budgeting\n* Checking if a calculation is reasonable\n* Quick decision-making\n* Comparing quantities", "visual": {"type": "giphy_search", "value": "measuring tape close enough"}, "interactive_element": {"type": "multiple_choice_text", "question_text": "Estimate 39 × 42 by rounding both numbers:", "options": [{"id": "mmm8opt1", "text": "About 1,600", "is_correct": true, "feedback_correct": "Correct! Rounding to 40 × 40 = 1,600 gives a good estimate. The exact answer is 1,638.", "feedback_incorrect": "Try rounding both numbers to the nearest ten."}, {"id": "mmm8opt2", "text": "About 1,200", "is_correct": false, "feedback_incorrect": "Check your rounding. 39 rounds to 40 and 42 rounds to 40."}, {"id": "mmm8opt3", "text": "About 2,000", "is_correct": false, "feedback_incorrect": "This estimate is too high. 39 rounds to 40 and 42 rounds to 40."}, {"id": "mmm8opt4", "text": "About 800", "is_correct": false, "feedback_incorrect": "This estimate is too low. 39 rounds to 40 and 42 rounds to 40."}], "action_button_text": "Real-World Applications"}}}, {"id": "mmm-screen9-real-world-applications", "type": "lesson_screen", "order": 9, "estimatedTimeSeconds": 60, "content": {"headline": "Mental Math in Daily Life", "body_md": "Mental math has countless practical applications:\n\n* **Shopping**: Calculating discounts (20% off? Divide by 5!)\n* **Tipping**: Quick calculation of 15-20% at restaurants\n* **Cooking**: Adjusting recipe quantities on the fly\n* **Time Management**: Estimating how long tasks will take\n* **Budgeting**: Tracking expenses without a calculator\n* **Games**: Keeping score in card games or board games", "visual": {"type": "unsplash_search", "value": "shopping calculator"}, "interactive_element": {"type": "interactive_conditional_flow", "data": {"title": "Calculating a Tip", "description": "Follow this flowchart to calculate a 15% tip on a $42 bill:", "startNode": "start", "nodes": {"start": {"text": "Calculate 10% of the bill", "options": [{"text": "$4.20", "nextNode": "wrong1"}, {"text": "$4.20", "nextNode": "wrong1"}, {"text": "$4.20", "nextNode": "wrong1"}]}, "wrong1": {"text": "10% of $42 is $4.20. Now calculate half of that amount.", "options": [{"text": "$2.10", "nextNode": "correct1"}]}, "correct1": {"text": "Correct! Now add the 10% and the 5% together.", "options": [{"text": "$6.30", "nextNode": "final"}]}, "final": {"text": "Correct! A 15% tip on $42 is $6.30. This is a great mental math shortcut for tipping!", "options": []}}, "showNameTag": false}, "action_button_text": "Practice Challenge"}}}, {"id": "mmm-screen10-practice-challenge", "type": "lesson_screen", "order": 10, "estimatedTimeSeconds": 90, "content": {"headline": "Mental Math Challenge!", "body_md": "Let's put your mental math skills to the test! Try these calculations using the shortcuts you've learned. Work through them step by step in your head.", "visual": {"type": "giphy_search", "value": "brain workout"}, "interactive_element": {"type": "interactive_sequence", "data": {"title": "Mental Math Challenge", "description": "Solve these problems using mental math shortcuts:", "sequence": ["78 + 19 = ?", "150 - 53 = ?", "24 × 5 = ?", "36 × 11 = ?", "68 × 25 = ?"], "correctAnswers": ["97", "97", "120", "396", "1700"], "feedbackCorrect": "Amazing! You're a mental mathlete!", "feedbackIncorrect": "Check your calculations. Remember to use the shortcuts we learned!", "showNameTag": false}, "action_button_text": "I'm a Genius!"}}}, {"id": "mmm-screen11-recap", "type": "lesson_screen", "order": 11, "estimatedTimeSeconds": 40, "content": {"headline": "Recap: Mental Math Mastery", "body_md": "You've learned powerful mental math strategies:\n\n*   **Addition/Subtraction**: Work left-to-right\n*   **Compensation**: Make numbers friendly, then adjust\n*   **Multiplication Shortcuts**:\n    - For 5: Multiply by 10, then halve\n    - For 9: Multiply by 10, then subtract the number\n    - For 11: Add adjacent digits and place between\n    - For 25: Divide by 4, multiply by 100\n*   **Squaring**: Special techniques for numbers near 100\n*   **Estimation**: Round to make calculations easier\n\nPractice these regularly to become a mental math master!", "interactive_element": {"type": "multiple_choice_text", "question_text": "Which mental math strategy would be MOST efficient for calculating 32 × 25?", "options": [{"id": "mmm11opt1", "text": "Left-to-right addition", "is_correct": false, "feedback_incorrect": "Left-to-right addition is for addition problems, not multiplication."}, {"id": "mmm11opt2", "text": "Compensation method", "is_correct": false, "feedback_incorrect": "Compensation works well for addition and subtraction with numbers close to round numbers."}, {"id": "mmm11opt3", "text": "Divide by 4, multiply by 100", "is_correct": true, "feedback_correct": "Correct! For multiplying by 25, we can divide by 4 (32 ÷ 4 = 8) and multiply by 100 (8 × 100 = 800).", "feedback_incorrect": "For multiplying by 25, the shortcut is to divide by 4 and multiply by 100."}, {"id": "mmm11opt4", "text": "Multiply by 10 and subtract", "is_correct": false, "feedback_incorrect": "Multiplying by 10 and subtracting is the shortcut for multiplying by 9, not 25."}], "action_button_text": "Next Lesson"}}}]}, {"id": "thinking-in-different-bases", "title": "Thinking in Different Bases", "description": "Explore how numbers can be represented in various systems.", "order": 4, "type": "interactive_lesson", "estimatedTimeMinutes": 10, "contentBlocks": [{"id": "tdb-screen1-intro", "type": "lesson_screen", "order": 1, "estimatedTimeSeconds": 30, "content": {"headline": "Beyond Base 10: A New Perspective!", "body_md": "We use base 10 (decimal) every day, with digits 0-9. But what if we used fewer digits? Or more? Welcome to the world of different number bases!", "visual": {"type": "giphy_search", "value": "different worlds numbers"}, "interactive_element": {"type": "interactive_pattern_gallery", "data": {"title": "Number Systems Around Us", "description": "Tap on all the number systems that use a base other than 10:", "numbers": ["Decimal", "Binary", "Octal", "Hexadecimal", "Roman Numerals", "<PERSON><PERSON>", "Metric System"], "correctPattern": ["Binary", "Octal", "Hexadecimal", "Roman Numerals", "<PERSON><PERSON>"], "feedbackCorrect": "Great job! All of these use different bases or different systems entirely!", "feedbackIncorrect": "Not quite. Decimal uses base 10, and the Metric System is based on powers of 10.", "showNameTag": false}, "action_button_text": "Show Me How!"}}}, {"id": "tdb-screen2-base-10-review", "type": "lesson_screen", "order": 2, "estimatedTimeSeconds": 60, "content": {"headline": "Quick Recap: Base 10 (Decimal)", "body_md": "In base 10, each digit's position represents a power of 10.\nExample: **253** = (2 × 10²) + (5 × 10¹) + (3 × 10⁰)\n= (2 × 100) + (5 × 10) + (3 × 1)\n= 200 + 50 + 3 = 253", "visual": {"type": "unsplash_search", "value": "abacus counting"}, "interactive_element": {"type": "interactive_calculator_widget", "data": {"title": "Place Value Explorer", "description": "See how the number 4,728 breaks down in base 10:", "type": "step_by_step", "problem": "4,728 in base 10", "steps": ["4 × 10³ = 4 × 1,000 = 4,000", "7 × 10² = 7 × 100 = 700", "2 × 10¹ = 2 × 10 = 20", "8 × 10⁰ = 8 × 1 = 8", "4,000 + 700 + 20 + 8 = 4,728"], "solution": "4,728", "showNameTag": false}, "action_button_text": "What about other bases?"}}}, {"id": "tdb-screen3-base-2-binary", "type": "lesson_screen", "order": 3, "estimatedTimeSeconds": 90, "content": {"headline": "Base 2: Binary (Computers' Language!)", "body_md": "Base 2 (binary) uses only two digits: 0 and 1.\nEach position represents a power of 2.\nExample: Binary **1011₂**\n= (1 × 2³) + (0 × 2²) + (1 × 2¹) + (1 × 2⁰)\n= (1 × 8) + (0 × 4) + (1 × 2) + (1 × 1)\n= 8 + 0 + 2 + 1 = **11** (in base 10).", "visual": {"type": "giphy_search", "value": "binary code"}, "interactive_element": {"type": "interactive_number_base_converter", "data": {"title": "Binary Converter", "primaryColor": "#3F51B5", "secondaryColor": "#009688", "accentColor": "#FF5722", "showSteps": true, "defaultInputValue": "1101", "defaultFromBase": 2, "defaultToBase": 10, "showNameTag": false}, "action_button_text": "Converting to Binary"}}}, {"id": "tdb-screen4-converting-to-binary", "type": "lesson_screen", "order": 4, "estimatedTimeSeconds": 100, "content": {"headline": "Decimal to Binary: Division Method", "body_md": "To convert a decimal number to binary, repeatedly divide by 2 and record the remainders in reverse order.\n\nExample: Convert 13 (base 10) to binary:\n*   13 ÷ 2 = 6 remainder **1**\n*   6 ÷ 2 = 3 remainder **0**\n*   3 ÷ 2 = 1 remainder **1**\n*   1 ÷ 2 = 0 remainder **1**\nRead remainders up: **1101₂**", "visual": {"type": "giphy_search", "value": "reverse order"}, "interactive_element": {"type": "interactive_step_by_step_equation_solver", "data": {"title": "Convert 25 to <PERSON>ary", "description": "Follow the division method to convert 25 to binary:", "initialEquation": "25 in decimal = ? in binary", "steps": [{"explanation": "Divide 25 by 2", "equation": "25 ÷ 2 = 12 remainder 1"}, {"explanation": "Divide 12 by 2", "equation": "12 ÷ 2 = 6 remainder 0"}, {"explanation": "Divide 6 by 2", "equation": "6 ÷ 2 = 3 remainder 0"}, {"explanation": "Divide 3 by 2", "equation": "3 ÷ 2 = 1 remainder 1"}, {"explanation": "Divide 1 by 2", "equation": "1 ÷ 2 = 0 remainder 1"}, {"explanation": "Read remainders from bottom to top", "equation": "25 in decimal = 11001 in binary"}], "showNameTag": false}, "action_button_text": "Hexadecimal Next!"}}}, {"id": "tdb-screen5-base-16-hexadecimal", "type": "lesson_screen", "order": 5, "estimatedTimeSeconds": 90, "content": {"headline": "Base 16: Hexadecimal (Colors & Code!)", "body_md": "Base 16 (hexadecimal) uses digits 0-9 and letters A-F (A=10, B=11, ..., F=15).\nUsed in web colors (e.g., #FF0000 is red) and programming.\nExample: Hex **2B₁₆**\n= (2 × 16¹) + (B × 16⁰)\n= (2 × 16) + (11 × 1)\n= 32 + 11 = **43** (in base 10).", "visual": {"type": "unsplash_search", "value": "computer code colors"}, "interactive_element": {"type": "interactive_number_base_converter", "data": {"title": "Hexadecimal Converter", "primaryColor": "#673AB7", "secondaryColor": "#FF5722", "accentColor": "#2196F3", "showSteps": true, "defaultInputValue": "A5", "defaultFromBase": 16, "defaultToBase": 10, "showNameTag": false}, "action_button_text": "Why use different bases?"}}}, {"id": "tdb-screen6-why-different-bases", "type": "lesson_screen", "order": 6, "estimatedTimeSeconds": 50, "content": {"headline": "Why <PERSON><PERSON>?", "body_md": "*   **Binary (Base 2):** Fundamental for computers (on/off states).\n*   **Hexadecimal (Base 16):** A compact way to represent binary data (1 hex digit = 4 binary digits).\n*   **Octal (Base 8):** Used in some programming contexts.\n*   Understanding bases deepens your number sense!", "visual": {"type": "unsplash_search", "value": "computer circuit board"}, "interactive_element": {"type": "interactive_diagram_widget", "data": {"title": "Number Base Applications", "description": "Different number bases have different real-world applications", "imageUrl": "assets/images/number_bases_applications.png", "hotspots": [{"x": 0.2, "y": 0.3, "label": "Binary (Base 2)", "description": "Computer processing, digital electronics, Boolean logic"}, {"x": 0.5, "y": 0.7, "label": "Decimal (Base 10)", "description": "Everyday counting, commerce, most human calculations"}, {"x": 0.8, "y": 0.3, "label": "Hexadecimal (Base 16)", "description": "Computer memory addresses, color codes, compact binary representation"}], "showNameTag": false}, "action_button_text": "Binary-Hex Relationship"}}}, {"id": "tdb-screen7-binary-hex-relationship", "type": "lesson_screen", "order": 7, "estimatedTimeSeconds": 60, "content": {"headline": "Binary-Hex Relationship", "body_md": "Hexadecimal is popular because it has a special relationship with binary:\n\n**Each hex digit represents exactly 4 binary digits!**\n\nExample: Binary 1011 0101 = Hex B5\n* 1011 = B in hex (11 in decimal)\n* 0101 = 5 in hex (5 in decimal)", "visual": {"type": "giphy_search", "value": "conversion table"}, "interactive_element": {"type": "interactive_sequence", "data": {"title": "Binary to Hex Conversion", "description": "Convert these 4-bit binary numbers to hexadecimal:", "sequence": ["0000", "1001", "1100", "1111"], "correctAnswers": ["0", "9", "C", "F"], "feedbackCorrect": "Excellent! You've mastered the binary-to-hex conversion.", "feedbackIncorrect": "Remember: 0000=0, 0001=1, 0010=2, 0011=3, 0100=4, 0101=5, 0110=6, 0111=7, 1000=8, 1001=9, 1010=A, 1011=B, 1100=C, 1101=D, 1110=E, 1111=F", "showNameTag": false}, "action_button_text": "Recap"}}}, {"id": "tdb-screen8-recap", "type": "lesson_screen", "order": 8, "estimatedTimeSeconds": 40, "content": {"headline": "Recap: Base Jumping!", "body_md": "*   Base 10 is just one way to write numbers.\n*   Binary (base 2) uses 0s and 1s.\n*   Hexadecimal (base 16) uses 0-9 and A-F.\n*   Each hex digit represents exactly 4 binary digits.\n*   Different bases have different applications, especially in computing!", "interactive_element": {"type": "interactive_number_base_converter", "data": {"title": "Final Challenge: Convert Between Any Bases", "primaryColor": "#4CAF50", "secondaryColor": "#2196F3", "accentColor": "#FF9800", "showSteps": true, "defaultInputValue": "42", "defaultFromBase": 10, "defaultToBase": 2, "showNameTag": false}, "action_button_text": "Next Lesson"}}}]}, {"id": "infinite-world-of-fractions", "title": "The Infinite World of Fractions", "description": "Visualize and manipulate parts of a whole with ease.", "order": 5, "type": "interactive_lesson", "estimatedTimeMinutes": 10, "contentBlocks": [{"id": "iwf-screen1-intro", "type": "lesson_screen", "order": 1, "estimatedTimeSeconds": 30, "content": {"headline": "Slicing Up Reality: Understanding Fractions", "body_md": "Half a pizza, a quarter of an hour, two-thirds of a cup... Fractions are everywhere! They help us talk about parts of a whole.", "visual": {"type": "giphy_search", "value": "slicing pizza"}, "interactive_element": {"type": "button", "text": "Let's Dive In!", "action": "next_screen"}}}, {"id": "iwf-screen2-what-is-fraction", "type": "lesson_screen", "order": 2, "estimatedTimeSeconds": 60, "content": {"headline": "Anatomy of a Fraction", "body_md": "A fraction has two main parts:\n*   **Numerator (Top):** How many parts you have.\n*   **Denominator (Bottom):** How many equal parts the whole is divided into.\n\nExample: **3/4** means 3 parts out of a whole divided into 4 equal parts.", "visual": {"type": "local_asset", "value": "assets/images/fractions/three_quarters_pie.svg"}, "interactive_element": {"type": "multiple_choice_text", "question_text": "In the fraction 2/5, what does the 5 represent?", "options": [{"id": "iwf2opt1", "text": "The number of parts we have.", "is_correct": false, "feedback_incorrect": "That's the numerator (top number)."}, {"id": "iwf2opt2", "text": "The total number of equal parts in the whole.", "is_correct": true, "feedback_correct": "Correct! The denominator tells us how many slices make a whole.", "feedback_incorrect": "The denominator is the bottom number."}], "action_button_text": "Continue"}}}, {"id": "iwf-screen3-equivalent-fractions", "type": "lesson_screen", "order": 3, "estimatedTimeSeconds": 80, "content": {"headline": "Same Value, Different Look: Equivalent Fractions", "body_md": "1/2, 2/4, and 4/8 all represent the same amount! These are equivalent fractions.\nYou can get an equivalent fraction by multiplying or dividing both the numerator and denominator by the same non-zero number.", "visual": {"type": "local_asset", "value": "assets/images/fractions/equivalent_halves_quarters.svg"}, "interactive_element": {"type": "text_input", "question_text": "What is an equivalent fraction to 2/3 if you multiply top and bottom by 4?", "correct_answer_regex": "^8/12$", "feedback_correct": "Exactly! 2×4=8 and 3×4=12, so 8/12.", "feedback_incorrect": "Multiply both the numerator (2) and the denominator (3) by 4.", "action_button_text": "Comparing Fractions"}}}, {"id": "iwf-screen4-comparing-fractions", "type": "lesson_screen", "order": 4, "estimatedTimeSeconds": 90, "content": {"headline": "Which is Bigger? Comparing Fractions", "body_md": "To compare fractions like 2/5 and 3/7, find a common denominator!\n*   A common multiple of 5 and 7 is 35.\n*   2/5 = (2×7)/(5×7) = 14/35\n*   3/7 = (3×5)/(7×5) = 15/35\nSince 15/35 > 14/35, then 3/7 > 2/5.", "interactive_element": {"type": "multiple_choice_text", "question_text": "Which is greater: 1/3 or 2/7?", "options": [{"id": "iwf4opt1", "text": "1/3", "is_correct": true, "feedback_correct": "Correct! 1/3 = 7/21 and 2/7 = 6/21. So 1/3 is greater.", "feedback_incorrect": "Find a common denominator (e.g., 21)."}, {"id": "iwf4opt2", "text": "2/7", "is_correct": false, "feedback_incorrect": "Find a common denominator (e.g., 21). 1/3 = 7/21. What is 2/7 as something/21?"}], "action_button_text": "Adding & Subtracting"}}}, {"id": "iwf-screen5-add-subtract-fractions", "type": "lesson_screen", "order": 5, "estimatedTimeSeconds": 90, "content": {"headline": "Adding and Subtracting Fractions", "body_md": "To add or subtract fractions, they MUST have a common denominator.\n1. Find a common denominator.\n2. Convert the fractions.\n3. Add or subtract the numerators.\n4. Keep the denominator the same.\n\nExample: 1/4 + 2/3\nCommon denominator is 12.\n1/4 = 3/12.  2/3 = 8/12.\n3/12 + 8/12 = (3+8)/12 = 11/12.", "visual": {"type": "giphy_search", "value": "adding pieces puzzle"}, "interactive_element": {"type": "button", "text": "What about multiplying?", "action": "next_screen"}}}, {"id": "iwf-screen6-multiply-divide-fractions", "type": "lesson_screen", "order": 6, "estimatedTimeSeconds": 80, "content": {"headline": "Multiplying and Dividing Fractions", "body_md": "**Multiplying:** Easy! Multiply numerators together, and denominators together.\n(a/b) × (c/d) = (a×c)/(b×d)\n\n**Dividing:** 'Keep, Change, Flip!' Keep the first fraction, change division to multiplication, flip the second fraction.\n(a/b) ÷ (c/d) = (a/b) × (d/c)", "interactive_element": {"type": "text_input", "question_text": "What is (1/2) × (3/5)? (Answer as x/y)", "correct_answer_regex": "^3/10$", "feedback_correct": "Spot on! (1×3)/(2×5) = 3/10.", "feedback_incorrect": "Multiply tops, multiply bottoms.", "action_button_text": "Recap Fractions!"}}}, {"id": "iwf-screen7-recap", "type": "lesson_screen", "order": 7, "estimatedTimeSeconds": 40, "content": {"headline": "Recap: Fraction Fun!", "body_md": "*   Fractions represent parts of a whole.\n*   Equivalent fractions have the same value.\n*   Common denominators are key for adding, subtracting, and comparing.\n*   Multiplying is straight across; dividing uses 'Keep, Change, Flip'.", "interactive_element": {"type": "button", "text": "On to the Module Test!", "action": "next_lesson"}}}]}, {"id": "numerical-playground-test", "title": "Numerical Playground", "description": "Engage with interactive number puzzles and demonstrate your intuitive understanding.", "order": 6, "type": "module_test_interactive", "estimatedTimeMinutes": 10, "contentBlocks": [{"id": "npt-q0-intro", "type": "question_screen", "order": 1, "estimatedTimeSeconds": 20, "content": {"headline": "Welcome to the Numerical Playground!", "body_md": "Time to play with numbers and show off your new skills! Solve the puzzles to conquer the playground.", "visual": {"type": "giphy_search", "value": "playground fun numbers"}, "interactive_element": {"type": "button", "text": "Start Puzzles!", "action": "next_screen"}}}, {"id": "npt-q1-primes-divisibility", "type": "question_screen", "order": 2, "estimatedTimeSeconds": 120, "content": {"headline": "Puzzle 1: Prime & Divisible", "body_md": "Which of the following numbers is a prime number AND is NOT divisible by 3?\n\nA) 9\nB) 17\nC) 21\nD) 2", "interactive_element": {"type": "multiple_choice_text", "options": [{"id": "nptq1optA", "text": "A) 9", "is_correct": false, "feedback_incorrect": "9 is divisible by 3 (and not prime)."}, {"id": "nptq1optB", "text": "B) 17", "is_correct": true, "feedback_correct": "Correct! 17 is prime. Sum of digits 1+7=8 (not div by 3).", "feedback_incorrect": "Check for primality first, then divisibility by 3. Remember 2 is prime!"}, {"id": "nptq1optC", "text": "C) 21", "is_correct": false, "feedback_incorrect": "21 is divisible by 3 (and not prime)."}, {"id": "nptq1optD", "text": "D) 2", "is_correct": false, "feedback_incorrect": "2 is prime and not divisible by 3, that's true! But is there another option that also fits and is perhaps a more 'complex' prime?"}], "action_button_text": "Next Puzzle"}}}, {"id": "npt-q2-mental-math-bases", "type": "question_screen", "order": 3, "estimatedTimeSeconds": 150, "content": {"headline": "Puzzle 2: Quick Calculations", "body_md": "Mentally calculate: (38 × 5) + Binary 101₂\n\n(Remember 101₂ = (1×2²) + (0×2¹) + (1×2⁰))", "interactive_element": {"type": "interactive_calculator_widget", "data": {"title": "Mental Math with Different Bases", "description": "Calculate (38 × 5) + Binary 101₂", "type": "step_by_step", "problem": "(38 × 5) + <PERSON><PERSON> 101₂", "steps": ["First, calculate 38 × 5 using the shortcut: 38 × 10 ÷ 2", "38 × 10 = 380", "380 ÷ 2 = 190", "Next, convert binary 101₂ to decimal", "(1 × 2²) + (0 × 2¹) + (1 × 2⁰) = 4 + 0 + 1 = 5", "Finally, add the results: 190 + 5 = 195"], "solution": "195", "showNameTag": false}, "action_button_text": "Fraction Challenge!"}}}, {"id": "npt-q3-number-bases", "type": "question_screen", "order": 4, "estimatedTimeSeconds": 120, "content": {"headline": "Puzzle 3: Base Conversion Challenge", "body_md": "Convert the hexadecimal number 2F₁₆ to binary.", "interactive_element": {"type": "interactive_number_base_converter", "data": {"title": "Hex to Binary Converter", "primaryColor": "#9C27B0", "secondaryColor": "#4CAF50", "accentColor": "#FF9800", "showSteps": true, "defaultInputValue": "2F", "defaultFromBase": 16, "defaultToBase": 2, "showNameTag": false}, "action_button_text": "Fraction Challenge!"}}}, {"id": "npt-q4-fractions", "type": "question_screen", "order": 5, "estimatedTimeSeconds": 150, "content": {"headline": "Puzzle 4: Fraction Action", "body_md": "Simplify the following expression: (3/4 + 1/6) ÷ 5/12\n\nGive your answer as a fraction (e.g., a/b) or a whole number.", "interactive_element": {"type": "text_input", "placeholder": "Enter simplified answer", "correct_answer_regex": "^(11/5|2\\.2|2 1/5)$", "feedback_correct": "Excellent work! 3/4 + 1/6 = 9/12 + 2/12 = 11/12. Then (11/12) ÷ (5/12) = (11/12) × (12/5) = 11/5.", "feedback_incorrect": "First, find a common denominator for 3/4 and 1/6 (e.g., 12). Add them. Then, to divide by 5/12, multiply by 12/5.", "action_button_text": "Finish Playground"}}}, {"id": "npt-q5-end", "type": "lesson_screen", "order": 6, "estimatedTimeSeconds": 30, "content": {"headline": "Playground Mastered!", "body_md": "You've shown great number sense and intuition! You've mastered prime numbers, divisibility rules, mental math shortcuts, different number bases, and fractions. Keep practicing these skills, and you'll become even more fluent with numbers.", "visual": {"type": "giphy_search", "value": "math genius celebration"}, "interactive_element": {"type": "button", "text": "Back to Course Overview", "action": "module_complete"}}}]}]}