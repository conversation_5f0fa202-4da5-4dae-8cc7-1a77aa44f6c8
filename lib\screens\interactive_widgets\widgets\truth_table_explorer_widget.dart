import 'package:flutter/material.dart';

/// A widget that allows users to explore and interact with truth tables
/// for logical propositions.
class TruthTableExplorerWidget extends StatefulWidget {
  /// The data for this widget
  final Map<String, dynamic> data;

  /// Optional callback when the widget state changes
  final Function(bool isCompleted)? onStateChanged;

  const TruthTableExplorerWidget({
    Key? key,
    required this.data,
    this.onStateChanged,
  }) : super(key: key);

  /// Factory constructor to create the widget from JSON data
  factory TruthTableExplorerWidget.fromData(Map<String, dynamic> data) {
    return TruthTableExplorerWidget(
      data: data,
    );
  }

  @override
  State<TruthTableExplorerWidget> createState() => _TruthTableExplorerWidgetState();
}

class _TruthTableExplorerWidgetState extends State<TruthTableExplorerWidget> {
  // State variables
  bool _isCompleted = false;
  late String _proposition;
  late List<String> _variables;
  late List<List<bool>> _truthCombinations;
  late List<bool?> _userAnswers;
  late bool _showFeedback = false;
  late bool _allCorrect = false;
  late List<bool> _correctAnswers;
  late String _explanation;

  @override
  void initState() {
    super.initState();
    
    // Initialize from data
    _proposition = widget.data['proposition'] ?? 'P → Q';
    _variables = List<String>.from(widget.data['variables'] ?? ['P', 'Q']);
    _explanation = widget.data['explanation'] ?? '';
    
    // Generate truth combinations (all possible combinations of true/false for variables)
    _generateTruthCombinations();
    
    // Initialize user answers as null (not answered yet)
    _userAnswers = List<bool?>.filled(_truthCombinations.length, null);
    
    // Get correct answers from data or calculate them
    if (widget.data.containsKey('correctAnswers')) {
      _correctAnswers = List<bool>.from(widget.data['correctAnswers']);
    } else {
      _correctAnswers = _calculateCorrectAnswers();
    }
  }

  /// Generates all possible combinations of truth values for the variables
  void _generateTruthCombinations() {
    final int numVariables = _variables.length;
    final int numCombinations = 1 << numVariables; // 2^numVariables
    
    _truthCombinations = List.generate(numCombinations, (index) {
      return List.generate(numVariables, (varIndex) {
        // Use bitwise operations to generate all combinations
        return ((index >> (numVariables - varIndex - 1)) & 1) == 1;
      });
    });
  }

  /// Calculates the correct answers based on the proposition
  List<bool> _calculateCorrectAnswers() {
    // This is a simplified implementation that handles only P → Q
    // A more complete implementation would parse and evaluate arbitrary propositions
    
    if (_proposition == 'P → Q' && _variables.length == 2) {
      return _truthCombinations.map((combination) {
        final p = combination[0];
        final q = combination[1];
        // P → Q is false only when P is true and Q is false
        return !(p && !q);
      }).toList();
    }
    
    // Default to all true if we can't calculate
    return List.filled(_truthCombinations.length, true);
  }

  /// Checks if all user answers are correct
  void _checkAnswers() {
    // Check if all questions are answered
    if (_userAnswers.contains(null)) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('Please answer all rows in the truth table'),
          backgroundColor: Colors.orange,
        ),
      );
      return;
    }
    
    // Check if all answers are correct
    _allCorrect = true;
    for (int i = 0; i < _correctAnswers.length; i++) {
      if (_userAnswers[i] != _correctAnswers[i]) {
        _allCorrect = false;
        break;
      }
    }
    
    setState(() {
      _showFeedback = true;
      _isCompleted = _allCorrect;
    });
    
    if (widget.onStateChanged != null) {
      widget.onStateChanged!(_isCompleted);
    }
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withOpacity(0.2),
            spreadRadius: 1,
            blurRadius: 3,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Title and proposition
          Text(
            'Truth Table Explorer',
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
              color: Colors.blue[800],
            ),
          ),
          const SizedBox(height: 8),
          Container(
            padding: const EdgeInsets.all(12),
            decoration: BoxDecoration(
              color: Colors.blue[50],
              borderRadius: BorderRadius.circular(8),
              border: Border.all(color: Colors.blue[200]!),
            ),
            child: Text(
              'Proposition: $_proposition',
              style: const TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.bold,
                fontFamily: 'monospace',
              ),
            ),
          ),
          const SizedBox(height: 16),
          
          // Truth table
          _buildTruthTable(),
          const SizedBox(height: 16),
          
          // Action buttons
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              // Reset button
              if (_showFeedback)
                ElevatedButton.icon(
                  onPressed: () {
                    setState(() {
                      _userAnswers = List<bool?>.filled(_truthCombinations.length, null);
                      _showFeedback = false;
                    });
                  },
                  icon: const Icon(Icons.refresh),
                  label: const Text('Try Again'),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Colors.grey[300],
                    foregroundColor: Colors.black87,
                  ),
                ),
              
              // Check answers button
              if (!_showFeedback)
                ElevatedButton.icon(
                  onPressed: _checkAnswers,
                  icon: const Icon(Icons.check),
                  label: const Text('Check Answers'),
                ),
            ],
          ),
          
          // Feedback
          if (_showFeedback) ...[
            const SizedBox(height: 16),
            Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: _allCorrect ? Colors.green[50] : Colors.red[50],
                borderRadius: BorderRadius.circular(8),
                border: Border.all(
                  color: _allCorrect ? Colors.green[300]! : Colors.red[300]!,
                ),
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    _allCorrect ? 'Correct!' : 'Not quite right',
                    style: TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.bold,
                      color: _allCorrect ? Colors.green[700] : Colors.red[700],
                    ),
                  ),
                  const SizedBox(height: 8),
                  Text(
                    _allCorrect
                        ? 'Great job! You\'ve correctly filled in the truth table.'
                        : 'Some answers are incorrect. Try again!',
                  ),
                  if (_allCorrect && _explanation.isNotEmpty) ...[
                    const SizedBox(height: 8),
                    const Text(
                      'Explanation:',
                      style: TextStyle(
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    Text(_explanation),
                  ],
                ],
              ),
            ),
          ],
          
          // Widget name tag at the bottom
          if (widget.data['showNameTag'] ?? true)
            Padding(
              padding: const EdgeInsets.only(top: 8),
              child: Text(
                'TruthTableExplorerWidget',
                style: TextStyle(
                  color: Colors.grey[600],
                  fontSize: 12,
                  fontStyle: FontStyle.italic,
                ),
              ),
            ),
        ],
      ),
    );
  }

  /// Builds the truth table UI
  Widget _buildTruthTable() {
    return Table(
      border: TableBorder.all(
        color: Colors.grey[300]!,
        width: 1,
      ),
      columnWidths: {
        for (int i = 0; i < _variables.length; i++)
          i: const FlexColumnWidth(1),
        _variables.length: const FlexColumnWidth(1.5),
      },
      children: [
        // Header row
        TableRow(
          decoration: BoxDecoration(
            color: Colors.grey[200],
          ),
          children: [
            ..._variables.map((variable) => _buildTableCell(
              variable,
              isHeader: true,
            )),
            _buildTableCell(
              _proposition,
              isHeader: true,
            ),
          ],
        ),
        // Data rows
        ..._truthCombinations.asMap().entries.map((entry) {
          final index = entry.key;
          final combination = entry.value;
          
          return TableRow(
            decoration: BoxDecoration(
              color: _showFeedback && _userAnswers[index] != null
                  ? _userAnswers[index] == _correctAnswers[index]
                      ? Colors.green[50]
                      : Colors.red[50]
                  : null,
            ),
            children: [
              ...combination.map((value) => _buildTableCell(
                value ? 'T' : 'F',
                isCenter: true,
              )),
              _buildAnswerCell(index),
            ],
          );
        }),
      ],
    );
  }

  /// Builds a standard table cell
  Widget _buildTableCell(String text, {bool isHeader = false, bool isCenter = false}) {
    return TableCell(
      child: Padding(
        padding: const EdgeInsets.symmetric(vertical: 8, horizontal: 4),
        child: Text(
          text,
          style: TextStyle(
            fontWeight: isHeader ? FontWeight.bold : FontWeight.normal,
          ),
          textAlign: isCenter ? TextAlign.center : TextAlign.left,
        ),
      ),
    );
  }

  /// Builds an interactive answer cell
  Widget _buildAnswerCell(int rowIndex) {
    return TableCell(
      child: Padding(
        padding: const EdgeInsets.all(4),
        child: _showFeedback
            ? Container(
                padding: const EdgeInsets.symmetric(vertical: 8),
                alignment: Alignment.center,
                decoration: BoxDecoration(
                  color: _userAnswers[rowIndex] == _correctAnswers[rowIndex]
                      ? Colors.green[100]
                      : Colors.red[100],
                  borderRadius: BorderRadius.circular(4),
                ),
                child: Text(
                  _userAnswers[rowIndex] == true ? 'T' : 'F',
                  style: TextStyle(
                    fontWeight: FontWeight.bold,
                    color: _userAnswers[rowIndex] == _correctAnswers[rowIndex]
                        ? Colors.green[800]
                        : Colors.red[800],
                  ),
                ),
              )
            : Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  _buildAnswerButton(rowIndex, true),
                  const SizedBox(width: 8),
                  _buildAnswerButton(rowIndex, false),
                ],
              ),
      ),
    );
  }

  /// Builds a T/F answer button
  Widget _buildAnswerButton(int rowIndex, bool value) {
    final isSelected = _userAnswers[rowIndex] == value;
    
    return InkWell(
      onTap: () {
        setState(() {
          _userAnswers[rowIndex] = value;
        });
      },
      child: Container(
        width: 30,
        padding: const EdgeInsets.symmetric(vertical: 4),
        decoration: BoxDecoration(
          color: isSelected ? Colors.blue : Colors.grey[200],
          borderRadius: BorderRadius.circular(4),
          border: Border.all(
            color: isSelected ? Colors.blue[700]! : Colors.grey[400]!,
          ),
        ),
        alignment: Alignment.center,
        child: Text(
          value ? 'T' : 'F',
          style: TextStyle(
            fontWeight: isSelected ? FontWeight.bold : FontWeight.normal,
            color: isSelected ? Colors.white : Colors.black87,
          ),
        ),
      ),
    );
  }
}
