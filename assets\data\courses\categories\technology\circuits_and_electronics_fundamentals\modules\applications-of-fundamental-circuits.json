{"id": "applications-of-fundamental-circuits", "title": "Applications of Fundamental Circuits", "description": "Explore practical applications of the circuit principles learned.", "order": 5, "lessons": [{"id": "power-supplies", "title": "Power Supplies: Converting AC to DC", "description": "Understand the basics of rectification and filtering.", "order": 1, "type": "interactive_lesson", "estimatedTimeMinutes": 20, "contentBlocks": [{"id": "power-supplies-screen-1", "type": "lesson_screen", "order": 1, "estimatedTimeSeconds": 60, "content": {"headline": "From Wall Socket to Device: The Power Supply", "body_md": "Most electronic devices need **Direct Current (DC)** to operate, but our power grid delivers **Alternating Current (AC)**. How do we convert between them?\n\nA **power supply** is a circuit that transforms the AC from your wall outlet into the stable DC voltage that electronic devices need.", "visual": {"type": "unsplash_search", "value": "power supply adapter electronics"}, "interactive_element": {"type": "button", "button_text": "Why Do We Need Conversion?", "action": "next_screen"}}}, {"id": "power-supplies-screen-2", "type": "lesson_screen", "order": 2, "estimatedTimeSeconds": 90, "content": {"headline": "AC vs. DC: Why Convert?", "body_md": "Electronic components like microchips, transistors, and integrated circuits are designed to work with **steady DC voltage**. They can't function properly with the constantly changing polarity of AC.\n\nHowever, our power grid uses AC because it's more efficient for:\n- Long-distance transmission\n- Voltage transformation using transformers\n- Power generation at power plants\n\nThis creates the need for AC-to-DC conversion in almost every electronic device.", "visual": {"type": "giphy_search", "value": "AC DC current animation"}, "interactive_element": {"type": "multiple_choice_text", "question": "Why do most electronic devices require DC rather than AC?", "options": [{"id": "opt1", "text": "DC is more powerful than AC", "is_correct": false, "feedback": "Power isn't inherently different between AC and DC - they're just different forms of electrical energy."}, {"id": "opt2", "text": "DC is safer than AC for small devices", "is_correct": false, "feedback": "Safety depends on voltage levels and design, not whether it's AC or DC."}, {"id": "opt3", "text": "Electronic components like transistors and ICs are designed to operate with steady voltages", "is_correct": true, "feedback": "Correct! Most semiconductor components require stable DC voltage to function properly. The constantly changing polarity of AC would cause unpredictable behavior."}, {"id": "opt4", "text": "DC is more energy-efficient in all applications", "is_correct": false, "feedback": "AC is actually more efficient for power transmission, which is why our grid uses it."}]}}}, {"id": "power-supplies-screen-3", "type": "lesson_screen", "order": 3, "estimatedTimeSeconds": 90, "content": {"headline": "Step 1: Transformation", "body_md": "A typical power supply has several stages. The first is usually a **transformer**.\n\nThe transformer:\n- Isolates the device from the power line (safety)\n- Steps down the voltage (typically from 120V/240V AC to a lower voltage)\n- Uses electromagnetic induction with no direct electrical connection\n\nThis is why power adapters are often heavy - they contain a transformer with copper windings around an iron core.", "visual": {"type": "unsplash_search", "value": "transformer electronics"}, "interactive_element": {"type": "button", "button_text": "Next: Rectification", "action": "next_screen"}}}, {"id": "power-supplies-screen-4", "type": "lesson_screen", "order": 4, "estimatedTimeSeconds": 120, "content": {"headline": "Step 2: Rectification", "body_md": "**Rectification** is the process of converting AC to pulsating DC by allowing current to flow in only one direction.\n\nThis is accomplished using **diodes**, which act like one-way valves for electricity.\n\nThere are two main types of rectifiers:\n- **Half-wave rectifier**: Uses a single diode to pass only the positive half of the AC waveform\n- **Full-wave rectifier**: Uses multiple diodes (typically four in a 'bridge' configuration) to convert both positive and negative halves of the AC waveform to positive pulses", "visual": {"type": "giphy_search", "value": "full wave rectifier animation"}, "interactive_element": {"type": "multiple_choice_text", "question": "Which type of rectifier is more efficient for power supplies?", "options": [{"id": "opt1", "text": "Half-wave rectifier", "is_correct": false, "feedback": "Half-wave rectifiers only use half of the input waveform, wasting the other half."}, {"id": "opt2", "text": "Full-wave rectifier", "is_correct": true, "feedback": "Correct! Full-wave rectifiers utilize both the positive and negative portions of the AC cycle, making them much more efficient. They produce a smoother output with less ripple."}, {"id": "opt3", "text": "They are equally efficient", "is_correct": false, "feedback": "Full-wave rectifiers are significantly more efficient as they use the entire input waveform."}, {"id": "opt4", "text": "Neither - rectifiers aren't used in modern power supplies", "is_correct": false, "feedback": "Rectifiers are still fundamental components in modern power supplies, with full-wave rectifiers being the standard."}]}}}, {"id": "power-supplies-screen-5", "type": "lesson_screen", "order": 5, "estimatedTimeSeconds": 120, "content": {"headline": "Step 3: Filtering", "body_md": "After rectification, we have pulsating DC - better than AC for electronics, but still not steady enough.\n\n**Filtering** smooths out these pulses using capacitors. A capacitor acts like a tiny rechargeable battery:\n- It charges when voltage is high\n- It discharges when voltage drops\n- This helps maintain a more constant voltage\n\nLarger capacitors provide better filtering but take longer to charge initially.", "visual": {"type": "giphy_search", "value": "capacitor filter circuit animation"}, "interactive_element": {"type": "multiple_choice_text", "question": "What component is primarily responsible for filtering in a basic power supply?", "options": [{"id": "opt1", "text": "Resistor", "is_correct": false, "feedback": "While resistors are used in power supplies, they don't store energy to smooth voltage."}, {"id": "opt2", "text": "Diode", "is_correct": false, "feedback": "Diodes are used for rectification, not filtering."}, {"id": "opt3", "text": "Capacitor", "is_correct": true, "feedback": "Correct! Capacitors store energy when voltage is high and release it when voltage drops, smoothing out the pulsating DC into a more steady voltage."}, {"id": "opt4", "text": "Transformer", "is_correct": false, "feedback": "Transformers change voltage levels and provide isolation, but don't filter the rectified DC."}]}}}, {"id": "power-supplies-screen-6", "type": "lesson_screen", "order": 6, "estimatedTimeSeconds": 90, "content": {"headline": "Step 4: Regulation", "body_md": "Even after filtering, the DC voltage can still vary with changes in load or input voltage. **Voltage regulation** maintains a constant output voltage regardless of these changes.\n\nRegulators can be:\n- **Linear regulators**: Simple but less efficient (e.g., 7805 for 5V output)\n- **Switching regulators**: More complex but highly efficient\n\nModern devices often use switching regulators to minimize heat generation and maximize battery life.", "visual": {"type": "unsplash_search", "value": "voltage regulator circuit"}, "interactive_element": {"type": "button", "button_text": "Modern Power Supplies", "action": "next_screen"}}}, {"id": "power-supplies-screen-7", "type": "lesson_screen", "order": 7, "estimatedTimeSeconds": 90, "content": {"headline": "Modern Power Supplies: SMPS", "body_md": "Most modern devices use **Switch-Mode Power Supplies (SMPS)** which:\n\n- Are much smaller and lighter than traditional supplies\n- Operate at high frequencies (20kHz-1MHz)\n- Use smaller transformers\n- Achieve 80-95% efficiency (vs. 50-60% for linear supplies)\n- Generate less heat\n\nYour phone charger, laptop adapter, and computer power supply are all examples of SMPS technology.", "visual": {"type": "unsplash_search", "value": "phone charger adapter"}, "interactive_element": {"type": "multiple_choice_text", "question": "Why are modern phone chargers so much smaller than older power adapters?", "options": [{"id": "opt1", "text": "They deliver less power", "is_correct": false, "feedback": "Many modern chargers actually deliver more power than older ones (e.g., fast charging)."}, {"id": "opt2", "text": "They use switch-mode technology with high-frequency operation", "is_correct": true, "feedback": "Correct! Switch-mode power supplies operate at much higher frequencies (kHz to MHz range), allowing the use of much smaller transformers and components."}, {"id": "opt3", "text": "They're less durable by design", "is_correct": false, "feedback": "While some may be less durable, this isn't the reason for their smaller size."}, {"id": "opt4", "text": "They don't include isolation transformers", "is_correct": false, "feedback": "Modern chargers still include isolation for safety, but use high-frequency transformers that are much smaller."}]}}}, {"id": "power-supplies-screen-8", "type": "lesson_screen", "order": 8, "estimatedTimeSeconds": 60, "content": {"headline": "Power Supply Summary", "body_md": "You've now learned how power supplies convert AC to DC:\n\n1. **Transformation**: Step down voltage and isolate\n2. **Rectification**: Convert AC to pulsating DC using diodes\n3. **Filtering**: Smooth the pulsating DC with capacitors\n4. **Regulation**: Maintain steady voltage regardless of load\n\nThese principles apply to everything from tiny USB chargers to massive industrial power supplies!", "visual": {"type": "giphy_search", "value": "electronics power success"}, "interactive_element": {"type": "button", "button_text": "Finish Lesson", "action": "complete_lesson"}}}]}, {"id": "amplifiers-basic-concepts", "title": "Amplifiers (Basic Concepts)", "description": "See how transistors can increase signal strength.", "order": 2, "type": "interactive_lesson", "estimatedTimeMinutes": 20, "contentBlocks": [{"id": "amplifiers-screen-1", "type": "lesson_screen", "order": 1, "estimatedTimeSeconds": 60, "content": {"headline": "Making Signals Stronger: Amplifiers", "body_md": "An **amplifier** is a circuit that increases the power, voltage, or current of a signal. They're essential in almost every electronic device, from smartphones to hearing aids to guitar amps.\n\nAmplifiers take a weak input signal and produce a stronger output signal with the same pattern but greater amplitude.", "visual": {"type": "giphy_search", "value": "audio amplifier waveform"}, "interactive_element": {"type": "button", "button_text": "Why Do We Need Amplifiers?", "action": "next_screen"}}}, {"id": "amplifiers-screen-2", "type": "lesson_screen", "order": 2, "estimatedTimeSeconds": 90, "content": {"headline": "The Need for Amplification", "body_md": "Amplifiers are necessary because many signal sources are too weak for practical use:\n\n- **Microphones** produce tiny voltages from sound waves\n- **Antennas** capture very weak radio signals\n- **Sensors** often generate small electrical changes\n- **Audio players** need to drive speakers\n\nWithout amplification, these signals would be too weak to be useful or even detectable.", "visual": {"type": "unsplash_search", "value": "audio amplifier electronics"}, "interactive_element": {"type": "multiple_choice_text", "question": "Which of these is NOT a common reason for using an amplifier?", "options": [{"id": "opt1", "text": "To increase the power of audio signals to drive speakers", "is_correct": false, "feedback": "This is a primary use of amplifiers in audio equipment."}, {"id": "opt2", "text": "To boost weak radio signals received by antennas", "is_correct": false, "feedback": "Radio receivers use amplifiers to boost the tiny signals captured by antennas."}, {"id": "opt3", "text": "To convert AC to DC power", "is_correct": true, "feedback": "Correct! Converting AC to DC is done by power supplies (rectifiers, filters, and regulators), not amplifiers. Amplifiers maintain the signal pattern while increasing its strength."}, {"id": "opt4", "text": "To make sensor outputs strong enough for measurement", "is_correct": false, "feedback": "Many sensors produce very small electrical changes that need amplification to be useful."}]}}}, {"id": "amplifiers-screen-3", "type": "lesson_screen", "order": 3, "estimatedTimeSeconds": 90, "content": {"headline": "The Heart of Amplifiers: Transistors", "body_md": "Most modern amplifiers use **transistors** as their active components. Transistors are semiconductor devices that can:\n\n- Control a large current with a small one (like an electrically controlled valve)\n- Take a small input signal and produce a larger output signal\n- Act as electronic switches or variable resistors\n\nTransistors come in two main types: **Bipolar Junction Transistors (BJTs)** and **Field-Effect Transistors (FETs)**.", "visual": {"type": "unsplash_search", "value": "transistor electronic component"}, "interactive_element": {"type": "button", "button_text": "How Transistors Amplify", "action": "next_screen"}}}, {"id": "amplifiers-screen-4", "type": "lesson_screen", "order": 4, "estimatedTimeSeconds": 120, "content": {"headline": "How Transistors Amplify Signals", "body_md": "A transistor amplifies by controlling a large current (from a power supply) with a small input signal:\n\n1. A small input signal is applied to the transistor's control terminal (base or gate)\n2. This small signal modulates how much current flows through the transistor\n3. The output is taken from a circuit that converts these current changes to voltage changes\n4. The power for amplification comes from the DC power supply, not the input signal\n\nThis is like using a small movement of your hand on a water valve to control a much larger flow of water.", "visual": {"type": "giphy_search", "value": "transistor amplifier animation"}, "interactive_element": {"type": "multiple_choice_text", "question": "Where does the extra power in an amplified signal come from?", "options": [{"id": "opt1", "text": "It's created by the transistor itself", "is_correct": false, "feedback": "Transistors don't create energy; they control the flow of existing energy."}, {"id": "opt2", "text": "From the DC power supply connected to the amplifier", "is_correct": true, "feedback": "Correct! The amplifier draws power from its DC power supply (like a battery or power adapter) and uses the input signal to control how this power is delivered to the output."}, {"id": "opt3", "text": "It's extracted from the input signal", "is_correct": false, "feedback": "The input signal doesn't contain enough energy to power the output - that would violate conservation of energy."}, {"id": "opt4", "text": "From capacitors that store energy", "is_correct": false, "feedback": "While capacitors may be used in amplifier circuits, they're not the primary source of the additional power."}]}}}, {"id": "amplifiers-screen-5", "type": "lesson_screen", "order": 5, "estimatedTimeSeconds": 90, "content": {"headline": "Amplifier Characteristics: <PERSON><PERSON>", "body_md": "The most important characteristic of an amplifier is its **gain** - the ratio of output to input:\n\n- **Voltage gain (Av)** = Output voltage / Input voltage\n- **Current gain (Ai)** = Output current / Input current\n- **Power gain (Ap)** = Output power / Input power\n\nFor example, an amplifier with a voltage gain of 10 will output a 10V signal when given a 1V input signal.", "visual": {"type": "static_text", "value": "Gain = Output / Input"}, "interactive_element": {"type": "multiple_choice_text", "question": "If an amplifier has a voltage gain of 20 and receives a 0.1V input signal, what will be the output voltage?", "options": [{"id": "opt1", "text": "0.005V", "is_correct": false, "feedback": "This would be division, not multiplication."}, {"id": "opt2", "text": "2V", "is_correct": true, "feedback": "Correct! Output voltage = Input voltage × Gain = 0.1V × 20 = 2V"}, {"id": "opt3", "text": "20V", "is_correct": false, "feedback": "This would be correct if the input were 1V, but it's only 0.1V."}, {"id": "opt4", "text": "0.2V", "is_correct": false, "feedback": "This would be a gain of 2, not 20."}]}}}, {"id": "amplifiers-screen-6", "type": "lesson_screen", "order": 6, "estimatedTimeSeconds": 90, "content": {"headline": "Types of Amplifiers", "body_md": "Amplifiers come in many specialized types:\n\n- **Voltage amplifiers**: Increase voltage (e.g., preamplifiers)\n- **Power amplifiers**: Deliver significant power to loads like speakers\n- **Current amplifiers**: Increase current for driving low-impedance loads\n- **Operational amplifiers (Op-amps)**: Versatile integrated circuit amplifiers\n- **Instrumentation amplifiers**: Precise amplification for sensors and measurements\n- **Audio amplifiers**: Optimized for human hearing range\n- **RF amplifiers**: For radio frequency signals in communications", "visual": {"type": "unsplash_search", "value": "audio amplifier equipment"}, "interactive_element": {"type": "button", "button_text": "Amplifier Limitations", "action": "next_screen"}}}, {"id": "amplifiers-screen-7", "type": "lesson_screen", "order": 7, "estimatedTimeSeconds": 90, "content": {"headline": "Amplifier Limitations and Distortion", "body_md": "Real amplifiers have limitations:\n\n- **Bandwidth**: The range of frequencies they can amplify effectively\n- **Slew rate**: How quickly the output can change\n- **Distortion**: Unwanted changes to the signal shape\n- **Noise**: Random unwanted signals added to the output\n- **Clipping**: When the output tries to exceed the power supply voltage\n\nWhen pushed beyond their limits, amplifiers distort the signal - sometimes deliberately (like in guitar distortion) but usually undesirably.", "visual": {"type": "giphy_search", "value": "audio waveform clipping"}, "interactive_element": {"type": "multiple_choice_text", "question": "What happens when an amplifier is driven beyond its maximum output capability?", "options": [{"id": "opt1", "text": "It automatically reduces its gain", "is_correct": false, "feedback": "Most basic amplifiers don't have automatic gain control."}, {"id": "opt2", "text": "It shuts down to protect itself", "is_correct": false, "feedback": "While some amplifiers have protection circuits, this isn't the immediate result of exceeding capabilities."}, {"id": "opt3", "text": "Clipping occurs, where the peaks of the waveform are flattened", "is_correct": true, "feedback": "Correct! When an amplifier tries to output a voltage beyond its power supply limits, the waveform gets 'clipped' at the maximum voltage, creating distortion."}, {"id": "opt4", "text": "The frequency of the signal changes", "is_correct": false, "feedback": "Exceeding an amplifier's capabilities typically affects amplitude, not frequency."}]}}}, {"id": "amplifiers-screen-8", "type": "lesson_screen", "order": 8, "estimatedTimeSeconds": 60, "content": {"headline": "Amplifiers: Bringing Signals to Life", "body_md": "You've now learned the basics of amplifiers:\n\n- They increase signal strength using transistors\n- The extra power comes from the power supply\n- Gain is the ratio of output to input\n- Different types serve different purposes\n- Real amplifiers have limitations\n\nAmplifiers are essential components that make modern electronics possible, from the tiniest hearing aids to massive concert sound systems.", "visual": {"type": "giphy_search", "value": "electronics success"}, "interactive_element": {"type": "button", "button_text": "Finish Lesson", "action": "complete_lesson"}}}]}, {"id": "filters-selecting-frequencies", "title": "Filters: Selecting Frequencies", "description": "Understand how circuits can block or pass certain frequencies.", "order": 3, "type": "interactive_lesson", "estimatedTimeMinutes": 20, "contentBlocks": [{"id": "filters-screen-1", "type": "lesson_screen", "order": 1, "estimatedTimeSeconds": 60, "content": {"headline": "Filters: Choosing Which Frequencies Pass", "body_md": "**Filters** are circuits that allow certain frequencies to pass while blocking others. They're essential in countless applications:\n\n- Audio systems (bass/treble controls)\n- Radio receivers (tuning to stations)\n- Power supplies (smoothing DC)\n- Signal processing (noise reduction)\n- Communications (channel selection)", "visual": {"type": "giphy_search", "value": "audio equalizer frequency"}, "interactive_element": {"type": "button", "button_text": "Why Do We Need Filters?", "action": "next_screen"}}}, {"id": "filters-screen-2", "type": "lesson_screen", "order": 2, "estimatedTimeSeconds": 90, "content": {"headline": "The Need for Filtering", "body_md": "In the real world, signals often contain unwanted frequencies:\n\n- **Noise**: Random unwanted signals\n- **Interference**: Unwanted signals from other sources\n- **Harmonics**: Multiples of the fundamental frequency\n- **Mixed signals**: Multiple channels or stations\n\nFilters help us extract just the frequencies we want and reject the rest, improving signal quality and enabling specific applications.", "visual": {"type": "unsplash_search", "value": "audio spectrum analyzer"}, "interactive_element": {"type": "multiple_choice_text", "question": "Which of these is NOT a common application of filters?", "options": [{"id": "opt1", "text": "Tuning a radio to a specific station", "is_correct": false, "feedback": "Filters are essential in radio tuning to select one station's frequency band from many."}, {"id": "opt2", "text": "Increasing the voltage of a signal", "is_correct": true, "feedback": "Correct! Increasing voltage is the job of an amplifier, not a filter. Filters select frequencies but don't inherently increase signal strength."}, {"id": "opt3", "text": "Reducing noise in audio recordings", "is_correct": false, "feedback": "Noise reduction often uses filters to remove frequencies where noise is predominant."}, {"id": "opt4", "text": "Separating bass and treble in audio systems", "is_correct": false, "feedback": "Audio equalizers use filters to separate and adjust different frequency bands."}]}}}, {"id": "filters-screen-3", "type": "lesson_screen", "order": 3, "estimatedTimeSeconds": 90, "content": {"headline": "Types of Filters", "body_md": "Filters are categorized by which frequencies they pass or block:\n\n- **Low-pass filter**: Passes low frequencies, blocks high frequencies\n- **High-pass filter**: Passes high frequencies, blocks low frequencies\n- **Band-pass filter**: Passes a range of frequencies, blocks frequencies above and below\n- **Band-stop filter (notch)**: Blocks a range of frequencies, passes frequencies above and below\n- **All-pass filter**: Passes all frequencies but changes phase relationships", "visual": {"type": "giphy_search", "value": "frequency response filter"}, "interactive_element": {"type": "button", "button_text": "Filter Characteristics", "action": "next_screen"}}}, {"id": "filters-screen-4", "type": "lesson_screen", "order": 4, "estimatedTimeSeconds": 120, "content": {"headline": "Key Filter Characteristics", "body_md": "Filters are described by several important characteristics:\n\n- **Cutoff frequency**: Where the filter starts to take effect\n- **Passband**: Range of frequencies that pass through with minimal attenuation\n- **Stopband**: Range of frequencies that are significantly attenuated\n- **Roll-off rate**: How quickly the filter transitions from pass to stop (measured in dB/octave)\n- **Order**: Higher-order filters have steeper roll-off rates\n- **Ripple**: Variations in the response within the passband or stopband", "visual": {"type": "static_text", "value": "Passband → Transition → Stopband"}, "interactive_element": {"type": "multiple_choice_text", "question": "What does a steeper roll-off rate in a filter indicate?", "options": [{"id": "opt1", "text": "The filter passes more frequencies", "is_correct": false, "feedback": "A steeper roll-off doesn't necessarily mean more frequencies are passed."}, {"id": "opt2", "text": "The filter has more ripple in the passband", "is_correct": false, "feedback": "Roll-off rate and ripple are separate characteristics."}, {"id": "opt3", "text": "The filter transitions more sharply from passing to blocking frequencies", "is_correct": true, "feedback": "Correct! A steeper roll-off means the filter transitions more abruptly from passing frequencies to blocking them, creating a more defined cutoff."}, {"id": "opt4", "text": "The filter uses fewer components", "is_correct": false, "feedback": "Steeper roll-offs typically require more components (higher-order filters)."}]}}}, {"id": "filters-screen-5", "type": "lesson_screen", "order": 5, "estimatedTimeSeconds": 120, "content": {"headline": "Passive Filters: RC and RL Circuits", "body_md": "The simplest filters use passive components (resistors, capacitors, inductors):\n\n- **RC low-pass filter**: A resistor followed by a capacitor to ground\n- **RC high-pass filter**: A capacitor followed by a resistor to ground\n- **RL filters**: Similar configurations using inductors instead of capacitors\n- **RLC filters**: Combining all three components for more complex responses\n\nPassive filters are simple but have limitations in roll-off rate and flexibility.", "visual": {"type": "giphy_search", "value": "RC filter circuit"}, "interactive_element": {"type": "multiple_choice_text", "question": "Why does a capacitor block DC but pass AC signals?", "options": [{"id": "opt1", "text": "Capacitors have infinite resistance to DC", "is_correct": false, "feedback": "Capacitors don't have infinite resistance to DC; they have infinite impedance after being fully charged."}, {"id": "opt2", "text": "Capacitors conduct better at higher frequencies due to decreasing reactance", "is_correct": true, "feedback": "Correct! A capacitor's reactance (opposition to current flow) decreases as frequency increases. At DC (0 Hz), the reactance is theoretically infinite, while at high frequencies, it approaches zero."}, {"id": "opt3", "text": "Capacitors amplify AC signals", "is_correct": false, "feedback": "Capacitors don't amplify signals; they pass or block them based on frequency."}, {"id": "opt4", "text": "Capacitors convert DC to AC", "is_correct": false, "feedback": "Capacitors don't convert between DC and AC; they selectively pass or block based on frequency."}]}}}, {"id": "filters-screen-6", "type": "lesson_screen", "order": 6, "estimatedTimeSeconds": 90, "content": {"headline": "Active Filters: Using Amplifiers", "body_md": "**Active filters** combine passive components with amplifiers (usually op-amps) to provide:\n\n- **Steeper roll-off rates**: Sharper transitions between pass and stop bands\n- **Gain**: Can amplify signals while filtering\n- **Buffering**: Isolation between stages\n- **No signal loss**: Can have unity gain or higher in the passband\n- **No loading effects**: High input impedance, low output impedance\n\nActive filters are more complex but offer superior performance for many applications.", "visual": {"type": "unsplash_search", "value": "electronic circuit board"}, "interactive_element": {"type": "button", "button_text": "Real-World Applications", "action": "next_screen"}}}, {"id": "filters-screen-7", "type": "lesson_screen", "order": 7, "estimatedTimeSeconds": 90, "content": {"headline": "Filter Applications in Daily Life", "body_md": "Filters are everywhere in modern technology:\n\n- **Audio equalizers**: Multiple band-pass filters for adjusting sound\n- **Crossover networks**: Directing different frequencies to appropriate speakers\n- **Radio/TV tuners**: Selecting specific broadcast channels\n- **Anti-aliasing filters**: Preventing distortion in digital sampling\n- **Power line filters**: Removing noise from electrical power\n- **DSL/cable internet**: Separating data from voice signals on the same line\n- **Medical devices**: Isolating specific biological signals (EKG, EEG)", "visual": {"type": "unsplash_search", "value": "audio equalizer"}, "interactive_element": {"type": "multiple_choice_text", "question": "In a home stereo system, what type of filter would be used to send only low frequencies to a subwoofer?", "options": [{"id": "opt1", "text": "High-pass filter", "is_correct": false, "feedback": "A high-pass filter would block low frequencies, which is the opposite of what's needed for a subwoofer."}, {"id": "opt2", "text": "Low-pass filter", "is_correct": true, "feedback": "Correct! A low-pass filter allows only low frequencies (typically below 80-120 Hz) to reach the subwoofer while blocking higher frequencies."}, {"id": "opt3", "text": "Band-stop filter", "is_correct": false, "feedback": "A band-stop filter would block a specific range of frequencies, not isolate low frequencies."}, {"id": "opt4", "text": "All-pass filter", "is_correct": false, "feedback": "An all-pass filter would pass all frequencies, not isolate just the low ones needed for a subwoofer."}]}}}, {"id": "filters-screen-8", "type": "lesson_screen", "order": 8, "estimatedTimeSeconds": 60, "content": {"headline": "Filters: Shaping the Frequency World", "body_md": "You've now learned the basics of filters:\n\n- They selectively pass or block frequencies\n- Different types (low-pass, high-pass, band-pass, band-stop)\n- Key characteristics (cutoff frequency, roll-off rate, order)\n- Passive vs. active implementations\n- Countless applications in modern technology\n\nFilters are essential tools that help us shape and control the frequency content of signals in virtually every electronic device.", "visual": {"type": "giphy_search", "value": "audio spectrum visualization"}, "interactive_element": {"type": "button", "button_text": "Finish Lesson", "action": "complete_lesson"}}}]}, {"id": "sensors-transducers-intro", "title": "Sensors and Transducers (Introduction)", "description": "Explore devices that convert physical quantities to electrical signals.", "order": 4, "type": "interactive_lesson", "estimatedTimeMinutes": 20, "contentBlocks": [{"id": "sensors-screen-1", "type": "lesson_screen", "order": 1, "estimatedTimeSeconds": 60, "content": {"headline": "Sensors & Transducers: The Electronic Senses", "body_md": "**Sensors** and **transducers** are devices that convert physical quantities into electrical signals. They act as the 'senses' of electronic systems, allowing them to perceive and respond to the world.\n\nWhile the terms are often used interchangeably, technically a transducer converts any form of energy to another, while a sensor specifically produces an electrical output signal.", "visual": {"type": "unsplash_search", "value": "electronic sensors"}, "interactive_element": {"type": "button", "button_text": "Why Do We Need Sensors?", "action": "next_screen"}}}, {"id": "sensors-screen-2", "type": "lesson_screen", "order": 2, "estimatedTimeSeconds": 90, "content": {"headline": "The Need for Sensing", "body_md": "Sensors bridge the gap between the physical world and electronic systems:\n\n- They allow electronic devices to **measure** physical quantities\n- They enable systems to **respond** to environmental changes\n- They provide **data** for monitoring and control systems\n- They form the foundation of **automation** and **feedback** systems\n\nWithout sensors, electronic systems would be blind, deaf, and unable to interact with their environment.", "visual": {"type": "giphy_search", "value": "robot sensors"}, "interactive_element": {"type": "multiple_choice_text", "question": "Which of these is NOT a common application of sensors?", "options": [{"id": "opt1", "text": "Measuring temperature in a thermostat", "is_correct": false, "feedback": "Temperature sensors are essential components in thermostats."}, {"id": "opt2", "text": "Detecting motion for security systems", "is_correct": false, "feedback": "Motion sensors are key components in security systems."}, {"id": "opt3", "text": "Amplifying weak electrical signals", "is_correct": true, "feedback": "Correct! Amplification is performed by amplifiers, not sensors. Sensors convert physical quantities to electrical signals, but don't amplify them."}, {"id": "opt4", "text": "Monitoring light levels to adjust screen brightness", "is_correct": false, "feedback": "Light sensors are used in devices to automatically adjust screen brightness."}]}}}, {"id": "sensors-screen-3", "type": "lesson_screen", "order": 3, "estimatedTimeSeconds": 90, "content": {"headline": "Types of Sensors by Physical Quantity", "body_md": "Sensors exist for virtually every measurable physical quantity:\n\n- **Temperature**: Thermistors, thermocouples, RTDs\n- **Light**: Photodiodes, photoresistors, phototransistors\n- **Sound**: Microphones (dynamic, condenser, piezoelectric)\n- **Position/Motion**: Potentiometers, encoders, accelerometers, gyroscopes\n- **Pressure**: Strain gauges, piezoelectric sensors\n- **Magnetic fields**: Hall effect sensors, magnetoresistive sensors\n- **Chemical**: pH sensors, gas sensors, biosensors", "visual": {"type": "unsplash_search", "value": "electronic sensor array"}, "interactive_element": {"type": "button", "button_text": "Sensor Characteristics", "action": "next_screen"}}}, {"id": "sensors-screen-4", "type": "lesson_screen", "order": 4, "estimatedTimeSeconds": 120, "content": {"headline": "Key Sensor Characteristics", "body_md": "Sensors are evaluated based on several important characteristics:\n\n- **Sensitivity**: How much the output changes per unit change in input\n- **Range**: The minimum and maximum values that can be measured\n- **Resolution**: The smallest change that can be detected\n- **Accuracy**: How close the measured value is to the true value\n- **Precision**: The repeatability of measurements\n- **Response time**: How quickly the sensor responds to changes\n- **Linearity**: Whether the output is proportional to the input", "visual": {"type": "static_text", "value": "Accuracy vs. Precision"}, "interactive_element": {"type": "multiple_choice_text", "question": "What's the difference between accuracy and precision in sensors?", "options": [{"id": "opt1", "text": "They are different terms for the same concept", "is_correct": false, "feedback": "Accuracy and precision are distinct concepts in measurement."}, {"id": "opt2", "text": "Accuracy refers to how close a measurement is to the true value, while precision refers to the repeatability of measurements", "is_correct": true, "feedback": "Correct! A sensor can be precise (giving consistent readings) without being accurate (if those readings are consistently off from the true value)."}, {"id": "opt3", "text": "Precision refers to the sensor's sensitivity, while accuracy refers to its range", "is_correct": false, "feedback": "Sensitivity and range are different characteristics from accuracy and precision."}, {"id": "opt4", "text": "Accuracy is a digital characteristic while precision is an analog characteristic", "is_correct": false, "feedback": "Both accuracy and precision apply to both analog and digital sensors."}]}}}, {"id": "sensors-screen-5", "type": "lesson_screen", "order": 5, "estimatedTimeSeconds": 120, "content": {"headline": "Passive vs. Active Sensors", "body_md": "Sensors can be categorized as passive or active:\n\n**Passive sensors** directly generate an electrical signal from the measured quantity:\n- Thermocouples generate voltage from temperature differences\n- Photovoltaic cells generate voltage from light\n- Piezoelectric sensors generate voltage when deformed\n\n**Active sensors** require an external power source:\n- Thermistors change resistance with temperature\n- Strain gauges change resistance when stretched\n- Hall effect sensors require power to detect magnetic fields", "visual": {"type": "giphy_search", "value": "thermocouple sensor"}, "interactive_element": {"type": "multiple_choice_text", "question": "Which of these is a passive sensor?", "options": [{"id": "opt1", "text": "Thermistor", "is_correct": false, "feedback": "Thermistors are active sensors that change resistance with temperature but require external power."}, {"id": "opt2", "text": "Thermocouple", "is_correct": true, "feedback": "Correct! Thermocouples generate a small voltage directly from temperature differences without needing external power, making them passive sensors."}, {"id": "opt3", "text": "Ultrasonic distance sensor", "is_correct": false, "feedback": "Ultrasonic sensors require power to generate sound waves and detect reflections."}, {"id": "opt4", "text": "Digital temperature sensor (IC)", "is_correct": false, "feedback": "Digital temperature sensor ICs require power to operate their internal circuitry."}]}}}, {"id": "sensors-screen-6", "type": "lesson_screen", "order": 6, "estimatedTimeSeconds": 90, "content": {"headline": "Signal Conditioning", "body_md": "Raw sensor outputs often need **signal conditioning** before they can be used:\n\n- **Amplification**: Increasing small signals (e.g., thermocouple outputs)\n- **Filtering**: Removing noise and unwanted frequencies\n- **Linearization**: Correcting non-linear responses\n- **Bridge circuits**: Converting resistance changes to voltage (e.g., Wheatstone bridge)\n- **Isolation**: Protecting circuits from high voltages or ground loops\n- **A/D conversion**: Converting analog signals to digital for processing\n\nSignal conditioning circuits are essential for getting usable data from sensors.", "visual": {"type": "unsplash_search", "value": "electronic circuit board"}, "interactive_element": {"type": "button", "button_text": "Real-World Applications", "action": "next_screen"}}}, {"id": "sensors-screen-7", "type": "lesson_screen", "order": 7, "estimatedTimeSeconds": 90, "content": {"headline": "Sensor Applications in Daily Life", "body_md": "Sensors are everywhere in modern technology:\n\n- **Smartphones**: Accelerometers, gyroscopes, proximity sensors, light sensors\n- **Automobiles**: Oxygen sensors, temperature sensors, pressure sensors, radar\n- **Smart homes**: Motion detectors, thermostats, smoke detectors\n- **Medical devices**: Blood pressure sensors, pulse oximeters, glucose monitors\n- **Industrial**: Flow sensors, level sensors, vibration sensors\n- **Environmental monitoring**: Air quality, weather stations, seismic sensors\n- **Wearables**: Heart rate monitors, step counters, GPS", "visual": {"type": "giphy_search", "value": "smartphone sensors"}, "interactive_element": {"type": "multiple_choice_text", "question": "Which sensor allows smartphones to automatically rotate their screen when you turn the phone?", "options": [{"id": "opt1", "text": "Light sensor", "is_correct": false, "feedback": "Light sensors detect ambient brightness for screen brightness adjustment, not orientation."}, {"id": "opt2", "text": "Proximity sensor", "is_correct": false, "feedback": "Proximity sensors detect when the phone is near your face to turn off the screen during calls."}, {"id": "opt3", "text": "Accelerometer", "is_correct": true, "feedback": "Correct! Accelerometers detect the direction of gravity, allowing the phone to determine its orientation relative to the ground and rotate the screen accordingly."}, {"id": "opt4", "text": "Fingerprint sensor", "is_correct": false, "feedback": "Fingerprint sensors are used for biometric authentication, not detecting orientation."}]}}}, {"id": "sensors-screen-8", "type": "lesson_screen", "order": 8, "estimatedTimeSeconds": 60, "content": {"headline": "Sensors: The Bridge to the Physical World", "body_md": "You've now learned the basics of sensors and transducers:\n\n- They convert physical quantities to electrical signals\n- They come in many types for different physical quantities\n- They have important characteristics like sensitivity, accuracy, and range\n- They can be passive (self-generating) or active (requiring power)\n- They often need signal conditioning to provide usable outputs\n- They're essential components in countless modern technologies\n\nSensors are the foundation of how electronic systems interact with and respond to the world around them.", "visual": {"type": "giphy_search", "value": "sensor technology future"}, "interactive_element": {"type": "button", "button_text": "Finish Lesson", "action": "complete_lesson"}}}]}, {"id": "intro-digital-logic-gates", "title": "Introduction to Digital Logic Gates", "description": "Understand the building blocks of digital circuits (AND, OR, NOT).", "order": 5, "type": "interactive_lesson", "estimatedTimeMinutes": 25, "contentBlocks": [{"id": "logic-gates-screen-1", "type": "lesson_screen", "order": 1, "estimatedTimeSeconds": 60, "content": {"headline": "Logic Gates: The Building Blocks of Digital Electronics", "body_md": "**Logic gates** are the fundamental building blocks of digital circuits. They perform basic logical operations on binary inputs (0s and 1s) and produce binary outputs according to specific rules.\n\nEvery digital device you use—from smartphones to computers to microwave ovens—contains thousands to billions of these simple elements working together.", "visual": {"type": "giphy_search", "value": "digital logic gates animation"}, "interactive_element": {"type": "button", "button_text": "Digital vs. Analog", "action": "next_screen"}}}, {"id": "logic-gates-screen-2", "type": "lesson_screen", "order": 2, "estimatedTimeSeconds": 90, "content": {"headline": "Digital vs. Analog: The Binary World", "body_md": "Before diving into logic gates, let's understand the digital approach:\n\n- **Analog signals** can have any value within a range (like a dimmer switch)\n- **Digital signals** use discrete values, typically just two: 0 and 1\n\nDigital electronics offers key advantages:\n- Noise immunity (small variations don't change the signal)\n- Perfect reproducibility\n- Compatibility with computers and memory\n- Complex processing with simple building blocks", "visual": {"type": "giphy_search", "value": "analog vs digital signal"}, "interactive_element": {"type": "multiple_choice_text", "question": "Which of these is NOT an advantage of digital over analog electronics?", "options": [{"id": "opt1", "text": "Better noise immunity", "is_correct": false, "feedback": "Digital signals have excellent noise immunity since small variations don't change the binary value."}, {"id": "opt2", "text": "More natural representation of real-world phenomena", "is_correct": true, "feedback": "Correct! Analog signals actually provide a more natural representation of real-world phenomena, which are often continuous rather than discrete. Digital systems must convert and approximate these continuous values."}, {"id": "opt3", "text": "Perfect reproducibility of signals", "is_correct": false, "feedback": "Digital signals can be perfectly reproduced since they only need to distinguish between two states."}, {"id": "opt4", "text": "Compatibility with computer processing", "is_correct": false, "feedback": "Digital signals are naturally compatible with computer processing, which is based on binary logic."}]}}}, {"id": "logic-gates-screen-3", "type": "lesson_screen", "order": 3, "estimatedTimeSeconds": 90, "content": {"headline": "The NOT Gate: Logical Inversion", "body_md": "The simplest logic gate is the **NOT gate** (or inverter):\n\n- It has one input and one output\n- It reverses the logical state of its input\n- If the input is 0, the output is 1\n- If the input is 1, the output is 0\n\nSymbol: A triangle with a small circle at the output (the circle indicates inversion)\n\nTruth table:\nInput | Output\n------|-------\n0 | 1\n1 | 0", "visual": {"type": "giphy_search", "value": "NOT gate logic"}, "interactive_element": {"type": "multiple_choice_text", "question": "If the input to a NOT gate is 1, what is the output?", "options": [{"id": "opt1", "text": "1", "is_correct": false, "feedback": "A NOT gate inverts the input, so an input of 1 produces an output of 0."}, {"id": "opt2", "text": "0", "is_correct": true, "feedback": "Correct! A NOT gate inverts the input, so an input of 1 produces an output of 0."}, {"id": "opt3", "text": "Either 0 or 1, depending on the type of NOT gate", "is_correct": false, "feedback": "All NOT gates function the same way, inverting the input."}, {"id": "opt4", "text": "The same as the previous output", "is_correct": false, "feedback": "The output of a NOT gate depends only on the current input, not previous outputs."}]}}}, {"id": "logic-gates-screen-4", "type": "lesson_screen", "order": 4, "estimatedTimeSeconds": 120, "content": {"headline": "The AND Gate: Logical Multiplication", "body_md": "The **AND gate** implements logical conjunction:\n\n- It typically has two or more inputs and one output\n- The output is 1 ONLY if ALL inputs are 1\n- If ANY input is 0, the output is 0\n\nSymbol: A shape like a D with a flat side on the left\n\nTruth table (2 inputs):\nInput A | Input B | Output\n--------|---------|-------\n0 | 0 | 0\n0 | 1 | 0\n1 | 0 | 0\n1 | 1 | 1", "visual": {"type": "giphy_search", "value": "AND gate logic"}, "interactive_element": {"type": "multiple_choice_text", "question": "An AND gate with three inputs has the values: Input A = 1, Input B = 1, Input C = 0. What is the output?", "options": [{"id": "opt1", "text": "1", "is_correct": false, "feedback": "For an AND gate to output 1, ALL inputs must be 1."}, {"id": "opt2", "text": "0", "is_correct": true, "feedback": "Correct! Since Input C is 0, the output must be 0. An AND gate outputs 1 only when ALL inputs are 1."}, {"id": "opt3", "text": "It depends on the previous state", "is_correct": false, "feedback": "Logic gates don't have memory; the output depends only on the current inputs."}, {"id": "opt4", "text": "Cannot be determined from the information given", "is_correct": false, "feedback": "We have all the information needed: three inputs to an AND gate, where one input is 0."}]}}}, {"id": "logic-gates-screen-5", "type": "lesson_screen", "order": 5, "estimatedTimeSeconds": 120, "content": {"headline": "The OR Gate: Logical Addition", "body_md": "The **OR gate** implements logical disjunction:\n\n- It typically has two or more inputs and one output\n- The output is 1 if ANY input is 1\n- The output is 0 ONLY if ALL inputs are 0\n\nSymbol: A shape like a pointed arch or shield\n\nTruth table (2 inputs):\nInput A | Input B | Output\n--------|---------|-------\n0 | 0 | 0\n0 | 1 | 1\n1 | 0 | 1\n1 | 1 | 1", "visual": {"type": "giphy_search", "value": "OR gate logic"}, "interactive_element": {"type": "multiple_choice_text", "question": "An OR gate with three inputs has the values: Input A = 0, Input B = 0, Input C = 0. What is the output?", "options": [{"id": "opt1", "text": "1", "is_correct": false, "feedback": "An OR gate outputs 1 if ANY input is 1, but all inputs here are 0."}, {"id": "opt2", "text": "0", "is_correct": true, "feedback": "Correct! An OR gate outputs 0 only when ALL inputs are 0, which is the case here."}, {"id": "opt3", "text": "It depends on the type of OR gate", "is_correct": false, "feedback": "Standard OR gates all function the same way."}, {"id": "opt4", "text": "Cannot be determined from the information given", "is_correct": false, "feedback": "We have all the information needed: three inputs to an OR gate, all with value 0."}]}}}, {"id": "logic-gates-screen-6", "type": "lesson_screen", "order": 6, "estimatedTimeSeconds": 90, "content": {"headline": "Compound Gates: NAND, NOR, XOR, XNOR", "body_md": "Beyond the basic gates, there are important compound gates:\n\n- **NAND**: AND followed by NOT (output is 0 only if all inputs are 1)\n- **NOR**: OR followed by NOT (output is 1 only if all inputs are 0)\n- **XOR** (Exclusive OR): Output is 1 if inputs are different (odd number of 1s)\n- **XNOR** (Exclusive NOR): Output is 1 if inputs are the same (even number of 1s)\n\nNAND and NOR are particularly important as they are \"universal gates\" - any logical function can be built using only NAND gates or only NOR gates.", "visual": {"type": "unsplash_search", "value": "digital circuit board"}, "interactive_element": {"type": "multiple_choice_text", "question": "Which logic gate outputs 1 when its two inputs are different from each other?", "options": [{"id": "opt1", "text": "AND", "is_correct": false, "feedback": "AND gates output 1 only when both inputs are 1."}, {"id": "opt2", "text": "OR", "is_correct": false, "feedback": "OR gates output 1 when either or both inputs are 1, not specifically when they differ."}, {"id": "opt3", "text": "XOR (Exclusive OR)", "is_correct": true, "feedback": "Correct! XOR (Exclusive OR) gates output 1 when the inputs are different (one is 0 and one is 1)."}, {"id": "opt4", "text": "XNOR (Exclusive NOR)", "is_correct": false, "feedback": "XNOR gates output 1 when the inputs are the same (both 0 or both 1)."}]}}}, {"id": "logic-gates-screen-7", "type": "lesson_screen", "order": 7, "estimatedTimeSeconds": 90, "content": {"headline": "From Gates to Circuits: Combining Logic", "body_md": "Logic gates can be combined to create more complex functions:\n\n- **Half adder**: Adds two bits, producing sum and carry outputs\n- **Full adder**: Adds three bits (including a carry-in)\n- **Multiplexer**: Selects one of several inputs based on control signals\n- **Decoder**: Converts binary codes to individual output lines\n- **Flip-flops**: Store binary states, forming the basis of memory\n\nBy combining these building blocks, we can create everything from simple calculators to complex microprocessors.", "visual": {"type": "giphy_search", "value": "digital circuit animation"}, "interactive_element": {"type": "multiple_choice_text", "question": "What can be built by combining multiple logic gates?", "options": [{"id": "opt1", "text": "Only simple arithmetic circuits", "is_correct": false, "feedback": "Logic gates can be combined to create much more than just arithmetic circuits."}, {"id": "opt2", "text": "Any digital system, from calculators to computers", "is_correct": true, "feedback": "Correct! All digital systems, from simple calculators to complex computers, are built by combining logic gates in various configurations."}, {"id": "opt3", "text": "Only memory storage devices", "is_correct": false, "feedback": "While memory devices use logic gates, they're just one of many possible applications."}, {"id": "opt4", "text": "Only analog signal processors", "is_correct": false, "feedback": "Logic gates are digital components and are used to build digital systems, not analog signal processors."}]}}}, {"id": "logic-gates-screen-8", "type": "lesson_screen", "order": 8, "estimatedTimeSeconds": 60, "content": {"headline": "Logic Gates: Foundation of the Digital World", "body_md": "You've now learned the basics of logic gates:\n\n- They process binary signals (0s and 1s)\n- Basic gates include NOT, AND, and OR\n- Compound gates include NAND, NOR, XOR, and XNOR\n- They can be combined to create complex digital systems\n- They form the foundation of all digital electronics\n\nFrom these simple elements, we've built the entire digital world—from pocket calculators to supercomputers, from digital watches to the internet.", "visual": {"type": "giphy_search", "value": "computer processor chip"}, "interactive_element": {"type": "button", "button_text": "Finish Lesson", "action": "complete_lesson"}}}]}, {"id": "module-test-circuit-application-engineer", "title": "Module Test: Circuit Application Engineer", "description": "Understand the basic principles behind common electronic applications.", "order": 6, "type": "module_test_interactive", "estimatedTimeMinutes": 30, "passingScorePercentage": 70, "contentBlocks": []}]}