import 'package:flutter/material.dart';
import 'dart:math' as math;

/// Model class for a model component
class ModelComponent {
  final String id;
  final String name;
  final String description;
  final String category;
  final IconData icon;

  ModelComponent({
    required this.id,
    required this.name,
    required this.description,
    required this.category,
    required this.icon,
  });

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is ModelComponent &&
          runtimeType == other.runtimeType &&
          id == other.id;

  @override
  int get hashCode => id.hashCode;
}

/// Model class for a model scenario
class ModelScenario {
  final String title;
  final String description;
  final String domain;
  final String phenomenon;
  final List<String> correctComponents;
  final String explanation;
  final String feedbackCorrect;
  final String feedbackIncorrect;

  ModelScenario({
    required this.title,
    required this.description,
    required this.domain,
    required this.phenomenon,
    required this.correctComponents,
    required this.explanation,
    required this.feedbackCorrect,
    required this.feedbackIncorrect,
  });
}

/// A widget that allows users to build and test scientific models
/// Users can create models, define variables, make predictions, and test against data
class InteractiveModelBuilderWidget extends StatefulWidget {
  /// The data for this widget
  final Map<String, dynamic> data;

  /// Optional callback when the widget state changes
  final Function(bool isCompleted)? onStateChanged;

  const InteractiveModelBuilderWidget({
    super.key,
    required this.data,
    this.onStateChanged,
  });

  /// Factory constructor to create the widget from JSON data
  factory InteractiveModelBuilderWidget.fromData(Map<String, dynamic> data) {
    return InteractiveModelBuilderWidget(
      data: data,
    );
  }

  @override
  State<InteractiveModelBuilderWidget> createState() => _InteractiveModelBuilderWidgetState();
}

class _InteractiveModelBuilderWidgetState extends State<InteractiveModelBuilderWidget> {
  // Model scenarios
  late List<ModelScenario> _scenarios;
  late int _currentScenarioIndex;

  // Model components
  late List<ModelComponent> _availableComponents;
  late List<ModelComponent> _selectedComponents;

  // UI state
  late bool _isCompleted;
  late bool _showExplanation;
  late bool _hasSubmitted;
  late bool _isCorrect;
  late String _feedback;

  // Colors
  late Color _primaryColor;
  late Color _secondaryColor;
  late Color _accentColor;
  late Color _backgroundColor;
  late Color _textColor;

  @override
  void initState() {
    super.initState();

    // Initialize colors
    _primaryColor = _getColorFromHex(widget.data['primaryColor'] ?? '#2196F3');
    _secondaryColor = _getColorFromHex(widget.data['secondaryColor'] ?? '#FF9800');
    _accentColor = _getColorFromHex(widget.data['accentColor'] ?? '#4CAF50');
    _backgroundColor = _getColorFromHex(widget.data['backgroundColor'] ?? '#FFFFFF');
    _textColor = _getColorFromHex(widget.data['textColor'] ?? '#212121');

    // Initialize scenarios
    _initializeScenarios();

    // Initialize state
    _currentScenarioIndex = 0;
    _isCompleted = false;
    _showExplanation = false;
    _hasSubmitted = false;
    _isCorrect = false;
    _feedback = '';

    // Initialize model components
    _initializeComponents();
  }

  // Initialize the model scenarios
  void _initializeScenarios() {
    final List<dynamic> scenariosData = widget.data['scenarios'] ?? _getDefaultScenarios();

    _scenarios = scenariosData.map((scenarioData) {
      return ModelScenario(
        title: scenarioData['title'] ?? '',
        description: scenarioData['description'] ?? '',
        domain: scenarioData['domain'] ?? '',
        phenomenon: scenarioData['phenomenon'] ?? '',
        correctComponents: List<String>.from(scenarioData['correctComponents'] ?? []),
        explanation: scenarioData['explanation'] ?? '',
        feedbackCorrect: scenarioData['feedbackCorrect'] ?? 'Great job! Your model correctly explains the phenomenon.',
        feedbackIncorrect: scenarioData['feedbackIncorrect'] ?? 'Your model needs some adjustments. Try again with different components.',
      );
    }).toList();
  }

  // Get default scenarios if none are provided
  List<Map<String, dynamic>> _getDefaultScenarios() {
    return [
      {
        'title': 'Climate Change Model',
        'description': 'Build a model to explain global climate change.',
        'domain': 'Earth Science',
        'phenomenon': 'Global average temperatures have been rising over the past century, with accelerated warming in recent decades. This has been accompanied by melting ice caps, rising sea levels, and increased frequency of extreme weather events.',
        'correctComponents': [
          'greenhouse_gases',
          'solar_radiation',
          'heat_trapping',
          'human_activity',
        ],
        'explanation': 'A comprehensive climate change model includes greenhouse gases (like CO2 and methane) that trap heat from solar radiation in the atmosphere. Human activities, particularly burning fossil fuels and deforestation, have increased the concentration of these gases, enhancing the natural greenhouse effect and leading to global warming.',
        'feedbackCorrect': 'Excellent! Your climate change model correctly incorporates the key components: greenhouse gases that trap solar radiation, amplified by human activities. This model explains the observed warming trend and its consequences.',
        'feedbackIncorrect': 'Your climate model is missing some key components. A comprehensive climate change model needs to account for greenhouse gases, solar radiation, heat trapping mechanisms, and human activities that enhance these effects.',
      },
      {
        'title': 'Cell Division Model',
        'description': 'Build a model to explain how cells divide and reproduce.',
        'domain': 'Biology',
        'phenomenon': 'Organisms grow and repair themselves through cell division. This process must ensure that genetic information is accurately copied and distributed to daughter cells.',
        'correctComponents': [
          'dna_replication',
          'chromosome_separation',
          'cell_membrane_division',
          'cell_cycle_regulation',
        ],
        'explanation': 'Cell division involves several coordinated processes: DNA replication ensures genetic material is duplicated, chromosome separation (mitosis) distributes this material equally, and cell membrane division (cytokinesis) creates two separate cells. The cell cycle is regulated by checkpoints that ensure each step occurs correctly and in the proper sequence.',
        'feedbackCorrect': 'Excellent! Your cell division model correctly includes DNA replication, chromosome separation, cell membrane division, and cell cycle regulation. This model explains how cells reproduce while maintaining genetic integrity.',
        'feedbackIncorrect': 'Your cell division model is missing some key components. A complete model needs to account for DNA replication, chromosome separation, cell membrane division, and cell cycle regulation.',
      },
      {
        'title': 'Atomic Structure Model',
        'description': 'Build a model to explain the structure of atoms.',
        'domain': 'Chemistry',
        'phenomenon': 'Atoms are the basic units of matter, with distinct properties for each element. They can form bonds with other atoms and participate in chemical reactions while maintaining their identity.',
        'correctComponents': [
          'nucleus',
          'protons',
          'neutrons',
          'electrons',
          'electron_shells',
        ],
        'explanation': 'The modern atomic model includes a central nucleus containing positively charged protons and neutral neutrons, surrounded by negatively charged electrons arranged in shells or energy levels. The number of protons determines the element, while the arrangement of electrons determines chemical properties and bonding behavior.',
        'feedbackCorrect': 'Excellent! Your atomic structure model correctly includes the nucleus with protons and neutrons, surrounded by electrons arranged in shells. This model explains the properties of elements and their chemical behavior.',
        'feedbackIncorrect': 'Your atomic structure model is missing some key components. A complete model needs to include the nucleus with protons and neutrons, as well as electrons arranged in shells or energy levels.',
      },
      {
        'title': 'Plate Tectonics Model',
        'description': 'Build a model to explain the movement of Earth\'s crust.',
        'domain': 'Earth Science',
        'phenomenon': 'Earth\'s surface features include mountain ranges, ocean trenches, volcanic activity, and earthquakes that occur in specific patterns across the globe. Continents appear to have moved over geological time.',
        'correctComponents': [
          'lithospheric_plates',
          'convection_currents',
          'seafloor_spreading',
          'subduction',
          'continental_drift',
        ],
        'explanation': 'The plate tectonics model explains that Earth\'s lithosphere is divided into plates that move on the semi-fluid asthenosphere. This movement is driven by convection currents in the mantle. Where plates meet, they can spread apart (seafloor spreading), collide (forming mountains), or slide past each other. One plate can be forced under another (subduction), creating trenches and volcanic activity. Over millions of years, this process has moved continents (continental drift).',
        'feedbackCorrect': 'Excellent! Your plate tectonics model correctly includes lithospheric plates moving due to convection currents, with processes like seafloor spreading, subduction, and continental drift. This model explains Earth\'s major geological features and events.',
        'feedbackIncorrect': 'Your plate tectonics model is missing some key components. A complete model needs to include lithospheric plates, convection currents, and processes like seafloor spreading, subduction, and continental drift.',
      },
    ];
  }

  // Initialize the model components
  void _initializeComponents() {
    final List<dynamic> componentsData = widget.data['components'] ?? _getDefaultComponents();

    _availableComponents = componentsData.map((componentData) {
      return ModelComponent(
        id: componentData['id'] ?? '',
        name: componentData['name'] ?? '',
        description: componentData['description'] ?? '',
        category: componentData['category'] ?? '',
        icon: _getIconData(componentData['icon'] ?? 'science'),
      );
    }).toList();

    // Filter available components based on current scenario
    _filterAvailableComponents();

    // Initialize selected components as empty
    _selectedComponents = [];
  }

  // Get default components if none are provided
  List<Map<String, dynamic>> _getDefaultComponents() {
    return [
      // Climate components
      {
        'id': 'greenhouse_gases',
        'name': 'Greenhouse Gases',
        'description': 'Gases like CO2 and methane that trap heat in the atmosphere',
        'category': 'Climate',
        'icon': 'cloud',
      },
      {
        'id': 'solar_radiation',
        'name': 'Solar Radiation',
        'description': 'Energy from the sun that warms Earth\'s surface',
        'category': 'Climate',
        'icon': 'wb_sunny',
      },
      {
        'id': 'heat_trapping',
        'name': 'Heat Trapping',
        'description': 'Process where atmospheric gases prevent heat from escaping into space',
        'category': 'Climate',
        'icon': 'whatshot',
      },
      {
        'id': 'human_activity',
        'name': 'Human Activity',
        'description': 'Actions like burning fossil fuels that release greenhouse gases',
        'category': 'Climate',
        'icon': 'factory',
      },
      {
        'id': 'natural_cycles',
        'name': 'Natural Cycles',
        'description': 'Regular climate variations like Milankovitch cycles',
        'category': 'Climate',
        'icon': 'autorenew',
      },

      // Biology components
      {
        'id': 'dna_replication',
        'name': 'DNA Replication',
        'description': 'Process of copying genetic material before cell division',
        'category': 'Biology',
        'icon': 'content_copy',
      },
      {
        'id': 'chromosome_separation',
        'name': 'Chromosome Separation',
        'description': 'Distribution of chromosomes to daughter cells during mitosis',
        'category': 'Biology',
        'icon': 'call_split',
      },
      {
        'id': 'cell_membrane_division',
        'name': 'Cell Membrane Division',
        'description': 'Process of dividing the cell membrane to form two cells',
        'category': 'Biology',
        'icon': 'crop',
      },
      {
        'id': 'cell_cycle_regulation',
        'name': 'Cell Cycle Regulation',
        'description': 'Checkpoints that control the progression of cell division',
        'category': 'Biology',
        'icon': 'timer',
      },
      {
        'id': 'protein_synthesis',
        'name': 'Protein Synthesis',
        'description': 'Production of proteins based on genetic instructions',
        'category': 'Biology',
        'icon': 'build',
      },

      // Chemistry components
      {
        'id': 'nucleus',
        'name': 'Nucleus',
        'description': 'Central core of an atom containing protons and neutrons',
        'category': 'Chemistry',
        'icon': 'radio_button_checked',
      },
      {
        'id': 'protons',
        'name': 'Protons',
        'description': 'Positively charged particles in the nucleus',
        'category': 'Chemistry',
        'icon': 'add_circle',
      },
      {
        'id': 'neutrons',
        'name': 'Neutrons',
        'description': 'Neutral particles in the nucleus',
        'category': 'Chemistry',
        'icon': 'fiber_manual_record',
      },
      {
        'id': 'electrons',
        'name': 'Electrons',
        'description': 'Negatively charged particles orbiting the nucleus',
        'category': 'Chemistry',
        'icon': 'remove_circle',
      },
      {
        'id': 'electron_shells',
        'name': 'Electron Shells',
        'description': 'Energy levels where electrons are arranged around the nucleus',
        'category': 'Chemistry',
        'icon': 'track_changes',
      },

      // Earth Science components
      {
        'id': 'lithospheric_plates',
        'name': 'Lithospheric Plates',
        'description': 'Sections of Earth\'s crust that move relative to each other',
        'category': 'Earth Science',
        'icon': 'dashboard',
      },
      {
        'id': 'convection_currents',
        'name': 'Convection Currents',
        'description': 'Movement of magma in the mantle that drives plate motion',
        'category': 'Earth Science',
        'icon': 'loop',
      },
      {
        'id': 'seafloor_spreading',
        'name': 'Seafloor Spreading',
        'description': 'Process where new oceanic crust forms at mid-ocean ridges',
        'category': 'Earth Science',
        'icon': 'open_with',
      },
      {
        'id': 'subduction',
        'name': 'Subduction',
        'description': 'Process where one plate is forced beneath another',
        'category': 'Earth Science',
        'icon': 'arrow_downward',
      },
      {
        'id': 'continental_drift',
        'name': 'Continental Drift',
        'description': 'Movement of continents over geological time',
        'category': 'Earth Science',
        'icon': 'shuffle',
      },
    ];
  }

  // Filter available components based on current scenario
  void _filterAvailableComponents() {
    final scenario = _scenarios[_currentScenarioIndex];

    // For simplicity, we'll add some relevant components and some distractors
    // In a real app, this would be more sophisticated
    final List<String> relevantCategories = [scenario.domain];

    // Add some general categories that might be relevant
    if (scenario.domain == 'Earth Science') {
      relevantCategories.add('Climate');
    }

    // Filter components by relevant categories
    _availableComponents = _availableComponents.where((component) {
      return relevantCategories.contains(component.category);
    }).toList();

    // Shuffle to mix correct and incorrect components
    _availableComponents.shuffle(math.Random(42)); // Fixed seed for reproducibility
  }

  // Get color from hex string
  Color _getColorFromHex(String hexColor) {
    hexColor = hexColor.replaceAll('#', '');
    if (hexColor.length == 6) {
      hexColor = 'FF$hexColor';
    }
    return Color(int.parse(hexColor, radix: 16));
  }

  // Get IconData from string
  IconData _getIconData(String iconName) {
    switch (iconName) {
      case 'science': return Icons.science;
      case 'cloud': return Icons.cloud;
      case 'wb_sunny': return Icons.wb_sunny;
      case 'whatshot': return Icons.whatshot;
      case 'factory': return Icons.factory;
      case 'autorenew': return Icons.autorenew;
      case 'content_copy': return Icons.content_copy;
      case 'call_split': return Icons.call_split;
      case 'crop': return Icons.crop;
      case 'timer': return Icons.timer;
      case 'build': return Icons.build;
      case 'radio_button_checked': return Icons.radio_button_checked;
      case 'add_circle': return Icons.add_circle;
      case 'fiber_manual_record': return Icons.fiber_manual_record;
      case 'remove_circle': return Icons.remove_circle;
      case 'track_changes': return Icons.track_changes;
      case 'dashboard': return Icons.dashboard;
      case 'loop': return Icons.loop;
      case 'open_with': return Icons.open_with;
      case 'arrow_downward': return Icons.arrow_downward;
      case 'shuffle': return Icons.shuffle;
      default: return Icons.science;
    }
  }

  // Add a component to the model
  void _addComponent(ModelComponent component) {
    if (!_selectedComponents.contains(component) && !_hasSubmitted) {
      setState(() {
        _selectedComponents.add(component);
      });
    }
  }

  // Remove a component from the model
  void _removeComponent(ModelComponent component) {
    if (!_hasSubmitted) {
      setState(() {
        _selectedComponents.remove(component);
      });
    }
  }

  // Check if the model is correct
  void _checkModel() {
    final scenario = _scenarios[_currentScenarioIndex];
    final selectedIds = _selectedComponents.map((c) => c.id).toList();

    // Check if all correct components are included
    bool allCorrectIncluded = true;
    for (final correctId in scenario.correctComponents) {
      if (!selectedIds.contains(correctId)) {
        allCorrectIncluded = false;
        break;
      }
    }

    // Check if no incorrect components are included
    bool noIncorrectIncluded = true;
    for (final selectedId in selectedIds) {
      if (!scenario.correctComponents.contains(selectedId)) {
        noIncorrectIncluded = false;
        break;
      }
    }

    // Model is correct if it has all correct components and no incorrect ones
    _isCorrect = allCorrectIncluded && noIncorrectIncluded;
    _feedback = _isCorrect ? scenario.feedbackCorrect : scenario.feedbackIncorrect;

    setState(() {
      _hasSubmitted = true;
    });
  }

  // Reset the current model
  void _resetModel() {
    setState(() {
      _selectedComponents = [];
      _hasSubmitted = false;
      _feedback = '';
    });
  }

  // Go to the next scenario
  void _nextScenario() {
    if (_currentScenarioIndex < _scenarios.length - 1) {
      setState(() {
        _currentScenarioIndex++;
        _resetModel();
        _filterAvailableComponents();
        _showExplanation = false;
      });
    } else if (!_isCompleted) {
      setState(() {
        _isCompleted = true;
      });
      widget.onStateChanged?.call(true);
    }
  }

  // Go to the previous scenario
  void _previousScenario() {
    if (_currentScenarioIndex > 0) {
      setState(() {
        _currentScenarioIndex--;
        _resetModel();
        _filterAvailableComponents();
        _showExplanation = false;
      });
    }
  }

  // Toggle explanation visibility
  void _toggleExplanation() {
    setState(() {
      _showExplanation = !_showExplanation;
    });
  }

  // Reset the widget
  void _resetWidget() {
    setState(() {
      _currentScenarioIndex = 0;
      _resetModel();
      _filterAvailableComponents();
      _isCompleted = false;
      _showExplanation = false;
    });
  }

  // Build a component card for the available components list
  Widget _buildComponentCard(
    ModelComponent component, {
    required Function() onTap,
    required bool isSelected,
    required bool isSelectable,
  }) {
    return GestureDetector(
      onTap: isSelectable ? onTap : null,
      child: Container(
        width: 100,
        margin: const EdgeInsets.all(8),
        padding: const EdgeInsets.all(8),
        decoration: BoxDecoration(
          color: isSelected ? _primaryColor.withAlpha(50) : _backgroundColor,
          borderRadius: BorderRadius.circular(8),
          border: Border.all(
            color: isSelected ? _primaryColor : Colors.grey.withAlpha(75),
          ),
        ),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              component.icon,
              color: isSelected ? _primaryColor : _textColor,
              size: 24,
            ),
            const SizedBox(height: 8),
            Text(
              component.name,
              textAlign: TextAlign.center,
              style: TextStyle(
                fontSize: 12,
                fontWeight: isSelected ? FontWeight.bold : FontWeight.normal,
                color: isSelected ? _primaryColor : _textColor,
              ),
              maxLines: 2,
              overflow: TextOverflow.ellipsis,
            ),
          ],
        ),
      ),
    );
  }

  // Build a chip for the selected components
  Widget _buildSelectedComponentChip(
    ModelComponent component, {
    required Function() onRemove,
    required bool isRemovable,
  }) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
      decoration: BoxDecoration(
        color: _primaryColor.withAlpha(50),
        borderRadius: BorderRadius.circular(16),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(
            component.icon,
            color: _primaryColor,
            size: 16,
          ),
          const SizedBox(width: 8),
          Text(
            component.name,
            style: TextStyle(
              fontSize: 12,
              fontWeight: FontWeight.bold,
              color: _primaryColor,
            ),
          ),
          if (isRemovable) ...[
            const SizedBox(width: 4),
            GestureDetector(
              onTap: onRemove,
              child: Icon(
                Icons.close,
                color: _primaryColor,
                size: 16,
              ),
            ),
          ],
        ],
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    final scenario = _scenarios[_currentScenarioIndex];

    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: _backgroundColor,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withAlpha(50),
            spreadRadius: 1,
            blurRadius: 3,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Title
          Text(
            widget.data['title'] ?? 'Scientific Model Builder',
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
              color: _textColor,
            ),
          ),

          const SizedBox(height: 8),

          // Scenario navigation
          Row(
            children: [
              Text(
                'Scenario ${_currentScenarioIndex + 1} of ${_scenarios.length}: ${scenario.title}',
                style: TextStyle(
                  fontSize: 14,
                  fontWeight: FontWeight.bold,
                  color: _primaryColor,
                ),
              ),
              const Spacer(),
              IconButton(
                icon: Icon(_showExplanation ? Icons.info_outline : Icons.info),
                onPressed: _hasSubmitted ? _toggleExplanation : null,
                tooltip: _showExplanation ? 'Hide Explanation' : 'Show Explanation',
                color: _secondaryColor,
              ),
            ],
          ),

          const SizedBox(height: 8),

          // Scenario description
          Text(
            scenario.description,
            style: TextStyle(
              fontSize: 14,
              fontStyle: FontStyle.italic,
              color: _textColor.withAlpha(180),
            ),
          ),

          const SizedBox(height: 16),

          // Phenomenon to explain
          Container(
            padding: const EdgeInsets.all(12),
            decoration: BoxDecoration(
              color: _primaryColor.withAlpha(30),
              borderRadius: BorderRadius.circular(8),
              border: Border.all(color: _primaryColor.withAlpha(100)),
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'Phenomenon to Explain:',
                  style: TextStyle(
                    fontSize: 14,
                    fontWeight: FontWeight.bold,
                    color: _primaryColor,
                  ),
                ),
                const SizedBox(height: 8),
                Text(
                  scenario.phenomenon,
                  style: TextStyle(
                    fontSize: 14,
                    color: _textColor,
                  ),
                ),
              ],
            ),
          ),

          const SizedBox(height: 16),

          // Available components
          Text(
            'Available Model Components:',
            style: TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.bold,
              color: _primaryColor,
            ),
          ),

          const SizedBox(height: 8),

          // Component list
          Container(
            height: 120,
            decoration: BoxDecoration(
              color: _backgroundColor,
              borderRadius: BorderRadius.circular(8),
              border: Border.all(color: _primaryColor.withAlpha(75)),
            ),
            child: ListView.builder(
              scrollDirection: Axis.horizontal,
              itemCount: _availableComponents.length,
              itemBuilder: (context, index) {
                final component = _availableComponents[index];
                return _buildComponentCard(
                  component,
                  onTap: () => _addComponent(component),
                  isSelected: _selectedComponents.contains(component),
                  isSelectable: !_hasSubmitted,
                );
              },
            ),
          ),

          const SizedBox(height: 16),

          // Your model
          Text(
            'Your Model:',
            style: TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.bold,
              color: _primaryColor,
            ),
          ),

          const SizedBox(height: 8),

          // Selected components
          Expanded(
            child: Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: _backgroundColor,
                borderRadius: BorderRadius.circular(8),
                border: Border.all(color: _primaryColor.withAlpha(75)),
              ),
              child: _selectedComponents.isEmpty
                  ? Center(
                      child: Text(
                        'Add components to build your model',
                        style: TextStyle(
                          fontSize: 14,
                          fontStyle: FontStyle.italic,
                          color: _textColor.withAlpha(150),
                        ),
                      ),
                    )
                  : Wrap(
                      spacing: 8,
                      runSpacing: 8,
                      children: _selectedComponents.map((component) {
                        return _buildSelectedComponentChip(
                          component,
                          onRemove: () => _removeComponent(component),
                          isRemovable: !_hasSubmitted,
                        );
                      }).toList(),
                    ),
            ),
          ),

          const SizedBox(height: 16),

          // Action buttons
          Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              ElevatedButton(
                onPressed: _hasSubmitted ? _resetModel : null,
                style: ElevatedButton.styleFrom(
                  backgroundColor: _secondaryColor,
                  foregroundColor: Colors.white,
                ),
                child: const Text('Reset Model'),
              ),
              const SizedBox(width: 16),
              ElevatedButton(
                onPressed: _selectedComponents.isNotEmpty && !_hasSubmitted ? _checkModel : null,
                style: ElevatedButton.styleFrom(
                  backgroundColor: _accentColor,
                  foregroundColor: Colors.white,
                ),
                child: const Text('Check Model'),
              ),
            ],
          ),

          const SizedBox(height: 16),

          // Feedback
          if (_hasSubmitted)
            Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: _isCorrect ? _accentColor.withAlpha(30) : _secondaryColor.withAlpha(30),
                borderRadius: BorderRadius.circular(8),
                border: Border.all(
                  color: _isCorrect ? _accentColor.withAlpha(75) : _secondaryColor.withAlpha(75),
                ),
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    _isCorrect ? 'Correct!' : 'Not Quite Right',
                    style: TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.bold,
                      color: _isCorrect ? _accentColor : _secondaryColor,
                    ),
                  ),
                  const SizedBox(height: 8),
                  Text(
                    _feedback,
                    style: TextStyle(
                      fontSize: 14,
                      color: _textColor,
                    ),
                  ),
                ],
              ),
            ),

          // Explanation
          if (_showExplanation && _hasSubmitted)
            Container(
              padding: const EdgeInsets.all(12),
              margin: const EdgeInsets.only(top: 16),
              decoration: BoxDecoration(
                color: _secondaryColor.withAlpha(25),
                borderRadius: BorderRadius.circular(8),
                border: Border.all(color: _secondaryColor.withAlpha(75)),
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'Model Explanation:',
                    style: TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.bold,
                      color: _secondaryColor,
                    ),
                  ),
                  const SizedBox(height: 8),
                  Text(
                    scenario.explanation,
                    style: TextStyle(
                      fontSize: 14,
                      color: _textColor,
                    ),
                  ),
                ],
              ),
            ),

          const SizedBox(height: 16),

          // Navigation buttons
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              ElevatedButton(
                onPressed: _currentScenarioIndex > 0 ? _previousScenario : null,
                style: ElevatedButton.styleFrom(
                  backgroundColor: _secondaryColor,
                  foregroundColor: Colors.white,
                ),
                child: const Text('Previous'),
              ),
              ElevatedButton(
                onPressed: (_hasSubmitted && _isCorrect) ?
                           (_currentScenarioIndex < _scenarios.length - 1 ? _nextScenario : (_isCompleted ? _resetWidget : _nextScenario)) :
                           null,
                style: ElevatedButton.styleFrom(
                  backgroundColor: _primaryColor,
                  foregroundColor: Colors.white,
                ),
                child: Text(_currentScenarioIndex < _scenarios.length - 1 ? 'Next' : (_isCompleted ? 'Restart' : 'Complete')),
              ),
            ],
          ),

          // Widget name tag at the bottom
          if (widget.data['showNameTag'] ?? true)
            Padding(
              padding: const EdgeInsets.only(top: 8),
              child: Text(
                'InteractiveModelBuilderWidget',
                style: TextStyle(
                  color: Colors.grey[600],
                  fontSize: 12,
                  fontStyle: FontStyle.italic,
                ),
              ),
            ),
        ],
      ),
    );
  }
}
