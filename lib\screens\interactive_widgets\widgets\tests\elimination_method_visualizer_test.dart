import 'package:flutter/material.dart';
import '../interactive_elimination_method_visualizer_widget.dart';

/// A simple test app for the Interactive Elimination Method Visualizer widget
void main() {
  runApp(const EliminationMethodVisualizerTestApp());
}

class EliminationMethodVisualizerTestApp extends StatelessWidget {
  const EliminationMethodVisualizerTestApp({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return MaterialApp(
      title: 'Elimination Method Visualizer Test',
      theme: ThemeData(
        primarySwatch: Colors.blue,
        visualDensity: VisualDensity.adaptivePlatformDensity,
      ),
      home: const EliminationMethodVisualizerTestScreen(),
    );
  }
}

class EliminationMethodVisualizerTestScreen extends StatelessWidget {
  const EliminationMethodVisualizerTestScreen({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    // Sample data for testing
    final testData = {
      'title': 'Elimination Method Visualizer Test',
      'description': 'Test the elimination method visualizer widget with step-by-step visualization.',
      'primaryColor': '#2196F3',
      'secondaryColor': '#FF9800',
      'accentColor': '#4CAF50',
      'textColor': '#212121',
      'highlightColor': '#FFEB3B',
      'initialEquations': [
        {
          'a': 2.0,
          'b': 3.0,
          'c': 8.0,
        },
        {
          'a': 5.0,
          'b': 1.0,
          'c': 14.0,
        },
      ],
      'showNameTag': true,
    };

    return Scaffold(
      appBar: AppBar(
        title: const Text('Elimination Method Visualizer Test'),
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'Interactive Elimination Method Visualizer Widget',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 16),
            const Text(
              'This is a test for the Interactive Elimination Method Visualizer widget. '
              'You can visualize the step-by-step process of solving a system of linear equations using the elimination method.',
              style: TextStyle(fontSize: 14),
            ),
            const SizedBox(height: 24),
            InteractiveEliminationMethodVisualizerWidget(
              data: testData,
              onStateChanged: (isCompleted) {
                print('Widget state changed: $isCompleted');
              },
            ),
            const SizedBox(height: 24),
            const Text(
              'Test Instructions:',
              style: TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 8),
            const Text(
              '1. Enter your own system of linear equations\n'
              '2. Click "Solve System" to see the step-by-step solution\n'
              '3. Use the navigation arrows to move through the steps\n'
              '4. Click "Animate Solution" to see the steps automatically\n'
              '5. Verify that the highlighting and explanations are clear',
              style: TextStyle(fontSize: 14),
            ),
          ],
        ),
      ),
    );
  }
}
