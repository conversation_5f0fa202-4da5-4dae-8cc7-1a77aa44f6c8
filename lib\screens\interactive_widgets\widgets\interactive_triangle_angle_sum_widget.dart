import 'package:flutter/material.dart';
import 'dart:math' as math;

/// A widget that demonstrates the angle sum property of triangles
/// Users can drag the vertices of a triangle and observe that the sum of interior angles is always 180°
class InteractiveTriangleAngleSumWidget extends StatefulWidget {
  /// The data for this widget
  final Map<String, dynamic> data;

  /// Optional callback when the widget state changes
  final Function(bool isCompleted)? onStateChanged;

  const InteractiveTriangleAngleSumWidget({
    Key? key,
    required this.data,
    this.onStateChanged,
  }) : super(key: key);

  /// Factory constructor to create the widget from JSON data
  factory InteractiveTriangleAngleSumWidget.fromData(Map<String, dynamic> data) {
    return InteractiveTriangleAngleSumWidget(
      data: data,
    );
  }

  @override
  State<InteractiveTriangleAngleSumWidget> createState() => _InteractiveTriangleAngleSumWidgetState();
}

class _InteractiveTriangleAngleSumWidgetState extends State<InteractiveTriangleAngleSumWidget> {
  // Triangle vertices
  late List<Offset> _vertices;
  
  // Colors
  late Color _triangleColor;
  late Color _angleColor;
  late Color _vertexColor;
  late Color _textColor;
  
  // Interaction state
  int? _draggedVertexIndex;
  bool _isCompleted = false;
  bool _showAngles = true;
  bool _showDegrees = true;
  bool _showSum = true;
  
  // Challenge mode
  bool _challengeMode = false;
  double _targetSum = 180.0;
  double _tolerance = 2.0; // Tolerance in degrees
  String _challengeText = '';
  bool _challengeCompleted = false;

  @override
  void initState() {
    super.initState();
    
    // Initialize vertices with default triangle or from data
    final defaultVertices = [
      Offset(150, 50),   // Top vertex
      Offset(50, 250),   // Bottom left vertex
      Offset(250, 250),  // Bottom right vertex
    ];
    
    _vertices = List<Offset>.from(
      (widget.data['vertices'] as List<dynamic>?)?.map(
        (v) => Offset(v['x']?.toDouble() ?? 0, v['y']?.toDouble() ?? 0),
      ) ?? defaultVertices,
    );
    
    // Initialize colors
    _triangleColor = _parseColor(widget.data['triangle_color'], Colors.blue.withOpacity(0.2));
    _angleColor = _parseColor(widget.data['angle_color'], Colors.orange);
    _vertexColor = _parseColor(widget.data['vertex_color'], Colors.blue);
    _textColor = _parseColor(widget.data['text_color'], Colors.black87);
    
    // Initialize display options
    _showAngles = widget.data['show_angles'] ?? true;
    _showDegrees = widget.data['show_degrees'] ?? true;
    _showSum = widget.data['show_sum'] ?? true;
    
    // Initialize challenge mode
    _challengeMode = widget.data['challenge_mode'] ?? false;
    _targetSum = widget.data['target_sum']?.toDouble() ?? 180.0;
    _tolerance = widget.data['tolerance']?.toDouble() ?? 2.0;
    _challengeText = widget.data['challenge_text'] ?? 'Create a triangle with interior angles summing to $_targetSum°';
  }

  // Helper method to parse color from string
  Color _parseColor(dynamic colorValue, Color defaultColor) {
    if (colorValue == null) return defaultColor;
    if (colorValue is String) {
      try {
        return Color(int.parse(colorValue.replaceAll('#', '0xFF')));
      } catch (e) {
        return defaultColor;
      }
    }
    return defaultColor;
  }

  // Calculate the angle between three points (in degrees)
  double _calculateAngle(Offset a, Offset b, Offset c) {
    final ab = math.sqrt(math.pow(b.dx - a.dx, 2) + math.pow(b.dy - a.dy, 2));
    final bc = math.sqrt(math.pow(c.dx - b.dx, 2) + math.pow(c.dy - b.dy, 2));
    final ac = math.sqrt(math.pow(c.dx - a.dx, 2) + math.pow(c.dy - a.dy, 2));
    
    // Law of cosines: cos(B) = (a² + c² - b²) / (2ac)
    final cosB = (math.pow(ab, 2) + math.pow(bc, 2) - math.pow(ac, 2)) / (2 * ab * bc);
    
    // Clamp to handle floating point errors
    final clampedCosB = cosB.clamp(-1.0, 1.0);
    
    // Convert to degrees
    return (math.acos(clampedCosB) * 180 / math.pi);
  }

  // Calculate all three angles of the triangle
  List<double> _calculateAngles() {
    final angleA = _calculateAngle(_vertices[2], _vertices[0], _vertices[1]);
    final angleB = _calculateAngle(_vertices[0], _vertices[1], _vertices[2]);
    final angleC = _calculateAngle(_vertices[1], _vertices[2], _vertices[0]);
    return [angleA, angleB, angleC];
  }

  // Calculate the sum of all angles
  double _calculateAngleSum() {
    final angles = _calculateAngles();
    return angles.reduce((a, b) => a + b);
  }

  // Check if the challenge is completed
  void _checkChallengeCompletion() {
    if (!_challengeMode) return;
    
    final sum = _calculateAngleSum();
    final completed = (sum - _targetSum).abs() <= _tolerance;
    
    if (completed != _challengeCompleted) {
      setState(() {
        _challengeCompleted = completed;
      });
      
      // Notify parent about completion
      widget.onStateChanged?.call(completed);
    }
  }

  @override
  Widget build(BuildContext context) {
    final angles = _calculateAngles();
    final sum = angles.reduce((a, b) => a + b);
    
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withOpacity(0.2),
            spreadRadius: 1,
            blurRadius: 3,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          if (_challengeMode)
            Padding(
              padding: const EdgeInsets.only(bottom: 12),
              child: Text(
                _challengeText,
                style: TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.bold,
                  color: _challengeCompleted ? Colors.green : _textColor,
                ),
              ),
            ),
          
          // Controls
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceEvenly,
            children: [
              ChoiceChip(
                label: const Text('Show Angles'),
                selected: _showAngles,
                onSelected: (selected) {
                  setState(() {
                    _showAngles = selected;
                  });
                },
              ),
              ChoiceChip(
                label: const Text('Show Degrees'),
                selected: _showDegrees,
                onSelected: (selected) {
                  setState(() {
                    _showDegrees = selected;
                  });
                },
              ),
              ChoiceChip(
                label: const Text('Show Sum'),
                selected: _showSum,
                onSelected: (selected) {
                  setState(() {
                    _showSum = selected;
                  });
                },
              ),
            ],
          ),
          
          const SizedBox(height: 16),
          
          // Triangle canvas
          SizedBox(
            height: 300,
            child: GestureDetector(
              onPanDown: (details) {
                final localPosition = details.localPosition;
                // Find if a vertex was tapped
                for (int i = 0; i < _vertices.length; i++) {
                  if ((localPosition - _vertices[i]).distance < 20) {
                    setState(() {
                      _draggedVertexIndex = i;
                    });
                    break;
                  }
                }
              },
              onPanUpdate: (details) {
                if (_draggedVertexIndex != null) {
                  setState(() {
                    _vertices[_draggedVertexIndex!] += details.delta;
                    _checkChallengeCompletion();
                  });
                }
              },
              onPanEnd: (details) {
                setState(() {
                  _draggedVertexIndex = null;
                });
              },
              child: CustomPaint(
                size: const Size(double.infinity, 300),
                painter: TriangleAnglePainter(
                  vertices: _vertices,
                  angles: angles,
                  triangleColor: _triangleColor,
                  angleColor: _angleColor,
                  vertexColor: _vertexColor,
                  textColor: _textColor,
                  showAngles: _showAngles,
                  showDegrees: _showDegrees,
                ),
              ),
            ),
          ),
          
          const SizedBox(height: 16),
          
          // Angle sum display
          if (_showSum)
            Center(
              child: Container(
                padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                decoration: BoxDecoration(
                  color: Colors.grey.withOpacity(0.1),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Text(
                  'Sum of angles: ${sum.toStringAsFixed(1)}°',
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                    color: _challengeMode
                        ? ((sum - _targetSum).abs() <= _tolerance ? Colors.green : Colors.red)
                        : _textColor,
                  ),
                ),
              ),
            ),
          
          // Widget name tag
          if (widget.data['showNameTag'] ?? true)
            Padding(
              padding: const EdgeInsets.only(top: 8),
              child: Text(
                'InteractiveTriangleAngleSumWidget',
                style: TextStyle(
                  color: Colors.grey[600],
                  fontSize: 12,
                  fontStyle: FontStyle.italic,
                ),
              ),
            ),
        ],
      ),
    );
  }
}

/// Custom painter for drawing the triangle and angles
class TriangleAnglePainter extends CustomPainter {
  final List<Offset> vertices;
  final List<double> angles;
  final Color triangleColor;
  final Color angleColor;
  final Color vertexColor;
  final Color textColor;
  final bool showAngles;
  final bool showDegrees;

  TriangleAnglePainter({
    required this.vertices,
    required this.angles,
    required this.triangleColor,
    required this.angleColor,
    required this.vertexColor,
    required this.textColor,
    required this.showAngles,
    required this.showDegrees,
  });

  @override
  void paint(Canvas canvas, Size size) {
    final paint = Paint()
      ..color = triangleColor
      ..style = PaintingStyle.fill;
    
    // Draw triangle
    final path = Path()
      ..moveTo(vertices[0].dx, vertices[0].dy)
      ..lineTo(vertices[1].dx, vertices[1].dy)
      ..lineTo(vertices[2].dx, vertices[2].dy)
      ..close();
    
    canvas.drawPath(path, paint);
    
    // Draw triangle outline
    final outlinePaint = Paint()
      ..color = Colors.black54
      ..style = PaintingStyle.stroke
      ..strokeWidth = 2;
    
    canvas.drawPath(path, outlinePaint);
    
    // Draw angle arcs if enabled
    if (showAngles) {
      final anglePaint = Paint()
        ..color = angleColor
        ..style = PaintingStyle.stroke
        ..strokeWidth = 2;
      
      // Draw angle at each vertex
      for (int i = 0; i < vertices.length; i++) {
        final prev = vertices[(i + 2) % 3];
        final curr = vertices[i];
        final next = vertices[(i + 1) % 3];
        
        // Calculate vectors
        final v1 = Offset(prev.dx - curr.dx, prev.dy - curr.dy);
        final v2 = Offset(next.dx - curr.dx, next.dy - curr.dy);
        
        // Calculate angles for arc
        final angle1 = math.atan2(v1.dy, v1.dx);
        final angle2 = math.atan2(v2.dy, v2.dx);
        
        // Draw arc
        canvas.drawArc(
          Rect.fromCircle(center: curr, radius: 20),
          angle1,
          (angle2 - angle1 + 2 * math.pi) % (2 * math.pi),
          false,
          anglePaint,
        );
        
        // Draw angle text if enabled
        if (showDegrees) {
          final textPainter = TextPainter(
            text: TextSpan(
              text: '${angles[i].toStringAsFixed(1)}°',
              style: TextStyle(
                color: textColor,
                fontSize: 14,
                fontWeight: FontWeight.bold,
              ),
            ),
            textDirection: TextDirection.ltr,
          );
          
          textPainter.layout();
          
          // Position text at a slight offset from the vertex
          final midAngle = (angle1 + angle2) / 2;
          final textOffset = Offset(
            curr.dx + 35 * math.cos(midAngle),
            curr.dy + 35 * math.sin(midAngle),
          );
          
          textPainter.paint(
            canvas,
            textOffset - Offset(textPainter.width / 2, textPainter.height / 2),
          );
        }
      }
    }
    
    // Draw vertices
    final vertexPaint = Paint()
      ..color = vertexColor
      ..style = PaintingStyle.fill;
    
    for (final vertex in vertices) {
      canvas.drawCircle(vertex, 8, vertexPaint);
    }
  }

  @override
  bool shouldRepaint(covariant TriangleAnglePainter oldDelegate) {
    return vertices != oldDelegate.vertices ||
           angles != oldDelegate.angles ||
           triangleColor != oldDelegate.triangleColor ||
           angleColor != oldDelegate.angleColor ||
           vertexColor != oldDelegate.vertexColor ||
           textColor != oldDelegate.textColor ||
           showAngles != oldDelegate.showAngles ||
           showDegrees != oldDelegate.showDegrees;
  }
}
