{"id": "rfr_predicate_logic", "title": "Predicate Logic: Reasoning with Quantifiers", "description": "Extend logical analysis to statements about properties of objects and relationships between them.", "order": 2, "lessons": [{"id": "rfr-prl-l1-predicates-quantifiers", "title": "Predicates and Quantifiers: 'All' and 'Some'", "description": "Learn to symbolize quantified statements.", "order": 1, "type": "interactive_lesson", "estimatedTimeMinutes": 15, "xp_reward": 100, "contentBlocks": [{"id": "rfr-prl-l1-s1", "type": "lesson_screen", "order": 1, "estimatedTimeSeconds": 30, "content": {"headline": "Beyond Propositions", "body_md": "Propositional logic is powerful, but it can't capture the internal structure of statements like 'All humans are mortal' or 'Some cats are black.' To do this, we need **Predicate Logic** (also known as First-Order Logic). It extends propositional logic by introducing **predicates** and **quantifiers**.", "visual": {"type": "giphy_search", "value": "logic thinking"}, "interactive_element": {"type": "button", "button_text": "Let's Dive In!"}}}, {"id": "rfr-prl-l1-s2", "type": "lesson_screen", "order": 2, "estimatedTimeSeconds": 45, "content": {"headline": "What are Predicates?", "body_md": "A **predicate** is like a sentence with a blank, or a property that an object can have, or a relation between objects. \n\nExamples:\n*   `___ is mortal.` We can write this as `M(x)`, where `M` is the predicate 'is mortal' and `x` is a variable representing an object.\n*   `___ is taller than ___.` We can write this as `T(x, y)`, where `T` is the predicate 'is taller than', and `x` and `y` are variables.\n\nVariables like `x` and `y` act as placeholders for specific objects.", "visual": {"type": "static_text", "value": "P(x)\nR(x, y)"}, "interactive_element": {"type": "button", "button_text": "Got it!"}}}, {"id": "rfr-prl-l1-s3", "type": "lesson_screen", "order": 3, "estimatedTimeSeconds": 60, "content": {"headline": "Quantifiers: Universal (FOR ALL)", "body_md": "To make general statements, we use **quantifiers**. The first is the **Universal Quantifier**.\n\n*   Symbol: **∀** (looks like an upside-down 'A' for 'All')\n*   Meaning: 'For all', 'For every', 'For each'.\n*   Usage: `∀x P(x)` means 'For all x, P(x) is true.'\n\nExample: If `H(x)` means 'x is human' and `M(x)` means 'x is mortal', then `∀x (H(x) → M(x))` translates to 'All humans are mortal' (or 'For every x, if x is a human, then x is mortal').", "visual": {"type": "giphy_search", "value": "universe for all"}, "interactive_element": {"type": "button", "button_text": "Next: Another Quantifier"}}}, {"id": "rfr-prl-l1-s4", "type": "lesson_screen", "order": 4, "estimatedTimeSeconds": 60, "content": {"headline": "Quantifiers: Existential (THERE EXISTS)", "body_md": "The second main quantifier is the **Existential Quantifier**.\n\n*   Symbol: **∃** (looks like a backwards 'E' for 'Exists')\n*   Meaning: 'There exists', 'For some', 'There is at least one'.\n*   Usage: `∃x P(x)` means 'There exists at least one x such that P(x) is true.'\n\nExample: If `B(x)` means 'x is a bird' and `F(x)` means 'x can fly', then `∃x (B(x) ∧ F(x))` translates to 'Some birds can fly' (or 'There exists an x such that x is a bird and x can fly').", "visual": {"type": "giphy_search", "value": "discovery exists"}, "interactive_element": {"type": "button", "button_text": "Let's Practice!"}}}, {"id": "rfr-prl-l1-s5", "type": "lesson_screen", "order": 5, "estimatedTimeSeconds": 90, "content": {"headline": "Quick Check: Translation", "body_md": "Time to try translating a statement!", "visual": {"type": "static_text", "value": "Cx: x is a cat\nMx: x is a mammal"}, "interactive_element": {"type": "interactive_predicate_quantifier_translator", "prompt": "Translate the following natural language statement into predicate logic: 'All cats are mammals.'\nUse `Cx` for 'x is a cat' and `Mx` for 'x is a mammal'.", "natural_language_statement": "All cats are mammals.", "predicates_provided": [{"symbol": "Cx", "meaning": "x is a cat"}, {"symbol": "Mx", "meaning": "x is a mammal"}], "correct_answer_regex": "^∀x\\s*\\(\\s*Cx\\s*→\\s*Mx\\s*\\)$", "feedback_correct": "Correct! This reads 'For all x, if x is a cat, then x is a mammal.' The structure ∀x (Condition(x) → Consequence(x)) is common for 'All' statements.", "feedback_incorrect_template": "Not quite. Remember how 'all' statements are typically structured with conditionals. The universal quantifier (∀) is used, and it usually involves an implication (→). The correct form is ∀x (Cx → Mx)."}}}]}, {"id": "rfr-prl-l2-translating-natural-language", "title": "Translating Natural Language into Predicate Logic", "description": "Formalize everyday statements.", "order": 2, "type": "interactive_lesson", "estimatedTimeMinutes": 20, "xp_reward": 120, "contentBlocks": [{"id": "rfr-prl-l2-s1", "type": "lesson_screen", "order": 1, "estimatedTimeSeconds": 30, "content": {"headline": "The Art of Translation", "body_md": "Translating from natural language (like English) to predicate logic is a crucial skill. It requires careful attention to the meaning and logical structure of sentences. It's like being a detective for logic!", "visual": {"type": "giphy_search", "value": "detective translate"}, "interactive_element": {"type": "button", "button_text": "Show Me Patterns"}}}, {"id": "rfr-prl-l2-s2", "type": "lesson_screen", "order": 2, "estimatedTimeSeconds": 90, "content": {"headline": "Common Translation Patterns", "body_md": "Here are some standard ways to translate common English phrases:\n\n*   **'All A are B'**:  `∀x (A(x) → B(x))`\n    *   _Example: 'All dogs bark.' → ∀x (Dog(x) → Barks(x))_\n*   **'No A are B'**: `∀x (A(x) → ¬B(x))`  OR  `¬∃x (A(x) ∧ B(x))`\n    *   _Example: 'No cats are green.' → ∀x (Cat(x) → ¬<PERSON>reen(x))_\n*   **'Some A are B'**: `∃x (A(x) ∧ B(x))`\n    *   _Example: 'Some students are clever.' → ∃x (Student(x) ∧ <PERSON>lever(x))_\n*   **'Some A are not B'**: `∃x (A(x) ∧ ¬B(x))`\n    *   _Example: 'Some birds do not fly.' → ∃x (Bird(x) ∧ ¬Flies(x))_", "visual": {"type": "static_text", "value": "∀x (Ax → Bx)\n∃x (Ax ∧ Bx)"}, "interactive_element": {"type": "button", "button_text": "Let's Try One"}}}, {"id": "rfr-prl-l2-s3", "type": "lesson_screen", "order": 3, "estimatedTimeSeconds": 75, "content": {"headline": "Translate This!", "body_md": "Statement: 'Some students are hardworking.'\nLet `S(x)` = 'x is a student', `H(x)` = 'x is hardworking'.\n\nHow would you translate this?", "visual": {"type": "giphy_search", "value": "thinking hard"}, "interactive_element": {"type": "multiple_choice_text", "question_text": "Choose the correct translation for 'Some students are hardworking.'", "options": [{"id": "opt1", "text": "∀x (S(x) → H(x))", "is_correct": false, "feedback": "This means 'All students are hardworking.' Remember 'some' uses the existential quantifier (∃) and conjunction (∧)."}, {"id": "opt2", "text": "∃x (S(x) → H(x))", "is_correct": false, "feedback": "Close! But 'some' statements usually pair the existential quantifier (∃) with conjunction (∧), not implication (→). An implication here would mean 'There is something that, if it's a student, then it's hardworking' which is true even for non-students."}, {"id": "opt3", "text": "∃x (S(x) ∧ H(x))", "is_correct": true, "feedback": "Perfect! This correctly captures 'There exists at least one x such that x is a student AND x is hardworking.'"}, {"id": "opt4", "text": "∀x (S(x) ∧ H(x))", "is_correct": false, "feedback": "This means 'Everything is a student and is hardworking,' which is too strong."}], "action_button_text": "Check Answer"}}}, {"id": "rfr-prl-l2-s4", "type": "lesson_screen", "order": 4, "estimatedTimeSeconds": 90, "content": {"headline": "Your Turn: 'No Dogs are Reptiles'", "body_md": "Translate: 'No dogs are reptiles.'\nUse `D(x)` for 'x is a dog' and `R(x)` for 'x is a reptile'.", "visual": {"type": "static_text", "value": "Dx: x is a dog\nRx: x is a reptile"}, "interactive_element": {"type": "interactive_predicate_quantifier_translator", "prompt": "Enter your translation for 'No dogs are reptiles.'", "natural_language_statement": "No dogs are reptiles.", "predicates_provided": [{"symbol": "Dx", "meaning": "x is a dog"}, {"symbol": "Rx", "meaning": "x is a reptile"}], "correct_answer_regex": "^(∀x\\s*\\(\\s*Dx\\s*→\\s*¬\\s*Rx\\s*\\)|¬∃x\\s*\\(\\s*Dx\\s*∧\\s*Rx\\s*\\))$", "feedback_correct": "Excellent! Both ∀x (Dx → ¬Rx) ('For all x, if x is a dog, then x is not a reptile') and ¬∃x (Dx ∧ Rx) ('It is not the case that there exists an x such that x is a dog and x is a reptile') are valid translations.", "feedback_incorrect_template": "Not quite. For 'No A are B', think about what it means: if something is an A, then it's *not* a B. This usually involves ∀ and → and ¬, or ¬∃ and ∧."}}}]}, {"id": "rfr-prl-l3-rules-inference-quantifiers", "title": "Rules of Inference for Quantifiers", "description": "Apply logical rules for 'all' and 'some.'", "order": 3, "type": "interactive_lesson", "estimatedTimeMinutes": 20, "xp_reward": 130, "contentBlocks": [{"id": "rfr-prl-l3-s1", "type": "lesson_screen", "order": 1, "estimatedTimeSeconds": 45, "content": {"headline": "Making Deductions with Quantifiers", "body_md": "Just like in propositional logic, we need rules of inference to construct proofs in predicate logic. These rules allow us to correctly derive conclusions from premises involving ∀ and ∃.", "visual": {"type": "giphy_search", "value": "magnifying glass deduction"}, "interactive_element": {"type": "button", "button_text": "Introduce the Rules"}}}, {"id": "rfr-prl-l3-s2", "type": "lesson_screen", "order": 2, "estimatedTimeSeconds": 90, "content": {"headline": "Key Quantifier Rules (Part 1)", "body_md": "**1. Universal Instantiation (UI):**\n*   From `∀x P(x)`, you can infer `P(a)` (where 'a' is any specific constant or individual from the domain).\n*   _Intuition: If something is true for ALL things, it's true for any specific thing you pick._\n\n**2. Existential Generalization (EG):**\n*   From `P(a)`, you can infer `∃x P(x)`.\n*   _Intuition: If something is true for a specific thing, then there EXISTS at least one thing for which it's true._", "visual": {"type": "static_text", "value": "∀x Px  ⊢  Pa\nPa  ⊢  ∃x Px"}, "interactive_element": {"type": "button", "button_text": "Next Set of Rules"}}}, {"id": "rfr-prl-l3-s3", "type": "lesson_screen", "order": 3, "estimatedTimeSeconds": 90, "content": {"headline": "Key Quantifier Rules (Part 2)", "body_md": "**3. Universal Generalization (UG):**\n*   From `P(a)` (where 'a' is an *arbitrarily chosen* individual from the domain), you can infer `∀x P(x)`.\n*   _Restriction: 'a' must be truly arbitrary, not a specific pre-mentioned constant._\n\n**4. Existential Instantiation (EI):**\n*   From `∃x P(x)`, you can infer `P(a)` (where 'a' is a *new* constant, not previously used in the proof, representing an individual for whom P is true).\n*   _Restriction: 'a' must be a fresh name you introduce to stand for the existing thing._", "visual": {"type": "giphy_search", "value": "caution rules"}, "interactive_element": {"type": "button", "button_text": "Let's Apply Them"}}}, {"id": "rfr-prl-l3-s4", "type": "lesson_screen", "order": 4, "estimatedTimeSeconds": 120, "content": {"headline": "Applying UI and Modus Ponens", "body_md": "Premises:\n1. 'All philosophers are wise': `∀x (P(x) → W(x))`\n2. 'Socrates is a philosopher': `P(s)`\n\nWhat can you infer about <PERSON><PERSON>' wisdom (`W(s)`)?", "visual": {"type": "static_text", "value": "∀x (Px → Wx)\nPs\n∴ ?"}, "interactive_element": {"type": "interactive_rule_of_inference_applier", "prompt": "First, apply Universal Instantiation (UI) to Premise 1 using 's' for x. What do you get?", "steps": [{"step_prompt": "Apply UI to `∀x (P(x) → W(x))` with `x=s`:", "correct_answer_regex": "^P\\(s\\)\\s*→\\s*W\\(s\\)$", "feedback_correct": "Correct! UI gives us `P(s) → W(s)`.", "feedback_incorrect": "Remember UI: if ∀x (something(x)), then something(s)."}, {"step_prompt": "Now you have:\n1. `P(s) → W(s)` (from UI)\n2. `P(s)` (original premise)\n\nWhat rule can you apply, and what's the conclusion?", "correct_answer_regex": "^(Ws|W\\(s\\))$", "feedback_correct": "Exactly! Using <PERSON><PERSON> on `P(s) → W(s)` and `P(s)`, we conclude `W(s)` (<PERSON><PERSON> is wise).", "feedback_incorrect": "Look for a pattern like A → B, A. What does that allow you to conclude?"}], "final_conclusion_prompt": "So, the final conclusion is:"}}}, {"id": "rfr-prl-l3-s5", "type": "lesson_screen", "order": 5, "estimatedTimeSeconds": 30, "content": {"headline": "Important Note!", "body_md": "Be very careful with the restrictions on Universal Generalization (UG) – the individual must be *arbitrarily chosen*. And for Existential Instantiation (EI) – the constant introduced must be *new* to the proof.", "visual": {"type": "giphy_search", "value": "warning sign"}, "interactive_element": {"type": "button", "button_text": "Understood!"}}}]}, {"id": "rfr-prl-l4-validity-predicate-logic", "title": "Validity and Invalidity in Predicate Logic", "description": "Determine the logical strength of quantified arguments.", "order": 4, "type": "interactive_lesson", "estimatedTimeMinutes": 20, "xp_reward": 130, "contentBlocks": [{"id": "rfr-prl-l4-s1", "type": "lesson_screen", "order": 1, "estimatedTimeSeconds": 45, "content": {"headline": "What Makes an Argument Valid?", "body_md": "An argument in predicate logic is **valid** if and only if, in every possible interpretation (or 'model') where all the premises are true, the conclusion is also true. \n\n*   Proving validity often involves constructing a formal proof using rules of inference.\n*   Proving **invalidity** involves finding a **counterexample model**.", "visual": {"type": "giphy_search", "value": "balance scale true false"}, "interactive_element": {"type": "button", "button_text": "Tell Me About Counterexamples"}}}, {"id": "rfr-prl-l4-s2", "type": "lesson_screen", "order": 2, "estimatedTimeSeconds": 75, "content": {"headline": "Counterexample Models", "body_md": "A **counterexample model** for a predicate logic argument consists of:\n1.  A non-empty **domain of discourse** (the set of objects we're talking about, e.g., {1, 2}, {a, b, c}).\n2.  An **interpretation** of all predicate symbols (e.g., what P(x) means for each object in the domain) and constants used in the argument.\n\nThis interpretation must make all premises TRUE and the conclusion FALSE.", "visual": {"type": "giphy_search", "value": "broken chain"}, "interactive_element": {"type": "button", "button_text": "Show an Example"}}}, {"id": "rfr-prl-l4-s3", "type": "lesson_screen", "order": 3, "estimatedTimeSeconds": 120, "content": {"headline": "Example of Invalidity", "body_md": "Argument:\n1. `∀x (A(x) → B(x))` (All A's are B's)\n2. `∃x B(x)` (Some B's exist)\n∴ `∃x A(x)` (Therefore, some A's exist)\n\nIs this valid? Let's try to find a counterexample.", "visual": {"type": "static_text", "value": "∀x(Ax→Bx)\n∃x Bx\n∴ ∃x Ax"}, "interactive_element": {"type": "multiple_choice_text", "question_text": "Consider a domain {apple, banana}. Let A(x) be 'x is red' and B(x) be 'x is a fruit'. If apple is a red fruit, and banana is a yellow fruit. Is the argument valid with this interpretation?", "options": [{"id": "v1", "text": "Yes, it's valid.", "is_correct": false, "feedback": "Not quite. Let's check the premises and conclusion. Domain: {apple, banana}. A(x): x is red. B(x): x is fruit. \nPremise 1: ∀x (A(x) → B(x)). (Red things are fruits). True if apple is red fruit, banana is not red. (e.g. A(apple)=T, B(apple)=T; A(banana)=F, B(banana)=T). (T→T)=T, (F→T)=T. So P1 is True.\nPremise 2: ∃x B(x). (Some fruits exist). True (apple, banana are fruits). So P2 is True.\nConclusion: ∃x A(x). (Some red things exist). True (apple is red). \nThis specific interpretation makes premises and conclusion true, but doesn't prove validity. We need a case where premises are true and conclusion is FALSE for invalidity."}, {"id": "v2", "text": "No, it's invalid. This interpretation is a counterexample.", "is_correct": false, "feedback": "This interpretation makes the conclusion true, so it's not a counterexample. A counterexample needs a FALSE conclusion with TRUE premises."}, {"id": "v3", "text": "No, it's invalid. We can find another counterexample.", "is_correct": true, "feedback": "Correct! The argument is invalid. Consider Domain: {1, 2}. Let A = { } (nothing is A). Let B = {1} (only 1 is B). \nP1: ∀x (A(x) → B(x)) is True (A(1)→B(1) is F→T=T; A(2)→B(2) is F→F=T). \nP2: ∃x B(x) is True (B(1) is true). \nConclusion: ∃x A(x) is False (nothing is A). Premises true, conclusion false!"}], "action_button_text": "Check Validity"}}}, {"id": "rfr-prl-l4-s4", "type": "lesson_screen", "order": 4, "estimatedTimeSeconds": 120, "content": {"headline": "Spot the Flaw!", "body_md": "Argument: `∃x F(x)`, `∃x G(x)`. Therefore, `∃x (F(x) ∧ G(x))`\n(Some things are F. Some things are G. Therefore, some things are both F and G.)\n\nIs this valid? Try to find a counterexample model.", "visual": {"type": "giphy_search", "value": "question mark puzzle"}, "interactive_element": {"type": "interactive_quantifier_scope_highlighter", "prompt": "Define a domain and interpretations for F and G that make the premises true and the conclusion false.", "argument_string": "∃x Fx, ∃x Gx ∴ ∃x (Fx ∧ Gx)", "is_valid": false, "feedback_valid": "This argument is actually invalid. Can you show why?", "feedback_invalid_prompt_for_model": "Good, it's invalid! Now, provide a counterexample. Domain (e.g., {a,b}): Interpretations for F (e.g., Fa, ¬Fb): Interpretations for G (e.g., ¬Ga, Gb):", "counterexample_model_details": {"domain_placeholder": "{a, b}", "interpretation_F_placeholder": "F(a)=True, F(b)=False", "interpretation_G_placeholder": "G(a)=False, G(b)=True", "explanation": "A common counterexample: Domain: {a, b}. Let F(a) be true, F(b) false. Let G(a) be false, G(b) true. \nPremise 1 (∃x F(x)): True (because of a). \nPremise 2 (∃x G(x)): True (because of b). \nConclusion (∃x (F(x) ∧ G(x))): False (neither 'a' nor 'b' makes both F and G true simultaneously)."}}}}]}, {"id": "rfr-prl-l5-applications-predicate-logic", "title": "Applications of Predicate Logic", "description": "Explore uses in mathematics and computer science.", "order": 5, "type": "interactive_lesson", "estimatedTimeMinutes": 15, "xp_reward": 100, "contentBlocks": [{"id": "rfr-prl-l5-s1", "type": "lesson_screen", "order": 1, "estimatedTimeSeconds": 45, "content": {"headline": "Logic in Action!", "body_md": "Predicate logic isn't just an abstract game. It's a foundational tool in many fields due to its ability to express complex relationships and properties with precision.", "visual": {"type": "giphy_search", "value": "gears working together"}, "interactive_element": {"type": "button", "button_text": "Where is it Used?"}}}, {"id": "rfr-prl-l5-s2", "type": "lesson_screen", "order": 2, "estimatedTimeSeconds": 60, "content": {"headline": "Mathematics", "body_md": "Predicate logic is the backbone of modern mathematics!\n*   **Formalizing Theories:** Used to precisely define mathematical concepts (like sets, numbers, functions).\n*   **Constructing Proofs:** Provides the framework for rigorous mathematical proofs.\n*   **Set Theory:** Axiomatic set theory (like ZFC) is expressed using predicate logic.", "visual": {"type": "unsplash_search", "value": "mathematical equations blackboard"}, "interactive_element": {"type": "button", "button_text": "What about Computers?"}}}, {"id": "rfr-prl-l5-s3", "type": "lesson_screen", "order": 3, "estimatedTimeSeconds": 90, "content": {"headline": "Computer Science", "body_md": "Predicate logic is indispensable in Computer Science:\n*   **Database Theory:** SQL queries have a logical structure very similar to predicate logic formulas. (e.g., `SELECT name FROM students WHERE grade = 'A'`)\n*   **Artificial Intelligence:** Used for knowledge representation and automated reasoning systems.\n*   **Software Verification:** Formal methods use logic to prove that programs are correct and bug-free.\n*   **Logic Programming:** Languages like Prolog are based directly on predicate logic principles.", "visual": {"type": "unsplash_search", "value": "computer code server room"}, "interactive_element": {"type": "button", "button_text": "Quick Question!"}}}, {"id": "rfr-prl-l5-s4", "type": "lesson_screen", "order": 4, "estimatedTimeSeconds": 60, "content": {"headline": "Application Check", "body_md": "Let's see if you've got the hang of where predicate logic pops up.", "visual": {"type": "giphy_search", "value": "lightbulb idea"}, "interactive_element": {"type": "multiple_choice_text", "question_text": "Which field heavily uses predicate logic for defining database query languages like SQL?", "options": [{"id": "app1", "text": "Linguistics", "is_correct": false, "feedback": "While logic is used in linguistics, database query languages are more specific to computer science."}, {"id": "app2", "text": "Database Theory (Computer Science)", "is_correct": true, "feedback": "Correct! SQL, a common database language, has strong ties to predicate logic, allowing precise data retrieval."}, {"id": "app3", "text": "Literary Criticism", "is_correct": false, "feedback": "Literary criticism analyzes texts, but doesn't typically use formal predicate logic for database queries."}, {"id": "app4", "text": "Ancient Philosophy", "is_correct": false, "feedback": "Ancient philosophers developed early forms of logic, but SQL and database theory are modern computer science applications."}], "action_button_text": "Check Answer"}}}]}], "module_test": {"id": "rfr-prl-mt1-predicate-pioneer", "title": "Predicate Pioneer", "description": "Translate natural language into predicate logic and evaluate the validity of quantified arguments.", "order": 1, "type": "module_test_interactive", "estimatedTimeMinutes": 20, "xp_reward": 250, "contentBlocks": [{"id": "rfr-prl-mt1-q1", "type": "question_screen", "order": 1, "estimatedTimeSeconds": 180, "content": {"headline": "Question 1: <PERSON><PERSON><PERSON>'s Flight", "body_md": "Argument: 'All birds can fly. Tweety is a bird. Therefore, Tweety can fly.'\n\nDefine predicates:\n*   `B(x)`: x is a bird\n*   `F(x)`: x can fly\n*   `t`: Tweety (a constant)", "visual": {"type": "giphy_search", "value": "flying bird"}, "interactive_element": {"type": "predicate_translation_validity_challenge", "natural_language_statement": "All birds can fly. Tweety is a bird. Therefore, Tweety can fly.", "predicates_to_define": [{"symbol": "B(x)", "meaning": "x is a bird"}, {"symbol": "F(x)", "meaning": "x can fly"}, {"constant": "t", "meaning": "Tweety"}], "prompts": [{"id": "p1_trans1", "text": "Translate 'All birds can fly':", "correct_answer_regex": "^∀x\\s*\\(\\s*B\\(x\\)\\s*→\\s*F\\(x\\)\\s*\\)$"}, {"id": "p1_trans2", "text": "Translate 'Tweety is a bird':", "correct_answer_regex": "^B\\(t\\)$"}, {"id": "p1_trans3", "text": "Translate 'Tweety can fly' (Conclusion):", "correct_answer_regex": "^F\\(t\\)$"}], "validity_prompt": "Is the argument (as translated) valid?", "correct_is_valid": true, "feedback_correct_translation": "Translation looks good!", "feedback_incorrect_translation": "Check your translation for that part.", "feedback_validity_correct": "Correct! The argument is valid. This is a classic example of Universal Instantiation followed by <PERSON><PERSON>.", "feedback_validity_incorrect": "Think about the rules. If all birds fly, and <PERSON><PERSON><PERSON> is a bird, must <PERSON><PERSON><PERSON> fly?"}}}, {"id": "rfr-prl-mt1-q2", "type": "question_screen", "order": 2, "estimatedTimeSeconds": 240, "content": {"headline": "Question 2: Mammals and Carnivores", "body_md": "Argument: 'Some mammals are carnivores. All carnivores eat meat. Therefore, some mammals eat meat.'\n\nDefine predicates:\n*   `M(x)`: x is a mammal\n*   `C(x)`: x is a carnivore\n*   `E(x)`: x eats meat", "visual": {"type": "unsplash_search", "value": "lion eating"}, "interactive_element": {"type": "predicate_translation_validity_challenge", "natural_language_statement": "Some mammals are carnivores. All carnivores eat meat. Therefore, some mammals eat meat.", "predicates_to_define": [{"symbol": "M(x)", "meaning": "x is a mammal"}, {"symbol": "C(x)", "meaning": "x is a carnivore"}, {"symbol": "E(x)", "meaning": "x eats meat"}], "prompts": [{"id": "p2_trans1", "text": "Translate 'Some mammals are carnivores':", "correct_answer_regex": "^∃x\\s*\\(\\s*M\\(x\\)\\s*∧\\s*C\\(x\\)\\s*\\)$"}, {"id": "p2_trans2", "text": "Translate 'All carnivores eat meat':", "correct_answer_regex": "^∀x\\s*\\(\\s*C\\(x\\)\\s*→\\s*E\\(x\\)\\s*\\)$"}, {"id": "p2_trans3", "text": "Translate 'Some mammals eat meat' (Conclusion):", "correct_answer_regex": "^∃x\\s*\\(\\s*M\\(x\\)\\s*∧\\s*E\\(x\\)\\s*\\)$"}], "validity_prompt": "Is the argument (as translated) valid?", "correct_is_valid": true, "feedback_correct_translation": "Translations are spot on!", "feedback_incorrect_translation": "Double-check that part of your translation.", "feedback_validity_correct": "Correct! This argument is valid. It requires a few steps to prove formally (EI, UI, MP, Conj, EG), but the conclusion logically follows.", "feedback_validity_incorrect": "Consider the structure. If some mammals are carnivores, and all those carnivores eat meat, does it follow that some mammals must eat meat?"}}}]}}