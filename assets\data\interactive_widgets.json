[{"category": "maths", "widgets": [{"id": "interactive_variable_explorer_1", "name": "Variable Explorer", "type": "interactive_variable_explorer", "subcategory": "Algebra", "description": "An interactive tool for exploring how variables affect algebraic expressions.", "data": {"title": "Variable Explorer", "description": "Adjust the variables to see how they affect the expressions. Try to match the target values!", "primaryColor": "#2196F3", "secondaryColor": "#4CAF50", "accentColor": "#FF9800", "textColor": "#212121", "variables": [{"name": "x", "initialValue": 1.0, "min": -10.0, "max": 10.0, "divisions": 20}, {"name": "y", "initialValue": 2.0, "min": -10.0, "max": 10.0, "divisions": 20}, {"name": "z", "initialValue": 3.0, "min": -10.0, "max": 10.0, "divisions": 20}], "expressions": [{"formula": "2*x + 3*y", "display": "2x + 3y"}, {"formula": "x*y", "display": "x × y"}, {"formula": "x*x + y*y", "display": "x² + y²"}, {"formula": "z/(x+y)", "display": "z ÷ (x + y)"}], "targetValues": [10.0, 15.0, 25.0, 1.0], "tolerance": 0.1, "completionMessage": "Great job! You've found the right combination of values to match all targets.", "showNameTag": true}, "isImplemented": true}, {"id": "interactive_expression_builder_1", "name": "Expression Builder", "type": "interactive_expression_builder", "subcategory": "Algebra", "description": "An interactive tool for building algebraic expressions by arranging terms.", "data": {"title": "Expression Builder", "primaryColor": "#2196F3", "secondaryColor": "#4CAF50", "accentColor": "#FF9800", "textColor": "#212121", "successColor": "#4CAF50", "errorColor": "#F44336", "challenges": [{"description": "Build an expression that represents the area of a rectangle with length x and width y.", "targetExpression": "x * y", "availableTerms": ["x", "*", "y", "+", "-", "2", "3"]}, {"description": "Build an expression for the perimeter of a rectangle with length x and width y.", "targetExpression": "2 * x + 2 * y", "availableTerms": ["2", "*", "x", "+", "y", "-", "3", "*"]}, {"description": "Build an expression for the volume of a rectangular prism with length x, width y, and height z.", "targetExpression": "x * y * z", "availableTerms": ["x", "*", "y", "*", "z", "+", "-", "2"]}, {"description": "Build an expression for the Pythagorean theorem (a² + b² = c²).", "targetExpression": "a * a + b * b = c * c", "availableTerms": ["a", "*", "a", "+", "b", "*", "b", "=", "c", "*", "c", "-"]}], "showNameTag": true}, "isImplemented": true}, {"id": "interactive_number_line_explorer_1", "name": "Number Line Explorer", "type": "interactive_number_line_explorer", "subcategory": "Inequalities", "description": "An interactive tool for exploring number lines and inequalities.", "data": {"title": "Number Line Explorer", "primaryColor": "#2196F3", "secondaryColor": "#4CAF50", "accentColor": "#FF9800", "textColor": "#212121", "successColor": "#4CAF50", "errorColor": "#F44336", "minValue": -10, "maxValue": 10, "challenges": [{"description": "Represent the inequality: -3 < x < 5", "expression": "-3 < x < 5", "leftValue": -3, "rightValue": 5, "leftInclusive": false, "rightInclusive": false, "targetLeftValue": -3, "targetRightValue": 5, "targetLeftInclusive": false, "targetRightInclusive": false, "tolerance": 0.1, "successMessage": "Correct! You've represented the inequality -3 < x < 5 on the number line."}, {"description": "Represent the inequality: -2 ≤ x ≤ 7", "expression": "-2 ≤ x ≤ 7", "leftValue": -2, "rightValue": 7, "leftInclusive": true, "rightInclusive": true, "targetLeftValue": -2, "targetRightValue": 7, "targetLeftInclusive": true, "targetRightInclusive": true, "tolerance": 0.1, "successMessage": "Correct! You've represented the inequality -2 ≤ x ≤ 7 on the number line."}, {"description": "Represent the inequality: x ≤ 4", "expression": "x ≤ 4", "leftValue": -10, "rightValue": 4, "leftInclusive": true, "rightInclusive": true, "targetLeftValue": -10, "targetRightValue": 4, "targetLeftInclusive": true, "targetRightInclusive": true, "tolerance": 0.1, "successMessage": "Correct! You've represented the inequality x ≤ 4 on the number line."}], "showNameTag": true}, "isImplemented": true}, {"id": "interactive_inequality_visualizer_1", "name": "Inequality Visualizer", "type": "interactive_inequality_visualizer", "subcategory": "Inequalities", "description": "An interactive tool for visualizing linear inequalities on a coordinate plane.", "data": {"title": "Inequality Visualizer", "primaryColor": "#2196F3", "secondaryColor": "#4CAF50", "accentColor": "#FF9800", "textColor": "#212121", "successColor": "#4CAF50", "errorColor": "#F44336", "shadingColor": "#2196F3", "challenges": [{"description": "Represent the inequality: y > 2x + 1", "slope": 2, "yIntercept": 1, "inequalityType": ">", "isShaded": true, "targetSlope": 2, "targetYIntercept": 1, "targetInequalityType": ">", "targetIsShaded": true, "tolerance": 0.1, "successMessage": "Correct! You've represented the inequality y > 2x + 1 on the coordinate plane."}, {"description": "Represent the inequality: y ≤ -x + 3", "slope": -1, "yIntercept": 3, "inequalityType": "<=", "isShaded": true, "targetSlope": -1, "targetYIntercept": 3, "targetInequalityType": "<=", "targetIsShaded": true, "tolerance": 0.1, "successMessage": "Correct! You've represented the inequality y ≤ -x + 3 on the coordinate plane."}, {"description": "Represent the inequality: y < 0.5x - 2", "slope": 0.5, "yIntercept": -2, "inequalityType": "<", "isShaded": true, "targetSlope": 0.5, "targetYIntercept": -2, "targetInequalityType": "<", "targetIsShaded": true, "tolerance": 0.1, "successMessage": "Correct! You've represented the inequality y < 0.5x - 2 on the coordinate plane."}, {"description": "Represent the inequality: y ≥ -2x", "slope": -2, "yIntercept": 0, "inequalityType": ">=", "isShaded": true, "targetSlope": -2, "targetYIntercept": 0, "targetInequalityType": ">=", "targetIsShaded": true, "tolerance": 0.1, "successMessage": "Correct! You've represented the inequality y ≥ -2x on the coordinate plane."}], "showNameTag": true}, "isImplemented": true}, {"id": "interactive_one_step_inequality_solver_1", "name": "One-Step Inequality Solver", "type": "interactive_one_step_inequality_solver", "subcategory": "Inequalities", "description": "An interactive tool for solving one-step inequalities.", "data": {"title": "One-Step Inequality Solver", "primaryColor": "#2196F3", "secondaryColor": "#4CAF50", "textColor": "#212121", "backgroundColor": "#FFFFFF", "successColor": "#4CAF50", "errorColor": "#F44336", "inequalities": [{"inequality": "x + 3 > 7", "solution": "x > 4", "steps": ["Start with the original inequality: x + 3 > 7", "Subtract 3 from both sides: x + 3 - 3 > 7 - 3", "Simplify: x > 4"], "explanations": ["This is our starting inequality.", "To isolate the variable x, we need to subtract 3 from both sides.", "After simplifying, we get x > 4, which means all values of x greater than 4 satisfy the original inequality."], "operationType": "subtraction", "numberLineData": {"boundaryValue": 4, "boundaryIncluded": false, "direction": "right"}}, {"inequality": "2x ≤ 10", "solution": "x ≤ 5", "steps": ["Start with the original inequality: 2x ≤ 10", "Divide both sides by 2: 2x ÷ 2 ≤ 10 ÷ 2", "Simplify: x ≤ 5"], "explanations": ["This is our starting inequality.", "To isolate the variable x, we need to divide both sides by 2.", "After simplifying, we get x ≤ 5, which means all values of x less than or equal to 5 satisfy the original inequality."], "operationType": "division", "numberLineData": {"boundaryValue": 5, "boundaryIncluded": true, "direction": "left"}}, {"inequality": "-3x > 12", "solution": "x < -4", "steps": ["Start with the original inequality: -3x > 12", "Divide both sides by -3: -3x ÷ (-3) < 12 ÷ (-3)", "When dividing by a negative number, flip the inequality sign: x < -4"], "explanations": ["This is our starting inequality.", "To isolate the variable x, we need to divide both sides by -3.", "When dividing both sides of an inequality by a negative number, we must flip the inequality sign. '>' becomes '<'."], "operationType": "division with sign flip", "numberLineData": {"boundaryValue": -4, "boundaryIncluded": false, "direction": "left"}}]}, "isImplemented": true}, {"id": "interactive_two_step_inequality_solver_1", "name": "Two-Step Inequality Solver", "type": "interactive_two_step_inequality_solver", "subcategory": "Inequalities", "description": "An interactive tool for solving two-step inequalities.", "data": {"title": "Two-Step Inequality Solver", "primaryColor": "#2196F3", "secondaryColor": "#4CAF50", "textColor": "#212121", "backgroundColor": "#FFFFFF", "successColor": "#4CAF50", "errorColor": "#F44336", "inequalities": [{"inequality": "2x + 3 < 7", "solution": "x < 2", "steps": ["Start with the original inequality: 2x + 3 < 7", "Subtract 3 from both sides: 2x + 3 - 3 < 7 - 3", "Simplify: 2x < 4", "Divide both sides by 2: 2x ÷ 2 < 4 ÷ 2", "Simplify: x < 2"], "explanations": ["This is our starting inequality.", "To isolate the variable term, we first subtract 3 from both sides. When we perform the same operation on both sides of an inequality, the inequality sign stays the same.", "After simplifying, we get 2x < 4.", "To isolate the variable x, we need to divide both sides by 2. When we divide both sides of an inequality by a positive number, the inequality sign stays the same.", "After simplifying, we get x < 2, which means all values of x less than 2 satisfy the original inequality."], "operationType": "subtraction, division", "numberLineData": {"boundaryValue": 2, "boundaryIncluded": false, "direction": "left"}}, {"inequality": "3x - 6 ≤ 9", "solution": "x ≤ 5", "steps": ["Start with the original inequality: 3x - 6 ≤ 9", "Add 6 to both sides: 3x - 6 + 6 ≤ 9 + 6", "Simplify: 3x ≤ 15", "Divide both sides by 3: 3x ÷ 3 ≤ 15 ÷ 3", "Simplify: x ≤ 5"], "explanations": ["This is our starting inequality.", "To isolate the variable term, we first add 6 to both sides. When we perform the same operation on both sides of an inequality, the inequality sign stays the same.", "After simplifying, we get 3x ≤ 15.", "To isolate the variable x, we need to divide both sides by 3. When we divide both sides of an inequality by a positive number, the inequality sign stays the same.", "After simplifying, we get x ≤ 5, which means all values of x less than or equal to 5 satisfy the original inequality."], "operationType": "addition, division", "numberLineData": {"boundaryValue": 5, "boundaryIncluded": true, "direction": "left"}}, {"inequality": "4 - 2x > 10", "solution": "x < -3", "steps": ["Start with the original inequality: 4 - 2x > 10", "Subtract 4 from both sides: 4 - 2x - 4 > 10 - 4", "Simplify: -2x > 6", "Divide both sides by -2: -2x ÷ (-2) < 6 ÷ (-2)", "Simplify: x < -3"], "explanations": ["This is our starting inequality.", "To isolate the variable term, we first subtract 4 from both sides. When we perform the same operation on both sides of an inequality, the inequality sign stays the same.", "After simplifying, we get -2x > 6.", "To isolate the variable x, we need to divide both sides by -2. When we divide both sides of an inequality by a negative number, the inequality sign flips (> becomes < and vice versa).", "After simplifying, we get x < -3, which means all values of x less than -3 satisfy the original inequality."], "operationType": "subtraction, division with sign flip", "numberLineData": {"boundaryValue": -3, "boundaryIncluded": false, "direction": "left"}}]}, "isImplemented": true}, {"id": "interactive_inequality_grapher_1", "name": "Inequality Grapher", "type": "Interactive Grapher", "subcategory": "Inequalities", "description": "An interactive tool for graphing linear inequalities on a coordinate plane.", "data": {"title": "Inequality Grapher", "primaryColor": "#2196F3", "secondaryColor": "#4CAF50", "textColor": "#212121", "backgroundColor": "#FFFFFF", "successColor": "#4CAF50", "errorColor": "#F44336", "shadingColor": "#2196F3", "inequalities": [{"inequality": "y > 2x + 1", "description": "Graph the inequality y > 2x + 1", "slope": 2.0, "yIntercept": 1.0, "inequalityType": ">", "isLineInclusive": false, "isUpperHalfShaded": true}, {"inequality": "y ≤ -x + 3", "description": "Graph the inequality y ≤ -x + 3", "slope": -1.0, "yIntercept": 3.0, "inequalityType": "≤", "isLineInclusive": true, "isUpperHalfShaded": true}, {"inequality": "y < 0.5x - 2", "description": "Graph the inequality y < 0.5x - 2", "slope": 0.5, "yIntercept": -2.0, "inequalityType": "<", "isLineInclusive": false, "isUpperHalfShaded": false}, {"inequality": "y ≥ -2x", "description": "Graph the inequality y ≥ -2x", "slope": -2.0, "yIntercept": 0.0, "inequalityType": "≥", "isLineInclusive": true, "isUpperHalfShaded": true}]}, "isImplemented": true}, {"id": "interactive_inequality_investigator_1", "name": "Inequality Investigator", "type": "Multiple Choice", "subcategory": "Inequalities", "description": "An interactive quiz that tests students' understanding of inequalities.", "data": {"title": "Inequality Investigator", "primaryColor": "#2196F3", "secondaryColor": "#4CAF50", "textColor": "#212121", "backgroundColor": "#FFFFFF", "successColor": "#4CAF50", "errorColor": "#F44336", "questions": [{"questionText": "Solve for x: x - 7 > 3", "answerType": "text_input", "correctAnswer": "x > 10", "options": [], "explanation": "Add 7 to both sides: x > 10", "hint": "Add 7 to both sides."}, {"questionText": "Which symbol means 'less than or equal to'?", "answerType": "multiple_choice", "correctAnswer": "≤", "options": ["<", ">", "≤", "≥"], "explanation": "≤ means less than or equal to.", "hint": "Think about which symbol includes equality."}, {"questionText": "Solve for y: -5y ≤ 20", "answerType": "text_input", "correctAnswer": "y ≥ -4", "options": [], "explanation": "Divide by -5 and <PERSON><PERSON> the inequality sign: y ≥ -4", "hint": "Remember to flip the sign when dividing by a negative number."}, {"questionText": "Which graph represents x < 1?", "answerType": "multiple_choice", "correctAnswer": "Open circle at 1, arrow to the left", "options": ["Open circle at 1, arrow to the right", "Closed circle at 1, arrow to the right", "Open circle at 1, arrow to the left", "Closed circle at 1, arrow to the left"], "explanation": "For x < 1, we use an open circle at 1 (not included) and an arrow pointing left (less than).", "hint": "Think about whether 1 is included in the solution and which direction 'less than' points."}, {"questionText": "Solve for z: (z/2) + 3 ≥ 5", "answerType": "text_input", "correctAnswer": "z ≥ 4", "options": [], "explanation": "Subtract 3 from both sides: z/2 ≥ 2. Then multiply by 2: z ≥ 4", "hint": "First isolate the term with z, then multiply to isolate z."}, {"questionText": "When solving -3x > 9, why do we flip the inequality sign?", "answerType": "multiple_choice", "correctAnswer": "Because we're dividing by a negative number", "options": ["Because we're dividing by a negative number", "Because we're multiplying by a negative number", "Because the variable is negative", "We don't need to flip the sign"], "explanation": "When dividing or multiplying both sides of an inequality by a negative number, we must flip the inequality sign.", "hint": "Think about what happens to the direction of an inequality when you multiply or divide by a negative number."}]}, "isImplemented": true}, {"id": "interactive_arithmetic_sequence_explorer", "name": "Arithmetic Sequence Explorer", "type": "interactive_arithmetic_sequence_explorer", "subcategory": "Sequences", "description": "An interactive tool for exploring arithmetic sequences by adjusting the first term and common difference.", "data": {"title": "Arithmetic Sequence Explorer", "description": "Adjust the sliders to see how the first term and common difference affect the sequence.", "first_term": 1.0, "common_difference": 2.0, "number_of_terms": 10, "primary_color": "#4CAF50", "secondary_color": "#FF9800", "tertiary_color": "#2196F3", "background_color": "#FFFFFF", "text_color": "#212121", "show_formula": true, "show_differences": true, "show_graph": true, "show_table": true, "min_first_term": -10.0, "max_first_term": 10.0, "min_common_difference": -5.0, "max_common_difference": 5.0, "showNameTag": true}, "isImplemented": true}, {"id": "interactive_geometric_sequence_explorer", "name": "Geometric Sequence Explorer", "type": "interactive_geometric_sequence_explorer", "subcategory": "Sequences", "description": "An interactive tool for exploring geometric sequences by adjusting the first term and common ratio.", "data": {"title": "Geometric Sequence Explorer", "description": "Adjust the sliders to see how the first term and common ratio affect the sequence.", "first_term": 1.0, "common_ratio": 2.0, "number_of_terms": 10, "primary_color": "#FF5722", "secondary_color": "#00BCD4", "tertiary_color": "#9C27B0", "background_color": "#FFFFFF", "text_color": "#212121", "show_formula": true, "show_ratios": true, "show_graph": true, "show_table": true, "use_log_scale": true, "min_first_term": 0.1, "max_first_term": 10.0, "min_common_ratio": 0.1, "max_common_ratio": 5.0, "showNameTag": true}, "isImplemented": true}, {"id": "interactive_random_process_modeler", "name": "Random Process Modeler", "type": "interactive_random_process_modeler", "subcategory": "Probability", "description": "An interactive tool for modeling and visualizing random processes, such as coin flips.", "data": {"title": "Random Process Modeler (Coin Flip)", "description": "Adjust the number of trials and probability of heads to simulate coin flips.", "number_of_trials": 100, "probability_of_heads": 0.5, "primary_color": "#673AB7", "secondary_color": "#FFC107", "background_color": "#FFFFFF", "text_color": "#212121", "showNameTag": true}, "isImplemented": true}, {"id": "interactive_linear_probability_calculator", "name": "Linear Probability Calculator", "type": "interactive_linear_probability_calculator", "subcategory": "Probability", "description": "A widget that allows users to calculate probabilities for linear (uniform) distributions.", "data": {"title": "Linear Probability Calculator", "description": "Adjust the range and interval to calculate the probability.", "min_range": 0.0, "max_range": 10.0, "lower_bound": 2.0, "upper_bound": 8.0, "primary_color": "#009688", "secondary_color": "#FF5722", "background_color": "#FFFFFF", "text_color": "#212121", "showNameTag": true}, "isImplemented": true}, {"id": "interactive_exponential_decay_simulator", "name": "Exponential Decay Simulator", "type": "interactive_exponential_decay_simulator", "subcategory": "Functions", "description": "A widget that allows users to simulate and visualize exponential decay.", "data": {"title": "Exponential Decay Simulator", "description": "Adjust the initial amount and decay constant to see how a quantity decays over time.", "initial_amount": 100.0, "decay_constant": 0.1, "initial_time": 0.0, "primary_color": "#FF9800", "secondary_color": "#795548", "background_color": "#FFFFFF", "text_color": "#212121", "showNameTag": true}, "isImplemented": true}, {"id": "interactive_data_analysis_tool", "name": "Data Analysis Tool", "type": "interactive_data_analysis_tool", "subcategory": "Statistics", "description": "A widget that allows users to perform basic data analysis and visualization.", "data": {"title": "Interactive Data Analysis Tool", "description": "Enter data points to calculate statistics and visualize a histogram.", "initial_data": [10, 12, 15, 12, 18, 20, 22, 25, 20, 15], "primary_color": "#4CAF50", "secondary_color": "#FFEB3B", "background_color": "#FFFFFF", "text_color": "#212121", "showNameTag": true}, "isImplemented": true}, {"id": "interactive_decision_making_simulator", "name": "Decision Making Simulator", "type": "interactive_decision_making_simulator", "subcategory": "Probability", "description": "A widget that allows users to simulate and explore decision-making under uncertainty.", "data": {"title": "Decision Making Simulator", "description": "Adjust outcomes and probabilities for two options to compare their expected values.", "option1_outcome1_value": 100.0, "option1_outcome1_probability": 0.7, "option1_outcome2_value": 20.0, "option1_outcome2_probability": 0.3, "option2_outcome1_value": 150.0, "option2_outcome1_probability": 0.4, "option2_outcome2_value": 50.0, "option2_outcome2_probability": 0.6, "primary_color": "#3F51B5", "secondary_color": "#FF9800", "background_color": "#FFFFFF", "text_color": "#212121", "showNameTag": true}, "isImplemented": true}, {"id": "interactive_functions_and_probability_test", "name": "Functions and Probability Test", "type": "Module Test", "subcategory": "Tests", "description": "An interactive module test on Functions and Probability.", "data": {"title": "Functions and Probability Module Test", "primary_color": "#7B1FA2", "secondary_color": "#D81B60", "background_color": "#FFFFFF", "text_color": "#212121", "correct_color": "#4CAF50", "incorrect_color": "#F44336", "questions": [{"id": "q1", "question": "Which of the following is NOT a function?", "options": [{"id": "opt1", "text": "y = x + 2", "is_correct": false}, {"id": "opt2", "text": "x = y^2", "is_correct": true, "feedback_correct": "Correct! For x = y^2, a single x-value can correspond to multiple y-values (e.g., if x=4, y can be 2 or -2), violating the definition of a function."}, {"id": "opt3", "text": "y = |x|", "is_correct": false}, {"id": "opt4", "text": "y = 2^x", "is_correct": false}]}, {"id": "q2", "question": "If f(x) = 3x - 1, what is f(5)?", "options": [{"id": "opt1", "text": "12", "is_correct": false}, {"id": "opt2", "text": "14", "is_correct": true, "feedback_correct": "Correct! f(5) = 3(5) - 1 = 15 - 1 = 14."}, {"id": "opt3", "text": "15", "is_correct": false}, {"id": "opt4", "text": "16", "is_correct": false}]}], "showNameTag": true}, "isImplemented": true}, {"id": "math_gif_player_1", "name": "Pattern Recognition Animation", "type": "GIF Player", "subcategory": "Pattern Recognition", "description": "A GIF showing pattern recognition in sequences.", "data": {"gifUrl": "https://media.giphy.com/media/3o7TKSjRrfIPjeiVyM/giphy.gif", "caption": "Recognizing and extending number patterns", "autoPlay": true, "loopCount": -1}, "isImplemented": true}, {"id": "math_gif_player_2", "name": "Function Graphing Animation", "type": "GIF Player", "subcategory": "Functions", "description": "A GIF showing how functions are graphed.", "data": {"gifUrl": "https://media.giphy.com/media/3osxYc2axjCJNsCXyE/giphy.gif", "caption": "Visualizing how functions transform when parameters change", "autoPlay": true, "loopCount": 3}, "isImplemented": true}, {"id": "math_multiple_choice_1", "name": "Math Puzzle Challenge", "type": "Multiple Choice", "subcategory": "Puzzles", "description": "A challenging math puzzle with multiple choice answers.", "data": {"question": "If the pattern is 2, 6, 12, 20, 30, what comes next?", "options": ["42", "40", "36", "32"], "correctAnswer": "42", "explanation": "The pattern follows the sequence of differences: +4, +6, +8, +10, +12. So 30 + 12 = 42."}, "isImplemented": true}, {"id": "math_interactive_tool_1", "name": "Geometry Calculator", "type": "Interactive Tool", "subcategory": "Geometry", "description": "A tool for calculating areas, perimeters, and volumes of geometric shapes.", "data": {"shapes": ["Circle", "Square", "Rectangle", "Triangle", "Sphere", "C<PERSON>"], "defaultShape": "Circle", "calculations": {"Circle": ["Area", "Circumference"], "Square": ["Area", "Perimeter"], "Rectangle": ["Area", "Perimeter"], "Triangle": ["Area", "Perimeter"], "Sphere": ["Surface Area", "Volume"], "Cube": ["Surface Area", "Volume"]}}, "isImplemented": true}, {"id": "math_interactive_grapher_1", "name": "Function Grapher", "type": "Interactive Grapher", "subcategory": "Functions", "description": "A tool for graphing mathematical functions and exploring their properties.", "data": {"functions": ["y = x^2", "y = sin(x)", "y = log(x)"], "xRange": [-10, 10], "yRange": [-10, 10], "grid": true, "axes": true}, "isImplemented": true}, {"id": "math_mini_game_1", "name": "Pattern Recognition Game", "type": "Mini-Game", "subcategory": "Pattern Recognition", "description": "A game where users identify patterns in sequences of numbers or shapes.", "data": {"difficulty": "easy", "timeLimit": 90, "sequences": [{"sequence": [2, 4, 6, 8], "next": "10", "pattern": "Add 2"}, {"sequence": [1, 3, 9, 27], "next": "81", "pattern": "Multiply by 3"}, {"sequence": [1, 4, 9, 16], "next": "25", "pattern": "Square numbers"}]}, "isImplemented": true}, {"id": "math_interactive_whiteboard_1", "name": "Math Whiteboard", "type": "Interactive Whiteboard", "subcategory": "Tools", "description": "A digital whiteboard for solving mathematical problems with drawing and text tools.", "data": {"tools": ["Pen", "Eraser", "Text", "<PERSON><PERSON><PERSON>", "Grid"], "defaultTool": "Pen", "colors": ["Black", "Blue", "Red", "Green"], "defaultColor": "Black", "gridOptions": ["None", "1cm", "0.5cm"], "defaultGrid": "None"}, "isImplemented": true}]}, {"category": "science", "widgets": [{"id": "science_gif_player_1", "name": "Physics Motion Animation", "type": "GIF Player", "subcategory": "Physics", "description": "A GIF showing principles of motion and forces.", "data": {"gifUrl": "https://media.giphy.com/media/l0HlQXlQ3nHyLMvte/giphy.gif", "caption": "Visualizing <PERSON>'s laws of motion", "autoPlay": true, "loopCount": -1}, "isImplemented": true}, {"id": "science_interactive_simulation_1", "name": "Pendulum Simulation", "type": "Interactive Simulation", "subcategory": "Physics", "description": "An interactive simulation of a pendulum where users can adjust parameters and see the effects.", "data": {"parameters": [{"name": "Length", "min": 0.1, "max": 2.0, "default": 1.0, "unit": "m"}, {"name": "Gravity", "min": 1.0, "max": 20.0, "default": 9.8, "unit": "m/s²"}, {"name": "Initial Angle", "min": 0, "max": 90, "default": 45, "unit": "°"}]}, "isImplemented": false}, {"id": "science_interactive_converter_1", "name": "Unit Converter", "type": "Interactive Converter", "subcategory": "Measurement", "description": "A tool for converting between different units of measurement.", "data": {"categories": ["Length", "Weight", "Temperature"], "defaultCategory": "Length", "units": {"Length": ["Meters", "Feet", "Inches", "Kilometers", "<PERSON>"], "Weight": ["Kilograms", "Pounds", "<PERSON><PERSON><PERSON>", "Grams"], "Temperature": ["<PERSON><PERSON><PERSON>", "Fahrenheit", "<PERSON><PERSON>"]}}, "isImplemented": false}, {"id": "interactive_acceleration_visualizer_1", "name": "Interactive Acceleration Visualizer", "type": "Interactive Visualizer", "subcategory": "Physics", "description": "An interactive tool for visualizing acceleration.", "data": {}, "isImplemented": true}, {"id": "interactive_motion_graphs_tool_1", "name": "Interactive Motion Graphs Tool", "type": "Interactive Tool", "subcategory": "Physics", "description": "An interactive tool for creating and analyzing motion graphs (position, velocity, acceleration).", "data": {}, "isImplemented": true}, {"id": "interactive_projectile_motion_simulator_1", "name": "Interactive Projectile Motion Simulator", "type": "Interactive Simulator", "subcategory": "Physics", "description": "An interactive simulator for visualizing projectile motion with adjustable parameters.", "data": {}, "isImplemented": true}, {"id": "interactive_relative_motion_explorer_1", "name": "Interactive Relative Motion Explorer", "type": "Interactive Explorer", "subcategory": "Physics", "description": "An interactive tool for exploring relative motion between two objects with vector visualization.", "data": {}, "isImplemented": true}, {"id": "interactive_kinematics_challenge_1", "name": "Interactive Kinematics Challenge (Module Test)", "type": "Module Test", "subcategory": "Physics", "description": "A comprehensive test on kinematics concepts.", "data": {}, "isImplemented": true}, {"id": "interactive_momentum_calculator_1", "name": "Interactive Momentum Calculator", "type": "Interactive Calculator", "subcategory": "Physics", "description": "An interactive tool for calculating momentum based on mass and velocity.", "data": {}, "isImplemented": true}, {"id": "interactive_rotational_kinematics_calculator_1", "name": "Interactive Rotational Kinematics Calculator", "type": "Interactive Calculator", "subcategory": "Physics", "description": "An interactive tool for calculating rotational kinematics variables.", "data": {}, "isImplemented": true}, {"id": "interactive_torque_angular_acceleration_simulator_1", "name": "Interactive Torque and Angular Acceleration Simulator", "type": "Interactive Simulator", "subcategory": "Physics", "description": "A widget to simulate how torque affects angular acceleration.", "data": {}, "isImplemented": true}, {"id": "interactive_moment_of_inertia_calculator_1", "name": "Interactive Moment of Inertia Calculator", "type": "Interactive Calculator", "subcategory": "Physics", "description": "An interactive tool for calculating the moment of inertia for various shapes.", "data": {}, "isImplemented": true}, {"id": "interactive_angular_momentum_conservation_demonstrator_1", "name": "Interactive Angular Momentum Conservation Demonstrator", "type": "Interactive Demonstrator", "subcategory": "Physics", "description": "An interactive tool to demonstrate the conservation of angular momentum.", "data": {}, "isImplemented": true}, {"id": "interactive_rotational_kinetic_energy_calculator_1", "name": "Interactive Rotational Kinetic Energy Calculator", "type": "Interactive Calculator", "subcategory": "Physics", "description": "An interactive tool for calculating rotational kinetic energy.", "data": {}, "isImplemented": true}, {"id": "interactive_rotational_motion_challenge_1", "name": "Interactive Rotational Motion Challenge (Module Test)", "type": "Module Test", "subcategory": "Physics", "description": "A comprehensive test on rotational motion concepts.", "data": {}, "isImplemented": true}]}, {"category": "computer_science", "widgets": [{"id": "cs_gif_player_1", "name": "Sorting Algorithm Visualization", "type": "GIF Player", "subcategory": "Algorithms", "description": "A GIF showing how sorting algorithms work.", "data": {"gifUrl": "https://media.giphy.com/media/l3vRmVv5P01I5NDAA/giphy.gif", "caption": "Bubble sort algorithm in action", "autoPlay": true, "loopCount": 2}, "isImplemented": true}, {"id": "cs_sorting_algorithm_visualizer_1", "name": "Sorting Algorithm Visualizer", "type": "interactive_sorting_algorithm_visualizer", "subcategory": "Algorithms", "description": "An interactive visualization tool for different sorting algorithms.", "data": {"title": "Sorting Algorithm Visualizer", "description": "Visualize and understand how different sorting algorithms work by seeing them in action.", "primaryColor": "#FF5722", "textColor": "#212121", "backgroundColor": "#FFFFFF", "comparingColor": "#FFC107", "swappingColor": "#F44336", "sortedColor": "#4CAF50", "pivotColor": "#9C27B0", "defaultArraySize": 20, "minValue": 5, "maxValue": 100, "initialAlgorithm": "bubble", "initialSpeed": 0.5, "challengeMode": false}, "isImplemented": true}, {"id": "cs_interactive_diagram_1", "name": "Sorting Algorithm Visualizer Demo", "type": "Interactive Diagram", "subcategory": "Algorithms", "description": "An interactive visualization of sorting algorithms.", "data": {"imagePath": "assets/images/placeholder.png", "interactivePoints": [{"x": 0.2, "y": 0.2, "label": "Bubble Sort", "type": "toggle"}, {"x": 0.4, "y": 0.2, "label": "Quick Sort", "type": "toggle"}, {"x": 0.6, "y": 0.2, "label": "<PERSON><PERSON>", "type": "toggle"}, {"x": 0.8, "y": 0.2, "label": "Start", "type": "toggle"}, {"x": 0.5, "y": 0.7, "label": "Visualization", "type": "indicator"}]}, "isImplemented": true}, {"id": "cs_data_structure_visualizer_1", "name": "Data Structure Visualizer", "type": "interactive_data_structure_visualizer", "subcategory": "Data Structures", "description": "An interactive tool for visualizing and exploring common data structures like arrays, linked lists, stacks, queues, trees, and graphs.", "data": {"title": "Data Structure Visualizer", "description": "Explore and interact with common data structures to understand their operations and behaviors.", "primaryColor": "#2196F3", "textColor": "#212121"}, "isImplemented": true}, {"id": "science_physics_simulation_1", "name": "Physics Simulation Lab", "type": "interactive_physics_simulation", "subcategory": "Physics", "description": "An interactive physics laboratory for exploring fundamental physics concepts through simulations.", "data": {"title": "Physics Simulation Lab", "description": "Explore physics concepts through interactive simulations of projectile motion, pendulums, springs, and waves.", "primaryColor": "#4CAF50", "textColor": "#212121", "backgroundColor": "#FFFFFF", "defaultSimulation": "Projectile Motion", "parameters": [{"name": "gravity", "default": 9.8}, {"name": "initialVelocity", "default": 20.0}, {"name": "angle", "default": 45.0}, {"name": "length", "default": 1.0}, {"name": "mass", "default": 1.0}, {"name": "springConstant", "default": 10.0}, {"name": "damping", "default": 0.1}, {"name": "frequency", "default": 1.0}, {"name": "amplitude", "default": 1.0}]}, "isImplemented": true}, {"id": "cs_data_structure_traversal_visualizer_1", "name": "Data Structure Traversal Visualizer", "type": "interactive_data_structure_traversal_visualizer", "subcategory": "Algorithms", "description": "An interactive tool for visualizing traversal algorithms on different data structures.", "data": {"title": "Data Structure Traversal Visualizer", "description": "Visualize and understand how different traversal algorithms work on data structures like trees, graphs, and linked lists.", "primaryColor": "#9C27B0", "textColor": "#212121", "visitedNodeColor": "#4CAF50", "currentNodeColor": "#FF9800", "edgeColor": "#757575", "backgroundColor": "#FFFFFF", "defaultDataStructure": "Binary Tree", "defaultSpeed": 0.5}, "isImplemented": true}, {"id": "science_molecular_viewer_1", "name": "Molecular Viewer", "type": "interactive_molecular_viewer", "subcategory": "Chemistry", "description": "An interactive 3D molecular viewer for exploring molecular structures and properties.", "data": {"title": "Molecular Viewer", "description": "Explore 3D molecular structures and their properties. Rotate, zoom, and examine common molecules.", "primaryColor": "#E91E63", "textColor": "#212121", "backgroundColor": "#FFFFFF"}, "isImplemented": true}, {"id": "cs_logic_gate_simulator_1", "name": "Logic Gate Simulator", "type": "interactive_logic_gate_simulator", "subcategory": "Digital Logic", "description": "An interactive simulator for exploring logic gates and their behavior.", "data": {"title": "Logic Gate Simulator", "description": "Explore logic gates, their behavior, and truth tables. Experiment with different inputs and see how they affect the output.", "primaryColor": "#3F51B5", "textColor": "#212121", "backgroundColor": "#FFFFFF"}, "isImplemented": true}, {"id": "math_statistical_distribution_explorer_1", "name": "Statistical Distribution Explorer", "type": "interactive_statistical_distribution_explorer", "subcategory": "Statistics", "description": "An interactive explorer for visualizing and understanding statistical distributions.", "data": {"title": "Statistical Distribution Explorer", "description": "Explore probability distributions, their properties, and visualize how changing parameters affects their shape.", "primaryColor": "#009688", "textColor": "#212121", "backgroundColor": "#FFFFFF"}, "isImplemented": true}, {"id": "tech_circuit_simulator_1", "name": "Circuit Simulator", "type": "interactive_circuit_simulator", "subcategory": "Electronics", "description": "An interactive simulator for exploring electrical circuits and their behavior.", "data": {"title": "Circuit Simulator", "description": "Explore electrical circuits, their components, and analyze their behavior using Ohm's Law and circuit analysis techniques.", "primaryColor": "#FF5722", "textColor": "#212121", "backgroundColor": "#FFFFFF"}, "isImplemented": true}, {"id": "science_periodic_table_1", "name": "Periodic Table of Elements", "type": "interactive_periodic_table", "subcategory": "Chemistry", "description": "An interactive periodic table for exploring elements and their properties.", "data": {"title": "Periodic Table of Elements", "description": "Explore the periodic table of elements, their properties, and learn about the building blocks of matter.", "primaryColor": "#9C27B0", "textColor": "#212121", "backgroundColor": "#FFFFFF"}, "isImplemented": true}, {"id": "interactive_array_explorer_1", "name": "Interactive Array Explorer", "type": "interactive_array_explorer", "subcategory": "Data Structures", "description": "An interactive tool for exploring array operations like adding, removing, and accessing elements.", "data": {"title": "Array Explorer", "description": "Visualize array elements and perform basic operations.", "initial_array": [10, 20, 30, 40, 50], "primary_color": "#FFC107", "secondary_color": "#FF5722", "background_color": "#FFFFFF", "text_color": "#212121", "showNameTag": true}, "isImplemented": true}, {"id": "interactive_linked_list_visualizer_1", "name": "Interactive Linked List Visualizer", "type": "interactive_linked_list_visualizer", "subcategory": "Data Structures", "description": "An interactive tool for visualizing linked list operations like adding, removing, and traversing nodes.", "data": {"title": "Linked List Visualizer", "description": "Visualize linked list nodes and perform basic operations.", "initial_nodes": [10, 20, 30, 40, 50], "primary_color": "#4CAF50", "secondary_color": "#8BC34A", "background_color": "#FFFFFF", "text_color": "#212121", "showNameTag": true}, "isImplemented": true}, {"id": "interactive_stack_queue_simulator_1", "name": "Interactive Stack and Queue Simulator", "type": "interactive_stack_queue_simulator", "subcategory": "Data Structures", "description": "An interactive tool for simulating stack (LIFO) and queue (FIFO) operations.", "data": {"title": "Stack and Queue Simulator", "description": "Perform push/pop on stack and enqueue/dequeue on queue.", "primary_color": "#2196F3", "secondary_color": "#FF9800", "background_color": "#FFFFFF", "text_color": "#212121", "showNameTag": true}, "isImplemented": true}, {"id": "interactive_hash_table_demonstrator_1", "name": "Interactive Hash Table Demonstrator", "type": "interactive_hash_table_demonstrator", "subcategory": "Data Structures", "description": "An interactive tool for demonstrating hash table operations like insertion, retrieval, and deletion.", "data": {"title": "Hash Table Demonstrator", "description": "Add, retrieve, and remove key-value pairs from a hash table.", "primary_color": "#FF5722", "secondary_color": "#00BCD4", "background_color": "#FFFFFF", "text_color": "#212121", "showNameTag": true}, "isImplemented": true}, {"id": "interactive_data_structure_comparison_tool_1", "name": "Interactive Data Structure Comparison Tool", "type": "interactive_data_structure_comparison_tool", "subcategory": "Data Structures", "description": "A tool for comparing the characteristics and performance of different data structures.", "data": {"title": "Data Structure Comparison", "description": "Compare properties like access time, insertion/deletion time, and memory usage.", "primary_color": "#9C27B0", "secondary_color": "#FFEB3B", "background_color": "#FFFFFF", "text_color": "#212121", "showNameTag": true}, "isImplemented": true}, {"id": "interactive_data_structures_challenge_test_1", "name": "Interactive Data Structures Challenge (Module Test)", "type": "interactive_data_structures_challenge_test", "subcategory": "Tests", "description": "A comprehensive test on fundamental data structures.", "data": {"title": "Data Structures Challenge", "description": "Test your knowledge of arrays, linked lists, stacks, queues, and hash tables.", "primary_color": "#3F51B5", "secondary_color": "#FFC107", "background_color": "#FFFFFF", "text_color": "#212121", "showNameTag": true}, "isImplemented": true}, {"id": "interactive_bubble_sort_visualizer_1", "name": "Interactive Bubble Sort Visualizer", "type": "interactive_bubble_sort_visualizer", "subcategory": "Sorting Algorithms", "description": "An interactive tool for visualizing the bubble sort algorithm step-by-step.", "data": {"title": "Bubble Sort Visualizer", "description": "See how bubble sort works by watching elements swap positions.", "primary_color": "#FFC107", "secondary_color": "#FF5722", "background_color": "#FFFFFF", "text_color": "#212121", "showNameTag": true}, "isImplemented": true}, {"id": "interactive_insertion_sort_simulator_1", "name": "Interactive Insertion Sort Simulator", "type": "interactive_insertion_sort_simulator", "subcategory": "Sorting Algorithms", "description": "An interactive tool for simulating the insertion sort algorithm.", "data": {"title": "Insertion Sort Simulator", "description": "Observe how insertion sort builds the final sorted array one item at a time.", "primary_color": "#4CAF50", "secondary_color": "#8BC34A", "background_color": "#FFFFFF", "text_color": "#212121", "showNameTag": true}, "isImplemented": true}, {"id": "interactive_merge_sort_demonstrator_1", "name": "Interactive Merge Sort Demonstrator", "type": "interactive_merge_sort_demonstrator", "subcategory": "Sorting Algorithms", "description": "An interactive tool for demonstrating the merge sort algorithm.", "data": {"title": "<PERSON><PERSON> Sort Demonstrator", "description": "Visualize the divide and conquer strategy of merge sort.", "primary_color": "#2196F3", "secondary_color": "#FF9800", "background_color": "#FFFFFF", "text_color": "#212121", "showNameTag": true}, "isImplemented": true}, {"id": "interactive_quick_sort_explorer_1", "name": "Interactive Quick Sort Explorer", "type": "interactive_quick_sort_explorer", "subcategory": "Sorting Algorithms", "description": "An interactive tool for exploring the quick sort algorithm.", "data": {"title": "Quick Sort Explorer", "description": "Understand quick sort by seeing how pivots are chosen and partitions are made.", "primary_color": "#FF5722", "secondary_color": "#00BCD4", "background_color": "#FFFFFF", "text_color": "#212121", "showNameTag": true}, "isImplemented": true}, {"id": "interactive_sorting_algorithm_comparison_tool_1", "name": "Interactive Sorting Algorithm Comparison Tool", "type": "interactive_sorting_algorithm_comparison_tool", "subcategory": "Sorting Algorithms", "description": "A tool for comparing the characteristics and performance of different sorting algorithms.", "data": {"title": "Sorting Algorithm Comparison", "description": "Compare time complexity, space complexity, and stability of various sorting algorithms.", "primary_color": "#9C27B0", "secondary_color": "#FFEB3B", "background_color": "#FFFFFF", "text_color": "#212121", "showNameTag": true}, "isImplemented": true}, {"id": "interactive_sorting_challenge_test_1", "name": "Interactive Sorting Challenge (Module Test)", "type": "interactive_sorting_challenge_test", "subcategory": "Tests", "description": "A comprehensive test on sorting algorithms.", "data": {"title": "Sorting Algorithms Challenge", "description": "Test your knowledge of bubble sort, insertion sort, merge sort, and quick sort.", "primary_color": "#3F51B5", "secondary_color": "#FFC107", "background_color": "#FFFFFF", "text_color": "#212121", "showNameTag": true}, "isImplemented": true}, {"id": "interactive_linear_search_visualizer_1", "name": "Interactive Linear Search Visualizer", "type": "interactive_linear_search_visualizer", "subcategory": "Searching Algorithms", "description": "An interactive tool for visualizing the linear search algorithm.", "data": {"title": "Linear Search Visualizer", "description": "See how linear search checks each element sequentially.", "primary_color": "#FFC107", "secondary_color": "#FF5722", "background_color": "#FFFFFF", "text_color": "#212121", "showNameTag": true}, "isImplemented": true}, {"id": "interactive_binary_search_simulator_1", "name": "Interactive Binary Search Simulator", "type": "interactive_binary_search_simulator", "subcategory": "Searching Algorithms", "description": "An interactive tool for simulating the binary search algorithm on a sorted array.", "data": {"title": "Binary Search Simulator", "description": "Observe how binary search efficiently narrows down the search space.", "primary_color": "#4CAF50", "secondary_color": "#8BC34A", "background_color": "#FFFFFF", "text_color": "#212121", "showNameTag": true}, "isImplemented": true}, {"id": "interactive_graph_representation_tool_1", "name": "Interactive Graph Representation Tool", "type": "interactive_graph_representation_tool", "subcategory": "Graph Basics", "description": "A tool for building and visualizing graphs using adjacency lists and matrices.", "data": {"title": "Graph Representation Tool", "description": "Create nodes and edges to build a graph and see its representations.", "primary_color": "#2196F3", "secondary_color": "#FF9800", "background_color": "#FFFFFF", "text_color": "#212121", "showNameTag": true}, "isImplemented": true}, {"id": "interactive_graph_traversal_demonstrator_1", "name": "Interactive Graph Traversal Demonstrator", "type": "interactive_graph_traversal_demonstrator", "subcategory": "Graph Basics", "description": "An interactive tool for demonstrating Breadth-First Search (BFS) and Depth-First Search (DFS).", "data": {"title": "Graph Traversal Demonstrator", "description": "Visualize BFS and DFS on a sample graph.", "primary_color": "#FF5722", "secondary_color": "#00BCD4", "background_color": "#FFFFFF", "text_color": "#212121", "showNameTag": true}, "isImplemented": true}, {"id": "interactive_pathfinding_algorithm_explorer_1", "name": "Interactive Pathfinding Algorithm Explorer", "type": "interactive_pathfinding_algorithm_explorer", "subcategory": "Graph Basics", "description": "An interactive tool for exploring pathfinding algorithms like Dijkstra's.", "data": {"title": "Pathfinding Algorithm Explorer", "description": "Find the shortest path between two nodes in a weighted graph.", "primary_color": "#9C27B0", "secondary_color": "#FFEB3B", "background_color": "#FFFFFF", "text_color": "#212121", "showNameTag": true}, "isImplemented": true}, {"id": "interactive_searching_and_graphs_challenge_test_1", "name": "Interactive Searching and Graphs Challenge (Module Test)", "type": "interactive_searching_and_graphs_challenge_test", "subcategory": "Tests", "description": "A comprehensive test on searching algorithms and graph basics.", "data": {"title": "Searching and Graphs Challenge", "description": "Test your knowledge of linear search, binary search, graph representations, and traversals.", "primary_color": "#3F51B5", "secondary_color": "#FFC107", "background_color": "#FFFFFF", "text_color": "#212121", "showNameTag": true}, "isImplemented": true}, {"id": "interactive_binary_tree_visualizer_1", "name": "Interactive Binary Tree Visualizer", "type": "interactive_binary_tree_visualizer", "subcategory": "Trees and Heaps", "description": "An interactive tool for visualizing binary trees and their structure.", "data": {"title": "Binary Tree Visualizer", "description": "Visualize how nodes are arranged in a binary tree.", "primary_color": "#FFC107", "secondary_color": "#FF5722", "background_color": "#FFFFFF", "text_color": "#212121", "showNameTag": true}, "isImplemented": true}, {"id": "interactive_binary_search_tree_operations_1", "name": "Interactive Binary Search Tree Operations", "type": "interactive_binary_search_tree_operations", "subcategory": "Trees and Heaps", "description": "An interactive tool for performing and visualizing insert, search, and delete operations on a Binary Search Tree (BST).", "data": {"title": "Binary Search Tree Operations", "description": "Perform and visualize operations on a BST.", "primary_color": "#4CAF50", "secondary_color": "#8BC34A", "background_color": "#FFFFFF", "text_color": "#212121", "showNameTag": true}, "isImplemented": true}, {"id": "interactive_heap_operations_simulator_1", "name": "Interactive Heap Operations Simulator", "type": "interactive_heap_operations_simulator", "subcategory": "Trees and Heaps", "description": "An interactive tool for simulating insert and extract operations on a Min-Heap or Max-Heap.", "data": {"title": "Heap Operations Simulator", "description": "Simulate insert and extract operations on a heap.", "primary_color": "#2196F3", "secondary_color": "#FF9800", "background_color": "#FFFFFF", "text_color": "#212121", "showNameTag": true}, "isImplemented": true}, {"id": "interactive_tree_traversal_demonstrator_1", "name": "Interactive Tree Traversal Demonstrator", "type": "interactive_tree_traversal_demonstrator", "subcategory": "Trees and Heaps", "description": "An interactive tool for demonstrating inorder, preorder, and postorder tree traversals.", "data": {"title": "Tree Traversal Demonstrator", "description": "Visualize different tree traversal algorithms.", "primary_color": "#FF5722", "secondary_color": "#00BCD4", "background_color": "#FFFFFF", "text_color": "#212121", "showNameTag": true}, "isImplemented": true}, {"id": "interactive_balanced_tree_explorer_1", "name": "Interactive Balanced Tree Explorer", "type": "interactive_balanced_tree_explorer", "subcategory": "Trees and Heaps", "description": "An interactive tool for exploring balanced binary search trees (e.g., AVL trees) and their self-balancing properties.", "data": {"title": "Balanced Tree Explorer", "description": "Insert and delete nodes to see how a balanced tree maintains its balance.", "primary_color": "#9C27B0", "secondary_color": "#FFEB3B", "background_color": "#FFFFFF", "text_color": "#212121", "showNameTag": true}, "isImplemented": true}, {"id": "interactive_trees_and_heaps_challenge_test_1", "name": "Interactive Trees and Heaps Challenge (Module Test)", "type": "interactive_trees_and_heaps_challenge_test", "subcategory": "Tests", "description": "A comprehensive test on trees and heaps.", "data": {"title": "Trees and Heaps Challenge", "description": "Test your knowledge of binary trees, BSTs, heaps, and tree traversals.", "primary_color": "#3F51B5", "secondary_color": "#FFC107", "background_color": "#FFFFFF", "text_color": "#212121", "showNameTag": true}, "isImplemented": true}]}, {"category": "reasoning", "widgets": [{"id": "data_interactive_calculator_1", "name": "Statistical Calculator", "type": "Interactive Calculator", "subcategory": "Statistics", "description": "A calculator for statistical operations like mean, median, and standard deviation.", "data": {"functions": ["Mean", "Median", "Mode", "Standard Deviation", "<PERSON><PERSON><PERSON>"], "defaultFunction": "Mean", "history": true, "memoryStorage": true}, "isImplemented": false}, {"id": "data_multiple_choice_1", "name": "Data Analysis Quiz", "type": "Multiple Choice", "subcategory": "Statistics", "description": "A quiz testing knowledge of data analysis concepts.", "data": {"question": "Which measure of central tendency is most affected by outliers?", "options": ["Mean", "Median", "Mode", "Range"], "correctAnswer": "Mean", "explanation": "The mean is calculated by summing all values and dividing by the count, so extreme values (outliers) can significantly affect the result. Median and mode are more resistant to outliers."}, "isImplemented": true}, {"id": "interactive_premise_conclusion_sorter_1", "name": "Interactive Premise-Conclusion Sorter", "type": "Interactive Sorter", "subcategory": "Foundations", "description": "An interactive tool for distinguishing premises from conclusions in arguments.", "data": {}, "isImplemented": true}, {"id": "interactive_argument_structure_analyzer_1", "name": "Interactive Argument Structure Analyzer", "type": "Interactive Analyzer", "subcategory": "Foundations", "description": "Analyze and visualize the structural components of various arguments.", "data": {}, "isImplemented": true}, {"id": "interactive_argument_strength_evaluator_1", "name": "Interactive Argument Strength Evaluator", "type": "Interactive Evaluator", "subcategory": "Foundations", "description": "Evaluate the strength and weakness of different types of arguments.", "data": {}, "isImplemented": true}, {"id": "interactive_argument_builder_1", "name": "Interactive Argument Builder", "type": "Interactive Builder", "subcategory": "Foundations", "description": "Construct arguments from given premises and conclusions.", "data": {}, "isImplemented": true}, {"id": "interactive_argument_type_classifier_1", "name": "Interactive Argument Type Classifier", "type": "Interactive Classifier", "subcategory": "Foundations", "description": "Classify arguments as deductive, inductive, or abductive.", "data": {}, "isImplemented": true}, {"id": "interactive_argument_analysis_challenge_1", "name": "Interactive Argument Analysis Challenge (Module Test)", "type": "Module Test", "subcategory": "Foundations", "description": "A comprehensive test on argument analysis and foundational reasoning concepts.", "data": {}, "isImplemented": true}, {"id": "interactive_syllogism_builder_1", "name": "Interactive Syllogism Builder", "type": "Interactive Builder", "subcategory": "Deductive Reasoning", "description": "Build and validate syllogisms.", "data": {}, "isImplemented": true}, {"id": "interactive_truth_table_explorer_1", "name": "Interactive Truth Table Explorer", "type": "Interactive Explorer", "subcategory": "Deductive Reasoning", "description": "Explore truth tables for logical propositions.", "data": {}, "isImplemented": true}, {"id": "interactive_logical_form_identifier_1", "name": "Interactive Logical Form Identifier", "type": "Interactive Identifier", "subcategory": "Deductive Reasoning", "description": "Identify the logical form of arguments.", "data": {}, "isImplemented": true}, {"id": "interactive_deductive_argument_validator_1", "name": "Interactive Deductive Argument Validator", "type": "Interactive Validator", "subcategory": "Deductive Reasoning", "description": "Validate deductive arguments for soundness and validity.", "data": {}, "isImplemented": true}, {"id": "interactive_logical_equivalence_demonstrator_1", "name": "Interactive Logical Equivalence Demonstrator", "type": "Interactive Demonstrator", "subcategory": "Deductive Reasoning", "description": "Demonstrate logical equivalences between propositions.", "data": {}, "isImplemented": true}, {"id": "interactive_inductive_argument_evaluator_1", "name": "Interactive Inductive Argument Evaluator", "type": "Interactive Evaluator", "subcategory": "Inductive Reasoning", "description": "Evaluate the strength of inductive arguments.", "data": {}, "isImplemented": true}, {"id": "interactive_causal_inference_builder_1", "name": "Interactive Causal Inference Builder", "type": "Interactive Builder", "subcategory": "Inductive Reasoning", "description": "Build and analyze causal inferences.", "data": {}, "isImplemented": true}, {"id": "interactive_statistical_reasoning_tool_1", "name": "Interactive Statistical Reasoning Tool", "type": "Interactive Tool", "subcategory": "Inductive Reasoning", "description": "Perform basic statistical analysis for inductive reasoning.", "data": {}, "isImplemented": true}, {"id": "interactive_analogical_reasoning_analyzer_1", "name": "Interactive Analogical Reasoning Analyzer", "type": "Interactive Analyzer", "subcategory": "Inductive Reasoning", "description": "Analyze and understand analogical arguments.", "data": {}, "isImplemented": true}, {"id": "interactive_inductive_strength_calculator_1", "name": "Interactive Inductive Strength Calculator", "type": "Interactive Calculator", "subcategory": "Inductive Reasoning", "description": "Calculate the strength of inductive arguments.", "data": {}, "isImplemented": true}, {"id": "interactive_inductive_reasoning_challenge_1", "name": "Interactive Inductive Reasoning Challenge (Module Test)", "type": "Module Test", "subcategory": "Inductive Reasoning", "description": "A comprehensive test on inductive reasoning concepts.", "data": {}, "isImplemented": true}, {"id": "interactive_fallacy_identifier_1", "name": "Interactive Fallacy Identifier", "type": "Interactive Identifier", "subcategory": "Informal Fallacies", "description": "Identify common informal fallacies in arguments.", "data": {}, "isImplemented": true}, {"id": "interactive_fallacy_categorizer_1", "name": "Interactive Fallacy Categorizer", "type": "Interactive Categorizer", "subcategory": "Informal Fallacies", "description": "Categorize arguments by the type of fallacy they contain.", "data": {}, "isImplemented": true}, {"id": "interactive_fallacious_argument_detector_1", "name": "Interactive Fallacious Argument Detector", "type": "Interactive Detector", "subcategory": "Informal Fallacies", "description": "Detect fallacious arguments and understand why they are flawed.", "data": {}, "isImplemented": true}, {"id": "interactive_fallacy_correction_tool_1", "name": "Interactive Fallacy Correction Tool", "type": "Interactive Tool", "subcategory": "Informal Fallacies", "description": "Practice correcting fallacious arguments to make them logically sound.", "data": {}, "isImplemented": true}, {"id": "interactive_fallacy_in_media_analyzer_1", "name": "Interactive Fallacy in Media Analyzer", "type": "Interactive Analyzer", "subcategory": "Informal Fallacies", "description": "Analyze media content for the presence of informal fallacies.", "data": {}, "isImplemented": true}, {"id": "interactive_fallacy_avoidance_challenge_1", "name": "Interactive Fallacy Avoidance Challenge (Module Test)", "type": "Module Test", "subcategory": "Informal Fallacies", "description": "A comprehensive test on identifying and avoiding informal fallacies.", "data": {}, "isImplemented": true}, {"id": "interactive_media_claim_analyzer_1", "name": "Interactive Media Claim Analyzer", "type": "Interactive Analyzer", "subcategory": "Everyday Life", "description": "Analyze claims made in various media forms.", "data": {}, "isImplemented": true}, {"id": "interactive_scientific_reasoning_evaluator_1", "name": "Interactive Scientific Reasoning Evaluator", "type": "Interactive Evaluator", "subcategory": "Everyday Life", "description": "Evaluate scientific arguments and methodologies.", "data": {}, "isImplemented": true}, {"id": "interactive_political_argument_dissector_1", "name": "Interactive Political Argument Dissector", "type": "Interactive Dissector", "subcategory": "Everyday Life", "description": "Dissect and analyze political arguments for logical soundness.", "data": {}, "isImplemented": true}, {"id": "interactive_advertisement_claim_analyzer_1", "name": "Interactive Advertisement Claim Analyzer", "type": "Interactive Analyzer", "subcategory": "Everyday Life", "description": "Analyze claims made in advertisements for persuasive techniques and fallacies.", "data": {}, "isImplemented": true}, {"id": "interactive_decision_making_framework_1", "name": "Interactive Decision-Making Framework", "type": "Interactive Framework", "subcategory": "Everyday Life", "description": "Apply a structured framework to make rational decisions.", "data": {}, "isImplemented": true}, {"id": "interactive_real_world_reasoning_challenge_1", "name": "Interactive Real-World Reasoning Challenge (Module Test)", "type": "Module Test", "subcategory": "Everyday Life", "description": "A comprehensive test on applying reasoning skills to real-world scenarios.", "data": {}, "isImplemented": true}, {"id": "interactive_deductive_reasoning_challenge_1", "name": "Interactive Deductive Reasoning Challenge (Module Test)", "type": "Module Test", "subcategory": "Deductive Reasoning", "description": "A comprehensive test on deductive reasoning concepts.", "data": {}, "isImplemented": true}, {"id": "interactive_propositional_logic_calculator_1", "name": "Interactive Propositional Logic Calculator", "type": "Interactive Calculator", "subcategory": "Formal Deduction", "description": "Calculate truth values and logical equivalences for propositional statements.", "data": {}, "isImplemented": true}, {"id": "interactive_proof_builder_1", "name": "Interactive Proof Builder", "type": "Interactive Builder", "subcategory": "Formal Deduction", "description": "Construct formal proofs using rules of inference.", "data": {}, "isImplemented": true}, {"id": "interactive_natural_deduction_system_1", "name": "Interactive Natural Deduction System", "type": "Interactive System", "subcategory": "Formal Deduction", "description": "Practice deriving conclusions in a natural deduction system.", "data": {}, "isImplemented": true}, {"id": "interactive_logical_connectives_explorer_1", "name": "Interactive Logical Connectives Explorer", "type": "Interactive Explorer", "subcategory": "Formal Deduction", "description": "Explore the properties and truth conditions of logical connectives.", "data": {}, "isImplemented": true}, {"id": "interactive_tautology_checker_1", "name": "Interactive Tautology Checker", "type": "Interactive Checker", "subcategory": "Formal Deduction", "description": "Check if a propositional statement is a tautology, contradiction, or contingency.", "data": {}, "isImplemented": true}, {"id": "interactive_propositional_logic_challenge_1", "name": "Interactive Propositional Logic Challenge (Module Test)", "type": "Module Test", "subcategory": "Formal Deduction", "description": "A comprehensive test on propositional logic and formal deduction.", "data": {}, "isImplemented": true}, {"id": "interactive_quantifier_explorer_1", "name": "Interactive Quantifier Explorer", "type": "Interactive Explorer", "subcategory": "Predicate Logic", "description": "Explore the meaning and usage of universal and existential quantifiers.", "data": {}, "isImplemented": true}, {"id": "interactive_predicate_logic_translator_1", "name": "Interactive Predicate Logic Translator", "type": "Interactive Translator", "subcategory": "Predicate Logic", "description": "Translate natural language statements into predicate logic expressions.", "data": {}, "isImplemented": true}, {"id": "interactive_predicate_proof_builder_1", "name": "Interactive Predicate Proof Builder", "type": "Interactive Builder", "subcategory": "Predicate Logic", "description": "Construct formal proofs in predicate logic using various inference rules.", "data": {}, "isImplemented": true}, {"id": "interactive_domain_and_interpretation_tool_1", "name": "Interactive Domain and Interpretation Tool", "type": "Interactive Tool", "subcategory": "Predicate Logic", "description": "Define domains and interpretations for predicate logic statements.", "data": {}, "isImplemented": true}, {"id": "interactive_quantifier_negation_demonstrator_1", "name": "Interactive Quantifier Negation Demonstrator", "type": "Interactive Demonstrator", "subcategory": "Predicate Logic", "description": "Demonstrate how to negate quantified statements in predicate logic.", "data": {}, "isImplemented": true}, {"id": "interactive_predicate_logic_challenge_1", "name": "Interactive Predicate Logic Challenge (Module Test)", "type": "Module Test", "subcategory": "Predicate Logic", "description": "Test your understanding of predicate logic with a series of challenges.", "data": {}, "isImplemented": true}, {"id": "interactive_modal_operator_explorer_1", "name": "Interactive Modal Operator Explorer", "type": "Interactive Explorer", "subcategory": "Modal Logic", "description": "Explore the meaning and usage of modal operators (necessity and possibility).", "data": {}, "isImplemented": true}, {"id": "interactive_possible_worlds_visualizer_1", "name": "Interactive Possible Worlds Visualizer", "type": "Interactive Visualizer", "subcategory": "Modal Logic", "description": "Visualize possible worlds and accessibility relations in modal logic.", "data": {}, "isImplemented": true}, {"id": "interactive_modal_argument_analyzer_1", "name": "Interactive Modal Argument Analyzer", "type": "Interactive Analyzer", "subcategory": "Modal Logic", "description": "Analyze the validity of arguments involving modal concepts.", "data": {}, "isImplemented": true}, {"id": "interactive_accessibility_relation_builder_1", "name": "Interactive Accessibility Relation Builder", "type": "Interactive Builder", "subcategory": "Modal Logic", "description": "Build and explore different accessibility relations between possible worlds.", "data": {}, "isImplemented": true}, {"id": "interactive_modal_system_comparator_1", "name": "Interactive Modal System Comparator", "type": "Interactive Comparator", "subcategory": "Modal Logic", "description": "Compare different modal logic systems (e.g., K, T, S4, S5) and their properties.", "data": {}, "isImplemented": true}, {"id": "interactive_modal_logic_challenge_1", "name": "Interactive Modal Logic Challenge (Module Test)", "type": "Module Test", "subcategory": "Modal Logic", "description": "Test your understanding of modal logic with a series of challenges.", "data": {}, "isImplemented": true}, {"id": "interactive_argument_scheme_identifier_1", "name": "Interactive Argument Scheme Identifier", "type": "Interactive Identifier", "subcategory": "Informal Logic and Argumentation Theory", "description": "An interactive tool to identify and analyze different argument schemes.", "data": {}, "isImplemented": true}, {"id": "interactive_dialectical_structure_builder_1", "name": "Interactive Dialectical Structure Builder", "type": "Interactive Builder", "subcategory": "Informal Logic and Argumentation Theory", "description": "A tool to build and visualize the dialectical structure of arguments.", "data": {}, "isImplemented": true}, {"id": "interactive_burden_of_proof_analyzer_1", "name": "Interactive Burden of Proof Analyzer", "type": "Interactive Analyzer", "subcategory": "Informal Logic and Argumentation Theory", "description": "An interactive tool to analyze and understand the concept of burden of proof in arguments.", "data": {}, "isImplemented": true}, {"id": "interactive_presumption_and_exception_explorer_1", "name": "Interactive Presumption and Exception Explorer", "type": "Interactive Explorer", "subcategory": "Informal Logic and Argumentation Theory", "description": "An interactive tool to explore the concepts of presumption and exception in argumentation.", "data": {}, "isImplemented": true}, {"id": "interactive_argumentation_framework_visualizer_1", "name": "Interactive Argumentation Framework Visualizer", "type": "Interactive Visualizer", "subcategory": "Informal Logic and Argumentation Theory", "description": "A tool to visualize and analyze abstract argumentation frameworks.", "data": {}, "isImplemented": true}, {"id": "interactive_argumentation_theory_challenge_1", "name": "Interactive Argumentation Theory Challenge (Module Test)", "type": "Module Test", "subcategory": "Informal Logic and Argumentation Theory", "description": "A challenge to test your understanding of informal logic and argumentation theory.", "data": {}, "isImplemented": true}, {"id": "interactive_modal_operator_explorer_1", "name": "Interactive Modal Operator Explorer", "type": "Interactive Explorer", "subcategory": "Modal Logic", "description": "Explore the meaning and usage of modal operators (necessity and possibility).", "data": {}, "isImplemented": true}, {"id": "interactive_possible_worlds_visualizer_1", "name": "Interactive Possible Worlds Visualizer", "type": "Interactive Visualizer", "subcategory": "Modal Logic", "description": "Visualize possible worlds and accessibility relations in modal logic.", "data": {}, "isImplemented": true}, {"id": "interactive_modal_argument_analyzer_1", "name": "Interactive Modal Argument Analyzer", "type": "Interactive Analyzer", "subcategory": "Modal Logic", "description": "Analyze the validity of arguments involving modal concepts.", "data": {}, "isImplemented": true}, {"id": "interactive_accessibility_relation_builder_1", "name": "Interactive Accessibility Relation Builder", "type": "Interactive Builder", "subcategory": "Modal Logic", "description": "Build and explore different accessibility relations between possible worlds.", "data": {}, "isImplemented": true}, {"id": "interactive_modal_system_comparator_1", "name": "Interactive Modal System Comparator", "type": "Interactive Comparator", "subcategory": "Modal Logic", "description": "Compare different modal logic systems (e.g., K, T, S4, S5) and their properties.", "data": {}, "isImplemented": true}, {"id": "interactive_modal_logic_challenge_1", "name": "Interactive Modal Logic Challenge (Module Test)", "type": "Module Test", "subcategory": "Modal Logic", "description": "Test your understanding of modal logic with a series of challenges.", "data": {}, "isImplemented": true}, {"id": "interactive_non_classical_logic_explorer_1", "name": "Interactive Non-Classical Logic Explorer", "type": "Interactive Explorer", "subcategory": "Advanced Topics in Reasoning", "description": "Explore various non-classical logic systems (e.g., fuzzy logic, intuitionistic logic).", "data": {}, "isImplemented": true}, {"id": "interactive_bayesian_reasoning_calculator_1", "name": "Interactive Bayesian Reasoning Calculator", "type": "Interactive Calculator", "subcategory": "Advanced Topics in Reasoning", "description": "Apply Bayesian reasoning to various scenarios.", "data": {}, "isImplemented": true}, {"id": "interactive_defeasible_reasoning_simulator_1", "name": "Interactive Defeasible Reasoning Simulator", "type": "Interactive Simulator", "subcategory": "Advanced Topics in Reasoning", "description": "Explore defeasible reasoning, where conclusions can be withdrawn in light of new information.", "data": {}, "isImplemented": true}, {"id": "interactive_computational_argumentation_tool_1", "name": "Interactive Computational Argumentation Tool", "type": "Interactive Tool", "subcategory": "Advanced Topics in Reasoning", "description": "Explore computational models of argumentation and argument mining.", "data": {}, "isImplemented": true}, {"id": "interactive_collective_intelligence_demonstrator_1", "name": "Interactive Collective Intelligence Demonstrator", "type": "Interactive Demonstrator", "subcategory": "Advanced Topics in Reasoning", "description": "Explore how collective intelligence emerges from individual reasoning.", "data": {}, "isImplemented": true}, {"id": "interactive_advanced_reasoning_challenge_1", "name": "Interactive Advanced Reasoning Challenge (Module Test)", "type": "Module Test", "subcategory": "Advanced Topics in Reasoning", "description": "Test your understanding of advanced topics in reasoning.", "data": {}, "isImplemented": true}]}, {"category": "technology", "widgets": [{"id": "interactive_resistor_calculator_1", "name": "Interactive Resistor Calculator", "type": "Interactive Calculator", "subcategory": "Core Electrical Components", "description": "Calculate resistance using <PERSON><PERSON>'s Law (V = IR).", "data": {}, "isImplemented": true}, {"id": "interactive_capacitor_explorer_1", "name": "Interactive Capacitor Explorer", "type": "Interactive Explorer", "subcategory": "Core Electrical Components", "description": "Explore the relationship between capacitance, voltage, and charge (Q = CV).", "data": {}, "isImplemented": true}, {"id": "interactive_inductor_simulator_1", "name": "Interactive Inductor Simulator", "type": "Interactive Simulator", "subcategory": "Core Electrical Components", "description": "Simulate induced voltage across an inductor (V = -L * dI/dt).", "data": {}, "isImplemented": true}, {"id": "interactive_diode_characteristics_tool_1", "name": "Interactive Diode Characteristics Tool", "type": "Interactive Tool", "subcategory": "Core Electrical Components", "description": "Explore the current-voltage characteristics of a diode using the Shockley Diode Equation.", "data": {}, "isImplemented": true}, {"id": "interactive_transistor_operation_demonstrator_1", "name": "Interactive Transistor Operation Demonstrator", "type": "Interactive Demonstrator", "subcategory": "Core Electrical Components", "description": "Demonstrate the operation modes of a BJT transistor (Cut-off, Active, Saturation).", "data": {}, "isImplemented": true}, {"id": "interactive_components_challenge_1", "name": "Interactive Components Challenge (Module Test)", "type": "Module Test", "subcategory": "Core Electrical Components", "description": "Test your knowledge of core electrical components.", "data": {}, "isImplemented": true}, {"id": "tech_interactive_tool_1", "name": "Circuit Simulator", "type": "Interactive Tool", "subcategory": "Electronics", "description": "An interactive circuit simulator for designing and testing electronic circuits.", "data": {"components": ["Resistor", "Capacitor", "Inductor", "Diode", "Transistor"], "defaultComponent": "Resistor", "gridSize": 20, "showVoltages": true}, "isImplemented": false}, {"id": "tech_gif_player_1", "name": "Computer Hardware Animation", "type": "GIF Player", "subcategory": "Hardware", "description": "A GIF showing how computer hardware components work together.", "data": {"gifUrl": "https://media.giphy.com/media/3o7btNhMBytxAM6YBa/giphy.gif", "caption": "How data flows through a computer system", "autoPlay": true, "loopCount": -1}, "isImplemented": true}, {"id": "interactive_ohms_law_calculator_1", "name": "Interactive Ohm's Law Calculator", "type": "Interactive Calculator", "subcategory": "Analyzing DC Circuits", "description": "Calculate voltage, current, or resistance using <PERSON><PERSON>'s Law.", "data": {}, "isImplemented": true}, {"id": "interactive_kirchhoffs_laws_demonstrator_1", "name": "<PERSON> Ki<PERSON>hoff's Laws Demonstrator", "type": "Interactive Demonstrator", "subcategory": "Analyzing DC Circuits", "description": "Explore <PERSON><PERSON><PERSON>'s Current and Voltage Laws in action.", "data": {}, "isImplemented": true}, {"id": "interactive_series_parallel_circuit_builder_1", "name": "Interactive Series-Parallel Circuit Builder", "type": "Interactive Builder", "subcategory": "Analyzing DC Circuits", "description": "Build and analyze series and parallel circuits.", "data": {}, "isImplemented": true}, {"id": "interactive_thevenin_norton_equivalent_calculator_1", "name": "Interactive Thevenin/Norton Equivalent Calculator", "type": "Interactive Calculator", "subcategory": "Analyzing DC Circuits", "description": "Calculate Thevenin and Norton equivalent circuits.", "data": {}, "isImplemented": true}, {"id": "interactive_dc_circuit_analyzer_1", "name": "Interactive DC Circuit Analyzer", "type": "Interactive Analyzer", "subcategory": "Analyzing DC Circuits", "description": "Analyze various DC circuits and their properties.", "data": {}, "isImplemented": true}, {"id": "interactive_dc_circuits_challenge_1", "name": "Interactive DC Circuits Challenge (Module Test)", "type": "Module Test", "subcategory": "Analyzing DC Circuits", "description": "Test your understanding of DC circuits.", "data": {}, "isImplemented": true}, {"id": "interactive_phasor_visualizer_1", "name": "Interactive Phasor Visualizer", "type": "Interactive Visualizer", "subcategory": "Introduction to AC Circuits", "description": "Visualize phasors for AC circuit analysis.", "data": {}, "isImplemented": true}, {"id": "interactive_impedance_calculator_1", "name": "Interactive Impedance Calculator", "type": "Interactive Calculator", "subcategory": "Introduction to AC Circuits", "description": "Calculate impedance for AC circuits.", "data": {}, "isImplemented": true}, {"id": "interactive_resonance_explorer_1", "name": "Interactive Resonance Explorer", "type": "Interactive Explorer", "subcategory": "Introduction to AC Circuits", "description": "Explore resonance in RLC circuits.", "data": {}, "isImplemented": true}, {"id": "interactive_filter_circuit_simulator_1", "name": "Interactive Filter Circuit Simulator", "type": "Interactive Simulator", "subcategory": "Introduction to AC Circuits", "description": "Simulate the behavior of various filter circuits (e.g., low-pass, high-pass).", "data": {}, "isImplemented": true}, {"id": "interactive_ac_power_calculator_1", "name": "Interactive AC Power Calculator", "type": "Interactive Calculator", "subcategory": "Introduction to AC Circuits", "description": "Calculate real, reactive, and apparent power in AC circuits.", "data": {}, "isImplemented": true}, {"id": "interactive_ac_circuits_challenge_1", "name": "Interactive AC Circuits Challenge (Module Test)", "type": "Module Test", "subcategory": "Introduction to AC Circuits", "description": "Test your understanding of AC circuits.", "data": {}, "isImplemented": true}, {"id": "interactive_nodal_analysis_tool_1", "name": "Interactive Nodal Analysis Tool", "type": "Interactive Tool", "subcategory": "Basic Circuit Analysis Tools and Techniques", "description": "An interactive tool for performing nodal analysis on circuits.", "data": {}, "isImplemented": true}, {"id": "interactive_mesh_analysis_simulator_1", "name": "Interactive Mesh Analysis Simulator", "type": "Interactive Simulator", "subcategory": "Basic Circuit Analysis Tools and Techniques", "description": "Simulate mesh analysis to find loop currents in circuits.", "data": {}, "isImplemented": true}, {"id": "interactive_transfer_function_calculator_1", "name": "Interactive Transfer Function Calculator", "type": "Interactive Calculator", "subcategory": "Basic Circuit Analysis Tools and Techniques", "description": "Calculate transfer functions for various circuits.", "data": {}, "isImplemented": true}, {"id": "interactive_bode_plot_generator_1", "name": "Interactive Bode Plot Generator", "type": "Interactive Generator", "subcategory": "Basic Circuit Analysis Tools and Techniques", "description": "Generate Bode plots to visualize frequency response.", "data": {}, "isImplemented": true}, {"id": "interactive_transient_response_visualizer_1", "name": "Interactive Transient Response Visualizer", "type": "Interactive Visualizer", "subcategory": "Basic Circuit Analysis Tools and Techniques", "description": "Visualize the transient response of circuits over time.", "data": {}, "isImplemented": true}, {"id": "interactive_circuit_analysis_challenge_1", "name": "Interactive Circuit Analysis Challenge (Module Test)", "type": "Module Test", "subcategory": "Basic Circuit Analysis Tools and Techniques", "description": "A comprehensive test on basic circuit analysis tools and techniques.", "data": {}, "isImplemented": true}, {"id": "interactive_power_supply_designer_1", "name": "Interactive Power Supply Designer", "type": "Interactive Designer", "subcategory": "Applications of Fundamental Circuits", "description": "Design and analyze various power supply circuits.", "data": {}, "isImplemented": true}, {"id": "interactive_amplifier_circuit_simulator_1", "name": "Interactive Amplifier Circuit Simulator", "type": "Interactive Simulator", "subcategory": "Applications of Fundamental Circuits", "description": "Simulate different amplifier configurations and their characteristics.", "data": {}, "isImplemented": true}, {"id": "interactive_oscillator_circuit_builder_1", "name": "Interactive Oscillator Circuit Builder", "type": "Interactive Builder", "subcategory": "Applications of Fundamental Circuits", "description": "Build and explore various oscillator circuits.", "data": {}, "isImplemented": true}, {"id": "interactive_digital_logic_gate_simulator_1", "name": "Interactive Digital Logic Gate Simulator", "type": "Interactive Simulator", "subcategory": "Applications of Fundamental Circuits", "description": "Simulate basic digital logic gates and their truth tables.", "data": {}, "isImplemented": true}, {"id": "interactive_sensor_circuit_designer_1", "name": "Interactive Sensor Circuit Designer", "type": "Interactive Designer", "subcategory": "Applications of Fundamental Circuits", "description": "Design and test circuits for various sensor applications.", "data": {}, "isImplemented": true}, {"id": "interactive_circuit_applications_challenge_1", "name": "Interactive Circuit Applications Challenge (Module Test)", "type": "Module Test", "subcategory": "Applications of Fundamental Circuits", "description": "A comprehensive test on the applications of fundamental circuits.", "data": {}, "isImplemented": true}]}, {"category": "puzzles", "widgets": [{"id": "puzzle_mini_game_1", "name": "Logic Puzzle Game", "type": "Mini-Game", "subcategory": "Logic Puzzles", "description": "A game with various logic puzzles to solve.", "data": {"difficulty": "medium", "timeLimit": 120, "puzzleTypes": ["<PERSON><PERSON><PERSON>", "Nonogram", "Logic Grid"]}, "isImplemented": false}, {"id": "puzzle_multiple_choice_1", "name": "Riddle Challenge", "type": "Multiple Choice", "subcategory": "Riddles", "description": "A collection of riddles with multiple choice answers.", "data": {"question": "I speak without a mouth and hear without ears. I have no body, but I come alive with wind. What am I?", "options": ["Echo", "Shadow", "Thought", "Dream"], "correctAnswer": "Echo", "explanation": "An echo speaks (repeats sounds) without having a mouth, and it 'hears' (receives sounds) without ears. It has no physical body but comes alive with the wind (air) carrying sound waves."}, "isImplemented": true}]}, {"category": "curiosity_corner", "widgets": [{"id": "curiosity_gif_player_1", "name": "Fascinating Phenomen<PERSON>", "type": "GIF Player", "subcategory": "Natural Phenomena", "description": "A GIF showing fascinating natural phenomena.", "data": {"gifUrl": "https://media.giphy.com/media/l0HlQhAmlgW8SKlS8/giphy.gif", "caption": "Aurora Bo<PERSON>is (Northern Lights) in real-time", "autoPlay": true, "loopCount": -1}, "isImplemented": true}, {"id": "curiosity_interactive_diagram_1", "name": "Human Body Explorer", "type": "Interactive Diagram", "subcategory": "Biology", "description": "An interactive diagram of the human body systems.", "data": {"imagePath": "assets/images/placeholder.png", "interactivePoints": [{"x": 0.2, "y": 0.3, "label": "Circulatory System", "type": "toggle"}, {"x": 0.5, "y": 0.3, "label": "Nervous System", "type": "toggle"}, {"x": 0.8, "y": 0.3, "label": "Digestive System", "type": "toggle"}, {"x": 0.5, "y": 0.7, "label": "Body Systems", "type": "indicator"}]}, "isImplemented": true}]}, {"category": "calculus", "widgets": [{"id": "interactive_limit_approach_visualizer_1", "name": "Interactive Limit Approach Visualizer", "type": "Interactive Visualizer", "subcategory": "Limits", "description": "Visualize how a function approaches a limit point.", "data": {}, "isImplemented": true}, {"id": "interactive_limit_definition_explorer_1", "name": "Interactive Limit Definition Explorer", "type": "Interactive Explorer", "subcategory": "Limits", "description": "Explore the epsilon-delta definition of a limit.", "data": {}, "isImplemented": true}, {"id": "interactive_limit_from_graph_tool_1", "name": "Interactive Limit from Graph Tool", "type": "Interactive Tool", "subcategory": "Limits", "description": "Determine limits by analyzing function graphs.", "data": {}, "isImplemented": true}, {"id": "interactive_limit_table_generator_1", "name": "Interactive Limit Table Generator", "type": "Interactive Generator", "subcategory": "Limits", "description": "Generate tables of function values to observe limit behavior.", "data": {}, "isImplemented": true}, {"id": "interactive_non_existent_limit_explorer_1", "name": "Interactive Non-Existent Limit Explorer", "type": "Interactive Explorer", "subcategory": "Limits", "description": "Explore functions where limits do not exist (e.g., jump discontinuities).", "data": {}, "isImplemented": true}, {"id": "interactive_limit_explorer_module_test_1", "name": "Interactive Limit Explorer (Module Test)", "type": "Module Test", "subcategory": "Limits", "description": "A comprehensive test for understanding limits.", "data": {}, "isImplemented": true}, {"id": "interactive_average_rate_of_change_calculator_1", "name": "Interactive Average Rate of Change Calculator", "type": "Interactive Calculator", "subcategory": "Derivatives", "description": "Calculate and visualize the average rate of change of a function.", "data": {}, "isImplemented": true}, {"id": "interactive_tangent_line_explorer_1", "name": "Interactive Tangent Line Explorer", "type": "Interactive Explorer", "subcategory": "Derivatives", "description": "Explore the tangent line to a function at a given point.", "data": {}, "isImplemented": true}, {"id": "interactive_derivative_definition_visualizer_1", "name": "Interactive Derivative Definition Visualizer", "type": "Interactive Visualizer", "subcategory": "Derivatives", "description": "Visualize the derivative as the limit of the difference quotient.", "data": {}, "isImplemented": true}, {"id": "interactive_derivative_interpreter_1", "name": "Interactive Derivative Interpreter", "type": "Interactive Interpreter", "subcategory": "Derivatives", "description": "Interpret the meaning of the derivative (increasing/decreasing, concavity).", "data": {}, "isImplemented": true}, {"id": "interactive_basic_differentiation_rules_explorer_1", "name": "Interactive Basic Differentiation Rules Explorer", "type": "Interactive Explorer", "subcategory": "Derivatives", "description": "Explore and apply fundamental differentiation rules (power, constant, sum, etc.).", "data": {}, "isImplemented": true}, {"id": "interactive_derivative_discoverer_module_test_1", "name": "Interactive Derivative Discoverer (Module Test)", "type": "Module Test", "subcategory": "Derivatives", "description": "A comprehensive test for understanding derivatives and differentiation rules.", "data": {}, "isImplemented": true}]}, {"category": "engineering_principles", "widgets": [{"id": "interactive_problem_definition_tool", "name": "Interactive Problem Definition Tool", "type": "Interactive Tool", "subcategory": "The Engineering Design Process in Detail", "description": "A tool to help define engineering problems clearly.", "data": {}, "isImplemented": true}, {"id": "interactive_requirements_analyzer", "name": "Interactive Requirements Analyzer", "type": "Interactive Analyzer", "subcategory": "The Engineering Design Process in Detail", "description": "Analyze and prioritize engineering requirements.", "data": {}, "isImplemented": true}, {"id": "interactive_concept_generator", "name": "Interactive Concept Generator", "type": "Interactive Generator", "subcategory": "The Engineering Design Process in Detail", "description": "Generate and brainstorm design concepts.", "data": {}, "isImplemented": true}, {"id": "interactive_design_evaluation_matrix", "name": "Interactive Design Evaluation Matrix", "type": "Interactive Matrix", "subcategory": "The Engineering Design Process in Detail", "description": "Evaluate design alternatives using a structured matrix.", "data": {}, "isImplemented": true}, {"id": "interactive_prototype_planning_tool", "name": "Interactive Prototype Planning Tool", "type": "Interactive Tool", "subcategory": "The Engineering Design Process in Detail", "description": "Plan and outline the steps for prototyping a design.", "data": {}, "isImplemented": true}, {"id": "interactive_design_process_challenge", "name": "Interactive Design Process Challenge (Module Test)", "type": "Module Test", "subcategory": "The Engineering Design Process in Detail", "description": "A comprehensive test on the engineering design process.", "data": {}, "isImplemented": true}, {"id": "interactive_force_and_motion_simulator", "name": "Interactive Force and Motion Simulator", "type": "Interactive Simulator", "subcategory": "Fundamental Principles of Mechanics", "description": "Simulate forces and their effects on motion.", "data": {}, "isImplemented": true}, {"id": "interactive_stress_and_strain_calculator", "name": "Interactive Stress and Strain Calculator", "type": "Interactive Calculator", "subcategory": "Fundamental Principles of Mechanics", "description": "Calculate stress and strain in materials under load.", "data": {}, "isImplemented": true}, {"id": "interactive_beam_deflection_visualizer", "name": "Interactive Beam Deflection Visualizer", "type": "Interactive Visualizer", "subcategory": "Fundamental Principles of Mechanics", "description": "Visualize the deflection of beams under various loads.", "data": {}, "isImplemented": true}, {"id": "interactive_mechanical_advantage_calculator", "name": "Interactive Mechanical Advantage Calculator", "type": "Interactive Calculator", "subcategory": "Fundamental Principles of Mechanics", "description": "Calculate mechanical advantage for simple machines.", "data": {}, "isImplemented": true}, {"id": "interactive_structural_analysis_tool", "name": "Interactive Structural Analysis Tool", "type": "Interactive Tool", "subcategory": "Fundamental Principles of Mechanics", "description": "Perform basic structural analysis on trusses and frames.", "data": {}, "isImplemented": true}, {"id": "interactive_mechanics_challenge", "name": "Interactive Mechanics Challenge (Module Test)", "type": "Module Test", "subcategory": "Fundamental Principles of Mechanics", "description": "A comprehensive test on fundamental principles of mechanics.", "data": {}, "isImplemented": true}, {"id": "interactive_material_properties_explorer", "name": "Interactive Material Properties Explorer", "type": "Interactive Explorer", "subcategory": "Materials Science and Engineering", "description": "Explore the properties of different engineering materials.", "data": {}, "isImplemented": true}, {"id": "interactive_stress_strain_curve_generator", "name": "Interactive Stress-Strain Curve Generator", "type": "Interactive Generator", "subcategory": "Materials Science and Engineering", "description": "Generate and analyze stress-strain curves for materials.", "data": {}, "isImplemented": true}, {"id": "interactive_material_selection_tool", "name": "Interactive Material Selection Tool", "type": "Interactive Tool", "subcategory": "Materials Science and Engineering", "description": "Select appropriate materials for engineering applications.", "data": {}, "isImplemented": true}, {"id": "interactive_phase_diagram_analyzer", "name": "Interactive Phase Diagram Analyzer", "type": "Interactive Analyzer", "subcategory": "Materials Science and Engineering", "description": "Analyze phase diagrams for material systems.", "data": {}, "isImplemented": true}, {"id": "interactive_material_testing_simulator", "name": "Interactive Material Testing Simulator", "type": "Interactive Simulator", "subcategory": "Materials Science and Engineering", "description": "Simulate various material testing procedures (e.g., tensile, hardness).", "data": {}, "isImplemented": true}, {"id": "interactive_materials_challenge", "name": "Interactive Materials Challenge (Module Test)", "type": "Module Test", "subcategory": "Materials Science and Engineering", "description": "A comprehensive test on materials science and engineering.", "data": {}, "isImplemented": true}, {"id": "interactive_thermodynamic_cycle_simulator", "name": "Interactive Thermodynamic Cycle Simulator", "type": "Interactive Simulator", "subcategory": "Thermodynamics and Fluid Mechanics (Introduction)", "description": "Simulate and analyze various thermodynamic cycles (e.g., <PERSON><PERSON>, <PERSON><PERSON>).", "data": {}, "isImplemented": true}, {"id": "interactive_heat_transfer_calculator", "name": "Interactive Heat Transfer Calculator", "type": "Interactive Calculator", "subcategory": "Thermodynamics and Fluid Mechanics (Introduction)", "description": "Calculate heat transfer rates for conduction, convection, and radiation.", "data": {}, "isImplemented": true}, {"id": "interactive_fluid_flow_visualizer", "name": "Interactive Fluid Flow Visualizer", "type": "Interactive Visualizer", "subcategory": "Thermodynamics and Fluid Mechanics (Introduction)", "description": "Visualize fluid flow patterns and properties (e.g., laminar, turbulent).", "data": {}, "isImplemented": true}, {"id": "interactive_pressure_and_buoyancy_demonstrator", "name": "Interactive Pressure and Buoyancy Demonstrator", "type": "Interactive Demonstrator", "subcategory": "Thermodynamics and Fluid Mechanics (Introduction)", "description": "Demonstrate principles of pressure and buoyancy in fluids.", "data": {}, "isImplemented": true}, {"id": "interactive_efficiency_calculator", "name": "Interactive Efficiency Calculator", "type": "Interactive Calculator", "subcategory": "Thermodynamics and Fluid Mechanics (Introduction)", "description": "Calculate the efficiency of thermodynamic systems and machines.", "data": {}, "isImplemented": true}, {"id": "interactive_thermo_fluids_challenge", "name": "Interactive Thermo-Fluids Challenge (Module Test)", "type": "Module Test", "subcategory": "Thermodynamics and Fluid Mechanics (Introduction)", "description": "A comprehensive test on thermodynamics and fluid mechanics.", "data": {}, "isImplemented": true}, {"id": "interactive_system_decomposition_tool", "name": "Interactive System Decomposition Tool", "type": "Interactive Tool", "subcategory": "Systems Engineering and Project Management", "description": "Decompose complex systems into smaller, manageable components.", "data": {}, "isImplemented": true}, {"id": "interactive_requirements_traceability_matrix", "name": "Interactive Requirements Traceability Matrix", "type": "Interactive Matrix", "subcategory": "Systems Engineering and Project Management", "description": "Create and manage a requirements traceability matrix.", "data": {}, "isImplemented": true}, {"id": "interactive_project_schedule_builder", "name": "Interactive Project Schedule Builder", "type": "Interactive Builder", "subcategory": "Systems Engineering and Project Management", "description": "Build and visualize project schedules using Gantt charts or PERT diagrams.", "data": {}, "isImplemented": true}, {"id": "interactive_risk_assessment_tool", "name": "Interactive Risk Assessment Tool", "type": "Interactive Tool", "subcategory": "Systems Engineering and Project Management", "description": "Identify, analyze, and mitigate project risks.", "data": {}, "isImplemented": true}, {"id": "interactive_resource_allocation_simulator", "name": "Interactive Resource Allocation Simulator", "type": "Interactive Simulator", "subcategory": "Systems Engineering and Project Management", "description": "Simulate resource allocation and its impact on project timelines.", "data": {}, "isImplemented": true}, {"id": "interactive_systems_engineering_challenge", "name": "Interactive Systems Engineering Challenge (Module Test)", "type": "Module Test", "subcategory": "Systems Engineering and Project Management", "description": "A comprehensive test on systems engineering and project management.", "data": {}, "isImplemented": true}]}, {"category": "puzzles", "widgets": [{"id": "interactive_logic_grid_puzzle", "name": "Interactive Logic Grid Puzzle", "type": "Interactive Puzzle", "subcategory": "Daily Puzzle Challenge", "description": "Solve logic grid puzzles by deducing relationships.", "data": {}, "isImplemented": true}, {"id": "interactive_pattern_completion_puzzle", "name": "Interactive Pattern Completion Puzzle", "type": "Interactive Puzzle", "subcategory": "Daily Puzzle Challenge", "description": "Complete visual or numerical patterns.", "data": {}, "isImplemented": true}, {"id": "interactive_spatial_reasoning_puzzle", "name": "Interactive Spatial Reasoning Puzzle", "type": "Interactive Puzzle", "subcategory": "Daily Puzzle Challenge", "description": "Solve puzzles requiring spatial manipulation and visualization.", "data": {}, "isImplemented": true}, {"id": "interactive_mathematical_puzzle", "name": "Interactive Mathematical Puzzle", "type": "Interactive Puzzle", "subcategory": "Daily Puzzle Challenge", "description": "Engage with various mathematical puzzles and brain teasers.", "data": {}, "isImplemented": true}, {"id": "interactive_word_puzzle", "name": "Interactive Word Puzzle", "type": "Interactive Puzzle", "subcategory": "Daily Puzzle Challenge", "description": "Solve word-based puzzles like crosswords, anagrams, or word searches.", "data": {}, "isImplemented": true}, {"id": "interactive_cryptic_puzzle", "name": "Interactive Cryptic Puzzle", "type": "Interactive Puzzle", "subcategory": "Daily Puzzle Challenge", "description": "Decipher cryptic clues to solve puzzles.", "data": {}, "isImplemented": true}, {"id": "interactive_visual_puzzle", "name": "Interactive Visual Puzzle", "type": "Interactive Puzzle", "subcategory": "Daily Puzzle Challenge", "description": "Solve puzzles based on visual cues and illusions.", "data": {}, "isImplemented": true}, {"id": "interactive_sequence_puzzle", "name": "Interactive Sequence Puzzle", "type": "Interactive Puzzle", "subcategory": "Daily Puzzle Challenge", "description": "Identify and extend patterns in sequences.", "data": {}, "isImplemented": true}, {"id": "interactive_lateral_thinking_puzzle", "name": "Interactive Lateral Thinking Puzzle", "type": "Interactive Puzzle", "subcategory": "Daily Puzzle Challenge", "description": "Solve puzzles that require unconventional thinking.", "data": {}, "isImplemented": true}, {"id": "interactive_strategy_puzzle", "name": "Interactive Strategy Puzzle", "type": "Interactive Puzzle", "subcategory": "Daily Puzzle Challenge", "description": "Engage in mini-games that test strategic thinking.", "data": {}, "isImplemented": true}]}, {"category": "curiosity_corner", "widgets": [{"id": "curiosity_gif_player_1", "name": "Fascinating Phenomen<PERSON>", "type": "GIF Player", "subcategory": "Natural Phenomena", "description": "A GIF showing fascinating natural phenomena.", "data": {"gifUrl": "https://media.giphy.com/media/l0HlQhAmlgW8SKlS8/giphy.gif", "caption": "Aurora Bo<PERSON>is (Northern Lights) in real-time", "autoPlay": true, "loopCount": -1}, "isImplemented": true}, {"id": "curiosity_interactive_diagram_1", "name": "Human Body Explorer", "type": "Interactive Diagram", "subcategory": "Biology", "description": "An interactive diagram of the human body systems.", "data": {"imagePath": "assets/images/placeholder.png", "interactivePoints": [{"x": 0.2, "y": 0.3, "label": "Circulatory System", "type": "toggle"}, {"x": 0.5, "y": 0.3, "label": "Nervous System", "type": "toggle"}, {"x": 0.8, "y": 0.3, "label": "Digestive System", "type": "toggle"}, {"x": 0.5, "y": 0.7, "label": "Body Systems", "type": "indicator"}]}, "isImplemented": true}, {"id": "interactive_gravity_reversal_simulator", "name": "Interactive Gravity Reversal Simulator", "type": "Interactive Simulator", "subcategory": "What If Gravity Suddenly Reversed?", "description": "Simulate the effects of gravity suddenly reversing.", "data": {}, "isImplemented": true}, {"id": "interactive_object_trajectory_calculator", "name": "Interactive Object Trajectory Calculator", "type": "Interactive Calculator", "subcategory": "What If Gravity Suddenly Reversed?", "description": "Calculate and visualize object trajectories under reversed gravity.", "data": {}, "isImplemented": true}, {"id": "interactive_structural_stability_analyzer", "name": "Interactive Structural Stability Analyzer", "type": "Interactive Analyzer", "subcategory": "What If Gravity Suddenly Reversed?", "description": "Analyze the stability of structures if gravity reversed.", "data": {}, "isImplemented": true}, {"id": "interactive_atmospheric_effects_visualizer", "name": "Interactive Atmospheric Effects Visualizer", "type": "Interactive Visualizer", "subcategory": "What If Gravity Suddenly Reversed?", "description": "Visualize atmospheric changes with reversed gravity.", "data": {}, "isImplemented": true}, {"id": "interactive_biological_impact_explorer", "name": "Interactive Biological Impact Explorer", "type": "Interactive Explorer", "subcategory": "What If Gravity Suddenly Reversed?", "description": "Explore the biological impacts of reversed gravity on life forms.", "data": {}, "isImplemented": true}, {"id": "interactive_light_speed_adjuster", "name": "Interactive Light Speed Adjuster", "type": "Interactive Adjuster", "subcategory": "What If Light Traveled Much Slower?", "description": "Adjust the speed of light and observe its effects on perception.", "data": {}, "isImplemented": false}, {"id": "interactive_time_delay_visualizer", "name": "Interactive Time Delay Visualizer", "type": "Interactive Visualizer", "subcategory": "What If Light Traveled Much Slower?", "description": "Visualize time delays in communication due to slower light speed.", "data": {}, "isImplemented": true}, {"id": "interactive_communication_impact_simulator", "name": "Interactive Communication Impact Simulator", "type": "Interactive Simulator", "subcategory": "What If Light Traveled Much Slower?", "description": "Simulate the impact on global communication with slower light.", "data": {}, "isImplemented": true}, {"id": "interactive_relativistic_effects_calculator", "name": "Interactive Relativistic Effects Calculator", "type": "Interactive Calculator", "subcategory": "What If Light Traveled Much Slower?", "description": "Calculate relativistic effects at slower light speeds.", "data": {}, "isImplemented": true}, {"id": "interactive_technology_impact_explorer", "name": "Interactive Technology Impact Explorer", "type": "Interactive Explorer", "subcategory": "What If Light Traveled Much Slower?", "description": "Explore the impact of slower light on technological development.", "data": {}, "isImplemented": false}, {"id": "interactive_orbital_dynamics_simulator", "name": "Interactive Orbital Dynamics Simulator", "type": "Interactive Simulator", "subcategory": "What If Earth Had Two Moons?", "description": "Simulate orbital dynamics with two moons.", "data": {}, "isImplemented": true}, {"id": "interactive_tidal_forces_calculator", "name": "Interactive Tidal Forces Calculator", "type": "Interactive Calculator", "subcategory": "What If Earth Had Two Moons?", "description": "Calculate tidal forces with an additional moon.", "data": {}, "isImplemented": true}, {"id": "interactive_climate_impact_visualizer", "name": "Interactive Climate Impact Visualizer", "type": "Interactive Visualizer", "subcategory": "What If Earth Had Two Moons?", "description": "Visualize climate changes due to two moons.", "data": {}, "isImplemented": true}, {"id": "interactive_ecosystem_effects_explorer", "name": "Interactive Ecosystem Effects Explorer", "type": "Interactive Explorer", "subcategory": "What If Earth Had Two Moons?", "description": "Explore the effects on ecosystems with two moons.", "data": {}, "isImplemented": false}, {"id": "interactive_cultural_development_simulator", "name": "Interactive Cultural Development Simulator", "type": "Interactive Simulator", "subcategory": "What If Earth Had Two Moons?", "description": "Simulate cultural and societal development with two moons.", "data": {}, "isImplemented": true}, {"id": "interactive_energy_production_calculator", "name": "Interactive Energy Production Calculator", "type": "Interactive Calculator", "subcategory": "What If Humans Could Photosynthesize?", "description": "Calculate energy production from human photosynthesis.", "data": {}, "isImplemented": false}, {"id": "interactive_biological_adaptation_visualizer", "name": "Interactive Biological Adaptation Visualizer", "type": "Interactive Visualizer", "subcategory": "What If Humans Could Photosynthesize?", "description": "Visualize biological adaptations for photosynthesis in humans.", "data": {}, "isImplemented": false}, {"id": "interactive_societal_impact_simulator", "name": "Interactive Societal Impact Simulator", "type": "Interactive Simulator", "subcategory": "What If Humans Could Photosynthesize?", "description": "Simulate societal changes if humans photosynthesized.", "data": {}, "isImplemented": true}, {"id": "interactive_evolutionary_pathway_explorer", "name": "Interactive Evolutionary Pathway Explorer", "type": "Interactive Explorer", "subcategory": "What If Humans Could Photosynthesize?", "description": "Explore evolutionary pathways for human photosynthesis.", "data": {}, "isImplemented": false}, {"id": "interactive_nutritional_requirements_analyzer", "name": "Interactive Nutritional Requirements Analyzer", "type": "Interactive Analyzer", "subcategory": "What If Humans Could Photosynthesize?", "description": "Analyze nutritional requirements with photosynthesis.", "data": {}, "isImplemented": false}, {"id": "interactive_timeline_branching_visualizer", "name": "Interactive Timeline Branching Visualizer", "type": "Interactive Visualizer", "subcategory": "What If Time Travel Were Possible (Paradox-Free)?", "description": "Visualize branching timelines in paradox-free time travel.", "data": {}, "isImplemented": true}, {"id": "interactive_causality_analyzer", "name": "Interactive Causality Analyzer", "type": "Interactive Analyzer", "subcategory": "What If Time Travel Were Possible (Paradox-Free)?", "description": "Analyze causality and its implications in time travel.", "data": {}, "isImplemented": false}, {"id": "interactive_historical_impact_simulator", "name": "Interactive Historical Impact Simulator", "type": "Interactive Simulator", "subcategory": "What If Time Travel Were Possible (Paradox-Free)?", "description": "Simulate the impact of time travel on historical events.", "data": {}, "isImplemented": false}, {"id": "interactive_parallel_universe_explorer", "name": "Interactive Parallel Universe Explorer", "type": "Interactive Explorer", "subcategory": "What If Time Travel Were Possible (Paradox-Free)?", "description": "Explore parallel universes created by time travel.", "data": {}, "isImplemented": false}, {"id": "interactive_ethical_dilemma_solver", "name": "Interactive Ethical Dilemma Solver", "type": "Interactive Solver", "subcategory": "What If Time Travel Were Possible (Paradox-Free)?", "description": "Solve ethical dilemmas arising from time travel scenarios.", "data": {}, "isImplemented": false}, {"id": "interactive_component_explorer", "name": "Interactive Component Explorer", "type": "Interactive Explorer", "subcategory": "The Smartphone: From Sand to Pocket Computer", "description": "Explore the internal components of a smartphone.", "data": {}, "isImplemented": false}, {"id": "interactive_manufacturing_process_visualizer", "name": "Interactive Manufacturing Process Visualizer", "type": "Interactive Visualizer", "subcategory": "The Smartphone: From Sand to Pocket Computer", "description": "Visualize the manufacturing process of a smartphone.", "data": {}, "isImplemented": false}, {"id": "interactive_supply_chain_mapper", "name": "Interactive Supply Chain Mapper", "type": "Interactive Mapper", "subcategory": "The Smartphone: From Sand to Pocket Computer", "description": "Map the global supply chain for smartphone production.", "data": {}, "isImplemented": false}, {"id": "interactive_assembly_simulator", "name": "Interactive Assembly Simulator", "type": "Interactive Simulator", "subcategory": "The Smartphone: From Sand to Pocket Computer", "description": "Simulate the assembly process of a smartphone.", "data": {}, "isImplemented": false}, {"id": "interactive_evolution_timeline", "name": "Interactive Evolution Timeline", "type": "Interactive Timeline", "subcategory": "The Smartphone: From Sand to Pocket Computer", "description": "Explore the evolution of smartphones over time.", "data": {}, "isImplemented": false}, {"id": "interactive_data_packet_tracer", "name": "Interactive Data Packet Tracer", "type": "Interactive Tracer", "subcategory": "The Internet: Connecting the World Wirelessly and Wired", "description": "Trace the path of data packets across the internet.", "data": {}, "isImplemented": false}, {"id": "interactive_network_infrastructure_explorer", "name": "Interactive Network Infrastructure Explorer", "type": "Interactive Explorer", "subcategory": "The Internet: Connecting the World Wirelessly and Wired", "description": "Explore the physical and logical infrastructure of the internet.", "data": {}, "isImplemented": false}, {"id": "interactive_protocol_stack_visualizer", "name": "Interactive Protocol Stack Visualizer", "type": "Interactive Visualizer", "subcategory": "The Internet: Connecting the World Wirelessly and Wired", "description": "Visualize the layers of the internet protocol stack.", "data": {}, "isImplemented": false}, {"id": "interactive_bandwidth_simulator", "name": "Interactive Bandwidth Simulator", "type": "Interactive Simulator", "subcategory": "The Internet: Connecting the World Wirelessly and Wired", "description": "Simulate the effects of different bandwidths on data transfer.", "data": {}, "isImplemented": false}, {"id": "interactive_global_connectivity_map", "name": "Interactive Global Connectivity Map", "type": "Interactive Map", "subcategory": "The Internet: Connecting the World Wirelessly and Wired", "description": "Explore a global map of internet connectivity.", "data": {}, "isImplemented": false}, {"id": "interactive_engine_component_explorer", "name": "Interactive Engine Component Explorer", "type": "Interactive Explorer", "subcategory": "The Automobile: A Century of Motion and Innovation", "description": "Explore the components of an automobile engine.", "data": {}, "isImplemented": false}, {"id": "interactive_drivetrain_simulator", "name": "Interactive Drivetrain Simulator", "type": "Interactive Simulator", "subcategory": "The Automobile: A Century of Motion and Innovation", "description": "Simulate the operation of an automobile drivetrain.", "data": {}, "isImplemented": false}, {"id": "interactive_safety_system_demonstrator", "name": "Interactive Safety System Demonstrator", "type": "Interactive Demonstrator", "subcategory": "The Automobile: A Century of Motion and Innovation", "description": "Demonstrate various automobile safety systems.", "data": {}, "isImplemented": false}, {"id": "interactive_manufacturing_process_visualizer_auto", "name": "Interactive Manufacturing Process Visualizer (Automobile)", "type": "Interactive Visualizer", "subcategory": "The Automobile: A Century of Motion and Innovation", "description": "Visualize the manufacturing process of an automobile.", "data": {}, "isImplemented": false}, {"id": "interactive_evolution_timeline_auto", "name": "Interactive Evolution Timeline (Automobile)", "type": "Interactive Timeline", "subcategory": "The Automobile: A Century of Motion and Innovation", "description": "Explore the evolution of automobiles over time.", "data": {}, "isImplemented": false}, {"id": "interactive_display_technology_explorer", "name": "Interactive Display Technology Explorer", "type": "Interactive Explorer", "subcategory": "The Television: From Vacuum Tubes to Flat Screens", "description": "Explore different television display technologies.", "data": {}, "isImplemented": false}, {"id": "interactive_signal_processing_visualizer", "name": "Interactive Signal Processing Visualizer", "type": "Interactive Visualizer", "subcategory": "The Television: From Vacuum Tubes to Flat Screens", "description": "Visualize signal processing in televisions.", "data": {}, "isImplemented": false}, {"id": "interactive_manufacturing_process_simulator_tv", "name": "Interactive Manufacturing Process Simulator (TV)", "type": "Interactive Simulator", "subcategory": "The Television: From Vacuum Tubes to Flat Screens", "description": "Simulate the manufacturing process of a television.", "data": {}, "isImplemented": false}, {"id": "interactive_resolution_comparator", "name": "Interactive Resolution Comparator", "type": "Interactive Comparator", "subcategory": "The Television: From Vacuum Tubes to Flat Screens", "description": "Compare different television display resolutions.", "data": {}, "isImplemented": false}, {"id": "interactive_evolution_timeline_tv", "name": "Interactive Evolution Timeline (TV)", "type": "Interactive Timeline", "subcategory": "The Television: From Vacuum Tubes to Flat Screens", "description": "Explore the evolution of televisions over time.", "data": {}, "isImplemented": false}, {"id": "interactive_cooling_cycle_simulator", "name": "Interactive Cooling Cycle Simulator", "type": "Interactive Simulator", "subcategory": "The Refrigerator: Keeping Things Cool Through Science", "description": "Simulate the refrigeration cooling cycle.", "data": {}, "isImplemented": true}, {"id": "interactive_component_explorer_refrigerator", "name": "Interactive Component Explorer (Refrigerator)", "type": "Interactive Explorer", "subcategory": "The Refrigerator: Keeping Things Cool Through Science", "description": "Explore the internal components of a refrigerator.", "data": {}, "isImplemented": true}, {"id": "interactive_energy_efficiency_calculator", "name": "Interactive Energy Efficiency Calculator", "type": "Interactive Calculator", "subcategory": "The Refrigerator: Keeping Things Cool Through Science", "description": "Calculate the energy efficiency of refrigerators.", "data": {}, "isImplemented": true}, {"id": "interactive_manufacturing_process_visualizer_refrigerator", "name": "Interactive Manufacturing Process Visualizer (Refrigerator)", "type": "Interactive Visualizer", "subcategory": "The Refrigerator: Keeping Things Cool Through Science", "description": "Visualize the manufacturing process of a refrigerator.", "data": {}, "isImplemented": true}, {"id": "interactive_evolution_timeline_refrigerator", "name": "Interactive Evolution Timeline (Refrigerator)", "type": "Interactive Timeline", "subcategory": "The Refrigerator: Keeping Things Cool Through Science", "description": "Explore the evolution of refrigerators over time.", "data": {}, "isImplemented": true}, {"id": "interactive_ethical_dilemma_solver", "name": "Interactive Ethical Dilemma Solver", "type": "Interactive Solver", "subcategory": "What If Time Travel Were Possible (Paradox-Free)?", "description": "Solve ethical dilemmas arising from time travel scenarios.", "data": {}, "isImplemented": true}, {"id": "interactive_component_explorer", "name": "Interactive Component Explorer", "type": "Interactive Explorer", "subcategory": "The Smartphone: From Sand to Pocket Computer", "description": "Explore the internal components of a smartphone.", "data": {}, "isImplemented": true}, {"id": "interactive_manufacturing_process_visualizer", "name": "Interactive Manufacturing Process Visualizer", "type": "Interactive Visualizer", "subcategory": "The Smartphone: From Sand to Pocket Computer", "description": "Visualize the manufacturing process of a smartphone.", "data": {}, "isImplemented": true}]}]