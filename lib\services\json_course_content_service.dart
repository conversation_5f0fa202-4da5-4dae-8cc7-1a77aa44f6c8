import 'dart:convert';
import 'package:flutter/foundation.dart';
import 'package:flutter/services.dart';
import '../models/course_models.dart';
import 'asset_manager.dart';

/// Service for loading and managing course content from JSON files
class JsonCourseContentService {
  static final JsonCourseContentService _instance =
      JsonCourseContentService._internal();

  // Singleton instance
  factory JsonCourseContentService() {
    return _instance;
  }

  JsonCourseContentService._internal();

  // Cache for loaded courses and modules
  final Map<String, Course> _courseCache = {};
  final Map<String, Module> _moduleCache = {};

  // Asset manager for handling media assets
  final AssetManager _assetManager = AssetManager();

  /// Load a course by its category and ID
  /// Returns null if the course doesn't exist or can't be loaded
  Future<Course?> loadCourse(String categoryId, String courseId) async {
    // Create a unique cache key
    final cacheKey = '$categoryId/$courseId';

    // Check if the course is already in the cache
    if (_courseCache.containsKey(cacheKey)) {
      debugPrint('[loadCourse] Returning cached course: $cacheKey');
      return _courseCache[cacheKey];
    }

    try {
      // Load the course JSON file
      final filePath =
          'assets/data/courses/categories/$categoryId/$courseId/course.json';
      debugPrint('[loadCourse] Attempting to load course from: $filePath');

      // Special debug for coming_soon category
      if (categoryId == 'coming_soon') {
        debugPrint(
          '[loadCourse] SPECIAL DEBUG FOR COMING_SOON CATEGORY: $courseId',
        );
      }

      // Check if the file exists in the asset manifest
      final manifestContent = await rootBundle.loadString('AssetManifest.json');
      final manifestMap = json.decode(manifestContent) as Map<String, dynamic>;

      if (!manifestMap.containsKey(filePath)) {
        debugPrint(
          '[loadCourse] WARNING: File not found in asset manifest: $filePath',
        );
        debugPrint('[loadCourse] Available assets in this category:');
        for (final key in manifestMap.keys) {
          if (key.contains('assets/data/courses/categories/$categoryId/')) {
            debugPrint('[loadCourse]   - $key');
          }
        }

        // Try to create a default course object for known courses
        if (categoryId == 'maths' && courseId == 'mathematical_thinking') {
          debugPrint(
            '[loadCourse] Creating default Mathematical Thinking course',
          );
          final defaultCourse = _createDefaultMathematicalThinkingCourse();
          _courseCache[cacheKey] = defaultCourse;
          return defaultCourse;
        } else if (categoryId == 'science' &&
            courseId == 'scientific_thinking') {
          debugPrint(
            '[loadCourse] Creating default Scientific Thinking course',
          );
          final defaultCourse = _createDefaultScientificThinkingCourse();
          _courseCache[cacheKey] = defaultCourse;
          return defaultCourse;
        } else if (categoryId == 'maths' &&
            courseId == 'equations-and-algebra') {
          debugPrint(
            '[loadCourse] Creating default Equations and Algebra course',
          );
          final defaultCourse = _createDefaultEquationsAndAlgebraCourse();
          _courseCache[cacheKey] = defaultCourse;
          return defaultCourse;
        }

        return null;
      }

      final jsonString = await rootBundle.loadString(filePath);
      debugPrint(
        '[loadCourse] Successfully loaded JSON string, length: ${jsonString.length}',
      );

      // Parse the JSON
      final jsonData = json.decode(jsonString);
      debugPrint('[loadCourse] Successfully parsed JSON data');

      // Convert to Course object
      final course = Course.fromJson(jsonData);
      debugPrint(
        '[loadCourse] Successfully created Course object: ${course.title}',
      );
      debugPrint(
        '[loadCourse] Parsed course has ${course.modules.length} module metadata entries from course.json.',
      );
      for (var i = 0; i < course.modules.length; i++) {
        debugPrint(
          '[loadCourse]   Course.modules[$i] (from course.json): ID=${course.modules[i].id}, Title=${course.modules[i].title}, Lessons=${course.modules[i].lessons.length}',
        );
      }

      // Cache the course
      _courseCache[cacheKey] = course;

      return course;
    } catch (e) {
      debugPrint('[loadCourse] Error loading course $categoryId/$courseId: $e');
      debugPrint('[loadCourse] Stack trace: ${StackTrace.current}');
      return null;
    }
  }

  /// Create a default Mathematical Thinking course
  Course _createDefaultMathematicalThinkingCourse() {
    return Course(
      id: 'mathematical_thinking',
      title: 'Mathematical Thinking',
      description:
          'Develop core logic and robust problem-solving approaches for mathematical challenges.',
      categoryId: 'maths',
      thumbnailPath: 'assets/images/math_thinking.png',
      difficulty: 'Beginner',
      modules: [
        Module(
          id: 'art-of-logical-deduction',
          title: 'The Art of Logical Deduction',
          description:
              'Unravel the power of reasoning, proving statements, and building solid arguments.',
          order: 1,
          lessons: [],
        ),
        Module(
          id: 'number-sense-and-intuition',
          title: 'Number Sense and Intuition',
          description:
              'Develop a deep understanding of numbers, their properties, and relationships.',
          order: 2,
          lessons: [],
        ),
        Module(
          id: 'visualizing-geometry',
          title: 'Visualizing Geometry',
          description:
              'Explore shapes, spaces, and their properties through interactive visualizations.',
          order: 3,
          lessons: [],
        ),
        Module(
          id: 'power-of-patterns-and-relationships',
          title: 'The Power of Patterns and Relationships',
          description:
              'Uncover the beauty of repeating sequences and how different quantities connect.',
          order: 4,
          lessons: [],
        ),
        Module(
          id: 'exploring-the-world-of-numbers',
          title: 'Exploring the World of Numbers',
          description:
              'Build a strong foundation in number operations and their visual interpretations.',
          order: 5,
          lessons: [],
        ),
      ],
    );
  }

  /// Create a default Scientific Thinking course
  Course _createDefaultScientificThinkingCourse() {
    return Course(
      id: 'scientific_thinking',
      title: 'Scientific Thinking',
      description:
          'Learn the foundations of scientific inquiry and develop critical thinking skills.',
      categoryId: 'science',
      thumbnailPath: 'assets/images/scientific_thinking.png',
      difficulty: 'Beginner',
      modules: [
        Module(
          id: 'foundation-of-inquiry',
          title: 'Foundation of Inquiry',
          description:
              'Understand the scientific method and how to ask good questions.',
          order: 1,
          lessons: [],
        ),
        Module(
          id: 'observing-recording-interpreting-data',
          title: 'Observing, Recording, and Interpreting Data',
          description:
              'Learn how to collect, organize, and analyze scientific data.',
          order: 2,
          lessons: [],
        ),
        Module(
          id: 'scientific-models-and-theories',
          title: 'Scientific Models and Theories',
          description:
              'Explore how scientists create models to explain natural phenomena.',
          order: 3,
          lessons: [],
        ),
        Module(
          id: 'scientific-reasoning-argumentation',
          title: 'Scientific Reasoning and Argumentation',
          description:
              'Develop skills in logical reasoning and evidence-based arguments.',
          order: 4,
          lessons: [],
        ),
        Module(
          id: 'frontiers-of-scientific-inquiry',
          title: 'Frontiers of Scientific Inquiry',
          description:
              'Discover cutting-edge research and unsolved scientific questions.',
          order: 5,
          lessons: [],
        ),
      ],
    );
  }

  /// Create a default Equations and Algebra course
  Course _createDefaultEquationsAndAlgebraCourse() {
    return Course(
      id: 'equations-and-algebra',
      title: 'Equations and Algebra',
      description:
          'Master the fundamentals of algebraic thinking and equation solving.',
      categoryId: 'maths',
      thumbnailPath: 'assets/images/equations_algebra.png',
      difficulty: 'Intermediate',
      modules: [
        Module(
          id: 'language-of-variables',
          title: 'The Language of Variables',
          description:
              'Learn how to represent unknown quantities and relationships using variables.',
          order: 1,
          lessons: [],
        ),
        Module(
          id: 'solving-one-step-equations',
          title: 'Solving One-Step Equations',
          description:
              'Master the techniques for solving basic algebraic equations.',
          order: 2,
          lessons: [],
        ),
        Module(
          id: 'tackling-two-step-equations',
          title: 'Tackling Two-Step Equations',
          description: 'Advance your skills to solve more complex equations.',
          order: 3,
          lessons: [],
        ),
        Module(
          id: 'introduction-to-inequalities',
          title: 'Introduction to Inequalities',
          description: 'Explore mathematical relationships beyond equality.',
          order: 4,
          lessons: [],
        ),
        Module(
          id: 'exploring-algebraic-relationships',
          title: 'Exploring Algebraic Relationships',
          description:
              'Discover how equations can model real-world situations.',
          order: 5,
          lessons: [],
        ),
      ],
    );
  }

  /// Load a specific module from a course
  Future<Module?> loadModule(
    String categoryId,
    String courseId,
    String moduleId,
  ) async {
    // Create a unique cache key
    final cacheKey = '$categoryId/$courseId/$moduleId';
    debugPrint('[loadModule] Attempting to load module: $cacheKey');

    // Check if the module is already in the cache
    if (_moduleCache.containsKey(cacheKey)) {
      debugPrint('[loadModule] Returning cached module: $cacheKey');
      return _moduleCache[cacheKey];
    }

    try {
      // Load the module JSON file
      final filePath =
          'assets/data/courses/categories/$categoryId/$courseId/modules/$moduleId.json';
      debugPrint('[loadModule] Loading module from file: $filePath');

      final jsonString = await rootBundle.loadString(filePath);
      debugPrint(
        '[loadModule] Successfully loaded module JSON, length: ${jsonString.length}',
      );

      // Parse the JSON
      final jsonData = json.decode(jsonString);
      debugPrint('[loadModule] Successfully parsed module JSON data');

      // Convert to Module object
      final module = Module.fromJson(jsonData);
      debugPrint(
        '[loadModule] Successfully created Module object: ${module.title}, with ${module.lessons.length} lessons',
      );
      for (var lesson in module.lessons) {
        debugPrint(
          '[loadModule]   Lesson in ${module.title}: ${lesson.title} (ID: ${lesson.id}), ContentBlocks: ${lesson.contentBlocks.length}',
        );
      }

      // Cache the module
      _moduleCache[cacheKey] = module;
      debugPrint('[loadModule] Module cached: $cacheKey');

      // Preload assets for this module
      await _assetManager.preloadModuleAssets(categoryId, courseId, module);
      debugPrint('[loadModule] Module assets preloaded for ${module.title}');

      return module;
    } catch (e) {
      debugPrint(
        '[loadModule] Error loading module $categoryId/$courseId/$moduleId: $e',
      );
      debugPrint('[loadModule] Stack trace: ${StackTrace.current}');
      return null;
    }
  }

  /// Load all modules for a course
  Future<List<Module>> loadAllModules(
    String categoryId,
    String courseId,
    Course course,
  ) async {
    debugPrint(
      '[loadAllModules] Loading all modules for course: $categoryId/$courseId',
    );
    debugPrint(
      '[loadAllModules] Initial course object has ${course.modules.length} module metadata entries.',
    );
    for (var i = 0; i < course.modules.length; i++) {
      debugPrint(
        '[loadAllModules] Initial course.modules[$i]: ID=${course.modules[i].id}, Title=${course.modules[i].title}, Lessons=${course.modules[i].lessons.length}',
      );
    }

    final List<Module> loadedModulesList = [];

    for (final moduleMetadata in course.modules) {
      debugPrint(
        '[loadAllModules] Processing moduleMetadata ID: ${moduleMetadata.id}, Title: ${moduleMetadata.title}',
      );
      final Module? fullyLoadedModule = await loadModule(
        categoryId,
        courseId,
        moduleMetadata.id,
      );
      if (fullyLoadedModule != null) {
        debugPrint(
          '[loadAllModules] Successfully loaded module: ${fullyLoadedModule.title} (ID: ${fullyLoadedModule.id}) with ${fullyLoadedModule.lessons.length} lessons.',
        );
        for (var lesson in fullyLoadedModule.lessons) {
          debugPrint(
            '[loadAllModules]   Lesson in ${fullyLoadedModule.title}: ${lesson.title} (ID: ${lesson.id})',
          );
        }
        loadedModulesList.add(fullyLoadedModule);
      } else {
        debugPrint(
          '[loadAllModules] Failed to load module: ${moduleMetadata.id}',
        );
      }
    }

    // Sort modules by order
    loadedModulesList.sort((a, b) => a.order.compareTo(b.order));

    debugPrint(
      '[loadAllModules] Finished. Loaded ${loadedModulesList.length} modules for course: $categoryId/$courseId.',
    );
    for (var m in loadedModulesList) {
      debugPrint(
        '[loadAllModules]   Final loaded module: ${m.title}, Lessons: ${m.lessons.length}',
      );
    }

    return loadedModulesList;
  }

  /// Get a fully loaded course with all modules
  Future<Course?> getFullCourse(String categoryId, String courseId) async {
    debugPrint(
      '[getFullCourse] Getting full course with modules: $categoryId/$courseId',
    );

    // Load the course metadata
    final courseMetadata = await loadCourse(categoryId, courseId);
    if (courseMetadata == null) {
      debugPrint(
        '[getFullCourse] Failed to load course metadata: $categoryId/$courseId',
      );
      return null;
    }

    debugPrint(
      '[getFullCourse] Successfully loaded course metadata: ${courseMetadata.title}',
    );
    debugPrint(
      '[getFullCourse] Course metadata has ${courseMetadata.modules.length} module entries.',
    );
    for (var i = 0; i < courseMetadata.modules.length; i++) {
      debugPrint(
        '[getFullCourse]   Metadata module[$i]: ID=${courseMetadata.modules[i].id}, Title=${courseMetadata.modules[i].title}, Lessons=${courseMetadata.modules[i].lessons.length}',
      );
    }

    // Load all modules
    final List<Module> fullyLoadedModules = await loadAllModules(
      categoryId,
      courseId,
      courseMetadata,
    );
    debugPrint(
      '[getFullCourse] loadAllModules returned ${fullyLoadedModules.length} modules.',
    );
    for (var i = 0; i < fullyLoadedModules.length; i++) {
      debugPrint(
        '[getFullCourse]   Fully loaded module[$i]: ID=${fullyLoadedModules[i].id}, Title=${fullyLoadedModules[i].title}, Lessons=${fullyLoadedModules[i].lessons.length}',
      );
    }

    // If no modules were loaded by loadAllModules (which iterates courseMetadata.modules),
    // and courseMetadata.modules was not empty, something is wrong.
    // The directModules path is more of a fallback if course.json itself is missing module entries.
    if (fullyLoadedModules.isEmpty && courseMetadata.modules.isNotEmpty) {
      debugPrint(
        '[getFullCourse] Warning: loadAllModules returned empty list, but course metadata had module entries. This is unexpected.',
      );
    }

    if (fullyLoadedModules.isEmpty) {
      debugPrint(
        '[getFullCourse] No modules loaded via loadAllModules, trying direct module loading',
      );
      final directModules = await loadModulesDirect(categoryId, courseId);
      if (directModules.isNotEmpty) {
        debugPrint(
          '[getFullCourse] Successfully loaded ${directModules.length} modules directly',
        );
        for (var i = 0; i < directModules.length; i++) {
          debugPrint(
            '[getFullCourse]   Directly loaded module[$i]: ID=${directModules[i].id}, Title=${directModules[i].title}, Lessons=${directModules[i].lessons.length}',
          );
        }

        // Create a new course with the directly loaded modules
        final fullCourse = Course(
          id: courseMetadata.id,
          title: courseMetadata.title,
          description: courseMetadata.description,
          categoryId: courseMetadata.categoryId,
          thumbnailPath: courseMetadata.thumbnailPath,
          difficulty: courseMetadata.difficulty,
          prerequisites: courseMetadata.prerequisites,
          modules: directModules,
          isEncrypted: courseMetadata.isEncrypted,
          encryptionKey: courseMetadata.encryptionKey,
        );

        debugPrint(
          '[getFullCourse] Created full course with ${fullCourse.modules.length} directly loaded modules.',
        );
        int totalLessons = 0;
        for (var module_ in fullCourse.modules) {
          totalLessons += module_.lessons.length;
          debugPrint(
            '[getFullCourse]   Module ${module_.title} has ${module_.lessons.length} lessons',
          );
        }
        debugPrint(
          '[getFullCourse] Total lessons in directly loaded course: $totalLessons',
        );
        _courseCache['$categoryId/$courseId'] = fullCourse;
        return fullCourse;
      } else {
        debugPrint(
          '[getFullCourse] Direct module loading also yielded no modules.',
        );
        _courseCache['$categoryId/$courseId'] = courseMetadata;
        return courseMetadata;
      }
    }

    // Create a new course with the loaded modules from loadAllModules
    final fullCourse = Course(
      id: courseMetadata.id,
      title: courseMetadata.title,
      description: courseMetadata.description,
      categoryId: courseMetadata.categoryId,
      thumbnailPath: courseMetadata.thumbnailPath,
      difficulty: courseMetadata.difficulty,
      prerequisites: courseMetadata.prerequisites,
      modules: fullyLoadedModules,
      isEncrypted: courseMetadata.isEncrypted,
      encryptionKey: courseMetadata.encryptionKey,
    );

    debugPrint(
      '[getFullCourse] Created full course with ${fullCourse.modules.length} modules from loadAllModules.',
    );
    int totalLessons = 0;
    for (var module_ in fullCourse.modules) {
      totalLessons += module_.lessons.length;
      debugPrint(
        '[getFullCourse]   Module ${module_.title} has ${module_.lessons.length} lessons',
      );
    }
    debugPrint(
      '[getFullCourse] Total lessons in course from loadAllModules: $totalLessons',
    );
    _courseCache['$categoryId/$courseId'] = fullCourse;
    return fullCourse;
  }

  /// Load modules directly from the file system without relying on course metadata
  Future<List<Module>> loadModulesDirect(
    String categoryId,
    String courseId,
  ) async {
    debugPrint(
      '[loadModulesDirect] Loading modules directly for $categoryId/$courseId',
    );
    final modules = <Module>[];

    try {
      // List all files in the modules directory
      final manifestContent = await rootBundle.loadString('AssetManifest.json');
      final manifestMap = json.decode(manifestContent) as Map<String, dynamic>;

      // Find all module JSON files
      final moduleFiles =
          manifestMap.keys
              .where(
                (String key) =>
                    key.startsWith(
                      'assets/data/courses/categories/$categoryId/$courseId/modules/',
                    ) &&
                    key.endsWith('.json'),
              )
              .toList();

      debugPrint(
        '[loadModulesDirect] Found ${moduleFiles.length} module files: $moduleFiles',
      );

      // If no module files found in the manifest, try loading them by name directly
      if (moduleFiles.isEmpty) {
        debugPrint(
          '[loadModulesDirect] No module files found in manifest, trying direct loading by name',
        );
        return await loadModulesByName(categoryId, courseId);
      }

      // Load each module
      for (final filePath in moduleFiles) {
        try {
          debugPrint('[loadModulesDirect] Loading module from file: $filePath');
          final jsonString = await rootBundle.loadString(filePath);
          debugPrint(
            '[loadModulesDirect] Successfully loaded module JSON, length: ${jsonString.length}',
          );

          // Parse the JSON
          final jsonData = json.decode(jsonString);
          debugPrint(
            '[loadModulesDirect] Successfully parsed module JSON data',
          );

          // Convert to Module object
          final module = Module.fromJson(jsonData);
          debugPrint(
            '[loadModulesDirect] Successfully created Module object: ${module.title}, with ${module.lessons.length} lessons',
          );

          // Add to the list
          modules.add(module);

          // Cache the module
          final moduleId = module.id;
          final cacheKey = '$categoryId/$courseId/$moduleId';
          _moduleCache[cacheKey] = module;

          // Preload assets for this module
          await _assetManager.preloadModuleAssets(categoryId, courseId, module);
        } catch (e) {
          debugPrint(
            '[loadModulesDirect] Error loading module from file $filePath: $e',
          );
          debugPrint('[loadModulesDirect] Stack trace: ${StackTrace.current}');
        }
      }

      // Sort modules by order
      modules.sort((a, b) => a.order.compareTo(b.order));

      debugPrint(
        '[loadModulesDirect] Loaded ${modules.length} modules directly',
      );
      return modules;
    } catch (e) {
      debugPrint('[loadModulesDirect] Error loading modules directly: $e');
      debugPrint('[loadModulesDirect] Stack trace: ${StackTrace.current}');
      return [];
    }
  }

  /// Load modules by name directly
  Future<List<Module>> loadModulesByName(
    String categoryId,
    String courseId,
  ) async {
    debugPrint(
      '[loadModulesByName] Loading modules by name for $categoryId/$courseId',
    );
    final modules = <Module>[];

    // Known module IDs for Mathematical Thinking course
    List<String> moduleIds = [];

    // First try to get module IDs from course metadata
    final course = await loadCourse(categoryId, courseId);
    if (course != null && course.modules.isNotEmpty) {
      debugPrint('[loadModulesByName] Using module IDs from course metadata');
      moduleIds = course.modules.map((m) => m.id).toList();
    }
    // If no modules found in course metadata, use hardcoded lists as fallback
    else if (categoryId == 'maths' && courseId == 'mathematical_thinking') {
      debugPrint(
        '[loadModulesByName] Using hardcoded module IDs for mathematical thinking course',
      );
      moduleIds = [
        'art-of-logical-deduction',
        'number-sense-and-intuition',
        'visualizing-geometry',
        'power-of-patterns-and-relationships',
        'exploring-the-world-of-numbers',
      ];
    }
    // Special handling for Equations and Algebra course
    else if (categoryId == 'maths' && courseId == 'equations-and-algebra') {
      debugPrint(
        '[loadModulesByName] Using hardcoded module IDs for equations and algebra course',
      );
      moduleIds = [
        'language-of-variables',
        'solving-one-step-equations',
        'tackling-two-step-equations',
        'introduction-to-inequalities',
        'exploring-algebraic-relationships',
      ];
    }
    // Special handling for Scientific Thinking course
    else if (categoryId == 'science' && courseId == 'scientific_thinking') {
      debugPrint(
        '[loadModulesByName] Using hardcoded module IDs for scientific thinking course',
      );
      moduleIds = [
        'foundation-of-inquiry',
        'observing-recording-interpreting-data',
        'scientific-models-and-theories',
        'scientific-reasoning-argumentation',
        'frontiers-of-scientific-inquiry',
      ];
    }
    // Special handling for Physics Fundamentals course
    else if (categoryId == 'science' && courseId == 'physics_fundamentals') {
      debugPrint(
        '[loadModulesByName] Using hardcoded module IDs for physics fundamentals course',
      );
      moduleIds = [
        'describing-motion',
        'forces-and-newtons-laws',
        'work-energy-and-power',
        'momentum-and-collisions',
        'rotational-motion',
      ];
    }

    debugPrint(
      '[loadModulesByName] Attempting to load these modules: $moduleIds',
    );

    for (final moduleId in moduleIds) {
      try {
        final filePath =
            'assets/data/courses/categories/$categoryId/$courseId/modules/$moduleId.json';
        debugPrint(
          '[loadModulesByName] Trying to load module from file: $filePath',
        );

        try {
          final jsonString = await rootBundle.loadString(filePath);
          debugPrint(
            '[loadModulesByName] Successfully loaded module JSON, length: ${jsonString.length}',
          );

          // Parse the JSON
          final jsonData = json.decode(jsonString);
          debugPrint(
            '[loadModulesByName] Successfully parsed module JSON data',
          );

          // Convert to Module object
          final module = Module.fromJson(jsonData);
          debugPrint(
            '[loadModulesByName] Successfully created Module object: ${module.title}, with ${module.lessons.length} lessons',
          );

          // Add to the list
          modules.add(module);

          // Cache the module
          final cacheKey = '$categoryId/$courseId/$moduleId';
          _moduleCache[cacheKey] = module;

          // Preload assets for this module
          await _assetManager.preloadModuleAssets(categoryId, courseId, module);
        } catch (e) {
          debugPrint(
            '[loadModulesByName] Error loading module from file $filePath: $e',
          );
          debugPrint('[loadModulesByName] Stack trace: ${StackTrace.current}');
        }
      } catch (e) {
        debugPrint('[loadModulesByName] Error loading module $moduleId: $e');
      }
    }

    // Sort modules by order
    modules.sort((a, b) => a.order.compareTo(b.order));

    debugPrint('[loadModulesByName] Loaded ${modules.length} modules by name');
    return modules;
  }

  /// Get a list of all available courses in a category
  Future<List<Course>> getCoursesByCategory(String categoryId) async {
    try {
      // List all directories in the category directory
      final manifestContent = await rootBundle.loadString('AssetManifest.json');
      final manifestMap = json.decode(manifestContent) as Map<String, dynamic>;

      // Debug: Print all assets in the manifest
      debugPrint(
        '[getCoursesByCategory] Asset Manifest Keys for category $categoryId:',
      );

      // Print total number of keys in the manifest
      debugPrint(
        '[getCoursesByCategory] Total keys in manifest: ${manifestMap.keys.length}',
      );

      // Count course.json files in the manifest
      int courseJsonCount = 0;
      for (final key in manifestMap.keys) {
        if (key.endsWith('/course.json')) {
          courseJsonCount++;
        }
      }
      debugPrint(
        '[getCoursesByCategory] Total course.json files in manifest: $courseJsonCount',
      );

      // Print all course.json files in the manifest
      debugPrint('[getCoursesByCategory] All course.json files in manifest:');
      for (final key in manifestMap.keys) {
        if (key.endsWith('/course.json')) {
          debugPrint('[getCoursesByCategory]   - $key');
        }
      }

      // Special debug for all categories
      debugPrint(
        '[getCoursesByCategory] DETAILED DEBUG FOR CATEGORY: $categoryId',
      );

      for (final key in manifestMap.keys) {
        // More general check to see all potential course-related assets
        if (key.contains('assets/data/courses/categories/')) {
          debugPrint('[getCoursesByCategory]   Manifest Key: $key');
        }
      }

      // Find all course.json files in this category
      final courseFiles =
          manifestMap.keys
              .where(
                (String key) =>
                    key.startsWith(
                      'assets/data/courses/categories/$categoryId/',
                    ) &&
                    key.endsWith('/course.json'),
              )
              .toList();

      debugPrint(
        '[getCoursesByCategory] Found ${courseFiles.length} course files for category $categoryId: $courseFiles',
      );

      // Debug: Print all keys that contain this category path
      debugPrint(
        '[getCoursesByCategory] All keys containing this category path:',
      );
      for (final key in manifestMap.keys) {
        if (key.contains('assets/data/courses/categories/$categoryId/')) {
          debugPrint('[getCoursesByCategory]   - $key');
        }
      }

      // Create the courses list
      final courses = <Course>[];

      // If no course files found in the manifest, try direct loading for known courses
      if (courseFiles.isEmpty) {
        debugPrint(
          '[getCoursesByCategory] No course files found in manifest, trying direct loading',
        );

        // List of known course IDs for each category
        List<String> knownCourseIds = [];

        if (categoryId == 'maths') {
          knownCourseIds = [
            'mathematical_thinking',
            'equations-and-algebra',
            'functions-and-probability',
            'calculus',
          ];
        } else if (categoryId == 'science') {
          knownCourseIds = [
            'scientific_thinking',
            'physics_fundamentals',
            'chemistry_fundamentals',
            'quantum_mechanics',
          ];
        } else if (categoryId == 'computer_science') {
          knownCourseIds = [
            'computational_thinking',
            'algorithms_data_structures',
            'ai_machine_learning',
            'ethical_hacking',
          ];
        } else if (categoryId == 'puzzles') {
          knownCourseIds = ['daily_puzzle_challenge'];
        } else if (categoryId == 'curiosity_corner') {
          knownCourseIds = [
            'what_if_hypothetical_science',
            'how_its_made_tech_teardown',
          ];
        } else if (categoryId == 'coming_soon') {
          knownCourseIds = [
            'business_finance_basics',
            'music_fundamentals',
            'psychology_essentials',
          ];
        } else if (categoryId == 'reasoning') {
          knownCourseIds = ['reasoning_foundations', 'reasoning_frontiers'];
        } else if (categoryId == 'technology') {
          knownCourseIds = [
            'circuits_and_electronics_fundamentals',
            'engineering_principles',
          ];
        }

        debugPrint(
          '[getCoursesByCategory] Trying to load these known courses: $knownCourseIds',
        );

        // Try to load each known course
        for (final courseId in knownCourseIds) {
          debugPrint(
            '[getCoursesByCategory] Trying to load course: $categoryId/$courseId',
          );
          final course = await loadCourse(categoryId, courseId);
          if (course != null) {
            debugPrint(
              '[getCoursesByCategory] Successfully loaded course: ${course.title}',
            );
            courses.add(course);
          } else {
            debugPrint(
              '[getCoursesByCategory] Failed to load course: $categoryId/$courseId',
            );
          }
        }

        return courses;
      }
      for (final file in courseFiles) {
        // Extract course ID from path
        final parts = file.split('/');
        final courseId = parts[parts.length - 2];

        debugPrint(
          '[getCoursesByCategory] Loading course: $categoryId/$courseId from file: $file',
        );
        final course = await loadCourse(categoryId, courseId);
        if (course != null) {
          debugPrint(
            '[getCoursesByCategory] Successfully loaded course: ${course.title}',
          );
          courses.add(course);
        } else {
          debugPrint(
            '[getCoursesByCategory] Failed to load course: $categoryId/$courseId',
          );
        }
      }

      return courses;
    } catch (e) {
      debugPrint(
        '[getCoursesByCategory] Error getting courses by category: $e',
      );
      return [];
    }
  }

  /// Get a lesson by ID from a module
  LessonDefinition? getLessonById(Module module, String lessonId) {
    try {
      return module.lessons.firstWhere((lesson) => lesson.id == lessonId);
    } catch (e) {
      return null;
    }
  }

  /// Get the next lesson in a module
  LessonDefinition? getNextLesson(Module module, String currentLessonId) {
    try {
      final currentIndex = module.lessons.indexWhere(
        (lesson) => lesson.id == currentLessonId,
      );
      if (currentIndex < 0 || currentIndex >= module.lessons.length - 1) {
        return null;
      }
      return module.lessons[currentIndex + 1];
    } catch (e) {
      return null;
    }
  }

  /// Get the previous lesson in a module
  LessonDefinition? getPreviousLesson(Module module, String currentLessonId) {
    try {
      final currentIndex = module.lessons.indexWhere(
        (lesson) => lesson.id == currentLessonId,
      );
      if (currentIndex <= 0) {
        return null;
      }
      return module.lessons[currentIndex - 1];
    } catch (e) {
      return null;
    }
  }

  /// Get the asset path for a content block
  Future<String> getAssetPath(
    String categoryId,
    String courseId,
    String assetPath,
    String? remoteUrl,
  ) {
    return _assetManager.getAssetPath(
      categoryId,
      courseId,
      assetPath,
      remoteUrl,
    );
  }

  /// Clear the course and module cache
  void clearCache() {
    _courseCache.clear();
    _moduleCache.clear();
    _assetManager.clearCache();
  }
}
