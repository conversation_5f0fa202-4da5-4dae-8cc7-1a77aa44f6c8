import 'package:flutter/material.dart';
import 'package:flutter/services.dart'; // For HapticFeedback
import 'package:flutter_svg/flutter_svg.dart';
import '../../../../models/course_models.dart';

class MultipleChoiceIconElementWidget extends StatefulWidget {
  final MultipleChoiceIconElement mcqIconElement;
  final VoidCallback? onNextAction; // For advancing slide or lesson
  final bool isLastSlideInLesson;

  const MultipleChoiceIconElementWidget({
    super.key,
    required this.mcqIconElement,
    this.onNextAction,
    this.isLastSlideInLesson = false,
  });

  @override
  State<MultipleChoiceIconElementWidget> createState() => _MultipleChoiceIconElementWidgetState();
}

class _MultipleChoiceIconElementWidgetState extends State<MultipleChoiceIconElementWidget> {
  String? _selectedOptionId;
  bool _isAnswered = false;
  bool _isCorrect = false;

  void _handleOptionSelected(MultipleChoiceIconOption option) {
    if (_isAnswered) return; // Prevent changing answer after submission

    HapticFeedback.lightImpact();
    setState(() {
      _selectedOptionId = option.id;
      _isAnswered = true;
      _isCorrect = option.is_correct;
    });

    String feedbackMessage = _isCorrect 
        ? widget.mcqIconElement.feedback_correct 
        : "Let's try that again.";

    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(feedbackMessage),
        backgroundColor: _isCorrect ? Colors.green : Colors.redAccent,
        duration: const Duration(seconds: 2),
      ),
    );
    
    // If correct and action is auto-next, proceed after a short delay
    if (_isCorrect && widget.mcqIconElement.action_on_correct == 'next_screen_auto') {
      Future.delayed(const Duration(milliseconds: 1500), () {
        if (widget.onNextAction != null && mounted) {
          widget.onNextAction!();
        }
      });
    }
  }

  Color _getOptionBackgroundColor(MultipleChoiceIconOption option) {
    if (!_isAnswered) {
      return Colors.white;
    }
    
    if (_selectedOptionId == option.id) {
      return option.is_correct ? Colors.green.shade50 : Colors.red.shade50;
    }
    
    return Colors.white;
  }

  Color _getOptionBorderColor(MultipleChoiceIconOption option) {
    if (!_isAnswered) {
      return Colors.grey.shade300;
    }
    
    if (_selectedOptionId == option.id) {
      return option.is_correct ? Colors.green : Colors.red;
    }
    
    return Colors.grey.shade300;
  }

  @override
  Widget build(BuildContext context) {
    final options = widget.mcqIconElement.options;
    
    // Calculate grid layout based on number of options
    int crossAxisCount = options.length <= 3 ? options.length : (options.length <= 6 ? 3 : 4);
    if (crossAxisCount < 2) crossAxisCount = 2; // Minimum 2 columns
    
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8.0, vertical: 16.0),
      margin: const EdgeInsets.symmetric(vertical: 12.0, horizontal: 4.0),
      decoration: BoxDecoration(
        color: Colors.purple.shade50, // Different background color
        borderRadius: BorderRadius.circular(16.0),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.stretch,
        children: [
          // Grid of icon options
          GridView.builder(
            shrinkWrap: true,
            physics: const NeverScrollableScrollPhysics(),
            gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
              crossAxisCount: crossAxisCount,
              crossAxisSpacing: 12.0,
              mainAxisSpacing: 12.0,
              childAspectRatio: 1.0,
            ),
            itemCount: options.length,
            itemBuilder: (context, index) {
              final option = options[index];
              return _buildIconOption(option);
            },
          ),
          
          // Continue button (shown only when needed)
          if (_isAnswered && _isCorrect && widget.mcqIconElement.action_on_correct != 'next_screen_auto')
            Padding(
              padding: const EdgeInsets.only(top: 24.0, left: 8.0, right: 8.0),
              child: SizedBox(
                width: double.infinity,
                height: 56,
                child: ElevatedButton.icon(
                  icon: Icon(
                    widget.isLastSlideInLesson
                        ? Icons.check_circle_outline_rounded
                        : Icons.arrow_forward_ios_rounded,
                    size: 22,
                  ),
                  label: const Text("Check"),
                  onPressed: widget.onNextAction,
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Theme.of(context).primaryColor,
                    foregroundColor: Colors.white,
                    padding: const EdgeInsets.symmetric(vertical: 14.0),
                    textStyle: const TextStyle(
                      fontSize: 17.0,
                      fontWeight: FontWeight.bold,
                    ),
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(12.0),
                    ),
                    elevation: 3,
                  ),
                ),
              ),
            ),
          
          // Try again button (shown when incorrect)
          if (_isAnswered && !_isCorrect)
            Padding(
              padding: const EdgeInsets.only(top: 24.0, left: 8.0, right: 8.0),
              child: SizedBox(
                width: double.infinity,
                height: 56,
                child: ElevatedButton.icon(
                  icon: const Icon(Icons.refresh, size: 22),
                  label: const Text("Try Again"),
                  onPressed: () {
                    setState(() {
                      _isAnswered = false;
                      _selectedOptionId = null;
                    });
                  },
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Colors.grey.shade500,
                    foregroundColor: Colors.white,
                    padding: const EdgeInsets.symmetric(vertical: 14.0),
                    textStyle: const TextStyle(
                      fontSize: 17.0,
                      fontWeight: FontWeight.bold,
                    ),
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(12.0),
                    ),
                    elevation: 1,
                  ),
                ),
              ),
            ),
        ],
      ),
    );
  }

  Widget _buildIconOption(MultipleChoiceIconOption option) {
    bool isSvg = option.icon_asset.toLowerCase().endsWith('.svg');
    
    return Material(
      color: _getOptionBackgroundColor(option),
      borderRadius: BorderRadius.circular(12.0),
      elevation: _isAnswered && _selectedOptionId == option.id ? 3.0 : 1.0,
      child: InkWell(
        onTap: () => _handleOptionSelected(option),
        borderRadius: BorderRadius.circular(12.0),
        child: Container(
          padding: const EdgeInsets.all(16.0),
          decoration: BoxDecoration(
            border: Border.all(
              color: _getOptionBorderColor(option),
              width: _isAnswered && _selectedOptionId == option.id ? 2.0 : 1.0,
            ),
            borderRadius: BorderRadius.circular(12.0),
          ),
          child: Center(
            child: _buildIconWidget(option.icon_asset, isSvg),
          ),
        ),
      ),
    );
  }

  Widget _buildIconWidget(String iconAsset, bool isSvg) {
    try {
      if (isSvg) {
        return SvgPicture.asset(
          iconAsset,
          height: 48,
          width: 48,
          placeholderBuilder: (BuildContext context) => const Icon(
            Icons.image_not_supported,
            size: 48,
            color: Colors.grey,
          ),
        );
      } else {
        return Image.asset(
          iconAsset,
          height: 48,
          width: 48,
          errorBuilder: (context, error, stackTrace) => const Icon(
            Icons.broken_image,
            size: 48,
            color: Colors.grey,
          ),
        );
      }
    } catch (e) {
      // If the asset can't be loaded, show a fallback icon
      return const Icon(
        Icons.image_not_supported,
        size: 48,
        color: Colors.grey,
      );
    }
  }
}
