import 'package:flutter/material.dart';
import 'dart:math' as math;

/// A widget that visualizes the substitution method for solving systems of linear equations
class InteractiveSubstitutionMethodVisualizerWidget extends StatefulWidget {
  /// The data for this widget
  final Map<String, dynamic> data;

  /// Optional callback when the widget state changes
  final Function(bool isCompleted)? onStateChanged;

  const InteractiveSubstitutionMethodVisualizerWidget({
    Key? key,
    required this.data,
    this.onStateChanged,
  }) : super(key: key);

  /// Factory constructor to create the widget from JSON data
  factory InteractiveSubstitutionMethodVisualizerWidget.fromData(Map<String, dynamic> data) {
    return InteractiveSubstitutionMethodVisualizerWidget(
      data: data,
    );
  }

  @override
  State<InteractiveSubstitutionMethodVisualizerWidget> createState() => _InteractiveSubstitutionMethodVisualizerWidgetState();
}

class _InteractiveSubstitutionMethodVisualizerWidgetState extends State<InteractiveSubstitutionMethodVisualizerWidget> {
  // State variables
  bool _isCompleted = false;
  
  // System of equations
  List<Equation> _equations = [];
  
  // Solution
  double? _solutionX;
  double? _solutionY;
  String _solutionMessage = '';
  bool _hasSolution = false;
  
  // Substitution steps
  List<SubstitutionStep> _steps = [];
  int _currentStepIndex = 0;
  bool _showingSolution = false;
  
  // Controllers for equation inputs
  final List<TextEditingController> _aControllers = [];
  final List<TextEditingController> _bControllers = [];
  final List<TextEditingController> _cControllers = [];
  
  // Colors
  late Color _primaryColor;
  late Color _secondaryColor;
  late Color _accentColor;
  late Color _textColor;
  late Color _highlightColor;
  
  // Animation controller
  bool _isAnimating = false;
  
  @override
  void initState() {
    super.initState();
    _initializeFromData();
  }
  
  @override
  void dispose() {
    // Dispose all controllers
    for (var controller in _aControllers) {
      controller.dispose();
    }
    for (var controller in _bControllers) {
      controller.dispose();
    }
    for (var controller in _cControllers) {
      controller.dispose();
    }
    super.dispose();
  }
  
  void _initializeFromData() {
    // Initialize colors
    _primaryColor = _parseColor(widget.data['primaryColor'] ?? '#2196F3');
    _secondaryColor = _parseColor(widget.data['secondaryColor'] ?? '#FF9800');
    _accentColor = _parseColor(widget.data['accentColor'] ?? '#4CAF50');
    _textColor = _parseColor(widget.data['textColor'] ?? '#212121');
    _highlightColor = _parseColor(widget.data['highlightColor'] ?? '#FFEB3B');
    
    // Initialize equations
    final initialEquations = widget.data['initialEquations'] as List<dynamic>? ?? [];
    
    if (initialEquations.isNotEmpty) {
      for (var eqData in initialEquations) {
        final a = (eqData['a'] as num).toDouble();
        final b = (eqData['b'] as num).toDouble();
        final c = (eqData['c'] as num).toDouble();
        
        _equations.add(Equation(a: a, b: b, c: c));
        
        // Create controllers for this equation
        _aControllers.add(TextEditingController(text: a.toString()));
        _bControllers.add(TextEditingController(text: b.toString()));
        _cControllers.add(TextEditingController(text: c.toString()));
      }
    } else {
      // Create default equations if none provided
      _equations = [
        Equation(a: 3, b: 2, c: 7),
        Equation(a: 1, b: -1, c: 0),
      ];
      
      // Create controllers for default equations
      _aControllers.add(TextEditingController(text: '3'));
      _bControllers.add(TextEditingController(text: '2'));
      _cControllers.add(TextEditingController(text: '7'));
      
      _aControllers.add(TextEditingController(text: '1'));
      _bControllers.add(TextEditingController(text: '-1'));
      _cControllers.add(TextEditingController(text: '0'));
    }
  }
  
  Color _parseColor(String colorString) {
    if (colorString.startsWith('#')) {
      return Color(int.parse('FF${colorString.substring(1)}', radix: 16));
    }
    return Colors.black;
  }
  
  void _updateEquationFromControllers(int index) {
    try {
      final a = double.parse(_aControllers[index].text);
      final b = double.parse(_bControllers[index].text);
      final c = double.parse(_cControllers[index].text);
      
      setState(() {
        _equations[index] = Equation(a: a, b: b, c: c);
        _resetSolution();
      });
    } catch (e) {
      // Handle parsing errors
      print('Error parsing equation values: $e');
    }
  }
  
  void _resetSolution() {
    setState(() {
      _solutionX = null;
      _solutionY = null;
      _solutionMessage = '';
      _hasSolution = false;
      _steps = [];
      _currentStepIndex = 0;
      _showingSolution = false;
      _isCompleted = false;
      _isAnimating = false;
    });
    
    // Notify parent of completion status
    widget.onStateChanged?.call(_isCompleted);
  }
  
  void _solveSystem() {
    if (_equations.length < 2) {
      setState(() {
        _solutionMessage = 'Need at least 2 equations to solve a system.';
        _hasSolution = false;
      });
      return;
    }
    
    setState(() {
      _steps = [];
      _currentStepIndex = 0;
      _showingSolution = true;
      _isAnimating = false;
    });
    
    _solveBySubstitution();
    
    // Mark as completed if we have a solution
    setState(() {
      _isCompleted = _hasSolution;
    });
    
    // Notify parent of completion status
    widget.onStateChanged?.call(_isCompleted);
  }
  
  void _solveBySubstitution() {
    // Get the two equations
    final eq1 = _equations[0];
    final eq2 = _equations[1];
    
    // Step 1: Initial equations
    _steps.add(SubstitutionStep(
      description: 'Initial system of equations',
      equation1: eq1,
      equation2: eq2,
      highlightedTerms: [],
    ));
    
    // Step 2: Choose an equation and solve for one variable
    // For simplicity, we'll always solve for y from the first equation
    _steps.add(SubstitutionStep(
      description: 'Step 1: Solve for y in terms of x from the first equation',
      equation1: eq1,
      equation2: eq2,
      highlightedTerms: ['y1'],
    ));
    
    // Check if we can solve for y from the first equation
    if (eq1.b == 0) {
      setState(() {
        _solutionMessage = 'Cannot solve using substitution method with these equations (b₁ = 0).';
        _hasSolution = false;
      });
      return;
    }
    
    // Step 3: Rearrange the first equation to isolate y
    final isolatedY = Equation(
      a: -eq1.a / eq1.b,
      b: 0,
      c: eq1.c / eq1.b,
    );
    
    _steps.add(SubstitutionStep(
      description: 'Step 2: Rearrange the first equation to isolate y\n'
                  '${eq1.b}y = ${eq1.c} - ${eq1.a}x\n'
                  'y = (${eq1.c} - ${eq1.a}x) / ${eq1.b}\n'
                  'y = ${eq1.c / eq1.b} + (${-eq1.a / eq1.b})x',
      equation1: eq1,
      isolatedVariable: 'y',
      isolatedEquation: isolatedY,
      highlightedTerms: ['all1'],
    ));
    
    // Step 4: Substitute the expression for y into the second equation
    _steps.add(SubstitutionStep(
      description: 'Step 3: Substitute the expression for y into the second equation\n'
                  '${eq2.a}x + ${eq2.b}(${isolatedY.c} + ${isolatedY.a}x) = ${eq2.c}',
      equation2: eq2,
      isolatedVariable: 'y',
      isolatedEquation: isolatedY,
      substitutionEquation: eq2,
      highlightedTerms: ['y2'],
    ));
    
    // Step 5: Expand the substituted equation
    final expandedA = eq2.a + eq2.b * isolatedY.a;
    final expandedC = eq2.c - eq2.b * isolatedY.c;
    
    final expandedEquation = Equation(
      a: expandedA,
      b: 0,
      c: expandedC,
    );
    
    _steps.add(SubstitutionStep(
      description: 'Step 4: Expand the substituted equation\n'
                  '${eq2.a}x + ${eq2.b}(${isolatedY.c} + ${isolatedY.a}x) = ${eq2.c}\n'
                  '${eq2.a}x + ${eq2.b * isolatedY.c} + ${eq2.b * isolatedY.a}x = ${eq2.c}\n'
                  '${eq2.a}x + ${eq2.b * isolatedY.a}x = ${eq2.c} - ${eq2.b * isolatedY.c}\n'
                  '${expandedA}x = ${expandedC}',
      expandedEquation: expandedEquation,
      highlightedTerms: ['allE'],
    ));
    
    // Step 6: Solve for x
    if (expandedA == 0) {
      if (expandedC == 0) {
        setState(() {
          _solutionMessage = 'The system has infinitely many solutions.';
          _hasSolution = false;
        });
      } else {
        setState(() {
          _solutionMessage = 'The system has no solution.';
          _hasSolution = false;
        });
      }
      return;
    }
    
    final x = expandedC / expandedA;
    
    _steps.add(SubstitutionStep(
      description: 'Step 5: Solve for x\n'
                  '${expandedA}x = ${expandedC}\n'
                  'x = ${expandedC} / ${expandedA}\n'
                  'x = $x',
      expandedEquation: expandedEquation,
      solutionX: x,
      highlightedTerms: ['allE'],
    ));
    
    // Step 7: Substitute x back to find y
    final y = isolatedY.c + isolatedY.a * x;
    
    _steps.add(SubstitutionStep(
      description: 'Step 6: Substitute x = $x back into the expression for y\n'
                  'y = ${isolatedY.c} + (${isolatedY.a})($x)\n'
                  'y = ${isolatedY.c} + ${isolatedY.a * x}\n'
                  'y = $y',
      isolatedEquation: isolatedY,
      solutionX: x,
      solutionY: y,
      highlightedTerms: ['allI'],
    ));
    
    // Set the solution
    setState(() {
      _solutionX = x;
      _solutionY = y;
      _solutionMessage = 'Solution: x = $x, y = $y';
      _hasSolution = true;
    });
  }
  
  void _nextStep() {
    if (_currentStepIndex < _steps.length - 1) {
      setState(() {
        _currentStepIndex++;
      });
    }
  }
  
  void _previousStep() {
    if (_currentStepIndex > 0) {
      setState(() {
        _currentStepIndex--;
      });
    }
  }
  
  void _startAnimation() {
    setState(() {
      _currentStepIndex = 0;
      _isAnimating = true;
    });
    
    _animateNextStep();
  }
  
  void _animateNextStep() {
    if (!_isAnimating || _currentStepIndex >= _steps.length - 1) {
      setState(() {
        _isAnimating = false;
      });
      return;
    }
    
    Future.delayed(const Duration(seconds: 2), () {
      if (mounted && _isAnimating) {
        setState(() {
          _currentStepIndex++;
        });
        _animateNextStep();
      }
    });
  }
  
  void _stopAnimation() {
    setState(() {
      _isAnimating = false;
    });
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withOpacity(0.2),
            spreadRadius: 1,
            blurRadius: 3,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Title
          Text(
            widget.data['title'] ?? 'Substitution Method Visualizer',
            style: const TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
            ),
          ),
          
          const SizedBox(height: 8),
          
          // Description
          if (widget.data['description'] != null)
            Text(
              widget.data['description'],
              style: const TextStyle(
                fontSize: 14,
                color: Colors.grey,
              ),
            ),
          
          const SizedBox(height: 16),
          
          // Equation inputs
          ..._buildEquationInputs(),
          
          const SizedBox(height: 16),
          
          // Solve button
          Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              ElevatedButton.icon(
                icon: const Icon(Icons.calculate),
                label: const Text('Solve System'),
                style: ElevatedButton.styleFrom(
                  backgroundColor: _primaryColor,
                  foregroundColor: Colors.white,
                  padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
                ),
                onPressed: _solveSystem,
              ),
              if (_steps.isNotEmpty) ...[
                const SizedBox(width: 16),
                ElevatedButton.icon(
                  icon: Icon(_isAnimating ? Icons.stop : Icons.play_arrow),
                  label: Text(_isAnimating ? 'Stop Animation' : 'Animate Solution'),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: _secondaryColor,
                    foregroundColor: Colors.white,
                    padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
                  ),
                  onPressed: _isAnimating ? _stopAnimation : _startAnimation,
                ),
              ],
            ],
          ),
          
          const SizedBox(height: 24),
          
          // Solution visualization
          if (_showingSolution && _steps.isNotEmpty) ...[
            const Divider(),
            
            // Step navigation
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                IconButton(
                  icon: const Icon(Icons.arrow_back),
                  onPressed: _currentStepIndex > 0 ? _previousStep : null,
                  tooltip: 'Previous Step',
                ),
                Text(
                  'Step ${_currentStepIndex + 1} of ${_steps.length}',
                  style: const TextStyle(fontWeight: FontWeight.bold),
                ),
                IconButton(
                  icon: const Icon(Icons.arrow_forward),
                  onPressed: _currentStepIndex < _steps.length - 1 ? _nextStep : null,
                  tooltip: 'Next Step',
                ),
              ],
            ),
            
            // Current step description
            Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: Colors.grey[100],
                borderRadius: BorderRadius.circular(8),
              ),
              child: Text(
                _steps[_currentStepIndex].description,
                style: const TextStyle(fontSize: 14),
              ),
            ),
            
            const SizedBox(height: 16),
            
            // Visualization of the current step
            _buildStepVisualization(_steps[_currentStepIndex]),
            
            const SizedBox(height: 16),
            
            // Final solution
            if (_hasSolution && _currentStepIndex == _steps.length - 1)
              Container(
                padding: const EdgeInsets.all(16),
                decoration: BoxDecoration(
                  color: _accentColor.withOpacity(0.1),
                  borderRadius: BorderRadius.circular(8),
                  border: Border.all(color: _accentColor),
                ),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Solution:',
                      style: TextStyle(
                        fontWeight: FontWeight.bold,
                        color: _accentColor,
                      ),
                    ),
                    const SizedBox(height: 8),
                    Text('x = ${_solutionX?.toStringAsFixed(2)}'),
                    Text('y = ${_solutionY?.toStringAsFixed(2)}'),
                  ],
                ),
              )
            else if (!_hasSolution && _solutionMessage.isNotEmpty)
              Container(
                padding: const EdgeInsets.all(16),
                decoration: BoxDecoration(
                  color: Colors.red[50],
                  borderRadius: BorderRadius.circular(8),
                  border: Border.all(color: Colors.red),
                ),
                child: Text(
                  _solutionMessage,
                  style: const TextStyle(color: Colors.red),
                ),
              ),
          ],
          
          // Widget name tag
          if (widget.data['showNameTag'] ?? true)
            Padding(
              padding: const EdgeInsets.only(top: 8),
              child: Text(
                'InteractiveSubstitutionMethodVisualizer',
                style: TextStyle(
                  color: Colors.grey[600],
                  fontSize: 12,
                  fontStyle: FontStyle.italic,
                ),
              ),
            ),
        ],
      ),
    );
  }
  
  List<Widget> _buildEquationInputs() {
    final widgets = <Widget>[];
    
    for (var i = 0; i < _equations.length; i++) {
      widgets.add(
        Padding(
          padding: const EdgeInsets.only(bottom: 16),
          child: Row(
            children: [
              Text('Equation ${i+1}:', style: const TextStyle(fontWeight: FontWeight.bold)),
              const SizedBox(width: 8),
              Expanded(
                child: TextField(
                  controller: _aControllers[i],
                  keyboardType: const TextInputType.numberWithOptions(decimal: true, signed: true),
                  decoration: const InputDecoration(
                    labelText: 'a',
                    border: OutlineInputBorder(),
                  ),
                  onChanged: (_) => _updateEquationFromControllers(i),
                ),
              ),
              const SizedBox(width: 8),
              const Text('x +'),
              const SizedBox(width: 8),
              Expanded(
                child: TextField(
                  controller: _bControllers[i],
                  keyboardType: const TextInputType.numberWithOptions(decimal: true, signed: true),
                  decoration: const InputDecoration(
                    labelText: 'b',
                    border: OutlineInputBorder(),
                  ),
                  onChanged: (_) => _updateEquationFromControllers(i),
                ),
              ),
              const SizedBox(width: 8),
              const Text('y ='),
              const SizedBox(width: 8),
              Expanded(
                child: TextField(
                  controller: _cControllers[i],
                  keyboardType: const TextInputType.numberWithOptions(decimal: true, signed: true),
                  decoration: const InputDecoration(
                    labelText: 'c',
                    border: OutlineInputBorder(),
                  ),
                  onChanged: (_) => _updateEquationFromControllers(i),
                ),
              ),
            ],
          ),
        ),
      );
    }
    
    return widgets;
  }
  
  Widget _buildStepVisualization(SubstitutionStep step) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // First equation
        if (step.equation1 != null)
          _buildEquationDisplay(
            equation: step.equation1!,
            index: 1,
            highlightedTerms: step.highlightedTerms,
          ),
          
        // Second equation
        if (step.equation2 != null) ...[
          const SizedBox(height: 8),
          _buildEquationDisplay(
            equation: step.equation2!,
            index: 2,
            highlightedTerms: step.highlightedTerms,
          ),
        ],
        
        // Isolated variable equation
        if (step.isolatedVariable != null && step.isolatedEquation != null) ...[
          const SizedBox(height: 16),
          Container(
            padding: const EdgeInsets.all(8),
            decoration: BoxDecoration(
              color: _secondaryColor.withOpacity(0.1),
              borderRadius: BorderRadius.circular(8),
              border: Border.all(color: _secondaryColor),
            ),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Text(
                  '${step.isolatedVariable} = ',
                  style: const TextStyle(
                    fontWeight: FontWeight.bold,
                    fontSize: 16,
                  ),
                ),
                Container(
                  padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                  decoration: BoxDecoration(
                    color: step.highlightedTerms.contains('allI')
                        ? _highlightColor.withOpacity(0.3)
                        : Colors.transparent,
                    borderRadius: BorderRadius.circular(4),
                  ),
                  child: Text(
                    '${step.isolatedEquation!.c} + (${step.isolatedEquation!.a})x',
                    style: const TextStyle(fontSize: 16),
                  ),
                ),
              ],
            ),
          ),
        ],
        
        // Expanded equation
        if (step.expandedEquation != null) ...[
          const SizedBox(height: 16),
          Container(
            padding: const EdgeInsets.all(8),
            decoration: BoxDecoration(
              color: _accentColor.withOpacity(0.1),
              borderRadius: BorderRadius.circular(8),
              border: Border.all(color: _accentColor),
            ),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Container(
                  padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                  decoration: BoxDecoration(
                    color: step.highlightedTerms.contains('allE')
                        ? _highlightColor.withOpacity(0.3)
                        : Colors.transparent,
                    borderRadius: BorderRadius.circular(4),
                  ),
                  child: Text(
                    '${step.expandedEquation!.a}x = ${step.expandedEquation!.c}',
                    style: const TextStyle(fontSize: 16),
                  ),
                ),
              ],
            ),
          ),
        ],
        
        // Solution display
        if (step.solutionX != null) ...[
          const SizedBox(height: 16),
          Text(
            'x = ${step.solutionX!.toStringAsFixed(2)}',
            style: const TextStyle(
              fontWeight: FontWeight.bold,
              fontSize: 16,
            ),
          ),
        ],
        
        if (step.solutionY != null) ...[
          const SizedBox(height: 8),
          Text(
            'y = ${step.solutionY!.toStringAsFixed(2)}',
            style: const TextStyle(
              fontWeight: FontWeight.bold,
              fontSize: 16,
            ),
          ),
        ],
      ],
    );
  }
  
  Widget _buildEquationDisplay({
    required Equation equation,
    required int index,
    required List<String> highlightedTerms,
  }) {
    final indexStr = index.toString();
    
    return Row(
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        // x term
        Container(
          padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
          decoration: BoxDecoration(
            color: highlightedTerms.contains('x$indexStr') || highlightedTerms.contains('all$indexStr')
                ? _highlightColor.withOpacity(0.3)
                : Colors.transparent,
            borderRadius: BorderRadius.circular(4),
          ),
          child: Text(
            '${equation.a}x',
            style: const TextStyle(fontSize: 16),
          ),
        ),
        
        // + sign
        const Text(' + ', style: TextStyle(fontSize: 16)),
        
        // y term
        Container(
          padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
          decoration: BoxDecoration(
            color: highlightedTerms.contains('y$indexStr') || highlightedTerms.contains('all$indexStr')
                ? _highlightColor.withOpacity(0.3)
                : Colors.transparent,
            borderRadius: BorderRadius.circular(4),
          ),
          child: Text(
            '${equation.b}y',
            style: const TextStyle(fontSize: 16),
          ),
        ),
        
        // = sign
        const Text(' = ', style: TextStyle(fontSize: 16)),
        
        // constant term
        Container(
          padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
          decoration: BoxDecoration(
            color: highlightedTerms.contains('c$indexStr') || highlightedTerms.contains('all$indexStr')
                ? _highlightColor.withOpacity(0.3)
                : Colors.transparent,
            borderRadius: BorderRadius.circular(4),
          ),
          child: Text(
            '${equation.c}',
            style: const TextStyle(fontSize: 16),
          ),
        ),
      ],
    );
  }
}

class Equation {
  final double a; // coefficient of x
  final double b; // coefficient of y
  final double c; // constant term
  
  Equation({
    required this.a,
    required this.b,
    required this.c,
  });
  
  @override
  String toString() {
    return '${a}x + ${b}y = $c';
  }
}

class SubstitutionStep {
  final String description;
  final Equation? equation1;
  final Equation? equation2;
  final String? isolatedVariable;
  final Equation? isolatedEquation;
  final Equation? substitutionEquation;
  final Equation? expandedEquation;
  final List<String> highlightedTerms;
  final double? solutionX;
  final double? solutionY;
  
  SubstitutionStep({
    required this.description,
    this.equation1,
    this.equation2,
    this.isolatedVariable,
    this.isolatedEquation,
    this.substitutionEquation,
    this.expandedEquation,
    required this.highlightedTerms,
    this.solutionX,
    this.solutionY,
  });
}
