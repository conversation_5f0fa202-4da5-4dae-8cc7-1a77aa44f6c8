import 'package:flutter/material.dart';
import 'dart:math' as math;

/// A widget that allows students to build and explore mathematical relationships.
class InteractiveRelationshipBuilderWidget extends StatefulWidget {
  final Map<String, dynamic> data;
  final Function(bool)? onStateChanged;
  final Color primaryColor;
  final Color secondaryColor;
  final Color backgroundColor;
  final Color textColor;
  final Color successColor;
  final Color errorColor;

  const InteractiveRelationshipBuilderWidget({
    super.key,
    this.data = const {},
    this.onStateChanged,
    this.primaryColor = Colors.blue,
    this.secondaryColor = Colors.orange,
    this.backgroundColor = Colors.white,
    this.textColor = Colors.black87,
    this.successColor = Colors.green,
    this.errorColor = Colors.red,
  });

  @override
  State<InteractiveRelationshipBuilderWidget> createState() =>
      _InteractiveRelationshipBuilderWidgetState();
}

class _InteractiveRelationshipBuilderWidgetState
    extends State<InteractiveRelationshipBuilderWidget> with SingleTickerProviderStateMixin {
  // State variables
  bool _isCompleted = false;
  int _currentRelationshipIndex = 0;
  List<RelationshipData> _relationships = [];
  late RelationshipData _currentRelationship;
  
  // Relationship building variables
  String _selectedRelationType = 'linear';
  double _slope = 1.0;
  double _yIntercept = 0.0;
  String _relationshipEquation = 'y = x';
  
  // Interactive variables
  String _userEquation = '';
  TextEditingController _equationController = TextEditingController();
  String? _feedbackMessage;
  bool _isCorrect = false;
  
  // Animation controller for feedback
  late AnimationController _animationController;
  late Animation<double> _animation;
  
  // Table data
  List<List<double>> _tableData = [];
  
  @override
  void initState() {
    super.initState();
    _initializeRelationships();
    _currentRelationship = _relationships[_currentRelationshipIndex];
    _updateRelationshipFromData(_currentRelationship);
    _generateTableData();
    
    // Initialize animation controller
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 300),
      vsync: this,
    );
    
    _animation = CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeInOut,
    );
  }
  
  @override
  void dispose() {
    _equationController.dispose();
    _animationController.dispose();
    super.dispose();
  }

  void _initializeRelationships() {
    // Check if relationships are provided in the data
    if (widget.data.containsKey('relationships') && 
        widget.data['relationships'] is List &&
        widget.data['relationships'].isNotEmpty) {
      
      final relationshipsData = widget.data['relationships'] as List;
      for (final relationshipData in relationshipsData) {
        if (relationshipData is Map<String, dynamic>) {
          final relationship = RelationshipData.fromJson(relationshipData);
          _relationships.add(relationship);
        }
      }
    }

    // If no relationships were provided, create default ones
    if (_relationships.isEmpty) {
      _relationships = [
        RelationshipData(
          description: "Build a linear relationship where y increases by 2 for every increase of 1 in x, and y is 3 when x is 0.",
          type: 'linear',
          slope: 2.0,
          yIntercept: 3.0,
          equation: 'y = 2x + 3',
          hint: "Use the form y = mx + b, where m is the slope and b is the y-intercept.",
        ),
        RelationshipData(
          description: "Build a linear relationship where y decreases by 1.5 for every increase of 1 in x, and y is -2 when x is 0.",
          type: 'linear',
          slope: -1.5,
          yIntercept: -2.0,
          equation: 'y = -1.5x - 2',
          hint: "Remember that a negative slope means y decreases as x increases.",
        ),
        RelationshipData(
          description: "Build a linear relationship that passes through the points (0, 4) and (2, 0).",
          type: 'linear',
          slope: -2.0,
          yIntercept: 4.0,
          equation: 'y = -2x + 4',
          hint: "Find the slope using (y₂ - y₁)/(x₂ - x₁), then find the y-intercept.",
        ),
        RelationshipData(
          description: "Build a proportional relationship where y is always 3 times x.",
          type: 'proportional',
          slope: 3.0,
          yIntercept: 0.0,
          equation: 'y = 3x',
          hint: "A proportional relationship passes through the origin (0,0).",
        ),
      ];
    }
  }
  
  void _updateRelationshipFromData(RelationshipData relationship) {
    setState(() {
      _selectedRelationType = relationship.type;
      _slope = relationship.slope;
      _yIntercept = relationship.yIntercept;
      _updateEquation();
    });
  }
  
  void _updateEquation() {
    String slopeStr = _slope == 1.0 ? '' : _slope == -1.0 ? '-' : _slope.toStringAsFixed(1);
    if (slopeStr.endsWith('.0')) {
      slopeStr = slopeStr.substring(0, slopeStr.length - 2);
    }
    
    String yInterceptStr = _yIntercept.toStringAsFixed(1);
    if (yInterceptStr.endsWith('.0')) {
      yInterceptStr = yInterceptStr.substring(0, yInterceptStr.length - 2);
    }
    
    if (_selectedRelationType == 'proportional') {
      _relationshipEquation = 'y = ${slopeStr}x';
    } else {
      // Linear relationship
      if (_yIntercept == 0) {
        _relationshipEquation = 'y = ${slopeStr}x';
      } else if (_yIntercept > 0) {
        _relationshipEquation = 'y = ${slopeStr}x + $yInterceptStr';
      } else {
        _relationshipEquation = 'y = ${slopeStr}x - ${yInterceptStr.substring(1)}';
      }
    }
    
    _generateTableData();
  }
  
  void _generateTableData() {
    _tableData = [];
    
    // Generate 5 points for the table
    for (int i = -2; i <= 2; i++) {
      double x = i.toDouble();
      double y = _slope * x + _yIntercept;
      _tableData.add([x, y]);
    }
  }
  
  void _updateSlope(double value) {
    setState(() {
      _slope = value;
      _updateEquation();
    });
  }
  
  void _updateYIntercept(double value) {
    setState(() {
      _yIntercept = value;
      if (_selectedRelationType == 'proportional') {
        _yIntercept = 0; // Force y-intercept to be 0 for proportional relationships
      }
      _updateEquation();
    });
  }
  
  void _setRelationType(String type) {
    setState(() {
      _selectedRelationType = type;
      if (type == 'proportional') {
        _yIntercept = 0; // Force y-intercept to be 0 for proportional relationships
      }
      _updateEquation();
    });
  }
  
  void _checkRelationship() {
    // Check if the user's relationship matches the target
    bool isCorrect = false;
    
    if (_selectedRelationType == _currentRelationship.type) {
      // For proportional relationships, only check the slope
      if (_selectedRelationType == 'proportional') {
        isCorrect = (_slope - _currentRelationship.slope).abs() < 0.1;
      } else {
        // For linear relationships, check both slope and y-intercept
        bool slopeMatch = (_slope - _currentRelationship.slope).abs() < 0.1;
        bool yInterceptMatch = (_yIntercept - _currentRelationship.yIntercept).abs() < 0.1;
        isCorrect = slopeMatch && yInterceptMatch;
      }
    }
    
    setState(() {
      _isCorrect = isCorrect;
      
      if (isCorrect) {
        _feedbackMessage = 'Correct! Your relationship matches the description.';
      } else {
        _feedbackMessage = 'Not quite. ${_currentRelationship.hint}';
      }
    });
    
    // Start the animation
    _animationController.forward(from: 0.0);
  }
  
  void _nextRelationship() {
    if (_currentRelationshipIndex < _relationships.length - 1) {
      setState(() {
        _currentRelationshipIndex++;
        _currentRelationship = _relationships[_currentRelationshipIndex];
        _updateRelationshipFromData(_currentRelationship);
        _feedbackMessage = null;
        _isCorrect = false;
      });
    } else {
      // All relationships completed
      setState(() {
        _isCompleted = true;
      });
      widget.onStateChanged?.call(true);
    }
  }
  
  void _resetWidget() {
    setState(() {
      _currentRelationshipIndex = 0;
      _currentRelationship = _relationships[_currentRelationshipIndex];
      _updateRelationshipFromData(_currentRelationship);
      _feedbackMessage = null;
      _isCorrect = false;
      _isCompleted = false;
    });
    widget.onStateChanged?.call(false);
  }
  
  @override
  Widget build(BuildContext context) {
    if (_isCompleted) {
      return _buildCompletionScreen();
    }
    
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Title and description
        Text(
          'Relationship Builder',
          style: TextStyle(
            fontSize: 20,
            fontWeight: FontWeight.bold,
            color: widget.textColor,
          ),
        ),
        const SizedBox(height: 8),
        Text(
          _currentRelationship.description,
          style: TextStyle(
            fontSize: 16,
            color: widget.textColor,
          ),
        ),
        
        const SizedBox(height: 16),
        
        // Relationship type selector
        Row(
          children: [
            Text(
              'Relationship Type:',
              style: TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.w500,
                color: widget.textColor,
              ),
            ),
            const SizedBox(width: 16),
            ChoiceChip(
              label: const Text('Linear'),
              selected: _selectedRelationType == 'linear',
              onSelected: (selected) {
                if (selected) _setRelationType('linear');
              },
              selectedColor: widget.primaryColor.withOpacity(0.3),
            ),
            const SizedBox(width: 8),
            ChoiceChip(
              label: const Text('Proportional'),
              selected: _selectedRelationType == 'proportional',
              onSelected: (selected) {
                if (selected) _setRelationType('proportional');
              },
              selectedColor: widget.primaryColor.withOpacity(0.3),
            ),
          ],
        ),
        
        const SizedBox(height: 16),
        
        // Slope slider
        Row(
          children: [
            SizedBox(width: 80, child: Text('Slope (m):', style: TextStyle(color: widget.textColor))),
            Expanded(
              child: Slider(
                value: _slope,
                min: -5.0,
                max: 5.0,
                divisions: 100,
                label: _slope.toStringAsFixed(1),
                onChanged: _updateSlope,
                activeColor: widget.primaryColor,
              ),
            ),
            SizedBox(
              width: 50,
              child: Text(
                _slope.toStringAsFixed(1),
                style: TextStyle(color: widget.textColor),
              ),
            ),
          ],
        ),
        
        // Y-intercept slider (only for linear relationships)
        if (_selectedRelationType == 'linear') ...[
          Row(
            children: [
              SizedBox(width: 80, child: Text('Y-intercept (b):', style: TextStyle(color: widget.textColor))),
              Expanded(
                child: Slider(
                  value: _yIntercept,
                  min: -10.0,
                  max: 10.0,
                  divisions: 200,
                  label: _yIntercept.toStringAsFixed(1),
                  onChanged: _updateYIntercept,
                  activeColor: widget.primaryColor,
                ),
              ),
              SizedBox(
                width: 50,
                child: Text(
                  _yIntercept.toStringAsFixed(1),
                  style: TextStyle(color: widget.textColor),
                ),
              ),
            ],
          ),
        ],
        
        const SizedBox(height: 16),
        
        // Equation display
        Container(
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            color: widget.backgroundColor,
            borderRadius: BorderRadius.circular(8),
            border: Border.all(color: widget.primaryColor),
          ),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Text(
                'Equation: ',
                style: TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                  color: widget.textColor,
                ),
              ),
              Text(
                _relationshipEquation,
                style: TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                  color: widget.primaryColor,
                ),
              ),
            ],
          ),
        ),
        
        const SizedBox(height: 16),
        
        // Table of values
        Text(
          'Table of Values:',
          style: TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.w500,
            color: widget.textColor,
          ),
        ),
        const SizedBox(height: 8),
        Container(
          decoration: BoxDecoration(
            border: Border.all(color: widget.primaryColor),
            borderRadius: BorderRadius.circular(8),
          ),
          child: Table(
            border: TableBorder.all(
              color: widget.primaryColor.withOpacity(0.3),
              width: 1,
            ),
            children: [
              TableRow(
                decoration: BoxDecoration(
                  color: widget.primaryColor.withOpacity(0.1),
                ),
                children: [
                  TableCell(
                    child: Padding(
                      padding: const EdgeInsets.all(8.0),
                      child: Center(
                        child: Text(
                          'x',
                          style: TextStyle(
                            fontWeight: FontWeight.bold,
                            color: widget.textColor,
                          ),
                        ),
                      ),
                    ),
                  ),
                  TableCell(
                    child: Padding(
                      padding: const EdgeInsets.all(8.0),
                      child: Center(
                        child: Text(
                          'y',
                          style: TextStyle(
                            fontWeight: FontWeight.bold,
                            color: widget.textColor,
                          ),
                        ),
                      ),
                    ),
                  ),
                ],
              ),
              ..._tableData.map((row) => TableRow(
                children: [
                  TableCell(
                    child: Padding(
                      padding: const EdgeInsets.all(8.0),
                      child: Center(
                        child: Text(
                          row[0].toStringAsFixed(1),
                          style: TextStyle(color: widget.textColor),
                        ),
                      ),
                    ),
                  ),
                  TableCell(
                    child: Padding(
                      padding: const EdgeInsets.all(8.0),
                      child: Center(
                        child: Text(
                          row[1].toStringAsFixed(1),
                          style: TextStyle(color: widget.textColor),
                        ),
                      ),
                    ),
                  ),
                ],
              )).toList(),
            ],
          ),
        ),
        
        const SizedBox(height: 16),
        
        // Check button
        ElevatedButton(
          onPressed: _checkRelationship,
          style: ElevatedButton.styleFrom(
            backgroundColor: widget.primaryColor,
            foregroundColor: Colors.white,
          ),
          child: const Text('Check My Relationship'),
        ),
        
        const SizedBox(height: 16),
        
        // Feedback message
        if (_feedbackMessage != null) ...[
          AnimatedBuilder(
            animation: _animation,
            builder: (context, child) {
              return Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: _isCorrect 
                      ? widget.successColor.withOpacity(0.1)
                      : widget.errorColor.withOpacity(0.1),
                  borderRadius: BorderRadius.circular(8),
                  border: Border.all(
                    color: _isCorrect ? widget.successColor : widget.errorColor,
                  ),
                ),
                child: Row(
                  children: [
                    Icon(
                      _isCorrect ? Icons.check_circle : Icons.error,
                      color: _isCorrect ? widget.successColor : widget.errorColor,
                    ),
                    const SizedBox(width: 8),
                    Expanded(
                      child: Text(
                        _feedbackMessage!,
                        style: TextStyle(
                          color: _isCorrect ? widget.successColor : widget.errorColor,
                        ),
                      ),
                    ),
                  ],
                ),
              );
            },
          ),
          const SizedBox(height: 16),
        ],
        
        // Navigation buttons
        Row(
          mainAxisAlignment: MainAxisAlignment.end,
          children: [
            ElevatedButton.icon(
              icon: const Icon(Icons.arrow_forward),
              label: Text(_currentRelationshipIndex < _relationships.length - 1 ? 'Next Relationship' : 'Finish'),
              onPressed: _isCorrect ? _nextRelationship : null,
              style: ElevatedButton.styleFrom(
                backgroundColor: widget.primaryColor,
                foregroundColor: Colors.white,
                disabledBackgroundColor: Colors.grey.shade300,
              ),
            ),
          ],
        ),
      ],
    );
  }
  
  Widget _buildCompletionScreen() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.check_circle,
            color: widget.successColor,
            size: 64,
          ),
          const SizedBox(height: 16),
          Text(
            'Great job!',
            style: TextStyle(
              fontSize: 24,
              fontWeight: FontWeight.bold,
              color: widget.textColor,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            'You\'ve completed all the relationship building challenges.',
            textAlign: TextAlign.center,
            style: TextStyle(
              fontSize: 16,
              color: widget.textColor,
            ),
          ),
          const SizedBox(height: 24),
          ElevatedButton.icon(
            icon: const Icon(Icons.refresh),
            label: const Text('Start Over'),
            onPressed: _resetWidget,
            style: ElevatedButton.styleFrom(
              backgroundColor: widget.primaryColor,
              foregroundColor: Colors.white,
              padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
            ),
          ),
        ],
      ),
    );
  }
}

/// Data class for relationship challenges
class RelationshipData {
  final String description;
  final String type;
  final double slope;
  final double yIntercept;
  final String equation;
  final String hint;

  RelationshipData({
    required this.description,
    required this.type,
    required this.slope,
    required this.yIntercept,
    required this.equation,
    required this.hint,
  });

  factory RelationshipData.fromJson(Map<String, dynamic> json) {
    return RelationshipData(
      description: json['description'] ?? '',
      type: json['type'] ?? 'linear',
      slope: json['slope']?.toDouble() ?? 1.0,
      yIntercept: json['yIntercept']?.toDouble() ?? 0.0,
      equation: json['equation'] ?? 'y = x',
      hint: json['hint'] ?? '',
    );
  }
}
