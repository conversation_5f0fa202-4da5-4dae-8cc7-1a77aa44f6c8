import 'package:flutter/material.dart';
import 'dart:math' as math;
import 'dart:async';

class CollisionObject {
  Offset position;
  Offset velocity;
  final double mass;
  final double radius;
  final Color color;

  CollisionObject({
    required this.position,
    required this.velocity,
    required this.mass,
    required this.radius,
    required this.color,
  });
}

class InteractiveElasticInelasticCollisionsWidget extends StatefulWidget {
  final Map<String, dynamic>? data;

  const InteractiveElasticInelasticCollisionsWidget({
    super.key,
    this.data,
  });

  factory InteractiveElasticInelasticCollisionsWidget.fromData(
      Map<String, dynamic> data) {
    return InteractiveElasticInelasticCollisionsWidget(
      data: data,
    );
  }

  @override
  State<InteractiveElasticInelasticCollisionsWidget> createState() =>
      _InteractiveElasticInelasticCollisionsWidgetState();
}

class _InteractiveElasticInelasticCollisionsWidgetState
    extends State<InteractiveElasticInelasticCollisionsWidget>
    with SingleTickerProviderStateMixin {
  // UI parameters
  late Color _primaryColor;
  late Color _secondaryColor;
  late Color _tertiaryColor;
  late Color _textColor;
  late Color _backgroundColor;
  late Color _elasticColor;
  late Color _inelasticColor;
  late Color _perfectlyInelasticColor;

  // Simulation parameters
  double _coefficientOfRestitution = 1.0; // 1.0 = elastic, 0.0 = perfectly inelastic
  bool _isSimulating = false;
  bool _showVelocityVectors = true;
  bool _showEnergyBars = true;
  bool _showSideBySide = true;
  Timer? _simulationTimer;
  double _timeStep = 0.016; // 60 FPS
  double _elapsedTime = 0.0;
  double _simulationSpeed = 1.0;

  // Physics objects
  late List<CollisionObject> _elasticObjects;
  late List<CollisionObject> _inelasticObjects;

  // Boundaries
  final double _minX = 0;
  final double _maxX = 100;
  final double _minY = 0;
  final double _maxY = 100;

  // Tracking
  List<double> _elasticKineticEnergyHistory = [];
  List<double> _inelasticKineticEnergyHistory = [];
  double _initialKineticEnergy = 0.0;

  // Animation controller
  late AnimationController _animationController;
  late Animation<double> _animation;

  @override
  void initState() {
    super.initState();
    _initializeFromData();
    _animationController = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 500),
    );
    _animation = CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeInOut,
    );
    _animationController.forward();
    _initializeSimulation();
  }

  @override
  void dispose() {
    _simulationTimer?.cancel();
    _animationController.dispose();
    super.dispose();
  }

  void _initializeFromData() {
    final data = widget.data;
    if (data != null) {
      _primaryColor = Color(data['primary_color'] ?? 0xFF2196F3);
      _secondaryColor = Color(data['secondary_color'] ?? 0xFFFFA000);
      _tertiaryColor = Color(data['tertiary_color'] ?? 0xFF4CAF50);
      _textColor = Color(data['text_color'] ?? 0xFF333333);
      _backgroundColor = Color(data['background_color'] ?? 0xFFF5F5F5);
      _elasticColor = Color(data['elastic_color'] ?? 0xFF4CAF50);
      _inelasticColor = Color(data['inelastic_color'] ?? 0xFFF44336);
      _perfectlyInelasticColor = Color(data['perfectly_inelastic_color'] ?? 0xFF9C27B0);

      if (data['coefficient_of_restitution'] != null) {
        _coefficientOfRestitution = data['coefficient_of_restitution'].toDouble();
      }
    } else {
      _primaryColor = Colors.blue;
      _secondaryColor = Colors.orange;
      _tertiaryColor = Colors.green;
      _textColor = Colors.black87;
      _backgroundColor = Colors.grey.shade100;
      _elasticColor = Colors.green;
      _inelasticColor = Colors.red;
      _perfectlyInelasticColor = Colors.purple;
    }
  }

  void _initializeSimulation() {
    // Reset tracking
    _elasticKineticEnergyHistory = [];
    _inelasticKineticEnergyHistory = [];
    _elapsedTime = 0.0;

    // Create physics objects for elastic collision
    _elasticObjects = [
      CollisionObject(
        position: Offset(30, 50),
        velocity: Offset(20, 0),
        mass: 1.0,
        radius: 5.0,
        color: _primaryColor,
      ),
      CollisionObject(
        position: Offset(70, 50),
        velocity: Offset(0, 0),
        mass: 1.0,
        radius: 5.0,
        color: _secondaryColor,
      ),
    ];

    // Create physics objects for inelastic collision (same initial conditions)
    _inelasticObjects = [
      CollisionObject(
        position: Offset(30, 50),
        velocity: Offset(20, 0),
        mass: 1.0,
        radius: 5.0,
        color: _primaryColor,
      ),
      CollisionObject(
        position: Offset(70, 50),
        velocity: Offset(0, 0),
        mass: 1.0,
        radius: 5.0,
        color: _secondaryColor,
      ),
    ];

    // Calculate initial kinetic energy
    _initialKineticEnergy = _calculateKineticEnergy(_elasticObjects);

    // Add initial values to history
    _elasticKineticEnergyHistory.add(_initialKineticEnergy);
    _inelasticKineticEnergyHistory.add(_initialKineticEnergy);
  }

  double _calculateKineticEnergy(List<CollisionObject> objects) {
    double totalKineticEnergy = 0;

    for (var object in objects) {
      final speed = math.sqrt(object.velocity.dx * object.velocity.dx +
                             object.velocity.dy * object.velocity.dy);
      totalKineticEnergy += 0.5 * object.mass * speed * speed;
    }

    return totalKineticEnergy;
  }

  void _resetSimulation() {
    _stopSimulation();
    _initializeSimulation();
    setState(() {});
  }

  void _toggleSimulation() {
    if (_isSimulating) {
      _stopSimulation();
    } else {
      _startSimulation();
    }
  }

  void _startSimulation() {
    if (_isSimulating) return;

    setState(() {
      _isSimulating = true;
    });

    _simulationTimer = Timer.periodic(Duration(milliseconds: (_timeStep * 1000 ~/ _simulationSpeed)), (timer) {
      _updateSimulation();
    });
  }

  void _stopSimulation() {
    _simulationTimer?.cancel();
    _simulationTimer = null;

    setState(() {
      _isSimulating = false;
    });
  }

  void _updateSimulation() {
    setState(() {
      // Update elapsed time
      _elapsedTime += _timeStep;

      // Move objects
      _moveObjects(_elasticObjects);
      _moveObjects(_inelasticObjects);

      // Check for collisions
      _checkCollisions(_elasticObjects, 1.0); // Elastic (e = 1.0)
      _checkCollisions(_inelasticObjects, _coefficientOfRestitution); // Inelastic

      // Update energy history
      _elasticKineticEnergyHistory.add(_calculateKineticEnergy(_elasticObjects));
      _inelasticKineticEnergyHistory.add(_calculateKineticEnergy(_inelasticObjects));

      // Keep history at a reasonable size
      if (_elasticKineticEnergyHistory.length > 100) {
        _elasticKineticEnergyHistory.removeAt(0);
        _inelasticKineticEnergyHistory.removeAt(0);
      }

      // Stop simulation if objects are far apart
      if (_elapsedTime > 5.0) {
        bool elasticFarApart = (_elasticObjects[0].position.dx - _elasticObjects[1].position.dx).abs() > 50;
        bool inelasticFarApart = (_inelasticObjects[0].position.dx - _inelasticObjects[1].position.dx).abs() > 50;

        if (elasticFarApart && inelasticFarApart) {
          _stopSimulation();
        }
      }
    });
  }

  void _moveObjects(List<CollisionObject> objects) {
    for (var object in objects) {
      object.position = Offset(
        object.position.dx + object.velocity.dx * _timeStep,
        object.position.dy + object.velocity.dy * _timeStep,
      );

      // Bounce off walls
      if (object.position.dx - object.radius < _minX) {
        object.position = Offset(_minX + object.radius, object.position.dy);
        object.velocity = Offset(-object.velocity.dx, object.velocity.dy);
      } else if (object.position.dx + object.radius > _maxX) {
        object.position = Offset(_maxX - object.radius, object.position.dy);
        object.velocity = Offset(-object.velocity.dx, object.velocity.dy);
      }

      if (object.position.dy - object.radius < _minY) {
        object.position = Offset(object.position.dx, _minY + object.radius);
        object.velocity = Offset(object.velocity.dx, -object.velocity.dy);
      } else if (object.position.dy + object.radius > _maxY) {
        object.position = Offset(object.position.dx, _maxY - object.radius);
        object.velocity = Offset(object.velocity.dx, -object.velocity.dy);
      }
    }
  }

  void _checkCollisions(List<CollisionObject> objects, double restitution) {
    if (objects.length < 2) return;

    // Check for collision between objects[0] and objects[1]
    final distance = (objects[0].position - objects[1].position).distance;
    if (distance <= objects[0].radius + objects[1].radius) {
      _resolveCollision(objects[0], objects[1], restitution);
    }
  }

  void _resolveCollision(CollisionObject a, CollisionObject b, double restitution) {
    // Calculate normal vector
    final normalVector = b.position - a.position;
    final distance = normalVector.distance;

    // Normalize the vector manually
    final normal = Offset(
      normalVector.dx / distance,
      normalVector.dy / distance,
    );

    // Calculate relative velocity
    final relativeVelocity = b.velocity - a.velocity;

    // Calculate relative velocity along normal
    final velocityAlongNormal = relativeVelocity.dx * normal.dx + relativeVelocity.dy * normal.dy;

    // Do not resolve if objects are moving away from each other
    if (velocityAlongNormal > 0) return;

    // Calculate impulse scalar
    final impulseScalar = -(1 + restitution) * velocityAlongNormal /
        (1 / a.mass + 1 / b.mass);

    // Apply impulse
    final impulse = Offset(normal.dx * impulseScalar, normal.dy * impulseScalar);

    a.velocity = Offset(
      a.velocity.dx - impulse.dx / a.mass,
      a.velocity.dy - impulse.dy / a.mass,
    );

    b.velocity = Offset(
      b.velocity.dx + impulse.dx / b.mass,
      b.velocity.dy + impulse.dy / b.mass,
    );

    // Separate objects to prevent sticking
    final overlap = a.radius + b.radius - distance;
    final separationVector = Offset(normal.dx * overlap / 2, normal.dy * overlap / 2);

    a.position = Offset(
      a.position.dx - separationVector.dx,
      a.position.dy - separationVector.dy,
    );

    b.position = Offset(
      b.position.dx + separationVector.dx,
      b.position.dy + separationVector.dy,
    );
  }

  @override
  Widget build(BuildContext context) {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
      ),
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Title and description
            Text(
              'Elastic vs. Inelastic Collisions',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
                color: _textColor,
              ),
            ),
            const SizedBox(height: 8),
            Text(
              'Compare energy conservation in different types of collisions',
              style: TextStyle(
                fontSize: 14,
                color: _textColor.withOpacity(0.7),
              ),
            ),
            const SizedBox(height: 16),

            // Coefficient of restitution slider
            Row(
              children: [
                Text(
                  'Coefficient of Restitution:',
                  style: TextStyle(
                    fontSize: 14,
                    fontWeight: FontWeight.bold,
                    color: _textColor,
                  ),
                ),
                const SizedBox(width: 8),
                Text(
                  _coefficientOfRestitution.toStringAsFixed(2),
                  style: TextStyle(
                    fontSize: 14,
                    color: _primaryColor,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
            Slider(
              value: _coefficientOfRestitution,
              min: 0.0,
              max: 1.0,
              divisions: 20,
              label: _coefficientOfRestitution.toStringAsFixed(2),
              onChanged: (value) {
                setState(() {
                  _coefficientOfRestitution = value;
                  _resetSimulation();
                });
              },
              activeColor: _primaryColor,
              inactiveColor: _primaryColor.withOpacity(0.2),
            ),

            // Collision type labels
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  'Perfectly Inelastic',
                  style: TextStyle(
                    fontSize: 12,
                    color: _perfectlyInelasticColor,
                  ),
                ),
                Text(
                  'Inelastic',
                  style: TextStyle(
                    fontSize: 12,
                    color: _inelasticColor,
                  ),
                ),
                Text(
                  'Elastic',
                  style: TextStyle(
                    fontSize: 12,
                    color: _elasticColor,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),

            // Simulation controls
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                // Start/Stop button
                ElevatedButton.icon(
                  onPressed: _toggleSimulation,
                  icon: Icon(_isSimulating ? Icons.pause : Icons.play_arrow),
                  label: Text(_isSimulating ? 'Pause' : 'Start'),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: _primaryColor,
                    foregroundColor: Colors.white,
                  ),
                ),

                // Reset button
                ElevatedButton.icon(
                  onPressed: _resetSimulation,
                  icon: const Icon(Icons.refresh),
                  label: const Text('Reset'),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: _secondaryColor,
                    foregroundColor: Colors.white,
                  ),
                ),

                // Display options
                PopupMenuButton<String>(
                  icon: const Icon(Icons.settings),
                  onSelected: (value) {
                    setState(() {
                      switch (value) {
                        case 'velocity':
                          _showVelocityVectors = !_showVelocityVectors;
                          break;
                        case 'energy':
                          _showEnergyBars = !_showEnergyBars;
                          break;
                        case 'layout':
                          _showSideBySide = !_showSideBySide;
                          break;
                      }
                    });
                  },
                  itemBuilder: (context) => [
                    CheckedPopupMenuItem(
                      value: 'velocity',
                      checked: _showVelocityVectors,
                      child: const Text('Show Velocity Vectors'),
                    ),
                    CheckedPopupMenuItem(
                      value: 'energy',
                      checked: _showEnergyBars,
                      child: const Text('Show Energy Bars'),
                    ),
                    CheckedPopupMenuItem(
                      value: 'layout',
                      checked: _showSideBySide,
                      child: const Text('Side-by-Side View'),
                    ),
                  ],
                ),
              ],
            ),
            const SizedBox(height: 16),

            // Simulation area
            Container(
              height: 300,
              width: double.infinity,
              decoration: BoxDecoration(
                color: Colors.grey.withOpacity(0.1),
                borderRadius: BorderRadius.circular(8),
                border: Border.all(color: Colors.grey.withOpacity(0.3)),
              ),
              child: ClipRRect(
                borderRadius: BorderRadius.circular(8),
                child: _showSideBySide
                    ? Row(
                        children: [
                          // Elastic collision
                          Expanded(
                            child: CustomPaint(
                              painter: CollisionSimulationPainter(
                                objects: _elasticObjects,
                                minX: _minX,
                                maxX: _maxX,
                                minY: _minY,
                                maxY: _maxY,
                                showVelocityVectors: _showVelocityVectors,
                                showEnergyBars: _showEnergyBars,
                                title: 'Elastic',
                                primaryColor: _primaryColor,
                                secondaryColor: _secondaryColor,
                                textColor: _textColor,
                                backgroundColor: _backgroundColor,
                              ),
                            ),
                          ),
                          // Divider
                          Container(
                            width: 1,
                            color: Colors.grey.withOpacity(0.5),
                          ),
                          // Inelastic collision
                          Expanded(
                            child: CustomPaint(
                              painter: CollisionSimulationPainter(
                                objects: _inelasticObjects,
                                minX: _minX,
                                maxX: _maxX,
                                minY: _minY,
                                maxY: _maxY,
                                showVelocityVectors: _showVelocityVectors,
                                showEnergyBars: _showEnergyBars,
                                title: 'Inelastic',
                                primaryColor: _primaryColor,
                                secondaryColor: _secondaryColor,
                                textColor: _textColor,
                                backgroundColor: _backgroundColor,
                              ),
                            ),
                          ),
                        ],
                      )
                    : Column(
                        children: [
                          // Elastic collision
                          Expanded(
                            child: CustomPaint(
                              painter: CollisionSimulationPainter(
                                objects: _elasticObjects,
                                minX: _minX,
                                maxX: _maxX,
                                minY: _minY,
                                maxY: _maxY,
                                showVelocityVectors: _showVelocityVectors,
                                showEnergyBars: _showEnergyBars,
                                title: 'Elastic',
                                primaryColor: _primaryColor,
                                secondaryColor: _secondaryColor,
                                textColor: _textColor,
                                backgroundColor: _backgroundColor,
                              ),
                            ),
                          ),
                          // Divider
                          Container(
                            height: 1,
                            color: Colors.grey.withOpacity(0.5),
                          ),
                          // Inelastic collision
                          Expanded(
                            child: CustomPaint(
                              painter: CollisionSimulationPainter(
                                objects: _inelasticObjects,
                                minX: _minX,
                                maxX: _maxX,
                                minY: _minY,
                                maxY: _maxY,
                                showVelocityVectors: _showVelocityVectors,
                                showEnergyBars: _showEnergyBars,
                                title: 'Inelastic',
                                primaryColor: _primaryColor,
                                secondaryColor: _secondaryColor,
                                textColor: _textColor,
                                backgroundColor: _backgroundColor,
                              ),
                            ),
                          ),
                        ],
                      ),
              ),
            ),
            const SizedBox(height: 16),

            // Energy comparison graph
            if (_showEnergyBars)
              Container(
                height: 100,
                width: double.infinity,
                decoration: BoxDecoration(
                  color: Colors.grey.withOpacity(0.1),
                  borderRadius: BorderRadius.circular(8),
                  border: Border.all(color: Colors.grey.withOpacity(0.3)),
                ),
                child: CustomPaint(
                  painter: EnergyComparisonPainter(
                    elasticEnergyHistory: _elasticKineticEnergyHistory,
                    inelasticEnergyHistory: _inelasticKineticEnergyHistory,
                    initialEnergy: _initialKineticEnergy,
                    elasticColor: _elasticColor,
                    inelasticColor: _inelasticColor,
                    textColor: _textColor,
                  ),
                ),
              ),
            const SizedBox(height: 16),

            // Physics explanation
            ExpansionTile(
              title: Text(
                'Understanding Elastic vs. Inelastic Collisions',
                style: TextStyle(
                  fontSize: 14,
                  fontWeight: FontWeight.bold,
                  color: _textColor,
                ),
              ),
              children: [
                Padding(
                  padding: const EdgeInsets.all(16.0),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'Coefficient of Restitution (e):',
                        style: TextStyle(
                          fontSize: 14,
                          fontWeight: FontWeight.bold,
                          color: _textColor,
                        ),
                      ),
                      const SizedBox(height: 4),
                      Text(
                        'The coefficient of restitution is a measure of how much kinetic energy is preserved in a collision:',
                        style: TextStyle(
                          fontSize: 14,
                          color: _textColor,
                        ),
                      ),
                      const SizedBox(height: 8),
                      Text(
                        '• e = 1: Perfectly elastic collision (all kinetic energy is conserved)',
                        style: TextStyle(
                          fontSize: 14,
                          color: _elasticColor,
                        ),
                      ),
                      Text(
                        '• 0 < e < 1: Inelastic collision (some kinetic energy is lost)',
                        style: TextStyle(
                          fontSize: 14,
                          color: _inelasticColor,
                        ),
                      ),
                      Text(
                        '• e = 0: Perfectly inelastic collision (maximum kinetic energy is lost)',
                        style: TextStyle(
                          fontSize: 14,
                          color: _perfectlyInelasticColor,
                        ),
                      ),
                      const SizedBox(height: 8),
                      Text(
                        'Conservation Laws:',
                        style: TextStyle(
                          fontSize: 14,
                          fontWeight: FontWeight.bold,
                          color: _textColor,
                        ),
                      ),
                      const SizedBox(height: 4),
                      Text(
                        '• Momentum is always conserved in all collisions',
                        style: TextStyle(
                          fontSize: 14,
                          color: _textColor,
                        ),
                      ),
                      Text(
                        '• Kinetic energy is fully conserved only in elastic collisions',
                        style: TextStyle(
                          fontSize: 14,
                          color: _textColor,
                        ),
                      ),
                      Text(
                        '• In inelastic collisions, some kinetic energy is converted to other forms (heat, sound, deformation)',
                        style: TextStyle(
                          fontSize: 14,
                          color: _textColor,
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }
}

class CollisionSimulationPainter extends CustomPainter {
  final List<CollisionObject> objects;
  final double minX;
  final double maxX;
  final double minY;
  final double maxY;
  final bool showVelocityVectors;
  final bool showEnergyBars;
  final String title;
  final Color primaryColor;
  final Color secondaryColor;
  final Color textColor;
  final Color backgroundColor;

  CollisionSimulationPainter({
    required this.objects,
    required this.minX,
    required this.maxX,
    required this.minY,
    required this.maxY,
    required this.showVelocityVectors,
    required this.showEnergyBars,
    required this.title,
    required this.primaryColor,
    required this.secondaryColor,
    required this.textColor,
    required this.backgroundColor,
  });

  @override
  void paint(Canvas canvas, Size size) {
    // Draw background grid
    _drawGrid(canvas, size);

    // Draw title
    final titleStyle = TextStyle(
      color: textColor,
      fontSize: 14,
      fontWeight: FontWeight.bold,
    );

    final titleSpan = TextSpan(
      text: title,
      style: titleStyle,
    );

    final titlePainter = TextPainter(
      text: titleSpan,
      textDirection: TextDirection.ltr,
    );

    titlePainter.layout();
    titlePainter.paint(
      canvas,
      Offset(
        size.width / 2 - titlePainter.width / 2,
        10,
      ),
    );

    // Scale factors to map simulation coordinates to canvas coordinates
    final scaleX = size.width / (maxX - minX);
    final scaleY = size.height / (maxY - minY);

    // Draw objects
    for (var object in objects) {
      // Map object position to canvas coordinates
      final canvasX = (object.position.dx - minX) * scaleX;
      final canvasY = (object.position.dy - minY) * scaleY;
      final canvasPosition = Offset(canvasX, canvasY);
      final canvasRadius = object.radius * scaleX;

      // Draw object
      final paint = Paint()
        ..color = object.color
        ..style = PaintingStyle.fill;

      canvas.drawCircle(
        canvasPosition,
        canvasRadius,
        paint,
      );

      // Draw border
      final borderPaint = Paint()
        ..color = Colors.black.withOpacity(0.3)
        ..style = PaintingStyle.stroke
        ..strokeWidth = 1.0;

      canvas.drawCircle(
        canvasPosition,
        canvasRadius,
        borderPaint,
      );

      // Draw velocity vector if enabled
      if (showVelocityVectors) {
        final velocityMagnitude = math.sqrt(
          object.velocity.dx * object.velocity.dx +
          object.velocity.dy * object.velocity.dy,
        );

        if (velocityMagnitude > 0.1) {
          final velocityDirection = Offset(
            object.velocity.dx / velocityMagnitude,
            object.velocity.dy / velocityMagnitude,
          );

          final vectorLength = math.min(velocityMagnitude * 2, 20.0);
          final endPoint = Offset(
            canvasPosition.dx + velocityDirection.dx * vectorLength,
            canvasPosition.dy + velocityDirection.dy * vectorLength,
          );

          final velocityPaint = Paint()
            ..color = primaryColor
            ..strokeWidth = 2.0
            ..style = PaintingStyle.stroke;

          // Draw line
          canvas.drawLine(
            canvasPosition,
            endPoint,
            velocityPaint,
          );

          // Draw arrowhead
          final arrowSize = 5.0;
          final angle = math.atan2(
            endPoint.dy - canvasPosition.dy,
            endPoint.dx - canvasPosition.dx,
          );

          final arrowPoint1 = Offset(
            endPoint.dx - arrowSize * math.cos(angle - math.pi / 6),
            endPoint.dy - arrowSize * math.sin(angle - math.pi / 6),
          );

          final arrowPoint2 = Offset(
            endPoint.dx - arrowSize * math.cos(angle + math.pi / 6),
            endPoint.dy - arrowSize * math.sin(angle + math.pi / 6),
          );

          final arrowPath = Path()
            ..moveTo(endPoint.dx, endPoint.dy)
            ..lineTo(arrowPoint1.dx, arrowPoint1.dy)
            ..lineTo(arrowPoint2.dx, arrowPoint2.dy)
            ..close();

          canvas.drawPath(arrowPath, velocityPaint..style = PaintingStyle.fill);
        }
      }

      // Draw mass label
      final textStyle = TextStyle(
        color: textColor,
        fontSize: 10,
        fontWeight: FontWeight.bold,
      );

      final textSpan = TextSpan(
        text: '${object.mass.toStringAsFixed(1)} kg',
        style: textStyle,
      );

      final textPainter = TextPainter(
        text: textSpan,
        textDirection: TextDirection.ltr,
      );

      textPainter.layout();
      textPainter.paint(
        canvas,
        Offset(
          canvasPosition.dx - textPainter.width / 2,
          canvasPosition.dy - canvasRadius - textPainter.height - 2,
        ),
      );
    }

    // Draw energy bars if enabled
    if (showEnergyBars) {
      _drawEnergyBar(canvas, size, objects);
    }
  }

  void _drawGrid(Canvas canvas, Size size) {
    final gridPaint = Paint()
      ..color = Colors.grey.withOpacity(0.2)
      ..strokeWidth = 0.5;

    // Draw horizontal grid lines
    final horizontalSpacing = size.height / 10;
    for (int i = 0; i <= 10; i++) {
      final y = i * horizontalSpacing;
      canvas.drawLine(
        Offset(0, y),
        Offset(size.width, y),
        gridPaint,
      );
    }

    // Draw vertical grid lines
    final verticalSpacing = size.width / 10;
    for (int i = 0; i <= 10; i++) {
      final x = i * verticalSpacing;
      canvas.drawLine(
        Offset(x, 0),
        Offset(x, size.height),
        gridPaint,
      );
    }
  }

  void _drawEnergyBar(Canvas canvas, Size size, List<CollisionObject> objects) {
    // Calculate total kinetic energy
    double totalKineticEnergy = 0;

    for (var object in objects) {
      final speed = math.sqrt(object.velocity.dx * object.velocity.dx +
                             object.velocity.dy * object.velocity.dy);
      totalKineticEnergy += 0.5 * object.mass * speed * speed;
    }

    // Draw energy bar at the bottom
    final barHeight = 20.0;
    final barWidth = size.width - 40;
    final barX = 20.0;
    final barY = size.height - barHeight - 10;

    // Background
    final bgPaint = Paint()
      ..color = Colors.grey.withOpacity(0.2)
      ..style = PaintingStyle.fill;

    canvas.drawRect(
      Rect.fromLTWH(barX, barY, barWidth, barHeight),
      bgPaint,
    );

    // Energy level
    final maxEnergy = 500.0; // Adjust based on your simulation
    final energyWidth = (totalKineticEnergy / maxEnergy) * barWidth;

    final energyPaint = Paint()
      ..color = secondaryColor
      ..style = PaintingStyle.fill;

    canvas.drawRect(
      Rect.fromLTWH(barX, barY, energyWidth, barHeight),
      energyPaint,
    );

    // Border
    final borderPaint = Paint()
      ..color = Colors.grey.withOpacity(0.5)
      ..style = PaintingStyle.stroke
      ..strokeWidth = 1.0;

    canvas.drawRect(
      Rect.fromLTWH(barX, barY, barWidth, barHeight),
      borderPaint,
    );

    // Label
    final textStyle = TextStyle(
      color: textColor,
      fontSize: 10,
      fontWeight: FontWeight.bold,
    );

    final textSpan = TextSpan(
      text: 'Kinetic Energy: ${totalKineticEnergy.toStringAsFixed(1)} J',
      style: textStyle,
    );

    final textPainter = TextPainter(
      text: textSpan,
      textDirection: TextDirection.ltr,
    );

    textPainter.layout();
    textPainter.paint(
      canvas,
      Offset(
        barX + barWidth / 2 - textPainter.width / 2,
        barY + barHeight / 2 - textPainter.height / 2,
      ),
    );
  }

  @override
  bool shouldRepaint(covariant CollisionSimulationPainter oldDelegate) {
    return oldDelegate.objects != objects ||
        oldDelegate.showVelocityVectors != showVelocityVectors ||
        oldDelegate.showEnergyBars != showEnergyBars;
  }
}

class EnergyComparisonPainter extends CustomPainter {
  final List<double> elasticEnergyHistory;
  final List<double> inelasticEnergyHistory;
  final double initialEnergy;
  final Color elasticColor;
  final Color inelasticColor;
  final Color textColor;

  EnergyComparisonPainter({
    required this.elasticEnergyHistory,
    required this.inelasticEnergyHistory,
    required this.initialEnergy,
    required this.elasticColor,
    required this.inelasticColor,
    required this.textColor,
  });

  @override
  void paint(Canvas canvas, Size size) {
    // Draw axes
    final axesPaint = Paint()
      ..color = textColor.withOpacity(0.5)
      ..strokeWidth = 1.0;

    // X-axis (time)
    canvas.drawLine(
      Offset(0, size.height - 20),
      Offset(size.width, size.height - 20),
      axesPaint,
    );

    // Y-axis (energy)
    canvas.drawLine(
      Offset(20, 0),
      Offset(20, size.height - 20),
      axesPaint,
    );

    // Draw labels
    final labelStyle = TextStyle(
      color: textColor,
      fontSize: 10,
    );

    // X-axis label
    final xLabelSpan = TextSpan(
      text: 'Time',
      style: labelStyle,
    );

    final xLabelPainter = TextPainter(
      text: xLabelSpan,
      textDirection: TextDirection.ltr,
    );

    xLabelPainter.layout();
    xLabelPainter.paint(
      canvas,
      Offset(
        size.width - xLabelPainter.width - 5,
        size.height - xLabelPainter.height - 2,
      ),
    );

    // Y-axis label
    final yLabelSpan = TextSpan(
      text: 'Kinetic Energy',
      style: labelStyle,
    );

    final yLabelPainter = TextPainter(
      text: yLabelSpan,
      textDirection: TextDirection.ltr,
    );

    yLabelPainter.layout();
    yLabelPainter.paint(
      canvas,
      Offset(
        5,
        5,
      ),
    );

    // Draw initial energy line
    final initialEnergyPaint = Paint()
      ..color = textColor.withOpacity(0.3)
      ..strokeWidth = 1.0
      ..style = PaintingStyle.stroke;

    // Calculate y position for initial energy
    final graphHeight = size.height - 30;
    final maxEnergy = initialEnergy * 1.2; // Add some margin
    final initialY = size.height - 20 - (initialEnergy / maxEnergy) * graphHeight;

    // Draw dashed line
    final dashWidth = 5.0;
    final gapWidth = 3.0;
    double currentX = 20;

    while (currentX < size.width) {
      canvas.drawLine(
        Offset(currentX, initialY),
        Offset(currentX + dashWidth, initialY),
        initialEnergyPaint,
      );
      currentX += dashWidth + gapWidth;
    }

    // Draw initial energy label
    final initialEnergyLabelSpan = TextSpan(
      text: 'Initial Energy',
      style: TextStyle(
        color: textColor.withOpacity(0.7),
        fontSize: 8,
      ),
    );

    final initialEnergyLabelPainter = TextPainter(
      text: initialEnergyLabelSpan,
      textDirection: TextDirection.ltr,
    );

    initialEnergyLabelPainter.layout();
    initialEnergyLabelPainter.paint(
      canvas,
      Offset(
        25,
        initialY - initialEnergyLabelPainter.height - 2,
      ),
    );

    // Draw energy graphs
    if (elasticEnergyHistory.isNotEmpty) {
      _drawEnergyGraph(
        canvas,
        size,
        elasticEnergyHistory,
        elasticColor,
        maxEnergy,
        'Elastic',
      );
    }

    if (inelasticEnergyHistory.isNotEmpty) {
      _drawEnergyGraph(
        canvas,
        size,
        inelasticEnergyHistory,
        inelasticColor,
        maxEnergy,
        'Inelastic',
      );
    }

    // Draw energy conservation note
    final noteStyle = TextStyle(
      color: textColor.withOpacity(0.7),
      fontSize: 9,
      fontStyle: FontStyle.italic,
    );

    final noteText = 'In elastic collisions, kinetic energy is conserved. In inelastic collisions, some kinetic energy is converted to other forms.';

    final noteSpan = TextSpan(
      text: noteText,
      style: noteStyle,
    );

    final notePainter = TextPainter(
      text: noteSpan,
      textDirection: TextDirection.ltr,
    );

    notePainter.layout(maxWidth: size.width - 40);
    notePainter.paint(
      canvas,
      Offset(
        30,
        size.height - 15,
      ),
    );
  }

  void _drawEnergyGraph(
    Canvas canvas,
    Size size,
    List<double> energyHistory,
    Color color,
    double maxEnergy,
    String label,
  ) {
    if (energyHistory.isEmpty) return;

    // Calculate scale factors
    final graphWidth = size.width - 30;
    final graphHeight = size.height - 30;
    final xScale = graphWidth / (energyHistory.length - 1 > 0 ? energyHistory.length - 1 : 1);
    final yScale = maxEnergy > 0 ? graphHeight / maxEnergy : 1;

    // Draw graph line
    final path = Path();
    final graphPaint = Paint()
      ..color = color
      ..style = PaintingStyle.stroke
      ..strokeWidth = 2.0;

    for (int i = 0; i < energyHistory.length; i++) {
      final x = 20 + i * xScale;
      final y = size.height - 20 - energyHistory[i] * yScale;

      if (i == 0) {
        path.moveTo(x, y);
      } else {
        path.lineTo(x, y);
      }
    }

    canvas.drawPath(path, graphPaint);

    // Draw current energy value
    final currentEnergy = energyHistory.last;
    final labelStyle = TextStyle(
      color: color,
      fontSize: 10,
      fontWeight: FontWeight.bold,
    );

    final labelSpan = TextSpan(
      text: '$label: ${currentEnergy.toStringAsFixed(1)} J',
      style: labelStyle,
    );

    final labelPainter = TextPainter(
      text: labelSpan,
      textDirection: TextDirection.ltr,
    );

    labelPainter.layout();
    labelPainter.paint(
      canvas,
      Offset(
        size.width - labelPainter.width - 10,
        20 + (label == 'Elastic' ? 0 : 15),
      ),
    );
  }

  @override
  bool shouldRepaint(covariant EnergyComparisonPainter oldDelegate) {
    return oldDelegate.elasticEnergyHistory != elasticEnergyHistory ||
        oldDelegate.inelasticEnergyHistory != inelasticEnergyHistory ||
        oldDelegate.initialEnergy != initialEnergy;
  }
}
