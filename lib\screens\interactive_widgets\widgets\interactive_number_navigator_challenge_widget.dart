import 'package:flutter/material.dart';
import 'dart:math' as math;
import 'dart:math' show Random;

/// A widget that provides a comprehensive module test for number concepts.
class InteractiveNumberNavigatorChallengeWidget extends StatefulWidget {
  final Map<String, dynamic> data;
  final Function(bool)? onStateChanged;
  final Color primaryColor;
  final Color secondaryColor;
  final Color backgroundColor;
  final Color textColor;

  const InteractiveNumberNavigatorChallengeWidget({
    super.key,
    this.data = const {},
    this.onStateChanged,
    this.primaryColor = Colors.blue,
    this.secondaryColor = Colors.orange,
    this.backgroundColor = Colors.white,
    this.textColor = Colors.black87,
  });

  @override
  State<InteractiveNumberNavigatorChallengeWidget> createState() =>
      _InteractiveNumberNavigatorChallengeWidgetState();
}

class _InteractiveNumberNavigatorChallengeWidgetState
    extends State<InteractiveNumberNavigatorChallengeWidget> {
  // State variables
  bool _isCompleted = false;
  int _currentQuestionIndex = 0;
  int _score = 0;
  int _totalQuestions = 10;
  bool _showResults = false;
  String? _selectedAnswer;
  String? _feedbackMessage;
  List<Map<String, dynamic>> _questions = [];

  // Timer variables
  int _timeRemaining = 300; // 5 minutes in seconds
  bool _timerActive = false;
  late DateTime _timerStartTime;

  @override
  void initState() {
    super.initState();
    _generateQuestions();
  }

  void _generateQuestions() {
    // Clear existing questions
    _questions = [];

    // Generate a mix of different question types
    Random random = Random();

    // Ensure we have at least one of each type
    _addPlaceValueQuestion();
    _addDivisionQuestion();
    _addComparisonQuestion();
    _addNumberLineQuestion();
    _addMultiplicationQuestion();

    // Add more random questions to reach total
    while (_questions.length < _totalQuestions) {
      int questionType = random.nextInt(5);

      switch (questionType) {
        case 0:
          _addPlaceValueQuestion();
          break;
        case 1:
          _addDivisionQuestion();
          break;
        case 2:
          _addComparisonQuestion();
          break;
        case 3:
          _addNumberLineQuestion();
          break;
        case 4:
          _addMultiplicationQuestion();
          break;
      }
    }

    // Shuffle the questions
    _questions.shuffle();
  }

  void _addPlaceValueQuestion() {
    Random random = Random();
    int number = random.nextInt(9000) + 1000; // 4-digit number
    List<String> digits = number.toString().split('');
    int digitPosition = random.nextInt(digits.length);
    int digit = int.parse(digits[digitPosition]);

    List<String> placeNames = ['Ones', 'Tens', 'Hundreds', 'Thousands'];
    String placeName = placeNames[digits.length - 1 - digitPosition];

    int placeValue = digit * math.pow(10, digits.length - 1 - digitPosition).toInt();

    Map<String, dynamic> question = {
      'type': 'place_value',
      'text': 'What is the value of the digit $digit in the ${placeName.toLowerCase()} place of $number?',
      'correctAnswer': placeValue.toString(),
      'options': _generateNumericOptions(placeValue, 4),
      'explanation': 'The digit $digit is in the ${placeName.toLowerCase()} place, so its value is $digit × ${math.pow(10, digits.length - 1 - digitPosition).toInt()} = $placeValue.'
    };

    _questions.add(question);
  }

  void _addDivisionQuestion() {
    Random random = Random();
    int divisor = random.nextInt(9) + 2; // 2-10
    int quotient = random.nextInt(10) + 1; // 1-10
    int remainder = random.nextBool() ? random.nextInt(divisor) : 0;
    int dividend = divisor * quotient + remainder;

    String correctAnswer = quotient.toString();
    if (remainder > 0) {
      correctAnswer = '$quotient R $remainder';
    }

    Map<String, dynamic> question = {
      'type': 'division',
      'text': 'What is $dividend ÷ $divisor?',
      'correctAnswer': correctAnswer,
      'options': _generateDivisionOptions(quotient, remainder, divisor),
      'explanation': 'When we divide $dividend by $divisor, we get $quotient with a remainder of $remainder.'
    };

    _questions.add(question);
  }

  void _addComparisonQuestion() {
    Random random = Random();
    int firstNumber = random.nextInt(100);
    int secondNumber = random.nextInt(100);

    String comparisonSymbol;
    String correctAnswer;

    if (firstNumber < secondNumber) {
      comparisonSymbol = '<';
      correctAnswer = 'Less than';
    } else if (firstNumber > secondNumber) {
      comparisonSymbol = '>';
      correctAnswer = 'Greater than';
    } else {
      comparisonSymbol = '=';
      correctAnswer = 'Equal to';
    }

    Map<String, dynamic> question = {
      'type': 'comparison',
      'text': 'How does $firstNumber compare to $secondNumber?',
      'correctAnswer': correctAnswer,
      'options': ['Less than', 'Greater than', 'Equal to'],
      'explanation': '$firstNumber is $correctAnswer $secondNumber, so we write $firstNumber $comparisonSymbol $secondNumber.'
    };

    _questions.add(question);
  }

  void _addNumberLineQuestion() {
    Random random = Random();
    int start = random.nextInt(50);
    int interval = random.nextInt(5) + 1;

    List<int> numbers = [];
    for (int i = 0; i < 5; i++) {
      numbers.add(start + i * interval);
    }

    int missingIndex = random.nextInt(5);
    int missingNumber = numbers[missingIndex];
    numbers[missingIndex] = -1; // Mark as missing

    Map<String, dynamic> question = {
      'type': 'number_line',
      'text': 'What number is missing from this sequence? ${numbers.map((n) => n == -1 ? "?" : n.toString()).join(", ")}',
      'correctAnswer': missingNumber.toString(),
      'options': _generateNumericOptions(missingNumber, 4),
      'explanation': 'The pattern increases by $interval each time. The missing number is $missingNumber.'
    };

    _questions.add(question);
  }

  void _addMultiplicationQuestion() {
    Random random = Random();
    int factor1 = random.nextInt(10) + 1;
    int factor2 = random.nextInt(10) + 1;
    int product = factor1 * factor2;

    Map<String, dynamic> question = {
      'type': 'multiplication',
      'text': 'What is $factor1 × $factor2?',
      'correctAnswer': product.toString(),
      'options': _generateNumericOptions(product, 4),
      'explanation': 'When we multiply $factor1 by $factor2, we get $product.'
    };

    _questions.add(question);
  }

  List<String> _generateNumericOptions(int correctAnswer, int count) {
    List<String> options = [correctAnswer.toString()];
    Random random = Random();

    while (options.length < count) {
      int offset = random.nextInt(correctAnswer ~/ 2 + 5) + 1;
      if (random.nextBool()) offset = -offset;

      int wrongAnswer = correctAnswer + offset;
      if (wrongAnswer > 0 && !options.contains(wrongAnswer.toString())) {
        options.add(wrongAnswer.toString());
      }
    }

    options.shuffle();
    return options;
  }

  List<String> _generateDivisionOptions(int quotient, int remainder, int divisor) {
    List<String> options = [];

    String correctAnswer = quotient.toString();
    if (remainder > 0) {
      correctAnswer = '$quotient R $remainder';
    }

    options.add(correctAnswer);

    Random random = Random();

    // Add wrong quotient
    int wrongQuotient = quotient + (random.nextBool() ? 1 : -1);
    if (wrongQuotient > 0) {
      String wrongAnswer = wrongQuotient.toString();
      if (remainder > 0) {
        wrongAnswer = '$wrongQuotient R $remainder';
      }
      options.add(wrongAnswer);
    }

    // Add wrong remainder
    if (remainder > 0) {
      int wrongRemainder = (remainder + 1) % divisor;
      options.add('$quotient R $wrongRemainder');
    } else {
      int wrongRemainder = random.nextInt(divisor - 1) + 1;
      options.add('$quotient R $wrongRemainder');
    }

    // Add one more wrong answer
    while (options.length < 4) {
      int wrongQuotient = quotient + (random.nextInt(3) - 1);
      int wrongRemainder = random.nextInt(divisor);

      if (wrongQuotient > 0) {
        String wrongAnswer = wrongQuotient.toString();
        if (wrongRemainder > 0) {
          wrongAnswer = '$wrongQuotient R $wrongRemainder';
        }

        if (!options.contains(wrongAnswer)) {
          options.add(wrongAnswer);
        }
      }
    }

    options.shuffle();
    return options;
  }

  void _startChallenge() {
    setState(() {
      _currentQuestionIndex = 0;
      _score = 0;
      _showResults = false;
      _selectedAnswer = null;
      _feedbackMessage = null;
      _isCompleted = false;
      _timerActive = true;
      _timeRemaining = 300; // Reset to 5 minutes
      _timerStartTime = DateTime.now();
    });

    // Start timer
    _startTimer();

    // Notify parent
    widget.onStateChanged?.call(false);
  }

  void _startTimer() {
    Future.delayed(const Duration(seconds: 1), () {
      if (!mounted) return;

      if (_timerActive) {
        setState(() {
          _timeRemaining = math.max(0, _timeRemaining - 1);

          // Check if time is up
          if (_timeRemaining <= 0) {
            _endChallenge();
          }
        });

        // Continue timer
        if (_timeRemaining > 0 && _timerActive) {
          _startTimer();
        }
      }
    });
  }

  void _checkAnswer(String answer) {
    final currentQuestion = _questions[_currentQuestionIndex];
    final bool isCorrect = answer == currentQuestion['correctAnswer'];

    setState(() {
      _selectedAnswer = answer;

      if (isCorrect) {
        _score++;
        _feedbackMessage = 'Correct! ${currentQuestion['explanation']}';
      } else {
        _feedbackMessage = 'Incorrect. ${currentQuestion['explanation']}';
      }
    });

    // Move to next question after a delay
    Future.delayed(const Duration(seconds: 2), () {
      if (!mounted) return;

      setState(() {
        if (_currentQuestionIndex < _questions.length - 1) {
          _currentQuestionIndex++;
          _selectedAnswer = null;
          _feedbackMessage = null;
        } else {
          _endChallenge();
        }
      });
    });
  }

  void _endChallenge() {
    setState(() {
      _timerActive = false;
      _showResults = true;

      // Calculate completion status (pass if score >= 70%)
      _isCompleted = (_score / _questions.length) >= 0.7;
    });

    // Notify parent of completion status
    widget.onStateChanged?.call(_isCompleted);
  }

  String _formatTime(int seconds) {
    int minutes = seconds ~/ 60;
    int remainingSeconds = seconds % 60;
    return '$minutes:${remainingSeconds.toString().padLeft(2, '0')}';
  }

  Widget _buildCurrentQuestion() {
    if (_currentQuestionIndex >= _questions.length) {
      return Container();
    }

    final question = _questions[_currentQuestionIndex];

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Question number and timer
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Text(
              'Question ${_currentQuestionIndex + 1} of ${_questions.length}',
              style: TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.bold,
                color: widget.textColor,
              ),
            ),
            Container(
              padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
              decoration: BoxDecoration(
                color: _timeRemaining < 60 ? Colors.red : widget.primaryColor,
                borderRadius: BorderRadius.circular(16),
              ),
              child: Row(
                children: [
                  Icon(
                    Icons.timer,
                    color: Colors.white,
                    size: 18,
                  ),
                  const SizedBox(width: 4),
                  Text(
                    _formatTime(_timeRemaining),
                    style: TextStyle(
                      color: Colors.white,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),

        const SizedBox(height: 16),

        // Question text
        Text(
          question['text'],
          style: TextStyle(
            fontSize: 18,
            fontWeight: FontWeight.bold,
            color: widget.textColor,
          ),
        ),

        const SizedBox(height: 24),

        // Answer options
        ...question['options'].map<Widget>((option) {
          bool isSelected = _selectedAnswer == option;
          bool isCorrect = option == question['correctAnswer'];

          Color buttonColor = isSelected
              ? (isCorrect ? Colors.green : Colors.red)
              : widget.primaryColor;

          return Padding(
            padding: const EdgeInsets.only(bottom: 12.0),
            child: SizedBox(
              width: double.infinity,
              child: ElevatedButton(
                onPressed: _selectedAnswer == null ? () => _checkAnswer(option) : null,
                style: ElevatedButton.styleFrom(
                  backgroundColor: buttonColor,
                  padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
                  alignment: Alignment.centerLeft,
                ),
                child: Text(
                  option,
                  style: TextStyle(
                    fontSize: 16,
                    color: Colors.white,
                  ),
                ),
              ),
            ),
          );
        }).toList(),

        if (_feedbackMessage != null)
          Padding(
            padding: const EdgeInsets.only(top: 16.0),
            child: Text(
              _feedbackMessage!,
              style: TextStyle(
                color: _selectedAnswer == question['correctAnswer']
                    ? Colors.green
                    : Colors.red,
                fontWeight: FontWeight.bold,
                fontSize: 16,
              ),
            ),
          ),
      ],
    );
  }

  Widget _buildResultsScreen() {
    // Calculate percentage score
    final percentage = (_score / _questions.length * 100).round();
    final isPassing = percentage >= 70;

    return Column(
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        // Trophy icon for passing, or try again icon for failing
        Icon(
          isPassing ? Icons.emoji_events : Icons.refresh,
          size: 80,
          color: isPassing ? Colors.amber : Colors.grey,
        ),

        const SizedBox(height: 24),

        // Result title
        Text(
          isPassing ? 'Congratulations!' : 'Keep Practicing!',
          style: TextStyle(
            fontSize: 24,
            fontWeight: FontWeight.bold,
            color: isPassing ? widget.primaryColor : Colors.red,
          ),
        ),

        const SizedBox(height: 16),

        // Score
        Text(
          'Your Score: $_score out of ${_questions.length} ($percentage%)',
          style: TextStyle(
            fontSize: 18,
            fontWeight: FontWeight.bold,
            color: widget.textColor,
          ),
        ),

        const SizedBox(height: 8),

        // Time taken
        Text(
          'Time Taken: ${_formatTime(300 - _timeRemaining)}',
          style: TextStyle(
            fontSize: 16,
            color: widget.textColor,
          ),
        ),

        const SizedBox(height: 24),

        // Feedback message
        Text(
          isPassing
              ? 'Great job! You\'ve demonstrated a solid understanding of number concepts.'
              : 'You need a score of at least 70% to pass. Review the concepts and try again!',
          textAlign: TextAlign.center,
          style: TextStyle(
            fontSize: 16,
            color: widget.textColor,
          ),
        ),

        const SizedBox(height: 32),

        // Try again button
        ElevatedButton.icon(
          onPressed: _startChallenge,
          icon: Icon(Icons.refresh),
          label: Text('Try Again'),
          style: ElevatedButton.styleFrom(
            backgroundColor: widget.secondaryColor,
            padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
          ),
        ),
      ],
    );
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: widget.backgroundColor,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withAlpha(50),
            spreadRadius: 1,
            blurRadius: 3,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Title
          Text(
            'Number Navigator Challenge',
            style: TextStyle(
              fontSize: 20,
              fontWeight: FontWeight.bold,
              color: widget.textColor,
            ),
          ),

          const SizedBox(height: 8),

          // Description
          Text(
            'Test your understanding of number concepts with this comprehensive challenge.',
            style: TextStyle(
              fontSize: 14,
              color: widget.textColor.withOpacity(0.8),
            ),
          ),

          const SizedBox(height: 24),

          // Main content area
          if (_showResults)
            _buildResultsScreen()
          else if (_questions.isEmpty)
            Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  const SizedBox(height: 40),
                  Text(
                    'Ready to test your number skills?',
                    style: TextStyle(
                      fontSize: 18,
                      fontWeight: FontWeight.bold,
                      color: widget.textColor,
                    ),
                  ),
                  const SizedBox(height: 16),
                  Text(
                    'This challenge includes 10 questions covering place value, division, comparison, and more.',
                    textAlign: TextAlign.center,
                    style: TextStyle(
                      fontSize: 14,
                      color: widget.textColor,
                    ),
                  ),
                  const SizedBox(height: 8),
                  Text(
                    'You\'ll have 5 minutes to complete the challenge.',
                    textAlign: TextAlign.center,
                    style: TextStyle(
                      fontSize: 14,
                      color: widget.textColor,
                    ),
                  ),
                  const SizedBox(height: 32),
                  ElevatedButton.icon(
                    onPressed: _startChallenge,
                    icon: Icon(Icons.play_arrow),
                    label: Text('Start Challenge'),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: widget.primaryColor,
                      padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
                    ),
                  ),
                ],
              ),
            )
          else
            _buildCurrentQuestion(),
        ],
      ),
    );
  }
}
