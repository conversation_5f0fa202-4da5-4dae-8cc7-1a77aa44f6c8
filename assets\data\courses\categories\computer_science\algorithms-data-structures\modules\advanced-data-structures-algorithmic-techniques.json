{"id": "advanced-data-structures-algorithmic-techniques", "title": "Advanced Data Structures and Algorithmic Techniques", "description": "Explore hash tables, dynamic programming, and greedy algorithms for complex problem-solving.", "order": 5, "lessons": [{"id": "hash-tables", "title": "Hash Tables: Efficient Lookups", "description": "Understand how hash tables provide fast data retrieval using hash functions.", "order": 1, "type": "interactive_lesson", "estimatedTimeMinutes": 12, "contentBlocks": [{"id": "hash_screen1_what_are_hashtables", "type": "lesson_screen", "order": 1, "estimatedTimeSeconds": 75, "content": {"headline": "Hash Tables: Key-Value Magic", "body_md": "A Hash Table (or Hash Map) is a data structure that implements an associative array, mapping keys to values. It uses a **hash function** to compute an index (or 'hash code') into an array of buckets or slots, from which the desired value can be found.\n\nThis allows for very fast average-case lookups, insertions, and deletions.", "visual": {"type": "giphy_search", "value": "magic key lock"}, "interactive_element": {"type": "button", "button_text": "How Hashing Works"}, "audio_narration_url": null}}, {"id": "hash_screen2_hash_function", "type": "lesson_screen", "order": 2, "estimatedTimeSeconds": 90, "content": {"headline": "The Hash Function", "body_md": "A good hash function:\n\n*   Is fast to compute.\n*   Distributes keys uniformly across the available slots (minimizes collisions).\n*   Produces the same hash for the same key every time.\n\n**Collisions:** When two different keys hash to the same index. These must be handled (e.g., by chaining - storing a linked list at the slot, or open addressing - probing for the next empty slot).", "visual": {"type": "unsplash_search", "value": "function machine"}, "interactive_element": {"type": "multiple_choice_text", "question_text": "A collision in a hash table occurs when:", "options": [{"text": "A key is not found.", "is_correct": false, "feedback": "That's a search miss, not a collision."}, {"text": "Two different keys produce the same hash index.", "is_correct": true, "feedback": "Correct! This requires a collision resolution strategy."}, {"text": "The hash table is full.", "is_correct": false, "feedback": "While a full table is an issue, a collision is about index mapping."}]}, "audio_narration_url": null}}, {"id": "hash_screen3_performance", "type": "lesson_screen", "order": 3, "estimatedTimeSeconds": 75, "content": {"headline": "Hash Table Performance", "body_md": "*   **Average Time Complexity (Search, Insert, Delete):** O(1) - incredibly fast, assuming a good hash function and few collisions.\n*   **Worst-Case Time Complexity:** O(n) - if all keys hash to the same slot (effectively becomes a linked list search).\n*   **Space Complexity:** O(n) for storing n elements.\n\nHash tables are widely used due to their excellent average-case performance.", "visual": {"type": "giphy_search", "value": "super fast speed"}, "interactive_element": {"type": "button", "button_text": "Dynamic Programming?"}, "audio_narration_url": null}}]}, {"id": "dynamic-programming", "title": "Dynamic Programming (DP)", "description": "Learn the principles of dynamic programming for solving complex problems by breaking them into overlapping subproblems.", "order": 2, "type": "interactive_lesson", "estimatedTimeMinutes": 15, "contentBlocks": [{"id": "dp_screen1_what_is_dp", "type": "lesson_screen", "order": 1, "estimatedTimeSeconds": 90, "content": {"headline": "Dynamic Programming: Smart Recursion", "body_md": "Dynamic Programming is an algorithmic technique for solving complex problems by breaking them down into simpler, **overlapping subproblems** and storing the results of these subproblems (memoization or tabulation) to avoid redundant computations.\n\nIt's particularly useful for optimization problems.", "visual": {"type": "giphy_search", "value": "puzzle pieces fitting together memory"}, "interactive_element": {"type": "button", "button_text": "Key Characteristics?"}, "audio_narration_url": null}}, {"id": "dp_screen2_characteristics", "type": "lesson_screen", "order": 2, "estimatedTimeSeconds": 100, "content": {"headline": "When to Use Dynamic Programming", "body_md": "DP is applicable when a problem has:\n\n1.  **Optimal Substructure:** The optimal solution to the overall problem can be constructed from the optimal solutions of its subproblems.\n2.  **Overlapping Subproblems:** The same subproblems are encountered multiple times when solving the main problem recursively.\n\nDP solves each subproblem only once and stores its solution.", "visual": {"type": "unsplash_search", "value": "overlapping circles"}, "interactive_element": {"type": "multiple_choice_text", "question_text": "DP avoids recomputing by:", "options": [{"text": "Ignoring subproblems.", "is_correct": false, "feedback": "DP relies on solving subproblems."}, {"text": "Storing and reusing solutions to subproblems.", "is_correct": true, "feedback": "Correct! This is memoization/tabulation."}, {"text": "Using a faster CPU.", "is_correct": false, "feedback": "Hardware helps, but DP is an algorithmic technique."}]}, "audio_narration_url": null}}, {"id": "dp_screen3_<PERSON>_<PERSON><PERSON><PERSON><PERSON>", "type": "lesson_screen", "order": 3, "estimatedTimeSeconds": 90, "content": {"headline": "Example: Fibonacci Sequence", "body_md": "Calculating <PERSON><PERSON><PERSON><PERSON> numbers naively with recursion `fib(n) = fib(n-1) + fib(n-2)` involves many redundant calculations (e.g., `fib(3)` is calculated multiple times).\n\n**DP Approach (Memoization):**\nStore the result of `fib(k)` the first time it's computed. If needed again, just look up the stored value.\nThis drastically reduces computation time from exponential O(2ⁿ) to linear O(n).", "visual": {"type": "giphy_search", "value": "numbers sequence"}, "interactive_element": {"type": "button", "button_text": "Greedy Algorithms Next!"}, "audio_narration_url": null}}]}, {"id": "greedy-algorithms", "title": "Greedy Algorithms", "description": "Understand the greedy approach: making locally optimal choices hoping for a global optimum.", "order": 3, "type": "interactive_lesson", "estimatedTimeMinutes": 10, "contentBlocks": [{"id": "greedy_screen1_what_are_greedy", "type": "lesson_screen", "order": 1, "estimatedTimeSeconds": 75, "content": {"headline": "Greedy Algorithms: Local Best, Global Hope", "body_md": "A greedy algorithm makes the choice that seems best at the current moment (the locally optimal choice) in the hope that this choice will lead to a globally optimal solution.\n\nThey don't always yield a global optimum, but are often simpler and faster, and can provide good approximations or exact solutions for certain problems.", "visual": {"type": "giphy_search", "value": "grabbing quick"}, "interactive_element": {"type": "button", "button_text": "When Do They Work?"}, "audio_narration_url": null}}, {"id": "greedy_screen2_properties", "type": "lesson_screen", "order": 2, "estimatedTimeSeconds": 90, "content": {"headline": "Characteristics of Greedy Problems", "body_md": "Greedy algorithms often work for problems exhibiting:\n\n1.  **Greedy Choice Property:** A global optimum can be arrived at by selecting a local optimum.\n2.  **Optimal Substructure:** An optimal solution to the problem contains optimal solutions to its subproblems (similar to DP, but the way it's used differs).\n\nExample: Making change with the fewest coins (using largest denomination coins first works for standard US currency).", "visual": {"type": "unsplash_search", "value": "coins money change"}, "interactive_element": {"type": "multiple_choice_text", "question_text": "Greedy algorithms always find the global optimum.", "options": [{"text": "True", "is_correct": false, "feedback": "Not always! They find it for some problems, but can fail for others."}, {"text": "False", "is_correct": true, "feedback": "Correct. Their local choices don't always lead to the best overall solution."}]}, "audio_narration_url": null}}, {"id": "greedy_screen3_summary", "type": "lesson_screen", "order": 3, "estimatedTimeSeconds": 60, "content": {"headline": "Advanced Techniques Recap", "body_md": "*   **Hash Tables:** O(1) average lookups via hash functions.\n*   **Dynamic Programming:** Solves overlapping subproblems by storing results.\n*   **Greedy Algorithms:** Makes locally optimal choices.\n\nThese techniques are essential for tackling more complex algorithmic challenges!", "visual": {"type": "giphy_search", "value": "brain gears advanced"}, "interactive_element": {"type": "button", "button_text": "Module Test Time!"}, "audio_narration_url": null}}]}], "moduleTest": {"id": "advanced-ds-algo-test", "title": "Module Test: Advanced DS & Techniques", "description": "Test your knowledge of hash tables, DP, and greedy algorithms.", "type": "module_test_interactive", "estimatedTimeMinutes": 8, "contentBlocks": [{"id": "adv_test_q1_hashtable_avg_time", "type": "question_screen", "order": 1, "estimatedTimeSeconds": 60, "content": {"headline": "Question 1: Hash Table Speed", "body_md": "What is the average time complexity for search, insertion, and deletion in a well-implemented hash table?", "visual": {"type": "giphy_search", "value": "fast clock"}, "interactive_element": {"type": "multiple_choice_text", "question_text": "Hash table average time?", "options": [{"text": "O(1)", "is_correct": true, "feedback": "Correct! This is why hash tables are so popular."}, {"text": "O(log n)", "is_correct": false, "feedback": "O(log n) is typical for tree-based structures like BSTs or heaps."}, {"text": "O(n)", "is_correct": false, "feedback": "O(n) is the worst-case for hash tables, not average."}]}, "audio_narration_url": null}}, {"id": "adv_test_q2_dp_condition", "type": "question_screen", "order": 2, "estimatedTimeSeconds": 75, "content": {"headline": "Question 2: Dynamic Programming", "body_md": "Dynamic Programming is most effective when a problem exhibits which two characteristics?", "visual": {"type": "unsplash_search", "value": "puzzle pieces fitting"}, "interactive_element": {"type": "multiple_choice_text", "question_text": "DP is best for problems with:", "options": [{"text": "Random subproblems and no optimal substructure.", "is_correct": false, "feedback": "DP requires optimal substructure and overlapping subproblems."}, {"text": "Optimal substructure and overlapping subproblems.", "is_correct": true, "feedback": "Correct! These are the hallmarks of problems suited for DP."}, {"text": "Greedy choice property and independent subproblems.", "is_correct": false, "feedback": "Greedy choice property is for greedy algorithms. DP handles overlapping subproblems."}]}, "audio_narration_url": null}}, {"id": "adv_test_q3_greedy_choice", "type": "question_screen", "order": 3, "estimatedTimeSeconds": 75, "content": {"headline": "Question 3: Greedy Algorithms", "body_md": "A greedy algorithm makes decisions based on:", "visual": {"type": "giphy_search", "value": "quick decision"}, "interactive_element": {"type": "multiple_choice_text", "question_text": "Greedy algorithms decide based on:", "options": [{"text": "The choice that seems best at the current moment.", "is_correct": true, "feedback": "Correct! This is the 'locally optimal' choice."}, {"text": "A complete exploration of all possible future choices.", "is_correct": false, "feedback": "That would be more like exhaustive search or some forms of DP, not greedy."}, {"text": "Random selection to ensure fairness.", "is_correct": false, "feedback": "Greedy choices are deterministic based on the current best option, not random."}]}, "audio_narration_url": null}}, {"id": "adv_test_q4_collision_resolution", "type": "question_screen", "order": 4, "estimatedTimeSeconds": 60, "content": {"headline": "Question 4: Hash Table Collisions", "body_md": "What is 'chaining' in the context of hash tables?", "visual": {"type": "unsplash_search", "value": "linked chain"}, "interactive_element": {"type": "multiple_choice_text", "question_text": "'Chaining' is a method for:", "options": [{"text": "Improving the hash function's speed.", "is_correct": false, "feedback": "Chaining doesn't directly speed up the hash function itself."}, {"text": "Handling hash collisions by storing multiple items at the same index.", "is_correct": true, "feedback": "Correct! Each slot can point to a list of items that hashed to that index."}, {"text": "Linking multiple hash tables together.", "is_correct": false, "feedback": "Chaining refers to collision resolution within a single hash table."}]}, "audio_narration_url": null}}]}}