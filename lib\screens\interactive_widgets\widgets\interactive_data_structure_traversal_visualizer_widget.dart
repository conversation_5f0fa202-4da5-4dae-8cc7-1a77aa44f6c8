import 'package:flutter/material.dart';
import 'dart:async';
import 'dart:math' as math;

/// Data structure models

/// Tree node model
class TreeNode {
  final int value;
  final int? left;
  final int? right;

  TreeNode({
    required this.value,
    this.left,
    this.right,
  });
}

/// Graph edge model
class GraphEdge {
  final int from;
  final int to;

  GraphEdge({
    required this.from,
    required this.to,
  });
}

/// Custom painters for data structure visualization

/// Tree painter
class TreePainter extends CustomPainter {
  final List<TreeNode> nodes;
  final int rootValue;
  final int? currentNodeValue;
  final List<int> visitedNodeValues;
  final Color primaryColor;
  final Color visitedNodeColor;
  final Color currentNodeColor;
  final Color edgeColor;
  final Color textColor;

  TreePainter({
    required this.nodes,
    required this.rootValue,
    this.currentNodeValue,
    required this.visitedNodeValues,
    required this.primaryColor,
    required this.visitedNodeColor,
    required this.currentNodeColor,
    required this.edgeColor,
    required this.textColor,
  });

  @override
  void paint(Canvas canvas, Size size) {
    final width = size.width;
    final height = size.height;

    // Calculate node positions
    final Map<int, Offset> nodePositions = {};
    _calculateNodePositions(rootValue, 0, width, 0, height * 0.8, nodePositions);

    // Draw edges
    final edgePaint = Paint()
      ..color = edgeColor
      ..style = PaintingStyle.stroke
      ..strokeWidth = 2;

    for (final node in nodes) {
      final nodePos = nodePositions[node.value];
      if (nodePos == null) continue;

      // Draw edge to left child
      if (node.left != null && nodePositions.containsKey(node.left)) {
        final leftPos = nodePositions[node.left]!;
        canvas.drawLine(nodePos, leftPos, edgePaint);
      }

      // Draw edge to right child
      if (node.right != null && nodePositions.containsKey(node.right)) {
        final rightPos = nodePositions[node.right]!;
        canvas.drawLine(nodePos, rightPos, edgePaint);
      }
    }

    // Draw nodes
    for (final node in nodes) {
      final nodePos = nodePositions[node.value];
      if (nodePos == null) continue;

      // Determine node color
      Color nodeColor;
      if (node.value == currentNodeValue) {
        nodeColor = currentNodeColor;
      } else if (visitedNodeValues.contains(node.value)) {
        nodeColor = visitedNodeColor;
      } else {
        nodeColor = primaryColor;
      }

      // Draw node circle
      final nodePaint = Paint()
        ..color = nodeColor
        ..style = PaintingStyle.fill;
      canvas.drawCircle(nodePos, 20, nodePaint);

      // Draw node border
      final borderPaint = Paint()
        ..color = nodeColor.withAlpha(200)
        ..style = PaintingStyle.stroke
        ..strokeWidth = 2;
      canvas.drawCircle(nodePos, 20, borderPaint);

      // Draw node value
      final textStyle = TextStyle(
        color: Colors.white,
        fontSize: 14,
        fontWeight: FontWeight.bold,
      );
      final textSpan = TextSpan(
        text: node.value.toString(),
        style: textStyle,
      );
      final textPainter = TextPainter(
        text: textSpan,
        textDirection: TextDirection.ltr,
      );
      textPainter.layout();
      textPainter.paint(
        canvas,
        Offset(
          nodePos.dx - textPainter.width / 2,
          nodePos.dy - textPainter.height / 2,
        ),
      );
    }
  }

  // Calculate node positions for the tree
  void _calculateNodePositions(
    int? nodeValue,
    double xMin,
    double xMax,
    double y,
    double maxHeight,
    Map<int, Offset> positions,
  ) {
    if (nodeValue == null) return;

    final node = nodes.firstWhere((n) => n.value == nodeValue);
    final x = (xMin + xMax) / 2;
    final levelHeight = maxHeight / (nodes.length ~/ 2 + 1);

    // Store node position
    positions[nodeValue] = Offset(x, y + 30);

    // Calculate positions for children
    if (node.left != null) {
      _calculateNodePositions(
        node.left,
        xMin,
        x,
        y + levelHeight,
        maxHeight,
        positions,
      );
    }

    if (node.right != null) {
      _calculateNodePositions(
        node.right,
        x,
        xMax,
        y + levelHeight,
        maxHeight,
        positions,
      );
    }
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => true;
}

/// Graph painter
class GraphPainter extends CustomPainter {
  final List<int> nodes;
  final List<GraphEdge> edges;
  final int? currentNodeValue;
  final List<int> visitedNodeValues;
  final Color primaryColor;
  final Color visitedNodeColor;
  final Color currentNodeColor;
  final Color edgeColor;
  final Color textColor;

  GraphPainter({
    required this.nodes,
    required this.edges,
    this.currentNodeValue,
    required this.visitedNodeValues,
    required this.primaryColor,
    required this.visitedNodeColor,
    required this.currentNodeColor,
    required this.edgeColor,
    required this.textColor,
  });

  @override
  void paint(Canvas canvas, Size size) {
    final width = size.width;
    final height = size.height;

    // Calculate node positions
    final Map<int, Offset> nodePositions = {};

    // Position nodes in a circle
    final centerX = width / 2;
    final centerY = height / 2;
    final radius = math.min(width, height) * 0.35;

    for (int i = 0; i < nodes.length; i++) {
      final angle = 2 * math.pi * i / nodes.length;
      final x = centerX + radius * math.cos(angle);
      final y = centerY + radius * math.sin(angle);
      nodePositions[nodes[i]] = Offset(x, y);
    }

    // Draw edges
    final edgePaint = Paint()
      ..color = edgeColor
      ..style = PaintingStyle.stroke
      ..strokeWidth = 2;

    for (final edge in edges) {
      final fromPos = nodePositions[edge.from];
      final toPos = nodePositions[edge.to];

      if (fromPos != null && toPos != null) {
        // Draw arrow
        _drawArrow(canvas, fromPos, toPos, edgePaint);
      }
    }

    // Draw nodes
    for (final node in nodes) {
      final nodePos = nodePositions[node];
      if (nodePos == null) continue;

      // Determine node color
      Color nodeColor;
      if (node == currentNodeValue) {
        nodeColor = currentNodeColor;
      } else if (visitedNodeValues.contains(node)) {
        nodeColor = visitedNodeColor;
      } else {
        nodeColor = primaryColor;
      }

      // Draw node circle
      final nodePaint = Paint()
        ..color = nodeColor
        ..style = PaintingStyle.fill;
      canvas.drawCircle(nodePos, 20, nodePaint);

      // Draw node border
      final borderPaint = Paint()
        ..color = nodeColor.withAlpha(200)
        ..style = PaintingStyle.stroke
        ..strokeWidth = 2;
      canvas.drawCircle(nodePos, 20, borderPaint);

      // Draw node value
      final textStyle = TextStyle(
        color: Colors.white,
        fontSize: 14,
        fontWeight: FontWeight.bold,
      );
      final textSpan = TextSpan(
        text: node.toString(),
        style: textStyle,
      );
      final textPainter = TextPainter(
        text: textSpan,
        textDirection: TextDirection.ltr,
      );
      textPainter.layout();
      textPainter.paint(
        canvas,
        Offset(
          nodePos.dx - textPainter.width / 2,
          nodePos.dy - textPainter.height / 2,
        ),
      );
    }
  }

  // Draw an arrow from start to end
  void _drawArrow(Canvas canvas, Offset start, Offset end, Paint paint) {
    // Calculate direction vector
    final dx = end.dx - start.dx;
    final dy = end.dy - start.dy;
    final distance = math.sqrt(dx * dx + dy * dy);

    // Normalize direction vector
    final dirX = dx / distance;
    final dirY = dy / distance;

    // Calculate start and end points (adjusted for node radius)
    final nodeRadius = 20.0;
    final adjustedStart = Offset(
      start.dx + dirX * nodeRadius,
      start.dy + dirY * nodeRadius,
    );
    final adjustedEnd = Offset(
      end.dx - dirX * nodeRadius,
      end.dy - dirY * nodeRadius,
    );

    // Draw the line
    canvas.drawLine(adjustedStart, adjustedEnd, paint);

    // Draw arrowhead
    final arrowSize = 10.0;
    final arrowAngle = 0.5; // 30 degrees in radians

    // Calculate arrowhead points
    final arrowPoint1 = Offset(
      adjustedEnd.dx - arrowSize * (dirX * math.cos(arrowAngle) - dirY * math.sin(arrowAngle)),
      adjustedEnd.dy - arrowSize * (dirY * math.cos(arrowAngle) + dirX * math.sin(arrowAngle)),
    );

    final arrowPoint2 = Offset(
      adjustedEnd.dx - arrowSize * (dirX * math.cos(arrowAngle) + dirY * math.sin(arrowAngle)),
      adjustedEnd.dy - arrowSize * (dirY * math.cos(arrowAngle) - dirX * math.sin(arrowAngle)),
    );

    // Draw arrowhead
    final arrowPath = Path()
      ..moveTo(adjustedEnd.dx, adjustedEnd.dy)
      ..lineTo(arrowPoint1.dx, arrowPoint1.dy)
      ..lineTo(arrowPoint2.dx, arrowPoint2.dy)
      ..close();

    canvas.drawPath(arrowPath, paint);
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => true;
}

/// Linked list painter
class LinkedListPainter extends CustomPainter {
  final List<int> nodes;
  final int currentNodeIndex;
  final List<int> visitedNodeIndices;
  final Color primaryColor;
  final Color visitedNodeColor;
  final Color currentNodeColor;
  final Color edgeColor;
  final Color textColor;

  LinkedListPainter({
    required this.nodes,
    required this.currentNodeIndex,
    required this.visitedNodeIndices,
    required this.primaryColor,
    required this.visitedNodeColor,
    required this.currentNodeColor,
    required this.edgeColor,
    required this.textColor,
  });

  @override
  void paint(Canvas canvas, Size size) {
    final width = size.width;
    final height = size.height;

    if (nodes.isEmpty) return;

    // Calculate node size and spacing
    final nodeWidth = 60.0;
    final nodeHeight = 40.0;
    final nodeSpacing = 40.0;
    final totalWidth = nodes.length * nodeWidth + (nodes.length - 1) * nodeSpacing;

    // Calculate starting x position to center the linked list
    final startX = (width - totalWidth) / 2;
    final centerY = height / 2;

    // Draw nodes and arrows
    for (int i = 0; i < nodes.length; i++) {
      final nodeX = startX + i * (nodeWidth + nodeSpacing);
      final nodeRect = Rect.fromLTWH(
        nodeX,
        centerY - nodeHeight / 2,
        nodeWidth,
        nodeHeight,
      );

      // Determine node color
      Color nodeColor;
      if (i == currentNodeIndex) {
        nodeColor = currentNodeColor;
      } else if (visitedNodeIndices.contains(i)) {
        nodeColor = visitedNodeColor;
      } else {
        nodeColor = primaryColor;
      }

      // Draw node rectangle
      final nodePaint = Paint()
        ..color = nodeColor
        ..style = PaintingStyle.fill;
      canvas.drawRRect(
        RRect.fromRectAndRadius(nodeRect, const Radius.circular(8)),
        nodePaint,
      );

      // Draw node border
      final borderPaint = Paint()
        ..color = nodeColor.withAlpha(200)
        ..style = PaintingStyle.stroke
        ..strokeWidth = 2;
      canvas.drawRRect(
        RRect.fromRectAndRadius(nodeRect, const Radius.circular(8)),
        borderPaint,
      );

      // Draw node value
      final textStyle = TextStyle(
        color: Colors.white,
        fontSize: 14,
        fontWeight: FontWeight.bold,
      );
      final textSpan = TextSpan(
        text: nodes[i].toString(),
        style: textStyle,
      );
      final textPainter = TextPainter(
        text: textSpan,
        textDirection: TextDirection.ltr,
      );
      textPainter.layout();
      textPainter.paint(
        canvas,
        Offset(
          nodeX + (nodeWidth - textPainter.width) / 2,
          centerY - textPainter.height / 2,
        ),
      );

      // Draw arrow to next node
      if (i < nodes.length - 1) {
        final arrowPaint = Paint()
          ..color = edgeColor
          ..style = PaintingStyle.stroke
          ..strokeWidth = 2;

        final arrowStartX = nodeX + nodeWidth;
        final arrowEndX = nodeX + nodeWidth + nodeSpacing;

        // Draw arrow line
        canvas.drawLine(
          Offset(arrowStartX, centerY),
          Offset(arrowEndX, centerY),
          arrowPaint,
        );

        // Draw arrowhead
        final arrowSize = 8.0;
        final arrowPath = Path()
          ..moveTo(arrowEndX, centerY)
          ..lineTo(arrowEndX - arrowSize, centerY - arrowSize / 2)
          ..lineTo(arrowEndX - arrowSize, centerY + arrowSize / 2)
          ..close();

        canvas.drawPath(arrowPath, arrowPaint);
      }
    }
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => true;
}

/// A widget that visualizes traversal algorithms on data structures
class InteractiveDataStructureTraversalVisualizerWidget extends StatefulWidget {
  /// The data for this widget
  final Map<String, dynamic> data;

  /// Optional callback when the widget state changes
  final Function(bool isCompleted)? onStateChanged;

  const InteractiveDataStructureTraversalVisualizerWidget({
    super.key,
    required this.data,
    this.onStateChanged,
  });

  /// Factory constructor to create the widget from JSON data
  factory InteractiveDataStructureTraversalVisualizerWidget.fromData(
      Map<String, dynamic> data) {
    return InteractiveDataStructureTraversalVisualizerWidget(
      data: data,
    );
  }

  @override
  State<InteractiveDataStructureTraversalVisualizerWidget> createState() =>
      _InteractiveDataStructureTraversalVisualizerWidgetState();
}

class _InteractiveDataStructureTraversalVisualizerWidgetState
    extends State<InteractiveDataStructureTraversalVisualizerWidget>
    with SingleTickerProviderStateMixin {
  // Colors
  late Color _primaryColor;
  late Color _textColor;
  late Color _visitedNodeColor;
  late Color _currentNodeColor;
  late Color _edgeColor;
  late Color _backgroundColor;

  // Data structure types
  late List<String> _dataStructureTypes;
  late String _currentDataStructureType;

  // Traversal algorithms
  late List<String> _traversalAlgorithms;
  late String _currentTraversalAlgorithm;

  // Animation controller
  late AnimationController _animationController;

  // UI state
  bool _isTraversing = false;
  bool _showExplanation = false;
  String _statusMessage = '';
  List<int> _traversalOrder = [];
  int _currentNodeIndex = -1;
  int _visitedNodesCount = 0;

  // Timer for traversal animation
  Timer? _traversalTimer;

  // Binary tree data
  late List<TreeNode> _treeNodes;
  late int _rootNodeValue;

  // Graph data
  late List<int> _graphNodes;
  late List<GraphEdge> _graphEdges;
  late int _startNodeValue;

  // Linked list data
  late List<int> _linkedListNodes;

  // Speed control
  late double _traversalSpeed;

  @override
  void initState() {
    super.initState();

    // Initialize colors
    _primaryColor = _getColorFromHex(
        widget.data['primaryColor'] ?? '#2196F3'); // Blue
    _textColor =
        _getColorFromHex(widget.data['textColor'] ?? '#212121'); // Dark Grey
    _visitedNodeColor =
        _getColorFromHex(widget.data['visitedNodeColor'] ?? '#4CAF50'); // Green
    _currentNodeColor =
        _getColorFromHex(widget.data['currentNodeColor'] ?? '#FF9800'); // Orange
    _edgeColor =
        _getColorFromHex(widget.data['edgeColor'] ?? '#757575'); // Grey
    _backgroundColor = _getColorFromHex(
        widget.data['backgroundColor'] ?? '#FFFFFF'); // White

    // Initialize data structure types
    _dataStructureTypes = [
      'Binary Tree',
      'Graph',
      'Linked List',
    ];
    _currentDataStructureType = widget.data['defaultDataStructure'] ?? _dataStructureTypes[0];

    // Initialize traversal algorithms
    _traversalAlgorithms = _getTraversalAlgorithmsForDataStructure();
    _currentTraversalAlgorithm = _traversalAlgorithms[0];

    // Initialize animation controller
    _animationController = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 500),
    );

    // Initialize traversal speed
    _traversalSpeed = widget.data['defaultSpeed']?.toDouble() ?? 1.0;

    // Initialize data structures
    _initializeDataStructures();
  }

  @override
  void dispose() {
    _animationController.dispose();
    _traversalTimer?.cancel();
    super.dispose();
  }

  // Convert hex color string to Color
  Color _getColorFromHex(String hexColor) {
    hexColor = hexColor.replaceAll('#', '');
    if (hexColor.length == 6) {
      hexColor = 'FF$hexColor';
    }
    return Color(int.parse(hexColor, radix: 16));
  }

  // Initialize data structures
  void _initializeDataStructures() {
    // Initialize binary tree
    _treeNodes = [
      TreeNode(value: 10, left: 5, right: 15),
      TreeNode(value: 5, left: 3, right: 7),
      TreeNode(value: 15, left: 12, right: 18),
      TreeNode(value: 3, left: null, right: null),
      TreeNode(value: 7, left: null, right: null),
      TreeNode(value: 12, left: null, right: null),
      TreeNode(value: 18, left: null, right: null),
    ];
    _rootNodeValue = 10;

    // Initialize graph
    _graphNodes = [1, 2, 3, 4, 5, 6];
    _graphEdges = [
      GraphEdge(from: 1, to: 2),
      GraphEdge(from: 1, to: 3),
      GraphEdge(from: 2, to: 4),
      GraphEdge(from: 2, to: 5),
      GraphEdge(from: 3, to: 5),
      GraphEdge(from: 4, to: 6),
      GraphEdge(from: 5, to: 6),
    ];
    _startNodeValue = 1;

    // Initialize linked list
    _linkedListNodes = [10, 20, 30, 40, 50];

    // Reset traversal state
    _resetTraversalState();
  }

  // Reset traversal state
  void _resetTraversalState() {
    setState(() {
      _traversalOrder = [];
      _currentNodeIndex = -1;
      _visitedNodesCount = 0;
      _isTraversing = false;
      _statusMessage = '';
    });
  }

  // Get traversal algorithms for the current data structure
  List<String> _getTraversalAlgorithmsForDataStructure() {
    switch (_currentDataStructureType) {
      case 'Binary Tree':
        return ['In-order', 'Pre-order', 'Post-order', 'Level-order'];
      case 'Graph':
        return ['Breadth-First Search', 'Depth-First Search'];
      case 'Linked List':
        return ['Forward Traversal', 'Recursive Traversal'];
      default:
        return [];
    }
  }

  // Start traversal
  void _startTraversal() {
    if (_isTraversing) return;

    setState(() {
      _isTraversing = true;
      _traversalOrder = [];
      _currentNodeIndex = -1;
      _visitedNodesCount = 0;
      _statusMessage = 'Traversing with $_currentTraversalAlgorithm...';
    });

    // Calculate traversal order based on algorithm
    switch (_currentDataStructureType) {
      case 'Binary Tree':
        _calculateTreeTraversalOrder();
        break;
      case 'Graph':
        _calculateGraphTraversalOrder();
        break;
      case 'Linked List':
        _calculateLinkedListTraversalOrder();
        break;
    }

    // Start animation
    _animateTraversal();
  }

  // Calculate tree traversal order
  void _calculateTreeTraversalOrder() {
    switch (_currentTraversalAlgorithm) {
      case 'In-order':
        _traversalOrder = [];
        _inOrderTraversal(_rootNodeValue);
        break;
      case 'Pre-order':
        _traversalOrder = [];
        _preOrderTraversal(_rootNodeValue);
        break;
      case 'Post-order':
        _traversalOrder = [];
        _postOrderTraversal(_rootNodeValue);
        break;
      case 'Level-order':
        _traversalOrder = _levelOrderTraversal(_rootNodeValue);
        break;
    }
  }

  // In-order traversal: Left -> Root -> Right
  void _inOrderTraversal(int? nodeValue) {
    if (nodeValue == null) return;

    final node = _treeNodes.firstWhere((n) => n.value == nodeValue);
    _inOrderTraversal(node.left);
    _traversalOrder.add(nodeValue);
    _inOrderTraversal(node.right);
  }

  // Pre-order traversal: Root -> Left -> Right
  void _preOrderTraversal(int? nodeValue) {
    if (nodeValue == null) return;

    final node = _treeNodes.firstWhere((n) => n.value == nodeValue);
    _traversalOrder.add(nodeValue);
    _preOrderTraversal(node.left);
    _preOrderTraversal(node.right);
  }

  // Post-order traversal: Left -> Right -> Root
  void _postOrderTraversal(int? nodeValue) {
    if (nodeValue == null) return;

    final node = _treeNodes.firstWhere((n) => n.value == nodeValue);
    _postOrderTraversal(node.left);
    _postOrderTraversal(node.right);
    _traversalOrder.add(nodeValue);
  }

  // Level-order traversal (Breadth-First)
  List<int> _levelOrderTraversal(int rootValue) {
    final result = <int>[];
    final queue = <int>[];

    queue.add(rootValue);

    while (queue.isNotEmpty) {
      final nodeValue = queue.removeAt(0);
      result.add(nodeValue);

      final node = _treeNodes.firstWhere((n) => n.value == nodeValue);
      if (node.left != null) queue.add(node.left!);
      if (node.right != null) queue.add(node.right!);
    }

    return result;
  }

  // Calculate graph traversal order
  void _calculateGraphTraversalOrder() {
    switch (_currentTraversalAlgorithm) {
      case 'Breadth-First Search':
        _traversalOrder = _breadthFirstSearch(_startNodeValue);
        break;
      case 'Depth-First Search':
        _traversalOrder = [];
        final visited = <int>{};
        _depthFirstSearch(_startNodeValue, visited);
        break;
    }
  }

  // Breadth-First Search
  List<int> _breadthFirstSearch(int startNode) {
    final result = <int>[];
    final visited = <int>{};
    final queue = <int>[];

    visited.add(startNode);
    queue.add(startNode);

    while (queue.isNotEmpty) {
      final node = queue.removeAt(0);
      result.add(node);

      // Get neighbors
      final neighbors = _graphEdges
          .where((edge) => edge.from == node)
          .map((edge) => edge.to)
          .toList();

      for (final neighbor in neighbors) {
        if (!visited.contains(neighbor)) {
          visited.add(neighbor);
          queue.add(neighbor);
        }
      }
    }

    return result;
  }

  // Depth-First Search
  void _depthFirstSearch(int node, Set<int> visited) {
    visited.add(node);
    _traversalOrder.add(node);

    // Get neighbors
    final neighbors = _graphEdges
        .where((edge) => edge.from == node)
        .map((edge) => edge.to)
        .toList();

    for (final neighbor in neighbors) {
      if (!visited.contains(neighbor)) {
        _depthFirstSearch(neighbor, visited);
      }
    }
  }

  // Calculate linked list traversal order
  void _calculateLinkedListTraversalOrder() {
    switch (_currentTraversalAlgorithm) {
      case 'Forward Traversal':
        _traversalOrder = List.from(_linkedListNodes);
        break;
      case 'Recursive Traversal':
        _traversalOrder = [];
        _recursiveLinkedListTraversal(0);
        break;
    }
  }

  // Recursive linked list traversal
  void _recursiveLinkedListTraversal(int index) {
    if (index >= _linkedListNodes.length) return;

    _traversalOrder.add(_linkedListNodes[index]);
    _recursiveLinkedListTraversal(index + 1);
  }

  // Animate traversal
  void _animateTraversal() {
    if (_traversalOrder.isEmpty) {
      setState(() {
        _isTraversing = false;
        _statusMessage = 'No nodes to traverse';
      });
      return;
    }

    _currentNodeIndex = 0;
    _visitedNodesCount = 0;

    // Calculate delay based on traversal speed (500ms to 2000ms)
    final delay = (2000 - _traversalSpeed * 1500).toInt();

    _traversalTimer = Timer.periodic(Duration(milliseconds: delay), (timer) {
      setState(() {
        if (_currentNodeIndex < _traversalOrder.length) {
          _visitedNodesCount = _currentNodeIndex + 1;
          _currentNodeIndex++;
        } else {
          _currentNodeIndex = -1;
          _isTraversing = false;
          _statusMessage = 'Traversal complete';
          timer.cancel();
        }
      });
    });
  }

  // Stop traversal
  void _stopTraversal() {
    _traversalTimer?.cancel();
    setState(() {
      _isTraversing = false;
      _statusMessage = 'Traversal stopped';
    });
  }

  // Build data structure and algorithm selectors
  Widget _buildSelectors() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Data structure selector
        Text(
          'Data Structure',
          style: TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.bold,
            color: _textColor,
          ),
        ),
        const SizedBox(height: 8),
        SingleChildScrollView(
          scrollDirection: Axis.horizontal,
          child: Row(
            children: _dataStructureTypes.map((type) {
              final isSelected = type == _currentDataStructureType;
              return Padding(
                padding: const EdgeInsets.only(right: 8),
                child: ChoiceChip(
                  label: Text(type),
                  selected: isSelected,
                  onSelected: (selected) {
                    if (selected && !_isTraversing) {
                      setState(() {
                        _currentDataStructureType = type;
                        _traversalAlgorithms = _getTraversalAlgorithmsForDataStructure();
                        _currentTraversalAlgorithm = _traversalAlgorithms[0];
                        _resetTraversalState();
                      });
                    } else if (_isTraversing) {
                      setState(() {
                        _statusMessage = 'Stop traversal before changing data structure';
                      });
                    }
                  },
                  backgroundColor: Colors.grey.withAlpha(50),
                  selectedColor: _primaryColor.withAlpha(100),
                ),
              );
            }).toList(),
          ),
        ),

        const SizedBox(height: 16),

        // Traversal algorithm selector
        Text(
          'Traversal Algorithm',
          style: TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.bold,
            color: _textColor,
          ),
        ),
        const SizedBox(height: 8),
        SingleChildScrollView(
          scrollDirection: Axis.horizontal,
          child: Row(
            children: _traversalAlgorithms.map((algorithm) {
              final isSelected = algorithm == _currentTraversalAlgorithm;
              return Padding(
                padding: const EdgeInsets.only(right: 8),
                child: ChoiceChip(
                  label: Text(algorithm),
                  selected: isSelected,
                  onSelected: (selected) {
                    if (selected && !_isTraversing) {
                      setState(() {
                        _currentTraversalAlgorithm = algorithm;
                        _resetTraversalState();
                      });
                    } else if (_isTraversing) {
                      setState(() {
                        _statusMessage = 'Stop traversal before changing algorithm';
                      });
                    }
                  },
                  backgroundColor: Colors.grey.withAlpha(50),
                  selectedColor: _primaryColor.withAlpha(100),
                ),
              );
            }).toList(),
          ),
        ),
      ],
    );
  }

  // Build traversal controls
  Widget _buildTraversalControls() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Controls',
          style: TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.bold,
            color: _textColor,
          ),
        ),
        const SizedBox(height: 8),
        Row(
          children: [
            // Start button
            ElevatedButton.icon(
              onPressed: _isTraversing ? null : _startTraversal,
              icon: const Icon(Icons.play_arrow),
              label: const Text('Start'),
              style: ElevatedButton.styleFrom(
                backgroundColor: _primaryColor,
                foregroundColor: Colors.white,
              ),
            ),
            const SizedBox(width: 16),
            // Stop button
            ElevatedButton.icon(
              onPressed: _isTraversing ? _stopTraversal : null,
              icon: const Icon(Icons.stop),
              label: const Text('Stop'),
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.red,
                foregroundColor: Colors.white,
              ),
            ),
            const SizedBox(width: 16),
            // Reset button
            ElevatedButton.icon(
              onPressed: _isTraversing ? null : _resetTraversalState,
              icon: const Icon(Icons.refresh),
              label: const Text('Reset'),
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.grey,
                foregroundColor: Colors.white,
              ),
            ),
          ],
        ),
        const SizedBox(height: 16),
        // Speed slider
        Row(
          children: [
            const Text('Speed:'),
            const SizedBox(width: 8),
            Expanded(
              child: Slider(
                value: _traversalSpeed,
                min: 0.1,
                max: 1.0,
                divisions: 9,
                label: _traversalSpeed.toStringAsFixed(1),
                onChanged: (value) {
                  setState(() {
                    _traversalSpeed = value;
                  });
                },
                activeColor: _primaryColor,
              ),
            ),
            const Text('Fast'),
          ],
        ),
      ],
    );
  }

  // Build visualization area
  Widget _buildVisualizationArea() {
    return Container(
      height: 250,
      width: double.infinity,
      decoration: BoxDecoration(
        color: Colors.grey.withAlpha(20),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: Colors.grey.withAlpha(100)),
      ),
      child: _buildVisualization(),
    );
  }

  // Build visualization based on data structure type
  Widget _buildVisualization() {
    switch (_currentDataStructureType) {
      case 'Binary Tree':
        return _buildTreeVisualization();
      case 'Graph':
        return _buildGraphVisualization();
      case 'Linked List':
        return _buildLinkedListVisualization();
      default:
        return const Center(
          child: Text('Select a data structure type'),
        );
    }
  }

  // Build binary tree visualization
  Widget _buildTreeVisualization() {
    return CustomPaint(
      painter: TreePainter(
        nodes: _treeNodes,
        rootValue: _rootNodeValue,
        currentNodeValue: _currentNodeIndex >= 0 && _currentNodeIndex < _traversalOrder.length
            ? _traversalOrder[_currentNodeIndex]
            : null,
        visitedNodeValues: _traversalOrder.sublist(
            0, math.min(_visitedNodesCount, _traversalOrder.length)),
        primaryColor: _primaryColor,
        visitedNodeColor: _visitedNodeColor,
        currentNodeColor: _currentNodeColor,
        edgeColor: _edgeColor,
        textColor: _textColor,
      ),
    );
  }

  // Build graph visualization
  Widget _buildGraphVisualization() {
    return CustomPaint(
      painter: GraphPainter(
        nodes: _graphNodes,
        edges: _graphEdges,
        currentNodeValue: _currentNodeIndex >= 0 && _currentNodeIndex < _traversalOrder.length
            ? _traversalOrder[_currentNodeIndex]
            : null,
        visitedNodeValues: _traversalOrder.sublist(
            0, math.min(_visitedNodesCount, _traversalOrder.length)),
        primaryColor: _primaryColor,
        visitedNodeColor: _visitedNodeColor,
        currentNodeColor: _currentNodeColor,
        edgeColor: _edgeColor,
        textColor: _textColor,
      ),
    );
  }

  // Build linked list visualization
  Widget _buildLinkedListVisualization() {
    return CustomPaint(
      painter: LinkedListPainter(
        nodes: _linkedListNodes,
        currentNodeIndex: _currentNodeIndex >= 0 && _currentNodeIndex < _traversalOrder.length
            ? _linkedListNodes.indexOf(_traversalOrder[_currentNodeIndex])
            : -1,
        visitedNodeIndices: _traversalOrder
            .sublist(0, math.min(_visitedNodesCount, _traversalOrder.length))
            .map((value) => _linkedListNodes.indexOf(value))
            .where((index) => index >= 0)
            .toList(),
        primaryColor: _primaryColor,
        visitedNodeColor: _visitedNodeColor,
        currentNodeColor: _currentNodeColor,
        edgeColor: _edgeColor,
        textColor: _textColor,
      ),
    );
  }

  // Build traversal order display
  Widget _buildTraversalOrderDisplay() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Traversal Order',
          style: TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.bold,
            color: _textColor,
          ),
        ),
        const SizedBox(height: 8),
        Container(
          width: double.infinity,
          padding: const EdgeInsets.all(12),
          decoration: BoxDecoration(
            color: Colors.grey.withAlpha(20),
            borderRadius: BorderRadius.circular(8),
            border: Border.all(color: Colors.grey.withAlpha(100)),
          ),
          child: Text(
            _traversalOrder.isEmpty
                ? 'Start traversal to see the order'
                : _traversalOrder.map((value) => value.toString()).join(' → '),
            style: TextStyle(
              color: _textColor,
              fontWeight: FontWeight.w500,
            ),
          ),
        ),
      ],
    );
  }

  // Build explanation panel
  Widget _buildExplanation() {
    return ExpansionTile(
      title: const Text('Explanation'),
      children: [
        Padding(
          padding: const EdgeInsets.all(16.0),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                _getExplanationForAlgorithm(),
                style: TextStyle(
                  color: _textColor,
                  fontSize: 14,
                ),
              ),
              const SizedBox(height: 16),
              Align(
                alignment: Alignment.centerRight,
                child: TextButton(
                  onPressed: () {
                    widget.onStateChanged?.call(true);
                  },
                  child: const Text('Mark as Completed'),
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }

  // Get explanation for the current algorithm
  String _getExplanationForAlgorithm() {
    switch (_currentTraversalAlgorithm) {
      case 'In-order':
        return 'In-order traversal visits nodes in the order: left subtree, root, right subtree. '
            'It visits the nodes in ascending order for a binary search tree. '
            'Time complexity: O(n), where n is the number of nodes.';
      case 'Pre-order':
        return 'Pre-order traversal visits nodes in the order: root, left subtree, right subtree. '
            'It is useful for creating a copy of the tree or prefix expression of an expression tree. '
            'Time complexity: O(n), where n is the number of nodes.';
      case 'Post-order':
        return 'Post-order traversal visits nodes in the order: left subtree, right subtree, root. '
            'It is useful for deleting the tree or postfix expression of an expression tree. '
            'Time complexity: O(n), where n is the number of nodes.';
      case 'Level-order':
        return 'Level-order traversal visits nodes level by level from top to bottom. '
            'It uses a queue to keep track of nodes to visit next. '
            'Time complexity: O(n), where n is the number of nodes.';
      case 'Breadth-First Search':
        return 'Breadth-First Search (BFS) explores all neighbor nodes at the present depth before moving to nodes at the next depth level. '
            'It uses a queue to keep track of nodes to visit next. '
            'Time complexity: O(V+E), where V is the number of vertices and E is the number of edges.';
      case 'Depth-First Search':
        return 'Depth-First Search (DFS) explores as far as possible along each branch before backtracking. '
            'It uses recursion or a stack to keep track of nodes to visit next. '
            'Time complexity: O(V+E), where V is the number of vertices and E is the number of edges.';
      case 'Forward Traversal':
        return 'Forward traversal of a linked list visits each node from head to tail. '
            'It is a simple iteration through the list. '
            'Time complexity: O(n), where n is the number of nodes.';
      case 'Recursive Traversal':
        return 'Recursive traversal of a linked list uses recursion to visit each node. '
            'It demonstrates how recursion can be used for traversal. '
            'Time complexity: O(n), where n is the number of nodes. Space complexity: O(n) for the call stack.';
      default:
        return 'Select an algorithm to see its explanation.';
    }
  }

  @override
  Widget build(BuildContext context) {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
        side: BorderSide(color: _primaryColor.withAlpha(77)),
      ),
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Title and description
            Text(
              widget.data['title'] ?? 'Data Structure Traversal Visualizer',
              style: TextStyle(
                fontSize: 20,
                fontWeight: FontWeight.bold,
                color: _textColor,
              ),
            ),
            const SizedBox(height: 8),
            Text(
              widget.data['description'] ??
                  'Visualize different traversal algorithms on data structures',
              style: TextStyle(
                fontSize: 14,
                color: _textColor.withAlpha(179),
              ),
            ),
            const SizedBox(height: 16),

            // Data structure and algorithm selectors
            _buildSelectors(),

            const SizedBox(height: 16),

            // Traversal controls
            _buildTraversalControls(),

            const SizedBox(height: 16),

            // Visualization area
            _buildVisualizationArea(),

            const SizedBox(height: 16),

            // Traversal order
            _buildTraversalOrderDisplay(),

            const SizedBox(height: 16),

            // Status message
            if (_statusMessage.isNotEmpty)
              Container(
                width: double.infinity,
                padding: const EdgeInsets.all(8),
                decoration: BoxDecoration(
                  color: _primaryColor.withAlpha(26),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Text(
                  _statusMessage,
                  style: TextStyle(
                    color: _primaryColor,
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ),

            const SizedBox(height: 16),

            // Explanation
            _buildExplanation(),
          ],
        ),
      ),
    );
  }
}
