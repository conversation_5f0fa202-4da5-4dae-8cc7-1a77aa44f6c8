import 'package:flutter/material.dart';
import 'dart:math' as math;

/// A widget that allows users to evaluate scientific evidence
class InteractiveEvidenceEvaluatorWidget extends StatefulWidget {
  /// The data for this widget
  final Map<String, dynamic> data;

  /// Optional callback when the widget state changes
  final Function(bool isCompleted)? onStateChanged;

  const InteractiveEvidenceEvaluatorWidget({
    super.key,
    required this.data,
    this.onStateChanged,
  });

  /// Factory constructor to create the widget from JSON data
  factory InteractiveEvidenceEvaluatorWidget.fromData(Map<String, dynamic> data) {
    return InteractiveEvidenceEvaluatorWidget(
      data: data,
    );
  }

  @override
  State<InteractiveEvidenceEvaluatorWidget> createState() => _InteractiveEvidenceEvaluatorWidgetState();
}

class _InteractiveEvidenceEvaluatorWidgetState extends State<InteractiveEvidenceEvaluatorWidget> {
  // Evidence and scenarios
  late List<EvidenceScenario> _scenarios;
  late int _currentScenarioIndex;
  
  // Evaluation state
  late Map<String, EvidenceRating> _evidenceRatings;
  late bool _hasSubmitted;
  late String _feedback;
  late bool _isCorrect;
  
  // UI state
  late bool _isCompleted;
  late bool _showExplanation;
  
  // Colors
  late Color _primaryColor;
  late Color _secondaryColor;
  late Color _accentColor;
  late Color _backgroundColor;
  late Color _textColor;

  @override
  void initState() {
    super.initState();
    _initializeWidget();
  }

  void _initializeWidget() {
    // Initialize colors
    _primaryColor = _parseColor(widget.data['primaryColor'] ?? '#2196F3');
    _secondaryColor = _parseColor(widget.data['secondaryColor'] ?? '#FF9800');
    _accentColor = _parseColor(widget.data['accentColor'] ?? '#4CAF50');
    _backgroundColor = _parseColor(widget.data['backgroundColor'] ?? '#FFFFFF');
    _textColor = _parseColor(widget.data['textColor'] ?? '#212121');

    // Initialize scenarios
    final List<dynamic> scenariosData = widget.data['scenarios'] ?? [];
    _scenarios = scenariosData.map((scenarioData) => EvidenceScenario.fromJson(scenarioData)).toList();
    _currentScenarioIndex = 0;
    
    // Initialize evaluation state
    _resetEvaluation();
  }

  void _resetEvaluation() {
    if (_scenarios.isEmpty) return;
    
    EvidenceScenario scenario = _scenarios[_currentScenarioIndex];
    _evidenceRatings = {};
    
    for (var evidence in scenario.evidenceItems) {
      _evidenceRatings[evidence.id] = EvidenceRating.neutral;
    }
    
    _hasSubmitted = false;
    _feedback = '';
    _isCorrect = false;
    _isCompleted = false;
    _showExplanation = false;
  }

  Color _parseColor(String colorString) {
    if (colorString.startsWith('#')) {
      return Color(int.parse('FF${colorString.substring(1)}', radix: 16));
    }
    return Colors.blue;
  }

  void _updateEvidenceRating(String evidenceId, EvidenceRating rating) {
    setState(() {
      _evidenceRatings[evidenceId] = rating;
    });
  }

  void _submitEvaluation() {
    if (_scenarios.isEmpty) return;
    
    EvidenceScenario scenario = _scenarios[_currentScenarioIndex];
    bool allCorrect = true;
    
    // Check if all evidence items are rated correctly
    for (var evidence in scenario.evidenceItems) {
      EvidenceRating? userRating = _evidenceRatings[evidence.id];
      if (userRating != evidence.correctRating) {
        allCorrect = false;
        break;
      }
    }
    
    _isCorrect = allCorrect;
    _feedback = allCorrect ? scenario.correctFeedback : scenario.incorrectFeedback;
    
    setState(() {
      _hasSubmitted = true;
    });
  }

  void _nextScenario() {
    if (_currentScenarioIndex < _scenarios.length - 1) {
      setState(() {
        _currentScenarioIndex++;
        _resetEvaluation();
      });
    } else if (!_isCompleted) {
      setState(() {
        _isCompleted = true;
      });
      widget.onStateChanged?.call(true);
    }
  }

  void _toggleExplanation() {
    setState(() {
      _showExplanation = !_showExplanation;
    });
  }

  @override
  Widget build(BuildContext context) {
    if (_scenarios.isEmpty) {
      return const Center(child: Text('No scenarios available'));
    }

    EvidenceScenario scenario = _scenarios[_currentScenarioIndex];

    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
        side: BorderSide(color: _primaryColor.withOpacity(0.5), width: 1),
      ),
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Title
            Text(
              widget.data['title'] ?? 'Evidence Evaluator',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
                color: _textColor,
              ),
            ),

            const SizedBox(height: 8),

            // Scenario navigation
            Row(
              children: [
                Text(
                  'Scenario ${_currentScenarioIndex + 1} of ${_scenarios.length}',
                  style: TextStyle(
                    fontSize: 14,
                    fontWeight: FontWeight.bold,
                    color: _primaryColor,
                  ),
                ),
                const Spacer(),
                IconButton(
                  icon: Icon(_showExplanation ? Icons.info_outline : Icons.info),
                  onPressed: _hasSubmitted ? _toggleExplanation : null,
                  tooltip: _showExplanation ? 'Hide Explanation' : 'Show Explanation',
                  color: _secondaryColor,
                ),
              ],
            ),

            const SizedBox(height: 8),

            // Scenario description
            Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: _backgroundColor,
                borderRadius: BorderRadius.circular(8),
                border: Border.all(color: _primaryColor.withOpacity(0.3)),
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'Claim: ${scenario.claim}',
                    style: TextStyle(
                      fontWeight: FontWeight.bold,
                      color: _textColor,
                    ),
                  ),
                  const SizedBox(height: 4),
                  Text(
                    scenario.description,
                    style: TextStyle(color: _textColor.withOpacity(0.8)),
                  ),
                ],
              ),
            ),

            const SizedBox(height: 16),

            // Instructions
            Text(
              'Evaluate each piece of evidence:',
              style: TextStyle(
                fontWeight: FontWeight.bold,
                color: _textColor,
              ),
            ),
            Text(
              'Supports (strengthens the claim), Contradicts (weakens the claim), or Not Relevant',
              style: TextStyle(
                fontSize: 12,
                color: _textColor.withOpacity(0.7),
              ),
            ),

            const SizedBox(height: 16),

            // Evidence items
            ...scenario.evidenceItems.map((evidence) => _buildEvidenceItem(evidence)),

            const SizedBox(height: 16),

            // Submit button
            if (!_hasSubmitted)
              Center(
                child: ElevatedButton(
                  onPressed: _submitEvaluation,
                  style: ElevatedButton.styleFrom(
                    backgroundColor: _primaryColor,
                    foregroundColor: Colors.white,
                    padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(8),
                    ),
                  ),
                  child: const Text('Submit Evaluation'),
                ),
              ),

            // Feedback
            if (_hasSubmitted) ...[
              Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: _isCorrect ? Colors.green.withOpacity(0.1) : Colors.red.withOpacity(0.1),
                  borderRadius: BorderRadius.circular(8),
                  border: Border.all(color: _isCorrect ? Colors.green : Colors.red),
                ),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      children: [
                        Icon(
                          _isCorrect ? Icons.check_circle : Icons.error,
                          color: _isCorrect ? Colors.green : Colors.red,
                        ),
                        const SizedBox(width: 8),
                        Text(
                          _isCorrect ? 'Correct!' : 'Not quite right',
                          style: TextStyle(
                            fontWeight: FontWeight.bold,
                            color: _isCorrect ? Colors.green : Colors.red,
                          ),
                        ),
                      ],
                    ),
                    const SizedBox(height: 8),
                    Text(
                      _feedback,
                      style: TextStyle(color: _textColor.withOpacity(0.8)),
                    ),
                  ],
                ),
              ),

              // Expert explanation (if shown)
              if (_showExplanation) ...[
                const SizedBox(height: 16),
                Container(
                  padding: const EdgeInsets.all(12),
                  decoration: BoxDecoration(
                    color: _secondaryColor.withOpacity(0.1),
                    borderRadius: BorderRadius.circular(8),
                    border: Border.all(color: _secondaryColor.withOpacity(0.3)),
                  ),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'Expert Explanation:',
                        style: TextStyle(
                          fontWeight: FontWeight.bold,
                          color: _secondaryColor,
                        ),
                      ),
                      const SizedBox(height: 4),
                      Text(
                        scenario.explanation,
                        style: TextStyle(color: _textColor.withOpacity(0.8)),
                      ),
                    ],
                  ),
                ),
              ],

              const SizedBox(height: 16),

              // Navigation buttons
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                children: [
                  OutlinedButton(
                    onPressed: () => setState(() => _resetEvaluation()),
                    style: OutlinedButton.styleFrom(
                      foregroundColor: _primaryColor,
                      side: BorderSide(color: _primaryColor),
                      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                    ),
                    child: const Text('Try Again'),
                  ),
                  ElevatedButton(
                    onPressed: _nextScenario,
                    style: ElevatedButton.styleFrom(
                      backgroundColor: _primaryColor,
                      foregroundColor: Colors.white,
                      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                    ),
                    child: Text(_currentScenarioIndex < _scenarios.length - 1
                        ? 'Next Scenario'
                        : 'Finish'),
                  ),
                ],
              ),
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildEvidenceItem(Evidence evidence) {
    return Container(
      margin: const EdgeInsets.only(bottom: 12),
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: _backgroundColor,
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: _primaryColor.withOpacity(0.2)),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            evidence.text,
            style: TextStyle(
              fontWeight: FontWeight.w500,
              color: _textColor,
            ),
          ),
          const SizedBox(height: 8),
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceEvenly,
            children: [
              _buildRatingButton(
                evidence.id,
                EvidenceRating.supports,
                'Supports',
                Colors.green,
              ),
              _buildRatingButton(
                evidence.id,
                EvidenceRating.contradicts,
                'Contradicts',
                Colors.red,
              ),
              _buildRatingButton(
                evidence.id,
                EvidenceRating.notRelevant,
                'Not Relevant',
                Colors.grey,
              ),
            ],
          ),
          if (_hasSubmitted) ...[
            const SizedBox(height: 8),
            Row(
              children: [
                Text(
                  'Correct answer: ',
                  style: TextStyle(
                    fontWeight: FontWeight.bold,
                    fontSize: 12,
                    color: _textColor.withOpacity(0.7),
                  ),
                ),
                Text(
                  _getRatingText(evidence.correctRating),
                  style: TextStyle(
                    fontWeight: FontWeight.bold,
                    fontSize: 12,
                    color: _getRatingColor(evidence.correctRating),
                  ),
                ),
              ],
            ),
          ],
        ],
      ),
    );
  }

  Widget _buildRatingButton(
    String evidenceId,
    EvidenceRating rating,
    String label,
    Color color,
  ) {
    final bool isSelected = _evidenceRatings[evidenceId] == rating;
    
    return ElevatedButton(
      onPressed: _hasSubmitted ? null : () => _updateEvidenceRating(evidenceId, rating),
      style: ElevatedButton.styleFrom(
        backgroundColor: isSelected ? color : Colors.white,
        foregroundColor: isSelected ? Colors.white : color,
        side: BorderSide(color: color),
        padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(20),
        ),
      ),
      child: Text(label),
    );
  }

  String _getRatingText(EvidenceRating rating) {
    switch (rating) {
      case EvidenceRating.supports:
        return 'Supports';
      case EvidenceRating.contradicts:
        return 'Contradicts';
      case EvidenceRating.notRelevant:
        return 'Not Relevant';
      case EvidenceRating.neutral:
        return 'Not Rated';
    }
  }

  Color _getRatingColor(EvidenceRating rating) {
    switch (rating) {
      case EvidenceRating.supports:
        return Colors.green;
      case EvidenceRating.contradicts:
        return Colors.red;
      case EvidenceRating.notRelevant:
        return Colors.grey;
      case EvidenceRating.neutral:
        return Colors.grey;
    }
  }
}

/// Represents a scenario with a claim and evidence to evaluate
class EvidenceScenario {
  final String id;
  final String claim;
  final String description;
  final List<Evidence> evidenceItems;
  final String correctFeedback;
  final String incorrectFeedback;
  final String explanation;

  EvidenceScenario({
    required this.id,
    required this.claim,
    required this.description,
    required this.evidenceItems,
    required this.correctFeedback,
    required this.incorrectFeedback,
    required this.explanation,
  });

  factory EvidenceScenario.fromJson(Map<String, dynamic> json) {
    final List<dynamic> evidenceData = json['evidenceItems'] ?? [];
    final List<Evidence> evidenceItems = evidenceData
        .map((item) => Evidence.fromJson(item))
        .toList();

    return EvidenceScenario(
      id: json['id'] as String,
      claim: json['claim'] as String,
      description: json['description'] as String,
      evidenceItems: evidenceItems,
      correctFeedback: json['correctFeedback'] as String,
      incorrectFeedback: json['incorrectFeedback'] as String,
      explanation: json['explanation'] as String,
    );
  }
}

/// Represents a piece of evidence to evaluate
class Evidence {
  final String id;
  final String text;
  final EvidenceRating correctRating;

  Evidence({
    required this.id,
    required this.text,
    required this.correctRating,
  });

  factory Evidence.fromJson(Map<String, dynamic> json) {
    return Evidence(
      id: json['id'] as String,
      text: json['text'] as String,
      correctRating: _parseRating(json['correctRating'] as String),
    );
  }

  static EvidenceRating _parseRating(String rating) {
    switch (rating.toLowerCase()) {
      case 'supports':
        return EvidenceRating.supports;
      case 'contradicts':
        return EvidenceRating.contradicts;
      case 'not_relevant':
        return EvidenceRating.notRelevant;
      default:
        return EvidenceRating.neutral;
    }
  }
}

/// Enum representing the possible ratings for evidence
enum EvidenceRating {
  supports,
  contradicts,
  notRelevant,
  neutral,
}
