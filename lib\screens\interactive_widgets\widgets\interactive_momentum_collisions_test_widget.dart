import 'package:flutter/material.dart';
import 'dart:math' as math;
import 'dart:async';

class InteractiveMomentumCollisionsTestWidget extends StatefulWidget {
  final Map<String, dynamic>? data;

  const InteractiveMomentumCollisionsTestWidget({
    super.key,
    this.data,
  });

  factory InteractiveMomentumCollisionsTestWidget.fromData(
      Map<String, dynamic> data) {
    return InteractiveMomentumCollisionsTestWidget(
      data: data,
    );
  }

  @override
  State<InteractiveMomentumCollisionsTestWidget> createState() =>
      _InteractiveMomentumCollisionsTestWidgetState();
}

class _InteractiveMomentumCollisionsTestWidgetState
    extends State<InteractiveMomentumCollisionsTestWidget>
    with SingleTickerProviderStateMixin {
  // UI parameters
  late Color _primaryColor;
  late Color _secondaryColor;
  late Color _tertiaryColor;
  late Color _textColor;
  late Color _backgroundColor;
  late Color _correctColor;
  late Color _incorrectColor;

  // Test state
  bool _testStarted = false;
  bool _testCompleted = false;
  int _currentQuestionIndex = 0;
  List<bool> _questionAnswered = [];
  List<bool> _questionCorrect = [];
  List<String> _userAnswers = [];
  int _score = 0;
  int _maxScore = 0;

  // Timer
  int _timeRemaining = 600; // 10 minutes in seconds
  bool _timerActive = false;
  late DateTime _timerStartTime;

  // Animation controller
  late AnimationController _animationController;
  late Animation<double> _animation;

  // Questions
  late List<Map<String, dynamic>> _questions;

  @override
  void initState() {
    super.initState();
    _initializeFromData();
    _animationController = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 500),
    );
    _animation = CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeInOut,
    );
    _animationController.forward();
    _initializeQuestions();
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  void _initializeFromData() {
    final data = widget.data;
    if (data != null) {
      _primaryColor = Color(data['primary_color'] ?? 0xFF2196F3);
      _secondaryColor = Color(data['secondary_color'] ?? 0xFFFFA000);
      _tertiaryColor = Color(data['tertiary_color'] ?? 0xFF4CAF50);
      _textColor = Color(data['text_color'] ?? 0xFF333333);
      _backgroundColor = Color(data['background_color'] ?? 0xFFF5F5F5);
      _correctColor = Color(data['correct_color'] ?? 0xFF4CAF50);
      _incorrectColor = Color(data['incorrect_color'] ?? 0xFFF44336);
    } else {
      _primaryColor = Colors.blue;
      _secondaryColor = Colors.orange;
      _tertiaryColor = Colors.green;
      _textColor = Colors.black87;
      _backgroundColor = Colors.grey.shade100;
      _correctColor = Colors.green;
      _incorrectColor = Colors.red;
    }
  }

  void _initializeQuestions() {
    _questions = [
      {
        'type': 'multiple_choice',
        'question': 'What is momentum?',
        'options': [
          'The product of mass and velocity',
          'The product of force and time',
          'The product of mass and acceleration',
          'The product of force and displacement',
        ],
        'correctIndex': 0,
        'explanation': 'Momentum (p) is defined as the product of mass (m) and velocity (v): p = mv. It is a vector quantity with the same direction as the velocity.',
      },
      {
        'type': 'multiple_choice',
        'question': 'Which of the following is the correct unit of momentum?',
        'options': [
          'kg·m/s²',
          'kg·m/s',
          'kg·m²/s',
          'kg·m',
        ],
        'correctIndex': 1,
        'explanation': 'The unit of momentum is kg·m/s (kilogram-meter per second). This comes from the definition of momentum as mass (kg) multiplied by velocity (m/s).',
      },
      {
        'type': 'multiple_choice',
        'question': 'What is impulse?',
        'options': [
          'The change in momentum',
          'The change in velocity',
          'The change in kinetic energy',
          'The change in acceleration',
        ],
        'correctIndex': 0,
        'explanation': 'Impulse is defined as the change in momentum. It can be calculated as the product of force and time interval: J = F·Δt = Δp.',
      },
      {
        'type': 'numerical',
        'question': 'A 2 kg object moving at 3 m/s collides with a 3 kg object at rest. If the collision is perfectly inelastic, what is the final velocity of the combined object?',
        'answer': '1.2',
        'tolerance': 0.05, // Allow for rounding errors
        'unit': 'm/s',
        'explanation': 'In a perfectly inelastic collision, the objects stick together. Using conservation of momentum: m₁v₁ + m₂v₂ = (m₁+m₂)v\'\n2×3 + 3×0 = (2+3)v\'\n6 = 5v\'\nv\' = 1.2 m/s',
      },
      {
        'type': 'multiple_choice',
        'question': 'In which type of collision is kinetic energy conserved?',
        'options': [
          'Elastic collisions',
          'Inelastic collisions',
          'Perfectly inelastic collisions',
          'All collisions',
        ],
        'correctIndex': 0,
        'explanation': 'Kinetic energy is conserved only in elastic collisions. In inelastic collisions, some kinetic energy is converted to other forms (like heat, sound, or deformation).',
      },
      {
        'type': 'multiple_choice',
        'question': 'What is the law of conservation of momentum?',
        'options': [
          'The total momentum of an isolated system remains constant',
          'The total energy of an isolated system remains constant',
          'The total force in an isolated system remains constant',
          'The total mass of an isolated system remains constant',
        ],
        'correctIndex': 0,
        'explanation': 'The law of conservation of momentum states that the total momentum of an isolated system (one with no external forces) remains constant regardless of the interactions between the parts of the system.',
      },
      {
        'type': 'numerical',
        'question': 'A 5 kg object moving at 4 m/s collides elastically with a 3 kg object moving in the opposite direction at 2 m/s. What is the final velocity of the 5 kg object?',
        'answer': '-0.5',
        'tolerance': 0.05, // Allow for rounding errors
        'unit': 'm/s',
        'explanation': 'For elastic collisions, we use conservation of momentum and kinetic energy. The final velocity of the 5 kg object is -0.5 m/s (negative indicates direction reversal).',
      },
      {
        'type': 'multiple_choice',
        'question': 'What is the coefficient of restitution for a perfectly elastic collision?',
        'options': [
          '0',
          '0.5',
          '1',
          'Infinity',
        ],
        'correctIndex': 2,
        'explanation': 'The coefficient of restitution (e) is 1 for a perfectly elastic collision, indicating that all kinetic energy is conserved. For perfectly inelastic collisions, e = 0.',
      },
      {
        'type': 'numerical',
        'question': 'A 1000 kg car moving at 20 m/s collides with a stationary 2000 kg truck. If they stick together after the collision, what is their common velocity?',
        'answer': '6.67',
        'tolerance': 0.1, // Allow for rounding errors
        'unit': 'm/s',
        'explanation': 'Using conservation of momentum: m₁v₁ + m₂v₂ = (m₁+m₂)v\'\n1000×20 + 2000×0 = (1000+2000)v\'\n20000 = 3000v\'\nv\' = 6.67 m/s',
      },
      {
        'type': 'multiple_choice',
        'question': 'In a rocket propulsion system, what is being conserved?',
        'options': [
          'Mass',
          'Velocity',
          'Momentum',
          'Kinetic energy',
        ],
        'correctIndex': 2,
        'explanation': 'In rocket propulsion, momentum is conserved. As the rocket expels gas in one direction, the rocket moves in the opposite direction to maintain the total momentum of the system.',
      },
      {
        'type': 'multiple_choice',
        'question': 'What happens to the kinetic energy in an inelastic collision?',
        'options': [
          'It increases',
          'It remains constant',
          'Some of it is converted to other forms of energy',
          'It becomes negative',
        ],
        'correctIndex': 2,
        'explanation': 'In an inelastic collision, some kinetic energy is converted to other forms of energy such as heat, sound, or deformation energy. The total energy is still conserved, but not all of it remains as kinetic energy.',
      },
      {
        'type': 'numerical',
        'question': 'A 60 kg person standing on frictionless ice throws a 0.5 kg ball at 30 m/s. What is the recoil velocity of the person?',
        'answer': '-0.25',
        'tolerance': 0.05, // Allow for rounding errors
        'unit': 'm/s',
        'explanation': 'Using conservation of momentum: Initial momentum = Final momentum\n0 = m₁v₁ + m₂v₂\n0 = 0.5×30 + 60×v₂\n0 = 15 + 60v₂\nv₂ = -0.25 m/s (negative indicates opposite direction)',
      },
    ];

    _maxScore = _questions.length;
    _questionAnswered = List.filled(_questions.length, false);
    _questionCorrect = List.filled(_questions.length, false);
    _userAnswers = List.filled(_questions.length, '');
  }

  void _startTest() {
    setState(() {
      _testStarted = true;
      _timerActive = true;
      _timerStartTime = DateTime.now();
      _currentQuestionIndex = 0;
    });

    // Start timer
    _startTimer();
  }

  void _startTimer() {
    Future.delayed(const Duration(seconds: 1), () {
      if (!mounted || !_timerActive) return;

      final elapsedSeconds = DateTime.now().difference(_timerStartTime).inSeconds;
      setState(() {
        _timeRemaining = math.max(0, 600 - elapsedSeconds);

        if (_timeRemaining <= 0) {
          _timerActive = false;
          _completeTest();
        }
      });

      if (_timerActive) {
        _startTimer();
      }
    });
  }

  void _nextQuestion() {
    if (_currentQuestionIndex < _questions.length - 1) {
      setState(() {
        _currentQuestionIndex++;
      });
    } else {
      _completeTest();
    }
  }

  void _previousQuestion() {
    if (_currentQuestionIndex > 0) {
      setState(() {
        _currentQuestionIndex--;
      });
    }
  }

  void _answerQuestion(String answer, bool correct) {
    setState(() {
      _questionAnswered[_currentQuestionIndex] = true;
      _questionCorrect[_currentQuestionIndex] = correct;
      _userAnswers[_currentQuestionIndex] = answer;

      if (correct) {
        _score++;
      }
    });
  }

  void _completeTest() {
    setState(() {
      _testCompleted = true;
      _timerActive = false;
    });
  }

  String _formatTime(int seconds) {
    final minutes = seconds ~/ 60;
    final remainingSeconds = seconds % 60;
    return '$minutes:${remainingSeconds.toString().padLeft(2, '0')}';
  }

  @override
  Widget build(BuildContext context) {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
      ),
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Title and description
            Text(
              'Momentum & Collisions: Module Test',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
                color: _textColor,
              ),
            ),
            const SizedBox(height: 8),
            Text(
              'Test your knowledge of momentum, impulse, and collisions',
              style: TextStyle(
                fontSize: 14,
                color: _textColor.withAlpha(180),
              ),
            ),
            const SizedBox(height: 16),

            // Test content
            Expanded(
              child: !_testStarted
                ? _buildStartScreen()
                : _testCompleted
                  ? _buildResultScreen()
                  : _buildQuestionScreen(),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildStartScreen() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.science_outlined,
            size: 64,
            color: _primaryColor,
          ),
          const SizedBox(height: 16),
          Text(
            'Physics Module 4: Momentum and Collisions',
            style: TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.bold,
              color: _textColor,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            'This test contains ${_questions.length} questions covering momentum, impulse, conservation of momentum, and different types of collisions.',
            textAlign: TextAlign.center,
            style: TextStyle(
              fontSize: 14,
              color: _textColor,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            'You have 10 minutes to complete the test.',
            textAlign: TextAlign.center,
            style: TextStyle(
              fontSize: 14,
              fontWeight: FontWeight.bold,
              color: _secondaryColor,
            ),
          ),
          const SizedBox(height: 24),
          ElevatedButton.icon(
            onPressed: _startTest,
            icon: const Icon(Icons.play_arrow),
            label: const Text('Start Test'),
            style: ElevatedButton.styleFrom(
              backgroundColor: _primaryColor,
              foregroundColor: Colors.white,
              padding: const EdgeInsets.symmetric(horizontal: 32, vertical: 12),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildResultScreen() {
    final percentage = (_score / _maxScore) * 100;
    final passed = percentage >= 70;

    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            passed ? Icons.check_circle_outline : Icons.error_outline,
            size: 64,
            color: passed ? _correctColor : _incorrectColor,
          ),
          const SizedBox(height: 16),
          Text(
            passed ? 'Congratulations!' : 'Keep Learning!',
            style: TextStyle(
              fontSize: 20,
              fontWeight: FontWeight.bold,
              color: passed ? _correctColor : _incorrectColor,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            'Your score: $_score out of $_maxScore (${percentage.toStringAsFixed(1)}%)',
            style: TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.bold,
              color: _textColor,
            ),
          ),
          const SizedBox(height: 24),

          // Question review
          Expanded(
            child: Container(
              width: double.infinity,
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: _backgroundColor,
                borderRadius: BorderRadius.circular(8),
                border: Border.all(color: _primaryColor.withAlpha(75)),
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'Question Review',
                    style: TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.bold,
                      color: _textColor,
                    ),
                  ),
                  const SizedBox(height: 8),
                  Expanded(
                    child: ListView.builder(
                      itemCount: _questions.length,
                      itemBuilder: (context, index) {
                        final question = _questions[index];
                        final answered = _questionAnswered[index];
                        final correct = _questionCorrect[index];

                        return ListTile(
                          leading: Icon(
                            answered
                                ? (correct ? Icons.check_circle : Icons.cancel)
                                : Icons.help_outline,
                            color: answered
                                ? (correct ? _correctColor : _incorrectColor)
                                : _textColor.withAlpha(128),
                          ),
                          title: Text(
                            'Question ${index + 1}',
                            style: TextStyle(
                              fontWeight: FontWeight.bold,
                              color: _textColor,
                            ),
                          ),
                          subtitle: Text(
                            answered
                                ? (correct ? 'Correct' : 'Incorrect')
                                : 'Not answered',
                            style: TextStyle(
                              color: answered
                                  ? (correct ? _correctColor : _incorrectColor)
                                  : _textColor.withAlpha(128),
                            ),
                          ),
                          trailing: IconButton(
                            icon: const Icon(Icons.info_outline),
                            onPressed: () {
                              _showExplanationDialog(question);
                            },
                          ),
                        );
                      },
                    ),
                  ),
                ],
              ),
            ),
          ),
          const SizedBox(height: 24),

          // Feedback based on score
          Container(
            width: double.infinity,
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: passed ? _correctColor.withAlpha(25) : _incorrectColor.withAlpha(25),
              borderRadius: BorderRadius.circular(8),
              border: Border.all(
                color: passed ? _correctColor.withAlpha(75) : _incorrectColor.withAlpha(75),
              ),
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'Feedback',
                  style: TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                    color: _textColor,
                  ),
                ),
                const SizedBox(height: 8),
                Text(
                  _getFeedbackText(percentage),
                  style: TextStyle(
                    fontSize: 14,
                    color: _textColor,
                  ),
                ),
              ],
            ),
          ),
          const SizedBox(height: 24),

          // Restart button
          ElevatedButton.icon(
            onPressed: () {
              setState(() {
                _testStarted = false;
                _testCompleted = false;
                _currentQuestionIndex = 0;
                _score = 0;
                _questionAnswered = List.filled(_questions.length, false);
                _questionCorrect = List.filled(_questions.length, false);
                _userAnswers = List.filled(_questions.length, '');
                _timeRemaining = 600;
              });
            },
            icon: const Icon(Icons.refresh),
            label: const Text('Restart Test'),
            style: ElevatedButton.styleFrom(
              backgroundColor: _secondaryColor,
              foregroundColor: Colors.white,
              padding: const EdgeInsets.symmetric(horizontal: 32, vertical: 12),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildQuestionScreen() {
    final question = _questions[_currentQuestionIndex];
    final questionType = question['type'] as String;
    final questionText = question['question'] as String;
    final answered = _questionAnswered[_currentQuestionIndex];

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Timer and progress
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            // Question progress
            Text(
              'Question ${_currentQuestionIndex + 1} of ${_questions.length}',
              style: TextStyle(
                fontSize: 14,
                fontWeight: FontWeight.bold,
                color: _textColor,
              ),
            ),

            // Timer
            Row(
              children: [
                Icon(
                  Icons.timer,
                  size: 16,
                  color: _timeRemaining < 60 ? _incorrectColor : _textColor,
                ),
                const SizedBox(width: 4),
                Text(
                  _formatTime(_timeRemaining),
                  style: TextStyle(
                    fontSize: 14,
                    fontWeight: FontWeight.bold,
                    color: _timeRemaining < 60 ? _incorrectColor : _textColor,
                  ),
                ),
              ],
            ),
          ],
        ),

        // Progress bar
        const SizedBox(height: 8),
        LinearProgressIndicator(
          value: (_currentQuestionIndex + 1) / _questions.length,
          backgroundColor: _primaryColor.withAlpha(50),
          valueColor: AlwaysStoppedAnimation<Color>(_primaryColor),
        ),
        const SizedBox(height: 24),

        // Question
        Text(
          questionText,
          style: TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.bold,
            color: _textColor,
          ),
        ),
        const SizedBox(height: 16),

        // Question content based on type
        Expanded(
          child: SingleChildScrollView(
            child: questionType == 'multiple_choice'
                ? _buildMultipleChoiceQuestion(question, answered)
                : _buildNumericalQuestion(question, answered),
          ),
        ),

        const SizedBox(height: 24),

        // Navigation buttons
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            // Previous button
            ElevatedButton.icon(
              onPressed: _currentQuestionIndex > 0 ? _previousQuestion : null,
              icon: const Icon(Icons.arrow_back),
              label: const Text('Previous'),
              style: ElevatedButton.styleFrom(
                backgroundColor: _secondaryColor,
                foregroundColor: Colors.white,
                disabledBackgroundColor: _secondaryColor.withAlpha(75),
              ),
            ),

            // Next/Finish button
            ElevatedButton.icon(
              onPressed: answered
                  ? (_currentQuestionIndex < _questions.length - 1
                      ? _nextQuestion
                      : _completeTest)
                  : null,
              icon: Icon(_currentQuestionIndex < _questions.length - 1
                  ? Icons.arrow_forward
                  : Icons.check),
              label: Text(
                  _currentQuestionIndex < _questions.length - 1 ? 'Next' : 'Finish'),
              style: ElevatedButton.styleFrom(
                backgroundColor: _primaryColor,
                foregroundColor: Colors.white,
                disabledBackgroundColor: _primaryColor.withAlpha(75),
              ),
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildMultipleChoiceQuestion(Map<String, dynamic> question, bool answered) {
    final options = question['options'] as List<dynamic>;
    final correctIndex = question['correctIndex'] as int;

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        for (int i = 0; i < options.length; i++)
          Padding(
            padding: const EdgeInsets.only(bottom: 8.0),
            child: RadioListTile<int>(
              title: Text(
                options[i] as String,
                style: TextStyle(
                  fontSize: 14,
                  color: _textColor,
                ),
              ),
              value: i,
              groupValue: answered && _userAnswers[_currentQuestionIndex] == i.toString()
                  ? i
                  : null,
              onChanged: answered
                  ? null
                  : (value) {
                      if (value != null) {
                        _answerQuestion(
                          value.toString(),
                          value == correctIndex,
                        );
                      }
                    },
              activeColor: _primaryColor,
              tileColor: answered
                  ? (_userAnswers[_currentQuestionIndex] == i.toString()
                      ? (i == correctIndex
                          ? _correctColor.withAlpha(50)
                          : _incorrectColor.withAlpha(50))
                      : (i == correctIndex
                          ? _correctColor.withAlpha(25)
                          : null))
                  : null,
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(8),
                side: BorderSide(
                  color: answered
                      ? (_userAnswers[_currentQuestionIndex] == i.toString()
                          ? (i == correctIndex
                              ? _correctColor
                              : _incorrectColor)
                          : (i == correctIndex
                              ? _correctColor
                              : Colors.grey.withAlpha(75)))
                      : Colors.grey.withAlpha(75),
                ),
              ),
            ),
          ),

        // Explanation if answered
        if (answered)
          Padding(
            padding: const EdgeInsets.only(top: 8.0),
            child: Container(
              width: double.infinity,
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: _questionCorrect[_currentQuestionIndex]
                    ? _correctColor.withAlpha(25)
                    : _incorrectColor.withAlpha(25),
                borderRadius: BorderRadius.circular(8),
                border: Border.all(
                  color: _questionCorrect[_currentQuestionIndex]
                      ? _correctColor
                      : _incorrectColor,
                ),
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    _questionCorrect[_currentQuestionIndex]
                        ? 'Correct!'
                        : 'Incorrect',
                    style: TextStyle(
                      fontSize: 14,
                      fontWeight: FontWeight.bold,
                      color: _questionCorrect[_currentQuestionIndex]
                          ? _correctColor
                          : _incorrectColor,
                    ),
                  ),
                  const SizedBox(height: 4),
                  Text(
                    question['explanation'] as String,
                    style: TextStyle(
                      fontSize: 14,
                      color: _textColor,
                    ),
                  ),
                ],
              ),
            ),
          ),
      ],
    );
  }

  Widget _buildNumericalQuestion(Map<String, dynamic> question, bool answered) {
    final correctAnswer = question['answer'] as String;
    final tolerance = question['tolerance'] as double;
    final unit = question['unit'] as String;

    final TextEditingController answerController = TextEditingController();
    if (answered) {
      answerController.text = _userAnswers[_currentQuestionIndex];
    }

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            Expanded(
              child: TextField(
                controller: answerController,
                enabled: !answered,
                keyboardType: TextInputType.number,
                decoration: InputDecoration(
                  labelText: 'Your answer',
                  hintText: 'Enter a numerical value',
                  suffixText: unit,
                  border: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(8),
                  ),
                  enabledBorder: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(8),
                    borderSide: BorderSide(color: _primaryColor),
                  ),
                  focusedBorder: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(8),
                    borderSide: BorderSide(color: _primaryColor, width: 2),
                  ),
                ),
                onSubmitted: answered
                    ? null
                    : (value) {
                        _checkNumericalAnswer(value, correctAnswer, tolerance);
                      },
              ),
            ),
            if (!answered)
              Padding(
                padding: const EdgeInsets.only(left: 8.0),
                child: ElevatedButton(
                  onPressed: () {
                    _checkNumericalAnswer(
                        answerController.text, correctAnswer, tolerance);
                  },
                  style: ElevatedButton.styleFrom(
                    backgroundColor: _primaryColor,
                    foregroundColor: Colors.white,
                    padding: const EdgeInsets.symmetric(vertical: 16),
                  ),
                  child: const Text('Submit'),
                ),
              ),
          ],
        ),

        // Explanation if answered
        if (answered)
          Padding(
            padding: const EdgeInsets.only(top: 16.0),
            child: Container(
              width: double.infinity,
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: _questionCorrect[_currentQuestionIndex]
                    ? _correctColor.withAlpha(25)
                    : _incorrectColor.withAlpha(25),
                borderRadius: BorderRadius.circular(8),
                border: Border.all(
                  color: _questionCorrect[_currentQuestionIndex]
                      ? _correctColor
                      : _incorrectColor,
                ),
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    _questionCorrect[_currentQuestionIndex]
                        ? 'Correct!'
                        : 'Incorrect',
                    style: TextStyle(
                      fontSize: 14,
                      fontWeight: FontWeight.bold,
                      color: _questionCorrect[_currentQuestionIndex]
                          ? _correctColor
                          : _incorrectColor,
                    ),
                  ),
                  const SizedBox(height: 4),
                  Text(
                    'The correct answer is $correctAnswer $unit',
                    style: TextStyle(
                      fontSize: 14,
                      fontWeight: FontWeight.bold,
                      color: _textColor,
                    ),
                  ),
                  const SizedBox(height: 4),
                  Text(
                    question['explanation'] as String,
                    style: TextStyle(
                      fontSize: 14,
                      color: _textColor,
                    ),
                  ),
                ],
              ),
            ),
          ),
      ],
    );
  }

  void _checkNumericalAnswer(String userAnswer, String correctAnswer, double tolerance) {
    if (userAnswer.isEmpty) return;

    try {
      final userValue = double.parse(userAnswer);
      final correctValue = double.parse(correctAnswer);

      final difference = (userValue - correctValue).abs();
      final isCorrect = difference <= tolerance;

      _answerQuestion(userAnswer, isCorrect);
    } catch (e) {
      // Invalid number format
      _answerQuestion(userAnswer, false);
    }
  }

  void _showExplanationDialog(Map<String, dynamic> question) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(
          'Explanation',
          style: TextStyle(
            fontSize: 18,
            fontWeight: FontWeight.bold,
            color: _textColor,
          ),
        ),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              question['question'] as String,
              style: TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.bold,
                color: _textColor,
              ),
            ),
            const SizedBox(height: 8),
            Text(
              question['explanation'] as String,
              style: TextStyle(
                fontSize: 14,
                color: _textColor,
              ),
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Close'),
          ),
        ],
      ),
    );
  }

  String _getFeedbackText(double percentage) {
    if (percentage >= 90) {
      return 'Excellent! You have a strong understanding of momentum, impulse, and collisions. You can confidently apply these principles to solve complex physics problems.';
    } else if (percentage >= 70) {
      return 'Good job! You understand most of the key concepts related to momentum and collisions. Review the questions you missed to strengthen your understanding.';
    } else if (percentage >= 50) {
      return 'You have a basic understanding of momentum and collisions, but there are some areas that need improvement. Focus on reviewing the fundamental principles and formulas.';
    } else {
      return 'You need more practice with momentum and collision concepts. Review the module materials, focus on understanding the fundamental principles, and try the test again.';
    }
  }
}
