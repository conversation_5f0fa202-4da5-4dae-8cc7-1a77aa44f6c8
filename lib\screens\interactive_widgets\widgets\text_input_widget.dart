import 'package:flutter/material.dart';
import '../../../models/interactive_widget_model.dart';

class TextInputWidget extends StatefulWidget {
  final InteractiveWidgetModel widget;

  const TextInputWidget({super.key, required this.widget});

  @override
  State<TextInputWidget> createState() => _TextInputWidgetState();
}

class _TextInputWidgetState extends State<TextInputWidget> {
  final TextEditingController _textController = TextEditingController();
  bool _isSubmitted = false;
  bool _isCorrect = false;
  String _feedbackMessage = '';

  @override
  void dispose() {
    _textController.dispose();
    super.dispose();
  }

  void _handleSubmit() {
    if (_isSubmitted) return; // Prevent re-submission

    final String userAnswer = _textController.text.trim();
    final String correctAnswer = widget.widget.data['correctAnswer'] as String;
    final bool caseSensitive = widget.widget.data['caseSensitive'] as bool? ?? false;
    
    bool isCorrect = false;
    
    if (widget.widget.data['useRegex'] == true) {
      try {
        final RegExp regex = RegExp(correctAnswer);
        isCorrect = regex.hasMatch(userAnswer);
      } catch (e) {
        print('Error with regex: $correctAnswer - $e');
      }
    } else {
      isCorrect = caseSensitive 
          ? userAnswer == correctAnswer
          : userAnswer.toLowerCase() == correctAnswer.toLowerCase();
    }

    setState(() {
      _isSubmitted = true;
      _isCorrect = isCorrect;
      _feedbackMessage = isCorrect
          ? widget.widget.data['feedbackCorrect'] as String? ?? 'Correct!'
          : widget.widget.data['feedbackIncorrect'] as String? ?? 'Try again!';
    });
  }

  void _reset() {
    setState(() {
      _textController.clear();
      _isSubmitted = false;
      _isCorrect = false;
      _feedbackMessage = '';
    });
  }

  @override
  Widget build(BuildContext context) {
    final String question = widget.widget.data['question'] as String;
    final String placeholder = widget.widget.data['placeholder'] as String? ?? 'Enter your answer...';

    return Container(
      padding: const EdgeInsets.all(16.0),
      decoration: BoxDecoration(
        color: Colors.blue.shade50,
        borderRadius: BorderRadius.circular(12.0),
        border: Border.all(
          color: Colors.blue.shade200,
          width: 1.0,
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Question
          Text(
            question,
            style: const TextStyle(
              fontSize: 16.0,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 16.0),
          
          // Text input field
          TextField(
            controller: _textController,
            decoration: InputDecoration(
              hintText: placeholder,
              border: OutlineInputBorder(
                borderRadius: BorderRadius.circular(8.0),
              ),
              filled: true,
              fillColor: Colors.white,
              suffixIcon: _isSubmitted
                  ? Icon(
                      _isCorrect
                          ? Icons.check_circle
                          : Icons.cancel,
                      color: _isCorrect ? Colors.green : Colors.red,
                    )
                  : null,
            ),
            enabled: !_isSubmitted,
            onSubmitted: (_) => _handleSubmit(),
          ),
          
          // Feedback message
          if (_isSubmitted)
            Padding(
              padding: const EdgeInsets.only(top: 12.0),
              child: Text(
                _feedbackMessage,
                style: TextStyle(
                  color: _isCorrect ? Colors.green.shade700 : Colors.red.shade700,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ),
          
          // Buttons
          Padding(
            padding: const EdgeInsets.only(top: 16.0),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                ElevatedButton(
                  onPressed: _isSubmitted ? _reset : null,
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Colors.grey.shade200,
                    foregroundColor: Colors.black87,
                  ),
                  child: const Text('Reset'),
                ),
                ElevatedButton(
                  onPressed: _isSubmitted ? null : _handleSubmit,
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Colors.blue,
                    foregroundColor: Colors.white,
                  ),
                  child: const Text('Submit'),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}
