import 'package:flutter/material.dart';
import 'dart:math' as math;

/// A widget that visualizes the elimination method for solving systems of linear equations
class InteractiveEliminationMethodVisualizerWidget extends StatefulWidget {
  /// The data for this widget
  final Map<String, dynamic> data;

  /// Optional callback when the widget state changes
  final Function(bool isCompleted)? onStateChanged;

  const InteractiveEliminationMethodVisualizerWidget({
    Key? key,
    required this.data,
    this.onStateChanged,
  }) : super(key: key);

  /// Factory constructor to create the widget from JSON data
  factory InteractiveEliminationMethodVisualizerWidget.fromData(Map<String, dynamic> data) {
    return InteractiveEliminationMethodVisualizerWidget(
      data: data,
    );
  }

  @override
  State<InteractiveEliminationMethodVisualizerWidget> createState() => _InteractiveEliminationMethodVisualizerWidgetState();
}

class _InteractiveEliminationMethodVisualizerWidgetState extends State<InteractiveEliminationMethodVisualizerWidget> {
  // State variables
  bool _isCompleted = false;

  // System of equations
  List<Equation> _equations = [];

  // Solution
  double? _solutionX;
  double? _solutionY;
  String _solutionMessage = '';
  bool _hasSolution = false;

  // Elimination steps
  List<EliminationStep> _steps = [];
  int _currentStepIndex = 0;
  bool _showingSolution = false;

  // Controllers for equation inputs
  final List<TextEditingController> _aControllers = [];
  final List<TextEditingController> _bControllers = [];
  final List<TextEditingController> _cControllers = [];

  // Controllers for multipliers
  final List<TextEditingController> _multiplierControllers = [];

  // Colors
  late Color _primaryColor;
  late Color _secondaryColor;
  late Color _accentColor;
  late Color _textColor;
  late Color _highlightColor;

  // Animation controller
  bool _isAnimating = false;

  @override
  void initState() {
    super.initState();
    _initializeFromData();
  }

  @override
  void dispose() {
    // Dispose all controllers
    for (var controller in _aControllers) {
      controller.dispose();
    }
    for (var controller in _bControllers) {
      controller.dispose();
    }
    for (var controller in _cControllers) {
      controller.dispose();
    }
    for (var controller in _multiplierControllers) {
      controller.dispose();
    }
    super.dispose();
  }

  void _initializeFromData() {
    // Initialize colors
    _primaryColor = _parseColor(widget.data['primaryColor'] ?? '#2196F3');
    _secondaryColor = _parseColor(widget.data['secondaryColor'] ?? '#FF9800');
    _accentColor = _parseColor(widget.data['accentColor'] ?? '#4CAF50');
    _textColor = _parseColor(widget.data['textColor'] ?? '#212121');
    _highlightColor = _parseColor(widget.data['highlightColor'] ?? '#FFEB3B');

    // Initialize equations
    final initialEquations = widget.data['initialEquations'] as List<dynamic>? ?? [];

    if (initialEquations.isNotEmpty) {
      for (var eqData in initialEquations) {
        final a = (eqData['a'] as num).toDouble();
        final b = (eqData['b'] as num).toDouble();
        final c = (eqData['c'] as num).toDouble();

        _equations.add(Equation(a: a, b: b, c: c));

        // Create controllers for this equation
        _aControllers.add(TextEditingController(text: a.toString()));
        _bControllers.add(TextEditingController(text: b.toString()));
        _cControllers.add(TextEditingController(text: c.toString()));
      }
    } else {
      // Create default equations if none provided
      _equations = [
        Equation(a: 2, b: 3, c: 8),
        Equation(a: 5, b: 1, c: 14),
      ];

      // Create controllers for default equations
      _aControllers.add(TextEditingController(text: '2'));
      _bControllers.add(TextEditingController(text: '3'));
      _cControllers.add(TextEditingController(text: '8'));

      _aControllers.add(TextEditingController(text: '5'));
      _bControllers.add(TextEditingController(text: '1'));
      _cControllers.add(TextEditingController(text: '14'));
    }

    // Initialize multiplier controllers
    _multiplierControllers.add(TextEditingController(text: '1'));
    _multiplierControllers.add(TextEditingController(text: '1'));
  }

  Color _parseColor(String colorString) {
    if (colorString.startsWith('#')) {
      return Color(int.parse('FF${colorString.substring(1)}', radix: 16));
    }
    return Colors.black;
  }

  void _updateEquationFromControllers(int index) {
    try {
      final a = double.parse(_aControllers[index].text);
      final b = double.parse(_bControllers[index].text);
      final c = double.parse(_cControllers[index].text);

      setState(() {
        _equations[index] = Equation(a: a, b: b, c: c);
        _resetSolution();
      });
    } catch (e) {
      // Handle parsing errors
      print('Error parsing equation values: $e');
    }
  }

  void _resetSolution() {
    setState(() {
      _solutionX = null;
      _solutionY = null;
      _solutionMessage = '';
      _hasSolution = false;
      _steps = [];
      _currentStepIndex = 0;
      _showingSolution = false;
      _isCompleted = false;
      _isAnimating = false;
    });

    // Notify parent of completion status
    widget.onStateChanged?.call(_isCompleted);
  }

  void _solveSystem() {
    if (_equations.length < 2) {
      setState(() {
        _solutionMessage = 'Need at least 2 equations to solve a system.';
        _hasSolution = false;
      });
      return;
    }

    setState(() {
      _steps = [];
      _currentStepIndex = 0;
      _showingSolution = true;
      _isAnimating = false;
    });

    _solveByElimination();

    // Mark as completed if we have a solution
    setState(() {
      _isCompleted = _hasSolution;
    });

    // Notify parent of completion status
    widget.onStateChanged?.call(_isCompleted);
  }

  void _solveByElimination() {
    // Get the two equations
    final eq1 = _equations[0];
    final eq2 = _equations[1];

    // Step 1: Initial equations
    _steps.add(EliminationStep(
      description: 'Initial system of equations',
      equation1: eq1,
      equation2: eq2,
      highlightedTerms: [],
    ));

    // Step 2: Determine which variable to eliminate
    // For simplicity, we'll always eliminate y
    _steps.add(EliminationStep(
      description: 'We will eliminate the y variable',
      equation1: eq1,
      equation2: eq2,
      highlightedTerms: ['y1', 'y2'],
    ));

    // Step 3: Find multipliers to make y coefficients opposites
    final lcm = _lcm(eq1.b.abs().toInt(), eq2.b.abs().toInt());
    final factor1 = lcm ~/ eq1.b.abs().toInt() * (eq1.b > 0 ? 1 : -1);
    final factor2 = lcm ~/ eq2.b.abs().toInt() * (eq2.b > 0 ? -1 : 1);

    _steps.add(EliminationStep(
      description: 'Find multipliers to make y coefficients opposites:\n'
                  'Multiply equation 1 by $factor1\n'
                  'Multiply equation 2 by $factor2',
      equation1: eq1,
      equation2: eq2,
      highlightedTerms: ['y1', 'y2'],
      multiplier1: factor1.toDouble(),
      multiplier2: factor2.toDouble(),
    ));

    // Step 4: Multiply equations by their factors
    final newEq1 = Equation(
      a: eq1.a * factor1,
      b: eq1.b * factor1,
      c: eq1.c * factor1,
    );

    final newEq2 = Equation(
      a: eq2.a * factor2,
      b: eq2.b * factor2,
      c: eq2.c * factor2,
    );

    _steps.add(EliminationStep(
      description: 'Multiply each equation by its factor',
      equation1: newEq1,
      equation2: newEq2,
      highlightedTerms: ['all1', 'all2'],
      multiplier1: factor1.toDouble(),
      multiplier2: factor2.toDouble(),
      showMultipliers: true,
    ));

    // Step 5: Add the equations to eliminate y
    final combinedA = newEq1.a + newEq2.a;
    final combinedB = newEq1.b + newEq2.b; // Should be 0
    final combinedC = newEq1.c + newEq2.c;

    final resultEq = Equation(a: combinedA, b: combinedB, c: combinedC);

    _steps.add(EliminationStep(
      description: 'Add the equations to eliminate y',
      equation1: newEq1,
      equation2: newEq2,
      resultEquation: resultEq,
      highlightedTerms: ['all1', 'all2', 'allR'],
      showAddition: true,
    ));

    // Check if we can solve for x
    if (combinedA == 0) {
      if (combinedC == 0) {
        setState(() {
          _solutionMessage = 'The system has infinitely many solutions.';
          _hasSolution = false;
        });
        return;
      } else {
        setState(() {
          _solutionMessage = 'The system has no solution.';
          _hasSolution = false;
        });
        return;
      }
    }

    // Step 6: Solve for x
    final x = combinedC / combinedA;

    _steps.add(EliminationStep(
      description: 'Solve for x: $combinedA x = $combinedC',
      resultEquation: resultEq,
      highlightedTerms: ['xR', 'cR'],
      solutionX: x,
    ));

    // Step 7: Substitute x back into one of the original equations to find y
    final y = (eq1.c - eq1.a * x) / eq1.b;

    _steps.add(EliminationStep(
      description: 'Substitute x = $x back into the first equation to find y:\n'
                  '${eq1.a}($x) + ${eq1.b}y = ${eq1.c}\n'
                  '${eq1.b}y = ${eq1.c} - ${eq1.a * x}\n'
                  '${eq1.b}y = ${eq1.c - eq1.a * x}\n'
                  'y = ${y}',
      equation1: eq1,
      highlightedTerms: ['x1', 'y1', 'c1'],
      solutionX: x,
      solutionY: y,
    ));

    // Set the solution
    setState(() {
      _solutionX = x;
      _solutionY = y;
      _solutionMessage = 'Solution: x = $x, y = $y';
      _hasSolution = true;
    });
  }

  int _gcd(int a, int b) {
    while (b != 0) {
      final t = b;
      b = a % b;
      a = t;
    }
    return a;
  }

  int _lcm(int a, int b) {
    return (a * b) ~/ _gcd(a, b);
  }

  void _nextStep() {
    if (_currentStepIndex < _steps.length - 1) {
      setState(() {
        _currentStepIndex++;
      });
    }
  }

  void _previousStep() {
    if (_currentStepIndex > 0) {
      setState(() {
        _currentStepIndex--;
      });
    }
  }

  void _startAnimation() {
    setState(() {
      _currentStepIndex = 0;
      _isAnimating = true;
    });

    _animateNextStep();
  }

  void _animateNextStep() {
    if (!_isAnimating || _currentStepIndex >= _steps.length - 1) {
      setState(() {
        _isAnimating = false;
      });
      return;
    }

    Future.delayed(const Duration(seconds: 2), () {
      if (mounted && _isAnimating) {
        setState(() {
          _currentStepIndex++;
        });
        _animateNextStep();
      }
    });
  }

  void _stopAnimation() {
    setState(() {
      _isAnimating = false;
    });
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withOpacity(0.2),
            spreadRadius: 1,
            blurRadius: 3,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Title
          Text(
            widget.data['title'] ?? 'Elimination Method Visualizer',
            style: const TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
            ),
          ),

          const SizedBox(height: 8),

          // Description
          if (widget.data['description'] != null)
            Text(
              widget.data['description'],
              style: const TextStyle(
                fontSize: 14,
                color: Colors.grey,
              ),
            ),

          const SizedBox(height: 16),

          // Equation inputs
          ..._buildEquationInputs(),

          const SizedBox(height: 16),

          // Solve button
          Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              ElevatedButton.icon(
                icon: const Icon(Icons.calculate),
                label: const Text('Solve System'),
                style: ElevatedButton.styleFrom(
                  backgroundColor: _primaryColor,
                  foregroundColor: Colors.white,
                  padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
                ),
                onPressed: _solveSystem,
              ),
              if (_steps.isNotEmpty) ...[
                const SizedBox(width: 16),
                ElevatedButton.icon(
                  icon: Icon(_isAnimating ? Icons.stop : Icons.play_arrow),
                  label: Text(_isAnimating ? 'Stop Animation' : 'Animate Solution'),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: _secondaryColor,
                    foregroundColor: Colors.white,
                    padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
                  ),
                  onPressed: _isAnimating ? _stopAnimation : _startAnimation,
                ),
              ],
            ],
          ),

          const SizedBox(height: 24),

          // Solution visualization
          if (_showingSolution && _steps.isNotEmpty) ...[
            const Divider(),

            // Step navigation
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                IconButton(
                  icon: const Icon(Icons.arrow_back),
                  onPressed: _currentStepIndex > 0 ? _previousStep : null,
                  tooltip: 'Previous Step',
                ),
                Text(
                  'Step ${_currentStepIndex + 1} of ${_steps.length}',
                  style: const TextStyle(fontWeight: FontWeight.bold),
                ),
                IconButton(
                  icon: const Icon(Icons.arrow_forward),
                  onPressed: _currentStepIndex < _steps.length - 1 ? _nextStep : null,
                  tooltip: 'Next Step',
                ),
              ],
            ),

            // Current step description
            Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: Colors.grey[100],
                borderRadius: BorderRadius.circular(8),
              ),
              child: Text(
                _steps[_currentStepIndex].description,
                style: const TextStyle(fontSize: 14),
              ),
            ),

            const SizedBox(height: 16),

            // Visualization of the current step
            _buildStepVisualization(_steps[_currentStepIndex]),

            const SizedBox(height: 16),

            // Final solution
            if (_hasSolution && _currentStepIndex == _steps.length - 1)
              Container(
                padding: const EdgeInsets.all(16),
                decoration: BoxDecoration(
                  color: _accentColor.withOpacity(0.1),
                  borderRadius: BorderRadius.circular(8),
                  border: Border.all(color: _accentColor),
                ),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Solution:',
                      style: TextStyle(
                        fontWeight: FontWeight.bold,
                        color: _accentColor,
                      ),
                    ),
                    const SizedBox(height: 8),
                    Text('x = ${_solutionX?.toStringAsFixed(2)}'),
                    Text('y = ${_solutionY?.toStringAsFixed(2)}'),
                  ],
                ),
              )
            else if (!_hasSolution && _solutionMessage.isNotEmpty)
              Container(
                padding: const EdgeInsets.all(16),
                decoration: BoxDecoration(
                  color: Colors.red[50],
                  borderRadius: BorderRadius.circular(8),
                  border: Border.all(color: Colors.red),
                ),
                child: Text(
                  _solutionMessage,
                  style: const TextStyle(color: Colors.red),
                ),
              ),
          ],

          // Widget name tag
          if (widget.data['showNameTag'] ?? true)
            Padding(
              padding: const EdgeInsets.only(top: 8),
              child: Text(
                'InteractiveEliminationMethodVisualizer',
                style: TextStyle(
                  color: Colors.grey[600],
                  fontSize: 12,
                  fontStyle: FontStyle.italic,
                ),
              ),
            ),
        ],
      ),
    );
  }

  List<Widget> _buildEquationInputs() {
    final widgets = <Widget>[];

    for (var i = 0; i < _equations.length; i++) {
      widgets.add(
        Padding(
          padding: const EdgeInsets.only(bottom: 16),
          child: Row(
            children: [
              Text('Equation ${i+1}:', style: const TextStyle(fontWeight: FontWeight.bold)),
              const SizedBox(width: 8),
              Expanded(
                child: TextField(
                  controller: _aControllers[i],
                  keyboardType: const TextInputType.numberWithOptions(decimal: true, signed: true),
                  decoration: const InputDecoration(
                    labelText: 'a',
                    border: OutlineInputBorder(),
                  ),
                  onChanged: (_) => _updateEquationFromControllers(i),
                ),
              ),
              const SizedBox(width: 8),
              const Text('x +'),
              const SizedBox(width: 8),
              Expanded(
                child: TextField(
                  controller: _bControllers[i],
                  keyboardType: const TextInputType.numberWithOptions(decimal: true, signed: true),
                  decoration: const InputDecoration(
                    labelText: 'b',
                    border: OutlineInputBorder(),
                  ),
                  onChanged: (_) => _updateEquationFromControllers(i),
                ),
              ),
              const SizedBox(width: 8),
              const Text('y ='),
              const SizedBox(width: 8),
              Expanded(
                child: TextField(
                  controller: _cControllers[i],
                  keyboardType: const TextInputType.numberWithOptions(decimal: true, signed: true),
                  decoration: const InputDecoration(
                    labelText: 'c',
                    border: OutlineInputBorder(),
                  ),
                  onChanged: (_) => _updateEquationFromControllers(i),
                ),
              ),
            ],
          ),
        ),
      );
    }

    return widgets;
  }

  Widget _buildStepVisualization(EliminationStep step) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // First equation
        if (step.equation1 != null)
          _buildEquationDisplay(
            equation: step.equation1!,
            index: 1,
            highlightedTerms: step.highlightedTerms,
            multiplier: step.multiplier1,
            showMultiplier: step.showMultipliers,
          ),

        // Second equation
        if (step.equation2 != null) ...[
          const SizedBox(height: 8),
          _buildEquationDisplay(
            equation: step.equation2!,
            index: 2,
            highlightedTerms: step.highlightedTerms,
            multiplier: step.multiplier2,
            showMultiplier: step.showMultipliers,
          ),
        ],

        // Addition line
        if (step.showAddition) ...[
          const SizedBox(height: 4),
          Container(
            width: double.infinity,
            height: 1,
            color: Colors.black,
          ),
          const SizedBox(height: 4),
        ],

        // Result equation
        if (step.resultEquation != null) ...[
          _buildEquationDisplay(
            equation: step.resultEquation!,
            index: 'R',
            highlightedTerms: step.highlightedTerms,
          ),
        ],

        // Solution display
        if (step.solutionX != null) ...[
          const SizedBox(height: 16),
          Text(
            'x = ${step.solutionX!.toStringAsFixed(2)}',
            style: const TextStyle(
              fontWeight: FontWeight.bold,
              fontSize: 16,
            ),
          ),
        ],

        if (step.solutionY != null) ...[
          const SizedBox(height: 8),
          Text(
            'y = ${step.solutionY!.toStringAsFixed(2)}',
            style: const TextStyle(
              fontWeight: FontWeight.bold,
              fontSize: 16,
            ),
          ),
        ],
      ],
    );
  }

  Widget _buildEquationDisplay({
    required Equation equation,
    required dynamic index,
    required List<String> highlightedTerms,
    double? multiplier,
    bool showMultiplier = false,
  }) {
    final indexStr = index.toString();

    return Row(
      children: [
        // Multiplier
        if (showMultiplier && multiplier != null) ...[
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
            decoration: BoxDecoration(
              color: _secondaryColor.withOpacity(0.2),
              borderRadius: BorderRadius.circular(4),
            ),
            child: Text(
              '$multiplier ×',
              style: const TextStyle(fontWeight: FontWeight.bold),
            ),
          ),
          const SizedBox(width: 8),
        ],

        // Equation
        Expanded(
          child: Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              // x term
              Container(
                padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                decoration: BoxDecoration(
                  color: highlightedTerms.contains('x$indexStr') || highlightedTerms.contains('all$indexStr')
                      ? _highlightColor.withOpacity(0.3)
                      : Colors.transparent,
                  borderRadius: BorderRadius.circular(4),
                ),
                child: Text(
                  '${equation.a}x',
                  style: const TextStyle(fontSize: 16),
                ),
              ),

              // + sign
              const Text(' + ', style: TextStyle(fontSize: 16)),

              // y term
              Container(
                padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                decoration: BoxDecoration(
                  color: highlightedTerms.contains('y$indexStr') || highlightedTerms.contains('all$indexStr')
                      ? _highlightColor.withOpacity(0.3)
                      : Colors.transparent,
                  borderRadius: BorderRadius.circular(4),
                ),
                child: Text(
                  '${equation.b}y',
                  style: const TextStyle(fontSize: 16),
                ),
              ),

              // = sign
              const Text(' = ', style: TextStyle(fontSize: 16)),

              // constant term
              Container(
                padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                decoration: BoxDecoration(
                  color: highlightedTerms.contains('c$indexStr') || highlightedTerms.contains('all$indexStr')
                      ? _highlightColor.withOpacity(0.3)
                      : Colors.transparent,
                  borderRadius: BorderRadius.circular(4),
                ),
                child: Text(
                  '${equation.c}',
                  style: const TextStyle(fontSize: 16),
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }
}

class Equation {
  final double a; // coefficient of x
  final double b; // coefficient of y
  final double c; // constant term

  Equation({
    required this.a,
    required this.b,
    required this.c,
  });

  @override
  String toString() {
    return '${a}x + ${b}y = $c';
  }
}

class EliminationStep {
  final String description;
  final Equation? equation1;
  final Equation? equation2;
  final Equation? resultEquation;
  final List<String> highlightedTerms;
  final double? multiplier1;
  final double? multiplier2;
  final bool showMultipliers;
  final bool showAddition;
  final double? solutionX;
  final double? solutionY;

  EliminationStep({
    required this.description,
    this.equation1,
    this.equation2,
    this.resultEquation,
    required this.highlightedTerms,
    this.multiplier1,
    this.multiplier2,
    this.showMultipliers = false,
    this.showAddition = false,
    this.solutionX,
    this.solutionY,
  });
}
