{"id": "algorithms-data-structures", "title": "Algorithms & Data Structures", "description": "Master fundamental algorithms and data structures to write efficient and scalable code.", "categoryId": "computer_science", "thumbnailPath": "assets/images/cs_icon.svg", "difficulty": "Intermediate", "modules": [{"id": "fundamental-data-structures", "title": "Fundamental Data Structures", "description": "Explore arrays, linked lists, stacks, and queues.", "order": 1}, {"id": "sorting-algorithms", "title": "Sorting Algorithms", "description": "Learn various sorting techniques like bubble sort, merge sort, and quick sort.", "order": 2}, {"id": "searching-algorithms-graph-basics", "title": "Searching Algorithms and Graph Basics", "description": "Understand linear search, binary search, and introductory graph concepts.", "order": 3}, {"id": "trees-and-heaps", "title": "Trees and Heaps", "description": "Dive into tree structures, binary search trees, and heap data structures.", "order": 4}, {"id": "advanced-data-structures-algorithmic-techniques", "title": "Advanced Data Structures and Algorithmic Techniques", "description": "Explore hash tables, dynamic programming, and greedy algorithms.", "order": 5}]}