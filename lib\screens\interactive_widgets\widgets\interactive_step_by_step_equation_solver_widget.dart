import 'package:flutter/material.dart';
import 'dart:math' as math;

/// A widget that guides users through solving equations step by step
class InteractiveStepByStepEquationSolverWidget extends StatefulWidget {
  /// The data for this widget
  final Map<String, dynamic> data;

  /// Optional callback when the widget state changes
  final Function(bool isCompleted)? onStateChanged;

  const InteractiveStepByStepEquationSolverWidget({
    Key? key,
    required this.data,
    this.onStateChanged,
  }) : super(key: key);

  /// Factory constructor to create the widget from JSON data
  factory InteractiveStepByStepEquationSolverWidget.fromData(Map<String, dynamic> data) {
    return InteractiveStepByStepEquationSolverWidget(
      data: data,
    );
  }

  @override
  State<InteractiveStepByStepEquationSolverWidget> createState() => _InteractiveStepByStepEquationSolverWidgetState();
}

class _InteractiveStepByStepEquationSolverWidgetState extends State<InteractiveStepByStepEquationSolverWidget> {
  // Equation data
  late String _initialEquation;
  late String _variableName;
  late String _solution;
  late List<EquationStep> _solutionSteps;
  
  // Current state
  int _currentStepIndex = 0;
  bool _isCompleted = false;
  bool _showHint = false;
  String? _errorMessage;
  
  // User input
  String _selectedOperation = '';
  TextEditingController _equationController = TextEditingController();
  
  // UI customization
  late Color _primaryColor;
  late Color _successColor;
  late Color _errorColor;
  late Color _hintColor;
  late Color _neutralColor;

  @override
  void initState() {
    super.initState();
    
    // Initialize from data
    _initialEquation = widget.data['initial_equation'] ?? '3x + 7 = 22';
    _variableName = widget.data['variable_name'] ?? 'x';
    _solution = widget.data['solution'] ?? '5';
    
    // Parse solution steps
    _solutionSteps = [];
    final stepsData = widget.data['solution_steps'] as List<dynamic>? ?? [];
    for (final step in stepsData) {
      if (step is Map<String, dynamic>) {
        _solutionSteps.add(EquationStep(
          equation: step['equation'] ?? '',
          operation: step['operation'] ?? '',
          explanation: step['explanation'] ?? '',
          options: _parseOptions(step['options']),
        ));
      }
    }
    
    // If no steps provided, create default steps
    if (_solutionSteps.isEmpty) {
      _solutionSteps = [
        EquationStep(
          equation: '3x + 7 = 22',
          operation: 'Initial equation',
          explanation: 'We start with the given equation.',
          options: [],
        ),
        EquationStep(
          equation: '3x + 7 - 7 = 22 - 7',
          operation: 'Subtract 7 from both sides',
          explanation: 'To isolate the variable term, we subtract 7 from both sides of the equation.',
          options: [
            OperationOption(
              text: 'Subtract 7 from both sides',
              isCorrect: true,
              explanation: 'Correct! Subtracting 7 from both sides helps isolate the variable term.',
            ),
            OperationOption(
              text: 'Add 7 to both sides',
              isCorrect: false,
              explanation: 'Adding 7 to both sides would make the equation more complex.',
            ),
            OperationOption(
              text: 'Divide both sides by 3',
              isCorrect: false,
              explanation: 'We should first isolate the variable term (3x) before dividing.',
            ),
          ],
        ),
        EquationStep(
          equation: '3x = 15',
          operation: 'Simplify',
          explanation: 'After subtracting 7 from both sides, we get 3x = 15.',
          options: [
            OperationOption(
              text: 'Simplify the equation',
              isCorrect: true,
              explanation: 'Correct! 22 - 7 = 15, so the equation becomes 3x = 15.',
            ),
          ],
        ),
        EquationStep(
          equation: '3x ÷ 3 = 15 ÷ 3',
          operation: 'Divide both sides by 3',
          explanation: 'To isolate x, we divide both sides of the equation by 3.',
          options: [
            OperationOption(
              text: 'Divide both sides by 3',
              isCorrect: true,
              explanation: 'Correct! Dividing both sides by 3 will isolate the variable x.',
            ),
            OperationOption(
              text: 'Multiply both sides by 3',
              isCorrect: false,
              explanation: 'Multiplying by 3 would make the equation more complex.',
            ),
            OperationOption(
              text: 'Subtract 3 from both sides',
              isCorrect: false,
              explanation: 'Subtracting 3 would not help isolate the variable.',
            ),
          ],
        ),
        EquationStep(
          equation: 'x = 5',
          operation: 'Simplify',
          explanation: 'After dividing both sides by 3, we get x = 5.',
          options: [
            OperationOption(
              text: 'Simplify the equation',
              isCorrect: true,
              explanation: 'Correct! 15 ÷ 3 = 5, so the equation becomes x = 5.',
            ),
          ],
        ),
      ];
    }
    
    // Initialize equation controller with initial equation
    _equationController.text = _initialEquation;
    
    // Initialize colors
    _primaryColor = _parseColor(widget.data['primary_color'], Colors.blue);
    _successColor = _parseColor(widget.data['success_color'], Colors.green);
    _errorColor = _parseColor(widget.data['error_color'], Colors.red);
    _hintColor = _parseColor(widget.data['hint_color'], Colors.orange);
    _neutralColor = _parseColor(widget.data['neutral_color'], Colors.grey.shade200);
  }

  @override
  void dispose() {
    _equationController.dispose();
    super.dispose();
  }

  // Helper method to parse color from string
  Color _parseColor(dynamic colorValue, Color defaultColor) {
    if (colorValue == null) return defaultColor;
    if (colorValue is String) {
      try {
        return Color(int.parse(colorValue.replaceAll('#', '0xFF')));
      } catch (e) {
        return defaultColor;
      }
    }
    return defaultColor;
  }
  
  // Helper method to parse operation options
  List<OperationOption> _parseOptions(dynamic optionsData) {
    final options = <OperationOption>[];
    
    if (optionsData is List) {
      for (final option in optionsData) {
        if (option is Map<String, dynamic>) {
          options.add(OperationOption(
            text: option['text'] ?? '',
            isCorrect: option['is_correct'] ?? false,
            explanation: option['explanation'] ?? '',
          ));
        }
      }
    }
    
    return options;
  }
  
  // Handle operation selection
  void _selectOperation(String operation) {
    setState(() {
      _selectedOperation = operation;
      _errorMessage = null;
    });
  }
  
  // Check if the selected operation is correct
  void _checkOperation() {
    if (_selectedOperation.isEmpty) {
      setState(() {
        _errorMessage = 'Please select an operation';
      });
      return;
    }
    
    final currentStep = _solutionSteps[_currentStepIndex];
    final correctOption = currentStep.options.firstWhere(
      (option) => option.isCorrect,
      orElse: () => OperationOption(
        text: '',
        isCorrect: false,
        explanation: '',
      ),
    );
    
    final isCorrect = _selectedOperation == correctOption.text;
    
    setState(() {
      if (isCorrect) {
        // Move to the next step
        _moveToNextStep();
      } else {
        // Show error message
        _errorMessage = 'That\'s not the best operation for this step. Try again.';
      }
    });
  }
  
  // Move to the next step
  void _moveToNextStep() {
    if (_currentStepIndex < _solutionSteps.length - 1) {
      setState(() {
        _currentStepIndex++;
        _selectedOperation = '';
        _errorMessage = null;
        _showHint = false;
        _equationController.text = _solutionSteps[_currentStepIndex].equation;
      });
    } else {
      // All steps completed
      setState(() {
        _isCompleted = true;
      });
      
      // Notify parent about completion
      widget.onStateChanged?.call(true);
    }
  }
  
  // Show a hint for the current step
  void _toggleHint() {
    setState(() {
      _showHint = !_showHint;
    });
  }
  
  // Reset the widget
  void _reset() {
    setState(() {
      _currentStepIndex = 0;
      _isCompleted = false;
      _showHint = false;
      _errorMessage = null;
      _selectedOperation = '';
      _equationController.text = _initialEquation;
    });
    
    widget.onStateChanged?.call(false);
  }

  @override
  Widget build(BuildContext context) {
    final currentStep = _solutionSteps[_currentStepIndex];
    
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withOpacity(0.2),
            spreadRadius: 1,
            blurRadius: 3,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Title
          Text(
            'Step-by-Step Equation Solver',
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
              color: _primaryColor,
            ),
          ),
          
          const SizedBox(height: 12),
          
          // Current equation
          Container(
            padding: const EdgeInsets.all(12),
            decoration: BoxDecoration(
              color: Colors.grey.shade50,
              borderRadius: BorderRadius.circular(8),
              border: Border.all(color: Colors.grey.shade300),
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'Current Equation:',
                  style: TextStyle(
                    fontWeight: FontWeight.bold,
                    color: _primaryColor,
                  ),
                ),
                const SizedBox(height: 4),
                Text(
                  currentStep.equation,
                  style: const TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
          ),
          
          const SizedBox(height: 16),
          
          // Progress indicator
          LinearProgressIndicator(
            value: (_currentStepIndex) / (_solutionSteps.length - 1),
            backgroundColor: _neutralColor,
            valueColor: AlwaysStoppedAnimation<Color>(_primaryColor),
          ),
          
          Padding(
            padding: const EdgeInsets.symmetric(vertical: 4),
            child: Text(
              'Step ${_currentStepIndex + 1} of ${_solutionSteps.length}',
              style: TextStyle(
                color: Colors.grey.shade600,
                fontSize: 12,
              ),
            ),
          ),
          
          const SizedBox(height: 16),
          
          if (_isCompleted) ...[
            // Completion message
            Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: _successColor.withOpacity(0.1),
                borderRadius: BorderRadius.circular(8),
                border: Border.all(color: _successColor),
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    children: [
                      Icon(Icons.check_circle, color: _successColor),
                      const SizedBox(width: 8),
                      Text(
                        'Equation Solved!',
                        style: TextStyle(
                          fontWeight: FontWeight.bold,
                          color: _successColor,
                          fontSize: 16,
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 8),
                  Text(
                    'You have successfully solved the equation. The solution is $_solution.',
                    style: const TextStyle(fontSize: 14),
                  ),
                  const SizedBox(height: 12),
                  ElevatedButton(
                    onPressed: _reset,
                    style: ElevatedButton.styleFrom(
                      backgroundColor: _successColor,
                      foregroundColor: Colors.white,
                      padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(8),
                      ),
                    ),
                    child: const Text('Solve Another Equation'),
                  ),
                ],
              ),
            ),
          ] else ...[
            // Step explanation
            Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: _primaryColor.withOpacity(0.1),
                borderRadius: BorderRadius.circular(8),
                border: Border.all(color: _primaryColor.withOpacity(0.5)),
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'What should we do next?',
                    style: TextStyle(
                      fontWeight: FontWeight.bold,
                      color: _primaryColor,
                      fontSize: 16,
                    ),
                  ),
                  const SizedBox(height: 8),
                  Text(
                    currentStep.explanation,
                    style: const TextStyle(fontSize: 14),
                  ),
                ],
              ),
            ),
            
            const SizedBox(height: 16),
            
            // Operation options
            if (currentStep.options.isNotEmpty) ...[
              Text(
                'Select the next operation:',
                style: const TextStyle(fontWeight: FontWeight.bold),
              ),
              const SizedBox(height: 8),
              ...currentStep.options.map((option) {
                final isSelected = option.text == _selectedOperation;
                return Container(
                  margin: const EdgeInsets.only(bottom: 8),
                  child: InkWell(
                    onTap: () => _selectOperation(option.text),
                    borderRadius: BorderRadius.circular(8),
                    child: Container(
                      padding: const EdgeInsets.all(12),
                      decoration: BoxDecoration(
                        color: isSelected ? _primaryColor.withOpacity(0.1) : _neutralColor,
                        borderRadius: BorderRadius.circular(8),
                        border: Border.all(
                          color: isSelected ? _primaryColor : Colors.grey.shade300,
                          width: isSelected ? 2 : 1,
                        ),
                      ),
                      child: Row(
                        children: [
                          Icon(
                            isSelected ? Icons.radio_button_checked : Icons.radio_button_unchecked,
                            color: isSelected ? _primaryColor : Colors.grey.shade600,
                            size: 20,
                          ),
                          const SizedBox(width: 8),
                          Expanded(
                            child: Text(
                              option.text,
                              style: TextStyle(
                                fontWeight: isSelected ? FontWeight.bold : FontWeight.normal,
                                color: isSelected ? _primaryColor : Colors.black,
                              ),
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),
                );
              }).toList(),
              
              if (_errorMessage != null) ...[
                const SizedBox(height: 8),
                Text(
                  _errorMessage!,
                  style: TextStyle(color: _errorColor, fontStyle: FontStyle.italic),
                ),
              ],
              
              const SizedBox(height: 16),
              
              // Action buttons
              Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  // Continue button
                  ElevatedButton(
                    onPressed: _checkOperation,
                    style: ElevatedButton.styleFrom(
                      backgroundColor: _primaryColor,
                      foregroundColor: Colors.white,
                      padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(8),
                      ),
                    ),
                    child: const Text('Continue'),
                  ),
                  
                  const SizedBox(width: 16),
                  
                  // Hint button
                  OutlinedButton(
                    onPressed: _toggleHint,
                    style: OutlinedButton.styleFrom(
                      foregroundColor: _hintColor,
                      side: BorderSide(color: _hintColor),
                      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(8),
                      ),
                    ),
                    child: Text(_showHint ? 'Hide Hint' : 'Show Hint'),
                  ),
                ],
              ),
              
              if (_showHint) ...[
                const SizedBox(height: 16),
                Container(
                  padding: const EdgeInsets.all(12),
                  decoration: BoxDecoration(
                    color: _hintColor.withOpacity(0.1),
                    borderRadius: BorderRadius.circular(8),
                    border: Border.all(color: _hintColor),
                  ),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Row(
                        children: [
                          Icon(Icons.lightbulb, color: _hintColor),
                          const SizedBox(width: 8),
                          Text(
                            'Hint',
                            style: TextStyle(
                              fontWeight: FontWeight.bold,
                              color: _hintColor,
                              fontSize: 16,
                            ),
                          ),
                        ],
                      ),
                      const SizedBox(height: 8),
                      Text(
                        _getHintForCurrentStep(),
                        style: const TextStyle(fontSize: 14),
                      ),
                    ],
                  ),
                ),
              ],
            ],
          ],
          
          // Widget name tag
          if (widget.data['showNameTag'] ?? true)
            Padding(
              padding: const EdgeInsets.only(top: 8),
              child: Text(
                'InteractiveStepByStepEquationSolverWidget',
                style: TextStyle(
                  color: Colors.grey[600],
                  fontSize: 12,
                  fontStyle: FontStyle.italic,
                ),
              ),
            ),
        ],
      ),
    );
  }
  
  // Get a hint for the current step
  String _getHintForCurrentStep() {
    final currentStep = _solutionSteps[_currentStepIndex];
    
    // If there are options, find the correct one and return its explanation
    if (currentStep.options.isNotEmpty) {
      final correctOption = currentStep.options.firstWhere(
        (option) => option.isCorrect,
        orElse: () => OperationOption(
          text: '',
          isCorrect: false,
          explanation: 'No hint available for this step.',
        ),
      );
      
      return correctOption.explanation;
    }
    
    // Default hint
    return 'Think about what operation would help isolate the variable.';
  }
}

/// Represents a step in the equation solving process
class EquationStep {
  final String equation;
  final String operation;
  final String explanation;
  final List<OperationOption> options;
  
  EquationStep({
    required this.equation,
    required this.operation,
    required this.explanation,
    required this.options,
  });
}

/// Represents an operation option for a step
class OperationOption {
  final String text;
  final bool isCorrect;
  final String explanation;
  
  OperationOption({
    required this.text,
    required this.isCorrect,
    required this.explanation,
  });
}
