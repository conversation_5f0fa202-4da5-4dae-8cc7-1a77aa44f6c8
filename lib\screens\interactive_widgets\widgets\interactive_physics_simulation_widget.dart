import 'package:flutter/material.dart';
import 'dart:math' as math;
import 'dart:async';

/// Custom painters for physics simulations

/// Projectile motion painter
class ProjectileMotionPainter extends CustomPainter {
  final double x;
  final double y;
  final List<Offset> trajectory;
  final Color primaryColor;
  final Color textColor;

  ProjectileMotionPainter({
    required this.x,
    required this.y,
    required this.trajectory,
    required this.primaryColor,
    required this.textColor,
  });

  @override
  void paint(Canvas canvas, Size size) {
    final width = size.width;
    final height = size.height;

    // Draw ground
    final groundPaint = Paint()
      ..color = Colors.brown.withAlpha(100)
      ..style = PaintingStyle.fill;
    canvas.drawRect(
      Rect.fromLTWH(0, height - 20, width, 20),
      groundPaint,
    );

    // Draw grid
    final gridPaint = Paint()
      ..color = Colors.grey.withAlpha(50)
      ..style = PaintingStyle.stroke
      ..strokeWidth = 1;

    // Vertical grid lines
    for (int i = 0; i <= 10; i++) {
      final x = i * width / 10;
      canvas.drawLine(
        Offset(x, 0),
        Offset(x, height - 20),
        gridPaint,
      );
    }

    // Horizontal grid lines
    for (int i = 0; i <= 5; i++) {
      final y = i * (height - 20) / 5;
      canvas.drawLine(
        Offset(0, y),
        Offset(width, y),
        gridPaint,
      );
    }

    // Scale factors for drawing
    final scaleX = width / 100;
    final scaleY = (height - 20) / 50;

    // Draw trajectory
    if (trajectory.isNotEmpty) {
      final trajectoryPaint = Paint()
        ..color = primaryColor.withAlpha(150)
        ..style = PaintingStyle.stroke
        ..strokeWidth = 2;

      final path = Path();
      path.moveTo(
        trajectory.first.dx * scaleX,
        height - 20 - trajectory.first.dy * scaleY,
      );

      for (int i = 1; i < trajectory.length; i++) {
        path.lineTo(
          trajectory[i].dx * scaleX,
          height - 20 - trajectory[i].dy * scaleY,
        );
      }

      canvas.drawPath(path, trajectoryPaint);
    }

    // Draw projectile
    if (y >= 0) {
      final projectilePaint = Paint()
        ..color = primaryColor
        ..style = PaintingStyle.fill;

      canvas.drawCircle(
        Offset(x * scaleX, height - 20 - y * scaleY),
        8,
        projectilePaint,
      );
    }

    // Draw axes labels
    final textStyle = TextStyle(
      color: textColor,
      fontSize: 12,
    );

    final textPainter = TextPainter(
      textDirection: TextDirection.ltr,
    );

    // X-axis labels
    for (int i = 0; i <= 10; i++) {
      final value = (i * 10).toString();
      textPainter.text = TextSpan(text: value, style: textStyle);
      textPainter.layout();
      textPainter.paint(
        canvas,
        Offset(i * width / 10 - textPainter.width / 2, height - 15),
      );
    }

    // Y-axis labels
    for (int i = 0; i <= 5; i++) {
      final value = (i * 10).toString();
      textPainter.text = TextSpan(text: value, style: textStyle);
      textPainter.layout();
      textPainter.paint(
        canvas,
        Offset(5, height - 20 - i * (height - 20) / 5 - textPainter.height / 2),
      );
    }
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => true;
}

/// Pendulum painter
class PendulumPainter extends CustomPainter {
  final double angle;
  final double length;
  final List<Offset> trajectory;
  final Color primaryColor;
  final Color textColor;

  PendulumPainter({
    required this.angle,
    required this.length,
    required this.trajectory,
    required this.primaryColor,
    required this.textColor,
  });

  @override
  void paint(Canvas canvas, Size size) {
    final width = size.width;
    final height = size.height;

    // Draw pivot point
    final pivotPaint = Paint()
      ..color = Colors.grey.shade700
      ..style = PaintingStyle.fill;

    final pivotPoint = Offset(width / 2, 50);
    canvas.drawCircle(pivotPoint, 5, pivotPaint);

    // Scale factor for pendulum length
    final scale = (height - 100) / 5;

    // Calculate pendulum bob position
    final bobX = pivotPoint.dx + length * scale * math.sin(angle);
    final bobY = pivotPoint.dy + length * scale * math.cos(angle);
    final bobPosition = Offset(bobX, bobY);

    // Draw pendulum string
    final stringPaint = Paint()
      ..color = Colors.black
      ..style = PaintingStyle.stroke
      ..strokeWidth = 2;

    canvas.drawLine(pivotPoint, bobPosition, stringPaint);

    // Draw pendulum bob
    final bobPaint = Paint()
      ..color = primaryColor
      ..style = PaintingStyle.fill;

    canvas.drawCircle(bobPosition, 15, bobPaint);

    // Draw trajectory
    if (trajectory.isNotEmpty) {
      final trajectoryPaint = Paint()
        ..color = primaryColor.withAlpha(100)
        ..style = PaintingStyle.stroke
        ..strokeWidth = 1;

      final path = Path();
      path.moveTo(
        pivotPoint.dx + trajectory.first.dx * scale,
        pivotPoint.dy + trajectory.first.dy * scale,
      );

      for (int i = 1; i < trajectory.length; i++) {
        path.lineTo(
          pivotPoint.dx + trajectory[i].dx * scale,
          pivotPoint.dy + trajectory[i].dy * scale,
        );
      }

      canvas.drawPath(path, trajectoryPaint);
    }

    // Draw angle indicator
    final anglePaint = Paint()
      ..color = Colors.orange
      ..style = PaintingStyle.stroke
      ..strokeWidth = 2;

    final angleRadius = 30.0;
    final startAngle = -math.pi / 2;
    final sweepAngle = angle;

    canvas.drawArc(
      Rect.fromCircle(center: pivotPoint, radius: angleRadius),
      startAngle,
      sweepAngle,
      false,
      anglePaint,
    );

    // Draw angle text
    final textStyle = TextStyle(
      color: textColor,
      fontSize: 12,
    );

    final textPainter = TextPainter(
      textDirection: TextDirection.ltr,
    );

    final angleText = '${(angle * 180 / math.pi).toStringAsFixed(1)}°';
    textPainter.text = TextSpan(text: angleText, style: textStyle);
    textPainter.layout();

    final textAngle = startAngle + sweepAngle / 2;
    final textX = pivotPoint.dx + (angleRadius + 10) * math.cos(textAngle);
    final textY = pivotPoint.dy + (angleRadius + 10) * math.sin(textAngle);

    textPainter.paint(
      canvas,
      Offset(textX - textPainter.width / 2, textY - textPainter.height / 2),
    );
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => true;
}

/// Spring painter
class SpringPainter extends CustomPainter {
  final double position;
  final List<double> positions;
  final Color primaryColor;
  final Color textColor;

  SpringPainter({
    required this.position,
    required this.positions,
    required this.primaryColor,
    required this.textColor,
  });

  @override
  void paint(Canvas canvas, Size size) {
    final width = size.width;
    final height = size.height;

    // Draw wall
    final wallPaint = Paint()
      ..color = Colors.grey.shade700
      ..style = PaintingStyle.fill;

    canvas.drawRect(
      Rect.fromLTWH(0, height / 2 - 50, 20, 100),
      wallPaint,
    );

    // Draw spring
    final springPaint = Paint()
      ..color = Colors.black
      ..style = PaintingStyle.stroke
      ..strokeWidth = 2;

    final springPath = Path();
    springPath.moveTo(20, height / 2);

    final springLength = width / 2 + position * 50;
    final coils = 10;
    final coilWidth = springLength / coils;
    final coilHeight = 30.0;

    for (int i = 0; i < coils; i++) {
      final x = 20 + i * coilWidth;
      final y = height / 2;

      if (i % 2 == 0) {
        springPath.quadraticBezierTo(
          x + coilWidth / 2, y - coilHeight,
          x + coilWidth, y,
        );
      } else {
        springPath.quadraticBezierTo(
          x + coilWidth / 2, y + coilHeight,
          x + coilWidth, y,
        );
      }
    }

    canvas.drawPath(springPath, springPaint);

    // Draw mass
    final massPaint = Paint()
      ..color = primaryColor
      ..style = PaintingStyle.fill;

    final massPosition = Offset(20 + springLength, height / 2);
    canvas.drawRect(
      Rect.fromCenter(
        center: massPosition,
        width: 40,
        height: 40,
      ),
      massPaint,
    );

    // Draw position graph
    if (positions.isNotEmpty) {
      final graphPaint = Paint()
        ..color = primaryColor.withAlpha(150)
        ..style = PaintingStyle.stroke
        ..strokeWidth = 2;

      final graphPath = Path();
      final graphHeight = 80.0;
      final graphY = height - 50.0;

      graphPath.moveTo(
        0,
        graphY - positions.first * 20,
      );

      for (int i = 1; i < positions.length; i++) {
        graphPath.lineTo(
          i * width / positions.length,
          graphY - positions[i] * 20,
        );
      }

      canvas.drawPath(graphPath, graphPaint);

      // Draw graph axes
      final axesPaint = Paint()
        ..color = Colors.grey
        ..style = PaintingStyle.stroke
        ..strokeWidth = 1;

      // X-axis
      canvas.drawLine(
        Offset(0, graphY),
        Offset(width, graphY),
        axesPaint,
      );

      // Y-axis
      canvas.drawLine(
        Offset(0, graphY - graphHeight),
        Offset(0, graphY + graphHeight),
        axesPaint,
      );
    }

    // Draw position text
    final textStyle = TextStyle(
      color: textColor,
      fontSize: 14,
    );

    final textPainter = TextPainter(
      textDirection: TextDirection.ltr,
    );

    final positionText = 'Position: ${position.toStringAsFixed(2)}';
    textPainter.text = TextSpan(text: positionText, style: textStyle);
    textPainter.layout();

    textPainter.paint(
      canvas,
      Offset(width - textPainter.width - 10, 10),
    );
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => true;
}

/// Wave painter
class WavePainter extends CustomPainter {
  final List<Offset> points;
  final Color primaryColor;
  final Color textColor;

  WavePainter({
    required this.points,
    required this.primaryColor,
    required this.textColor,
  });

  @override
  void paint(Canvas canvas, Size size) {
    final width = size.width;
    final height = size.height;

    // Draw axes
    final axesPaint = Paint()
      ..color = Colors.grey
      ..style = PaintingStyle.stroke
      ..strokeWidth = 1;

    // X-axis
    canvas.drawLine(
      Offset(0, height / 2),
      Offset(width, height / 2),
      axesPaint,
    );

    // Y-axis
    canvas.drawLine(
      Offset(0, 0),
      Offset(0, height),
      axesPaint,
    );

    // Draw grid
    final gridPaint = Paint()
      ..color = Colors.grey.withAlpha(30)
      ..style = PaintingStyle.stroke
      ..strokeWidth = 1;

    // Vertical grid lines
    for (int i = 1; i <= 10; i++) {
      final x = i * width / 10;
      canvas.drawLine(
        Offset(x, 0),
        Offset(x, height),
        gridPaint,
      );
    }

    // Horizontal grid lines
    for (int i = 1; i <= 4; i++) {
      final y = i * height / 4;
      canvas.drawLine(
        Offset(0, y),
        Offset(width, y),
        gridPaint,
      );
    }

    // Draw wave
    if (points.isNotEmpty) {
      final wavePaint = Paint()
        ..color = primaryColor
        ..style = PaintingStyle.stroke
        ..strokeWidth = 3;

      final path = Path();
      path.moveTo(
        points.first.dx * width / 10,
        height / 2 - points.first.dy * height / 4,
      );

      for (int i = 1; i < points.length; i++) {
        path.lineTo(
          points[i].dx * width / 10,
          height / 2 - points[i].dy * height / 4,
        );
      }

      canvas.drawPath(path, wavePaint);
    }

    // Draw labels
    final textStyle = TextStyle(
      color: textColor,
      fontSize: 12,
    );

    final textPainter = TextPainter(
      textDirection: TextDirection.ltr,
    );

    // X-axis labels
    for (int i = 0; i <= 10; i++) {
      final value = i.toString();
      textPainter.text = TextSpan(text: value, style: textStyle);
      textPainter.layout();
      textPainter.paint(
        canvas,
        Offset(i * width / 10 - textPainter.width / 2, height / 2 + 5),
      );
    }

    // Y-axis labels
    for (int i = -2; i <= 2; i++) {
      if (i == 0) continue;
      final value = i.toString();
      textPainter.text = TextSpan(text: value, style: textStyle);
      textPainter.layout();
      textPainter.paint(
        canvas,
        Offset(5, height / 2 - i * height / 4 - textPainter.height / 2),
      );
    }
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => true;
}

/// A widget that provides interactive physics simulations
class InteractivePhysicsSimulationWidget extends StatefulWidget {
  /// The data for this widget
  final Map<String, dynamic> data;

  /// Optional callback when the widget state changes
  final Function(bool isCompleted)? onStateChanged;

  const InteractivePhysicsSimulationWidget({
    super.key,
    required this.data,
    this.onStateChanged,
  });

  /// Factory constructor to create the widget from JSON data
  factory InteractivePhysicsSimulationWidget.fromData(
      Map<String, dynamic> data) {
    return InteractivePhysicsSimulationWidget(
      data: data,
    );
  }

  @override
  State<InteractivePhysicsSimulationWidget> createState() =>
      _InteractivePhysicsSimulationWidgetState();
}

class _InteractivePhysicsSimulationWidgetState
    extends State<InteractivePhysicsSimulationWidget>
    with SingleTickerProviderStateMixin {
  // Colors
  late Color _primaryColor;
  late Color _textColor;
  late Color _backgroundColor;

  // Simulation types
  late List<String> _simulationTypes;
  late String _currentSimulationType;

  // Animation controller
  late AnimationController _animationController;

  // Simulation parameters
  late Map<String, double> _parameters;
  late Map<String, Map<String, dynamic>> _parameterRanges;

  // UI state
  bool _isSimulating = false;
  bool _showExplanation = false;
  String _statusMessage = '';

  // Timer for physics calculations
  Timer? _simulationTimer;

  // Projectile motion state
  double _projectileX = 0;
  double _projectileY = 0;
  double _projectileVelocityX = 0;
  double _projectileVelocityY = 0;
  double _projectileTime = 0;
  List<Offset> _projectileTrajectory = [];

  // Pendulum state
  double _pendulumAngle = math.pi / 4; // 45 degrees
  double _pendulumAngularVelocity = 0;
  List<Offset> _pendulumTrajectory = [];

  // Spring state
  double _springPosition = 0;
  double _springVelocity = 0;
  List<double> _springPositions = [];

  // Wave state
  double _waveTime = 0;
  List<Offset> _wavePoints = [];

  @override
  void initState() {
    super.initState();

    // Initialize colors
    _primaryColor = _getColorFromHex(
        widget.data['primaryColor'] ?? '#2196F3'); // Blue
    _textColor =
        _getColorFromHex(widget.data['textColor'] ?? '#212121'); // Dark Grey
    _backgroundColor = _getColorFromHex(
        widget.data['backgroundColor'] ?? '#FFFFFF'); // White

    // Initialize simulation types
    _simulationTypes = [
      'Projectile Motion',
      'Pendulum',
      'Spring',
      'Wave',
    ];
    _currentSimulationType = widget.data['defaultSimulation'] ?? _simulationTypes[0];

    // Initialize animation controller
    _animationController = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 16), // ~60 FPS
    );

    // Initialize parameters with defaults
    _parameters = {
      'gravity': 9.8,
      'initialVelocity': 20.0,
      'angle': 45.0,
      'length': 1.0,
      'mass': 1.0,
      'springConstant': 10.0,
      'damping': 0.1,
      'frequency': 1.0,
      'amplitude': 1.0,
    };

    // Initialize parameter ranges
    _parameterRanges = {
      'gravity': {'min': 0.1, 'max': 20.0, 'divisions': 100},
      'initialVelocity': {'min': 1.0, 'max': 50.0, 'divisions': 100},
      'angle': {'min': 0.0, 'max': 90.0, 'divisions': 90},
      'length': {'min': 0.1, 'max': 5.0, 'divisions': 50},
      'mass': {'min': 0.1, 'max': 10.0, 'divisions': 100},
      'springConstant': {'min': 1.0, 'max': 50.0, 'divisions': 100},
      'damping': {'min': 0.0, 'max': 1.0, 'divisions': 100},
      'frequency': {'min': 0.1, 'max': 5.0, 'divisions': 50},
      'amplitude': {'min': 0.1, 'max': 5.0, 'divisions': 50},
    };

    // Override defaults with data from widget
    if (widget.data['parameters'] != null) {
      for (var param in widget.data['parameters']) {
        if (param['name'] != null && param['default'] != null) {
          _parameters[param['name']] = param['default'].toDouble();
        }
      }
    }

    // Initialize simulation based on type
    _resetSimulation();
  }

  @override
  void dispose() {
    _animationController.dispose();
    _simulationTimer?.cancel();
    super.dispose();
  }

  // Convert hex color string to Color
  Color _getColorFromHex(String hexColor) {
    hexColor = hexColor.replaceAll('#', '');
    if (hexColor.length == 6) {
      hexColor = 'FF$hexColor';
    }
    return Color(int.parse(hexColor, radix: 16));
  }

  // Reset simulation state
  void _resetSimulation() {
    _simulationTimer?.cancel();

    setState(() {
      _isSimulating = false;
      _statusMessage = '';

      // Reset specific simulation state
      switch (_currentSimulationType) {
        case 'Projectile Motion':
          _projectileX = 0;
          _projectileY = 0;
          _projectileVelocityX = _parameters['initialVelocity']! *
              math.cos(_parameters['angle']! * math.pi / 180);
          _projectileVelocityY = _parameters['initialVelocity']! *
              math.sin(_parameters['angle']! * math.pi / 180);
          _projectileTime = 0;
          _projectileTrajectory = [];
          break;
        case 'Pendulum':
          _pendulumAngle = _parameters['angle']! * math.pi / 180;
          _pendulumAngularVelocity = 0;
          _pendulumTrajectory = [];
          break;
        case 'Spring':
          _springPosition = _parameters['amplitude']!;
          _springVelocity = 0;
          _springPositions = [];
          break;
        case 'Wave':
          _waveTime = 0;
          _wavePoints = [];
          break;
      }
    });
  }

  // Start simulation
  void _startSimulation() {
    if (_isSimulating) return;

    setState(() {
      _isSimulating = true;
      _statusMessage = 'Simulating $_currentSimulationType...';
    });

    // Start animation controller
    _animationController.repeat();

    // Start physics calculations
    const timeStep = 1 / 60; // 60 FPS
    _simulationTimer = Timer.periodic(const Duration(milliseconds: 16), (timer) {
      setState(() {
        // Update physics based on simulation type
        switch (_currentSimulationType) {
          case 'Projectile Motion':
            _updateProjectileMotion(timeStep);
            break;
          case 'Pendulum':
            _updatePendulum(timeStep);
            break;
          case 'Spring':
            _updateSpring(timeStep);
            break;
          case 'Wave':
            _updateWave(timeStep);
            break;
        }
      });
    });
  }

  // Stop simulation
  void _stopSimulation() {
    _simulationTimer?.cancel();
    _animationController.stop();

    setState(() {
      _isSimulating = false;
      _statusMessage = 'Simulation stopped';
    });
  }

  // Update projectile motion physics
  void _updateProjectileMotion(double dt) {
    // Update time
    _projectileTime += dt;

    // Calculate new position
    _projectileX = _projectileVelocityX * _projectileTime;
    _projectileY = _projectileVelocityY * _projectileTime -
        0.5 * _parameters['gravity']! * _projectileTime * _projectileTime;

    // Add point to trajectory
    _projectileTrajectory.add(Offset(_projectileX, _projectileY));

    // Limit trajectory points to prevent memory issues
    if (_projectileTrajectory.length > 500) {
      _projectileTrajectory.removeAt(0);
    }

    // Check if projectile has hit the ground
    if (_projectileY < 0) {
      _stopSimulation();
      _statusMessage = 'Projectile landed at distance: ${_projectileX.toStringAsFixed(2)} m';
    }
  }

  // Update pendulum physics
  void _updatePendulum(double dt) {
    // Calculate angular acceleration
    final angularAcceleration = -_parameters['gravity']! / _parameters['length']! *
        math.sin(_pendulumAngle);

    // Update angular velocity and angle
    _pendulumAngularVelocity += angularAcceleration * dt;
    _pendulumAngle += _pendulumAngularVelocity * dt;

    // Add damping
    _pendulumAngularVelocity *= (1 - _parameters['damping']! * dt);

    // Calculate pendulum position
    final x = _parameters['length']! * math.sin(_pendulumAngle);
    final y = -_parameters['length']! * math.cos(_pendulumAngle);

    // Add point to trajectory
    _pendulumTrajectory.add(Offset(x, y));

    // Limit trajectory points
    if (_pendulumTrajectory.length > 500) {
      _pendulumTrajectory.removeAt(0);
    }
  }

  // Update spring physics
  void _updateSpring(double dt) {
    // Calculate acceleration
    final acceleration = -_parameters['springConstant']! / _parameters['mass']! *
        _springPosition - _parameters['damping']! * _springVelocity;

    // Update velocity and position
    _springVelocity += acceleration * dt;
    _springPosition += _springVelocity * dt;

    // Add position to history
    _springPositions.add(_springPosition);

    // Limit history points
    if (_springPositions.length > 500) {
      _springPositions.removeAt(0);
    }
  }

  // Update wave physics
  void _updateWave(double dt) {
    // Update time
    _waveTime += dt;

    // Calculate wave points
    _wavePoints = List.generate(100, (i) {
      final x = i / 10.0;
      final y = _parameters['amplitude']! *
          math.sin(2 * math.pi * _parameters['frequency']! * (x - _waveTime));
      return Offset(x, y);
    });
  }

  // Build simulation type selector
  Widget _buildSimulationTypeSelector() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Simulation Type',
          style: TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.bold,
            color: _textColor,
          ),
        ),
        const SizedBox(height: 8),
        SingleChildScrollView(
          scrollDirection: Axis.horizontal,
          child: Row(
            children: _simulationTypes.map((type) {
              final isSelected = type == _currentSimulationType;
              return Padding(
                padding: const EdgeInsets.only(right: 8),
                child: ChoiceChip(
                  label: Text(type),
                  selected: isSelected,
                  onSelected: (selected) {
                    if (selected && !_isSimulating) {
                      setState(() {
                        _currentSimulationType = type;
                        _resetSimulation();
                      });
                    } else if (_isSimulating) {
                      setState(() {
                        _statusMessage = 'Stop simulation before changing type';
                      });
                    }
                  },
                  backgroundColor: Colors.grey.withAlpha(50),
                  selectedColor: _primaryColor.withAlpha(100),
                ),
              );
            }).toList(),
          ),
        ),
      ],
    );
  }

  // Build parameter controls based on simulation type
  Widget _buildParameterControls() {
    final parameters = _getParametersForSimulation();

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Parameters',
          style: TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.bold,
            color: _textColor,
          ),
        ),
        const SizedBox(height: 8),
        for (final param in parameters)
          Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Text(
                    _formatParameterName(param),
                    style: TextStyle(
                      color: _textColor,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                  Text(
                    _parameters[param]!.toStringAsFixed(2),
                    style: TextStyle(
                      color: _textColor,
                    ),
                  ),
                ],
              ),
              Slider(
                value: _parameters[param]!,
                min: _parameterRanges[param]!['min'],
                max: _parameterRanges[param]!['max'],
                divisions: _parameterRanges[param]!['divisions'],
                onChanged: _isSimulating
                    ? null
                    : (value) {
                        setState(() {
                          _parameters[param] = value;
                          _resetSimulation();
                        });
                      },
                activeColor: _primaryColor,
              ),
              const SizedBox(height: 8),
            ],
          ),
      ],
    );
  }

  // Get parameters relevant to the current simulation
  List<String> _getParametersForSimulation() {
    switch (_currentSimulationType) {
      case 'Projectile Motion':
        return ['gravity', 'initialVelocity', 'angle'];
      case 'Pendulum':
        return ['gravity', 'length', 'angle', 'damping'];
      case 'Spring':
        return ['mass', 'springConstant', 'damping', 'amplitude'];
      case 'Wave':
        return ['frequency', 'amplitude'];
      default:
        return [];
    }
  }

  // Format parameter name for display
  String _formatParameterName(String param) {
    final words = param.split(RegExp(r'(?=[A-Z])'));
    return words.map((word) => word[0].toUpperCase() + word.substring(1)).join(' ');
  }

  // Build simulation controls
  Widget _buildSimulationControls() {
    return Row(
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        ElevatedButton.icon(
          onPressed: _isSimulating ? null : _startSimulation,
          icon: const Icon(Icons.play_arrow),
          label: const Text('Start'),
          style: ElevatedButton.styleFrom(
            backgroundColor: _primaryColor,
            foregroundColor: Colors.white,
          ),
        ),
        const SizedBox(width: 16),
        ElevatedButton.icon(
          onPressed: _isSimulating ? _stopSimulation : null,
          icon: const Icon(Icons.stop),
          label: const Text('Stop'),
          style: ElevatedButton.styleFrom(
            backgroundColor: Colors.red,
            foregroundColor: Colors.white,
          ),
        ),
        const SizedBox(width: 16),
        ElevatedButton.icon(
          onPressed: _isSimulating ? null : _resetSimulation,
          icon: const Icon(Icons.refresh),
          label: const Text('Reset'),
          style: ElevatedButton.styleFrom(
            backgroundColor: Colors.grey,
            foregroundColor: Colors.white,
          ),
        ),
      ],
    );
  }

  // Build simulation visualization
  Widget _buildSimulationVisualization() {
    return Container(
      height: 300,
      width: double.infinity,
      decoration: BoxDecoration(
        color: _backgroundColor,
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: Colors.grey.withAlpha(100)),
      ),
      child: ClipRRect(
        borderRadius: BorderRadius.circular(8),
        child: CustomPaint(
          painter: _getSimulationPainter(),
          size: const Size(double.infinity, 300),
        ),
      ),
    );
  }

  // Get the appropriate painter for the current simulation
  CustomPainter _getSimulationPainter() {
    switch (_currentSimulationType) {
      case 'Projectile Motion':
        return ProjectileMotionPainter(
          x: _projectileX,
          y: _projectileY,
          trajectory: _projectileTrajectory,
          primaryColor: _primaryColor,
          textColor: _textColor,
        );
      case 'Pendulum':
        return PendulumPainter(
          angle: _pendulumAngle,
          length: _parameters['length']!,
          trajectory: _pendulumTrajectory,
          primaryColor: _primaryColor,
          textColor: _textColor,
        );
      case 'Spring':
        return SpringPainter(
          position: _springPosition,
          positions: _springPositions,
          primaryColor: _primaryColor,
          textColor: _textColor,
        );
      case 'Wave':
        return WavePainter(
          points: _wavePoints,
          primaryColor: _primaryColor,
          textColor: _textColor,
        );
      default:
        return ProjectileMotionPainter(
          x: 0,
          y: 0,
          trajectory: [],
          primaryColor: _primaryColor,
          textColor: _textColor,
        );
    }
  }

  // Build explanation panel
  Widget _buildExplanation() {
    if (!_showExplanation) {
      return TextButton.icon(
        onPressed: () {
          setState(() {
            _showExplanation = true;
          });
        },
        icon: const Icon(Icons.info_outline),
        label: const Text('Show Explanation'),
      );
    }

    String explanation = '';
    switch (_currentSimulationType) {
      case 'Projectile Motion':
        explanation =
            'Projectile motion is the motion of an object thrown or projected into the air, subject only to gravity. '
            'The path followed by a projectile is called its trajectory. '
            'Key equations: x = v₀cosθ·t, y = v₀sinθ·t - ½gt²';
        break;
      case 'Pendulum':
        explanation =
            'A pendulum is a weight suspended from a pivot so that it can swing freely. '
            'For small angles, the period T is approximately T = 2π√(L/g), where L is the length and g is gravity. '
            'The motion is approximately simple harmonic motion.';
        break;
      case 'Spring':
        explanation =
            'A spring-mass system follows Hooke\'s Law: F = -kx, where k is the spring constant and x is displacement. '
            'The motion is simple harmonic with period T = 2π√(m/k), where m is the mass. '
            'Damping causes the oscillations to decrease over time.';
        break;
      case 'Wave':
        explanation =
            'Waves are disturbances that transfer energy through matter or space. '
            'The wave equation is y = Asin(2πf(x-vt)), where A is amplitude, f is frequency, and v is wave speed. '
            'Key properties include wavelength, frequency, amplitude, and speed.';
        break;
    }

    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: Colors.grey.withAlpha(20),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: Colors.grey.withAlpha(100)),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                'Explanation',
                style: TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.bold,
                  color: _textColor,
                ),
              ),
              IconButton(
                icon: const Icon(Icons.close),
                onPressed: () {
                  setState(() {
                    _showExplanation = false;
                  });
                },
                padding: EdgeInsets.zero,
                constraints: const BoxConstraints(),
              ),
            ],
          ),
          const SizedBox(height: 8),
          Text(
            explanation,
            style: TextStyle(
              color: _textColor,
              fontSize: 14,
            ),
          ),
          const SizedBox(height: 8),
          Align(
            alignment: Alignment.centerRight,
            child: TextButton(
              onPressed: () {
                widget.onStateChanged?.call(true);
              },
              child: const Text('Mark as Completed'),
            ),
          ),
        ],
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
        side: BorderSide(color: _primaryColor.withAlpha(77)),
      ),
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Title and description
            Text(
              widget.data['title'] ?? 'Physics Simulation',
              style: TextStyle(
                fontSize: 20,
                fontWeight: FontWeight.bold,
                color: _textColor,
              ),
            ),
            const SizedBox(height: 8),
            Text(
              widget.data['description'] ??
                  'Explore physics concepts through interactive simulations',
              style: TextStyle(
                fontSize: 14,
                color: _textColor.withAlpha(179),
              ),
            ),
            const SizedBox(height: 16),

            // Simulation type selector
            _buildSimulationTypeSelector(),

            const SizedBox(height: 16),

            // Parameter controls
            _buildParameterControls(),

            const SizedBox(height: 16),

            // Simulation controls
            _buildSimulationControls(),

            const SizedBox(height: 16),

            // Simulation visualization
            _buildSimulationVisualization(),

            const SizedBox(height: 16),

            // Status message
            if (_statusMessage.isNotEmpty)
              Container(
                width: double.infinity,
                padding: const EdgeInsets.all(8),
                decoration: BoxDecoration(
                  color: _primaryColor.withAlpha(26),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Text(
                  _statusMessage,
                  style: TextStyle(
                    color: _primaryColor,
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ),

            const SizedBox(height: 16),

            // Explanation
            _buildExplanation(),
          ],
        ),
      ),
    );
  }
}
