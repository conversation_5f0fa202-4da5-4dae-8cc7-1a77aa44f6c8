{"id": "rf_informal_fallacies", "title": "Identifying and Avoiding Informal Fallacies", "description": "Learn to recognize common errors in reasoning that weaken arguments.", "estimated_lesson_duration_minutes": 70, "lessons": [{"id": "rf-if-l1-fallacies-relevance", "title": "Fallacies of Relevance: Arguments that Distract", "description": "Explore fallacies like ad hominem and appeal to emotion.", "xp_reward": 100, "content_blocks": [{"type": "text", "content": "Informal fallacies are errors in reasoning that are not based on the formal structure of an argument, but rather on its content or the way it's presented. Fallacies of relevance occur when the premises of an argument are not logically relevant to its conclusion, even if they might seem psychologically persuasive."}, {"type": "heading", "content": "<PERSON> (Attacking the Person)"}, {"type": "text", "content": "This fallacy occurs when someone attacks the person making an argument rather than the argument itself. Example: 'Don't listen to her ideas about city planning; she's always late for meetings!'"}, {"type": "heading", "content": "Appeal to Emotion (Argumentum ad Populum/Passiones)"}, {"type": "text", "content": "This involves manipulating emotions (like fear, pity, or joy) to persuade someone, rather than using logical reasoning. Example: 'You must donate to our cause; think of all the suffering children!'"}, {"type": "heading", "content": "<PERSON>"}, {"type": "text", "content": "A red herring is an irrelevant topic introduced into an argument to divert the attention of listeners or readers from the original issue. Example: 'We need to talk about the company's poor environmental record.' - 'But look at how much we donate to charity!'"}, {"type": "interactive_element_placeholder", "interactive_element": {"type": "interactive_fallacy_matcher", "prompt": "Match the fallacy name to its description.", "items_to_match": [{"id": "f1", "text": "Ad Hominem"}, {"id": "f2", "text": "Appeal to Emotion"}, {"id": "f3", "text": "<PERSON>"}], "definitions": [{"id": "d1", "text": "Attacking the person instead of the argument."}, {"id": "d2", "text": "Manipulating feelings instead of using logic."}, {"id": "d3", "text": "Introducing an irrelevant topic to distract."}], "correct_pairs": [{"item_id": "f1", "definition_id": "d1"}, {"item_id": "f2", "definition_id": "d2"}, {"item_id": "f3", "definition_id": "d3"}]}}]}, {"id": "rf-if-l2-fallacies-weak-induction", "title": "Fallacies of Weak Induction: Arguments with Insufficient Evidence", "description": "Explore fallacies like hasty generalization.", "xp_reward": 100, "content_blocks": [{"type": "text", "content": "Fallacies of weak induction occur when the premises provide some support for the conclusion, but not enough to make it likely true. The connection between premises and conclusion is too weak."}, {"type": "heading", "content": "Hasty Generalization"}, {"type": "text", "content": "Drawing a conclusion about an entire group based on an unrepresentative or too-small sample. Example: 'I met two people from City X, and they were both rude. Therefore, everyone from City X is rude.'"}, {"type": "heading", "content": "Appeal to Unqualified Authority (Argumentum ad Verecundiam)"}, {"type": "text", "content": "Citing an authority figure who is not an expert in the relevant field. Example: 'My favorite actor says this brand of vitamins is the best, so it must be true.'"}, {"type": "heading", "content": "False Cause (Post Hoc Ergo Propter Hoc)"}, {"type": "text", "content": "Assuming that because one event followed another, the first event caused the second. Example: 'I wore my lucky socks, and my team won. Therefore, my lucky socks caused the win.'"}, {"type": "interactive_element_placeholder", "interactive_element": {"type": "multiple_choice_text", "question_text": "A food blogger tries one dish at a new restaurant and declares the entire restaurant terrible. This is an example of:", "options": [{"id": "fw1", "text": "False Cause", "is_correct": false, "feedback_incorrect": "False cause relates to assuming causation from sequence."}, {"id": "fw2", "text": "Hasty Generalization", "is_correct": true, "feedback_correct": "Correct! One dish is too small a sample to judge the whole restaurant."}, {"id": "fw3", "text": "Appeal to Unqualified Authority", "is_correct": false, "feedback_incorrect": "This involves citing a non-expert."}], "action_button_text": "Check Answer"}}]}, {"id": "rf-if-l3-fallacies-presumption", "title": "Fallacies of Presumption: Arguments with Unwarranted Assumptions", "description": "Explore fallacies like begging the question.", "xp_reward": 100, "content_blocks": [{"type": "text", "content": "Fallacies of presumption arise because the premises presume what they are supposed to prove, or make other unwarranted assumptions."}, {"type": "heading", "content": "Begging the Question (<PERSON><PERSON>)"}, {"type": "text", "content": "The argument's premises assume the truth of the conclusion, instead of supporting it. It's circular reasoning. Example: 'The Bible is true because it is the word of God, and we know <PERSON> exists and tells the truth because the Bible says so.'"}, {"type": "heading", "content": "Complex Question (Loaded Question)"}, {"type": "text", "content": "Asking a question that presupposes something that has not been proven or accepted by all the people involved. Example: 'Have you stopped cheating on exams?' (This presumes the person has cheated in the past)."}, {"type": "heading", "content": "False Dichotomy (False Dilemma)"}, {"type": "text", "content": "Presenting only two choices as the only possibilities, when in fact other alternatives exist. Example: 'Either you're with us, or you're against us.'"}, {"type": "interactive_element_placeholder", "interactive_element": {"type": "interactive_spot_the_fallacy_in_dialogue", "prompt": "Read the dialogue and identify the fallacy of presumption being used.", "dialogue": [{"speaker": "<PERSON>", "line": "This new policy is clearly the best one because it's the most optimal solution."}, {"speaker": "<PERSON>", "line": "Why is it the most optimal?"}, {"speaker": "<PERSON>", "line": "Because it's superior to all other options, of course!"}], "fallacy_type": "Begging the Question", "explanation": "<PERSON>'s reasoning is circular. 'Best' and 'most optimal' mean the same thing, and 'superior to all other options' is just another way of saying it's the best. The argument doesn't provide independent support for the policy being the best."}}]}, {"id": "rf-if-l4-fallacies-ambiguity", "title": "Fallacies of Ambiguity: Arguments with Unclear Language", "description": "Explore fallacies like equivocation.", "xp_reward": 100, "content_blocks": [{"type": "text", "content": "Fallacies of ambiguity arise from the use of unclear or equivocal language in an argument."}, {"type": "heading", "content": "Equivocation"}, {"type": "text", "content": "Using a word or phrase with two or more different meanings in an argument, making it appear as though the meanings are the same. Example: 'Feathers are light. What is light cannot be dark. Therefore, feathers cannot be dark.' (The word 'light' has two different meanings here)."}, {"type": "heading", "content": "Amphiboly"}, {"type": "text", "content": "This occurs when the grammatical construction of a sentence is ambiguous, allowing for multiple interpretations. Example: 'The tourist saw a man on a hill with a telescope.' (Who has the telescope? The tourist or the man on the hill?)"}, {"type": "interactive_element_placeholder", "interactive_element": {"type": "multiple_choice_text", "question_text": "Argument: 'All banks are by the riverside. The place where I deposit my money is a bank. Therefore, the place where I deposit my money is by the riverside.' What fallacy is committed?", "options": [{"id": "fa1", "text": "Amphiboly", "is_correct": false, "feedback_incorrect": "Amphiboly is about sentence structure ambiguity."}, {"id": "fa2", "text": "Equivocation", "is_correct": true, "feedback_correct": "Correct! The word 'bank' is used with two different meanings (river bank vs. financial institution)."}, {"id": "fa3", "text": "False Dichotomy", "is_correct": false, "feedback_incorrect": "False dichotomy presents only two options when more exist."}], "action_button_text": "Check Answer"}}]}, {"id": "rf-if-l5-analyzing-real-world-fallacies", "title": "Analyzing Real-World Examples of Fallacies", "description": "Identify flawed reasoning in various contexts.", "xp_reward": 100, "content_blocks": [{"type": "text", "content": "Fallacies are common in everyday conversations, advertisements, political debates, and media. Learning to spot them is a crucial critical thinking skill."}, {"type": "text", "content": "When analyzing a piece of text or a speech, look for arguments where the reasoning seems weak, irrelevant, or based on unstated assumptions. Then, try to identify if a specific fallacy is being committed."}, {"type": "example", "content": "Advertisement: 'Millions of people use our product, so it must be the best!'\nFallacy: Appeal to Popularity (a type of Appeal to Emotion or Ad Populum). Just because something is popular doesn't make it good or true."}, {"type": "interactive_element_placeholder", "interactive_element": {"type": "interactive_spot_the_fallacy_in_dialogue", "prompt": "Read this political debate snippet. What fallacy is Candidate A using?", "dialogue": [{"speaker": "Moderator", "line": "Candidate <PERSON>, your opponent has raised concerns about your proposed tax plan."}, {"speaker": "Candidate A", "line": "Well, my opponent failed three businesses and can barely manage their own finances, so I wouldn't trust their opinion on economic matters."}], "fallacy_type": "Ad Hominem", "explanation": "Candidate <PERSON> is attacking their opponent's character and past failures instead of addressing the concerns about the tax plan itself."}}, {"type": "tip", "content": "Identifying fallacies helps you avoid being misled and allows you to construct stronger, more logical arguments yourself."}]}], "module_test": {"id": "rf-if-mt1-fallacy-detector", "title": "Fallacy Detector", "description": "Identify and explain common informal fallacies in arguments.", "estimated_duration_minutes": 30, "questions": [{"id": "rf-if-q1", "question_type": "fallacy_identification_scenario", "scenario_text": "A celebrity endorses a new brand of cereal, claiming it's the healthiest option because they eat it every day. What fallacy is most evident here?", "options": [{"id": "q1opt1", "text": "Ad Hominem", "is_correct": false}, {"id": "q1opt2", "text": "Appeal to Unqualified Authority", "is_correct": true}, {"id": "q1opt3", "text": "Hasty Generalization", "is_correct": false}, {"id": "q1opt4", "text": "<PERSON>", "is_correct": false}], "feedback_correct": "Correct! A celebrity is not necessarily a nutrition expert.", "feedback_incorrect": "Consider if the authority cited is relevant to the claim being made."}, {"id": "rf-if-q2", "question_type": "fallacy_identification_scenario", "scenario_text": "'Either we ban all sugary drinks, or our children will face an obesity epidemic.' This statement primarily commits which fallacy?", "options": [{"id": "q2opt1", "text": "Begging the Question", "is_correct": false}, {"id": "q2opt2", "text": "Equivocation", "is_correct": false}, {"id": "q2opt3", "text": "False Dichotomy", "is_correct": true}, {"id": "q2opt4", "text": "Appeal to Emotion", "is_correct": false}], "feedback_correct": "Correct! It presents only two extreme options, ignoring other possibilities.", "feedback_incorrect": "Think about whether the argument unfairly limits the available options."}, {"id": "rf-if-q3", "question_type": "fallacy_identification_scenario", "scenario_text": "After a new mayor was elected, the city's crime rate went down. A supporter claims, 'See? The new mayor is already making our city safer!' This reasoning is an example of:", "options": [{"id": "q3opt1", "text": "False Cause (Post Hoc)", "is_correct": true}, {"id": "q3opt2", "text": "Ad Hominem", "is_correct": false}, {"id": "q3opt3", "text": "Straw Man", "is_correct": false}, {"id": "q3opt4", "text": "Appeal to Ignorance", "is_correct": false}], "feedback_correct": "Correct! Just because one event followed another doesn't mean the first caused the second. Other factors could be at play.", "feedback_incorrect": "Consider if the argument assumes a causal link based purely on sequence."}]}}