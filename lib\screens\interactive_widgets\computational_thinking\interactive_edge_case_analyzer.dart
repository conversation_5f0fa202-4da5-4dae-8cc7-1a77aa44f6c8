import 'package:flutter/material.dart';
import 'dart:math';

class InteractiveEdgeCaseAnalyzer extends StatefulWidget {
  const InteractiveEdgeCaseAnalyzer({super.key});

  @override
  State<InteractiveEdgeCaseAnalyzer> createState() => _InteractiveEdgeCaseAnalyzerState();
}

class _InteractiveEdgeCaseAnalyzerState extends State<InteractiveEdgeCaseAnalyzer> {
  final TextEditingController _functionController = TextEditingController(text: 'x / (x - 2)');
  final TextEditingController _testValueController = TextEditingController();
  String _analysisResult = '';

  double _evaluateFunction(double x, String functionString) {
    try {
      if (functionString.contains('x / (x - 2)')) {
        if (x == 2.0) return double.nan; // Division by zero
        return x / (x - 2);
      } else if (functionString.contains('sqrt(x)')) {
        if (x < 0) return double.nan; // Square root of negative
        return _sqrt(x);
      } else if (functionString.contains('1 / x')) {
        if (x == 0) return double.nan; // Division by zero
        return 1 / x;
      }
      return double.nan;
    } catch (e) {
      return double.nan;
    }
  }

  // Custom sqrt to avoid direct dart:math import issues with NaN
  double _sqrt(double x) {
    if (x < 0) return double.nan;
    return sqrt(x);
  }

  void _analyzeEdgeCase() {
    setState(() {
      double? testValue = double.tryParse(_testValueController.text.trim());
      if (testValue == null) {
        _analysisResult = 'Please enter a valid number for the test value.';
        return;
      }

      double result = _evaluateFunction(testValue, _functionController.text);

      if (result.isNaN) {
        _analysisResult = 'Result: Undefined (This is an edge case!)';
      } else if (result.isInfinite) {
        _analysisResult = 'Result: Infinity (This is an edge case!)';
      } else {
        _analysisResult = 'Result: ${result.toStringAsFixed(3)} (Normal case)';
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    return Card(
      margin: const EdgeInsets.all(16.0),
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            const Text(
              'Interactive Edge Case Analyzer',
              style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 20),
            TextField(
              controller: _functionController,
              decoration: InputDecoration(
                labelText: 'Enter Function f(x) (e.g., x / (x - 2), sqrt(x), 1 / x)',
                border: OutlineInputBorder(),
              ),
              onChanged: (value) {
                setState(() {
                  _analysisResult = ''; // Clear previous result on function change
                });
              },
            ),
            const SizedBox(height: 10),
            TextField(
              controller: _testValueController,
              keyboardType: TextInputType.number,
              decoration: InputDecoration(
                labelText: 'Enter a test value for x',
                border: OutlineInputBorder(),
              ),
            ),
            const SizedBox(height: 10),
            ElevatedButton(
              onPressed: _analyzeEdgeCase,
              child: const Text('Analyze Edge Case'),
            ),
            const SizedBox(height: 20),
            Text(
              _analysisResult,
              style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold, color: Colors.blue),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 20),
            const Text(
              'Edge cases are extreme or unusual conditions that can cause unexpected behavior in a system or function.',
              style: TextStyle(fontStyle: FontStyle.italic, color: Colors.grey),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }
}
