import 'package:flutter/material.dart';
import 'dart:math';
import 'dart:math' as math;
import 'dart:math' show Random;

/// A comprehensive module test for algebraic expressions and variables.
class InteractiveExpressionExplorerWidget extends StatefulWidget {
  final Map<String, dynamic> data;
  final Function(bool)? onStateChanged;
  final Color primaryColor;
  final Color secondaryColor;
  final Color backgroundColor;
  final Color textColor;

  const InteractiveExpressionExplorerWidget({
    super.key,
    this.data = const {},
    this.onStateChanged,
    this.primaryColor = Colors.blue,
    this.secondaryColor = Colors.orange,
    this.backgroundColor = Colors.white,
    this.textColor = Colors.black87,
  });

  @override
  State<InteractiveExpressionExplorerWidget> createState() =>
      _InteractiveExpressionExplorerWidgetState();
}

class _InteractiveExpressionExplorerWidgetState
    extends State<InteractiveExpressionExplorerWidget> {
  // State variables
  bool _isCompleted = false;
  int _currentQuestionIndex = 0;
  int _score = 0;
  int _totalQuestions = 10;
  bool _showResults = false;
  String? _selectedAnswer;
  String? _feedbackMessage;
  List<Map<String, dynamic>> _questions = [];

  // Timer variables
  int _timeRemaining = 300; // 5 minutes in seconds
  bool _timerActive = false;
  late DateTime _timerStartTime;

  @override
  void initState() {
    super.initState();
    _generateQuestions();
  }

  void _generateQuestions() {
    // Clear existing questions
    _questions = [];

    // Generate a mix of different question types
    Random random = Random();

    // Ensure we have at least one of each type
    _addVariableIdentificationQuestion();
    _addExpressionEvaluationQuestion();
    _addLikeTermsQuestion();
    _addSimplificationQuestion();
    _addEquivalentExpressionQuestion();

    // Add more random questions to reach total
    while (_questions.length < _totalQuestions) {
      int questionType = random.nextInt(5);

      switch (questionType) {
        case 0:
          _addVariableIdentificationQuestion();
          break;
        case 1:
          _addExpressionEvaluationQuestion();
          break;
        case 2:
          _addLikeTermsQuestion();
          break;
        case 3:
          _addSimplificationQuestion();
          break;
        case 4:
          _addEquivalentExpressionQuestion();
          break;
      }
    }

    // Shuffle the questions
    _questions.shuffle();
  }

  void _addVariableIdentificationQuestion() {
    Random random = Random();
    List<String> variables = ['x', 'y', 'z', 'a', 'b', 'n', 'm'];
    List<String> expressions = [
      '3x + 2y = 7',
      'y = 2x + 5',
      'z² - 3z + 2 = 0',
      '2a + b = 5a - 3b',
      '4m - 3n = 12',
      'x² + y² = 25',
      '3(a + 2) = 15',
      'n + n + n = 3n',
      '2m + 5 = 13',
      'z - 3 = 2z + 1'
    ];

    String expression = expressions[random.nextInt(expressions.length)];
    List<String> expressionVariables = [];

    for (String variable in variables) {
      if (expression.contains(variable)) {
        expressionVariables.add(variable);
      }
    }

    if (expressionVariables.isEmpty) {
      expressionVariables = ['x']; // Fallback
    }

    String correctAnswer = expressionVariables.join(', ');

    // Generate wrong answers
    List<String> wrongAnswers = [];
    while (wrongAnswers.length < 3) {
      List<String> wrongVars = [];
      int numVars = random.nextInt(3) + 1;

      for (int i = 0; i < numVars; i++) {
        String var1 = variables[random.nextInt(variables.length)];
        if (!wrongVars.contains(var1) && !expressionVariables.contains(var1)) {
          wrongVars.add(var1);
        }
      }

      if (wrongVars.isNotEmpty) {
        String wrongAnswer = wrongVars.join(', ');
        if (!wrongAnswers.contains(wrongAnswer) && wrongAnswer != correctAnswer) {
          wrongAnswers.add(wrongAnswer);
        }
      }
    }

    List<String> options = [correctAnswer, ...wrongAnswers];
    options.shuffle();

    Map<String, dynamic> question = {
      'type': 'variable_identification',
      'text': 'Identify all variables in the expression: $expression',
      'correctAnswer': correctAnswer,
      'options': options,
      'explanation': 'The variables in $expression are $correctAnswer.'
    };

    _questions.add(question);
  }

  void _addExpressionEvaluationQuestion() {
    Random random = Random();
    List<String> variables = ['x', 'y', 'z'];
    String variable = variables[random.nextInt(variables.length)];
    int value = random.nextInt(10) - 5; // -5 to 4

    List<Map<String, dynamic>> expressions = [
      {'expression': '2$variable + 3', 'formula': (int x) => 2 * x + 3},
      {'expression': '$variable² - 1', 'formula': (int x) => x * x - 1},
      {'expression': '3$variable - 7', 'formula': (int x) => 3 * x - 7},
      {'expression': '$variable + $variable', 'formula': (int x) => x + x},
      {'expression': '10 - $variable', 'formula': (int x) => 10 - x},
      {'expression': '$variable/2 + 4', 'formula': (int x) => x ~/ 2 + 4},
      {'expression': '($variable + 1)²', 'formula': (int x) => (x + 1) * (x + 1)},
      {'expression': '3($variable + 2)', 'formula': (int x) => 3 * (x + 2)},
    ];

    Map<String, dynamic> expressionData = expressions[random.nextInt(expressions.length)];
    String expression = expressionData['expression'];
    Function formula = expressionData['formula'];

    int result = formula(value);
    String correctAnswer = result.toString();

    // Generate wrong answers
    List<String> wrongAnswers = [];
    while (wrongAnswers.length < 3) {
      int offset = random.nextInt(5) + 1;
      if (random.nextBool()) offset = -offset;

      int wrongResult = result + offset;
      String wrongAnswer = wrongResult.toString();

      if (!wrongAnswers.contains(wrongAnswer) && wrongAnswer != correctAnswer) {
        wrongAnswers.add(wrongAnswer);
      }
    }

    List<String> options = [correctAnswer, ...wrongAnswers];
    options.shuffle();

    Map<String, dynamic> question = {
      'type': 'expression_evaluation',
      'text': 'Evaluate the expression $expression when $variable = $value',
      'correctAnswer': correctAnswer,
      'options': options,
      'explanation': 'When $variable = $value, $expression = $correctAnswer.'
    };

    _questions.add(question);
  }

  void _addLikeTermsQuestion() {
    Random random = Random();
    List<String> variables = ['x', 'y', 'z'];
    String variable = variables[random.nextInt(variables.length)];

    List<String> terms = [
      '3$variable',
      '5$variable',
      '2$variable²',
      '$variable²',
      '7',
      '4',
      '-2$variable',
      '-$variable²',
      '$variable',
      '9'
    ];

    // Shuffle and pick some terms
    terms.shuffle();
    List<String> selectedTerms = terms.take(6).toList();

    // Group terms
    Map<String, List<String>> groupedTerms = {};
    for (String term in selectedTerms) {
      String key;
      if (term.contains('$variable²')) {
        key = '$variable²';
      } else if (term.contains(variable)) {
        key = variable;
      } else {
        key = 'constant';
      }

      if (!groupedTerms.containsKey(key)) {
        groupedTerms[key] = [];
      }
      groupedTerms[key]!.add(term);
    }

    // Pick a group to ask about
    List<String> groupKeys = groupedTerms.keys.toList();
    if (groupKeys.isEmpty) {
      groupKeys = [variable]; // Fallback
      groupedTerms[variable] = ['$variable'];
    }

    String targetGroup = groupKeys[random.nextInt(groupKeys.length)];
    List<String> correctTerms = groupedTerms[targetGroup] ?? [];
    String correctAnswer = correctTerms.join(', ');

    if (correctAnswer.isEmpty) {
      correctAnswer = 'None of these terms';
    }

    // Generate wrong answers
    List<String> wrongAnswers = [];

    // Wrong answer 1: Mix of terms from different groups
    if (groupedTerms.length > 1) {
      List<String> mixedTerms = [];
      for (String key in groupedTerms.keys) {
        if (key != targetGroup && groupedTerms[key]!.isNotEmpty) {
          mixedTerms.add(groupedTerms[key]!.first);
        }
      }
      if (mixedTerms.isNotEmpty) {
        wrongAnswers.add(mixedTerms.join(', '));
      }
    }

    // Wrong answer 2: Subset of correct terms
    if (correctTerms.length > 1) {
      wrongAnswers.add(correctTerms.sublist(0, correctTerms.length - 1).join(', '));
    }

    // Wrong answer 3: "None of these terms"
    if (correctAnswer != 'None of these terms') {
      wrongAnswers.add('None of these terms');
    } else {
      // If correct answer is "None", add a random term
      wrongAnswers.add(terms[0]);
    }

    // Ensure we have enough wrong answers
    while (wrongAnswers.length < 3) {
      wrongAnswers.add('Cannot be determined');
    }

    List<String> options = [correctAnswer, ...wrongAnswers];
    options.shuffle();

    String questionText;
    if (targetGroup == 'constant') {
      questionText = 'Which of these are constant terms?';
    } else if (targetGroup.contains('²')) {
      questionText = 'Which of these are $variable² terms?';
    } else {
      questionText = 'Which of these are $variable terms?';
    }

    Map<String, dynamic> question = {
      'type': 'like_terms',
      'text': '$questionText\n${selectedTerms.join(', ')}',
      'correctAnswer': correctAnswer,
      'options': options,
      'explanation': 'The $targetGroup terms are $correctAnswer.'
    };

    _questions.add(question);
  }

  void _addSimplificationQuestion() {
    Random random = Random();
    List<String> variables = ['x', 'y', 'z'];
    String variable = variables[random.nextInt(variables.length)];

    List<Map<String, String>> expressionPairs = [
      {'unsimplified': '3$variable + 2$variable', 'simplified': '5$variable'},
      {'unsimplified': '4$variable - $variable', 'simplified': '3$variable'},
      {'unsimplified': '$variable + $variable + $variable', 'simplified': '3$variable'},
      {'unsimplified': '2$variable² + 3$variable²', 'simplified': '5$variable²'},
      {'unsimplified': '7 + 3 + 2$variable', 'simplified': '10 + 2$variable'},
      {'unsimplified': '5$variable - 2$variable + 3', 'simplified': '3$variable + 3'},
      {'unsimplified': '2($variable + 3)', 'simplified': '2$variable + 6'},
      {'unsimplified': '3$variable + 2$variable - $variable', 'simplified': '4$variable'},
      {'unsimplified': '$variable² + 2$variable² - $variable', 'simplified': '3$variable² - $variable'},
      {'unsimplified': '4 + 3($variable - 1)', 'simplified': '3$variable + 1'},
    ];

    Map<String, String> selectedPair = expressionPairs[random.nextInt(expressionPairs.length)];
    String unsimplified = selectedPair['unsimplified']!;
    String correctAnswer = selectedPair['simplified']!;

    // Generate wrong answers
    List<String> wrongAnswers = [];

    // Wrong answer 1: No simplification
    wrongAnswers.add(unsimplified);

    // Wrong answer 2: Incorrect coefficient
    String wrongCoefficient = correctAnswer.replaceAllMapped(RegExp(r'[0-9]+'), (match) {
      int num = int.parse(match.group(0)!);
      return (num + (random.nextBool() ? 1 : -1)).toString();
    });
    wrongAnswers.add(wrongCoefficient);

    // Wrong answer 3: Completely different expression
    int index = random.nextInt(expressionPairs.length);
    while (expressionPairs[index]['simplified'] == correctAnswer) {
      index = random.nextInt(expressionPairs.length);
    }
    wrongAnswers.add(expressionPairs[index]['simplified']!);

    List<String> options = [correctAnswer, ...wrongAnswers];
    options.shuffle();

    Map<String, dynamic> question = {
      'type': 'simplification',
      'text': 'Simplify the expression: $unsimplified',
      'correctAnswer': correctAnswer,
      'options': options,
      'explanation': 'The simplified form of $unsimplified is $correctAnswer.'
    };

    _questions.add(question);
  }

  void _addEquivalentExpressionQuestion() {
    Random random = Random();
    List<String> variables = ['x', 'y', 'z'];
    String variable = variables[random.nextInt(variables.length)];

    List<Map<String, List<String>>> equivalentSets = [
      {
        'expressions': [
          '2($variable + 3)',
          '2$variable + 6',
          '$variable + $variable + 6'
        ]
      },
      {
        'expressions': [
          '3$variable - 2',
          '3$variable + (-2)',
          '$variable + $variable + $variable - 2'
        ]
      },
      {
        'expressions': [
          '$variable² - 4',
          '($variable - 2)($variable + 2)',
          '$variable · $variable - 2 · 2'
        ]
      },
      {
        'expressions': [
          '($variable + 1)²',
          '$variable² + 2$variable + 1',
          '$variable · $variable + 2 · $variable · 1 + 1 · 1'
        ]
      },
      {
        'expressions': [
          '2$variable + 3$variable',
          '5$variable',
          '$variable + $variable + $variable + $variable + $variable'
        ]
      },
    ];

    Map<String, List<String>> selectedSet = equivalentSets[random.nextInt(equivalentSets.length)];
    List<String> equivalentExpressions = selectedSet['expressions']!;

    // Select the target expression
    String targetExpression = equivalentExpressions[0];

    // Select the correct answer from the equivalent expressions
    String correctAnswer = equivalentExpressions[1];

    // Generate wrong answers
    List<String> wrongAnswers = [];

    // Create some non-equivalent expressions
    List<String> nonEquivalent = [
      '$variable + 1',
      '2$variable - 1',
      '$variable² + 1',
      '3$variable + 4',
      '$variable - $variable',
      '2$variable²',
      '$variable + 5',
      '3($variable - 1)',
      '$variable / 2',
      '4 - $variable'
    ];

    // Ensure the wrong answers are not equivalent to the target
    for (int i = 0; i < nonEquivalent.length && wrongAnswers.length < 3; i++) {
      if (!equivalentExpressions.contains(nonEquivalent[i])) {
        wrongAnswers.add(nonEquivalent[i]);
      }
    }

    List<String> options = [correctAnswer, ...wrongAnswers];
    options.shuffle();

    Map<String, dynamic> question = {
      'type': 'equivalent_expression',
      'text': 'Which expression is equivalent to $targetExpression?',
      'correctAnswer': correctAnswer,
      'options': options,
      'explanation': '$targetExpression is equivalent to $correctAnswer.'
    };

    _questions.add(question);
  }

  void _startChallenge() {
    setState(() {
      _currentQuestionIndex = 0;
      _score = 0;
      _showResults = false;
      _selectedAnswer = null;
      _feedbackMessage = null;
      _isCompleted = false;
      _timerActive = true;
      _timeRemaining = 300; // Reset to 5 minutes
      _timerStartTime = DateTime.now();
    });

    // Start timer
    _startTimer();

    // Notify parent
    widget.onStateChanged?.call(false);
  }

  void _startTimer() {
    Future.delayed(const Duration(seconds: 1), () {
      if (!mounted) return;

      if (_timerActive) {
        setState(() {
          _timeRemaining = math.max(0, _timeRemaining - 1);

          // Check if time is up
          if (_timeRemaining <= 0) {
            _endChallenge();
          }
        });

        // Continue timer
        if (_timeRemaining > 0 && _timerActive) {
          _startTimer();
        }
      }
    });
  }

  void _checkAnswer(String answer) {
    final currentQuestion = _questions[_currentQuestionIndex];
    final bool isCorrect = answer == currentQuestion['correctAnswer'];

    setState(() {
      _selectedAnswer = answer;

      if (isCorrect) {
        _score++;
        _feedbackMessage = 'Correct! ${currentQuestion['explanation']}';
      } else {
        _feedbackMessage = 'Incorrect. ${currentQuestion['explanation']}';
      }
    });

    // Move to next question after a delay
    Future.delayed(const Duration(seconds: 2), () {
      if (!mounted) return;

      setState(() {
        if (_currentQuestionIndex < _questions.length - 1) {
          _currentQuestionIndex++;
          _selectedAnswer = null;
          _feedbackMessage = null;
        } else {
          _endChallenge();
        }
      });
    });
  }

  void _endChallenge() {
    setState(() {
      _timerActive = false;
      _showResults = true;

      // Calculate completion status (pass if score >= 70%)
      _isCompleted = (_score / _questions.length) >= 0.7;
    });

    // Notify parent of completion status
    widget.onStateChanged?.call(_isCompleted);
  }

  String _formatTime(int seconds) {
    int minutes = seconds ~/ 60;
    int remainingSeconds = seconds % 60;
    return '$minutes:${remainingSeconds.toString().padLeft(2, '0')}';
  }

  Widget _buildCurrentQuestion() {
    if (_currentQuestionIndex >= _questions.length) {
      return Container();
    }

    final question = _questions[_currentQuestionIndex];

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Question number and timer
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Text(
              'Question ${_currentQuestionIndex + 1} of ${_questions.length}',
              style: TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.bold,
                color: widget.textColor,
              ),
            ),
            Container(
              padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
              decoration: BoxDecoration(
                color: _timeRemaining < 60 ? Colors.red : widget.primaryColor,
                borderRadius: BorderRadius.circular(16),
              ),
              child: Row(
                children: [
                  Icon(
                    Icons.timer,
                    color: Colors.white,
                    size: 18,
                  ),
                  const SizedBox(width: 4),
                  Text(
                    _formatTime(_timeRemaining),
                    style: TextStyle(
                      color: Colors.white,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),

        const SizedBox(height: 16),

        // Question text
        Text(
          question['text'],
          style: TextStyle(
            fontSize: 18,
            fontWeight: FontWeight.bold,
            color: widget.textColor,
          ),
        ),

        const SizedBox(height: 24),

        // Answer options
        ...question['options'].map<Widget>((option) {
          bool isSelected = _selectedAnswer == option;
          bool isCorrect = option == question['correctAnswer'];

          Color buttonColor = isSelected
              ? (isCorrect ? Colors.green : Colors.red)
              : widget.primaryColor;

          return Padding(
            padding: const EdgeInsets.only(bottom: 12.0),
            child: SizedBox(
              width: double.infinity,
              child: ElevatedButton(
                onPressed: _selectedAnswer == null ? () => _checkAnswer(option) : null,
                style: ElevatedButton.styleFrom(
                  backgroundColor: buttonColor,
                  padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
                  alignment: Alignment.centerLeft,
                ),
                child: Text(
                  option,
                  style: TextStyle(
                    fontSize: 16,
                    color: Colors.white,
                  ),
                ),
              ),
            ),
          );
        }).toList(),

        if (_feedbackMessage != null)
          Padding(
            padding: const EdgeInsets.only(top: 16.0),
            child: Text(
              _feedbackMessage!,
              style: TextStyle(
                color: _selectedAnswer == question['correctAnswer']
                    ? Colors.green
                    : Colors.red,
                fontWeight: FontWeight.bold,
                fontSize: 16,
              ),
            ),
          ),
      ],
    );
  }

  Widget _buildResultsScreen() {
    // Calculate percentage score
    final percentage = (_score / _questions.length * 100).round();
    final isPassing = percentage >= 70;

    return Column(
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        // Trophy icon for passing, or try again icon for failing
        Icon(
          isPassing ? Icons.emoji_events : Icons.refresh,
          size: 80,
          color: isPassing ? Colors.amber : Colors.grey,
        ),

        const SizedBox(height: 24),

        // Result title
        Text(
          isPassing ? 'Congratulations!' : 'Keep Practicing!',
          style: TextStyle(
            fontSize: 24,
            fontWeight: FontWeight.bold,
            color: isPassing ? widget.primaryColor : Colors.red,
          ),
        ),

        const SizedBox(height: 16),

        // Score
        Text(
          'Your Score: $_score out of ${_questions.length} ($percentage%)',
          style: TextStyle(
            fontSize: 18,
            fontWeight: FontWeight.bold,
            color: widget.textColor,
          ),
        ),

        const SizedBox(height: 8),

        // Time taken
        Text(
          'Time Taken: ${_formatTime(300 - _timeRemaining)}',
          style: TextStyle(
            fontSize: 16,
            color: widget.textColor,
          ),
        ),

        const SizedBox(height: 24),

        // Feedback message
        Text(
          isPassing
              ? 'Great job! You\'ve demonstrated a solid understanding of algebraic expressions and variables.'
              : 'You need a score of at least 70% to pass. Review the concepts and try again!',
          textAlign: TextAlign.center,
          style: TextStyle(
            fontSize: 16,
            color: widget.textColor,
          ),
        ),

        const SizedBox(height: 32),

        // Try again button
        ElevatedButton.icon(
          onPressed: _startChallenge,
          icon: Icon(Icons.refresh),
          label: Text('Try Again'),
          style: ElevatedButton.styleFrom(
            backgroundColor: widget.secondaryColor,
            padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
          ),
        ),
      ],
    );
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: widget.backgroundColor,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withAlpha(50),
            spreadRadius: 1,
            blurRadius: 3,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Title
          Text(
            'Expression Explorer Challenge',
            style: TextStyle(
              fontSize: 20,
              fontWeight: FontWeight.bold,
              color: widget.textColor,
            ),
          ),

          const SizedBox(height: 8),

          // Description
          Text(
            'Test your understanding of algebraic expressions and variables with this comprehensive challenge.',
            style: TextStyle(
              fontSize: 14,
              color: widget.textColor.withOpacity(0.8),
            ),
          ),

          const SizedBox(height: 24),

          // Main content area
          if (_showResults)
            _buildResultsScreen()
          else if (_questions.isEmpty || _currentQuestionIndex >= _questions.length)
            Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  const SizedBox(height: 40),
                  Text(
                    'Ready to test your algebra skills?',
                    style: TextStyle(
                      fontSize: 18,
                      fontWeight: FontWeight.bold,
                      color: widget.textColor,
                    ),
                  ),
                  const SizedBox(height: 16),
                  Text(
                    'This challenge includes 10 questions covering variables, expressions, like terms, and more.',
                    textAlign: TextAlign.center,
                    style: TextStyle(
                      fontSize: 14,
                      color: widget.textColor,
                    ),
                  ),
                  const SizedBox(height: 8),
                  Text(
                    'You\'ll have 5 minutes to complete the challenge.',
                    textAlign: TextAlign.center,
                    style: TextStyle(
                      fontSize: 14,
                      color: widget.textColor,
                    ),
                  ),
                  const SizedBox(height: 32),
                  ElevatedButton.icon(
                    onPressed: _startChallenge,
                    icon: Icon(Icons.play_arrow),
                    label: Text('Start Challenge'),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: widget.primaryColor,
                      padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
                    ),
                  ),
                ],
              ),
            )
          else
            _buildCurrentQuestion(),
        ],
      ),
    );
  }
}
