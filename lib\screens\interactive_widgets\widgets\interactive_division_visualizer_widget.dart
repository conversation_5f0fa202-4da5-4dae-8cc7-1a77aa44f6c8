import 'package:flutter/material.dart';
import 'dart:math' as math;
import 'dart:math' show Random;

/// Custom painter for sharing visualization
class SharingVisualizationPainter extends CustomPainter {
  final int dividend;
  final int divisor;
  final int currentStep;
  final double animationValue;
  final Color primaryColor;
  final Color secondaryColor;
  final Color textColor;
  final bool showRemainder;

  SharingVisualizationPainter({
    required this.dividend,
    required this.divisor,
    required this.currentStep,
    required this.animationValue,
    required this.primaryColor,
    required this.secondaryColor,
    required this.textColor,
    required this.showRemainder,
  });

  @override
  void paint(Canvas canvas, Size size) {
    final double objectSize = 20;
    final double objectSpacing = 10;
    final double groupSpacing = 30;

    // Calculate quotient and remainder
    final int quotient = dividend ~/ divisor;
    final int remainder = dividend % divisor;

    // Calculate group dimensions
    final int maxObjectsPerRow = 5;
    final double groupWidth = maxObjectsPerRow * (objectSize + objectSpacing);
    final double groupHeight = ((quotient / maxObjectsPerRow).ceil() + 1) * (objectSize + objectSpacing);

    // Calculate starting position
    final double startX = (size.width - divisor * (groupWidth + groupSpacing)) / 2;
    final double startY = 50;

    // Draw groups
    for (int group = 0; group < divisor; group++) {
      // Determine visibility based on current step and animation
      bool isGroupVisible = false;
      double groupOpacity = 1.0;

      if (currentStep >= 0) {
        // In step 0+, all groups are visible
        isGroupVisible = true;
        if (currentStep == 0) {
          groupOpacity = animationValue;
        }
      }

      if (isGroupVisible) {
        final double groupX = startX + group * (groupWidth + groupSpacing);

        // Draw group container
        final Rect groupRect = Rect.fromLTWH(
          groupX,
          startY,
          groupWidth,
          groupHeight,
        );

        // Draw group background
        canvas.drawRect(
          groupRect,
          Paint()
            ..color = Colors.grey.shade100.withAlpha((200 * groupOpacity).toInt())
            ..style = PaintingStyle.fill,
        );

        // Draw group border
        canvas.drawRect(
          groupRect,
          Paint()
            ..color = secondaryColor.withAlpha((200 * groupOpacity).toInt())
            ..style = PaintingStyle.stroke
            ..strokeWidth = 1,
        );

        // Draw group label
        final textPainter = TextPainter(
          text: TextSpan(
            text: 'Group ${group + 1}',
            style: TextStyle(
              color: textColor.withAlpha((255 * groupOpacity).toInt()),
              fontSize: 12,
              fontWeight: FontWeight.bold,
            ),
          ),
          textDirection: TextDirection.ltr,
        );
        textPainter.layout();
        textPainter.paint(
          canvas,
          Offset(
            groupX + (groupWidth - textPainter.width) / 2,
            startY + 5,
          ),
        );
      }
    }

    // Draw objects being distributed
    if (currentStep >= 1) {
      // Calculate how many objects to show based on animation
      int objectsToShow = currentStep == 1
          ? (dividend * animationValue).floor()
          : dividend;

      // Distribute objects among groups
      for (int i = 0; i < objectsToShow; i++) {
        final int groupIndex = i % divisor;
        final int objectIndexInGroup = i ~/ divisor;

        final int rowInGroup = objectIndexInGroup ~/ maxObjectsPerRow;
        final int colInGroup = objectIndexInGroup % maxObjectsPerRow;

        final double groupX = startX + groupIndex * (groupWidth + groupSpacing);
        final double objectX = groupX + colInGroup * (objectSize + objectSpacing) + 10;
        final double objectY = startY + (rowInGroup + 1) * (objectSize + objectSpacing) + 10;

        // Draw circle object
        canvas.drawCircle(
          Offset(objectX + objectSize / 2, objectY + objectSize / 2),
          objectSize / 2,
          Paint()..color = primaryColor,
        );

        // Draw number inside
        final textPainter = TextPainter(
          text: TextSpan(
            text: (i + 1).toString(),
            style: TextStyle(
              color: Colors.white,
              fontSize: 10,
              fontWeight: FontWeight.bold,
            ),
          ),
          textDirection: TextDirection.ltr,
          textAlign: TextAlign.center,
        );
        textPainter.layout();
        textPainter.paint(
          canvas,
          Offset(
            objectX + (objectSize - textPainter.width) / 2,
            objectY + (objectSize - textPainter.height) / 2,
          ),
        );
      }
    }

    // Draw remainder objects if needed
    if (showRemainder && remainder > 0 && currentStep >= 2) {
      final double remainderStartX = (size.width - remainder * (objectSize + objectSpacing)) / 2;
      final double remainderY = startY + groupHeight + 50;

      // Draw remainder label
      final textPainter = TextPainter(
        text: TextSpan(
          text: 'Remainder:',
          style: TextStyle(
            color: textColor,
            fontSize: 14,
            fontWeight: FontWeight.bold,
          ),
        ),
        textDirection: TextDirection.ltr,
      );
      textPainter.layout();
      textPainter.paint(
        canvas,
        Offset(
          size.width / 2 - textPainter.width / 2,
          remainderY - 30,
        ),
      );

      // Draw remainder objects
      for (int i = 0; i < remainder; i++) {
        final double objectX = remainderStartX + i * (objectSize + objectSpacing);

        // Draw circle object with different color
        canvas.drawCircle(
          Offset(objectX + objectSize / 2, remainderY + objectSize / 2),
          objectSize / 2,
          Paint()..color = secondaryColor,
        );

        // Draw number inside
        final textPainter = TextPainter(
          text: TextSpan(
            text: (dividend - remainder + i + 1).toString(),
            style: TextStyle(
              color: Colors.white,
              fontSize: 10,
              fontWeight: FontWeight.bold,
            ),
          ),
          textDirection: TextDirection.ltr,
          textAlign: TextAlign.center,
        );
        textPainter.layout();
        textPainter.paint(
          canvas,
          Offset(
            objectX + (objectSize - textPainter.width) / 2,
            remainderY + (objectSize - textPainter.height) / 2,
          ),
        );
      }
    }

    // Draw division expression
    if (currentStep >= 0) {
      String expression;
      if (showRemainder && remainder > 0) {
        expression = '$dividend ÷ $divisor = $quotient remainder $remainder';
      } else {
        expression = '$dividend ÷ $divisor = $quotient';
      }

      final TextStyle expressionStyle = TextStyle(
        color: textColor,
        fontSize: 18,
        fontWeight: FontWeight.bold,
      );

      final textPainter = TextPainter(
        text: TextSpan(
          text: expression,
          style: expressionStyle,
        ),
        textDirection: TextDirection.ltr,
        textAlign: TextAlign.center,
      );
      textPainter.layout();
      textPainter.paint(
        canvas,
        Offset(
          size.width / 2 - textPainter.width / 2,
          size.height - 30,
        ),
      );
    }
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) {
    return true;
  }
}

/// Custom painter for grouping visualization
class GroupingVisualizationPainter extends CustomPainter {
  final int dividend;
  final int divisor;
  final int currentStep;
  final double animationValue;
  final Color primaryColor;
  final Color secondaryColor;
  final Color textColor;
  final bool showRemainder;

  GroupingVisualizationPainter({
    required this.dividend,
    required this.divisor,
    required this.currentStep,
    required this.animationValue,
    required this.primaryColor,
    required this.secondaryColor,
    required this.textColor,
    required this.showRemainder,
  });

  @override
  void paint(Canvas canvas, Size size) {
    final double objectSize = 20;
    final double objectSpacing = 10;
    final double groupSpacing = 20;

    // Calculate quotient and remainder
    final int quotient = dividend ~/ divisor;
    final int remainder = dividend % divisor;

    // Calculate total objects to show based on animation and step
    int objectsToShow = 0;
    if (currentStep >= 1) {
      objectsToShow = currentStep == 1
          ? (dividend * animationValue).floor()
          : dividend;
    }

    // Calculate group dimensions
    final int objectsPerGroup = divisor;
    final int maxObjectsPerRow = 5;
    final double groupWidth = math.min(objectsPerGroup, maxObjectsPerRow) * (objectSize + objectSpacing);
    final double groupHeight = ((objectsPerGroup / maxObjectsPerRow).ceil()) * (objectSize + objectSpacing) + 20;

    // Calculate starting position
    final double startX = (size.width - quotient * (groupWidth + groupSpacing)) / 2;
    final double startY = 50;

    // Draw groups
    for (int group = 0; group < quotient; group++) {
      // Determine visibility based on current step and animation
      bool isGroupVisible = false;
      double groupOpacity = 1.0;

      if (currentStep >= 2) {
        // In step 2+, groups become visible
        if (currentStep == 2) {
          isGroupVisible = group < (quotient * animationValue);
          if (group == (quotient * animationValue).floor()) {
            groupOpacity = (quotient * animationValue) - group;
          }
        } else {
          isGroupVisible = true;
        }
      }

      if (isGroupVisible) {
        final double groupX = startX + group * (groupWidth + groupSpacing);

        // Draw group container
        final Rect groupRect = Rect.fromLTWH(
          groupX,
          startY,
          groupWidth,
          groupHeight,
        );

        // Draw group background
        canvas.drawRect(
          groupRect,
          Paint()
            ..color = Colors.grey.shade100.withAlpha((200 * groupOpacity).toInt())
            ..style = PaintingStyle.fill,
        );

        // Draw group border
        canvas.drawRect(
          groupRect,
          Paint()
            ..color = secondaryColor.withAlpha((200 * groupOpacity).toInt())
            ..style = PaintingStyle.stroke
            ..strokeWidth = 1,
        );

        // Draw group label
        final textPainter = TextPainter(
          text: TextSpan(
            text: 'Group ${group + 1}',
            style: TextStyle(
              color: textColor.withAlpha((255 * groupOpacity).toInt()),
              fontSize: 12,
              fontWeight: FontWeight.bold,
            ),
          ),
          textDirection: TextDirection.ltr,
        );
        textPainter.layout();
        textPainter.paint(
          canvas,
          Offset(
            groupX + (groupWidth - textPainter.width) / 2,
            startY + 5,
          ),
        );
      }
    }

    // Draw all objects first (ungrouped)
    if (currentStep >= 1 && currentStep < 3) {
      final int maxObjectsPerRow = 10;
      final double totalWidth = maxObjectsPerRow * (objectSize + objectSpacing);
      final double startX = (size.width - totalWidth) / 2;
      final double startY = 150;

      for (int i = 0; i < objectsToShow; i++) {
        final int row = i ~/ maxObjectsPerRow;
        final int col = i % maxObjectsPerRow;

        final double objectX = startX + col * (objectSize + objectSpacing);
        final double objectY = startY + row * (objectSize + objectSpacing);

        // Draw circle object
        canvas.drawCircle(
          Offset(objectX + objectSize / 2, objectY + objectSize / 2),
          objectSize / 2,
          Paint()..color = primaryColor,
        );

        // Draw number inside
        final textPainter = TextPainter(
          text: TextSpan(
            text: (i + 1).toString(),
            style: TextStyle(
              color: Colors.white,
              fontSize: 10,
              fontWeight: FontWeight.bold,
            ),
          ),
          textDirection: TextDirection.ltr,
          textAlign: TextAlign.center,
        );
        textPainter.layout();
        textPainter.paint(
          canvas,
          Offset(
            objectX + (objectSize - textPainter.width) / 2,
            objectY + (objectSize - textPainter.height) / 2,
          ),
        );
      }
    }
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) {
    return true;
  }
}

/// Custom painter for repeated subtraction visualization
class RepeatedSubtractionVisualizationPainter extends CustomPainter {
  final int dividend;
  final int divisor;
  final int currentStep;
  final double animationValue;
  final Color primaryColor;
  final Color secondaryColor;
  final Color textColor;
  final bool showRemainder;

  RepeatedSubtractionVisualizationPainter({
    required this.dividend,
    required this.divisor,
    required this.currentStep,
    required this.animationValue,
    required this.primaryColor,
    required this.secondaryColor,
    required this.textColor,
    required this.showRemainder,
  });

  @override
  void paint(Canvas canvas, Size size) {
    final double objectSize = 20;
    final double objectSpacing = 10;

    // Calculate quotient and remainder
    final int quotient = dividend ~/ divisor;
    final int remainder = dividend % divisor;

    // Calculate how many subtractions to show
    int subtractionsToShow = 0;
    if (currentStep >= 1) {
      subtractionsToShow = currentStep == 1
          ? (quotient * animationValue).floor()
          : quotient;
    }

    // Calculate starting position
    final int maxObjectsPerRow = 10;
    final double totalWidth = maxObjectsPerRow * (objectSize + objectSpacing);
    final double startX = (size.width - totalWidth) / 2;
    final double startY = 50;

    // Draw number line
    final double lineY = size.height / 2;
    final double maxNumber = dividend + 2;
    final double pixelsPerUnit = (size.width - 40) / maxNumber;

    // Draw the number line
    final Paint linePaint = Paint()
      ..color = Colors.grey.shade400
      ..strokeWidth = 2
      ..style = PaintingStyle.stroke;

    canvas.drawLine(
      Offset(20, lineY),
      Offset(size.width - 20, lineY),
      linePaint,
    );

    // Draw tick marks and labels
    for (int i = 0; i <= maxNumber.toInt(); i++) {
      final double x = 20 + i * pixelsPerUnit;

      // Draw tick
      canvas.drawLine(
        Offset(x, lineY - 10),
        Offset(x, lineY + 10),
        linePaint,
      );

      // Draw label
      final textPainter = TextPainter(
        text: TextSpan(
          text: i.toString(),
          style: TextStyle(
            color: textColor,
            fontSize: 12,
          ),
        ),
        textDirection: TextDirection.ltr,
        textAlign: TextAlign.center,
      );
      textPainter.layout();
      textPainter.paint(
        canvas,
        Offset(x - textPainter.width / 2, lineY + 15),
      );
    }

    // Draw initial position marker
    if (currentStep >= 0) {
      final double x = 20 + dividend * pixelsPerUnit;

      canvas.drawCircle(
        Offset(x, lineY),
        10,
        Paint()..color = primaryColor,
      );

      // Draw label above
      final textPainter = TextPainter(
        text: TextSpan(
          text: dividend.toString(),
          style: TextStyle(
            color: Colors.white,
            fontSize: 12,
            fontWeight: FontWeight.bold,
          ),
        ),
        textDirection: TextDirection.ltr,
        textAlign: TextAlign.center,
      );
      textPainter.layout();
      textPainter.paint(
        canvas,
        Offset(x - textPainter.width / 2, lineY - 5),
      );
    }

    // Draw subtraction jumps
    if (currentStep >= 1) {
      for (int i = 0; i < subtractionsToShow; i++) {
        final double startX = 20 + dividend * pixelsPerUnit - i * divisor * pixelsPerUnit;
        final double endX = startX - divisor * pixelsPerUnit;

        // Draw arc for the jump
        final Path jumpPath = Path();
        jumpPath.moveTo(startX, lineY);
        jumpPath.quadraticBezierTo(
          (startX + endX) / 2,
          lineY + 40,
          endX,
          lineY,
        );

        canvas.drawPath(
          jumpPath,
          Paint()
            ..color = secondaryColor
            ..style = PaintingStyle.stroke
            ..strokeWidth = 2,
        );

        // Draw jump label
        final String jumpLabel = '-$divisor';
        final textPainter = TextPainter(
          text: TextSpan(
            text: jumpLabel,
            style: TextStyle(
              color: secondaryColor,
              fontSize: 14,
              fontWeight: FontWeight.bold,
            ),
          ),
          textDirection: TextDirection.ltr,
          textAlign: TextAlign.center,
        );
        textPainter.layout();
        textPainter.paint(
          canvas,
          Offset(
            (startX + endX) / 2 - textPainter.width / 2,
            lineY + 50,
          ),
        );

        // Draw position marker at end of jump
        canvas.drawCircle(
          Offset(endX, lineY),
          8,
          Paint()..color = secondaryColor.withAlpha(200),
        );

        // Draw count label
        final String countLabel = '${i + 1}';
        final countPainter = TextPainter(
          text: TextSpan(
            text: countLabel,
            style: TextStyle(
              color: textColor,
              fontSize: 12,
              fontWeight: FontWeight.bold,
            ),
          ),
          textDirection: TextDirection.ltr,
          textAlign: TextAlign.center,
        );
        countPainter.layout();
        countPainter.paint(
          canvas,
          Offset(
            endX - countPainter.width / 2,
            lineY - 30,
          ),
        );
      }
    }
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) {
    return true;
  }
}

/// A widget that visualizes division as sharing, grouping, or repeated subtraction
/// for elementary math education.
class InteractiveDivisionVisualizerWidget extends StatefulWidget {
  final Map<String, dynamic> data;
  final Function(bool)? onStateChanged;
  final Color primaryColor;
  final Color secondaryColor;
  final Color backgroundColor;
  final Color textColor;

  const InteractiveDivisionVisualizerWidget({
    super.key,
    this.data = const {},
    this.onStateChanged,
    this.primaryColor = Colors.blue,
    this.secondaryColor = Colors.orange,
    this.backgroundColor = Colors.white,
    this.textColor = Colors.black87,
  });

  @override
  State<InteractiveDivisionVisualizerWidget> createState() =>
      _InteractiveDivisionVisualizerWidgetState();
}

class _InteractiveDivisionVisualizerWidgetState
    extends State<InteractiveDivisionVisualizerWidget>
    with SingleTickerProviderStateMixin {
  // Controllers
  late AnimationController _animationController;
  late Animation<double> _animation;
  final TextEditingController _dividendController = TextEditingController();
  final TextEditingController _divisorController = TextEditingController();

  // State variables
  String _visualizationType = 'Sharing';
  bool _isAnimating = false;
  bool _isCompleted = false;
  int _currentStep = 0;
  List<String> _steps = [];
  String? _errorMessage;
  String? _feedbackMessage;
  bool _showQuestion = false;
  String? _selectedAnswer;
  String? _correctAnswer;
  List<String> _answerOptions = [];
  bool _showRemainder = true;

  // Operation values
  int _dividend = 0;
  int _divisor = 0;
  int _quotient = 0;
  int _remainder = 0;

  // Constants
  final int _maxDividend = 30;
  final int _maxDivisor = 10;
  final List<String> _visualizationTypes = ['Sharing', 'Grouping', 'Repeated Subtraction'];

  @override
  void initState() {
    super.initState();

    // Initialize animation controller
    _animationController = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 1500),
    );

    _animation = CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeInOut,
    );

    // Add listener to animation controller
    _animationController.addStatusListener((status) {
      if (status == AnimationStatus.completed) {
        if (_currentStep < _steps.length - 1) {
          setState(() {
            _currentStep++;
          });
          _animationController.reset();
          _animationController.forward();
        } else {
          setState(() {
            _isAnimating = false;
            _showQuestion = true;
          });
        }
      }
    });

    // Set initial values
    _dividendController.text = '12';
    _divisorController.text = '3';

    // Initialize with default values
    _updateCalculation();
  }

  @override
  void dispose() {
    _animationController.dispose();
    _dividendController.dispose();
    _divisorController.dispose();
    super.dispose();
  }

  void _updateCalculation() {
    try {
      _dividend = int.parse(_dividendController.text);
      _divisor = int.parse(_divisorController.text);

      // Validate input
      if (_dividend <= 0 || _dividend > _maxDividend ||
          _divisor <= 0 || _divisor > _maxDivisor) {
        setState(() {
          _errorMessage = 'Please enter dividend between 1 and $_maxDividend and divisor between 1 and $_maxDivisor';
        });
        return;
      }

      // Calculate quotient and remainder
      _quotient = _dividend ~/ _divisor;
      _remainder = _dividend % _divisor;

      setState(() {
        _errorMessage = null;
        _feedbackMessage = null;
        _showQuestion = false;
        _selectedAnswer = null;
        _currentStep = 0;
      });

      // Generate steps based on visualization type
      _generateSteps();

      // Generate question and answers
      _generateQuestion();

    } catch (e) {
      setState(() {
        _errorMessage = 'Please enter valid numbers';
      });
    }
  }

  void _generateSteps() {
    switch (_visualizationType) {
      case 'Sharing':
        _steps = [
          'Start with $_dividend objects',
          'Share them equally among $_divisor groups',
          'Each group gets $_quotient objects'
        ];
        if (_showRemainder && _remainder > 0) {
          _steps.add('There are $_remainder objects left over (remainder)');
        }
        break;
      case 'Grouping':
        _steps = [
          'Start with $_dividend objects',
          'Make groups of $_divisor objects each',
          'We can make $_quotient complete groups'
        ];
        if (_showRemainder && _remainder > 0) {
          _steps.add('There are $_remainder objects left over (remainder)');
        }
        break;
      case 'Repeated Subtraction':
        _steps = [
          'Start with $_dividend on the number line',
          'Repeatedly subtract $_divisor until we can\'t subtract anymore',
          'We subtracted $_quotient times'
        ];
        if (_showRemainder && _remainder > 0) {
          _steps.add('We have $_remainder left over (remainder)');
        }
        break;
    }
  }

  void _generateQuestion() {
    // Generate answer options (including the correct one)
    _correctAnswer = _quotient.toString();
    if (_showRemainder && _remainder > 0) {
      _correctAnswer = '$_quotient R $_remainder';
    }

    // Generate 3 wrong answers that are close to the correct one
    List<String> options = [];
    if (_correctAnswer != null) {
      options.add(_correctAnswer!);
    }
    Random random = Random();

    while (options.length < 4) {
      int wrongQuotient = _quotient;
      int wrongRemainder = _remainder;

      // Modify either quotient or remainder
      if (random.nextBool() || _remainder == 0) {
        // Modify quotient
        int offset = random.nextInt(3) + 1;
        if (random.nextBool()) offset = -offset;
        wrongQuotient = _quotient + offset;
        if (wrongQuotient <= 0) wrongQuotient = 1;
      } else {
        // Modify remainder
        int offset = random.nextInt(_divisor - 1) + 1;
        if (random.nextBool() && _remainder > 0) offset = -offset;
        wrongRemainder = (_remainder + offset) % _divisor;
      }

      String wrongAnswerStr = wrongQuotient.toString();
      if (_showRemainder && wrongRemainder > 0) {
        wrongAnswerStr = '$wrongQuotient R $wrongRemainder';
      }

      if (!options.contains(wrongAnswerStr)) {
        options.add(wrongAnswerStr);
      }
    }

    options.shuffle();
    _answerOptions = options;
  }

  void _startAnimation() {
    if (_steps.isEmpty) return;

    setState(() {
      _currentStep = 0;
      _isAnimating = true;
      _showQuestion = false;
    });

    _animationController.reset();
    _animationController.forward();
  }

  void _stopAnimation() {
    setState(() {
      _isAnimating = false;
    });
    _animationController.stop();
  }

  void _resetAnimation() {
    setState(() {
      _currentStep = 0;
      _isAnimating = false;
      _showQuestion = false;
      _selectedAnswer = null;
      _feedbackMessage = null;
    });
    _animationController.reset();
  }

  void _checkAnswer(String answer) {
    setState(() {
      _selectedAnswer = answer;
      if (answer == _correctAnswer) {
        _feedbackMessage = 'Correct! Great job!';
        _isCompleted = true;
      } else {
        _feedbackMessage = 'Not quite. Try again!';
      }
    });

    // Notify parent of completion status
    widget.onStateChanged?.call(_isCompleted);
  }

  void _generateRandomProblem() {
    Random random = Random();
    int divisor = random.nextInt(_maxDivisor) + 1;
    int quotient = random.nextInt(5) + 1;
    int remainder = random.nextBool() ? random.nextInt(divisor) : 0;
    int dividend = divisor * quotient + remainder;

    setState(() {
      _dividendController.text = dividend.toString();
      _divisorController.text = divisor.toString();
    });

    _updateCalculation();
  }

  void _toggleRemainder() {
    setState(() {
      _showRemainder = !_showRemainder;
    });
    _updateCalculation();
  }

  Widget _buildInputControls() {
    return Column(
      children: [
        // Visualization type selector
        Row(
          children: [
            Text(
              'Visualization:',
              style: TextStyle(
                fontWeight: FontWeight.bold,
                color: widget.textColor,
              ),
            ),
            const SizedBox(width: 16),
            DropdownButton<String>(
              value: _visualizationType,
              onChanged: (String? newValue) {
                if (newValue != null) {
                  setState(() {
                    _visualizationType = newValue;
                  });
                  _updateCalculation();
                }
              },
              items: _visualizationTypes.map<DropdownMenuItem<String>>((String value) {
                return DropdownMenuItem<String>(
                  value: value,
                  child: Text(value),
                );
              }).toList(),
            ),
            const Spacer(),
            // Remainder toggle
            Row(
              children: [
                Text(
                  'Show Remainder:',
                  style: TextStyle(
                    fontWeight: FontWeight.bold,
                    color: widget.textColor,
                  ),
                ),
                Checkbox(
                  value: _showRemainder,
                  onChanged: (bool? value) {
                    if (value != null) {
                      _toggleRemainder();
                    }
                  },
                  activeColor: widget.primaryColor,
                ),
              ],
            ),
          ],
        ),

        const SizedBox(height: 16),

        // Number inputs
        Row(
          children: [
            Expanded(
              child: TextField(
                controller: _dividendController,
                keyboardType: TextInputType.number,
                decoration: InputDecoration(
                  labelText: 'Dividend',
                  border: OutlineInputBorder(),
                  contentPadding: EdgeInsets.symmetric(horizontal: 12, vertical: 8),
                ),
                onChanged: (_) => _updateCalculation(),
              ),
            ),
            Padding(
              padding: const EdgeInsets.symmetric(horizontal: 16),
              child: Text(
                '÷',
                style: TextStyle(
                  fontSize: 24,
                  fontWeight: FontWeight.bold,
                  color: widget.primaryColor,
                ),
              ),
            ),
            Expanded(
              child: TextField(
                controller: _divisorController,
                keyboardType: TextInputType.number,
                decoration: InputDecoration(
                  labelText: 'Divisor',
                  border: OutlineInputBorder(),
                  contentPadding: EdgeInsets.symmetric(horizontal: 12, vertical: 8),
                ),
                onChanged: (_) => _updateCalculation(),
              ),
            ),
            const SizedBox(width: 16),
            ElevatedButton(
              onPressed: _generateRandomProblem,
              style: ElevatedButton.styleFrom(
                backgroundColor: widget.secondaryColor,
                padding: EdgeInsets.symmetric(horizontal: 12, vertical: 8),
              ),
              child: Text('Random'),
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildVisualizationArea() {
    return Container(
      height: 250,
      width: double.infinity,
      decoration: BoxDecoration(
        color: Colors.grey.shade50,
        borderRadius: BorderRadius.circular(8),
        border: Border.all(
          color: _isCompleted ? Colors.green : Colors.grey.shade300,
          width: _isCompleted ? 2 : 1,
        ),
      ),
      child: AnimatedBuilder(
        animation: _animation,
        builder: (context, child) {
          return CustomPaint(
            painter: _getVisualizationPainter(),
            child: Container(),
          );
        },
      ),
    );
  }

  CustomPainter _getVisualizationPainter() {
    switch (_visualizationType) {
      case 'Sharing':
        return SharingVisualizationPainter(
          dividend: _dividend,
          divisor: _divisor,
          currentStep: _currentStep,
          animationValue: _isAnimating ? _animation.value : 1.0,
          primaryColor: widget.primaryColor,
          secondaryColor: widget.secondaryColor,
          textColor: widget.textColor,
          showRemainder: _showRemainder,
        );
      case 'Grouping':
        return GroupingVisualizationPainter(
          dividend: _dividend,
          divisor: _divisor,
          currentStep: _currentStep,
          animationValue: _isAnimating ? _animation.value : 1.0,
          primaryColor: widget.primaryColor,
          secondaryColor: widget.secondaryColor,
          textColor: widget.textColor,
          showRemainder: _showRemainder,
        );
      case 'Repeated Subtraction':
        return RepeatedSubtractionVisualizationPainter(
          dividend: _dividend,
          divisor: _divisor,
          currentStep: _currentStep,
          animationValue: _isAnimating ? _animation.value : 1.0,
          primaryColor: widget.primaryColor,
          secondaryColor: widget.secondaryColor,
          textColor: widget.textColor,
          showRemainder: _showRemainder,
        );
      default:
        return SharingVisualizationPainter(
          dividend: _dividend,
          divisor: _divisor,
          currentStep: _currentStep,
          animationValue: _isAnimating ? _animation.value : 1.0,
          primaryColor: widget.primaryColor,
          secondaryColor: widget.secondaryColor,
          textColor: widget.textColor,
          showRemainder: _showRemainder,
        );
    }
  }

  Widget _buildAnimationControls() {
    return Row(
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        IconButton(
          icon: Icon(_isAnimating ? Icons.pause : Icons.play_arrow),
          onPressed: _isAnimating ? _stopAnimation : _startAnimation,
          color: widget.primaryColor,
          iconSize: 32,
        ),
        IconButton(
          icon: const Icon(Icons.replay),
          onPressed: _resetAnimation,
          color: widget.primaryColor,
          iconSize: 32,
        ),
      ],
    );
  }

  Widget _buildQuestionSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'What is $_dividend ÷ $_divisor?',
          style: TextStyle(
            fontSize: 18,
            fontWeight: FontWeight.bold,
            color: widget.textColor,
          ),
        ),
        const SizedBox(height: 12),
        Wrap(
          spacing: 8,
          runSpacing: 8,
          children: _answerOptions.map((option) {
            bool isSelected = _selectedAnswer == option;
            bool isCorrect = option == _correctAnswer;

            Color buttonColor = isSelected
                ? (isCorrect ? Colors.green : Colors.red)
                : widget.primaryColor;

            return ElevatedButton(
              onPressed: _selectedAnswer == null ? () => _checkAnswer(option) : null,
              style: ElevatedButton.styleFrom(
                backgroundColor: buttonColor,
                padding: EdgeInsets.symmetric(horizontal: 16, vertical: 8),
              ),
              child: Text(
                option,
                style: TextStyle(
                  fontSize: 16,
                  color: Colors.white,
                ),
              ),
            );
          }).toList(),
        ),
      ],
    );
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: widget.backgroundColor,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withAlpha(50),
            spreadRadius: 1,
            blurRadius: 3,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Title and controls
          Text(
            'Division Visualizer',
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
              color: widget.textColor,
            ),
          ),

          const SizedBox(height: 16),

          // Input controls
          _buildInputControls(),

          if (_errorMessage != null)
            Padding(
              padding: const EdgeInsets.only(top: 8.0),
              child: Text(
                _errorMessage!,
                style: TextStyle(
                  color: Colors.red,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ),

          const SizedBox(height: 16),

          // Visualization area
          _buildVisualizationArea(),

          const SizedBox(height: 16),

          // Step description
          if (_steps.isNotEmpty && _currentStep < _steps.length)
            Text(
              _steps[_currentStep],
              style: TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.bold,
                color: widget.primaryColor,
              ),
            ),

          const SizedBox(height: 16),

          // Animation controls
          _buildAnimationControls(),

          const SizedBox(height: 16),

          // Question and feedback
          if (_showQuestion) _buildQuestionSection(),

          if (_feedbackMessage != null)
            Padding(
              padding: const EdgeInsets.only(top: 8.0),
              child: Text(
                _feedbackMessage!,
                style: TextStyle(
                  color: _selectedAnswer == _correctAnswer
                      ? Colors.green
                      : Colors.red,
                  fontWeight: FontWeight.bold,
                  fontSize: 16,
                ),
              ),
            ),
        ],
      ),
    );
  }
}