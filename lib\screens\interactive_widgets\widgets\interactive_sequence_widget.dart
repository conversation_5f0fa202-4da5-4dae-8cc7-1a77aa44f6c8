import 'package:flutter/material.dart';
import 'dart:async';

class InteractiveSequenceWidget extends StatefulWidget {
  final List<String> sequenceItems;
  final bool showControls;
  final String animationSpeed;
  final bool autoPlay;
  final bool loop;
  final double itemSize;
  final double spacing;

  const InteractiveSequenceWidget({
    Key? key,
    required this.sequenceItems,
    this.showControls = true,
    this.animationSpeed = "medium",
    this.autoPlay = true,
    this.loop = true,
    this.itemSize = 50,
    this.spacing = 8,
  }) : super(key: key);

  factory InteractiveSequenceWidget.fromData(Map<String, dynamic> data) {
    return InteractiveSequenceWidget(
      sequenceItems: List<String>.from(data['sequence_items'] ?? []),
      showControls: data['show_controls'] ?? true,
      animationSpeed: data['animation_speed'] ?? "medium",
      autoPlay: data['auto_play'] ?? true,
      loop: data['loop'] ?? true,
      itemSize: data['item_size']?.toDouble() ?? 50,
      spacing: data['spacing']?.toDouble() ?? 8,
    );
  }

  @override
  State<InteractiveSequenceWidget> createState() =>
      _InteractiveSequenceWidgetState();
}

class _InteractiveSequenceWidgetState extends State<InteractiveSequenceWidget>
    with SingleTickerProviderStateMixin {
  late AnimationController _controller;
  late List<Animation<double>> _animations;
  int _currentIndex = -1;
  Timer? _animationTimer;
  bool _isPlaying = false;

  @override
  void initState() {
    super.initState();

    // Set up animation controller
    _controller = AnimationController(
      vsync: this,
      duration: _getAnimationDuration(),
    );

    // Initialize animations for each item
    _initializeAnimations();

    // Start auto-play if enabled
    if (widget.autoPlay) {
      _startAnimation();
    }
  }

  Duration _getAnimationDuration() {
    switch (widget.animationSpeed) {
      case "slow":
        return const Duration(milliseconds: 1000);
      case "fast":
        return const Duration(milliseconds: 300);
      case "medium":
      default:
        return const Duration(milliseconds: 600);
    }
  }

  void _initializeAnimations() {
    _animations = List.generate(
      widget.sequenceItems.length,
      (index) => Tween<double>(begin: 0.8, end: 1.2).animate(
        CurvedAnimation(
          parent: _controller,
          curve: Interval(0.0, 1.0, curve: Curves.elasticOut),
        ),
      ),
    );
  }

  void _startAnimation() {
    _isPlaying = true;
    _animateNextItem();
  }

  void _animateNextItem() {
    if (!mounted) return;

    setState(() {
      _currentIndex = (_currentIndex + 1) % widget.sequenceItems.length;
    });

    _controller.forward(from: 0.0).then((_) {
      if (_isPlaying) {
        _animationTimer = Timer(
          const Duration(milliseconds: 500),
          () {
            if (mounted && _isPlaying) {
              if (_currentIndex == widget.sequenceItems.length - 1 && !widget.loop) {
                setState(() {
                  _isPlaying = false;
                });
              } else {
                _animateNextItem();
              }
            }
          },
        );
      }
    });
  }

  void _togglePlayPause() {
    setState(() {
      _isPlaying = !_isPlaying;
      if (_isPlaying) {
        _animateNextItem();
      } else {
        _animationTimer?.cancel();
      }
    });
  }

  void _restart() {
    _animationTimer?.cancel();
    setState(() {
      _currentIndex = -1;
      _isPlaying = true;
    });
    _animateNextItem();
  }

  @override
  void dispose() {
    _controller.dispose();
    _animationTimer?.cancel();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      mainAxisSize: MainAxisSize.min,
      children: [
        // Sequence display
        Container(
          height: widget.itemSize * 1.5,
          padding: const EdgeInsets.symmetric(vertical: 8),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: List.generate(
              widget.sequenceItems.length,
              (index) => _buildSequenceItem(index),
            ),
          ),
        ),

        // Controls
        if (widget.showControls)
          Padding(
            padding: const EdgeInsets.only(top: 8.0),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                IconButton(
                  icon: Icon(_isPlaying ? Icons.pause : Icons.play_arrow),
                  onPressed: _togglePlayPause,
                  color: Colors.blue,
                ),
                IconButton(
                  icon: const Icon(Icons.replay),
                  onPressed: _restart,
                  color: Colors.blue,
                ),
              ],
            ),
          ),
      ],
    );
  }

  Widget _buildSequenceItem(int index) {
    final isHighlighted = index == _currentIndex;
    
    return AnimatedBuilder(
      animation: _controller,
      builder: (context, child) {
        return Container(
          margin: EdgeInsets.symmetric(horizontal: widget.spacing),
          child: Transform.scale(
            scale: isHighlighted ? _animations[index].value : 1.0,
            child: Container(
              width: widget.itemSize,
              height: widget.itemSize,
              decoration: BoxDecoration(
                color: isHighlighted ? Colors.blue.withOpacity(0.1) : Colors.transparent,
                borderRadius: BorderRadius.circular(8),
                border: Border.all(
                  color: isHighlighted ? Colors.blue : Colors.transparent,
                  width: 2,
                ),
              ),
              child: Center(
                child: Text(
                  widget.sequenceItems[index],
                  style: TextStyle(
                    fontSize: widget.itemSize * 0.6,
                    fontWeight: isHighlighted ? FontWeight.bold : FontWeight.normal,
                    color: isHighlighted ? Colors.blue : Colors.black87,
                  ),
                ),
              ),
            ),
          ),
        );
      },
    );
  }
}
