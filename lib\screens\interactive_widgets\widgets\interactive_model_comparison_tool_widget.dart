import 'package:flutter/material.dart';
import 'dart:math' as math;

/// A widget that allows users to compare different scientific models
class InteractiveModelComparisonToolWidget extends StatefulWidget {
  /// The data for this widget
  final Map<String, dynamic> data;

  /// Optional callback when the widget state changes
  final Function(bool isCompleted)? onStateChanged;

  const InteractiveModelComparisonToolWidget({
    super.key,
    required this.data,
    this.onStateChanged,
  });

  /// Factory constructor to create the widget from JSON data
  factory InteractiveModelComparisonToolWidget.fromData(Map<String, dynamic> data) {
    return InteractiveModelComparisonToolWidget(
      data: data,
    );
  }

  @override
  State<InteractiveModelComparisonToolWidget> createState() => _InteractiveModelComparisonToolWidgetState();
}

class _InteractiveModelComparisonToolWidgetState extends State<InteractiveModelComparisonToolWidget> {
  // Models and scenarios
  late List<ScientificModel> _models;
  late List<Scenario> _scenarios;
  late int _currentScenarioIndex;

  // Comparison state
  late Map<String, bool> _selectedModels;
  late Map<String, List<int>> _modelScores;
  late Map<String, String> _userJustifications;
  late Map<String, String> _feedbacks;

  // UI state
  late bool _isCompleted;
  late bool _showResults;
  late bool _showExplanation;
  late TextEditingController _justificationController;
  late String _currentModelId;

  // Colors
  late Color _primaryColor;
  late Color _secondaryColor;
  late Color _accentColor;
  late Color _backgroundColor;
  late Color _textColor;

  @override
  void initState() {
    super.initState();
    _initializeWidget();
  }

  @override
  void dispose() {
    _justificationController.dispose();
    super.dispose();
  }

  void _initializeWidget() {
    // Initialize colors
    _primaryColor = _parseColor(widget.data['primaryColor'] ?? '#2196F3');
    _secondaryColor = _parseColor(widget.data['secondaryColor'] ?? '#FF9800');
    _accentColor = _parseColor(widget.data['accentColor'] ?? '#4CAF50');
    _backgroundColor = _parseColor(widget.data['backgroundColor'] ?? '#FFFFFF');
    _textColor = _parseColor(widget.data['textColor'] ?? '#212121');

    // Initialize models
    final List<dynamic> modelsData = widget.data['models'] ?? [];
    _models = modelsData.map((modelData) => ScientificModel.fromJson(modelData)).toList();

    // Initialize scenarios
    final List<dynamic> scenariosData = widget.data['scenarios'] ?? [];
    _scenarios = scenariosData.map((scenarioData) => Scenario.fromJson(scenarioData)).toList();
    _currentScenarioIndex = 0;

    // Initialize comparison state
    _selectedModels = {};
    _modelScores = {};
    _userJustifications = {};
    _feedbacks = {};

    for (var model in _models) {
      _selectedModels[model.id] = false;
      _modelScores[model.id] = List<int>.filled(_scenarios.length, 0);
      _userJustifications[model.id] = '';
      _feedbacks[model.id] = '';
    }

    // Initialize UI state
    _isCompleted = false;
    _showResults = false;
    _showExplanation = false;
    _justificationController = TextEditingController();
    _currentModelId = _models.isNotEmpty ? _models[0].id : '';
  }

  Color _parseColor(String colorString) {
    if (colorString.startsWith('#')) {
      return Color(int.parse('FF${colorString.substring(1)}', radix: 16));
    }
    return Colors.blue;
  }

  void _toggleModelSelection(String modelId) {
    setState(() {
      _selectedModels[modelId] = !(_selectedModels[modelId] ?? false);
    });
  }

  void _updateModelScore(String modelId, int score) {
    if (_currentScenarioIndex < 0 || _currentScenarioIndex >= _scenarios.length) return;

    setState(() {
      _modelScores[modelId]?[_currentScenarioIndex] = score;
    });
  }

  void _saveJustification() {
    setState(() {
      _userJustifications[_currentModelId] = _justificationController.text;
      _justificationController.clear();
    });
  }

  void _selectCurrentModel(String modelId) {
    setState(() {
      _currentModelId = modelId;
      _justificationController.text = _userJustifications[modelId] ?? '';
    });
  }

  void _submitComparison() {
    // Calculate total scores for each model
    Map<String, int> totalScores = {};
    Map<String, double> averageScores = {};

    for (var model in _models) {
      List<int>? scores = _modelScores[model.id];
      if (scores != null) {
        int total = scores.fold(0, (sum, score) => sum + score);
        totalScores[model.id] = total;
        averageScores[model.id] = total / _scenarios.length;
      }
    }

    // Generate feedback for each model
    for (var model in _models) {
      double? avgScore = averageScores[model.id];
      if (avgScore != null) {
        if (avgScore >= 4.0) {
          _feedbacks[model.id] = model.highScoreFeedback;
        } else if (avgScore >= 2.5) {
          _feedbacks[model.id] = model.mediumScoreFeedback;
        } else {
          _feedbacks[model.id] = model.lowScoreFeedback;
        }
      }
    }

    setState(() {
      _showResults = true;
    });
  }

  void _resetComparison() {
    setState(() {
      _showResults = false;

      // Reset scores and justifications
      for (var model in _models) {
        _modelScores[model.id] = List<int>.filled(_scenarios.length, 0);
        _userJustifications[model.id] = '';
        _feedbacks[model.id] = '';
      }
    });
  }

  void _nextScenario() {
    if (_currentScenarioIndex < _scenarios.length - 1) {
      setState(() {
        _currentScenarioIndex++;
        _showResults = false;
      });
    } else if (!_isCompleted) {
      setState(() {
        _isCompleted = true;
      });
      widget.onStateChanged?.call(true);
    }
  }

  void _toggleExplanation() {
    setState(() {
      _showExplanation = !_showExplanation;
    });
  }

  @override
  Widget build(BuildContext context) {
    if (_models.isEmpty || _scenarios.isEmpty) {
      return const Center(child: Text('No models or scenarios available'));
    }

    Scenario scenario = _scenarios[_currentScenarioIndex];

    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
        side: BorderSide(color: _primaryColor.withOpacity(0.5), width: 1),
      ),
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Title
            Text(
              widget.data['title'] ?? 'Model Comparison Tool',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
                color: _textColor,
              ),
            ),

            const SizedBox(height: 8),

            // Scenario navigation
            Row(
              children: [
                Text(
                  'Scenario ${_currentScenarioIndex + 1} of ${_scenarios.length}',
                  style: TextStyle(
                    fontSize: 14,
                    fontWeight: FontWeight.bold,
                    color: _primaryColor,
                  ),
                ),
                const Spacer(),
                IconButton(
                  icon: Icon(_showExplanation ? Icons.info_outline : Icons.info),
                  onPressed: _showResults ? _toggleExplanation : null,
                  tooltip: _showExplanation ? 'Hide Explanation' : 'Show Explanation',
                  color: _secondaryColor,
                ),
              ],
            ),

            const SizedBox(height: 8),

            // Scenario description
            Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: _backgroundColor,
                borderRadius: BorderRadius.circular(8),
                border: Border.all(color: _primaryColor.withOpacity(0.3)),
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'Scenario: ${scenario.name}',
                    style: TextStyle(
                      fontWeight: FontWeight.bold,
                      color: _textColor,
                    ),
                  ),
                  const SizedBox(height: 4),
                  Text(
                    scenario.description,
                    style: TextStyle(color: _textColor.withOpacity(0.8)),
                  ),
                ],
              ),
            ),

            const SizedBox(height: 16),

            // Model selection
            if (!_showResults) ...[
              Text(
                'Select models to compare:',
                style: TextStyle(
                  fontWeight: FontWeight.bold,
                  color: _textColor,
                ),
              ),

              const SizedBox(height: 8),

              // Model checkboxes
              ..._models.map((model) => CheckboxListTile(
                title: Text(model.name),
                subtitle: Text(model.shortDescription),
                value: _selectedModels[model.id] ?? false,
                onChanged: (_) => _toggleModelSelection(model.id),
                activeColor: _primaryColor,
                checkColor: Colors.white,
                controlAffinity: ListTileControlAffinity.leading,
                dense: true,
              )).toList(),

              const SizedBox(height: 16),

              // Model evaluation section
              if (_selectedModels.values.any((selected) => selected)) ...[
                Text(
                  'Rate how well each model explains this scenario:',
                  style: TextStyle(
                    fontWeight: FontWeight.bold,
                    color: _textColor,
                  ),
                ),

                const SizedBox(height: 8),

                // Model tabs
                SizedBox(
                  height: 40,
                  child: ListView(
                    scrollDirection: Axis.horizontal,
                    children: _models
                        .where((model) => _selectedModels[model.id] ?? false)
                        .map((model) => Padding(
                          padding: const EdgeInsets.only(right: 8.0),
                          child: ElevatedButton(
                            onPressed: () => _selectCurrentModel(model.id),
                            style: ElevatedButton.styleFrom(
                              backgroundColor: _currentModelId == model.id
                                  ? _primaryColor
                                  : _primaryColor.withOpacity(0.3),
                              foregroundColor: _currentModelId == model.id
                                  ? Colors.white
                                  : _textColor,
                              shape: RoundedRectangleBorder(
                                borderRadius: BorderRadius.circular(20),
                              ),
                            ),
                            child: Text(model.name),
                          ),
                        ))
                        .toList(),
                  ),
                ),

                const SizedBox(height: 16),

                // Current model evaluation
                if (_currentModelId.isNotEmpty) ...[
                  Builder(
                    builder: (context) {
                      final currentModel = _models.firstWhere(
                        (model) => model.id == _currentModelId,
                        orElse: () => _models.first,
                      );

                      return Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            'Rating for ${currentModel.name}:',
                            style: TextStyle(
                              fontWeight: FontWeight.w500,
                              color: _textColor,
                            ),
                          ),

                          const SizedBox(height: 8),

                          // Rating slider
                          Row(
                            children: [
                              Text('Poor', style: TextStyle(color: _textColor.withOpacity(0.6))),
                              Expanded(
                                child: Slider(
                                  value: (_modelScores[_currentModelId]?[_currentScenarioIndex] ?? 0).toDouble(),
                                  min: 0,
                                  max: 5,
                                  divisions: 5,
                                  label: '${_modelScores[_currentModelId]?[_currentScenarioIndex] ?? 0}',
                                  onChanged: (value) => _updateModelScore(_currentModelId, value.round()),
                                  activeColor: _primaryColor,
                                  inactiveColor: _primaryColor.withOpacity(0.2),
                                ),
                              ),
                              Text('Excellent', style: TextStyle(color: _textColor.withOpacity(0.6))),
                            ],
                          ),

                          const SizedBox(height: 16),

                          // Justification
                          TextField(
                            controller: _justificationController,
                            decoration: InputDecoration(
                              labelText: 'Justification',
                              hintText: 'Explain why you gave this rating...',
                              border: OutlineInputBorder(
                                borderRadius: BorderRadius.circular(8),
                              ),
                              suffixIcon: IconButton(
                                icon: const Icon(Icons.save),
                                onPressed: _saveJustification,
                                tooltip: 'Save justification',
                              ),
                            ),
                            maxLines: 3,
                          ),
                        ],
                      );
                    },
                  ),
                ],

                const SizedBox(height: 16),

                // Submit button
                Center(
                  child: ElevatedButton(
                    onPressed: _submitComparison,
                    style: ElevatedButton.styleFrom(
                      backgroundColor: _primaryColor,
                      foregroundColor: Colors.white,
                      padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(8),
                      ),
                    ),
                    child: const Text('Compare Models'),
                  ),
                ),
              ],
            ],

            // Results section
            if (_showResults) ...[
              Builder(
                builder: (context) {
                  return Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'Comparison Results:',
                        style: TextStyle(
                          fontWeight: FontWeight.bold,
                          color: _textColor,
                        ),
                      ),

                      const SizedBox(height: 8),

                      // Results for each model
                      ..._models
                          .where((model) => _selectedModels[model.id] ?? false)
                          .map((model) => _buildModelResultCard(model)),

                      // Expert explanation (if shown)
                      if (_showExplanation) ...[
                        const SizedBox(height: 16),
                        Container(
                          padding: const EdgeInsets.all(12),
                          decoration: BoxDecoration(
                            color: _secondaryColor.withOpacity(0.1),
                            borderRadius: BorderRadius.circular(8),
                            border: Border.all(color: _secondaryColor.withOpacity(0.3)),
                          ),
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Text(
                                'Expert Analysis:',
                                style: TextStyle(
                                  fontWeight: FontWeight.bold,
                                  color: _secondaryColor,
                                ),
                              ),
                              const SizedBox(height: 4),
                              Text(
                                scenario.expertAnalysis,
                                style: TextStyle(color: _textColor.withOpacity(0.8)),
                              ),
                            ],
                          ),
                        ),
                      ],

                      const SizedBox(height: 16),

                      // Navigation buttons
                      Row(
                        mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                        children: [
                          OutlinedButton(
                            onPressed: _resetComparison,
                            style: OutlinedButton.styleFrom(
                              foregroundColor: _primaryColor,
                              side: BorderSide(color: _primaryColor),
                              padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                            ),
                            child: const Text('Reset'),
                          ),
                          ElevatedButton(
                            onPressed: _nextScenario,
                            style: ElevatedButton.styleFrom(
                              backgroundColor: _primaryColor,
                              foregroundColor: Colors.white,
                              padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                            ),
                            child: Text(_currentScenarioIndex < _scenarios.length - 1
                                ? 'Next Scenario'
                                : 'Finish'),
                          ),
                        ],
                      ),
                    ],
                  );
                },
              ),
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildModelResultCard(ScientificModel model) {
    int score = _modelScores[model.id]?[_currentScenarioIndex] ?? 0;
    String justification = _userJustifications[model.id] ?? '';
    String feedback = _feedbacks[model.id] ?? '';

    return Container(
      margin: const EdgeInsets.only(bottom: 12),
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: _backgroundColor,
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: _getScoreColor(score)),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                model.name,
                style: TextStyle(
                  fontWeight: FontWeight.bold,
                  color: _textColor,
                ),
              ),
              Row(
                children: [
                  Text(
                    'Score: ',
                    style: TextStyle(
                      fontWeight: FontWeight.bold,
                      color: _textColor,
                    ),
                  ),
                  Text(
                    '$score/5',
                    style: TextStyle(
                      fontWeight: FontWeight.bold,
                      color: _getScoreColor(score),
                    ),
                  ),
                ],
              ),
            ],
          ),
          if (justification.isNotEmpty) ...[
            const SizedBox(height: 8),
            Text(
              'Your justification:',
              style: TextStyle(
                fontWeight: FontWeight.w500,
                color: _textColor.withOpacity(0.7),
              ),
            ),
            Text(
              justification,
              style: TextStyle(
                fontStyle: FontStyle.italic,
                color: _textColor.withOpacity(0.8),
              ),
            ),
          ],
          if (feedback.isNotEmpty) ...[
            const SizedBox(height: 8),
            Text(
              'Feedback:',
              style: TextStyle(
                fontWeight: FontWeight.w500,
                color: _textColor.withOpacity(0.7),
              ),
            ),
            Text(
              feedback,
              style: TextStyle(color: _textColor.withOpacity(0.8)),
            ),
          ],
        ],
      ),
    );
  }

  Color _getScoreColor(int score) {
    if (score >= 4) {
      return Colors.green;
    } else if (score >= 2) {
      return Colors.orange;
    } else {
      return Colors.red;
    }
  }
}

/// Represents a scientific model to be compared
class ScientificModel {
  final String id;
  final String name;
  final String shortDescription;
  final String fullDescription;
  final String highScoreFeedback;
  final String mediumScoreFeedback;
  final String lowScoreFeedback;

  ScientificModel({
    required this.id,
    required this.name,
    required this.shortDescription,
    required this.fullDescription,
    required this.highScoreFeedback,
    required this.mediumScoreFeedback,
    required this.lowScoreFeedback,
  });

  factory ScientificModel.fromJson(Map<String, dynamic> json) {
    return ScientificModel(
      id: json['id'] as String,
      name: json['name'] as String,
      shortDescription: json['shortDescription'] as String,
      fullDescription: json['fullDescription'] as String,
      highScoreFeedback: json['highScoreFeedback'] as String,
      mediumScoreFeedback: json['mediumScoreFeedback'] as String,
      lowScoreFeedback: json['lowScoreFeedback'] as String,
    );
  }
}

/// Represents a scenario for comparing models
class Scenario {
  final String id;
  final String name;
  final String description;
  final String expertAnalysis;

  Scenario({
    required this.id,
    required this.name,
    required this.description,
    required this.expertAnalysis,
  });

  factory Scenario.fromJson(Map<String, dynamic> json) {
    return Scenario(
      id: json['id'] as String,
      name: json['name'] as String,
      description: json['description'] as String,
      expertAnalysis: json['expertAnalysis'] as String,
    );
  }
}
