import 'package:flutter/material.dart';
import '../../../../models/course_models.dart';
import '../../../interactive_widgets/interactive_widget_factory.dart';
import 'giphy_visual_widget.dart';
import 'unsplash_visual_widget.dart';
import 'local_asset_visual_widget.dart';
import 'local_asset_sequence_visual_widget.dart';

/// A widget that handles different types of visual elements
class VisualElementHandler extends StatelessWidget {
  final VisualElement visual;
  final bool showNameTag;

  const VisualElementHandler({
    super.key,
    required this.visual,
    this.showNameTag = true,
  });

  @override
  Widget build(BuildContext context) {
    // Handle standard visual types
    if (visual is GiphyVisual) {
      return _buildVisualWithNameTag(
        GiphyVisualWidget(searchTerm: (visual as GiphyVisual).value),
        'GiphyVisual Widget',
      );
    } else if (visual is UnsplashVisual) {
      return _buildVisualWithNameTag(
        UnsplashVisualWidget(searchTerm: (visual as UnsplashVisual).value),
        'UnsplashVisual Widget',
      );
    } else if (visual is LocalAssetVisual) {
      return _buildVisualWithNameTag(
        LocalAssetVisualWidget(assetPath: (visual as LocalAssetVisual).value),
        'LocalAssetVisual Widget',
      );
    } else if (visual is LocalAssetSequenceVisual) {
      return _buildVisualWithNameTag(
        LocalAssetSequenceVisualWidget(
          sequenceVisual: visual as LocalAssetSequenceVisual,
        ),
        'LocalAssetSequenceVisual Widget',
      );
    } else if (visual is StaticTextVisual) {
      return _buildVisualWithNameTag(
        Padding(
          padding: const EdgeInsets.symmetric(vertical: 16.0),
          child: Text(
            (visual as StaticTextVisual).value,
            style: Theme.of(context).textTheme.bodyLarge?.copyWith(
              fontStyle: FontStyle.italic,
              fontSize: 18,
            ),
            textAlign: TextAlign.center,
          ),
        ),
        'StaticTextVisual Widget',
      );
    }
    // Handle interactive visual types
    else if (visual.type.startsWith('interactive_')) {
      try {
        // Create a default data map for the interactive widget
        Map<String, dynamic> widgetData = {'type': visual.type};

        return _buildVisualWithNameTag(
          InteractiveWidgetFactory.createWidget(visual.type, widgetData),
          '${visual.type} Widget',
        );
      } catch (e) {
        return _buildPlaceholderVisual(
          'Interactive Visual: ${visual.type}\nError: $e',
          visual.type,
        );
      }
    }
    // Generic placeholder for other visual types
    else if (visual.type != 'placeholder_visual' ||
        (visual is PlaceholderVisual &&
            (visual as PlaceholderVisual).data['value'] !=
                'Missing visual data')) {
      return _buildPlaceholderVisual('Visual: ${visual.type}', visual.type);
    }

    // Return empty container if no visual
    return const SizedBox.shrink();
  }

  Widget _buildVisualWithNameTag(Widget visualWidget, String tagName) {
    return Column(
      children: [
        visualWidget,
        if (showNameTag)
          Padding(
            padding: const EdgeInsets.only(top: 8.0),
            child: Text(
              '[ $tagName ]',
              style: TextStyle(
                fontSize: 12,
                color: Colors.grey[600],
                fontStyle: FontStyle.italic,
              ),
              textAlign: TextAlign.center,
            ),
          ),
      ],
    );
  }

  Widget _buildPlaceholderVisual(String text, String type) {
    return _buildVisualWithNameTag(
      Padding(
        padding: const EdgeInsets.symmetric(vertical: 16.0),
        child: Container(
          padding: const EdgeInsets.all(12),
          decoration: BoxDecoration(
            color: Colors.grey[200],
            borderRadius: BorderRadius.circular(8),
            border: Border.all(color: Colors.grey[300]!),
          ),
          child: Text(
            text,
            textAlign: TextAlign.center,
            style: TextStyle(
              fontStyle: FontStyle.italic,
              color: Colors.grey[700],
            ),
          ),
        ),
      ),
      '$type Visual',
    );
  }
}
