import 'package:flutter/material.dart';
import '../../models/course_models.dart';
import '../../services/service_provider.dart';

class ContinuousLessonScreenNew extends StatefulWidget {
  final String courseId;
  final String lessonId;

  const ContinuousLessonScreenNew({
    super.key,
    required this.courseId,
    required this.lessonId,
  });

  @override
  State<ContinuousLessonScreenNew> createState() => _ContinuousLessonScreenNewState();
}

class _ContinuousLessonScreenNewState extends State<ContinuousLessonScreenNew> {
  Course? _course;
  LessonDefinition? _lesson;
  List<LessonDefinition> _allCourseLessons = [];
  int _lessonIndex = 0;
  bool _isLoading = true;

  // For continuous scrolling
  late ScrollController _scrollController;
  int _currentContentBlockIndex = 0;
  final Map<int, bool> _revealedBlocks = {};
  final Map<int, GlobalKey> _contentBlockKeys = {};

  @override
  void initState() {
    super.initState();
    _scrollController = ScrollController();

    // Initialize with the first block revealed
    _revealedBlocks[0] = true;
  }

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
    _loadLessonData();
  }

  Future<void> _loadLessonData() async {
    setState(() {
      _isLoading = true;
    });

    final courseService = ServiceProvider.of(context).courseService;
    final course = await courseService.getCourseById(widget.courseId);

    if (course == null || !mounted) {
      if (mounted) Navigator.pop(context);
      return;
    }

    _course = course;
    _allCourseLessons = _course!.modules.expand((module) => module.lessons).toList();

    try {
      _lesson = _allCourseLessons.firstWhere(
        (lesson) => lesson.id == widget.lessonId,
        orElse: () => _allCourseLessons.first,
      );

      _lessonIndex = _allCourseLessons.indexOf(_lesson!);

      // Initialize content block keys
      for (int i = 0; i < _lesson!.contentBlocks.length; i++) {
        _contentBlockKeys[i] = GlobalKey();
      }

      setState(() {
        _isLoading = false;
      });
    } catch (e) {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error loading lesson: $e'),
            backgroundColor: Colors.red,
          ),
        );
        Navigator.pop(context);
      }
    }
  }

  void _revealNextBlock() {
    if (_lesson == null) return;

    if (_currentContentBlockIndex < _lesson!.contentBlocks.length - 1) {
      setState(() {
        _currentContentBlockIndex++;
        _revealedBlocks[_currentContentBlockIndex] = true;
      });

      // Scroll to the newly revealed block
      WidgetsBinding.instance.addPostFrameCallback((_) {
        // Delay slightly to ensure the widget is built
        Future.delayed(const Duration(milliseconds: 100), () {
          final key = _contentBlockKeys[_currentContentBlockIndex];
          if (key?.currentContext != null) {
            Scrollable.ensureVisible(
              key!.currentContext!,
              duration: const Duration(milliseconds: 800),
              curve: Curves.easeInOutCubic,
              alignment: 0.2, // Show a bit of the previous content for context
            );
          }
        });
      });
    } else {
      // Last content block of the current lesson is completed
      // Mark lesson as complete
      final courseService = ServiceProvider.of(context).courseService;
      if (courseService.currentUser != null) {
        final user = courseService.currentUser!;
        final updatedCourseProgress = Map<String, CourseProgress>.from(
          user.courseProgress,
        );
        final courseProgress =
            updatedCourseProgress[widget.courseId] ??
            CourseProgress(courseId: widget.courseId);
        final updatedLessonProgress = Map<String, LessonProgress>.from(
          courseProgress.lessonProgress,
        );

        // Create a completed lesson progress
        updatedLessonProgress[_lesson!.id] = LessonProgress(
          lessonId: _lesson!.id,
          isCompleted: true,
          progressPercentage: 100.0,
        );

        // Update course progress
        updatedCourseProgress[widget.courseId] = CourseProgress(
          courseId: widget.courseId,
          lessonProgress: updatedLessonProgress,
          lastAccessed: DateTime.now(),
          isCompleted: courseProgress.isCompleted,
        );

        // Save the updated progress
        courseService.updateUserCourseProgress(updatedCourseProgress);
      }

      // Show completion message
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('${_lesson!.title} completed!'),
          backgroundColor: Colors.green,
          duration: const Duration(seconds: 2),
        ),
      );

      // Navigate back to the roadmap screen
      Navigator.of(context).pop();
    }
  }

  @override
  void dispose() {
    _scrollController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    if (_lesson == null) {
      return const Scaffold(body: Center(child: CircularProgressIndicator()));
    }

    return Scaffold(
      backgroundColor: Colors.white,
      appBar: AppBar(
        title: Text(
          _lesson!.title,
          style: const TextStyle(
            color: Colors.black,
            fontWeight: FontWeight.w600,
            fontFamily: 'WorkSans',
          ),
        ),
        backgroundColor: Colors.white,
        elevation: 0,
        iconTheme: const IconThemeData(color: Colors.black),
        leading: IconButton(
          icon: const Icon(Icons.close),
          onPressed: () {
            Navigator.of(context).pop();
          },
        ),
        actions: [
          Center(
            child: Text(
              'Lesson ${_lessonIndex + 1}/${_allCourseLessons.length}',
              style: TextStyle(
                color: Colors.grey[700],
                fontSize: 14,
                fontFamily: 'WorkSans',
              ),
            ),
          ),
          const SizedBox(width: 16),
        ],
      ),
      body: Stack(
        children: [
          // Main content with vertical progress bar
          Row(
            children: [
              // Vertical progress bar
              Container(
                width: 4,
                margin: const EdgeInsets.only(left: 4),
                child: LayoutBuilder(
                  builder: (context, constraints) {
                    final double maxHeight = constraints.maxHeight;
                    final double progressHeight = maxHeight *
                      (_currentContentBlockIndex + 1) / _lesson!.contentBlocks.length;

                    return Stack(
                      children: [
                        // Background track
                        Container(
                          width: 4,
                          height: maxHeight,
                          color: Colors.grey[200],
                        ),
                        // Progress indicator (fills from top to bottom)
                        Container(
                          width: 4,
                          height: progressHeight,
                          color: const Color.fromRGBO(124, 66, 210, 1),
                        ),
                      ],
                    );
                  },
                ),
              ),

              // Main content area
              Expanded(
                child: SingleChildScrollView(
                  controller: _scrollController,
                  padding: const EdgeInsets.only(
                    top: 16.0,
                    bottom: 80.0, // Extra padding for the fixed button
                  ),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      // Lesson title and description
                      Padding(
                        padding: const EdgeInsets.fromLTRB(16, 0, 16, 24),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              _lesson!.title,
                              style: const TextStyle(
                                fontSize: 24,
                                fontWeight: FontWeight.bold,
                                color: Colors.black,
                              ),
                            ),
                            const SizedBox(height: 8),
                            Text(
                              _lesson!.description,
                              style: TextStyle(
                                fontSize: 16,
                                color: Colors.grey[700],
                              ),
                            ),
                          ],
                        ),
                      ),

                      // Content blocks
                      ...List.generate(_lesson!.contentBlocks.length, (index) {
                        // Only show blocks that have been revealed
                        if (_revealedBlocks[index] != true) {
                          return const SizedBox.shrink();
                        }

                        final contentBlock = _lesson!.contentBlocks[index];
                        return Container(
                          key: _contentBlockKeys[index],
                          margin: const EdgeInsets.only(bottom: 24.0),
                          child: _buildContentBlock(contentBlock),
                        );
                      }),
                    ],
                  ),
                ),
              ),
            ],
          ),

          // Fixed continue button at the bottom
          Positioned(
            left: 0,
            right: 0,
            bottom: 0,
            child: Container(
              decoration: BoxDecoration(
                color: Colors.white,
                boxShadow: [
                  BoxShadow(
                    color: Colors.black.withAlpha(25),
                    blurRadius: 8,
                    offset: const Offset(0, -2),
                  ),
                ],
              ),
              padding: const EdgeInsets.symmetric(horizontal: 16.0, vertical: 8.0),
              child: SafeArea(
                top: false,
                child: ElevatedButton(
                  onPressed: _revealNextBlock,
                  style: ElevatedButton.styleFrom(
                    backgroundColor: const Color.fromRGBO(124, 66, 210, 1),
                    foregroundColor: Colors.white,
                    elevation: 3,
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(12),
                    ),
                    padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 14),
                  ),
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Text(
                        (_currentContentBlockIndex >= _lesson!.contentBlocks.length - 1)
                            ? 'Finish Lesson'
                            : 'Continue (${_currentContentBlockIndex + 1}/${_lesson!.contentBlocks.length})',
                        style: const TextStyle(
                          fontSize: 17,
                          fontWeight: FontWeight.w600,
                          fontFamily: 'WorkSans',
                        ),
                      ),
                      const SizedBox(width: 10),
                      Container(
                        padding: const EdgeInsets.all(3),
                        decoration: BoxDecoration(
                          color: Colors.white.withAlpha(60),
                          shape: BoxShape.circle,
                        ),
                        child: Icon(
                          (_currentContentBlockIndex >= _lesson!.contentBlocks.length - 1)
                              ? Icons.check_circle_outline
                              : Icons.arrow_forward_ios,
                          size: 20,
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildContentBlock(ContentScreen contentScreen) {
    final visual = contentScreen.content.visual;
    final interactive = contentScreen.content.interactive_element;
    final screenContent = contentScreen.content;
    List<Widget> children = [];

    // Headline
    if (screenContent.headline != null && screenContent.headline!.isNotEmpty) {
      children.add(
        Padding(
          padding: const EdgeInsets.only(bottom: 16.0),
          child: Text(
            screenContent.headline!,
            style: Theme.of(context).textTheme.headlineSmall?.copyWith(
              fontWeight: FontWeight.bold,
              fontSize: 24,
            ),
            textAlign: TextAlign.center,
          ),
        ),
      );
    }

    // Body Markdown
    children.add(
      Padding(
        padding: const EdgeInsets.only(bottom: 16.0),
        child: Text(
          screenContent.body_md,
          style: Theme.of(context).textTheme.bodyLarge?.copyWith(
            fontSize: 18,
            height: 1.5
          ),
          textAlign: TextAlign.left,
        ),
      ),
    );

    // Hook Text
    if (screenContent.hook != null && screenContent.hook!.isNotEmpty) {
      children.add(
        Padding(
          padding: const EdgeInsets.only(top: 8.0, bottom: 16.0),
          child: Text(
            screenContent.hook!,
            style: Theme.of(context).textTheme.titleMedium?.copyWith(
              fontStyle: FontStyle.italic,
              color: Colors.grey[700],
              fontSize: 16,
            ),
            textAlign: TextAlign.center,
          ),
        ),
      );
    }

    // Visual Element Placeholder
    String visualText = 'Visual: ${visual.type}';
    if (visual is GiphyVisual) {
      visualText = 'Visual: Giphy - "${visual.value}"';
    } else if (visual is UnsplashVisual) {
      visualText = 'Visual: Unsplash - "${visual.value}"';
    } else if (visual is LocalAssetVisual) {
      visualText = 'Visual: Local Asset - "${visual.value}"';
    } else if (visual is StaticTextVisual) {
      visualText = visual.value; // Display static text directly
    }

    // Only add visual placeholder if it's not static text already displayed, or if it's a non-empty type
    if (visual is! StaticTextVisual || visual.value.isEmpty) {
      if (visual.type != 'placeholder_visual' ||
          (visual is PlaceholderVisual &&
              visual.data['value'] != 'Missing visual data')) {
        children.add(
          Padding(
            padding: const EdgeInsets.symmetric(vertical: 16.0),
            child: Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: Colors.grey[200],
                borderRadius: BorderRadius.circular(8),
                border: Border.all(color: Colors.grey[300]!),
              ),
              child: Text(
                visualText,
                textAlign: TextAlign.center,
                style: TextStyle(
                  fontStyle: FontStyle.italic,
                  color: Colors.grey[700],
                ),
              ),
            ),
          ),
        );
      }
    }

    // Interactive Element
    if (interactive is ButtonElement) {
      children.add(
        Padding(
          padding: const EdgeInsets.only(top: 24.0),
          child: ElevatedButton(
            style: ElevatedButton.styleFrom(
              backgroundColor: Theme.of(context).primaryColor,
              foregroundColor: Colors.white,
              padding: const EdgeInsets.symmetric(horizontal: 32, vertical: 16),
              textStyle: const TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
              ),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(12),
              ),
            ),
            onPressed: _revealNextBlock,
            child: Text(interactive.text),
          ),
        ),
      );
    } else if (interactive is MultipleChoiceTextElement) {
      List<Widget> mcqChildren = [];
      if (interactive.question_text != null &&
          interactive.question_text!.isNotEmpty) {
        mcqChildren.add(
          Padding(
            padding: const EdgeInsets.only(bottom: 12.0),
            child: Text(
              interactive.question_text!,
              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.w500
              ),
              textAlign: TextAlign.center,
            ),
          ),
        );
      }
      mcqChildren.addAll(
        interactive.options.map((option) {
          return Padding(
            padding: const EdgeInsets.symmetric(vertical: 4.0),
            child: OutlinedButton(
              onPressed: () {
                // Handle option selection, feedback, and then _revealNextBlock
                ScaffoldMessenger.of(context).showSnackBar(
                  SnackBar(
                    content: Text(
                      "Selected: ${option.text} (Correct: ${option.is_correct})",
                    ),
                  ),
                );
                if (option.is_correct) _revealNextBlock();
              },
              style: OutlinedButton.styleFrom(
                padding: const EdgeInsets.symmetric(
                  vertical: 12.0,
                  horizontal: 16.0,
                ),
                textStyle: const TextStyle(fontSize: 16.0),
                side: BorderSide(color: Colors.grey[400]!),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(8.0),
                ),
              ),
              child: Text(option.text, textAlign: TextAlign.center),
            ),
          );
        }).toList(),
      );
      children.add(
        Container(
          padding: const EdgeInsets.all(16),
          margin: const EdgeInsets.symmetric(vertical: 16),
          decoration: BoxDecoration(
            color: Colors.grey[100],
            borderRadius: BorderRadius.circular(12),
          ),
          child: Column(children: mcqChildren),
        ),
      );
    } else {
      // Generic placeholder for other interactive types
      if (interactive.type != 'placeholder_interactive' ||
          (interactive is PlaceholderInteractiveElement &&
              interactive.data['text'] != 'Missing interactive data')) {
        children.add(
          Padding(
            padding: const EdgeInsets.symmetric(vertical: 16.0),
            child: Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: Colors.blue[50],
                borderRadius: BorderRadius.circular(8),
                border: Border.all(color: Colors.blue[200]!),
              ),
              child: Text(
                'Interactive: ${interactive.type}',
                textAlign: TextAlign.center,
                style: TextStyle(
                  fontStyle: FontStyle.italic,
                  color: Colors.blue[700],
                ),
              ),
            ),
          ),
        );
      }
    }

    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 20.0, vertical: 24.0),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.start,
        crossAxisAlignment: CrossAxisAlignment.stretch,
        children: children,
      ),
    );
  }
}
