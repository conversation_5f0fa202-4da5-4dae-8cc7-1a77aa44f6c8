import 'package:flutter/material.dart';
import 'dart:math' as math;

/// A widget that helps users identify logical fallacies in arguments
class InteractiveFallacyIdentificationWidget extends StatefulWidget {
  /// The data for this widget
  final Map<String, dynamic> data;

  /// Optional callback when the widget state changes
  final Function(bool isCompleted)? onStateChanged;

  const InteractiveFallacyIdentificationWidget({
    Key? key,
    required this.data,
    this.onStateChanged,
  }) : super(key: key);

  /// Factory constructor to create the widget from JSON data
  factory InteractiveFallacyIdentificationWidget.fromData(Map<String, dynamic> data) {
    return InteractiveFallacyIdentificationWidget(
      data: data,
    );
  }

  @override
  State<InteractiveFallacyIdentificationWidget> createState() => _InteractiveFallacyIdentificationWidgetState();
}

class _InteractiveFallacyIdentificationWidgetState extends State<InteractiveFallacyIdentificationWidget> {
  // Fallacy scenario data
  late String _title;
  late String _scenario;
  late List<FallacyOption> _options;
  late int _correctOptionIndex;
  late String _explanation;
  
  // User interaction state
  int? _selectedOptionIndex;
  bool _showExplanation = false;
  bool _isCompleted = false;
  
  // UI customization
  late Color _primaryColor;
  late Color _correctColor;
  late Color _incorrectColor;
  late Color _neutralColor;

  @override
  void initState() {
    super.initState();
    
    // Initialize from data
    _title = widget.data['title'] ?? 'Identify the Fallacy';
    _scenario = widget.data['scenario'] ?? 'Examine the argument and identify the logical fallacy.';
    _correctOptionIndex = widget.data['correct_option_index'] ?? 0;
    _explanation = widget.data['explanation'] ?? 'No explanation provided.';
    
    // Parse options
    _options = [];
    final optionsData = widget.data['options'] as List<dynamic>? ?? [];
    for (final option in optionsData) {
      if (option is Map<String, dynamic>) {
        _options.add(FallacyOption(
          name: option['name'] ?? '',
          description: option['description'] ?? '',
        ));
      }
    }
    
    // Initialize colors
    _primaryColor = _parseColor(widget.data['primary_color'], Colors.blue);
    _correctColor = _parseColor(widget.data['correct_color'], Colors.green);
    _incorrectColor = _parseColor(widget.data['incorrect_color'], Colors.red);
    _neutralColor = _parseColor(widget.data['neutral_color'], Colors.grey.shade200);
  }

  // Helper method to parse color from string
  Color _parseColor(dynamic colorValue, Color defaultColor) {
    if (colorValue == null) return defaultColor;
    if (colorValue is String) {
      try {
        return Color(int.parse(colorValue.replaceAll('#', '0xFF')));
      } catch (e) {
        return defaultColor;
      }
    }
    return defaultColor;
  }

  // Handle option selection
  void _selectOption(int index) {
    if (_isCompleted) return;
    
    setState(() {
      _selectedOptionIndex = index;
      _showExplanation = true;
      _isCompleted = true;
    });
    
    // Notify parent about completion
    widget.onStateChanged?.call(true);
  }

  // Reset the widget
  void _reset() {
    setState(() {
      _selectedOptionIndex = null;
      _showExplanation = false;
      _isCompleted = false;
    });
    
    // Notify parent about reset
    widget.onStateChanged?.call(false);
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withOpacity(0.2),
            spreadRadius: 1,
            blurRadius: 3,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Title
          Text(
            _title,
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
              color: _primaryColor,
            ),
          ),
          
          const SizedBox(height: 12),
          
          // Scenario
          Container(
            padding: const EdgeInsets.all(12),
            decoration: BoxDecoration(
              color: Colors.grey.shade50,
              borderRadius: BorderRadius.circular(8),
              border: Border.all(color: Colors.grey.shade300),
            ),
            child: Text(
              _scenario,
              style: const TextStyle(
                fontSize: 16,
                fontStyle: FontStyle.italic,
              ),
            ),
          ),
          
          const SizedBox(height: 16),
          
          // Options
          ...List.generate(_options.length, (index) {
            final option = _options[index];
            final isSelected = _selectedOptionIndex == index;
            final isCorrect = index == _correctOptionIndex;
            
            // Determine the option color based on selection and correctness
            Color optionColor = _neutralColor;
            if (_showExplanation) {
              if (isCorrect) {
                optionColor = _correctColor.withOpacity(0.2);
              } else if (isSelected && !isCorrect) {
                optionColor = _incorrectColor.withOpacity(0.2);
              }
            } else if (isSelected) {
              optionColor = _primaryColor.withOpacity(0.1);
            }
            
            // Determine the border color
            Color borderColor = Colors.grey.shade300;
            if (_showExplanation) {
              if (isCorrect) {
                borderColor = _correctColor;
              } else if (isSelected && !isCorrect) {
                borderColor = _incorrectColor;
              }
            } else if (isSelected) {
              borderColor = _primaryColor;
            }
            
            return GestureDetector(
              onTap: () => _selectOption(index),
              child: Container(
                margin: const EdgeInsets.only(bottom: 8),
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: optionColor,
                  borderRadius: BorderRadius.circular(8),
                  border: Border.all(color: borderColor, width: 2),
                ),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // Option name
                    Text(
                      option.name,
                      style: const TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    if (option.description.isNotEmpty) ...[
                      const SizedBox(height: 4),
                      Text(
                        option.description,
                        style: TextStyle(
                          fontSize: 14,
                          color: Colors.grey.shade700,
                        ),
                      ),
                    ],
                  ],
                ),
              ),
            );
          }),
          
          // Explanation
          if (_showExplanation) ...[
            const SizedBox(height: 16),
            Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: _correctColor.withOpacity(0.1),
                borderRadius: BorderRadius.circular(8),
                border: Border.all(color: _correctColor),
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'Explanation:',
                    style: TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.bold,
                      color: _correctColor,
                    ),
                  ),
                  const SizedBox(height: 4),
                  Text(
                    _explanation,
                    style: const TextStyle(fontSize: 14),
                  ),
                ],
              ),
            ),
            
            const SizedBox(height: 16),
            
            // Reset button
            Center(
              child: ElevatedButton(
                onPressed: _reset,
                style: ElevatedButton.styleFrom(
                  backgroundColor: _primaryColor,
                  foregroundColor: Colors.white,
                  padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(8),
                  ),
                ),
                child: const Text('Try Another Example'),
              ),
            ),
          ],
          
          // Widget name tag
          if (widget.data['showNameTag'] ?? true)
            Padding(
              padding: const EdgeInsets.only(top: 8),
              child: Text(
                'InteractiveFallacyIdentificationWidget',
                style: TextStyle(
                  color: Colors.grey[600],
                  fontSize: 12,
                  fontStyle: FontStyle.italic,
                ),
              ),
            ),
        ],
      ),
    );
  }
}

/// Represents a fallacy option in the widget
class FallacyOption {
  final String name;
  final String description;
  
  FallacyOption({
    required this.name,
    required this.description,
  });
}
