#!/bin/bash

# Script to run the widget showcase for testing interactive widgets
# Usage: ./scripts/run_widget_showcase.sh

echo "Starting Widget Showcase for testing interactive widgets..."

# Navigate to project root (if not already there)
cd "$(dirname "$0")/.." || exit

# Check if Flutter is installed
if ! command -v flutter &> /dev/null; then
    echo "Error: Flutter is not installed or not in PATH"
    exit 1
fi

# Run the app with the widget showcase route
flutter run --route=/widget-showcase

echo "Widget Showcase started. Use Ctrl+C to exit."
