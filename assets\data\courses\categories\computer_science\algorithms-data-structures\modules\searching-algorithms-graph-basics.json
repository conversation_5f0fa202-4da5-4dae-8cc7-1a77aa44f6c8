{"id": "searching-algorithms-graph-basics", "title": "Searching Algorithms and Graph Basics", "description": "Understand linear search, binary search, and introductory graph concepts like nodes and edges.", "order": 3, "lessons": [{"id": "linear-binary-search", "title": "Linear and Binary Search", "description": "Learn two fundamental searching algorithms and their trade-offs.", "order": 1, "type": "interactive_lesson", "estimatedTimeMinutes": 12, "contentBlocks": [{"id": "search_screen1_intro", "type": "lesson_screen", "order": 1, "estimatedTimeSeconds": 60, "content": {"headline": "Finding Needles in Haystacks", "body_md": "Searching is the process of finding a specific item (the 'target') within a collection of items. It's a common task in computing, from finding a file on your computer to looking up a word in a dictionary.\n\nLet's explore two basic search algorithms.", "visual": {"type": "giphy_search", "value": "searching magnifying glass"}, "interactive_element": {"type": "button", "button_text": "Start with Linear Search"}, "audio_narration_url": null}}, {"id": "search_screen2_linear_search", "type": "lesson_screen", "order": 2, "estimatedTimeSeconds": 90, "content": {"headline": "Linear Search: <PERSON>", "body_md": "Linear search (or sequential search) is the simplest search algorithm. It checks each element in the collection one by one, from the beginning, until the target is found or the end of the collection is reached.\n\n**Pros:** Simple to implement, works on unsorted data.\n**Cons:** Can be very slow for large collections (O(n) time complexity).", "visual": {"type": "unsplash_search", "value": "person scanning a line of items"}, "interactive_element": {"type": "multiple_choice_text", "question_text": "Linear search is efficient for:", "options": [{"text": "Large, sorted lists.", "is_correct": false, "feedback": "For large sorted lists, binary search is much better."}, {"text": "Small or unsorted lists.", "is_correct": true, "feedback": "Correct! Its simplicity is an advantage for small lists, and it works on unsorted data."}, {"text": "Finding multiple items at once.", "is_correct": false, "feedback": "Linear search typically finds one item at a time."}]}, "audio_narration_url": null}}, {"id": "search_screen3_binary_search_intro", "type": "lesson_screen", "order": 3, "estimatedTimeSeconds": 75, "content": {"headline": "Binary Search: Divide and Conquer for Sorted Data", "body_md": "Binary search is a much more efficient algorithm, but it **requires the data to be sorted**.\n\nIt works by repeatedly dividing the search interval in half. If the value of the search key is less than the item in the middle of the interval, narrow the interval to the lower half. Otherwise, narrow it to the upper half.\n\nThis continues until the value is found or the interval is empty.", "visual": {"type": "giphy_search", "value": "splitting in half"}, "interactive_element": {"type": "button", "button_text": "Binary Search Performance?"}, "audio_narration_url": null}}, {"id": "search_screen4_binary_search_performance", "type": "lesson_screen", "order": 4, "estimatedTimeSeconds": 90, "content": {"headline": "Binary Search Performance", "body_md": "*   **Time Complexity:** O(log n). This is extremely fast for large datasets! Each step roughly halves the search space.\n*   **Prerequisite:** The list MUST be sorted.\n*   **Space Complexity:** O(1) for iterative versions, O(log n) for recursive versions (due to call stack).\n\nImagine searching a dictionary for a word. You naturally use a binary search-like approach!", "visual": {"type": "unsplash_search", "value": "open dictionary book"}, "interactive_element": {"type": "text_input", "question_text": "If a sorted list has 1024 items, roughly how many comparisons would binary search take in worst case?", "placeholder_text": "e.g., 10 (since 2^10 = 1024)", "correct_answer_regex": "10|ten", "feedback_correct": "Exactly! log₂(1024) = 10. Linear search could take up to 1024 comparisons."}, "audio_narration_url": null}}, {"id": "search_screen5_summary", "type": "lesson_screen", "order": 5, "estimatedTimeSeconds": 60, "content": {"headline": "Searching Recap", "body_md": "*   **Linear Search:** Simple, O(n) time, works on unsorted data.\n*   **Binary Search:** Efficient, O(log n) time, REQUIRES sorted data.\n\nChoosing the right search algorithm depends on the data's size and whether it's sorted.", "visual": {"type": "giphy_search", "value": "choices options"}, "interactive_element": {"type": "button", "button_text": "Intro to Graphs!"}, "audio_narration_url": null}}]}, {"id": "introduction-to-graphs", "title": "Introduction to Graphs", "description": "Learn the basic terminology of graph theory: nodes, edges, and types of graphs.", "order": 2, "type": "interactive_lesson", "estimatedTimeMinutes": 10, "contentBlocks": [{"id": "graph_screen1_what_is_graph", "type": "lesson_screen", "order": 1, "estimatedTimeSeconds": 75, "content": {"headline": "Graphs: Modeling Connections", "body_md": "In computer science, a graph is a data structure used to represent relationships between objects. It consists of:\n\n*   **Vertices (or Nodes):** Represent the objects or entities.\n*   **Edges (or Links/Arcs):** Represent the connections or relationships between vertices.\n\nThink of a social network: people are vertices, and friendships are edges.", "visual": {"type": "giphy_search", "value": "network connecting points"}, "interactive_element": {"type": "button", "button_text": "Key Terminology?"}, "audio_narration_url": null}}, {"id": "graph_screen2_terminology", "type": "lesson_screen", "order": 2, "estimatedTimeSeconds": 90, "content": {"headline": "Basic Graph Terminology", "body_md": "*   **Vertex (Node):** A point or an object in the graph.\n*   **Edge:** A connection between two vertices.\n*   **Directed Graph (Digraph):** Edges have a direction (e.g., a one-way street, a Twitter follow).\n*   **Undirected Graph:** Edges have no direction (e.g., a two-way street, a Facebook friendship).\n*   **Weighted Graph:** Edges have a weight or cost associated with them (e.g., distance between cities on a map).\n\nIf a map shows cities and roads between them, what are the vertices and edges?", "visual": {"type": "unsplash_search", "value": "map with routes"}, "interactive_element": {"type": "text_input", "question_text": "Vertices = ?, Edges = ?", "placeholder_text": "e.g., Vertices: Cities, Edges: Roads", "correct_answer_regex": ".+", "feedback_correct": "Correct! Cities are vertices, and roads are edges connecting them."}, "audio_narration_url": null}}, {"id": "graph_screen3_types_of_graphs", "type": "lesson_screen", "order": 3, "estimatedTimeSeconds": 90, "content": {"headline": "More Graph Types & Concepts", "body_md": "*   **Path:** A sequence of vertices connected by edges.\n*   **Cycle:** A path that starts and ends at the same vertex.\n*   **Connected Graph:** There is a path between every pair of vertices (for undirected graphs).\n*   **Tree:** A connected undirected graph with no cycles (a special type of graph).\n\nIs a family tree (ancestry chart) typically a directed or undirected graph?", "visual": {"type": "giphy_search", "value": "family tree"}, "interactive_element": {"type": "multiple_choice_text", "question_text": "Family tree: directed or undirected?", "options": [{"text": "Directed (e.g., parent -> child)", "is_correct": true, "feedback": "Correct! The parent-child relationship has a clear direction."}, {"text": "Undirected (relationships are mutual)", "is_correct": false, "feedback": "While some family relations are mutual, ancestry implies direction (parent to child)."}]}, "audio_narration_url": null}}, {"id": "graph_screen4_applications", "type": "lesson_screen", "order": 4, "estimatedTimeSeconds": 75, "content": {"headline": "Applications of Graphs", "body_md": "Graphs are incredibly versatile for modeling:\n\n*   Social networks\n*   Road networks and navigation\n*   The World Wide Web (pages are nodes, links are edges)\n*   Computer networks\n*   Dependencies in project management\n*   State transitions in finite automata\n\nMany real-world problems can be modeled and solved using graphs.", "visual": {"type": "unsplash_search", "value": "global network connections"}, "interactive_element": {"type": "button", "button_text": "Lesson Summary"}, "audio_narration_url": null}}, {"id": "graph_screen5_summary", "type": "lesson_screen", "order": 5, "estimatedTimeSeconds": 60, "content": {"headline": "Graph Basics Recap", "body_md": "*   Graphs model relationships using vertices and edges.\n*   Can be directed/undirected, weighted/unweighted.\n*   Key concepts: path, cycle, connectivity.\n*   Vast range of applications.\n\nGraphs are a gateway to many advanced algorithms!", "visual": {"type": "giphy_search", "value": "connected world"}, "interactive_element": {"type": "button", "button_text": "Module Test Time!"}, "audio_narration_url": null}}]}], "moduleTest": {"id": "searching-algorithms-graph-basics-test", "title": "Module Test: Searching & Graph Basics", "description": "Test your understanding of searching algorithms and fundamental graph concepts.", "type": "module_test_interactive", "estimatedTimeMinutes": 8, "contentBlocks": [{"id": "search_graph_test_q1_linear_search", "type": "question_screen", "order": 1, "estimatedTimeSeconds": 60, "content": {"headline": "Question 1: Linear Search", "body_md": "What is the worst-case time complexity of Linear Search on a list of 'n' elements?", "visual": {"type": "giphy_search", "value": "long line"}, "interactive_element": {"type": "multiple_choice_text", "question_text": "Linear Search worst-case time?", "options": [{"text": "O(1)", "is_correct": false, "feedback": "O(1) would mean finding it in the first try, which is best case, not worst."}, {"text": "O(log n)", "is_correct": false, "feedback": "O(log n) is characteristic of binary search."}, {"text": "O(n)", "is_correct": true, "feedback": "Correct! In the worst case, you might have to check every element."}]}, "audio_narration_url": null}}, {"id": "search_graph_test_q2_binary_search_req", "type": "question_screen", "order": 2, "estimatedTimeSeconds": 75, "content": {"headline": "Question 2: Binary Search", "body_md": "What is a crucial prerequisite for Binary Search to work effectively?", "visual": {"type": "unsplash_search", "value": "sorted items"}, "interactive_element": {"type": "multiple_choice_text", "question_text": "Binary Search prerequisite?", "options": [{"text": "The list must be unsorted.", "is_correct": false, "feedback": "Binary search relies on the list being sorted."}, {"text": "The list must contain only numbers.", "is_correct": false, "feedback": "Binary search can work on any data type that can be ordered (e.g., strings alphabetically)."}, {"text": "The list must be sorted.", "is_correct": true, "feedback": "Correct! Binary search needs sorted data to divide the search space."}]}, "audio_narration_url": null}}, {"id": "search_graph_test_q3_graph_def", "type": "question_screen", "order": 3, "estimatedTimeSeconds": 75, "content": {"headline": "Question 3: Graph Definition", "body_md": "In graph theory, what do 'vertices' (or nodes) typically represent?", "visual": {"type": "giphy_search", "value": "points connecting"}, "interactive_element": {"type": "multiple_choice_text", "question_text": "Vertices represent?", "options": [{"text": "The connections or relationships.", "is_correct": false, "feedback": "Connections are represented by edges."}, {"text": "The objects or entities in the system.", "is_correct": true, "feedback": "Correct! Vertices are the items being connected."}, {"text": "The direction of flow in the graph.", "is_correct": false, "feedback": "Directionality is a property of edges in a directed graph."}]}, "audio_narration_url": null}}, {"id": "search_graph_test_q4_directed_graph", "type": "question_screen", "order": 4, "estimatedTimeSeconds": 60, "content": {"headline": "Question 4: Directed G<PERSON>h", "body_md": "If a social network represents 'following' relationships (e.g., User A follows User B, but B doesn't necessarily follow A), what type of graph is most appropriate?", "visual": {"type": "unsplash_search", "value": "one way arrow sign"}, "interactive_element": {"type": "multiple_choice_text", "question_text": "Graph for 'following' relationships?", "options": [{"text": "Undirected Graph", "is_correct": false, "feedback": "Undirected implies mutual relationships."}, {"text": "Weighted Graph", "is_correct": false, "feedback": "Weight isn't the primary characteristic here; direction is."}, {"text": "Directed Graph (Digraph)", "is_correct": true, "feedback": "Correct! The 'follows' relationship has a direction."}]}, "audio_narration_url": null}}]}}