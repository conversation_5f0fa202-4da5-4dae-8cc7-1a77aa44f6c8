import 'package:flutter/material.dart';

/// A widget that presents a logical fallacy quiz with scenarios and multiple choice options
class InteractiveLogicalFallacyQuizWidget extends StatefulWidget {
  /// The data for this widget
  final Map<String, dynamic> data;

  /// Optional callback when the widget state changes
  final Function(bool isCompleted)? onStateChanged;

  const InteractiveLogicalFallacyQuizWidget({
    Key? key,
    required this.data,
    this.onStateChanged,
  }) : super(key: key);

  /// Factory constructor to create the widget from JSON data
  factory InteractiveLogicalFallacyQuizWidget.fromData(Map<String, dynamic> data) {
    return InteractiveLogicalFallacyQuizWidget(
      data: data,
    );
  }

  @override
  State<InteractiveLogicalFallacyQuizWidget> createState() => _InteractiveLogicalFallacyQuizWidgetState();
}

class _InteractiveLogicalFallacyQuizWidgetState extends State<InteractiveLogicalFallacyQuizWidget> {
  // State variables
  bool _isCompleted = false;
  int _currentScenarioIndex = 0;
  int? _selectedOptionIndex;
  bool _hasSubmitted = false;
  String _feedbackText = '';
  bool _showExplanation = false;

  late List<Map<String, dynamic>> _scenarios;
  late int _totalScenarios;
  late int _correctAnswers;

  @override
  void initState() {
    super.initState();
    // Initialize state from data
    _scenarios = List<Map<String, dynamic>>.from(widget.data['scenarios'] ?? []);
    _totalScenarios = _scenarios.length;
    _correctAnswers = 0;
  }

  void _selectOption(int index) {
    if (!_hasSubmitted) {
      setState(() {
        _selectedOptionIndex = index;
      });
    }
  }

  void _submitAnswer() {
    if (_selectedOptionIndex == null || _hasSubmitted) return;

    final currentScenario = _scenarios[_currentScenarioIndex];
    final options = List<Map<String, dynamic>>.from(currentScenario['options'] ?? []);
    final selectedOption = options[_selectedOptionIndex!];
    final isCorrect = selectedOption['is_correct'] ?? false;

    setState(() {
      _hasSubmitted = true;
      if (isCorrect) {
        _correctAnswers++;
        _feedbackText = selectedOption['feedback_correct'] ?? 'Correct!';
      } else {
        _feedbackText = selectedOption['feedback_incorrect'] ?? 'Incorrect. Try again!';
      }
    });

    // Notify parent of completion status
    if (widget.onStateChanged != null) {
      widget.onStateChanged!(_isCompleted);
    }
  }

  void _showFullExplanation() {
    setState(() {
      _showExplanation = true;
    });
  }

  void _nextScenario() {
    if (_currentScenarioIndex < _totalScenarios - 1) {
      setState(() {
        _currentScenarioIndex++;
        _selectedOptionIndex = null;
        _hasSubmitted = false;
        _feedbackText = '';
        _showExplanation = false;
      });
    } else {
      setState(() {
        _isCompleted = true;
      });
      if (widget.onStateChanged != null) {
        widget.onStateChanged!(true);
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    final title = widget.data['title'] as String? ?? 'Logical Fallacy Quiz';
    final showProgress = widget.data['showProgress'] as bool? ?? true;
    
    if (_scenarios.isEmpty) {
      return const Center(child: Text('No scenarios available'));
    }

    final currentScenario = _scenarios[_currentScenarioIndex];
    final scenarioText = currentScenario['scenario'] as String? ?? '';
    final question = currentScenario['question'] as String? ?? 'What fallacy is being used?';
    final options = List<Map<String, dynamic>>.from(currentScenario['options'] ?? []);
    final explanation = currentScenario['explanation'] as String? ?? '';

    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withOpacity(0.2),
            spreadRadius: 1,
            blurRadius: 3,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Title and progress
          Row(
            children: [
              Expanded(
                child: Text(
                  title,
                  style: const TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ),
              if (showProgress)
                Text(
                  'Scenario ${_currentScenarioIndex + 1}/$_totalScenarios',
                  style: TextStyle(
                    fontSize: 14,
                    color: Colors.grey[600],
                  ),
                ),
            ],
          ),
          const SizedBox(height: 16),

          // Scenario
          Container(
            padding: const EdgeInsets.all(12),
            decoration: BoxDecoration(
              color: Colors.blue.withOpacity(0.1),
              borderRadius: BorderRadius.circular(8),
              border: Border.all(color: Colors.blue.withOpacity(0.3)),
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'Scenario:',
                  style: TextStyle(
                    fontWeight: FontWeight.bold,
                    color: Colors.blue[800],
                  ),
                ),
                const SizedBox(height: 8),
                Text(
                  scenarioText,
                  style: const TextStyle(fontSize: 15),
                ),
              ],
            ),
          ),
          const SizedBox(height: 16),

          // Question
          Text(
            question,
            style: const TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 12),

          // Options
          ...List.generate(options.length, (index) {
            final option = options[index];
            final optionText = option['text'] as String? ?? '';
            final isCorrect = option['is_correct'] as bool? ?? false;

            return Padding(
              padding: const EdgeInsets.only(bottom: 8),
              child: InkWell(
                onTap: _hasSubmitted ? null : () => _selectOption(index),
                child: Container(
                  padding: const EdgeInsets.all(12),
                  decoration: BoxDecoration(
                    color: _getOptionColor(index, isCorrect),
                    borderRadius: BorderRadius.circular(8),
                    border: Border.all(
                      color: _getOptionBorderColor(index, isCorrect),
                    ),
                  ),
                  child: Row(
                    children: [
                      Expanded(
                        child: Text(
                          optionText,
                          style: TextStyle(
                            color: _hasSubmitted && (index == _selectedOptionIndex || isCorrect)
                                ? Colors.white
                                : Colors.black,
                          ),
                        ),
                      ),
                      if (_selectedOptionIndex == index)
                        Icon(
                          Icons.check_circle,
                          color: _hasSubmitted
                              ? (isCorrect ? Colors.white : Colors.white)
                              : Colors.blue,
                        ),
                    ],
                  ),
                ),
              ),
            );
          }),
          const SizedBox(height: 16),

          // Feedback
          if (_hasSubmitted)
            Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: _correctAnswers > _currentScenarioIndex
                    ? Colors.green.withOpacity(0.1)
                    : Colors.red.withOpacity(0.1),
                borderRadius: BorderRadius.circular(8),
                border: Border.all(
                  color: _correctAnswers > _currentScenarioIndex
                      ? Colors.green.withOpacity(0.3)
                      : Colors.red.withOpacity(0.3),
                ),
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    _feedbackText,
                    style: TextStyle(
                      color: _correctAnswers > _currentScenarioIndex
                          ? Colors.green[800]
                          : Colors.red[800],
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  if (_showExplanation && explanation.isNotEmpty) ...[
                    const SizedBox(height: 8),
                    Text(
                      explanation,
                      style: const TextStyle(fontSize: 14),
                    ),
                  ],
                ],
              ),
            ),
          const SizedBox(height: 16),

          // Buttons
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              if (_hasSubmitted && !_showExplanation && explanation.isNotEmpty)
                ElevatedButton(
                  onPressed: _showFullExplanation,
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Colors.blue[700],
                  ),
                  child: const Text('Show Explanation'),
                ),
              const Spacer(),
              if (!_hasSubmitted)
                ElevatedButton(
                  onPressed: _selectedOptionIndex != null ? _submitAnswer : null,
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Colors.blue[700],
                  ),
                  child: const Text('Submit'),
                )
              else
                ElevatedButton(
                  onPressed: _nextScenario,
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Colors.green[700],
                  ),
                  child: Text(
                    _currentScenarioIndex < _totalScenarios - 1
                        ? 'Next Scenario'
                        : 'Finish Quiz',
                  ),
                ),
            ],
          ),

          // Results if completed
          if (_isCompleted)
            Container(
              margin: const EdgeInsets.only(top: 16),
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: Colors.green.withOpacity(0.1),
                borderRadius: BorderRadius.circular(8),
                border: Border.all(color: Colors.green),
              ),
              child: Column(
                children: [
                  const Text(
                    'Quiz Completed!',
                    style: TextStyle(
                      fontSize: 18,
                      fontWeight: FontWeight.bold,
                      color: Colors.green,
                    ),
                  ),
                  const SizedBox(height: 8),
                  Text(
                    'You got $_correctAnswers out of $_totalScenarios correct!',
                    style: const TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ],
              ),
            ),

          // Widget name tag
          if (widget.data['showNameTag'] ?? true)
            Padding(
              padding: const EdgeInsets.only(top: 8),
              child: Text(
                'InteractiveLogicalFallacyQuizWidget',
                style: TextStyle(
                  color: Colors.grey[600],
                  fontSize: 12,
                  fontStyle: FontStyle.italic,
                ),
              ),
            ),
        ],
      ),
    );
  }

  Color _getOptionColor(int index, bool isCorrect) {
    if (!_hasSubmitted) {
      return _selectedOptionIndex == index
          ? Colors.blue.withOpacity(0.2)
          : Colors.grey.withOpacity(0.1);
    } else {
      if (index == _selectedOptionIndex) {
        return isCorrect ? Colors.green : Colors.red;
      } else if (isCorrect) {
        return Colors.green;
      } else {
        return Colors.grey.withOpacity(0.1);
      }
    }
  }

  Color _getOptionBorderColor(int index, bool isCorrect) {
    if (!_hasSubmitted) {
      return _selectedOptionIndex == index
          ? Colors.blue
          : Colors.grey.withOpacity(0.3);
    } else {
      if (index == _selectedOptionIndex) {
        return isCorrect ? Colors.green : Colors.red;
      } else if (isCorrect) {
        return Colors.green;
      } else {
        return Colors.grey.withOpacity(0.3);
      }
    }
  }
}
