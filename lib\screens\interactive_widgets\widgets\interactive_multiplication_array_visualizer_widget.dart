import 'package:flutter/material.dart';
import 'dart:math' as math;
import 'dart:math' show Random;

/// Custom painter for array/grid visualization
class ArrayVisualizationPainter extends CustomPainter {
  final int firstNumber;
  final int secondNumber;
  final int currentStep;
  final double animationValue;
  final Color primaryColor;
  final Color secondaryColor;
  final Color textColor;
  final bool showCommutative;

  ArrayVisualizationPainter({
    required this.firstNumber,
    required this.secondNumber,
    required this.currentStep,
    required this.animationValue,
    required this.primaryColor,
    required this.secondaryColor,
    required this.textColor,
    required this.showCommutative,
  });

  @override
  void paint(Canvas canvas, Size size) {
    final double cellSize = _calculateCellSize(size);
    final double spacing = 2;
    final double startX = size.width / 2 - (firstNumber * (cellSize + spacing)) / 2;
    final double startY = size.height / 2 - (secondNumber * (cellSize + spacing)) / 2;

    // Draw grid cells
    for (int row = 0; row < secondNumber; row++) {
      for (int col = 0; col < firstNumber; col++) {
        // Calculate cell visibility based on animation and current step
        bool isVisible = false;
        double opacity = 1.0;

        if (currentStep == 0) {
          // In step 0, gradually show the grid outline
          isVisible = true;
          opacity = animationValue;
        } else if (currentStep == 1) {
          // In step 1, fill in cells row by row
          int cellIndex = row * firstNumber + col;
          int totalCells = firstNumber * secondNumber;
          double cellProgress = totalCells * animationValue;
          isVisible = cellIndex <= cellProgress;
          opacity = isVisible ? 1.0 : 0.0;
          if (cellIndex == cellProgress.floor() && cellProgress < totalCells) {
            opacity = cellProgress - cellProgress.floor();
          }
        } else {
          // In step 2+, all cells are visible
          isVisible = true;
        }

        if (isVisible) {
          final double x = startX + col * (cellSize + spacing);
          final double y = startY + row * (cellSize + spacing);

          final Rect rect = Rect.fromLTWH(x, y, cellSize, cellSize);
          canvas.drawRect(
            rect,
            Paint()
              ..color = primaryColor.withAlpha((255 * opacity).toInt())
              ..style = PaintingStyle.fill,
          );

          // Draw border
          canvas.drawRect(
            rect,
            Paint()
              ..color = secondaryColor.withAlpha((200 * opacity).toInt())
              ..style = PaintingStyle.stroke
              ..strokeWidth = 1,
          );
        }
      }
    }

    // Draw row and column labels
    if (currentStep >= 0) {
      final TextStyle labelStyle = TextStyle(
        color: textColor,
        fontSize: 14,
        fontWeight: FontWeight.bold,
      );

      // Draw column labels (first number)
      for (int col = 0; col < firstNumber; col++) {
        final double x = startX + col * (cellSize + spacing) + cellSize / 2;
        final double y = startY - 20;

        _drawText(canvas, (col + 1).toString(), x, y, labelStyle);
      }

      // Draw row labels (second number)
      for (int row = 0; row < secondNumber; row++) {
        final double x = startX - 20;
        final double y = startY + row * (cellSize + spacing) + cellSize / 2;

        _drawText(canvas, (row + 1).toString(), x, y, labelStyle);
      }
    }

    // Draw multiplication expression
    if (currentStep >= 0) {
      final String expression = '$firstNumber × $secondNumber = ${firstNumber * secondNumber}';
      final TextStyle expressionStyle = TextStyle(
        color: textColor,
        fontSize: 18,
        fontWeight: FontWeight.bold,
      );

      _drawText(
        canvas,
        expression,
        size.width / 2,
        size.height - 30,
        expressionStyle,
      );
    }

    // Draw commutative property if enabled
    if (showCommutative && currentStep >= 2) {
      final double progress = currentStep == 2 ? animationValue : 1.0;
      final double rotationAngle = progress * math.pi / 2;

      // Draw arrow indicating rotation/transposition
      final Paint arrowPaint = Paint()
        ..color = secondaryColor
        ..strokeWidth = 3
        ..style = PaintingStyle.stroke;

      final double arrowCenterX = size.width / 2;
      final double arrowCenterY = size.height / 2;
      final double arrowRadius = math.min(size.width, size.height) / 4;

      // Draw curved arrow
      final Path arrowPath = Path();
      arrowPath.moveTo(
        arrowCenterX - arrowRadius * 0.7,
        arrowCenterY - arrowRadius * 0.7,
      );
      arrowPath.arcTo(
        Rect.fromCenter(
          center: Offset(arrowCenterX, arrowCenterY),
          width: arrowRadius * 2,
          height: arrowRadius * 2,
        ),
        -math.pi * 3 / 4,
        -math.pi * progress,
        false,
      );

      canvas.drawPath(arrowPath, arrowPaint);

      // Draw arrowhead
      final double arrowheadX = arrowCenterX + arrowRadius * math.cos(-math.pi * 3 / 4 - math.pi * progress);
      final double arrowheadY = arrowCenterY + arrowRadius * math.sin(-math.pi * 3 / 4 - math.pi * progress);

      final Path arrowheadPath = Path();
      arrowheadPath.moveTo(arrowheadX, arrowheadY);
      arrowheadPath.lineTo(
        arrowheadX + 10 * math.cos(-math.pi * 3 / 4 - math.pi * progress - math.pi / 6),
        arrowheadY + 10 * math.sin(-math.pi * 3 / 4 - math.pi * progress - math.pi / 6),
      );
      arrowheadPath.lineTo(
        arrowheadX + 10 * math.cos(-math.pi * 3 / 4 - math.pi * progress + math.pi / 6),
        arrowheadY + 10 * math.sin(-math.pi * 3 / 4 - math.pi * progress + math.pi / 6),
      );
      arrowheadPath.close();

      canvas.drawPath(
        arrowheadPath,
        Paint()..color = secondaryColor,
      );

      // Draw commutative property text
      final String commutativeText = '$firstNumber × $secondNumber = $secondNumber × $firstNumber';
      final TextStyle commutativeStyle = TextStyle(
        color: secondaryColor,
        fontSize: 16,
        fontWeight: FontWeight.bold,
      );

      _drawText(
        canvas,
        commutativeText,
        size.width / 2,
        30,
        commutativeStyle,
      );
    }
  }

  void _drawText(Canvas canvas, String text, double x, double y, TextStyle style) {
    final textPainter = TextPainter(
      text: TextSpan(text: text, style: style),
      textDirection: TextDirection.ltr,
      textAlign: TextAlign.center,
    );
    textPainter.layout();
    textPainter.paint(
      canvas,
      Offset(x - textPainter.width / 2, y - textPainter.height / 2),
    );
  }

  double _calculateCellSize(Size size) {
    // Calculate the maximum cell size that will fit in the available space
    final double maxWidth = size.width * 0.8;
    final double maxHeight = size.height * 0.7;

    final double cellWidth = maxWidth / firstNumber;
    final double cellHeight = maxHeight / secondNumber;

    return math.min(cellWidth, cellHeight);
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) {
    return true;
  }
}

/// Custom painter for groups visualization
class GroupsVisualizationPainter extends CustomPainter {
  final int firstNumber;
  final int secondNumber;
  final int currentStep;
  final double animationValue;
  final Color primaryColor;
  final Color secondaryColor;
  final Color textColor;

  GroupsVisualizationPainter({
    required this.firstNumber,
    required this.secondNumber,
    required this.currentStep,
    required this.animationValue,
    required this.primaryColor,
    required this.secondaryColor,
    required this.textColor,
  });

  @override
  void paint(Canvas canvas, Size size) {
    final double groupSpacing = 20;
    final double objectSize = 20;
    final double objectSpacing = 10;

    // Calculate group dimensions
    final double groupWidth = firstNumber * (objectSize + objectSpacing) - objectSpacing + 20;
    final double groupHeight = objectSize + 20;

    // Calculate starting position
    final double startX = (size.width - groupWidth) / 2;
    final double startY = 50;

    // Draw groups
    for (int group = 0; group < secondNumber; group++) {
      // Determine visibility based on current step and animation
      bool isGroupVisible = false;
      double groupOpacity = 1.0;

      if (currentStep == 0) {
        // In step 0, gradually show the group outlines
        isGroupVisible = true;
        groupOpacity = animationValue;
      } else {
        // In step 1+, all groups are visible
        isGroupVisible = true;
      }

      if (isGroupVisible) {
        final double groupY = startY + group * (groupHeight + groupSpacing);

        // Draw group container
        final Rect groupRect = Rect.fromLTWH(
          startX,
          groupY,
          groupWidth,
          groupHeight,
        );

        // Draw group background
        canvas.drawRect(
          groupRect,
          Paint()
            ..color = Colors.grey.shade100.withAlpha((200 * groupOpacity).toInt())
            ..style = PaintingStyle.fill,
        );

        // Draw group border
        canvas.drawRect(
          groupRect,
          Paint()
            ..color = secondaryColor.withAlpha((200 * groupOpacity).toInt())
            ..style = PaintingStyle.stroke
            ..strokeWidth = 1,
        );

        // Draw group label
        final textPainter = TextPainter(
          text: TextSpan(
            text: 'Group ${group + 1}',
            style: TextStyle(
              color: textColor.withAlpha((255 * groupOpacity).toInt()),
              fontSize: 12,
              fontWeight: FontWeight.bold,
            ),
          ),
          textDirection: TextDirection.ltr,
        );
        textPainter.layout();
        textPainter.paint(
          canvas,
          Offset(
            startX + 10,
            groupY + (groupHeight - textPainter.height) / 2,
          ),
        );
      }

      // Draw objects in each group
      if (currentStep >= 1) {
        final int visibleObjects = currentStep == 1
            ? (firstNumber * animationValue).floor()
            : firstNumber;

        for (int obj = 0; obj < visibleObjects; obj++) {
          final double objectX = startX + 80 + obj * (objectSize + objectSpacing);
          final double objectY = startY + group * (groupHeight + groupSpacing) + (groupHeight - objectSize) / 2;

          // Draw circle object
          canvas.drawCircle(
            Offset(objectX + objectSize / 2, objectY + objectSize / 2),
            objectSize / 2,
            Paint()..color = primaryColor,
          );

          // Draw number inside
          final textPainter = TextPainter(
            text: TextSpan(
              text: (obj + 1).toString(),
              style: TextStyle(
                color: Colors.white,
                fontSize: 10,
                fontWeight: FontWeight.bold,
              ),
            ),
            textDirection: TextDirection.ltr,
            textAlign: TextAlign.center,
          );
          textPainter.layout();
          textPainter.paint(
            canvas,
            Offset(
              objectX + (objectSize - textPainter.width) / 2,
              objectY + (objectSize - textPainter.height) / 2,
            ),
          );
        }
      }
    }

    // Draw multiplication expression
    if (currentStep >= 0) {
      final String expression = '$secondNumber groups × $firstNumber objects = ${firstNumber * secondNumber} objects';
      final TextStyle expressionStyle = TextStyle(
        color: textColor,
        fontSize: 18,
        fontWeight: FontWeight.bold,
      );

      final textPainter = TextPainter(
        text: TextSpan(
          text: expression,
          style: expressionStyle,
        ),
        textDirection: TextDirection.ltr,
        textAlign: TextAlign.center,
      );
      textPainter.layout();
      textPainter.paint(
        canvas,
        Offset(
          size.width / 2 - textPainter.width / 2,
          size.height - 30,
        ),
      );
    }
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) {
    return true;
  }
}

/// Custom painter for number line visualization
class NumberLineVisualizationPainter extends CustomPainter {
  final int firstNumber;
  final int secondNumber;
  final int currentStep;
  final double animationValue;
  final Color primaryColor;
  final Color secondaryColor;
  final Color textColor;

  NumberLineVisualizationPainter({
    required this.firstNumber,
    required this.secondNumber,
    required this.currentStep,
    required this.animationValue,
    required this.primaryColor,
    required this.secondaryColor,
    required this.textColor,
  });

  @override
  void paint(Canvas canvas, Size size) {
    final double lineY = size.height / 2;
    final double maxNumber = firstNumber * secondNumber + 2;
    final double pixelsPerUnit = (size.width - 40) / maxNumber;

    // Draw the number line
    final Paint linePaint = Paint()
      ..color = Colors.grey.shade400
      ..strokeWidth = 2
      ..style = PaintingStyle.stroke;

    canvas.drawLine(
      Offset(20, lineY),
      Offset(size.width - 20, lineY),
      linePaint,
    );

    // Draw tick marks and labels
    for (int i = 0; i <= maxNumber.toInt(); i++) {
      final double x = 20 + i * pixelsPerUnit;

      // Draw tick
      canvas.drawLine(
        Offset(x, lineY - 10),
        Offset(x, lineY + 10),
        linePaint,
      );

      // Draw label
      final textPainter = TextPainter(
        text: TextSpan(
          text: i.toString(),
          style: TextStyle(
            color: textColor,
            fontSize: 12,
          ),
        ),
        textDirection: TextDirection.ltr,
        textAlign: TextAlign.center,
      );
      textPainter.layout();
      textPainter.paint(
        canvas,
        Offset(x - textPainter.width / 2, lineY + 15),
      );
    }

    // Draw jumps on the number line
    if (currentStep >= 1) {
      final int visibleJumps = currentStep == 1
          ? (secondNumber * animationValue).floor()
          : secondNumber;

      for (int i = 0; i < visibleJumps; i++) {
        final double startX = 20 + i * firstNumber * pixelsPerUnit;
        final double endX = 20 + (i + 1) * firstNumber * pixelsPerUnit;

        // Draw arc for the jump
        final Path jumpPath = Path();
        jumpPath.moveTo(startX, lineY);
        jumpPath.quadraticBezierTo(
          (startX + endX) / 2,
          lineY - 40,
          endX,
          lineY,
        );

        canvas.drawPath(
          jumpPath,
          Paint()
            ..color = primaryColor
            ..style = PaintingStyle.stroke
            ..strokeWidth = 2,
        );

        // Draw jump label
        final String jumpLabel = '+$firstNumber';
        final textPainter = TextPainter(
          text: TextSpan(
            text: jumpLabel,
            style: TextStyle(
              color: primaryColor,
              fontSize: 14,
              fontWeight: FontWeight.bold,
            ),
          ),
          textDirection: TextDirection.ltr,
          textAlign: TextAlign.center,
        );
        textPainter.layout();
        textPainter.paint(
          canvas,
          Offset(
            (startX + endX) / 2 - textPainter.width / 2,
            lineY - 50,
          ),
        );
      }
    }

    // Draw current position marker
    if (currentStep >= 1) {
      final int position = currentStep == 1
          ? (secondNumber * animationValue).ceil() * firstNumber
          : secondNumber * firstNumber;

      final double x = 20 + position * pixelsPerUnit;

      canvas.drawCircle(
        Offset(x, lineY),
        10,
        Paint()..color = secondaryColor,
      );
    }

    // Draw multiplication expression
    if (currentStep >= 0) {
      final String expression = '$firstNumber × $secondNumber = ${firstNumber * secondNumber}';
      final TextStyle expressionStyle = TextStyle(
        color: textColor,
        fontSize: 18,
        fontWeight: FontWeight.bold,
      );

      final textPainter = TextPainter(
        text: TextSpan(
          text: expression,
          style: expressionStyle,
        ),
        textDirection: TextDirection.ltr,
        textAlign: TextAlign.center,
      );
      textPainter.layout();
      textPainter.paint(
        canvas,
        Offset(
          size.width / 2 - textPainter.width / 2,
          size.height - 30,
        ),
      );
    }
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) {
    return true;
  }
}

/// A widget that visualizes multiplication as arrays and repeated addition
/// for elementary math education.
class InteractiveMultiplicationArrayVisualizerWidget extends StatefulWidget {
  final Map<String, dynamic> data;
  final Function(bool)? onStateChanged;
  final Color primaryColor;
  final Color secondaryColor;
  final Color backgroundColor;
  final Color textColor;

  const InteractiveMultiplicationArrayVisualizerWidget({
    super.key,
    this.data = const {},
    this.onStateChanged,
    this.primaryColor = Colors.blue,
    this.secondaryColor = Colors.orange,
    this.backgroundColor = Colors.white,
    this.textColor = Colors.black87,
  });

  @override
  State<InteractiveMultiplicationArrayVisualizerWidget> createState() =>
      _InteractiveMultiplicationArrayVisualizerWidgetState();
}

class _InteractiveMultiplicationArrayVisualizerWidgetState
    extends State<InteractiveMultiplicationArrayVisualizerWidget>
    with SingleTickerProviderStateMixin {
  // Controllers
  late AnimationController _animationController;
  late Animation<double> _animation;
  final TextEditingController _firstNumberController = TextEditingController();
  final TextEditingController _secondNumberController = TextEditingController();

  // State variables
  String _visualizationType = 'Array';
  bool _isAnimating = false;
  bool _isCompleted = false;
  int _currentStep = 0;
  List<String> _steps = [];
  String? _errorMessage;
  String? _feedbackMessage;
  bool _showQuestion = false;
  String? _selectedAnswer;
  String? _correctAnswer;
  List<String> _answerOptions = [];
  bool _showCommutative = false;

  // Operation values
  int _firstNumber = 0;
  int _secondNumber = 0;
  int _result = 0;

  // Constants
  final int _maxNumber = 10;
  final List<String> _visualizationTypes = ['Array', 'Number Line', 'Groups'];

  @override
  void initState() {
    super.initState();

    // Initialize animation controller
    _animationController = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 1500),
    );

    _animation = CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeInOut,
    );

    // Add listener to animation controller
    _animationController.addStatusListener((status) {
      if (status == AnimationStatus.completed) {
        if (_currentStep < _steps.length - 1) {
          setState(() {
            _currentStep++;
          });
          _animationController.reset();
          _animationController.forward();
        } else {
          setState(() {
            _isAnimating = false;
            _showQuestion = true;
          });
        }
      }
    });

    // Set initial values
    _firstNumberController.text = '3';
    _secondNumberController.text = '4';

    // Initialize with default values
    _updateCalculation();
  }

  @override
  void dispose() {
    _animationController.dispose();
    _firstNumberController.dispose();
    _secondNumberController.dispose();
    super.dispose();
  }

  void _updateCalculation() {
    try {
      _firstNumber = int.parse(_firstNumberController.text);
      _secondNumber = int.parse(_secondNumberController.text);

      // Validate input
      if (_firstNumber <= 0 || _firstNumber > _maxNumber ||
          _secondNumber <= 0 || _secondNumber > _maxNumber) {
        setState(() {
          _errorMessage = 'Please enter numbers between 1 and $_maxNumber';
        });
        return;
      }

      setState(() {
        _errorMessage = null;
        _feedbackMessage = null;
        _showQuestion = false;
        _selectedAnswer = null;
        _currentStep = 0;
        _result = _firstNumber * _secondNumber;
      });

      // Generate steps based on visualization type
      _generateSteps();

      // Generate question and answers
      _generateQuestion();

    } catch (e) {
      setState(() {
        _errorMessage = 'Please enter valid numbers';
      });
    }
  }

  void _generateSteps() {
    switch (_visualizationType) {
      case 'Array':
        _steps = [
          'Create a grid with $_firstNumber columns and $_secondNumber rows',
          'Fill in the grid with $_firstNumber × $_secondNumber = $_result objects',
          'The total number of objects is $_result'
        ];
        if (_showCommutative) {
          _steps.add('Notice that $_firstNumber × $_secondNumber = $_secondNumber × $_firstNumber');
        }
        break;
      case 'Number Line':
        _steps = [
          'Start at 0 on the number line',
          'Make $_secondNumber jumps of size $_firstNumber',
          'We end up at $_result, so $_firstNumber × $_secondNumber = $_result'
        ];
        break;
      case 'Groups':
        _steps = [
          'Create $_secondNumber groups',
          'Put $_firstNumber objects in each group',
          'Count the total: $_firstNumber × $_secondNumber = $_result objects'
        ];
        break;
    }
  }

  void _generateQuestion() {
    // Generate answer options (including the correct one)
    _correctAnswer = _result.toString();

    // Generate 3 wrong answers that are close to the correct one
    List<String> options = [];
    if (_correctAnswer != null) {
      options.add(_correctAnswer!);
    }
    Random random = Random();

    while (options.length < 4) {
      int offset = random.nextInt(5) + 1;
      if (random.nextBool()) offset = -offset;
      int wrongAnswer = _result + offset;
      if (wrongAnswer > 0) {
        String wrongAnswerStr = wrongAnswer.toString();
        if (!options.contains(wrongAnswerStr)) {
          options.add(wrongAnswerStr);
        }
      }
    }

    options.shuffle();
    _answerOptions = options;
  }

  void _startAnimation() {
    if (_steps.isEmpty) return;

    setState(() {
      _currentStep = 0;
      _isAnimating = true;
      _showQuestion = false;
    });

    _animationController.reset();
    _animationController.forward();
  }

  void _stopAnimation() {
    setState(() {
      _isAnimating = false;
    });
    _animationController.stop();
  }

  void _resetAnimation() {
    setState(() {
      _currentStep = 0;
      _isAnimating = false;
      _showQuestion = false;
      _selectedAnswer = null;
      _feedbackMessage = null;
    });
    _animationController.reset();
  }

  void _checkAnswer(String answer) {
    setState(() {
      _selectedAnswer = answer;
      if (answer == _correctAnswer) {
        _feedbackMessage = 'Correct! Great job!';
        _isCompleted = true;
      } else {
        _feedbackMessage = 'Not quite. Try again!';
      }
    });

    // Notify parent of completion status
    widget.onStateChanged?.call(_isCompleted);
  }

  void _generateRandomProblem() {
    Random random = Random();
    int first = random.nextInt(_maxNumber) + 1;
    int second = random.nextInt(_maxNumber) + 1;

    setState(() {
      _firstNumberController.text = first.toString();
      _secondNumberController.text = second.toString();
    });

    _updateCalculation();
  }

  void _toggleCommutative() {
    setState(() {
      _showCommutative = !_showCommutative;
    });
    _updateCalculation();
  }

  Widget _buildInputControls() {
    return Column(
      children: [
        // Visualization type selector
        Row(
          children: [
            Text(
              'Visualization:',
              style: TextStyle(
                fontWeight: FontWeight.bold,
                color: widget.textColor,
              ),
            ),
            const SizedBox(width: 16),
            DropdownButton<String>(
              value: _visualizationType,
              onChanged: (String? newValue) {
                if (newValue != null) {
                  setState(() {
                    _visualizationType = newValue;
                  });
                  _updateCalculation();
                }
              },
              items: _visualizationTypes.map<DropdownMenuItem<String>>((String value) {
                return DropdownMenuItem<String>(
                  value: value,
                  child: Text(value),
                );
              }).toList(),
            ),
            const Spacer(),
            // Commutative property toggle
            Row(
              children: [
                Text(
                  'Show Commutative:',
                  style: TextStyle(
                    fontWeight: FontWeight.bold,
                    color: widget.textColor,
                  ),
                ),
                Checkbox(
                  value: _showCommutative,
                  onChanged: (bool? value) {
                    if (value != null) {
                      _toggleCommutative();
                    }
                  },
                  activeColor: widget.primaryColor,
                ),
              ],
            ),
          ],
        ),

        const SizedBox(height: 16),

        // Number inputs
        Row(
          children: [
            Expanded(
              child: TextField(
                controller: _firstNumberController,
                keyboardType: TextInputType.number,
                decoration: InputDecoration(
                  labelText: 'First Number',
                  border: OutlineInputBorder(),
                  contentPadding: EdgeInsets.symmetric(horizontal: 12, vertical: 8),
                ),
                onChanged: (_) => _updateCalculation(),
              ),
            ),
            Padding(
              padding: const EdgeInsets.symmetric(horizontal: 16),
              child: Text(
                '×',
                style: TextStyle(
                  fontSize: 24,
                  fontWeight: FontWeight.bold,
                  color: widget.primaryColor,
                ),
              ),
            ),
            Expanded(
              child: TextField(
                controller: _secondNumberController,
                keyboardType: TextInputType.number,
                decoration: InputDecoration(
                  labelText: 'Second Number',
                  border: OutlineInputBorder(),
                  contentPadding: EdgeInsets.symmetric(horizontal: 12, vertical: 8),
                ),
                onChanged: (_) => _updateCalculation(),
              ),
            ),
            const SizedBox(width: 16),
            ElevatedButton(
              onPressed: _generateRandomProblem,
              style: ElevatedButton.styleFrom(
                backgroundColor: widget.secondaryColor,
                padding: EdgeInsets.symmetric(horizontal: 12, vertical: 8),
              ),
              child: Text('Random'),
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildVisualizationArea() {
    return Container(
      height: 250,
      width: double.infinity,
      decoration: BoxDecoration(
        color: Colors.grey.shade50,
        borderRadius: BorderRadius.circular(8),
        border: Border.all(
          color: _isCompleted ? Colors.green : Colors.grey.shade300,
          width: _isCompleted ? 2 : 1,
        ),
      ),
      child: AnimatedBuilder(
        animation: _animation,
        builder: (context, child) {
          return CustomPaint(
            painter: _getVisualizationPainter(),
            child: Container(),
          );
        },
      ),
    );
  }

  CustomPainter _getVisualizationPainter() {
    switch (_visualizationType) {
      case 'Array':
        return ArrayVisualizationPainter(
          firstNumber: _firstNumber,
          secondNumber: _secondNumber,
          currentStep: _currentStep,
          animationValue: _isAnimating ? _animation.value : 1.0,
          primaryColor: widget.primaryColor,
          secondaryColor: widget.secondaryColor,
          textColor: widget.textColor,
          showCommutative: _showCommutative,
        );
      case 'Number Line':
        return NumberLineVisualizationPainter(
          firstNumber: _firstNumber,
          secondNumber: _secondNumber,
          currentStep: _currentStep,
          animationValue: _isAnimating ? _animation.value : 1.0,
          primaryColor: widget.primaryColor,
          secondaryColor: widget.secondaryColor,
          textColor: widget.textColor,
        );
      case 'Groups':
        return GroupsVisualizationPainter(
          firstNumber: _firstNumber,
          secondNumber: _secondNumber,
          currentStep: _currentStep,
          animationValue: _isAnimating ? _animation.value : 1.0,
          primaryColor: widget.primaryColor,
          secondaryColor: widget.secondaryColor,
          textColor: widget.textColor,
        );
      default:
        return ArrayVisualizationPainter(
          firstNumber: _firstNumber,
          secondNumber: _secondNumber,
          currentStep: _currentStep,
          animationValue: _isAnimating ? _animation.value : 1.0,
          primaryColor: widget.primaryColor,
          secondaryColor: widget.secondaryColor,
          textColor: widget.textColor,
          showCommutative: _showCommutative,
        );
    }
  }

  Widget _buildAnimationControls() {
    return Row(
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        IconButton(
          icon: Icon(_isAnimating ? Icons.pause : Icons.play_arrow),
          onPressed: _isAnimating ? _stopAnimation : _startAnimation,
          color: widget.primaryColor,
          iconSize: 32,
        ),
        IconButton(
          icon: const Icon(Icons.replay),
          onPressed: _resetAnimation,
          color: widget.primaryColor,
          iconSize: 32,
        ),
      ],
    );
  }

  Widget _buildQuestionSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'What is $_firstNumber × $_secondNumber?',
          style: TextStyle(
            fontSize: 18,
            fontWeight: FontWeight.bold,
            color: widget.textColor,
          ),
        ),
        const SizedBox(height: 12),
        Wrap(
          spacing: 8,
          runSpacing: 8,
          children: _answerOptions.map((option) {
            bool isSelected = _selectedAnswer == option;
            bool isCorrect = option == _correctAnswer;

            Color buttonColor = isSelected
                ? (isCorrect ? Colors.green : Colors.red)
                : widget.primaryColor;

            return ElevatedButton(
              onPressed: _selectedAnswer == null ? () => _checkAnswer(option) : null,
              style: ElevatedButton.styleFrom(
                backgroundColor: buttonColor,
                padding: EdgeInsets.symmetric(horizontal: 16, vertical: 8),
              ),
              child: Text(
                option,
                style: TextStyle(
                  fontSize: 16,
                  color: Colors.white,
                ),
              ),
            );
          }).toList(),
        ),
      ],
    );
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: widget.backgroundColor,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withAlpha(50),
            spreadRadius: 1,
            blurRadius: 3,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Title and controls
          Text(
            'Multiplication Array Visualizer',
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
              color: widget.textColor,
            ),
          ),

          const SizedBox(height: 16),

          // Input controls
          _buildInputControls(),

          if (_errorMessage != null)
            Padding(
              padding: const EdgeInsets.only(top: 8.0),
              child: Text(
                _errorMessage!,
                style: TextStyle(
                  color: Colors.red,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ),

          const SizedBox(height: 16),

          // Visualization area
          _buildVisualizationArea(),

          const SizedBox(height: 16),

          // Step description
          if (_steps.isNotEmpty && _currentStep < _steps.length)
            Text(
              _steps[_currentStep],
              style: TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.bold,
                color: widget.primaryColor,
              ),
            ),

          const SizedBox(height: 16),

          // Animation controls
          _buildAnimationControls(),

          const SizedBox(height: 16),

          // Question and feedback
          if (_showQuestion) _buildQuestionSection(),

          if (_feedbackMessage != null)
            Padding(
              padding: const EdgeInsets.only(top: 8.0),
              child: Text(
                _feedbackMessage!,
                style: TextStyle(
                  color: _selectedAnswer == _correctAnswer
                      ? Colors.green
                      : Colors.red,
                  fontWeight: FontWeight.bold,
                  fontSize: 16,
                ),
              ),
            ),
        ],
      ),
    );
  }
}
