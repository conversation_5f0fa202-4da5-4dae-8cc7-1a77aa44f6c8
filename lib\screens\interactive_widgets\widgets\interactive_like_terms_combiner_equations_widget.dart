import 'package:flutter/material.dart';
import 'dart:math' as math;

/// A widget that demonstrates combining like terms in equations.
class InteractiveLikeTermsCombinerEquationsWidget extends StatefulWidget {
  final Map<String, dynamic> data;
  final Function(bool)? onStateChanged;
  final Color primaryColor;
  final Color secondaryColor;
  final Color backgroundColor;
  final Color textColor;

  const InteractiveLikeTermsCombinerEquationsWidget({
    super.key,
    this.data = const {},
    this.onStateChanged,
    this.primaryColor = Colors.blue,
    this.secondaryColor = Colors.orange,
    this.backgroundColor = Colors.white,
    this.textColor = Colors.black87,
  });

  @override
  State<InteractiveLikeTermsCombinerEquationsWidget> createState() =>
      _InteractiveLikeTermsCombinerEquationsWidgetState();
}

class _InteractiveLikeTermsCombinerEquationsWidgetState
    extends State<InteractiveLikeTermsCombinerEquationsWidget>
    with SingleTickerProviderStateMixin {
  // Animation controller
  late AnimationController _animationController;
  late Animation<double> _animation;

  // State variables
  bool _isCompleted = false;
  bool _isAnimating = false;
  int _currentEquationIndex = 0;
  int _currentStep = 0;
  List<EquationData> _equations = [];
  late EquationData _currentEquation;
  bool _showQuiz = false;
  String? _selectedAnswer;
  bool _isCorrect = false;
  String? _feedbackMessage;

  @override
  void initState() {
    super.initState();

    // Initialize animation controller
    _animationController = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 800),
    );

    _animation = CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeInOut,
    );

    // Add listener to animation controller
    _animationController.addStatusListener((status) {
      if (status == AnimationStatus.completed) {
        setState(() {
          _currentStep++;
          if (_currentStep < _currentEquation.steps.length) {
            _animationController.reset();
            _animationController.forward();
          } else {
            _isAnimating = false;
          }
        });
      }
    });

    // Initialize equations
    _initializeEquations();
    _currentEquation = _equations[_currentEquationIndex];
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  void _initializeEquations() {
    // Check if equations are provided in the data
    if (widget.data.containsKey('equations') &&
        widget.data['equations'] is List &&
        widget.data['equations'].isNotEmpty) {

      final equationsData = widget.data['equations'] as List;
      for (final eqData in equationsData) {
        if (eqData is Map<String, dynamic>) {
          final equation = EquationData.fromJson(eqData);
          _equations.add(equation);
        }
      }
    }

    // If no equations were provided, create default ones
    if (_equations.isEmpty) {
      _equations = [
        EquationData(
          initialEquation: '3x + 5x = 16',
          simplifiedEquation: '8x = 16',
          solution: '2',
          steps: [
            EquationStep(
              equation: '3x + 5x = 16',
              explanation: 'Start with the original equation.',
              highlightRanges: [
                HighlightRange(start: 0, end: 9, color: Colors.grey.withOpacity(0.3)),
              ],
              operation: 'Original equation',
            ),
            EquationStep(
              equation: '3x + 5x = 16',
              explanation: 'Identify like terms: 3x and 5x are like terms because they have the same variable.',
              highlightRanges: [
                HighlightRange(start: 0, end: 2, color: Colors.orange.withOpacity(0.3)),
                HighlightRange(start: 5, end: 7, color: Colors.orange.withOpacity(0.3)),
              ],
              operation: 'Identify like terms',
            ),
            EquationStep(
              equation: '(3 + 5)x = 16',
              explanation: 'Group the coefficients of like terms.',
              highlightRanges: [
                HighlightRange(start: 1, end: 6, color: Colors.orange.withOpacity(0.3)),
              ],
              operation: 'Group coefficients',
            ),
            EquationStep(
              equation: '8x = 16',
              explanation: 'Combine like terms: 3x + 5x = 8x.',
              highlightRanges: [
                HighlightRange(start: 0, end: 2, color: Colors.green.withOpacity(0.3)),
              ],
              operation: 'Combine like terms',
            ),
            EquationStep(
              equation: 'x = 2',
              explanation: 'Solve the simplified equation: 8x = 16 → x = 16 ÷ 8 = 2.',
              highlightRanges: [
                HighlightRange(start: 0, end: 5, color: Colors.green.withOpacity(0.3)),
              ],
              operation: 'Solve the equation',
            ),
          ],
          quiz: {
            'question': 'Simplify and solve: 4x + 7x = 33',
            'options': ['3', '11', '3.3', '11/3'],
            'correctAnswer': '3',
            'explanation': 'First combine like terms: 4x + 7x = 11x. Then solve: 11x = 33 → x = 33 ÷ 11 = 3.'
          },
        ),
        EquationData(
          initialEquation: '2x + 7 + 5x - 3 = 18',
          simplifiedEquation: '7x + 4 = 18',
          solution: '2',
          steps: [
            EquationStep(
              equation: '2x + 7 + 5x - 3 = 18',
              explanation: 'Start with the original equation.',
              highlightRanges: [
                HighlightRange(start: 0, end: 17, color: Colors.grey.withOpacity(0.3)),
              ],
              operation: 'Original equation',
            ),
            EquationStep(
              equation: '2x + 7 + 5x - 3 = 18',
              explanation: 'Identify like terms: 2x and 5x are like terms (variable terms). 7 and -3 are like terms (constant terms).',
              highlightRanges: [
                HighlightRange(start: 0, end: 2, color: Colors.orange.withOpacity(0.3)),
                HighlightRange(start: 9, end: 11, color: Colors.orange.withOpacity(0.3)),
                HighlightRange(start: 5, end: 6, color: Colors.blue.withOpacity(0.3)),
                HighlightRange(start: 14, end: 16, color: Colors.blue.withOpacity(0.3)),
              ],
              operation: 'Identify like terms',
            ),
            EquationStep(
              equation: '(2 + 5)x + (7 - 3) = 18',
              explanation: 'Group the coefficients of like terms.',
              highlightRanges: [
                HighlightRange(start: 1, end: 6, color: Colors.orange.withOpacity(0.3)),
                HighlightRange(start: 10, end: 15, color: Colors.blue.withOpacity(0.3)),
              ],
              operation: 'Group coefficients',
            ),
            EquationStep(
              equation: '7x + 4 = 18',
              explanation: 'Combine like terms: 2x + 5x = 7x and 7 - 3 = 4.',
              highlightRanges: [
                HighlightRange(start: 0, end: 2, color: Colors.green.withOpacity(0.3)),
                HighlightRange(start: 5, end: 6, color: Colors.green.withOpacity(0.3)),
              ],
              operation: 'Combine like terms',
            ),
            EquationStep(
              equation: '7x = 14',
              explanation: 'Subtract 4 from both sides: 7x + 4 - 4 = 18 - 4 → 7x = 14.',
              highlightRanges: [
                HighlightRange(start: 0, end: 6, color: Colors.green.withOpacity(0.3)),
              ],
              operation: 'Isolate the variable term',
            ),
            EquationStep(
              equation: 'x = 2',
              explanation: 'Divide both sides by 7: 7x ÷ 7 = 14 ÷ 7 → x = 2.',
              highlightRanges: [
                HighlightRange(start: 0, end: 5, color: Colors.green.withOpacity(0.3)),
              ],
              operation: 'Solve the equation',
            ),
          ],
          quiz: {
            'question': 'Simplify and solve: 3x + 5 + 4x - 2 = 21',
            'options': ['2', '3', '4', '7'],
            'correctAnswer': '3',
            'explanation': 'First combine like terms: 3x + 4x + 5 - 2 = 7x + 3 = 21. Then solve: 7x = 18 → x = 18 ÷ 7 = 3.'
          },
        ),
        EquationData(
          initialEquation: '5x - 2 = 3x + 6',
          simplifiedEquation: '2x - 2 = 6',
          solution: '4',
          steps: [
            EquationStep(
              equation: '5x - 2 = 3x + 6',
              explanation: 'Start with the original equation.',
              highlightRanges: [
                HighlightRange(start: 0, end: 14, color: Colors.grey.withOpacity(0.3)),
              ],
              operation: 'Original equation',
            ),
            EquationStep(
              equation: '5x - 2 = 3x + 6',
              explanation: 'To combine like terms, we need to get all variable terms on one side and all constant terms on the other side.',
              highlightRanges: [
                HighlightRange(start: 0, end: 2, color: Colors.orange.withOpacity(0.3)),
                HighlightRange(start: 9, end: 11, color: Colors.orange.withOpacity(0.3)),
              ],
              operation: 'Identify like terms on different sides',
            ),
            EquationStep(
              equation: '5x - 3x - 2 = 6',
              explanation: 'Subtract 3x from both sides to get all variable terms on the left.',
              highlightRanges: [
                HighlightRange(start: 0, end: 7, color: Colors.orange.withOpacity(0.3)),
              ],
              operation: 'Move variable terms to one side',
            ),
            EquationStep(
              equation: '2x - 2 = 6',
              explanation: 'Combine like terms: 5x - 3x = 2x.',
              highlightRanges: [
                HighlightRange(start: 0, end: 2, color: Colors.green.withOpacity(0.3)),
              ],
              operation: 'Combine like terms',
            ),
            EquationStep(
              equation: '2x = 8',
              explanation: 'Add 2 to both sides: 2x - 2 + 2 = 6 + 2 → 2x = 8.',
              highlightRanges: [
                HighlightRange(start: 0, end: 6, color: Colors.green.withOpacity(0.3)),
              ],
              operation: 'Isolate the variable term',
            ),
            EquationStep(
              equation: 'x = 4',
              explanation: 'Divide both sides by 2: 2x ÷ 2 = 8 ÷ 2 → x = 4.',
              highlightRanges: [
                HighlightRange(start: 0, end: 5, color: Colors.green.withOpacity(0.3)),
              ],
              operation: 'Solve the equation',
            ),
          ],
          quiz: {
            'question': 'Simplify and solve: 7x + 3 = 4x - 6',
            'options': ['2', '3', '4', '-3'],
            'correctAnswer': '3',
            'explanation': 'First rearrange to get variables on one side: 7x - 4x = -6 - 3. Then solve: 3x = -9 → x = -9 ÷ 3 = -3.'
          },
        ),
      ];
    }
  }

  void _startAnimation() {
    if (_currentEquation.steps.isEmpty) return;

    setState(() {
      _currentStep = 0;
      _isAnimating = true;
      _showQuiz = false;
      _selectedAnswer = null;
      _feedbackMessage = null;
    });

    _animationController.reset();
    _animationController.forward();
  }

  void _stopAnimation() {
    setState(() {
      _isAnimating = false;
    });
    _animationController.stop();
  }

  void _resetAnimation() {
    setState(() {
      _currentStep = 0;
      _isAnimating = false;
      _showQuiz = false;
      _selectedAnswer = null;
      _feedbackMessage = null;
    });
    _animationController.reset();
  }

  void _showQuizQuestion() {
    setState(() {
      _showQuiz = true;
      _selectedAnswer = null;
      _feedbackMessage = null;
    });
  }

  void _checkAnswer(String answer) {
    final isCorrect = answer == _currentEquation.quiz['correctAnswer'];

    setState(() {
      _selectedAnswer = answer;
      _isCorrect = isCorrect;

      if (isCorrect) {
        _feedbackMessage = 'Correct! ${_currentEquation.quiz['explanation']}';
      } else {
        _feedbackMessage = 'Not quite. ${_currentEquation.quiz['explanation']}';
      }
    });
  }

  void _nextEquation() {
    if (_currentEquationIndex < _equations.length - 1) {
      setState(() {
        _currentEquationIndex++;
        _currentEquation = _equations[_currentEquationIndex];
        _resetAnimation();
      });
    } else {
      // All equations completed
      setState(() {
        _isCompleted = true;
      });
      widget.onStateChanged?.call(true);
    }
  }

  void _previousEquation() {
    if (_currentEquationIndex > 0) {
      setState(() {
        _currentEquationIndex--;
        _currentEquation = _equations[_currentEquationIndex];
        _resetAnimation();
      });
    }
  }

  void _resetWidget() {
    setState(() {
      _currentEquationIndex = 0;
      _currentEquation = _equations[_currentEquationIndex];
      _resetAnimation();
      _isCompleted = false;
    });
    widget.onStateChanged?.call(false);
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: widget.backgroundColor,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withOpacity(0.2),
            spreadRadius: 1,
            blurRadius: 3,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: _isCompleted ? _buildCompletionScreen() : _buildMainContent(),
    );
  }

  Widget _buildMainContent() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Title and progress indicator
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Text(
              'Like Terms in Equations',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
                color: widget.primaryColor,
              ),
            ),
            Text(
              'Example ${_currentEquationIndex + 1}/${_equations.length}',
              style: TextStyle(
                fontSize: 14,
                color: widget.textColor.withOpacity(0.7),
              ),
            ),
          ],
        ),

        const SizedBox(height: 16),

        // Like terms reminder
        Container(
          width: double.infinity,
          padding: const EdgeInsets.all(12),
          decoration: BoxDecoration(
            color: widget.primaryColor.withOpacity(0.1),
            borderRadius: BorderRadius.circular(8),
            border: Border.all(color: widget.primaryColor.withOpacity(0.3)),
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                'Like Terms',
                style: TextStyle(
                  fontWeight: FontWeight.bold,
                  color: widget.primaryColor,
                ),
              ),
              const SizedBox(height: 8),
              Text(
                'Like terms have the same variable(s) with the same exponent(s).',
                style: TextStyle(
                  color: widget.textColor,
                ),
              ),
              const SizedBox(height: 4),
              Text(
                'Examples:',
                style: TextStyle(
                  fontWeight: FontWeight.bold,
                  color: widget.textColor,
                ),
              ),
              const SizedBox(height: 4),
              Row(
                children: [
                  _buildLikeTermsExample('3x, 5x, -2x', true),
                  const SizedBox(width: 8),
                  _buildLikeTermsExample('x², 3x²', true),
                  const SizedBox(width: 8),
                  _buildLikeTermsExample('7, -4, 10', true),
                ],
              ),
              const SizedBox(height: 4),
              Row(
                children: [
                  _buildLikeTermsExample('2x, 3y', false),
                  const SizedBox(width: 8),
                  _buildLikeTermsExample('x, x²', false),
                  const SizedBox(width: 8),
                  _buildLikeTermsExample('5, 2x', false),
                ],
              ),
            ],
          ),
        ),

        const SizedBox(height: 24),

        // Current equation
        if (!_showQuiz) ...[
          Text(
            'Equation:',
            style: TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.w500,
              color: widget.textColor,
            ),
          ),
          const SizedBox(height: 8),
          Container(
            width: double.infinity,
            padding: const EdgeInsets.symmetric(vertical: 16, horizontal: 16),
            decoration: BoxDecoration(
              color: Colors.grey.shade50,
              borderRadius: BorderRadius.circular(8),
              border: Border.all(color: Colors.grey.shade300),
            ),
            child: _isAnimating && _currentStep < _currentEquation.steps.length
                ? _buildHighlightedExpression(_currentEquation.steps[_currentStep])
                : Text(
                    _currentEquation.initialEquation,
                    style: TextStyle(
                      fontSize: 24,
                      fontWeight: FontWeight.bold,
                      color: widget.textColor,
                    ),
                    textAlign: TextAlign.center,
                  ),
          ),

          const SizedBox(height: 16),

          // Current step explanation
          if (_isAnimating && _currentStep < _currentEquation.steps.length) ...[
            Container(
              width: double.infinity,
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: widget.secondaryColor.withOpacity(0.1),
                borderRadius: BorderRadius.circular(8),
                border: Border.all(color: widget.secondaryColor.withOpacity(0.3)),
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'Step ${_currentStep + 1}: ${_currentEquation.steps[_currentStep].operation}',
                    style: TextStyle(
                      fontWeight: FontWeight.bold,
                      color: widget.secondaryColor,
                    ),
                  ),
                  const SizedBox(height: 4),
                  Text(
                    _currentEquation.steps[_currentStep].explanation,
                    style: TextStyle(
                      color: widget.textColor,
                    ),
                  ),
                ],
              ),
            ),
          ],

          const Spacer(),

          // Animation controls
          Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              if (!_isAnimating) ...[
                ElevatedButton.icon(
                  onPressed: _startAnimation,
                  icon: const Icon(Icons.play_arrow),
                  label: const Text('Start Animation'),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: widget.primaryColor,
                  ),
                ),
                const SizedBox(width: 16),
                ElevatedButton.icon(
                  onPressed: _showQuizQuestion,
                  icon: const Icon(Icons.quiz),
                  label: const Text('Try a Quiz'),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: widget.secondaryColor,
                  ),
                ),
              ] else ...[
                ElevatedButton.icon(
                  onPressed: _stopAnimation,
                  icon: const Icon(Icons.pause),
                  label: const Text('Pause'),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Colors.red,
                  ),
                ),
              ],
            ],
          ),
        ],

        // Quiz section
        if (_showQuiz) ...[
          Text(
            'Quiz:',
            style: TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.w500,
              color: widget.textColor,
            ),
          ),
          const SizedBox(height: 8),
          Container(
            width: double.infinity,
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: widget.primaryColor.withOpacity(0.1),
              borderRadius: BorderRadius.circular(8),
              border: Border.all(color: widget.primaryColor.withOpacity(0.3)),
            ),
            child: Text(
              _currentEquation.quiz['question'],
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
                color: widget.textColor,
              ),
            ),
          ),

          const SizedBox(height: 16),

          // Answer options
          ...(_currentEquation.quiz['options'] as List<String>).map((option) {
            final bool isSelected = _selectedAnswer == option;
            final bool isCorrect = option == _currentEquation.quiz['correctAnswer'];

            return Padding(
              padding: const EdgeInsets.only(bottom: 8.0),
              child: InkWell(
                onTap: _selectedAnswer == null ? () => _checkAnswer(option) : null,
                child: Container(
                  width: double.infinity,
                  padding: const EdgeInsets.all(12),
                  decoration: BoxDecoration(
                    color: isSelected
                        ? (isCorrect ? Colors.green.withOpacity(0.2) : Colors.red.withOpacity(0.2))
                        : Colors.grey.shade50,
                    borderRadius: BorderRadius.circular(8),
                    border: Border.all(
                      color: isSelected
                          ? (isCorrect ? Colors.green : Colors.red)
                          : Colors.grey.shade300,
                    ),
                  ),
                  child: Row(
                    children: [
                      Expanded(
                        child: Text(
                          option,
                          style: TextStyle(
                            fontWeight: isSelected ? FontWeight.bold : FontWeight.normal,
                            color: widget.textColor,
                          ),
                        ),
                      ),
                      if (isSelected)
                        Icon(
                          isCorrect ? Icons.check_circle : Icons.cancel,
                          color: isCorrect ? Colors.green : Colors.red,
                        ),
                    ],
                  ),
                ),
              ),
            );
          }).toList(),

          if (_feedbackMessage != null)
            Container(
              width: double.infinity,
              padding: const EdgeInsets.all(12),
              margin: const EdgeInsets.only(top: 16),
              decoration: BoxDecoration(
                color: _isCorrect
                    ? Colors.green.withOpacity(0.1)
                    : Colors.red.withOpacity(0.1),
                borderRadius: BorderRadius.circular(8),
                border: Border.all(
                  color: _isCorrect ? Colors.green : Colors.red,
                ),
              ),
              child: Text(
                _feedbackMessage!,
                style: TextStyle(
                  color: _isCorrect ? Colors.green : Colors.red,
                ),
              ),
            ),

          const Spacer(),

          // Back to animation button
          ElevatedButton.icon(
            onPressed: _startAnimation,
            icon: const Icon(Icons.arrow_back),
            label: const Text('Back to Animation'),
            style: ElevatedButton.styleFrom(
              backgroundColor: widget.primaryColor,
            ),
          ),
        ],

        const SizedBox(height: 16),

        // Navigation buttons
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            // Previous button
            ElevatedButton(
              onPressed: _currentEquationIndex > 0 ? _previousEquation : null,
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.grey.shade200,
                foregroundColor: Colors.black87,
              ),
              child: const Text('Previous'),
            ),

            // Next button
            ElevatedButton(
              onPressed: _nextEquation,
              style: ElevatedButton.styleFrom(
                backgroundColor: widget.primaryColor,
                foregroundColor: Colors.white,
              ),
              child: Text(_currentEquationIndex < _equations.length - 1 ? 'Next' : 'Finish'),
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildLikeTermsExample(String terms, bool areLike) {
    return Expanded(
      child: Container(
        padding: const EdgeInsets.symmetric(vertical: 4, horizontal: 8),
        decoration: BoxDecoration(
          color: areLike ? Colors.green.withOpacity(0.1) : Colors.red.withOpacity(0.1),
          borderRadius: BorderRadius.circular(4),
          border: Border.all(
            color: areLike ? Colors.green.withOpacity(0.3) : Colors.red.withOpacity(0.3),
          ),
        ),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Text(
              terms,
              style: TextStyle(
                fontSize: 12,
                color: widget.textColor,
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(width: 4),
            Icon(
              areLike ? Icons.check_circle : Icons.cancel,
              color: areLike ? Colors.green : Colors.red,
              size: 12,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildHighlightedExpression(EquationStep step) {
    final text = step.equation;
    final List<HighlightRange> highlights = step.highlightRanges;

    if (highlights.isEmpty) {
      return Text(
        text,
        style: TextStyle(
          fontSize: 24,
          fontWeight: FontWeight.bold,
          color: widget.textColor,
        ),
        textAlign: TextAlign.center,
      );
    }

    // Sort highlights by start index
    highlights.sort((a, b) => a.start.compareTo(b.start));

    final List<TextSpan> spans = [];
    int currentIndex = 0;

    for (final highlight in highlights) {
      if (highlight.start > currentIndex) {
        // Add non-highlighted text before this highlight
        spans.add(
          TextSpan(
            text: text.substring(currentIndex, highlight.start),
            style: TextStyle(
              fontSize: 24,
              fontWeight: FontWeight.bold,
              color: widget.textColor,
            ),
          ),
        );
      }

      // Add highlighted text
      spans.add(
        TextSpan(
          text: text.substring(highlight.start, highlight.end),
          style: TextStyle(
            fontSize: 24,
            fontWeight: FontWeight.bold,
            color: widget.textColor,
            backgroundColor: highlight.color,
          ),
        ),
      );

      currentIndex = highlight.end;
    }

    // Add any remaining text after the last highlight
    if (currentIndex < text.length) {
      spans.add(
        TextSpan(
          text: text.substring(currentIndex),
          style: TextStyle(
            fontSize: 24,
            fontWeight: FontWeight.bold,
            color: widget.textColor,
          ),
        ),
      );
    }

    return RichText(
      text: TextSpan(children: spans),
      textAlign: TextAlign.center,
    );
  }

  Widget _buildCompletionScreen() {
    return Column(
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        Icon(
          Icons.check_circle,
          size: 80,
          color: Colors.green,
        ),
        const SizedBox(height: 24),
        Text(
          'Congratulations!',
          style: TextStyle(
            fontSize: 24,
            fontWeight: FontWeight.bold,
            color: widget.primaryColor,
          ),
        ),
        const SizedBox(height: 16),
        Text(
          'You\'ve completed all the like terms examples!',
          textAlign: TextAlign.center,
          style: TextStyle(
            fontSize: 16,
            color: widget.textColor,
          ),
        ),
        const SizedBox(height: 32),
        ElevatedButton.icon(
          onPressed: _resetWidget,
          icon: const Icon(Icons.refresh),
          label: const Text('Start Again'),
          style: ElevatedButton.styleFrom(
            backgroundColor: widget.secondaryColor,
            padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
          ),
        ),
      ],
    );
  }
}

/// Data class for equation information
class EquationData {
  final String initialEquation;
  final String simplifiedEquation;
  final String solution;
  final List<EquationStep> steps;
  final Map<String, dynamic> quiz;

  EquationData({
    required this.initialEquation,
    required this.simplifiedEquation,
    required this.solution,
    required this.steps,
    required this.quiz,
  });

  factory EquationData.fromJson(Map<String, dynamic> json) {
    final steps = <EquationStep>[];
    if (json.containsKey('steps') && json['steps'] is List) {
      for (final stepData in json['steps']) {
        if (stepData is Map<String, dynamic>) {
          steps.add(EquationStep.fromJson(stepData));
        }
      }
    }

    return EquationData(
      initialEquation: json['initialEquation'] ?? '3x + 5x = 16',
      simplifiedEquation: json['simplifiedEquation'] ?? '8x = 16',
      solution: json['solution'] ?? '2',
      steps: steps,
      quiz: json['quiz'] ?? {
        'question': 'Simplify and solve: 4x + 7x = 33',
        'options': ['3', '11', '3.3', '11/3'],
        'correctAnswer': '3',
        'explanation': 'First combine like terms: 4x + 7x = 11x. Then solve: 11x = 33 → x = 33 ÷ 11 = 3.'
      },
    );
  }
}

/// Data class for equation solution steps
class EquationStep {
  final String equation;
  final String explanation;
  final String operation;
  final List<HighlightRange> highlightRanges;

  EquationStep({
    required this.equation,
    required this.explanation,
    required this.operation,
    required this.highlightRanges,
  });

  factory EquationStep.fromJson(Map<String, dynamic> json) {
    final highlightRanges = <HighlightRange>[];
    if (json.containsKey('highlightRanges') && json['highlightRanges'] is List) {
      for (final rangeData in json['highlightRanges']) {
        if (rangeData is Map<String, dynamic>) {
          highlightRanges.add(HighlightRange.fromJson(rangeData));
        }
      }
    }

    return EquationStep(
      equation: json['equation'] ?? '',
      explanation: json['explanation'] ?? '',
      operation: json['operation'] ?? '',
      highlightRanges: highlightRanges,
    );
  }
}

/// Data class for highlighting parts of expressions
class HighlightRange {
  final int start;
  final int end;
  final Color color;

  HighlightRange({
    required this.start,
    required this.end,
    required this.color,
  });

  factory HighlightRange.fromJson(Map<String, dynamic> json) {
    return HighlightRange(
      start: json['start'] ?? 0,
      end: json['end'] ?? 0,
      color: Color(json['color'] ?? 0x4D9E9E9E), // Default to grey with 30% opacity
    );
  }
}
