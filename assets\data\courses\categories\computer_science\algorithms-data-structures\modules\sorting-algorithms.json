{"id": "sorting-algorithms", "title": "Sorting Algorithms", "description": "Learn various sorting techniques like bubble sort, merge sort, and quick sort to organize data efficiently.", "order": 2, "lessons": [{"id": "intro-to-sorting", "title": "Why Sorting Matters", "description": "Understand the importance of sorting and basic concepts like comparison sorts.", "order": 1, "type": "interactive_lesson", "estimatedTimeMinutes": 8, "contentBlocks": [{"id": "sorting_screen1_importance", "type": "lesson_screen", "order": 1, "estimatedTimeSeconds": 60, "content": {"headline": "Putting Things in Order", "body_md": "Sorting is the process of arranging items in a specific sequence (e.g., numerical, alphabetical). It's one of the most fundamental problems in computer science.\n\nWhy is sorting so important? Sorted data is often much easier and faster to search and process.", "visual": {"type": "giphy_search", "value": "organizing sorting"}, "interactive_element": {"type": "button", "button_text": "Benefits of Sorting?"}, "audio_narration_url": null}}, {"id": "sorting_screen2_benefits", "type": "lesson_screen", "order": 2, "estimatedTimeSeconds": 75, "content": {"headline": "Benefits of Sorted Data", "body_md": "*   **Efficient Searching:** Binary search (very fast!) only works on sorted data.\n*   **Easier Data Analysis:** Finding min/max, median, duplicates is simpler.\n*   **Data Presentation:** Sorted data is easier for humans to read and understand.\n*   **Foundation for Other Algorithms:** Many algorithms require sorted input.\n\nThink of a phone book (if you've seen one!). How does sorting help?", "visual": {"type": "unsplash_search", "value": "ordered books on shelf"}, "interactive_element": {"type": "text_input", "question_text": "How does sorting help in a phone book?", "placeholder_text": "e.g., Quick lookup by name", "correct_answer_regex": ".+", "feedback_correct": "Exactly! Alphabetical sorting allows for quick lookups."}, "audio_narration_url": null}}, {"id": "sorting_screen3_comparison_sorts", "type": "lesson_screen", "order": 3, "estimatedTimeSeconds": 60, "content": {"headline": "Comparison Sorts", "body_md": "Many sorting algorithms are **comparison sorts**. They determine the order by comparing pairs of elements.\n\nWe'll look at a few classic comparison sorts: Bubble Sort, Merge Sort, and Quick Sort. Each has different performance characteristics.", "visual": {"type": "giphy_search", "value": "comparing two items"}, "interactive_element": {"type": "button", "button_text": "Let's Start with B<PERSON>ble Sort"}, "audio_narration_url": null}}]}, {"id": "bubble-sort", "title": "Bubble Sort: Simple but Slow", "description": "Understand the mechanics of Bubble Sort and its performance.", "order": 2, "type": "interactive_lesson", "estimatedTimeMinutes": 10, "contentBlocks": [{"id": "bubble_screen1_how_it_works", "type": "lesson_screen", "order": 1, "estimatedTimeSeconds": 75, "content": {"headline": "Bubble Sort: The Basics", "body_md": "Bubble Sort repeatedly steps through the list, compares adjacent elements, and swaps them if they are in the wrong order. The largest (or smallest) elements \"bubble\" to their correct position at the end of the list with each pass.\n\nIt's simple to understand but not very efficient for large lists.", "visual": {"type": "giphy_search", "value": "bubbles rising"}, "interactive_element": {"type": "button", "button_text": "See an Example"}, "audio_narration_url": null}}, {"id": "bubble_screen2_example", "type": "lesson_screen", "order": 2, "estimatedTimeSeconds": 90, "content": {"headline": "Bubble Sort Example: [5, 1, 4, 2]", "body_md": "**Pass 1:**\n*   ( **5, 1**, 4, 2 ) -> ( **1, 5**, 4, 2 ) - Swap\n*   ( 1, **5, 4**, 2 ) -> ( 1, **4, 5**, 2 ) - Swap\n*   ( 1, 4, **5, 2** ) -> ( 1, 4, **2, 5** ) - Swap\n*Result after Pass 1: [1, 4, 2, 5]* (5 is in place)\n\n**Pass 2 (on first 3 elements):**\n*   ( **1, 4**, 2, 5 ) -> ( **1, 4**, 2, 5 ) - No Swap\n*   ( 1, **4, 2**, 5 ) -> ( 1, **2, 4**, 5 ) - Swap\n*Result after Pass 2: [1, 2, 4, 5]* (4 is in place)\n\nAnd so on. The list is now sorted!", "visual": {"type": "static_text", "value": "[5,1,4,2] -> [1,4,2,5] -> [1,2,4,5]"}, "interactive_element": {"type": "button", "button_text": "Performance?"}, "audio_narration_url": null}}, {"id": "bubble_screen3_performance", "type": "lesson_screen", "order": 3, "estimatedTimeSeconds": 75, "content": {"headline": "Bubble Sort Performance", "body_md": "*   **Time Complexity:** O(n²) in the average and worst cases. This means it gets very slow as the list size (n) increases.\n*   **Space Complexity:** O(1) - it's an in-place sort (doesn't need much extra memory).\n\nWhile simple, its inefficiency makes it impractical for large datasets.", "visual": {"type": "giphy_search", "value": "slow turtle"}, "interactive_element": {"type": "multiple_choice_text", "question_text": "Bubble Sort is best for:", "options": [{"text": "Large, unsorted datasets.", "is_correct": false, "feedback": "No, O(n²) is too slow for large datasets."}, {"text": "Small datasets or nearly sorted lists.", "is_correct": true, "feedback": "Correct! It's simple and can be efficient if the list is almost sorted (optimized versions)."}, {"text": "Situations requiring minimal memory.", "is_correct": false, "feedback": "While it's O(1) space, its time complexity is usually the bigger concern. Other O(1) space sorts are faster."}]}, "audio_narration_url": null}}, {"id": "bubble_screen4_summary", "type": "lesson_screen", "order": 4, "estimatedTimeSeconds": 60, "content": {"headline": "Bubble Sort Recap", "body_md": "*   Simple comparison sort.\n*   Swaps adjacent elements.\n*   O(n²) time complexity, O(1) space.\n*   Good for educational purposes or very small lists.\n\nNext: A more efficient approach - Merge Sort!", "visual": {"type": "giphy_search", "value": "learning simple"}, "interactive_element": {"type": "button", "button_text": "Explore Merge Sort"}, "audio_narration_url": null}}]}, {"id": "merge-sort-quicksort", "title": "Merge Sort & Quick Sort: Divide and Conquer", "description": "Learn two efficient sorting algorithms based on the divide and conquer strategy.", "order": 3, "type": "interactive_lesson", "estimatedTimeMinutes": 15, "contentBlocks": [{"id": "divide_conquer_screen1_intro", "type": "lesson_screen", "order": 1, "estimatedTimeSeconds": 75, "content": {"headline": "Divide and Conquer Strategy", "body_md": "<PERSON>rge Sort and <PERSON> Sort use a powerful algorithmic paradigm called **Divide and Conquer**:\n\n1.  **Divide:** Break the problem into smaller subproblems.\n2.  **Conquer:** Solve the subproblems recursively. If small enough, solve directly.\n3.  **Combine:** Combine the solutions of subproblems to get the final solution.\n\nThis often leads to more efficient algorithms.", "visual": {"type": "giphy_search", "value": "breaking apart and joining"}, "interactive_element": {"type": "button", "button_text": "How Merge Sort Uses This"}, "audio_narration_url": null}}, {"id": "merge_screen2_how_it_works", "type": "lesson_screen", "order": 2, "estimatedTimeSeconds": 100, "content": {"headline": "<PERSON><PERSON>", "body_md": "1.  **Divide:** Split the list in half repeatedly until you have lists of size 1 (which are inherently sorted).\n2.  **Conquer (Merge):** Merge pairs of sorted sublists back together into larger sorted lists until the entire list is sorted.\n\nThe key step is the efficient merging of two sorted lists.", "visual": {"type": "unsplash_search", "value": "two streams merging"}, "interactive_element": {"type": "button", "button_text": "Merge <PERSON>rt Performance?"}, "audio_narration_url": null}}, {"id": "merge_screen3_performance", "type": "lesson_screen", "order": 3, "estimatedTimeSeconds": 90, "content": {"headline": "<PERSON><PERSON>", "body_md": "*   **Time Complexity:** O(n log n) in all cases (worst, average, best). This is very efficient!\n*   **Space Complexity:** O(n) because it typically requires extra space to store the merged sublists (not in-place).\n\nIt's stable (maintains relative order of equal elements) and reliable due to its consistent performance.", "visual": {"type": "giphy_search", "value": "consistent reliable"}, "interactive_element": {"type": "button", "button_text": "What about Quick Sort?"}, "audio_narration_url": null}}, {"id": "quick_screen4_how_it_works", "type": "lesson_screen", "order": 4, "estimatedTimeSeconds": 100, "content": {"headline": "Quick Sort", "body_md": "1.  **Divide (Partition):** Pick an element as a **pivot**. Rearrange the list so all elements smaller than the pivot come before it, and all elements larger come after it.\n2.  **Conquer:** Recursively apply Quick Sort to the sublists on either side of the pivot.\n3.  **Combine:** Trivial, as sorting is done in-place during partitioning.\n\nThe choice of pivot is crucial for performance.", "visual": {"type": "unsplash_search", "value": "pivot point balance"}, "interactive_element": {"type": "button", "button_text": "Quick Sort Performance?"}, "audio_narration_url": null}}, {"id": "quick_screen5_performance", "type": "lesson_screen", "order": 5, "estimatedTimeSeconds": 90, "content": {"headline": "Quick Sort Performance", "body_md": "*   **Time Complexity:**\n    *   Average Case: O(n log n) - very efficient.\n    *   Worst Case: O(n²) - if pivot choices are consistently bad (e.g., always smallest/largest element in a sorted list).\n*   **Space Complexity:** O(log n) on average (due to recursion stack), O(n) in worst case.\n\nOften faster in practice than Merge Sort due to better cache performance and being in-place (variants).", "visual": {"type": "giphy_search", "value": "fast lightning"}, "interactive_element": {"type": "multiple_choice_text", "question_text": "Quick Sort's worst-case time is:", "options": [{"text": "O(n log n)", "is_correct": false, "feedback": "That's its average and best case."}, {"text": "O(n²)", "is_correct": true, "feedback": "Correct. Bad pivot choices can degrade it to O(n²)."}, {"text": "O(n)", "is_correct": false, "feedback": "O(n) would be exceptionally fast for a comparison sort on general data."}]}, "audio_narration_url": null}}, {"id": "divide_conquer_screen6_summary", "type": "lesson_screen", "order": 6, "estimatedTimeSeconds": 60, "content": {"headline": "Merge Sort & Quick Sort Recap", "body_md": "*   Both use Divide and Conquer.\n*   **Merge Sort:** O(n log n) time always, O(n) space. Stable.\n*   **Quick Sort:** O(n log n) average time, O(n²) worst-case. O(log n) average space. Often faster in practice.\n\nThese are widely used efficient sorting algorithms!", "visual": {"type": "giphy_search", "value": "trophy winner"}, "interactive_element": {"type": "button", "button_text": "Module Test Time!"}, "audio_narration_url": null}}]}], "moduleTest": {"id": "sorting-algorithms-test", "title": "Module Test: Sorting Algorithms", "description": "Test your knowledge of common sorting algorithms.", "type": "module_test_interactive", "estimatedTimeMinutes": 8, "contentBlocks": [{"id": "sort_test_q1_bubble_complexity", "type": "question_screen", "order": 1, "estimatedTimeSeconds": 60, "content": {"headline": "Question 1: B<PERSON>ble Sort", "body_md": "What is the average-case time complexity of Bubble Sort?", "visual": {"type": "giphy_search", "value": "slow snail"}, "interactive_element": {"type": "multiple_choice_text", "question_text": "Bubble Sort average time?", "options": [{"text": "O(n)", "is_correct": false, "feedback": "O(n) would be very fast for a general comparison sort; Bubble Sort isn't this efficient on average."}, {"text": "O(n log n)", "is_correct": false, "feedback": "O(n log n) is characteristic of more efficient sorts like Merge Sort or Quick Sort."}, {"text": "O(n²)", "is_correct": true, "feedback": "Correct! Bubble Sort is O(n²) on average."}]}, "audio_narration_url": null}}, {"id": "sort_test_q2_merge_space", "type": "question_screen", "order": 2, "estimatedTimeSeconds": 75, "content": {"headline": "Question 2: <PERSON><PERSON>", "body_md": "What is the typical space complexity of <PERSON><PERSON>?", "visual": {"type": "unsplash_search", "value": "empty boxes"}, "interactive_element": {"type": "multiple_choice_text", "question_text": "Merge Sort space complexity?", "options": [{"text": "O(1)", "is_correct": false, "feedback": "Merge Sort is not typically an in-place sort; it requires extra space."}, {"text": "O(log n)", "is_correct": false, "feedback": "O(log n) space is more typical of Quick Sort's recursion stack on average."}, {"text": "O(n)", "is_correct": true, "feedback": "Correct! Merge Sort usually needs O(n) auxiliary space for merging."}]}, "audio_narration_url": null}}, {"id": "sort_test_q3_quick_pivot", "type": "question_screen", "order": 3, "estimatedTimeSeconds": 75, "content": {"headline": "Question 3: Quick Sort", "body_md": "The performance of Quick Sort heavily depends on the choice of:", "visual": {"type": "giphy_search", "value": "choice decision"}, "interactive_element": {"type": "multiple_choice_text", "question_text": "Quick Sort performance depends on?", "options": [{"text": "The initial order of elements only.", "is_correct": false, "feedback": "While initial order can affect it, the pivot choice is the more direct factor during the algorithm."}, {"text": "The pivot element.", "is_correct": true, "feedback": "Correct! A good pivot divides the list well; a bad pivot leads to worst-case performance."}, {"text": "The number of duplicate elements.", "is_correct": false, "feedback": "Duplicates can affect some sorting algorithms, but pivot choice is key for Quick Sort's structure."}]}, "audio_narration_url": null}}, {"id": "sort_test_q4_divide_conquer", "type": "question_screen", "order": 4, "estimatedTimeSeconds": 60, "content": {"headline": "Question 4: <PERSON><PERSON> and <PERSON>quer", "body_md": "Which of these sorting algorithms is NOT based on the 'Divide and Conquer' paradigm?", "visual": {"type": "unsplash_search", "value": "puzzle pieces separate"}, "interactive_element": {"type": "multiple_choice_text", "question_text": "NOT Divide and Conquer?", "options": [{"text": "<PERSON><PERSON>", "is_correct": false, "feedback": "Merge Sort is a classic example of Divide and Conquer."}, {"text": "Quick Sort", "is_correct": false, "feedback": "Quick Sort also uses the Divide and Conquer strategy."}, {"text": "Bubble Sort", "is_correct": true, "feedback": "Correct! Bubble Sort uses a simpler iterative swapping approach, not Divide and Conquer."}]}, "audio_narration_url": null}}]}}