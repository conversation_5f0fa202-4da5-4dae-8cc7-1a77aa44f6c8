import 'package:flutter/material.dart';
import '../../theme/widget_colors.dart';
import '../../theme/text_styles.dart';

/// A standardized error state widget for interactive widgets
/// Provides consistent error handling and user feedback
class ErrorState extends StatelessWidget {
  /// The height of the error container
  final double? height;
  
  /// The width of the error container
  final double? width;
  
  /// Error message to display
  final String message;
  
  /// Optional detailed error description
  final String? description;
  
  /// Callback for retry action
  final VoidCallback? onRetry;
  
  /// Callback for help action
  final VoidCallback? onHelp;
  
  /// Whether to show the retry button
  final bool showRetry;
  
  /// Whether to show the help button
  final bool showHelp;
  
  /// Category ID for color theming
  final String? categoryId;
  
  /// Error type for different styling
  final ErrorType errorType;

  const ErrorState({
    super.key,
    required this.message,
    this.height,
    this.width,
    this.description,
    this.onRetry,
    this.onHelp,
    this.showRetry = true,
    this.showHelp = false,
    this.categoryId,
    this.errorType = ErrorType.general,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      height: height ?? 200,
      width: width ?? double.infinity,
      decoration: BoxDecoration(
        color: _getBackgroundColor(),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: _getBorderColor(),
          width: 1,
        ),
      ),
      padding: const EdgeInsets.all(20),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          // Error icon
          Icon(
            _getErrorIcon(),
            size: 48,
            color: _getIconColor(),
          ),
          
          const SizedBox(height: 16),
          
          // Error message
          Text(
            message,
            style: WidgetTextStyles.headingSmall.copyWith(
              color: _getTextColor(),
            ),
            textAlign: TextAlign.center,
          ),
          
          // Error description
          if (description != null) ...[
            const SizedBox(height: 8),
            Text(
              description!,
              style: WidgetTextStyles.bodyMedium.copyWith(
                color: WidgetColors.textSecondary,
              ),
              textAlign: TextAlign.center,
            ),
          ],
          
          const SizedBox(height: 20),
          
          // Action buttons
          Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              if (showRetry && onRetry != null) ...[
                _buildActionButton(
                  text: 'Try Again',
                  icon: Icons.refresh,
                  onPressed: onRetry!,
                  isPrimary: true,
                ),
                if (showHelp && onHelp != null) const SizedBox(width: 12),
              ],
              
              if (showHelp && onHelp != null)
                _buildActionButton(
                  text: 'Get Help',
                  icon: Icons.help_outline,
                  onPressed: onHelp!,
                  isPrimary: false,
                ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildActionButton({
    required String text,
    required IconData icon,
    required VoidCallback onPressed,
    required bool isPrimary,
  }) {
    final categoryColor = categoryId != null
        ? WidgetColors.getCategoryColor(categoryId!)
        : WidgetColors.buttonPrimary;

    return ElevatedButton.icon(
      onPressed: onPressed,
      icon: Icon(icon, size: 18),
      label: Text(text),
      style: ElevatedButton.styleFrom(
        backgroundColor: isPrimary ? categoryColor : WidgetColors.buttonSecondary,
        foregroundColor: isPrimary ? Colors.white : WidgetColors.textPrimary,
        elevation: 2,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(8),
        ),
        padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
        textStyle: WidgetTextStyles.buttonMedium,
      ),
    );
  }

  Color _getBackgroundColor() {
    switch (errorType) {
      case ErrorType.validation:
        return WidgetColors.warningLight;
      case ErrorType.network:
        return WidgetColors.infoLight;
      case ErrorType.critical:
        return WidgetColors.errorLight;
      case ErrorType.general:
      default:
        return WidgetColors.backgroundSecondary;
    }
  }

  Color _getBorderColor() {
    switch (errorType) {
      case ErrorType.validation:
        return WidgetColors.warning.withOpacity(0.3);
      case ErrorType.network:
        return WidgetColors.info.withOpacity(0.3);
      case ErrorType.critical:
        return WidgetColors.error.withOpacity(0.3);
      case ErrorType.general:
      default:
        return WidgetColors.borderLight;
    }
  }

  IconData _getErrorIcon() {
    switch (errorType) {
      case ErrorType.validation:
        return Icons.warning_amber_outlined;
      case ErrorType.network:
        return Icons.wifi_off_outlined;
      case ErrorType.critical:
        return Icons.error_outline;
      case ErrorType.general:
      default:
        return Icons.info_outline;
    }
  }

  Color _getIconColor() {
    switch (errorType) {
      case ErrorType.validation:
        return WidgetColors.warning;
      case ErrorType.network:
        return WidgetColors.info;
      case ErrorType.critical:
        return WidgetColors.error;
      case ErrorType.general:
      default:
        return WidgetColors.textSecondary;
    }
  }

  Color _getTextColor() {
    switch (errorType) {
      case ErrorType.validation:
        return WidgetColors.warningDark;
      case ErrorType.network:
        return WidgetColors.infoDark;
      case ErrorType.critical:
        return WidgetColors.errorDark;
      case ErrorType.general:
      default:
        return WidgetColors.textPrimary;
    }
  }
}

/// Enum for different types of errors
enum ErrorType {
  general,
  validation,
  network,
  critical,
}

/// Compact error indicator for inline use
class CompactErrorIndicator extends StatelessWidget {
  final String message;
  final VoidCallback? onRetry;
  final ErrorType errorType;

  const CompactErrorIndicator({
    super.key,
    required this.message,
    this.onRetry,
    this.errorType = ErrorType.general,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: _getBackgroundColor(),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(
          color: _getBorderColor(),
          width: 1,
        ),
      ),
      child: Row(
        children: [
          Icon(
            _getErrorIcon(),
            size: 20,
            color: _getIconColor(),
          ),
          const SizedBox(width: 8),
          Expanded(
            child: Text(
              message,
              style: WidgetTextStyles.bodySmall.copyWith(
                color: _getTextColor(),
              ),
            ),
          ),
          if (onRetry != null) ...[
            const SizedBox(width: 8),
            IconButton(
              onPressed: onRetry,
              icon: const Icon(Icons.refresh, size: 18),
              padding: EdgeInsets.zero,
              constraints: const BoxConstraints(minWidth: 24, minHeight: 24),
            ),
          ],
        ],
      ),
    );
  }

  Color _getBackgroundColor() {
    switch (errorType) {
      case ErrorType.validation:
        return WidgetColors.warningLight;
      case ErrorType.network:
        return WidgetColors.infoLight;
      case ErrorType.critical:
        return WidgetColors.errorLight;
      case ErrorType.general:
      default:
        return WidgetColors.backgroundTertiary;
    }
  }

  Color _getBorderColor() {
    switch (errorType) {
      case ErrorType.validation:
        return WidgetColors.warning.withOpacity(0.3);
      case ErrorType.network:
        return WidgetColors.info.withOpacity(0.3);
      case ErrorType.critical:
        return WidgetColors.error.withOpacity(0.3);
      case ErrorType.general:
      default:
        return WidgetColors.borderLight;
    }
  }

  IconData _getErrorIcon() {
    switch (errorType) {
      case ErrorType.validation:
        return Icons.warning_amber_outlined;
      case ErrorType.network:
        return Icons.wifi_off_outlined;
      case ErrorType.critical:
        return Icons.error_outline;
      case ErrorType.general:
      default:
        return Icons.info_outline;
    }
  }

  Color _getIconColor() {
    switch (errorType) {
      case ErrorType.validation:
        return WidgetColors.warning;
      case ErrorType.network:
        return WidgetColors.info;
      case ErrorType.critical:
        return WidgetColors.error;
      case ErrorType.general:
      default:
        return WidgetColors.textSecondary;
    }
  }

  Color _getTextColor() {
    switch (errorType) {
      case ErrorType.validation:
        return WidgetColors.warningDark;
      case ErrorType.network:
        return WidgetColors.infoDark;
      case ErrorType.critical:
        return WidgetColors.errorDark;
      case ErrorType.general:
      default:
        return WidgetColors.textPrimary;
    }
  }
}
