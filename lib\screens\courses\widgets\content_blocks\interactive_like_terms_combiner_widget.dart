import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import '../../../../models/course_models.dart';

class InteractiveLikeTermsCombinerWidget extends StatefulWidget {
  final InteractiveLikeTermsCombinerElement likeTermsCombinerElement;
  final VoidCallback onNextAction;
  final bool isLastSlideInLesson;

  const InteractiveLikeTermsCombinerWidget({
    Key? key,
    required this.likeTermsCombinerElement,
    required this.onNextAction,
    this.isLastSlideInLesson = false,
  }) : super(key: key);

  @override
  State<InteractiveLikeTermsCombinerWidget> createState() =>
      _InteractiveLikeTermsCombinerWidgetState();
}

class _InteractiveLikeTermsCombinerWidgetState
    extends State<InteractiveLikeTermsCombinerWidget> {
  // Maps variable signature to a list of term IDs
  final Map<String, List<String>> _groupedTerms = {};
  final Set<String> _groupedTermIds = {};
  String _userAnswer = '';
  bool _isAnswered = false;
  bool _isCorrect = false;
  bool _showSolution = false;
  final TextEditingController _answerController = TextEditingController();

  @override
  void initState() {
    super.initState();
    // Initialize with empty groups for each unique variable signature
    final signatures =
        widget.likeTermsCombinerElement.expression_parts
            .map((part) => '${part.variable ?? ""}^${part.power}')
            .toSet();

    for (final signature in signatures) {
      _groupedTerms[signature] = [];
    }
  }

  @override
  void dispose() {
    _answerController.dispose();
    super.dispose();
  }

  void _handleTermDrag(ExpressionPart term, String signature) {
    final termId = term.term;

    // Remove the term from any existing group
    for (final group in _groupedTerms.keys) {
      _groupedTerms[group]?.remove(termId);
    }

    // Add to the new group
    _groupedTerms[signature]?.add(termId);
    _groupedTermIds.add(termId);

    setState(() {});
  }

  void _checkAnswer() {
    // Check if all terms have been grouped
    if (_groupedTermIds.length !=
        widget.likeTermsCombinerElement.expression_parts.length) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('Please group all terms first.'),
          backgroundColor: Colors.orange,
        ),
      );
      return;
    }

    // Check if the simplified expression is correct
    final userSimplified = _answerController.text.trim();
    final correctSimplified =
        widget.likeTermsCombinerElement.solution.simplified_expression;

    // Simple string comparison for now - could be enhanced with more sophisticated expression parsing
    final isCorrect =
        _normalizeExpression(userSimplified) ==
        _normalizeExpression(correctSimplified);

    setState(() {
      _isAnswered = true;
      _isCorrect = isCorrect;
    });

    // Provide haptic feedback
    HapticFeedback.mediumImpact();

    // Show feedback
    ScaffoldMessenger.of(context).clearSnackBars();
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(
          _isCorrect
              ? 'Correct! $correctSimplified is the simplified form.'
              : 'Not quite. Try again or check the solution.',
        ),
        backgroundColor: _isCorrect ? Colors.green : Colors.redAccent,
        duration: const Duration(seconds: 3),
      ),
    );
  }

  // Helper to normalize expressions for comparison (remove spaces, etc.)
  String _normalizeExpression(String expr) {
    return expr
        .replaceAll(' ', '')
        .replaceAll('+-', '-')
        .replaceAll('--', '+')
        .replaceAll('++', '+');
  }

  void _showSolutionSteps() {
    setState(() {
      _showSolution = true;
    });
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withAlpha(51),
            spreadRadius: 1,
            blurRadius: 3,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Prompt
          Text(
            widget.likeTermsCombinerElement.prompt,
            style: const TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
          ),
          const SizedBox(height: 16),

          // Original expression
          Container(
            width: double.infinity,
            padding: const EdgeInsets.symmetric(vertical: 12, horizontal: 16),
            decoration: BoxDecoration(
              color: Colors.blue.withAlpha(25),
              borderRadius: BorderRadius.circular(8),
            ),
            child: Text(
              'Expression: ${widget.likeTermsCombinerElement.expression_parts.map((p) => p.term).join(' ')}',
              style: const TextStyle(fontSize: 16, fontWeight: FontWeight.w500),
            ),
          ),
          const SizedBox(height: 24),

          // Term grouping area
          const Text(
            'Group like terms by dragging them to the appropriate category:',
            style: TextStyle(fontSize: 14, fontWeight: FontWeight.w500),
          ),
          const SizedBox(height: 12),

          // Draggable terms
          Wrap(
            spacing: 8,
            runSpacing: 8,
            children:
                widget.likeTermsCombinerElement.expression_parts.map((term) {
                  final isGrouped = _groupedTermIds.contains(term.term);
                  return Draggable<ExpressionPart>(
                    data: term,
                    feedback: Material(
                      elevation: 4,
                      borderRadius: BorderRadius.circular(8),
                      child: Container(
                        padding: const EdgeInsets.symmetric(
                          horizontal: 12,
                          vertical: 8,
                        ),
                        decoration: BoxDecoration(
                          color: Colors.amber,
                          borderRadius: BorderRadius.circular(8),
                        ),
                        child: Text(
                          term.term,
                          style: const TextStyle(
                            fontSize: 16,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                      ),
                    ),
                    childWhenDragging: Opacity(
                      opacity: 0.3,
                      child: Container(
                        padding: const EdgeInsets.symmetric(
                          horizontal: 12,
                          vertical: 8,
                        ),
                        decoration: BoxDecoration(
                          color: Colors.grey[200],
                          borderRadius: BorderRadius.circular(8),
                          border: Border.all(color: Colors.grey[400]!),
                        ),
                        child: Text(
                          term.term,
                          style: TextStyle(
                            fontSize: 16,
                            fontWeight: FontWeight.bold,
                            color: Colors.grey[600],
                          ),
                        ),
                      ),
                    ),
                    child: Opacity(
                      opacity: isGrouped ? 0.5 : 1.0,
                      child: Container(
                        padding: const EdgeInsets.symmetric(
                          horizontal: 12,
                          vertical: 8,
                        ),
                        decoration: BoxDecoration(
                          color:
                              isGrouped ? Colors.grey[200] : Colors.amber[100],
                          borderRadius: BorderRadius.circular(8),
                          border: Border.all(
                            color: isGrouped ? Colors.grey[400]! : Colors.amber,
                          ),
                        ),
                        child: Text(
                          term.term,
                          style: TextStyle(
                            fontSize: 16,
                            fontWeight: FontWeight.bold,
                            color:
                                isGrouped ? Colors.grey[600] : Colors.black87,
                          ),
                        ),
                      ),
                    ),
                  );
                }).toList(),
          ),
          const SizedBox(height: 24),

          // Group drop targets
          ...widget.likeTermsCombinerElement.expression_parts
              .map((part) => '${part.variable ?? ""}^${part.power}')
              .toSet()
              .map((signature) {
                String groupLabel;
                if (signature == '^0' || signature == 'null^0') {
                  groupLabel = 'Constants';
                } else if (signature.contains('^1')) {
                  groupLabel = '${signature.split('^')[0]} terms';
                } else {
                  groupLabel = signature.replaceAll('^', '²');
                  groupLabel = '$groupLabel terms';
                }

                return Padding(
                  padding: const EdgeInsets.only(bottom: 12),
                  child: DragTarget<ExpressionPart>(
                    onAcceptWithDetails:
                        (details) => _handleTermDrag(details.data, signature),
                    builder: (context, candidateData, rejectedData) {
                      return Container(
                        width: double.infinity,
                        padding: const EdgeInsets.all(12),
                        decoration: BoxDecoration(
                          color:
                              candidateData.isNotEmpty
                                  ? Colors.green.withAlpha(51)
                                  : Colors.grey.withAlpha(25),
                          borderRadius: BorderRadius.circular(8),
                          border: Border.all(
                            color:
                                candidateData.isNotEmpty
                                    ? Colors.green
                                    : Colors.grey[400]!,
                          ),
                        ),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              groupLabel,
                              style: TextStyle(
                                fontSize: 14,
                                fontWeight: FontWeight.bold,
                                color: Colors.grey[700],
                              ),
                            ),
                            const SizedBox(height: 8),
                            if (_groupedTerms[signature]!.isNotEmpty)
                              Wrap(
                                spacing: 8,
                                children:
                                    _groupedTerms[signature]!.map((termId) {
                                      final term = widget
                                          .likeTermsCombinerElement
                                          .expression_parts
                                          .firstWhere((p) => p.term == termId);
                                      return Container(
                                        padding: const EdgeInsets.symmetric(
                                          horizontal: 8,
                                          vertical: 4,
                                        ),
                                        decoration: BoxDecoration(
                                          color: Colors.amber.shade100,
                                          borderRadius: BorderRadius.circular(
                                            4,
                                          ),
                                        ),
                                        child: Text(term.term),
                                      );
                                    }).toList(),
                              )
                            else
                              Text(
                                'Drop like terms here',
                                style: TextStyle(
                                  fontSize: 14,
                                  fontStyle: FontStyle.italic,
                                  color: Colors.grey[500],
                                ),
                              ),
                          ],
                        ),
                      );
                    },
                  ),
                );
              }),

          const SizedBox(height: 24),

          // Simplified expression input
          TextField(
            controller: _answerController,
            decoration: InputDecoration(
              labelText: 'Simplified Expression',
              hintText: 'Enter the simplified expression',
              border: OutlineInputBorder(
                borderRadius: BorderRadius.circular(8),
              ),
              enabled: !_isAnswered,
            ),
            onChanged: (value) {
              setState(() {
                _userAnswer = value;
              });
            },
          ),
          const SizedBox(height: 16),

          // Action buttons
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              // Check answer button
              if (!_isAnswered)
                ElevatedButton.icon(
                  onPressed:
                      _answerController.text.isNotEmpty ? _checkAnswer : null,
                  icon: const Icon(Icons.check),
                  label: const Text('Check Answer'),
                ),

              // Show solution button
              if (!_showSolution && _isAnswered && !_isCorrect)
                ElevatedButton.icon(
                  onPressed: _showSolutionSteps,
                  icon: const Icon(Icons.lightbulb_outline),
                  label: const Text('Show Solution'),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Colors.amber,
                    foregroundColor: Colors.black87,
                  ),
                ),

              // Continue button
              if (_isAnswered)
                ElevatedButton.icon(
                  onPressed: widget.onNextAction,
                  icon: const Icon(Icons.arrow_forward),
                  label: Text(
                    widget.isLastSlideInLesson ? 'Complete Lesson' : 'Continue',
                  ),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: _isCorrect ? Colors.green : Colors.blue,
                    foregroundColor: Colors.white,
                  ),
                ),
            ],
          ),

          // Solution
          if (_showSolution) ...[
            const SizedBox(height: 24),
            Container(
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: Colors.amber.withAlpha(25),
                borderRadius: BorderRadius.circular(8),
                border: Border.all(color: Colors.amber.shade300),
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'Solution:',
                    style: TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.bold,
                      color: Colors.amber.shade800,
                    ),
                  ),
                  const SizedBox(height: 8),
                  Text(
                    'Simplified expression: ${widget.likeTermsCombinerElement.solution.simplified_expression}',
                    style: const TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                  const SizedBox(height: 12),
                  const Text(
                    'Steps:',
                    style: TextStyle(fontSize: 14, fontWeight: FontWeight.bold),
                  ),
                  ...widget.likeTermsCombinerElement.solution.steps.map(
                    (step) => Padding(
                      padding: const EdgeInsets.symmetric(vertical: 4),
                      child: Text(step),
                    ),
                  ),
                ],
              ),
            ),
          ],
        ],
      ),
    );
  }
}
