import 'package:flutter/material.dart';
import 'dart:math' as math;

/// Model class for a data set
class DataSet {
  final String title;
  final String description;
  final List<double> values;
  final String explanation;

  DataSet({
    required this.title,
    required this.description,
    required this.values,
    required this.explanation,
  });
}

/// A widget that allows users to perform statistical analysis on data sets
/// Users can calculate various statistical measures and visualize the results
class InteractiveStatisticalAnalysisCalculatorWidget extends StatefulWidget {
  /// The data for this widget
  final Map<String, dynamic> data;

  /// Optional callback when the widget state changes
  final Function(bool isCompleted)? onStateChanged;

  const InteractiveStatisticalAnalysisCalculatorWidget({
    super.key,
    required this.data,
    this.onStateChanged,
  });

  /// Factory constructor to create the widget from JSON data
  factory InteractiveStatisticalAnalysisCalculatorWidget.fromData(Map<String, dynamic> data) {
    return InteractiveStatisticalAnalysisCalculatorWidget(
      data: data,
    );
  }

  @override
  State<InteractiveStatisticalAnalysisCalculatorWidget> createState() => _InteractiveStatisticalAnalysisCalculatorWidgetState();
}

class _InteractiveStatisticalAnalysisCalculatorWidgetState extends State<InteractiveStatisticalAnalysisCalculatorWidget> {
  // Data sets
  late List<DataSet> _dataSets;
  late int _currentDataSetIndex;

  // UI state
  late bool _isCompleted;
  late bool _showExplanation;
  late String _customDataInput;

  late bool _isEditingCustomData;
  late String _customDataError;

  // Analysis options
  late List<String> _analysisTypes;
  late String _currentAnalysisType;

  // Colors
  late Color _primaryColor;
  late Color _secondaryColor;
  late Color _accentColor;
  late Color _backgroundColor;
  late Color _textColor;

  @override
  void initState() {
    super.initState();

    // Initialize colors
    _primaryColor = _getColorFromHex(widget.data['primaryColor'] ?? '#2196F3');
    _secondaryColor = _getColorFromHex(widget.data['secondaryColor'] ?? '#FF9800');
    _accentColor = _getColorFromHex(widget.data['accentColor'] ?? '#4CAF50');
    _backgroundColor = _getColorFromHex(widget.data['backgroundColor'] ?? '#FFFFFF');
    _textColor = _getColorFromHex(widget.data['textColor'] ?? '#212121');

    // Initialize data sets
    _initializeDataSets();

    // Initialize analysis types
    _analysisTypes = [
      'Descriptive Statistics',
      'Frequency Distribution',
      'Z-Scores',
      'Correlation',
    ];
    _currentAnalysisType = _analysisTypes[0];

    // Initialize state
    _currentDataSetIndex = 0;
    _isCompleted = false;
    _showExplanation = false;
    _customDataInput = '';
    _isEditingCustomData = false;
    _customDataError = '';
  }

  // Initialize the data sets
  void _initializeDataSets() {
    final List<dynamic> dataSetsData = widget.data['dataSets'] ?? _getDefaultDataSets();

    _dataSets = dataSetsData.map((dataSetData) {
      return DataSet(
        title: dataSetData['title'] ?? '',
        description: dataSetData['description'] ?? '',
        values: List<double>.from(dataSetData['values'] ?? []),
        explanation: dataSetData['explanation'] ?? '',
      );
    }).toList();

    // Add a custom data set option
    _dataSets.add(DataSet(
      title: 'Custom Data',
      description: 'Enter your own data for analysis.',
      values: [],
      explanation: 'Analyze your own custom data set.',
    ));
  }

  // Get default data sets if none are provided
  List<Map<String, dynamic>> _getDefaultDataSets() {
    return [
      {
        'title': 'Student Test Scores',
        'description': 'Test scores from a class of 30 students.',
        'values': [65, 70, 75, 80, 85, 90, 95, 60, 65, 70, 75, 80, 85, 90, 75, 80, 85, 90, 95, 100, 55, 60, 65, 70, 75, 80, 85, 90, 95, 100],
        'explanation': 'This data set represents test scores from a class of 30 students. The scores range from 55 to 100, with a mean of approximately 80 and a standard deviation of about 12. The distribution is slightly negatively skewed, indicating that more students scored above the mean than below it.',
      },
      {
        'title': 'Heights (cm)',
        'description': 'Heights of 20 adults in centimeters.',
        'values': [165, 170, 175, 180, 185, 190, 160, 165, 170, 175, 180, 185, 175, 180, 185, 190, 165, 170, 175, 180],
        'explanation': 'This data set represents the heights of 20 adults in centimeters. The heights range from 160 cm to 190 cm, with a mean of approximately 175 cm and a standard deviation of about 8 cm. The distribution is approximately normal, which is typical for human height data.',
      },
      {
        'title': 'Monthly Expenses (USD)',
        'description': 'Monthly expenses in dollars for 15 households.',
        'values': [1200, 1500, 1800, 2100, 2400, 2700, 3000, 1350, 1650, 1950, 2250, 2550, 2850, 3150, 3450],
        'explanation': 'This data set represents monthly expenses in dollars for 15 households. The expenses range from 1,200 to 3,450 USD, with a mean of approximately 2,200 USD and a standard deviation of about 700 USD. The distribution is approximately uniform, indicating that the households have a wide range of spending habits.',
      },
      {
        'title': 'Reaction Times (ms)',
        'description': 'Reaction times in milliseconds for 25 participants.',
        'values': [220, 240, 260, 280, 300, 320, 340, 230, 250, 270, 290, 310, 330, 350, 225, 245, 265, 285, 305, 325, 345, 235, 255, 275, 295],
        'explanation': 'This data set represents reaction times in milliseconds for 25 participants. The reaction times range from 220 ms to 350 ms, with a mean of approximately 285 ms and a standard deviation of about 40 ms. The distribution is approximately normal, which is typical for reaction time data.',
      },
    ];
  }

  // Get color from hex string
  Color _getColorFromHex(String hexColor) {
    hexColor = hexColor.replaceAll('#', '');
    if (hexColor.length == 6) {
      hexColor = 'FF$hexColor';
    }
    return Color(int.parse(hexColor, radix: 16));
  }

  // Go to the next data set
  void _nextDataSet() {
    if (_currentDataSetIndex < _dataSets.length - 1) {
      setState(() {
        _currentDataSetIndex++;
        _showExplanation = false;
        _isEditingCustomData = _currentDataSetIndex == _dataSets.length - 1;
      });
    } else if (!_isCompleted) {
      setState(() {
        _isCompleted = true;
      });
      widget.onStateChanged?.call(true);
    }
  }

  // Go to the previous data set
  void _previousDataSet() {
    if (_currentDataSetIndex > 0) {
      setState(() {
        _currentDataSetIndex--;
        _showExplanation = false;
        _isEditingCustomData = false;
      });
    }
  }

  // Change the analysis type
  void _changeAnalysisType(String type) {
    setState(() {
      _currentAnalysisType = type;
    });
  }

  // Toggle explanation visibility
  void _toggleExplanation() {
    setState(() {
      _showExplanation = !_showExplanation;
    });
  }

  // Reset the widget
  void _resetWidget() {
    setState(() {
      _currentDataSetIndex = 0;
      _currentAnalysisType = _analysisTypes[0];
      _isCompleted = false;
      _showExplanation = false;
      _isEditingCustomData = false;
      _customDataInput = '';
      _customDataError = '';
    });
  }

  // Parse custom data input
  void _parseCustomData() {
    try {
      final values = _customDataInput
          .split(',')
          .map((s) => s.trim())
          .where((s) => s.isNotEmpty)
          .map((s) => double.parse(s))
          .toList();

      if (values.isEmpty) {
        setState(() {
          _customDataError = 'Please enter at least one value.';
        });
        return;
      }

      setState(() {
        _dataSets[_dataSets.length - 1] = DataSet(
          title: 'Custom Data',
          description: 'Your custom data set with ${values.length} values.',
          values: values,
          explanation: 'This is your custom data set with ${values.length} values.',
        );
        _isEditingCustomData = false;
        _customDataError = '';
      });
    } catch (e) {
      setState(() {
        _customDataError = 'Invalid input. Please enter comma-separated numbers.';
      });
    }
  }

  // Calculate descriptive statistics for the current data set
  Map<String, double> _calculateDescriptiveStatistics(List<double> values) {
    if (values.isEmpty) {
      return {
        'count': 0,
        'min': 0,
        'max': 0,
        'range': 0,
        'mean': 0,
        'median': 0,
        'mode': 0,
        'variance': 0,
        'stdDev': 0,
        'skewness': 0,
        'kurtosis': 0,
      };
    }

    // Sort values for easier calculations
    final sortedValues = List<double>.from(values)..sort();

    // Count
    final count = values.length.toDouble();

    // Min, max, range
    final min = sortedValues.first;
    final max = sortedValues.last;
    final range = max - min;

    // Mean
    final sum = values.reduce((a, b) => a + b);
    final mean = sum / count;

    // Median
    double median;
    if (count % 2 == 0) {
      median = (sortedValues[(count ~/ 2 - 1)] + sortedValues[(count ~/ 2)]) / 2;
    } else {
      median = sortedValues[(count ~/ 2)];
    }

    // Mode (most frequent value)
    final Map<double, int> frequency = {};
    for (final value in values) {
      frequency[value] = (frequency[value] ?? 0) + 1;
    }
    int maxFrequency = 0;
    double mode = 0;
    frequency.forEach((value, freq) {
      if (freq > maxFrequency) {
        maxFrequency = freq;
        mode = value;
      }
    });

    // Variance and standard deviation
    double sumSquaredDiff = 0;
    for (final value in values) {
      sumSquaredDiff += math.pow(value - mean, 2);
    }
    final variance = sumSquaredDiff / count;
    final stdDev = math.sqrt(variance);

    // Skewness
    double sumCubedDiff = 0;
    for (final value in values) {
      sumCubedDiff += math.pow(value - mean, 3);
    }
    final skewness = count > 0 ? (sumCubedDiff / count) / math.pow(stdDev, 3) : 0.0;

    // Kurtosis
    double sumQuartedDiff = 0;
    for (final value in values) {
      sumQuartedDiff += math.pow(value - mean, 4);
    }
    final kurtosis = count > 0 ? (sumQuartedDiff / count) / math.pow(variance, 2) - 3 : 0.0;

    return {
      'count': count,
      'min': min,
      'max': max,
      'range': range,
      'mean': mean,
      'median': median,
      'mode': mode,
      'variance': variance,
      'stdDev': stdDev,
      'skewness': skewness,
      'kurtosis': kurtosis,
    };
  }

  // Build the analysis results based on the selected analysis type
  Widget _buildAnalysisResults(List<double> values) {
    if (values.isEmpty) {
      return Center(
        child: Text(
          'No data available for analysis.',
          style: TextStyle(
            fontSize: 16,
            fontStyle: FontStyle.italic,
            color: _textColor,
          ),
        ),
      );
    }

    switch (_currentAnalysisType) {
      case 'Descriptive Statistics':
        return _buildDescriptiveStatistics(values);
      case 'Frequency Distribution':
        return _buildFrequencyDistribution(values);
      case 'Z-Scores':
        return _buildZScores(values);
      case 'Correlation':
        return _buildCorrelation();
      default:
        return _buildDescriptiveStatistics(values);
    }
  }

  // Build descriptive statistics display
  Widget _buildDescriptiveStatistics(List<double> values) {
    final stats = _calculateDescriptiveStatistics(values);

    return SingleChildScrollView(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Descriptive Statistics',
            style: TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.bold,
              color: _primaryColor,
            ),
          ),
          const SizedBox(height: 16),

          // Basic statistics
          _buildStatisticRow('Count', stats['count']!.toStringAsFixed(0)),
          _buildStatisticRow('Minimum', stats['min']!.toStringAsFixed(2)),
          _buildStatisticRow('Maximum', stats['max']!.toStringAsFixed(2)),
          _buildStatisticRow('Range', stats['range']!.toStringAsFixed(2)),

          const Divider(),

          // Central tendency
          Text(
            'Central Tendency',
            style: TextStyle(
              fontSize: 14,
              fontWeight: FontWeight.bold,
              color: _secondaryColor,
            ),
          ),
          const SizedBox(height: 8),
          _buildStatisticRow('Mean', stats['mean']!.toStringAsFixed(2)),
          _buildStatisticRow('Median', stats['median']!.toStringAsFixed(2)),
          _buildStatisticRow('Mode', stats['mode']!.toStringAsFixed(2)),

          const Divider(),

          // Dispersion
          Text(
            'Dispersion',
            style: TextStyle(
              fontSize: 14,
              fontWeight: FontWeight.bold,
              color: _secondaryColor,
            ),
          ),
          const SizedBox(height: 8),
          _buildStatisticRow('Variance', stats['variance']!.toStringAsFixed(2)),
          _buildStatisticRow('Standard Deviation', stats['stdDev']!.toStringAsFixed(2)),

          const Divider(),

          // Shape
          Text(
            'Distribution Shape',
            style: TextStyle(
              fontSize: 14,
              fontWeight: FontWeight.bold,
              color: _secondaryColor,
            ),
          ),
          const SizedBox(height: 8),
          _buildStatisticRow('Skewness', stats['skewness']!.toStringAsFixed(2)),
          _buildStatisticRow('Kurtosis', stats['kurtosis']!.toStringAsFixed(2)),
        ],
      ),
    );
  }

  // Build frequency distribution display
  Widget _buildFrequencyDistribution(List<double> values) {
    final bins = _calculateFrequencyDistribution(values);

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Frequency Distribution',
          style: TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.bold,
            color: _primaryColor,
          ),
        ),
        const SizedBox(height: 16),

        // Table header
        Row(
          children: [
            Expanded(
              flex: 2,
              child: Text(
                'Bin Range',
                style: TextStyle(
                  fontWeight: FontWeight.bold,
                  color: _textColor,
                ),
              ),
            ),
            Expanded(
              child: Text(
                'Frequency',
                style: TextStyle(
                  fontWeight: FontWeight.bold,
                  color: _textColor,
                ),
                textAlign: TextAlign.center,
              ),
            ),
            Expanded(
              child: Text(
                'Relative',
                style: TextStyle(
                  fontWeight: FontWeight.bold,
                  color: _textColor,
                ),
                textAlign: TextAlign.center,
              ),
            ),
          ],
        ),

        const Divider(),

        // Table body
        Expanded(
          child: ListView.builder(
            itemCount: bins.length,
            itemBuilder: (context, index) {
              final bin = bins[index];
              return Row(
                children: [
                  Expanded(
                    flex: 2,
                    child: Text(
                      '${bin['lowerBound'].toStringAsFixed(1)} - ${bin['upperBound'].toStringAsFixed(1)}',
                      style: TextStyle(
                        color: _textColor,
                      ),
                    ),
                  ),
                  Expanded(
                    child: Text(
                      '${bin['frequency']}',
                      style: TextStyle(
                        color: _textColor,
                      ),
                      textAlign: TextAlign.center,
                    ),
                  ),
                  Expanded(
                    child: Text(
                      '${(bin['relativeFrequency'] * 100).toStringAsFixed(1)}%',
                      style: TextStyle(
                        color: _textColor,
                      ),
                      textAlign: TextAlign.center,
                    ),
                  ),
                ],
              );
            },
          ),
        ),
      ],
    );
  }

  // Build Z-scores display
  Widget _buildZScores(List<double> values) {
    final zScores = _calculateZScores(values);

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Z-Scores',
          style: TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.bold,
            color: _primaryColor,
          ),
        ),
        const SizedBox(height: 8),
        Text(
          'Z-scores show how many standard deviations a value is from the mean.',
          style: TextStyle(
            fontSize: 14,
            fontStyle: FontStyle.italic,
            color: _textColor,
          ),
        ),
        const SizedBox(height: 16),

        // Table header
        Row(
          children: [
            Expanded(
              child: Text(
                'Index',
                style: TextStyle(
                  fontWeight: FontWeight.bold,
                  color: _textColor,
                ),
                textAlign: TextAlign.center,
              ),
            ),
            Expanded(
              child: Text(
                'Value',
                style: TextStyle(
                  fontWeight: FontWeight.bold,
                  color: _textColor,
                ),
                textAlign: TextAlign.center,
              ),
            ),
            Expanded(
              child: Text(
                'Z-Score',
                style: TextStyle(
                  fontWeight: FontWeight.bold,
                  color: _textColor,
                ),
                textAlign: TextAlign.center,
              ),
            ),
          ],
        ),

        const Divider(),

        // Table body
        Expanded(
          child: ListView.builder(
            itemCount: zScores.length,
            itemBuilder: (context, index) {
              final zScore = zScores[index];
              return Row(
                children: [
                  Expanded(
                    child: Text(
                      '${zScore['index']}',
                      style: TextStyle(
                        color: _textColor,
                      ),
                      textAlign: TextAlign.center,
                    ),
                  ),
                  Expanded(
                    child: Text(
                      '${zScore['value'].toStringAsFixed(1)}',
                      style: TextStyle(
                        color: _textColor,
                      ),
                      textAlign: TextAlign.center,
                    ),
                  ),
                  Expanded(
                    child: Text(
                      '${zScore['zScore'].toStringAsFixed(2)}',
                      style: TextStyle(
                        color: _getZScoreColor(zScore['zScore']),
                        fontWeight: (zScore['zScore'] as double).abs() > 2 ? FontWeight.bold : FontWeight.normal,
                      ),
                      textAlign: TextAlign.center,
                    ),
                  ),
                ],
              );
            },
          ),
        ),
      ],
    );
  }

  // Get color for Z-score based on its value
  Color _getZScoreColor(double zScore) {
    if (zScore < -2) return Colors.red;
    if (zScore < -1) return Colors.orange;
    if (zScore < 1) return _textColor;
    if (zScore < 2) return Colors.lightBlue;
    return Colors.blue;
  }

  // Build correlation display
  Widget _buildCorrelation() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.scatter_plot,
            size: 48,
            color: _primaryColor,
          ),
          const SizedBox(height: 16),
          Text(
            'Correlation Analysis',
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
              color: _primaryColor,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            'To perform correlation analysis, you need to select two data sets.',
            style: TextStyle(
              fontSize: 16,
              color: _textColor,
            ),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 24),
          Text(
            'This feature will be available in a future update.',
            style: TextStyle(
              fontSize: 14,
              fontStyle: FontStyle.italic,
              color: _textColor.withAlpha(150),
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  // Build a row for displaying a statistic
  Widget _buildStatisticRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4),
      child: Row(
        children: [
          Expanded(
            flex: 2,
            child: Text(
              label,
              style: TextStyle(
                color: _textColor,
              ),
            ),
          ),
          Expanded(
            child: Text(
              value,
              style: TextStyle(
                fontWeight: FontWeight.bold,
                color: _textColor,
              ),
              textAlign: TextAlign.right,
            ),
          ),
        ],
      ),
    );
  }

  // Calculate frequency distribution
  List<Map<String, dynamic>> _calculateFrequencyDistribution(List<double> values) {
    if (values.isEmpty) {
      return [];
    }

    // Determine number of bins (Sturges' formula)
    final n = values.length;
    final k = math.max(5, (1 + 3.322 * math.log(n) / math.ln10).round());

    // Calculate bin width
    final min = values.reduce(math.min);
    final max = values.reduce(math.max);
    final binWidth = (max - min) / k;

    // Create bins
    final List<Map<String, dynamic>> bins = [];
    for (int i = 0; i < k; i++) {
      final lowerBound = min + i * binWidth;
      final upperBound = min + (i + 1) * binWidth;
      bins.add({
        'lowerBound': lowerBound,
        'upperBound': upperBound,
        'frequency': 0,
        'relativeFrequency': 0.0,
      });
    }

    // Count frequencies
    for (final value in values) {
      for (int i = 0; i < bins.length; i++) {
        if (value >= bins[i]['lowerBound'] &&
            (value < bins[i]['upperBound'] || (i == bins.length - 1 && value == bins[i]['upperBound']))) {
          bins[i]['frequency'] = (bins[i]['frequency'] as int) + 1;
          break;
        }
      }
    }

    // Calculate relative frequencies
    for (final bin in bins) {
      bin['relativeFrequency'] = (bin['frequency'] as int) / n;
    }

    return bins;
  }

  // Calculate Z-scores
  List<Map<String, dynamic>> _calculateZScores(List<double> values) {
    if (values.isEmpty) {
      return [];
    }

    // Calculate mean and standard deviation
    final stats = _calculateDescriptiveStatistics(values);
    final mean = stats['mean']!;
    final stdDev = stats['stdDev']!;

    // Calculate Z-scores
    final List<Map<String, dynamic>> zScores = [];
    for (int i = 0; i < values.length; i++) {
      final value = values[i];
      final zScore = stdDev > 0 ? (value - mean) / stdDev : 0;
      zScores.add({
        'index': i + 1,
        'value': value,
        'zScore': zScore,
      });
    }

    return zScores;
  }



  @override
  Widget build(BuildContext context) {
    final dataSet = _dataSets[_currentDataSetIndex];
    final values = dataSet.values;

    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: _backgroundColor,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withAlpha(50),
            spreadRadius: 1,
            blurRadius: 3,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Title
          Text(
            widget.data['title'] ?? 'Statistical Analysis Calculator',
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
              color: _textColor,
            ),
          ),

          const SizedBox(height: 8),

          // Data set navigation
          Row(
            children: [
              Text(
                'Dataset ${_currentDataSetIndex + 1} of ${_dataSets.length}: ${dataSet.title}',
                style: TextStyle(
                  fontSize: 14,
                  fontWeight: FontWeight.bold,
                  color: _primaryColor,
                ),
              ),
              const Spacer(),
              IconButton(
                icon: Icon(_showExplanation ? Icons.info_outline : Icons.info),
                onPressed: _toggleExplanation,
                tooltip: _showExplanation ? 'Hide Explanation' : 'Show Explanation',
                color: _secondaryColor,
              ),
            ],
          ),

          const SizedBox(height: 8),

          // Data set description
          Text(
            dataSet.description,
            style: TextStyle(
              fontSize: 14,
              fontStyle: FontStyle.italic,
              color: _textColor.withAlpha(180),
            ),
          ),

          const SizedBox(height: 16),

          // Custom data input
          if (_isEditingCustomData) ...[
            TextField(
              decoration: InputDecoration(
                labelText: 'Enter comma-separated values',
                hintText: 'e.g., 10, 20, 30, 40, 50',
                errorText: _customDataError.isNotEmpty ? _customDataError : null,
                border: OutlineInputBorder(),
              ),
              onChanged: (value) {
                setState(() {
                  _customDataInput = value;
                });
              },
              maxLines: 3,
            ),
            const SizedBox(height: 8),
            ElevatedButton(
              onPressed: _parseCustomData,
              style: ElevatedButton.styleFrom(
                backgroundColor: _accentColor,
                foregroundColor: Colors.white,
              ),
              child: const Text('Analyze Data'),
            ),
          ] else ...[
            // Analysis type selector
            Row(
              children: [
                Text(
                  'Analysis Type:',
                  style: TextStyle(
                    fontSize: 14,
                    fontWeight: FontWeight.bold,
                    color: _textColor,
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: SingleChildScrollView(
                    scrollDirection: Axis.horizontal,
                    child: Row(
                      children: _analysisTypes.map((type) {
                        return Padding(
                          padding: const EdgeInsets.only(right: 8),
                          child: ChoiceChip(
                            label: Text(type),
                            selected: _currentAnalysisType == type,
                            onSelected: (selected) {
                              if (selected) _changeAnalysisType(type);
                            },
                          ),
                        );
                      }).toList(),
                    ),
                  ),
                ),
              ],
            ),

            const SizedBox(height: 16),

            // Analysis results
            Expanded(
              child: _buildAnalysisResults(values),
            ),
          ],

          // Explanation
          if (_showExplanation && !_isEditingCustomData)
            Container(
              padding: const EdgeInsets.all(12),
              margin: const EdgeInsets.only(bottom: 16),
              decoration: BoxDecoration(
                color: _secondaryColor.withAlpha(25),
                borderRadius: BorderRadius.circular(8),
                border: Border.all(color: _secondaryColor.withAlpha(75)),
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'Explanation:',
                    style: TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.bold,
                      color: _secondaryColor,
                    ),
                  ),
                  const SizedBox(height: 8),
                  Text(
                    dataSet.explanation,
                    style: TextStyle(
                      fontSize: 14,
                      color: _textColor,
                    ),
                  ),
                ],
              ),
            ),

          // Navigation buttons
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              ElevatedButton(
                onPressed: _currentDataSetIndex > 0 ? _previousDataSet : null,
                style: ElevatedButton.styleFrom(
                  backgroundColor: _secondaryColor,
                  foregroundColor: Colors.white,
                ),
                child: const Text('Previous'),
              ),
              ElevatedButton(
                onPressed: _currentDataSetIndex < _dataSets.length - 1 ? _nextDataSet : (_isCompleted ? _resetWidget : _nextDataSet),
                style: ElevatedButton.styleFrom(
                  backgroundColor: _primaryColor,
                  foregroundColor: Colors.white,
                ),
                child: Text(_currentDataSetIndex < _dataSets.length - 1 ? 'Next' : (_isCompleted ? 'Restart' : 'Complete')),
              ),
            ],
          ),

          // Widget name tag at the bottom
          if (widget.data['showNameTag'] ?? true)
            Padding(
              padding: const EdgeInsets.only(top: 8),
              child: Text(
                'InteractiveStatisticalAnalysisCalculatorWidget',
                style: TextStyle(
                  color: Colors.grey[600],
                  fontSize: 12,
                  fontStyle: FontStyle.italic,
                ),
              ),
            ),
        ],
      ),
    );
  }
}
