{"id": "language-of-variables", "title": "The Language of Variables", "description": "Introduce the power of symbols to represent unknowns and build mathematical expressions.", "order": 1, "lessons": [{"id": "lov-l1-what-is-a-variable", "title": "What is a Variable?", "description": "Uncover the idea of using letters to stand for numbers and represent changing quantities or unknowns.", "order": 1, "type": "interactive_lesson", "estimatedTimeMinutes": 8, "contentBlocks": [{"id": "lov-l1-s1", "type": "lesson_screen", "order": 1, "estimatedTimeSeconds": 30, "content": {"headline": "Welcome to Algebra!", "body_md": "Welcome to the fascinating world of algebra! Ever wondered how mathematicians describe patterns, solve puzzles with unknown pieces, or even predict future events? One of their most powerful tools is the **variable**. At its heart, algebra is a language of symbols, and variables are its core vocabulary.", "visual": {"type": "giphy_search", "value": "algebra magic thinking"}, "interactive_element": {"type": "button", "text": "Let's Discover Variables!", "action": "next_screen"}}}, {"id": "lov-l1-s2", "type": "lesson_screen", "order": 2, "estimatedTimeSeconds": 60, "content": {"headline": "What is a Variable?", "body_md": "So, what exactly *is* a variable? \n\nImagine you have a magic box. This box can hold different numbers at different times, or it might hold a number you haven't figured out yet. That's essentially what a variable is: **a letter or symbol that stands for a value that can change, or for a value that is currently unknown.** It's a placeholder, a container for a quantity that isn't fixed.", "visual": {"type": "local_asset", "value": "assets/images/algebra/variable_container_analogy.svg", "alt_text": "Illustration of a box labeled 'x' that can hold different numbers like 3, 7, or -2, emphasizing its placeholder nature."}, "interactive_element": {"type": "button", "text": "Got it, what's next?", "action": "next_screen"}}}, {"id": "lov-l1-s2b-check-understanding", "type": "lesson_screen", "order": 3, "estimatedTimeSeconds": 45, "content": {"headline": "Quick Check!", "body_md": "Based on what you just learned, which of these best describes a variable?", "interactive_element": {"type": "multiple_choice_text", "options": [{"id": "s2bopt1", "text": "A specific, unchanging number like 5.", "is_correct": false, "feedback_incorrect": "That's a constant! Variables are more flexible."}, {"id": "s2bopt2", "text": "A symbol that represents a value that can change or is unknown.", "is_correct": true, "feedback_correct": "Exactly! Like our magic box, it can hold different things.", "feedback_incorrect": "Think about the 'magic box' analogy."}, {"id": "s2bopt3", "text": "An operation like addition or subtraction.", "is_correct": false, "feedback_incorrect": "Those are operations we perform *with* numbers and variables."}], "action_button_text": "See Real Examples"}}}, {"id": "lov-l1-s3", "type": "lesson_screen", "order": 4, "estimatedTimeSeconds": 70, "content": {"headline": "Variables in the Wild", "body_md": "Let's look at some real-world examples:\n\n- If a bakery sells 'c' cookies each day, 'c' is a variable because the number of cookies sold can change. Monday 'c' might be 50, Tuesday it might be 120.\n- The temperature 't' outside changes throughout the day. 't' is a variable.\n- If you're saving money for a new game that costs 'p' dollars, 'p' is a variable representing an unknown amount until you check the price tag.\n\nVariables allow us to talk about these changing or unknown quantities in a general way.", "visual": {"type": "giphy_search", "value": "everyday life examples"}, "hook": "Ready to see how you can play with variables?", "interactive_element": {"type": "button", "text": "Can I try this out?", "action": "next_screen"}}}, {"id": "lov-l1-s4", "type": "lesson_screen", "order": 5, "estimatedTimeSeconds": 120, "content": {"headline": "Explore Variable 'n'", "body_md": "Let's explore this idea with an interactive example! A variable can represent different values. Select a scenario, think about what 'n' could be, enter a possible value, and see what it means in that context!", "visual": {"type": "static_text", "value": "Interactive: Variable Value Explorer Below"}, "interactive_element": {"type": "variable_value_explorer", "variable_name": "n", "overall_prompt": "Select a scenario, think about what 'n' could be, enter a possible value, and see what it means in that context!", "scenarios": [{"id": "s1", "icon_path": "assets/icons/algebra/students_classroom.svg", "scenario_title": "Students in a Classroom", "context_text_before_input": "Let 'n' be the number of students in a classroom. If today, n = ", "input_type": "number", "initial_value": 25, "placeholder_text": "e.g., 30", "target_value": 28, "feedback_on_target": "Exactly! If n = 28, there are 28 students. This number can change if new students join or some are absent!", "feedback_general_template": "Okay, if n = {value}, that means there are {value} students today. Variables help us represent quantities that aren't always the same!"}, {"id": "s2", "icon_path": "assets/icons/algebra/temperature_celsius.svg", "scenario_title": "Temperature Outside", "context_text_before_input": "Let 'n' be the temperature in Celsius. This morning, n = ", "input_type": "number", "initial_value": 18, "placeholder_text": "e.g., 22", "target_value": 15, "feedback_on_target": "Spot on! If n = 15, it's 15°C. It might be warmer later, or colder tomorrow – 'n' changes!", "feedback_general_template": "Alright, if n = {value}, the temperature is {value}°C. Variables are perfect for tracking conditions that fluctuate!"}, {"id": "s3", "icon_path": "assets/icons/algebra/movie_ticket.svg", "scenario_title": "Movie Ticket Price", "context_text_before_input": "Let 'n' be the price of a movie ticket in dollars. For a matinee show, n = ", "input_type": "number", "initial_value": 8, "placeholder_text": "e.g., 12.50", "target_value": 10, "feedback_on_target": "Correct! If n = 10, the ticket costs $10. Evening shows or 3D movies might have a different price, so 'n' can vary!", "feedback_general_template": "Got it, if n = {value}, the ticket is ${value}. Variables are great for representing prices that can change based on different factors!"}], "check_button_text": "Check Value", "action_button_text": "What about naming them?"}, "hook": "Pretty cool, right? Variables are like chameleons!"}}, {"id": "lov-l1-s5", "type": "lesson_screen", "order": 6, "estimatedTimeSeconds": 60, "content": {"headline": "Naming Your Variables", "body_md": "We commonly use letters from the alphabet like x, y, z, a, b, c, n, or t as variables. Sometimes, we might even use Greek letters like θ (theta) or Δ (delta). The specific letter isn't as important as the idea that it represents a quantity that's not fixed or is yet to be determined. The choice of letter often relates to what it represents (e.g., 't' for time, 'h' for height).", "visual": {"type": "giphy_search", "value": "alphabet letters thinking"}, "hook": "So many choices! But the power isn't in the name, it's in the idea.", "interactive_element": {"type": "button", "text": "Okay, but why are they SO important?", "action": "next_screen"}}}, {"id": "lov-l1-s6", "type": "lesson_screen", "order": 7, "estimatedTimeSeconds": 70, "content": {"headline": "The Utility of Variables", "body_md": "Why are variables so incredibly useful? They are the building blocks of algebra and allow us to:\n\n- **Write general rules and formulas:** Think of the formula for the area of a rectangle, `Area = length × width`. By using variables `length` and `width`, this single formula works for *any* rectangle, no matter its specific dimensions.\n- **Solve for unknowns:** If you know the area and length, you can use the formula to find the unknown width.\n- **Model real-world situations:** Scientists, engineers, and economists use variables to create mathematical models that describe complex systems and make predictions.\n\nAs we continue, you'll see just how powerful this simple idea of a variable can be!", "visual": {"type": "unsplash_search", "value": "architect blueprint tools"}, "hook": "You're now equipped with a fundamental tool of mathematics!", "interactive_element": {"type": "button", "text": "I'm ready to build with variables!", "action": "next_lesson"}}}]}, {"id": "lov-l2-building-expressions", "title": "Building Expressions", "description": "Learn to combine numbers, variables, and operations to form mathematical statements, translating words into algebra.", "order": 2, "type": "interactive_lesson", "estimatedTimeMinutes": 9, "contentBlocks": [{"id": "lov-l2-s1", "type": "lesson_screen", "order": 1, "estimatedTimeSeconds": 60, "content": {"headline": "From Variables to Expressions", "body_md": "Great! You now understand that variables are like flexible containers for numbers. But what can we *do* with them? This is where **mathematical expressions** come into play. An expression is a meaningful combination of numbers, variables, and operation signs (like addition `+`, subtraction `-`, multiplication `*` or `×`, and division `/` or `÷`).\n\nThink of expressions as phrases in the language of mathematics. They don't make a complete statement with an equals sign (that's an **equation**, which we'll explore soon!), but they represent a single value or quantity. For example, if you buy 3 apples that cost 'a' cents each, the expression `3a` represents the total cost.", "visual": {"type": "giphy_search", "value": "math symbols"}, "hook": "Think of it as learning the grammar of math!", "interactive_element": {"type": "button", "text": "Show me the building blocks!", "action": "next_screen"}}}, {"id": "lov-l2-s2", "type": "lesson_screen", "order": 2, "estimatedTimeSeconds": 70, "content": {"headline": "Common Expression Types", "body_md": "Let's look at some common ways to build expressions:\n\n- **Addition:** `x + 7` (read as 'x plus 7', or '7 more than x', or 'the sum of x and 7')\n- **Subtraction:** `10 - a` (read as '10 minus a', or 'a less than 10', or 'the difference between 10 and a')\n- **Multiplication:** `3y` (read as '3 times y', or 'the product of 3 and y'). Notice that we often omit the multiplication sign `*` when a number is next to a variable. `3 * y` is the same as `3y`.\n- **Division:** `k / 4` or `k ÷ 4` or even as a fraction `k\n—\n4` (read as 'k divided by 4', or 'the quotient of k and 4').\n\nTranslating words into algebraic expressions is a key skill. It's like learning the vocabulary and grammar of algebra.", "visual": {"type": "static_text", "value": "x+7, 10-a, 3y, k/4"}, "hook": "These are your basic tools for writing math sentences.", "interactive_element": {"type": "button", "text": "Time to get hands-on!", "action": "next_screen"}}}, {"id": "lov-l2-s3", "type": "lesson_screen", "order": 3, "estimatedTimeSeconds": 150, "content": {"headline": "Interactive Expression Builder", "body_md": "Let's practice translating phrases into algebraic expressions. Drag the tokens to build the correct expression for each challenge. Pay close attention to the wording!", "visual": {"type": "static_text", "value": "Interactive Challenge: Build Expressions"}, "interactive_element": {"type": "interactive_expression_builder", "overall_prompt": "Drag the tokens to build the correct expression for each challenge.", "challenges": [{"id": "challenge1", "target_phrase": "The sum of a number 'x' and 12", "target_expression_components": ["x", "+", "12"], "available_tokens": ["x", "y", "12", "5", "+", "-", "*", "/"], "hint": "Keywords like 'sum', 'plus', 'added to', 'more than' all indicate addition.", "feedback_correct": "Excellent! `x + 12` correctly represents the sum of x and 12.", "feedback_incorrect_template": "Not quite. 'The sum of x and 12' means you need to add x and 12 together."}, {"id": "challenge2", "target_phrase": "7 multiplied by a number 'y'", "target_expression_components": ["7", "*", "y"], "available_tokens": ["y", "7", "3", "x", "+", "-", "*", "/"], "hint": "Keywords like 'multiplied by', 'times', 'product of' indicate multiplication. Remember, `7y` is also a correct way to write this.", "feedback_correct": "Perfect! `7 * y` (or just `7y`) means 7 multiplied by y.", "feedback_incorrect_template": "Think about how we show multiplication. You can use the '*' symbol or simply write the number next to the variable."}, {"id": "challenge3", "target_phrase": "A number 'a' decreased by 4", "target_expression_components": ["a", "-", "4"], "available_tokens": ["a", "b", "4", "9", "+", "-", "*", "/"], "hint": "Keywords like 'decreased by', 'minus', 'less', 'subtracted from' indicate subtraction. Order matters here!", "feedback_correct": "You got it! `a - 4` means 'a' is decreased by 4.", "feedback_incorrect_template": "Close! 'Decreased by' means you subtract 4 *from* 'a'. The order is important."}, {"id": "challenge4", "target_phrase": "The quotient of 15 and a number 'k'", "target_expression_components": ["15", "/", "k"], "available_tokens": ["k", "15", "m", "3", "+", "-", "*", "/"], "hint": "Keywords like 'quotient of', 'divided by', 'per' indicate division. The first number mentioned is usually the dividend (on top or first).", "feedback_correct": "Correct! `15 / k` represents the quotient of 15 and k.", "feedback_incorrect_template": "Remember, 'quotient' means division. 'The quotient of A and B' is A/B. Make sure the order is right!"}, {"id": "challenge5", "target_phrase": "5 less than twice a number 'n'", "target_expression_components": ["2", "*", "n", "-", "5"], "available_tokens": ["n", "2", "5", "x", "+", "-", "*", "/"], "hint": "Break it down: 'twice a number n' is `2n`. Then, '5 less than' that means you subtract 5 from `2n`.", "feedback_correct": "Fantastic! `2*n - 5` (or `2n - 5`) is '5 less than twice n'.", "feedback_incorrect_template": "This is a bit trickier! First find 'twice a number n', then think about what '5 less than that result' means."}], "check_button_text": "Check Expression", "action_button_text": "Any tips for tricky phrases?"}, "hook": "This is where the fun begins - translating words to math!"}}, {"id": "lov-l2-s4", "type": "lesson_screen", "order": 4, "estimatedTimeSeconds": 60, "content": {"headline": "Key Translation Tips", "body_md": "It's crucial to translate words into mathematical symbols correctly. For example, 'the sum of a and b' is `a + b`, while 'the product of a and b' is `a * b` (or just `ab`). A common point of confusion is 'less than'. For example, '5 less than x' is `x - 5`, not `5 - x`.\n\nExpressions can also be more complex and involve multiple operations or parentheses to group terms. For instance:\n\n- `2(L + W)` represents the perimeter of a rectangle with length L and width W. Here, you first add L and W, then multiply the sum by 2.\n- `(a + b) / 2` represents the average of two numbers a and b. You add a and b first, then divide the sum by 2.\n\nMastering expressions is like learning the notes and chords before you can play a full song. They are the fundamental building blocks for all the exciting algebra to come!", "visual": {"type": "unsplash_search", "value": "dictionary language learning"}, "hook": "With these tips, you'll be an expression expert in no time.", "interactive_element": {"type": "button", "text": "I'm ready to give these expressions some value!", "action": "next_lesson"}}}]}, {"id": "lov-l3-evaluating-expressions", "title": "Evaluating Expressions: What's It Worth?", "description": "Unlock the numerical value of algebraic expressions by substituting known values for variables and applying the order of operations.", "order": 3, "type": "interactive_lesson", "estimatedTimeMinutes": 10, "contentBlocks": [{"id": "lov-l3-s1", "type": "lesson_screen", "order": 1, "estimatedTimeSeconds": 60, "content": {"headline": "What's an Expression Worth?", "body_md": "You've learned how to build expressions using variables, numbers, and operations. That's like having a recipe! But what's the point of a recipe if you don't know what the final dish tastes like? In algebra, **evaluating an expression** is like 'cooking' the recipe to find its final numerical value once you know the specific 'ingredients' (the values of the variables).\n\nTo evaluate an expression, you follow two main steps:\n1.  **Substitute:** Replace each variable in the expression with its given numerical value.\n2.  **Calculate:** Perform the arithmetic operations, carefully following the **Order of Operations** (PEMDAS/BODMAS).", "visual": {"type": "giphy_search", "value": "calculator money value"}, "hook": "Time to find out what these expressions are really saying!", "interactive_element": {"type": "button", "text": "Remind me about the order!", "action": "next_screen"}}}, {"id": "lov-l3-s2", "type": "lesson_screen", "order": 2, "estimatedTimeSeconds": 90, "content": {"headline": "Order of Operations (PEMDAS/BODMAS)", "body_md": "**A Quick Refresher:**\n\n-   **P**arentheses (or **B**rackets): Simplify anything inside parentheses first.\n-   **E**xponents (or **O**rders/Of): Calculate powers and roots next.\n-   **M**ultiplication and **D**ivision: Perform these from left to right as they appear.\n-   **A**ddition and **S**ubtraction: Finally, perform these from left to right as they appear.\n\nSticking to this order is crucial!\n\nExample: Evaluate `3a + 2b - 1` when `a = 4` and `b = 5`:\n1.  **Substitute:** `3(4) + 2(5) - 1`\n2.  **Calculate:**\n    -   Multiplication: `12 + 10 - 1`\n    -   Addition/Subtraction: `22 - 1 = 21`\nSo, the value is **21**.", "visual": {"type": "static_text", "value": "PEMDAS: Parentheses, Exponents, Multiply/Divide, Add/Subtract"}, "hook": "This order is your secret weapon for correct calculations.", "interactive_element": {"type": "button", "text": "Let's put PEMDAS to the test!", "action": "next_screen"}}}, {"id": "lov-l3-s3", "type": "lesson_screen", "order": 3, "estimatedTimeSeconds": 180, "content": {"headline": "Interactive Expression Evaluator", "body_md": "Time to practice! For each task, substitute the given values for the variables into the expression. Then, carefully apply the order of operations to find the numerical result.", "visual": {"type": "static_text", "value": "Interactive Challenge: Evaluate Expressions"}, "interactive_element": {"type": "interactive_expression_evaluator", "overall_prompt": "Substitute and calculate using PEMDAS.", "tasks": [{"id": "task1", "expression_string_template": "3k + 7", "display_expression": "3k + 7", "variables_to_define": [{"name": "k", "value": 2}], "prompt_with_values": "Evaluate `3k + 7` when `k = 2`.", "correct_answer": 13, "solution_steps": ["1. Substitute k = 2:  `3(2) + 7`", "2. Multiply: `6 + 7`", "3. Add: `13`"], "feedback_correct": "Spot on! 3(2) + 7 = 6 + 7 = 13.", "feedback_incorrect": "Not quite. Remember PEMDAS: multiply 3 by k *before* adding 7."}, {"id": "task2", "expression_string_template": "10 - 2m", "display_expression": "10 - 2m", "variables_to_define": [{"name": "m", "value": 4}], "prompt_with_values": "Evaluate `10 - 2m` when `m = 4`.", "correct_answer": 2, "solution_steps": ["1. Substitute m = 4: `10 - 2(4)`", "2. Multiply: `10 - 8`", "3. Subtract: `2`"], "feedback_correct": "Excellent! 10 - 2(4) = 10 - 8 = 2.", "feedback_incorrect": "Careful with the order of operations! Multiplication (2m) comes before subtraction."}, {"id": "task3", "expression_string_template": "(a + b) / 2", "display_expression": "(a + b) / 2", "variables_to_define": [{"name": "a", "value": 8}, {"name": "b", "value": 4}], "prompt_with_values": "Evaluate `(a + b) / 2` when `a = 8` and `b = 4`. (Hint: This finds the average!)", "correct_answer": 6, "solution_steps": ["1. Substitute a = 8, b = 4: `(8 + 4) / 2`", "2. Parentheses first: `(12) / 2`", "3. <PERSON><PERSON>: `6`"], "feedback_correct": "Great job! The average of 8 and 4 is indeed 6.", "feedback_incorrect": "Remember PEMDAS! Operations *inside parentheses* must be done first."}, {"id": "task4", "expression_string_template": "x^2 - 3y + 5", "display_expression": "x² - 3y + 5", "variables_to_define": [{"name": "x", "value": 4}, {"name": "y", "value": 2}], "prompt_with_values": "Evaluate `x² - 3y + 5` when `x = 4` and `y = 2`.", "correct_answer": 15, "solution_steps": ["1. Substitute x = 4, y = 2: `(4)² - 3(2) + 5`", "2. Exponents first: `16 - 3(2) + 5`", "3. Multiplication next: `16 - 6 + 5`", "4. Addition/Subtraction (left to right): `10 + 5 = 15`"], "feedback_correct": "Fantastic! (4)² - 3(2) + 5 = 16 - 6 + 5 = 15.", "feedback_incorrect": "Double-check the order of operations. Exponents first, then multiplication, then addition/subtraction from left to right."}], "input_type": "number", "check_button_text": "Check Answer", "show_solution_button_text": "Show Steps", "action_button_text": "Why is this skill so crucial?"}, "hook": "Get your calculators (or brains) ready!"}}, {"id": "lov-l3-s4", "type": "lesson_screen", "order": 4, "estimatedTimeSeconds": 50, "content": {"headline": "Importance of Evaluating Expressions", "body_md": "Being able to accurately evaluate expressions is a cornerstone of algebra. It allows us to make sense of formulas, predict outcomes in real-world models (like calculating the trajectory of a ball or the growth of an investment), and is essential for solving equations later on. Keep practicing, and you'll become a pro at finding what expressions are worth!", "visual": {"type": "unsplash_search", "value": "scientist working formula"}, "hook": "You're now a codebreaker for mathematical phrases!", "interactive_element": {"type": "button", "text": "I'm ready to simplify things!", "action": "next_lesson"}}}]}, {"id": "lov-l4-like-terms-unite", "title": "Like Terms Unite! Simplifying Expressions", "description": "Discover how to simplify algebraic expressions by identifying and combining 'like terms' – terms that share the same variable parts.", "order": 4, "type": "interactive_lesson", "estimatedTimeMinutes": 10, "contentBlocks": [{"id": "lov-l4-s1", "type": "lesson_screen", "order": 1, "estimatedTimeSeconds": 60, "content": {"headline": "Simplifying Messy Expressions", "body_md": "Algebraic expressions can sometimes look a bit messy, like a jumbled collection of items. `2x + 3y - x + 2y + 5` looks more complicated than it needs to be! The good news is that we can often tidy them up by **combining like terms**. This process is called **simplifying an expression**.", "visual": {"type": "giphy_search", "value": "cleaning up mess"}, "hook": "Let's bring some order to the chaos!", "interactive_element": {"type": "button", "text": "Tell me about these 'like terms'!", "action": "next_screen"}}}, {"id": "lov-l4-s2", "type": "lesson_screen", "order": 2, "estimatedTimeSeconds": 90, "content": {"headline": "Understanding Like Terms", "body_md": "Think of it like sorting fruit. You can easily count how many apples you have and how many bananas you have, but you wouldn't add apples and bananas together to get 'apple-bananas'.\n\nIn algebra, like terms are terms that have the **exact same variable part(s)**, raised to the **exact same power(s)**. The numerical coefficients can be different.\n\n- `3x` and `5x` are like terms.\n- `7y²` and `-2y²` are like terms.\n- `4ab` and `6ab` are like terms.\n- Constant numbers (like `5`, `-2`) are like terms.\n\n**NOT like terms:**\n- `4a` and `4b` (different variables)\n- `6p²` and `6p` (different powers)\n- `2x` and `3xy` (different variable parts)", "visual": {"type": "giphy_search", "value": "sorting objects"}, "hook": "It's all about finding the matching pairs.", "interactive_element": {"type": "button", "text": "Let's start combining!", "action": "next_screen"}}}, {"id": "lov-l4-s3", "type": "lesson_screen", "order": 3, "estimatedTimeSeconds": 180, "content": {"headline": "Interactive: Combine Like Terms", "body_md": "To **combine like terms**, you simply add or subtract their coefficients. The variable part stays exactly the same.\n\n-   `2x + 5x = (2+5)x = 7x`.\n-   `4m + 3n - 2m + n = (4m - 2m) + (3n + n) = 2m + 4n`.\n\nLet's practice simplifying! For each expression, identify the like terms, combine them, and then write the fully simplified expression.", "visual": {"type": "static_text", "value": "Interactive Challenge: Simplify Expressions"}, "interactive_element": {"type": "interactive_like_terms_combiner", "overall_prompt": "Simplify by combining like terms.", "challenges": [{"id": "challenge1", "original_expression_display": "4x + 2y - x + 5 + 3y - 2", "expression_parts_for_grouping": [{"term_display": "4x", "term_id": "t1", "variable_signature": "x^1"}, {"term_display": "+ 2y", "term_id": "t2", "variable_signature": "y^1"}, {"term_display": "- x", "term_id": "t3", "variable_signature": "x^1"}, {"term_display": "+ 5", "term_id": "t4", "variable_signature": "const"}, {"term_display": "+ 3y", "term_id": "t5", "variable_signature": "y^1"}, {"term_display": "- 2", "term_id": "t6", "variable_signature": "const"}], "grouping_prompt": "Drag and group the like terms from the expression `4x + 2y - x + 5 + 3y - 2`.", "simplified_input_prompt": "Now, write the simplified expression:", "correct_simplified_expression": "3x + 5y + 3", "solution_steps": ["1. x terms: `4x - x = 3x`.", "2. y terms: `2y + 3y = 5y`.", "3. constants: `5 - 2 = 3`.", "4. Result: `3x + 5y + 3`."], "feedback_correct": "Excellent! The expression simplifies to 3x + 5y + 3.", "feedback_incorrect_template": "Not quite. Double-check grouping and combining coefficients."}, {"id": "challenge2", "original_expression_display": "7a² + 3a - 2a² - a + 8", "expression_parts_for_grouping": [{"term_display": "7a²", "term_id": "c2t1", "variable_signature": "a^2"}, {"term_display": "+ 3a", "term_id": "c2t2", "variable_signature": "a^1"}, {"term_display": "- 2a²", "term_id": "c2t3", "variable_signature": "a^2"}, {"term_display": "- a", "term_id": "c2t4", "variable_signature": "a^1"}, {"term_display": "+ 8", "term_id": "c2t5", "variable_signature": "const"}], "grouping_prompt": "Simplify the expression: `7a² + 3a - 2a² - a + 8`", "simplified_input_prompt": "Enter the simplified expression:", "correct_simplified_expression": "5a² + 2a + 8", "solution_steps": ["1. a² terms: `7a² - 2a² = 5a²`.", "2. a terms: `3a - a = 2a`.", "3. constant: `+8`.", "4. Result: `5a² + 2a + 8`."], "feedback_correct": "Perfect! `5a² + 2a + 8` is the simplified form.", "feedback_incorrect_template": "Take another look. Only combine terms with the exact same variable and exponent."}], "input_type": "text_simplified_expression", "check_button_text": "Check Simplified Expression", "action_button_text": "But *why* do we simplify?"}, "hook": "Time to put your sorting skills to the test!"}}, {"id": "lov-l4-s4", "type": "lesson_screen", "order": 4, "estimatedTimeSeconds": 50, "content": {"headline": "Why Simplify?", "body_md": "Why bother simplifying? Simplified expressions are much easier to read, understand, and work with, especially when we start solving equations or evaluating them for different variable values. It's like decluttering your workspace – it makes everything more efficient! This skill is absolutely crucial as you dive deeper into algebra.", "visual": {"type": "unsplash_search", "value": "tidy desk organization"}, "hook": "Simplicity is power in algebra!", "interactive_element": {"type": "button", "text": "I'm ready for the balance!", "action": "next_lesson"}}}]}, {"id": "lov-l5-balance-analogy", "title": "The Balance Analogy: Introduction to Equations", "description": "Visualize equations as perfectly balanced scales to intuitively grasp the fundamental concept of equality and prepare for solving equations.", "order": 5, "type": "interactive_lesson", "estimatedTimeMinutes": 9, "contentBlocks": [{"id": "lov-l5-s1", "type": "lesson_screen", "order": 1, "estimatedTimeSeconds": 60, "content": {"headline": "Introducing Equations!", "body_md": "We've journeyed through variables, built expressions, evaluated them, and even simplified them. Now, we're ready to tackle one of the most powerful concepts in algebra: **equations**!\n\nAn **equation** is a mathematical statement asserting that two expressions are **equal** in value. The key feature of an equation is the **equals sign (`=`)**, which acts like the fulcrum of a perfectly balanced scale.", "visual": {"type": "local_asset", "value": "assets/images/algebra/balance_scale_equation.svg", "alt_text": "A balance scale with 'x + 3' blocks on one side and '7' unit blocks on the other, perfectly balanced."}, "hook": "Get ready to see math in a whole new light - as a perfectly balanced act!", "interactive_element": {"type": "button", "text": "Show me this balance!", "action": "next_screen"}}}, {"id": "lov-l5-s2", "type": "lesson_screen", "order": 2, "estimatedTimeSeconds": 150, "content": {"headline": "Interactive Balance Scale", "body_md": "Consider the equation: `x + 3 = 7`. This means `x + 3` (left side) is equal to `7` (right side).\nWhen we **solve an equation**, we find the value of 'x' that makes this true – keeping the scale balanced.\n\nImagine 'x' is a mystery box. The left pan has 'x' and 3 unit weights. The right pan has 7 unit weights.", "visual": {"type": "static_text", "value": "Interactive: Balance the Scale for x + 3 = 7"}, "interactive_element": {"type": "interactive_balance_scale_analogy", "left_side_setup": {"variable_blocks": [{"name": "x", "count": 1, "label": "Mystery Box 'x'"}], "unit_blocks": 3, "unit_block_label": "Unit Weight"}, "right_side_setup": {"unit_blocks": 7, "unit_block_label": "Unit Weight"}, "variable_name": "x", "prompt": "The scale shows `x + 3 = 7`. For the scale to be balanced, what must be the weight (value) of 'x'? \n\nThink: If you remove 3 unit weights from both sides, what would 'x' be equal to?", "target_x_value_for_balance": 4, "feedback_on_balance": "Perfectly balanced! If x=4, then 4 + 3 = 7. Both sides are equal.", "feedback_on_unbalance_template": "Not quite balanced with x = {current_x_value}. Left side: {current_x_value} + 3 = {left_total}. Right side: 7.", "action_button_text": "What's the secret to keeping it balanced?"}, "hook": "This simple idea is the key to solving countless puzzles!"}}, {"id": "lov-l5-s3", "type": "lesson_screen", "order": 3, "estimatedTimeSeconds": 70, "content": {"headline": "The Golden Rule of Equations", "body_md": "The **Golden Rule of Solving Equations** comes directly from this balance analogy: \n\n***Whatever operation you perform on one side of an equation, you MUST perform the exact same operation on the other side to maintain the balance (equality).***\n\nIf you add something to one side, add the same to the other. If you subtract, multiply, or divide one side by a value, do the same to the other side (except dividing by zero!).\n\nThis principle is key to unlocking equations. In upcoming modules, we'll use this rule to solve various equations!", "visual": {"type": "unsplash_search", "value": "golden scales justice"}, "hook": "Master this rule, and you'll master equations!", "interactive_element": {"type": "button", "text": "I'm ready to test my new algebra skills!", "action": "next_lesson"}}}]}], "moduleTest": {"id": "lov-mt1-expression-explorer", "title": "Module Test: Expression Explorer", "description": "Manipulate expressions and evaluate them for given variable values.", "order": 6, "type": "module_test_interactive", "estimatedTimeMinutes": 7, "contentBlocks": [{"id": "lov-mt1-s0-intro", "type": "test_screen_intro", "order": 1, "estimatedTimeSeconds": 20, "content": {"headline": "Module Test: Expression Explorer", "body_md": "Time to test your knowledge on variables, expressions, and simplifying!", "visual": {"type": "giphy_search", "value": "test quiz challenge"}, "interactive_element": {"type": "button", "text": "Start Test!", "action": "next_screen"}}}, {"id": "lov-mt1-s1-q1", "type": "test_screen_intro", "order": 2, "estimatedTimeSeconds": 60, "content": {"headline": "Question 1", "body_md": "Which of the following is NOT a variable?", "interactive_element": {"type": "multiple_choice_text", "options": [{"id": "q1opt1", "text": "x", "is_correct": false, "feedback_incorrect": "'x' is commonly used as a variable."}, {"id": "q1opt2", "text": "7", "is_correct": true, "feedback_correct": "Correct! 7 is a constant number.", "feedback_incorrect": "Is 7 a symbol for a changing value, or is it fixed?"}, {"id": "q1opt3", "text": "a", "is_correct": false, "feedback_incorrect": "'a' can represent an unknown or changing value."}, {"id": "q1opt4", "text": "temperature", "is_correct": false, "feedback_incorrect": "If 'temperature' represents a value that can change, it acts as a variable."}], "action_button_text": "Next Question"}}}, {"id": "lov-mt1-s2-q2", "type": "test_screen_intro", "order": 3, "estimatedTimeSeconds": 70, "content": {"headline": "Question 2", "body_md": "Write an expression for 'the sum of a number y and 10'.", "interactive_element": {"type": "text_input", "placeholder": "Enter expression", "correct_answer_regex": "^(y\\s*\\+\\s*10|10\\s*\\+\\s*y)$", "feedback_correct": "Correct! 'y + 10' or '10 + y' is the sum.", "feedback_incorrect": "Think: 'sum' means addition.", "action_button_text": "Next Question"}}}, {"id": "lov-mt1-s3-q3", "type": "test_screen_intro", "order": 4, "estimatedTimeSeconds": 70, "content": {"headline": "Question 3", "body_md": "Evaluate the expression `2m - 5` when `m = 6`.", "interactive_element": {"type": "text_input", "placeholder": "Enter numerical value", "correct_answer_regex": "^7$", "feedback_correct": "Correct! 2(6) - 5 = 12 - 5 = 7.", "feedback_incorrect": "Substitute m=6, then use order of operations (multiply before subtracting).", "action_button_text": "Next Question"}}}, {"id": "lov-mt1-s4-q4", "type": "test_screen_intro", "order": 5, "estimatedTimeSeconds": 80, "content": {"headline": "Question 4", "body_md": "Simplify the expression: `4p + 2q - p + 3q`", "interactive_element": {"type": "multiple_choice_text", "options": [{"id": "q4opt1", "text": "3p + 5q", "is_correct": true, "feedback_correct": "Correct! (4p - p) + (2q + 3q) = 3p + 5q.", "feedback_incorrect": "Combine 'p' terms together and 'q' terms together."}, {"id": "q4opt2", "text": "4p + 5q", "is_correct": false, "feedback_incorrect": "Check the 'p' terms again."}, {"id": "q4opt3", "text": "5p + 3q", "is_correct": false, "feedback_incorrect": "Check the 'q' terms again."}, {"id": "q4opt4", "text": "6pq", "is_correct": false, "feedback_incorrect": "You can only add/subtract like terms; you can't combine 'p' and 'q' into 'pq' this way."}], "action_button_text": "Finish Test"}}}, {"id": "lov-mt1-s5-conclusion", "type": "lesson_screen", "order": 6, "estimatedTimeSeconds": 30, "content": {"headline": "Test Complete!", "body_md": "Well done on completing the Expression Explorer test! You're building a strong foundation in algebra.", "visual": {"type": "giphy_search", "value": "celebration success"}, "interactive_element": {"type": "button", "text": "Back to Course", "action": "module_complete"}}}]}}