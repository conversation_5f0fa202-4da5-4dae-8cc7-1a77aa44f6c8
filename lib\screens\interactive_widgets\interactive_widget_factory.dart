import 'package:flutter/material.dart';
import 'widgets/interactive_number_sequence_widget.dart';
import 'widgets/interactive_pattern_animation_widget.dart';
import 'widgets/interactive_pattern_gallery_widget.dart';
import 'widgets/interactive_sequence_widget.dart';
import 'widgets/interactive_letter_sequence_widget.dart';
import 'widgets/interactive_shape_sequence_widget_fixed.dart';
import 'widgets/truth_table_explorer_widget.dart';
import 'widgets/interactive_proof_contradiction_widget.dart';
import 'widgets/interactive_syllogism_builder_widget.dart';
import 'widgets/interactive_triangle_angle_sum_widget.dart';
import 'widgets/interactive_fallacy_identification_widget.dart';
import 'widgets/interactive_conditional_flow_widget.dart';
import 'widgets/interactive_balance_scale_analogy_widget.dart';
import 'maths/equations_and_algebra/interactive_equation_solver_widget.dart';
import 'widgets/interactive_step_by_step_equation_solver_widget.dart';
import 'widgets/interactive_word_problem_translator_widget.dart';
import 'widgets/interactive_counterexample_builder_widget.dart';
import 'widgets/interactive_logical_chain_constructor_widget.dart';
import 'widgets/interactive_logical_fallacy_quiz_widget.dart';
import 'widgets/interactive_logic_puzzle_widget.dart';
import 'maths/equations_and_algebra/interactive_variable_explorer_widget.dart';
import 'maths/equations_and_algebra/interactive_expression_builder_widget.dart';
import 'widgets/interactive_number_line_explorer_widget.dart';
import 'maths/equations_and_algebra/interactive_inequality_visualizer_widget.dart';
import 'widgets/interactive_absolute_value_explorer_widget.dart';
import 'widgets/interactive_pendulum_simulation_widget.dart';
import 'widgets/interactive_unit_converter_widget.dart';
import 'widgets/interactive_sorting_algorithm_visualizer_widget.dart';
import 'maths/equations_and_algebra/interactive_compound_inequality_builder_widget.dart';
import 'widgets/interactive_coordinate_plane_grapher_widget.dart';
import 'widgets/interactive_system_solver_widget.dart';
import 'widgets/interactive_elimination_method_visualizer_widget.dart';
import 'widgets/interactive_substitution_method_visualizer_widget.dart';
import 'widgets/interactive_matrix_operations_visualizer_widget.dart';
import 'widgets/interactive_number_base_converter_widget.dart';
import 'widgets/interactive_scientific_method_flowchart_widget.dart';
import 'widgets/interactive_hypothesis_builder_widget.dart';
import 'widgets/interactive_experimental_design_tool_widget.dart';
import 'widgets/interactive_variable_identifier_widget.dart';
import 'widgets/interactive_data_visualization_tool_widget.dart';
import 'widgets/interactive_statistical_analysis_calculator_widget.dart';
import 'widgets/interactive_measurement_error_simulator_widget.dart';
import 'widgets/interactive_graph_interpretation_exercise_widget.dart';
import 'widgets/interactive_model_builder_widget.dart';
import 'widgets/interactive_theory_evaluation_tool_widget.dart';
import 'widgets/interactive_prediction_generator_widget.dart';
import 'widgets/interactive_model_comparison_tool_widget.dart';
import 'widgets/interactive_evidence_evaluator_widget.dart';
import 'widgets/interactive_argument_strength_analyzer_widget.dart';
import 'widgets/interactive_correlation_causation_explorer_widget.dart';
import 'widgets/interactive_logical_fallacy_detector_widget.dart';
import 'widgets/interactive_timeline_scientific_discoveries_widget.dart';
import 'widgets/interactive_emerging_technology_explorer_widget.dart';
import 'widgets/interactive_pythagorean_theorem_viz_widget.dart';
import 'widgets/interactive_circle_properties_viz_widget.dart';
import 'widgets/interactive_transformation_identification_game_widget.dart';
import 'widgets/rotation_interactive_game_widget.dart';
import 'widgets/interactive_function_machine_widget.dart';
import 'widgets/interactive_function_representation_tool_widget.dart';
import 'widgets/interactive_function_identifier_widget.dart';
import 'widgets/interactive_domain_range_explorer_widget.dart';
import 'widgets/interactive_function_notation_practice_widget.dart';
import 'widgets/interactive_function_fundamentals_test_widget.dart';
import 'widgets/interactive_linear_function_explorer_widget.dart';
import 'maths/functions_and_probability/interactive_quadratic_function_explorer_widget.dart';
import 'widgets/interactive_exponential_function_explorer_widget.dart';
import 'maths/functions_and_probability/interactive_probability_calculator_widget.dart';
import 'widgets/interactive_sample_space_visualizer_widget.dart';
import 'widgets/interactive_function_probability_test_widget.dart';
import 'widgets/interactive_function_transformer_widget.dart';
import 'widgets/interactive_conditional_probability_calculator_widget.dart';
import 'widgets/interactive_probability_rules_visualizer_widget.dart';
import 'widgets/interactive_arithmetic_sequence_explorer_widget.dart';
import 'widgets/interactive_geometric_sequence_explorer_widget.dart';
import 'widgets/interactive_ratio_visualizer_widget.dart';
import 'widgets/interactive_algorithm_flowchart_builder_widget.dart';
import 'widgets/interactive_argument_analyzer_widget.dart';
import 'widgets/interactive_data_structure_visualizer_widget.dart';
import 'widgets/interactive_physics_simulation_widget.dart';
import 'widgets/interactive_data_structure_traversal_visualizer_widget.dart';
import 'widgets/interactive_molecular_viewer_widget.dart';
import 'widgets/interactive_logic_gate_simulator_widget.dart';
import 'widgets/interactive_addition_subtraction_visualizer_widget.dart';
import 'widgets/interactive_multiplication_array_visualizer_widget.dart';
import 'widgets/interactive_division_visualizer_widget.dart';
import 'widgets/interactive_place_value_demonstrator_widget.dart';
import 'widgets/interactive_probability_distribution_visualizer_widget.dart';
import 'widgets/interactive_number_comparison_tool_widget.dart';
import 'widgets/interactive_number_navigator_challenge_widget.dart';
import 'widgets/interactive_expression_explorer_widget.dart';
import 'widgets/interactive_addition_subtraction_equation_solver_widget.dart';
import 'widgets/interactive_division_equation_solver_widget.dart';
import 'widgets/interactive_multiplication_equation_solver_widget.dart';
import 'widgets/interactive_number_line_equation_visualizer_widget.dart';
import 'widgets/interactive_equation_checker_widget.dart';
import 'widgets/interactive_one_step_solver_widget.dart';
import 'widgets/interactive_order_of_operations_visualizer_widget.dart';
import 'widgets/interactive_like_terms_combiner_equations_widget.dart';
import 'widgets/interactive_fraction_equation_solver_widget.dart';
import 'widgets/interactive_two_step_challenge_widget.dart';
import 'maths/equations_and_algebra/interactive_inequality_symbol_explorer_widget.dart';
import 'widgets/interactive_number_line_inequality_visualizer_widget.dart';
import 'maths/equations_and_algebra/interactive_one_step_inequality_solver_widget.dart';
import 'maths/equations_and_algebra/interactive_two_step_inequality_solver_widget.dart';
import 'maths/equations_and_algebra/interactive_inequality_grapher_widget.dart';
import 'maths/equations_and_algebra/interactive_inequality_investigator_widget.dart';
import 'widgets/interactive_statistical_distribution_explorer_widget.dart';
import 'widgets/interactive_circuit_simulator_widget.dart';
import 'widgets/interactive_growing_patterns_visualizer_widget.dart';
import 'widgets/interactive_relationship_mapper_widget.dart';
import 'widgets/interactive_pattern_prediction_puzzle_widget.dart';

import 'widgets/interactive_periodic_table_widget.dart';
import 'widgets/interactive_algorithm_performance_evaluator_widget.dart';
import 'widgets/interactive_conservation_energy_simulator_widget.dart';
import 'widgets/interactive_power_calculator_widget.dart';
import 'widgets/interactive_energy_in_action_test_widget.dart';
import 'widgets/interactive_collision_simulator_widget.dart';
import 'widgets/interactive_momentum_conservation_demonstrator_widget.dart';
import 'widgets/interactive_elastic_inelastic_collisions_widget.dart';
import 'widgets/interactive_momentum_collisions_test_widget.dart';
import 'widgets/interactive_function_identifier_widget.dart';
import 'widgets/interactive_domain_range_explorer_widget.dart';
import 'widgets/interactive_function_notation_practice_widget.dart';
import 'widgets/interactive_function_fundamentals_test_widget.dart';
import 'widgets/interactive_linear_function_explorer_widget.dart';
import 'maths/functions_and_probability/interactive_quadratic_function_explorer_widget.dart';
import 'widgets/interactive_exponential_function_explorer_widget.dart';
import 'maths/functions_and_probability/interactive_probability_calculator_widget.dart';

/// Factory class for creating interactive widgets based on their type
class InteractiveWidgetFactory {
  /// Creates an interactive widget based on the provided type and data
  static Widget createWidget(String type, Map<String, dynamic> data) {
    switch (type) {
      case 'interactive_number_sequence':
        return InteractiveNumberSequenceWidget.fromData(data);

      case 'interactive_pattern_animation':
        return InteractivePatternAnimationWidget.fromData(data);

      case 'interactive_pattern_gallery':
        return InteractivePatternGalleryWidget.fromData(data);

      case 'interactive_sequence_widget':
        return InteractiveSequenceWidget.fromData(data);

      case 'interactive_letter_sequence':
        return InteractiveLetterSequenceWidget.fromData(data);

      case 'interactive_shape_sequence':
        return InteractiveShapeSequenceWidget.fromData(data);

      case 'truth_table_explorer':
        return TruthTableExplorerWidget.fromData(data);

      case 'interactive_proof_contradiction':
        return InteractiveProofContradictionWidget.fromData(data);

      case 'interactive_syllogism_builder':
        return InteractiveSyllogismBuilderWidget.fromData(data);

      case 'interactive_triangle_angle_sum':
        return InteractiveTriangleAngleSumWidget.fromData(data);

      case 'interactive_fallacy_identification':
        return InteractiveFallacyIdentificationWidget.fromData(data);

      case 'interactive_conditional_flow':
        return InteractiveConditionalFlowWidget.fromData(data);

      case 'interactive_balance_scale_analogy':
        return InteractiveBalanceScaleAnalogyWidget.fromData(data);

      case 'interactive_equation_solver':
        return InteractiveEquationSolverWidget.fromData(data);

      case 'interactive_step_by_step_equation_solver':
        return InteractiveStepByStepEquationSolverWidget.fromData(data);

      case 'interactive_word_problem_translator':
        return InteractiveWordProblemTranslatorWidget.fromData(data);

      case 'interactive_counterexample_builder':
        return InteractiveCounterexampleBuilderWidget.fromData(data);

      case 'interactive_logical_chain_constructor':
        return InteractiveLogicalChainConstructorWidget.fromData(data);

      case 'interactive_logical_fallacy_quiz':
        return InteractiveLogicalFallacyQuizWidget.fromData(data);

      case 'interactive_logic_puzzle':
        return InteractiveLogicPuzzleWidget.fromData(data);

      case 'interactive_variable_explorer':
        return InteractiveVariableExplorerWidget.fromData(data);

      case 'interactive_expression_builder':
        return InteractiveExpressionBuilderWidget.fromData(data);

      case 'interactive_number_line_explorer':
        return InteractiveNumberLineExplorerWidget.fromData(data);

      case 'interactive_inequality_visualizer':
        return InteractiveInequalityVisualizerWidget.fromData(data);

      case 'interactive_absolute_value_explorer':
        return InteractiveAbsoluteValueExplorerWidget.fromData(data);

      case 'interactive_pendulum_simulation':
        return InteractivePendulumSimulationWidget.fromData(data);

      case 'interactive_unit_converter':
        return InteractiveUnitConverterWidget.fromData(data);

      case 'interactive_sorting_algorithm_visualizer':
        return InteractiveSortingAlgorithmVisualizerWidget.fromData(data);

      case 'interactive_compound_inequality_builder':
        return InteractiveCompoundInequalityBuilderWidget.fromData(data);

      case 'interactive_coordinate_plane_grapher':
        return InteractiveCoordinatePlaneGrapherWidget.fromData(data);

      case 'interactive_system_solver':
        return InteractiveSystemSolverWidget.fromData(data);

      case 'interactive_elimination_method_visualizer':
        return InteractiveEliminationMethodVisualizerWidget.fromData(data);

      case 'interactive_substitution_method_visualizer':
        return InteractiveSubstitutionMethodVisualizerWidget.fromData(data);

      case 'interactive_matrix_operations_visualizer':
        return InteractiveMatrixOperationsVisualizerWidget.fromData(data);

      case 'interactive_number_base_converter':
        return InteractiveNumberBaseConverterWidget.fromData(data);

      case 'interactive_scientific_method_flowchart':
        return InteractiveScientificMethodFlowchartWidget.fromData(data);

      case 'interactive_hypothesis_builder':
        return InteractiveHypothesisBuilderWidget.fromData(data);

      case 'interactive_experimental_design_tool':
        return InteractiveExperimentalDesignToolWidget.fromData(data);

      case 'interactive_variable_identifier':
        return InteractiveVariableIdentifierWidget.fromData(data);

      case 'interactive_data_visualization_tool':
        return InteractiveDataVisualizationToolWidget.fromData(data);

      case 'interactive_statistical_analysis_calculator':
        return InteractiveStatisticalAnalysisCalculatorWidget.fromData(data);

      case 'interactive_measurement_error_simulator':
        return InteractiveMeasurementErrorSimulatorWidget.fromData(data);

      case 'interactive_graph_interpretation_exercise':
        return InteractiveGraphInterpretationExerciseWidget.fromData(data);

      case 'interactive_model_builder':
        return InteractiveModelBuilderWidget.fromData(data);

      case 'interactive_theory_evaluation_tool':
        return InteractiveTheoryEvaluationToolWidget.fromData(data);

      case 'interactive_prediction_generator':
        return InteractivePredictionGeneratorWidget.fromData(data);

      case 'interactive_model_comparison_tool':
        return InteractiveModelComparisonToolWidget.fromData(data);

      case 'interactive_evidence_evaluator':
        return InteractiveEvidenceEvaluatorWidget.fromData(data);

      case 'interactive_argument_strength_analyzer':
        return InteractiveArgumentStrengthAnalyzerWidget.fromData(data);

      case 'interactive_correlation_causation_explorer':
        return InteractiveCorrelationCausationExplorerWidget.fromData(data);

      case 'interactive_logical_fallacy_detector':
        return InteractiveLogicalFallacyDetectorWidget.fromData(data);

      case 'interactive_timeline_scientific_discoveries':
        return InteractiveTimelineScientificDiscoveriesWidget.fromData(data);

      case 'interactive_emerging_technology_explorer':
        return InteractiveEmergingTechnologyExplorerWidget.fromData(data);

      case 'interactive_pythagorean_theorem_viz':
        return InteractivePythagoreanTheoremVizWidget.fromData(data);

      case 'interactive_circle_properties_viz':
        return InteractiveCirclePropertiesVizWidget.fromData(data);

      case 'interactive_transformation_identification_game':
        return InteractiveTransformationIdentificationGameWidget.fromData(data);

      case 'rotation_interactive_game':
        return RotationInteractiveGameWidget.fromData(data);

      case 'interactive_function_machine':
        return InteractiveFunctionMachineWidget.fromData(data);

      case 'interactive_function_representation_tool':
        return InteractiveFunctionRepresentationToolWidget.fromData(data);

      case 'interactive_arithmetic_sequence_explorer':
        return InteractiveArithmeticSequenceExplorerWidget.fromData(data);

      case 'interactive_geometric_sequence_explorer':
        return InteractiveGeometricSequenceExplorerWidget.fromData(data);

      case 'interactive_ratio_visualizer':
        return InteractiveRatioVisualizerWidget.fromData(data);

      case 'interactive_algorithm_flowchart_builder':
        return InteractiveAlgorithmFlowchartBuilderWidget.fromData(data);

      case 'interactive_argument_analyzer':
        return InteractiveArgumentAnalyzerWidget.fromData(data);

      case 'interactive_data_structure_visualizer':
        return InteractiveDataStructureVisualizerWidget.fromData(data);

      case 'interactive_physics_simulation':
        return InteractivePhysicsSimulationWidget.fromData(data);

      case 'interactive_data_structure_traversal_visualizer':
        return InteractiveDataStructureTraversalVisualizerWidget.fromData(data);

      case 'interactive_molecular_viewer':
        return InteractiveMolecularViewerWidget.fromData(data);

      case 'interactive_logic_gate_simulator':
        return InteractiveLogicGateSimulatorWidget.fromData(data);

      case 'interactive_statistical_distribution_explorer':
        return InteractiveStatisticalDistributionExplorerWidget.fromData(data);

      case 'interactive_circuit_simulator':
        return InteractiveCircuitSimulatorWidget.fromData(data);

      case 'interactive_periodic_table':
        return InteractivePeriodicTableWidget.fromData(data);

      case 'interactive_algorithm_performance_evaluator':
        return InteractiveAlgorithmPerformanceEvaluatorWidget.fromData(data);

      case 'interactive_conservation_energy_simulator':
        return InteractiveConservationEnergySimulatorWidget.fromData(data);

      case 'interactive_power_calculator':
        return InteractivePowerCalculatorWidget.fromData(data);

      case 'interactive_energy_in_action_test':
        return InteractiveEnergyInActionTestWidget.fromData(data);

      case 'interactive_collision_simulator':
        return InteractiveCollisionSimulatorWidget.fromData(data);

      case 'interactive_momentum_conservation_demonstrator':
        return InteractiveMomentumConservationDemonstratorWidget.fromData(data);

      case 'interactive_elastic_inelastic_collisions':
        return InteractiveElasticInelasticCollisionsWidget.fromData(data);

      case 'interactive_momentum_collisions_test':
        return InteractiveMomentumCollisionsTestWidget.fromData(data);

      case 'interactive_growing_patterns_visualizer':
        return InteractiveGrowingPatternsVisualizerWidget.fromData(data);

      case 'interactive_relationship_mapper':
        return InteractiveRelationshipMapperWidget.fromData(data);

      case 'interactive_pattern_prediction_puzzle':
        return InteractivePatternPredictionPuzzleWidget.fromData(data);

      case 'interactive_addition_subtraction_visualizer':
        return InteractiveAdditionSubtractionVisualizerWidget(
          data: data,
          primaryColor: data['primaryColor'] ?? Colors.blue,
          secondaryColor: data['secondaryColor'] ?? Colors.orange,
          backgroundColor: data['backgroundColor'] ?? Colors.white,
          textColor: data['textColor'] ?? Colors.black87,
        );

      case 'interactive_multiplication_array_visualizer':
        return InteractiveMultiplicationArrayVisualizerWidget(
          data: data,
          primaryColor: data['primaryColor'] ?? Colors.blue,
          secondaryColor: data['secondaryColor'] ?? Colors.orange,
          backgroundColor: data['backgroundColor'] ?? Colors.white,
          textColor: data['textColor'] ?? Colors.black87,
        );

      case 'interactive_division_visualizer':
        return InteractiveDivisionVisualizerWidget(
          data: data,
          primaryColor: data['primaryColor'] ?? Colors.blue,
          secondaryColor: data['secondaryColor'] ?? Colors.orange,
          backgroundColor: data['backgroundColor'] ?? Colors.white,
          textColor: data['textColor'] ?? Colors.black87,
        );

      case 'interactive_place_value_demonstrator':
        return InteractivePlaceValueDemonstratorWidget(
          data: data,
          primaryColor: data['primaryColor'] ?? Colors.blue,
          secondaryColor: data['secondaryColor'] ?? Colors.orange,
          backgroundColor: data['backgroundColor'] ?? Colors.white,
          textColor: data['textColor'] ?? Colors.black87,
        );

      case 'interactive_number_comparison_tool':
        return InteractiveNumberComparisonToolWidget(
          data: data,
          primaryColor: data['primaryColor'] ?? Colors.blue,
          secondaryColor: data['secondaryColor'] ?? Colors.orange,
          backgroundColor: data['backgroundColor'] ?? Colors.white,
          textColor: data['textColor'] ?? Colors.black87,
        );

      case 'interactive_number_navigator_challenge':
        return InteractiveNumberNavigatorChallengeWidget(
          data: data,
          primaryColor: data['primaryColor'] ?? Colors.blue,
          secondaryColor: data['secondaryColor'] ?? Colors.orange,
          backgroundColor: data['backgroundColor'] ?? Colors.white,
          textColor: data['textColor'] ?? Colors.black87,
        );

      case 'interactive_expression_explorer':
        return InteractiveExpressionExplorerWidget(
          data: data,
          primaryColor: data['primaryColor'] ?? Colors.blue,
          secondaryColor: data['secondaryColor'] ?? Colors.orange,
          backgroundColor: data['backgroundColor'] ?? Colors.white,
          textColor: data['textColor'] ?? Colors.black87,
        );

      case 'interactive_addition_subtraction_equation_solver':
        return InteractiveAdditionSubtractionEquationSolverWidget(
          data: data,
          primaryColor: data['primaryColor'] ?? Colors.blue,
          secondaryColor: data['secondaryColor'] ?? Colors.orange,
          backgroundColor: data['backgroundColor'] ?? Colors.white,
          textColor: data['textColor'] ?? Colors.black87,
        );

      case 'interactive_division_equation_solver':
        return InteractiveDivisionEquationSolverWidget(
          data: data,
          primaryColor: data['primaryColor'] ?? Colors.blue,
          secondaryColor: data['secondaryColor'] ?? Colors.orange,
          backgroundColor: data['backgroundColor'] ?? Colors.white,
          textColor: data['textColor'] ?? Colors.black87,
        );

      case 'interactive_multiplication_equation_solver':
        return InteractiveMultiplicationEquationSolverWidget(
          data: data,
          primaryColor: data['primaryColor'] ?? Colors.blue,
          secondaryColor: data['secondaryColor'] ?? Colors.orange,
          backgroundColor: data['backgroundColor'] ?? Colors.white,
          textColor: data['textColor'] ?? Colors.black87,
        );

      case 'interactive_number_line_equation_visualizer':
        return InteractiveNumberLineEquationVisualizerWidget(
          data: data,
          primaryColor: data['primaryColor'] ?? Colors.blue,
          secondaryColor: data['secondaryColor'] ?? Colors.orange,
          backgroundColor: data['backgroundColor'] ?? Colors.white,
          textColor: data['textColor'] ?? Colors.black87,
        );

      case 'interactive_equation_checker':
        return InteractiveEquationCheckerWidget(
          data: data,
          primaryColor: data['primaryColor'] ?? Colors.blue,
          secondaryColor: data['secondaryColor'] ?? Colors.orange,
          backgroundColor: data['backgroundColor'] ?? Colors.white,
          textColor: data['textColor'] ?? Colors.black87,
        );

      case 'interactive_one_step_solver':
        return InteractiveOneStepSolverWidget(
          data: data,
          primaryColor: data['primaryColor'] ?? Colors.blue,
          secondaryColor: data['secondaryColor'] ?? Colors.orange,
          backgroundColor: data['backgroundColor'] ?? Colors.white,
          textColor: data['textColor'] ?? Colors.black87,
        );

      case 'interactive_order_of_operations_visualizer':
        return InteractiveOrderOfOperationsVisualizerWidget(
          data: data,
          primaryColor: data['primaryColor'] ?? Colors.blue,
          secondaryColor: data['secondaryColor'] ?? Colors.orange,
          backgroundColor: data['backgroundColor'] ?? Colors.white,
          textColor: data['textColor'] ?? Colors.black87,
        );

      case 'interactive_like_terms_combiner_equations':
        return InteractiveLikeTermsCombinerEquationsWidget(
          data: data,
          primaryColor: data['primaryColor'] ?? Colors.blue,
          secondaryColor: data['secondaryColor'] ?? Colors.orange,
          backgroundColor: data['backgroundColor'] ?? Colors.white,
          textColor: data['textColor'] ?? Colors.black87,
        );

      case 'interactive_step_by_step_equation_solver':
        return InteractiveStepByStepEquationSolverWidget(
          data: data,
          onStateChanged: (isCompleted) {
            if (data['onStateChanged'] != null) {
              data['onStateChanged'](isCompleted);
            }
          },
        );

      case 'interactive_fraction_equation_solver':
        return InteractiveFractionEquationSolverWidget(
          data: data,
          primaryColor: data['primaryColor'] ?? Colors.blue,
          secondaryColor: data['secondaryColor'] ?? Colors.orange,
          backgroundColor: data['backgroundColor'] ?? Colors.white,
          textColor: data['textColor'] ?? Colors.black87,
        );

      case 'interactive_word_problem_translator':
        return InteractiveWordProblemTranslatorWidget(
          data: data,
          onStateChanged: (isCompleted) {
            if (data['onStateChanged'] != null) {
              data['onStateChanged'](isCompleted);
            }
          },
        );

      case 'interactive_two_step_challenge':
        return InteractiveTwoStepChallengeWidget(
          data: data,
          primaryColor: data['primaryColor'] ?? Colors.blue,
          secondaryColor: data['secondaryColor'] ?? Colors.orange,
          backgroundColor: data['backgroundColor'] ?? Colors.white,
          textColor: data['textColor'] ?? Colors.black87,
        );

      case 'interactive_inequality_symbol_explorer':
        return InteractiveInequalitySymbolExplorerWidget(
          data: data,
          primaryColor: data['primaryColor'] ?? Colors.blue,
          secondaryColor: data['secondaryColor'] ?? Colors.orange,
          backgroundColor: data['backgroundColor'] ?? Colors.white,
          textColor: data['textColor'] ?? Colors.black87,
        );

      case 'interactive_number_line_inequality_visualizer':
        return InteractiveNumberLineInequalityVisualizerWidget(
          data: data,
          primaryColor: data['primaryColor'] ?? Colors.blue,
          secondaryColor: data['secondaryColor'] ?? Colors.orange,
          backgroundColor: data['backgroundColor'] ?? Colors.white,
          textColor: data['textColor'] ?? Colors.black87,
        );

      case 'interactive_one_step_inequality_solver':
        return InteractiveOneStepInequalitySolverWidget(
          data: data,
          primaryColor: data['primaryColor'] ?? Colors.blue,
          secondaryColor: data['secondaryColor'] ?? Colors.orange,
          backgroundColor: data['backgroundColor'] ?? Colors.white,
          textColor: data['textColor'] ?? Colors.black87,
          successColor: data['successColor'] ?? Colors.green,
          errorColor: data['errorColor'] ?? Colors.red,
          onStateChanged: (isCompleted) {
            if (data['onStateChanged'] != null) {
              data['onStateChanged'](isCompleted);
            }
          },
        );

      case 'interactive_two_step_inequality_solver':
        return InteractiveTwoStepInequalitySolverWidget(
          data: data,
          primaryColor: data['primaryColor'] ?? Colors.blue,
          secondaryColor: data['secondaryColor'] ?? Colors.orange,
          backgroundColor: data['backgroundColor'] ?? Colors.white,
          textColor: data['textColor'] ?? Colors.black87,
          successColor: data['successColor'] ?? Colors.green,
          errorColor: data['errorColor'] ?? Colors.red,
          onStateChanged: (isCompleted) {
            if (data['onStateChanged'] != null) {
              data['onStateChanged'](isCompleted);
            }
          },
        );

      case 'interactive_inequality_grapher':
        return InteractiveInequalityGrapherWidget(
          data: data,
          primaryColor: data['primaryColor'] ?? Colors.blue,
          secondaryColor: data['secondaryColor'] ?? Colors.orange,
          backgroundColor: data['backgroundColor'] ?? Colors.white,
          textColor: data['textColor'] ?? Colors.black87,
          successColor: data['successColor'] ?? Colors.green,
          errorColor: data['errorColor'] ?? Colors.red,
          shadingColor: data['shadingColor'] ?? Colors.blue,
          onStateChanged: (isCompleted) {
            if (data['onStateChanged'] != null) {
              data['onStateChanged'](isCompleted);
            }
          },
        );

      case 'interactive_inequality_investigator':
        return InteractiveInequalityInvestigatorWidget(
          data: data,
          primaryColor: data['primaryColor'] ?? Colors.blue,
          secondaryColor: data['secondaryColor'] ?? Colors.orange,
          backgroundColor: data['backgroundColor'] ?? Colors.white,
          textColor: data['textColor'] ?? Colors.black87,
          successColor: data['successColor'] ?? Colors.green,
          errorColor: data['errorColor'] ?? Colors.red,
          onStateChanged: (isCompleted) {
            if (data['onStateChanged'] != null) {
              data['onStateChanged'](isCompleted);
            }
          },
        );

      case 'interactive_function_identifier':
        return InteractiveFunctionIdentifierWidget(
          data: data,
          onStateChanged: (isCompleted) {
            if (data['onStateChanged'] != null) {
              data['onStateChanged'](isCompleted);
            }
          },
        );

      case 'interactive_domain_range_explorer':
        return InteractiveDomainRangeExplorerWidget(
          data: data,
          onStateChanged: (isCompleted) {
            if (data['onStateChanged'] != null) {
              data['onStateChanged'](isCompleted);
            }
          },
        );

      case 'interactive_function_notation_practice':
        return InteractiveFunctionNotationPracticeWidget(
          data: data,
          onStateChanged: (isCompleted) {
            if (data['onStateChanged'] != null) {
              data['onStateChanged'](isCompleted);
            }
          },
        );

      case 'interactive_function_fundamentals_test':
        return InteractiveFunctionFundamentalsTestWidget(
          data: data,
          onStateChanged: (isCompleted) {
            if (data['onStateChanged'] != null) {
              data['onStateChanged'](isCompleted);
            }
          },
        );

      case 'interactive_linear_function_explorer':
        return InteractiveLinearFunctionExplorerWidget(
          data: data,
          onStateChanged: (isCompleted) {
            if (data['onStateChanged'] != null) {
              data['onStateChanged'](isCompleted);
            }
          },
        );

      case 'interactive_quadratic_function_explorer':
        return InteractiveQuadraticFunctionExplorerWidget(
          data: data,
          onStateChanged: (isCompleted) {
            if (data['onStateChanged'] != null) {
              data['onStateChanged'](isCompleted);
            }
          },
        );

      case 'interactive_exponential_function_explorer':
        return InteractiveExponentialFunctionExplorerWidget(
          data: data,
          onStateChanged: (isCompleted) {
            if (data['onStateChanged'] != null) {
              data['onStateChanged'](isCompleted);
            }
          },
        );

      case 'interactive_probability_calculator':
        return InteractiveProbabilityCalculatorWidget(
          data: data,
          onStateChanged: (isCompleted) {
            if (data['onStateChanged'] != null) {
              data['onStateChanged'](isCompleted);
            }
          },
        );

      case 'interactive_sample_space_visualizer':
        return InteractiveSampleSpaceVisualizerWidget(
          data: data,
          onStateChanged: (isCompleted) {
            if (data['onStateChanged'] != null) {
              data['onStateChanged'](isCompleted);
            }
          },
        );

      case 'interactive_function_probability_test':
        return InteractiveFunctionProbabilityTestWidget(
          data: data,
          onStateChanged: (isCompleted) {
            if (data['onStateChanged'] != null) {
              data['onStateChanged'](isCompleted);
            }
          },
        );

      case 'interactive_function_transformer':
        return InteractiveFunctionTransformerWidget(
          data: data,
          onStateChanged: (isCompleted) {
            if (data['onStateChanged'] != null) {
              data['onStateChanged'](isCompleted);
            }
          },
        );

      case 'interactive_conditional_probability_calculator':
        return InteractiveConditionalProbabilityCalculatorWidget(
          data: data,
          onStateChanged: (isCompleted) {
            if (data['onStateChanged'] != null) {
              data['onStateChanged'](isCompleted);
            }
          },
        );

      case 'interactive_probability_rules_visualizer':
        return InteractiveProbabilityRulesVisualizerWidget(
          data: data,
          onStateChanged: (isCompleted) {
            if (data['onStateChanged'] != null) {
              data['onStateChanged'](isCompleted);
            }
          },
        );

      case 'interactive_probability_distribution_visualizer':
        return InteractiveProbabilityDistributionVisualizerWidget(
          data: data,
          onStateChanged: (isCompleted) {
            if (data['onStateChanged'] != null) {
              data['onStateChanged'](isCompleted);
            }
          },
        );

      // Add more widget types here as they are implemented

      default:
        return _buildPlaceholderWidget(type);
    }
  }

  /// Creates a placeholder widget for unimplemented interactive widget types
  static Widget _buildPlaceholderWidget(String type) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.grey[200],
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: Colors.grey[400]!),
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(Icons.construction, size: 48, color: Colors.orange[700]),
          const SizedBox(height: 8),
          Text(
            'Interactive Widget: $type',
            style: const TextStyle(fontWeight: FontWeight.bold, fontSize: 16),
          ),
          const SizedBox(height: 4),
          const Text(
            'This interactive widget is under construction.',
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }
}
