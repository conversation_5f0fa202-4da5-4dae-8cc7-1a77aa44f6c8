{"id": "power-of-patterns-and-relationships", "title": "THE POWER OF PATTERNS AND RELATIONSHIPS", "description": "Uncover the beauty of repeating sequences and how different quantities connect.", "order": 4, "lessons": [{"id": "growing-patterns", "title": "Growing Patterns", "description": "Visualize how sequences increase and predict future elements.", "order": 1, "type": "interactive_lesson", "estimatedTimeMinutes": 10, "contentBlocks": [{"id": "gp-screen1-intro", "type": "lesson_screen", "order": 1, "estimatedTimeSeconds": 30, "content": {"headline": "Patterns That Grow!", "body_md": "Some patterns don't just repeat, they grow! Think of a plant sprouting or a tower getting taller. Let's explore these dynamic sequences.", "visual": {"type": "giphy_search", "value": "plant growing animation"}, "interactive_element": {"type": "button", "text": "See Them Grow!", "action": "next_screen"}}}, {"id": "gp-screen2-visual-sequence", "type": "lesson_screen", "order": 2, "estimatedTimeSeconds": 80, "content": {"headline": "Visual Growing Patterns", "body_md": "Look at this pattern of squares:\n\nTerm 1: 🟥 (1 square)\nTerm 2: 🟥🟥 (2 squares)\nTerm 3: 🟥🟥🟥 (3 squares)\n\nHow many squares will be in Term 4? Term 5?", "visual": {"type": "local_asset", "value": "assets/images/patterns/growing_squares_123.svg"}, "interactive_element": {"type": "text_input", "question_text": "Squares in Term 5?", "correct_answer_regex": "^5$", "feedback_correct": "Exactly! Each term adds one more square.", "feedback_incorrect": "Count how many squares are in each term and see the pattern.", "action_button_text": "Next Example"}}}, {"id": "gp-screen3-triangular-numbers", "type": "lesson_screen", "order": 3, "estimatedTimeSeconds": 100, "content": {"headline": "Triangular Numbers: A Classic Growth", "body_md": "Consider this pattern forming triangles:\nTerm 1: ● (1 dot)\nTerm 2: ●\n       ● ● (3 dots)\nTerm 3: ●\n       ● ●\n       ● ● ● (6 dots)\n\nThese are called triangular numbers. What's the rule for growth?", "visual": {"type": "local_asset", "value": "assets/images/patterns/triangular_numbers_123.svg"}, "interactive_element": {"type": "text_input", "question_text": "How many dots in Term 4?", "correct_answer_regex": "^10$", "feedback_correct": "Correct! Term 1: 1. Term 2: 1+2=3. Term 3: 1+2+3=6. Term 4: 1+2+3+4=10.", "feedback_incorrect": "Term 1 adds 1. Term 2 adds 2 to the previous. Term 3 adds 3 to the previous...", "action_button_text": "Numerical Growth"}}}, {"id": "gp-screen4-arithmetic-sequence", "type": "lesson_screen", "order": 4, "estimatedTimeSeconds": 80, "content": {"headline": "Arithmetic Sequences: Constant Addition", "body_md": "An **arithmetic sequence** is a list of numbers where the difference between consecutive terms is constant. This constant difference is called the **common difference**.\n\nExample: 2, 5, 8, 11, 14...\nCommon difference = 3 (5-2=3, 8-5=3, etc.)", "visual": {"type": "interactive_arithmetic_sequence_explorer", "title": "Arithmetic Sequence Explorer", "description": "Adjust the first term and common difference to see how the sequence changes.", "first_term": 4.0, "common_difference": 5.0, "number_of_terms": 10, "primary_color": "#2196F3", "secondary_color": "#FF9800", "tertiary_color": "#4CAF50", "show_formula": true, "show_differences": true, "show_graph": true, "show_table": true}, "interactive_element": {"type": "text_input", "question_text": "What's the next term in: 4, 9, 14, 19, ___?", "correct_answer_regex": "^24$", "feedback_correct": "Spot on! The common difference is 5.", "feedback_incorrect": "Find the common difference between terms, then add it to the last term.", "action_button_text": "Geometric Growth?"}}}, {"id": "gp-screen5-geometric-sequence", "type": "lesson_screen", "order": 5, "estimatedTimeSeconds": 90, "content": {"headline": "Geometric Sequences: Constant Multiplication", "body_md": "A **geometric sequence** is a list of numbers where each term after the first is found by multiplying the previous one by a fixed, non-zero number called the **common ratio**.\n\nExample: 2, 6, 18, 54...\nCommon ratio = 3 (6/2=3, 18/6=3, etc.)", "visual": {"type": "interactive_geometric_sequence_explorer", "title": "Geometric Sequence Explorer", "description": "Adjust the first term and common ratio to see how the sequence changes.", "first_term": 3.0, "common_ratio": 2.0, "number_of_terms": 10, "primary_color": "#2196F3", "secondary_color": "#FF9800", "tertiary_color": "#4CAF50", "show_formula": true, "show_ratios": true, "show_graph": true, "show_table": true, "use_log_scale": true}, "interactive_element": {"type": "text_input", "question_text": "What's the next term in: 3, 6, 12, 24, ___?", "correct_answer_regex": "^48$", "feedback_correct": "Perfect! The common ratio is 2.", "feedback_incorrect": "Find the common ratio (divide a term by its preceding term), then multiply the last term by it.", "action_button_text": "Predicting Future!"}}}, {"id": "gp-screen6-predicting-terms", "type": "lesson_screen", "order": 6, "estimatedTimeSeconds": 70, "content": {"headline": "Predicting the Future (of <PERSON><PERSON><PERSON>!)", "body_md": "Once you understand the rule of a growing pattern (like the common difference or common ratio), you can predict any future term in the sequence!\n\nThis is super useful in math, science, and even finance.", "visual": {"type": "unsplash_search", "value": "crystal ball future prediction"}, "interactive_element": {"type": "button", "text": "I'm a Pattern Predictor!", "action": "next_screen"}}}, {"id": "gp-screen7-recap", "type": "lesson_screen", "order": 7, "estimatedTimeSeconds": 40, "content": {"headline": "Recap: Growing Strong!", "body_md": "*   Growing patterns change predictably.\n*   Arithmetic sequences: Add a common difference.\n*   Geometric sequences: Multiply by a common ratio.\n*   Understanding the rule helps predict future terms.", "interactive_element": {"type": "button", "text": "Next Lesson", "action": "next_lesson"}}}]}, {"id": "mapping-relationships", "title": "Mapping Relationships", "description": "Explore how variables interact and create visual representations.", "order": 2, "type": "interactive_lesson", "estimatedTimeMinutes": 10, "contentBlocks": [{"id": "mr-screen1-intro", "type": "lesson_screen", "order": 1, "estimatedTimeSeconds": 30, "content": {"headline": "Connecting the Dots: Variables & Relationships", "body_md": "How does the time you study affect your test score? How does the price of a toy affect how many are sold? We can map these relationships visually!", "visual": {"type": "giphy_search", "value": "connecting dots graph"}, "interactive_element": {"type": "button", "text": "Let's Map It Out!", "action": "next_screen"}}}, {"id": "mr-screen2-variables", "type": "lesson_screen", "order": 2, "estimatedTimeSeconds": 60, "content": {"headline": "What are Variables?", "body_md": "A **variable** is something that can change or vary. In math, we often use letters like 'x' or 'y' to represent them.\n*   **Independent Variable:** The one you change or control (e.g., hours studied).\n*   **Dependent Variable:** The one that changes in response (e.g., test score).", "interactive_element": {"type": "multiple_choice_text", "question_text": "If you track plant growth (height) over several weeks (time), which is the dependent variable?", "options": [{"id": "mr2opt1", "text": "Time in weeks", "is_correct": false, "feedback_incorrect": "Time is what you're tracking *against*; it's independent here."}, {"id": "mr2opt2", "text": "Plant height", "is_correct": true, "feedback_correct": "Correct! The height *depends* on how much time has passed.", "feedback_incorrect": "The dependent variable is the one being measured or observed as a result of changes in the other."}], "action_button_text": "How to Visualize?"}}}, {"id": "mr-screen3-coordinate-plane", "type": "lesson_screen", "order": 3, "estimatedTimeSeconds": 70, "content": {"headline": "The Coordinate Plane: Our Map", "body_md": "We use a **coordinate plane** (or Cartesian plane) to visualize relationships. It has two perpendicular number lines:\n*   **x-axis (horizontal):** Usually for the independent variable.\n*   **y-axis (vertical):** Usually for the dependent variable.\nPoints are plotted as (x, y) pairs.", "visual": {"type": "local_asset", "value": "assets/images/geometry/coordinate_plane.svg"}, "interactive_element": {"type": "button", "text": "Let's Plot!", "action": "next_screen"}}}, {"id": "mr-screen4-plotting-points", "type": "lesson_screen", "order": 4, "estimatedTimeSeconds": 90, "content": {"headline": "Plotting Points: An Example", "body_md": "Imagine tracking ice cream sales vs. temperature:\n*   Temp 20°C, Sales 10 cones (Point: 20,10)\n*   Temp 25°C, Sales 25 cones (Point: 25,25)\n*   Temp 30°C, Sales 50 cones (Point: 30,50)\n\nPlot these points. What kind of relationship do you see?", "visual": {"type": "interactive_plotting_game", "points_to_plot": [{"x": 20, "y": 10}, {"x": 25, "y": 25}, {"x": 30, "y": 50}], "x_axis_label": "Temperature (°C)", "y_axis_label": "<PERSON><PERSON> Sold"}, "interactive_element": {"type": "multiple_choice_text", "question_text": "As temperature increases, what happens to sales?", "options": [{"id": "mr4opt1", "text": "Sales increase", "is_correct": true, "feedback_correct": "Correct! This is a positive relationship."}, {"id": "mr4opt2", "text": "Sales decrease", "is_correct": false, "feedback_incorrect": "Look at the trend of the points."}, {"id": "mr4opt3", "text": "Sales stay the same", "is_correct": false, "feedback_incorrect": "The points are not flat."}], "action_button_text": "Types of Relationships"}}}, {"id": "mr-screen5-types-of-relationships", "type": "lesson_screen", "order": 5, "estimatedTimeSeconds": 70, "content": {"headline": "Interpreting the Map: Types of Relationships", "body_md": "*   **Positive Relationship:** As one variable increases, the other tends to increase (points go up to the right).\n*   **Negative Relationship:** As one variable increases, the other tends to decrease (points go down to the right).\n*   **No Relationship:** Points are scattered, no clear trend.", "visual": {"type": "local_asset", "value": "assets/images/graphs/relationship_types.svg"}, "interactive_element": {"type": "button", "text": "Makes Sense!", "action": "next_screen"}}}, {"id": "mr-screen6-real-world-graphs", "type": "lesson_screen", "order": 6, "estimatedTimeSeconds": 60, "content": {"headline": "Graphs in the Real World", "body_md": "Graphs are used everywhere to show relationships:\n*   Stock market trends (price vs. time)\n*   Scientific experiments (results vs. conditions)\n*   Population growth (population vs. time)\n\nBeing able to read and understand them is a vital skill!", "visual": {"type": "giphy_search", "value": "stock market graph animation"}, "interactive_element": {"type": "button", "text": "I'm Ready to Map!", "action": "next_screen"}}}, {"id": "mr-screen7-recap", "type": "lesson_screen", "order": 7, "estimatedTimeSeconds": 40, "content": {"headline": "Recap: Mapping It All Out", "body_md": "*   Variables can be independent or dependent.\n*   Coordinate planes help visualize their relationships.\n*   Patterns in plotted points reveal positive, negative, or no relationships.", "interactive_element": {"type": "button", "text": "Next Lesson", "action": "next_lesson"}}}]}, {"id": "art-of-ratios", "title": "The Art of Ratios", "description": "Understand proportional relationships through visual comparisons.", "order": 3, "type": "interactive_lesson", "estimatedTimeMinutes": 9, "contentBlocks": [{"id": "aor-screen1-intro", "type": "lesson_screen", "order": 1, "estimatedTimeSeconds": 30, "content": {"headline": "Comparing Quantities: The Magic of Ratios!", "body_md": "If a recipe calls for 2 cups of flour for every 1 cup of sugar, that's a ratio! Ratios help us compare quantities and understand proportions.", "visual": {"type": "giphy_search", "value": "comparing measuring cups"}, "interactive_element": {"type": "button", "text": "Tell Me More!", "action": "next_screen"}}}, {"id": "aor-screen2-what-is-ratio", "type": "lesson_screen", "order": 2, "estimatedTimeSeconds": 60, "content": {"headline": "What is a Ratio?", "body_md": "A **ratio** compares two quantities. It can be written in a few ways:\n*   Using a colon: **a : b** (e.g., 3 : 2)\n*   As a fraction: **a/b** (e.g., 3/2)\n*   With words: \"a to b\" (e.g., \"3 to 2\")\n\nExample: If there are 5 apples and 3 oranges, the ratio of apples to oranges is 5:3.", "interactive_element": {"type": "multiple_choice_text", "question_text": "In a class with 10 girls and 15 boys, what's the ratio of girls to boys?", "options": [{"id": "aor2opt1", "text": "15:10", "is_correct": false, "feedback_incorrect": "That's boys to girls. Order matters in ratios!"}, {"id": "aor2opt2", "text": "10:15", "is_correct": true, "feedback_correct": "Correct! It can also be simplified to 2:3.", "feedback_incorrect": "Girls first, then boys."}, {"id": "aor2opt3", "text": "10:25", "is_correct": false, "feedback_incorrect": "25 is the total, not just boys."}], "action_button_text": "Simplifying <PERSON>ios"}}}, {"id": "aor-screen3-simplifying-ratios", "type": "lesson_screen", "order": 3, "estimatedTimeSeconds": 70, "content": {"headline": "Simplifying Ratios: Keeping it Clear", "body_md": "Just like fractions, ratios can often be simplified by dividing both parts by their greatest common divisor.\n\nExample: A ratio of 6:9.\nBoth 6 and 9 are divisible by 3.\n6 ÷ 3 = 2\n9 ÷ 3 = 3\nSo, 6:9 simplifies to 2:3.", "visual": {"type": "unsplash_search", "value": "clear simple design"}, "interactive_element": {"type": "text_input", "question_text": "Simplify the ratio 12:18.", "correct_answer_regex": "^(2:3|2/3)$", "feedback_correct": "Perfect! Both are divisible by 6, giving 2:3.", "feedback_incorrect": "Find the largest number that divides both 12 and 18.", "action_button_text": "Proportions!"}}}, {"id": "aor-screen4-proportions", "type": "lesson_screen", "order": 4, "estimatedTimeSeconds": 80, "content": {"headline": "Proportions: Equal Ratios", "body_md": "A **proportion** is an equation stating that two ratios are equal.\n\nExample: 1/2 = 3/6 is a proportion because both ratios represent the same relationship.\n\nWe can use proportions to solve problems. If you know 3 apples cost $2, how much do 9 apples cost? \n3 apples / $2 = 9 apples / $x", "interactive_element": {"type": "text_input", "question_text": "Solve for x: 2/5 = 8/x", "correct_answer_regex": "^20$", "feedback_correct": "Correct! To get from 2 to 8, you multiply by 4. So, 5 × 4 = 20.", "feedback_incorrect": "Think: what do you multiply 2 by to get 8? Do the same to 5.", "action_button_text": "Visual Ratios"}}}, {"id": "aor-screen5-visual-comparisons", "type": "lesson_screen", "order": 5, "estimatedTimeSeconds": 90, "content": {"headline": "Visualizing Ratios: Bar Models", "body_md": "Bar models can help visualize ratios.\nIf the ratio of red to blue marbles is 2:3, you can draw:\nRed: [][]\nBlue: [][][]\n\nIf there are 10 red marbles, each [] represents 5 marbles. So there are 3 × 5 = 15 blue marbles.", "visual": {"type": "interactive_ratio_visualizer", "title": "Ratio Visualizer", "description": "Adjust the numerator and denominator to see different ratio visualizations.", "numerator": 2, "denominator": 3, "visualization_type": "Blocks", "primary_color": "#F44336", "secondary_color": "#2196F3", "show_decimal": true, "show_percentage": true, "show_fraction": true}, "interactive_element": {"type": "button", "text": "Real World Ratios", "action": "next_screen"}}}, {"id": "aor-screen6-real-world", "type": "lesson_screen", "order": 6, "estimatedTimeSeconds": 60, "content": {"headline": "Ratios in Action!", "body_md": "Ratios are used in:\n*   **Cooking:** Ingredient proportions.\n*   **Maps:** Scale (e.g., 1 inch : 10 miles).\n*   **Mixing paints:** To get the right color shade.\n*   **Finance:** Debt-to-income ratio.", "visual": {"type": "giphy_search", "value": "cooking recipe ingredients"}, "interactive_element": {"type": "button", "text": "I Get It!", "action": "next_screen"}}}, {"id": "aor-screen7-recap", "type": "lesson_screen", "order": 7, "estimatedTimeSeconds": 40, "content": {"headline": "Recap: The Art of Ratios", "body_md": "*   Ratios compare two quantities.\n*   They can be simplified like fractions.\n*   Proportions state that two ratios are equal.\n*   Visuals like bar models can help understand them.", "interactive_element": {"type": "button", "text": "Next Lesson", "action": "next_lesson"}}}]}, {"id": "visualizing-data", "title": "Visualizing Data", "description": "Interpret information presented in charts and graphs effectively.", "order": 4, "type": "interactive_lesson", "estimatedTimeMinutes": 10, "contentBlocks": [{"id": "vd-screen1-intro", "type": "lesson_screen", "order": 1, "estimatedTimeSeconds": 30, "content": {"headline": "Pictures of Information: Charts & Graphs", "body_md": "Data can be overwhelming! Charts and graphs turn numbers into pictures, making it easier to spot trends, comparisons, and insights.", "visual": {"type": "giphy_search", "value": "data visualization charts"}, "interactive_element": {"type": "button", "text": "Show Me the Visuals!", "action": "next_screen"}}}, {"id": "vd-screen2-bar-charts", "type": "lesson_screen", "order": 2, "estimatedTimeSeconds": 80, "content": {"headline": "Bar Charts: Comparing Categories", "body_md": "**Bar charts** use rectangular bars to compare values across different categories. The length of the bar represents the value.\n\nGreat for showing things like favorite pets, votes for class president, or sales per month.", "visual": {"type": "local_asset", "value": "assets/images/graphs/bar_chart_example.svg"}, "interactive_element": {"type": "chart_interpretation_mcq", "chart_type": "bar_chart", "data": {"categories": ["A", "B", "C"], "values": [10, 15, 7]}, "question_text": "In the bar chart shown (A=10, B=15, C=7), which category has the highest value?", "options": [{"id": "vd2opt1", "text": "Category A", "is_correct": false}, {"id": "vd2opt2", "text": "Category B", "is_correct": true}, {"id": "vd2opt3", "text": "Category C", "is_correct": false}], "feedback_correct": "Correct! Category B has the tallest bar.", "action_button_text": "Pie Charts Next!"}}}, {"id": "vd-screen3-pie-charts", "type": "lesson_screen", "order": 3, "estimatedTimeSeconds": 80, "content": {"headline": "Pie Charts: Parts of a Whole", "body_md": "**Pie charts** (or circle graphs) show how a whole is divided into parts. Each 'slice' represents a percentage or proportion.\n\nUseful for budgets (how money is spent), survey results (percentage of opinions), or time allocation.", "visual": {"type": "local_asset", "value": "assets/images/graphs/pie_chart_example.svg"}, "interactive_element": {"type": "chart_interpretation_mcq", "chart_type": "pie_chart", "data": {"categories": ["Work", "Sleep", "Fun"], "values": [8, 8, 8]}, "question_text": "If a pie chart shows daily activities, and 'Sleep' takes up 1/3 of the circle, how many hours is that?", "options": [{"id": "vd3opt1", "text": "6 hours", "is_correct": false}, {"id": "vd3opt2", "text": "8 hours", "is_correct": true}, {"id": "vd3opt3", "text": "12 hours", "is_correct": false}], "feedback_correct": "Correct! 1/3 of 24 hours is 8 hours.", "action_button_text": "Line Graphs!"}}}, {"id": "vd-screen4-line-graphs", "type": "lesson_screen", "order": 4, "estimatedTimeSeconds": 90, "content": {"headline": "Line Graphs: Showing Trends Over Time", "body_md": "**Line graphs** display data points connected by lines, typically showing how something changes over time.\n\nPerfect for tracking temperature changes, stock prices, or website visitors over days/months/years.", "visual": {"type": "local_asset", "value": "assets/images/graphs/line_graph_example.svg"}, "interactive_element": {"type": "chart_interpretation_mcq", "chart_type": "line_graph", "data": {"time_points": ["Mon", "<PERSON><PERSON>", "Wed", "<PERSON>hu", "<PERSON><PERSON>"], "values": [10, 12, 9, 15, 14]}, "question_text": "Looking at a line graph of temperature over a week, if the line goes sharply upwards from Wednesday to Thursday, what does it mean?", "options": [{"id": "vd4opt1", "text": "Temperature dropped significantly.", "is_correct": false}, {"id": "vd4opt2", "text": "Temperature rose significantly.", "is_correct": true}, {"id": "vd4opt3", "text": "Temperature stayed the same.", "is_correct": false}], "feedback_correct": "Correct! An upward slope indicates an increase.", "action_button_text": "Choosing the Right Chart"}}}, {"id": "vd-screen5-choosing-charts", "type": "lesson_screen", "order": 5, "estimatedTimeSeconds": 70, "content": {"headline": "Which Chart to Use?", "body_md": "*   **Comparing categories?** Use a Bar Chart.\n*   **Showing parts of a whole?** Use a Pie Chart.\n*   **Tracking changes over time?** Use a Line Graph.", "interactive_element": {"type": "multiple_choice_text", "question_text": "To show the percentage of students who prefer different school subjects, which chart is best?", "options": [{"id": "vd5opt1", "text": "Bar Chart", "is_correct": false, "feedback_incorrect": "A bar chart could work, but a pie chart is often better for percentages of a whole."}, {"id": "vd5opt2", "text": "Pie Chart", "is_correct": true, "feedback_correct": "Yes! Pie charts excel at showing parts of a whole.", "feedback_incorrect": "Think about representing percentages."}, {"id": "vd5opt3", "text": "Line Graph", "is_correct": false, "feedback_incorrect": "Line graphs are best for trends over time."}], "action_button_text": "Data Storytelling"}}}, {"id": "vd-screen6-interactive-data-viz", "type": "lesson_screen", "order": 6, "estimatedTimeSeconds": 120, "content": {"headline": "Interactive Data Visualization", "body_md": "Let's explore data visualization hands-on! Use this interactive tool to visualize sample data in different ways.\n\nTry switching between chart types to see how the same data can tell different stories.", "visual": {"type": "interactive_data_visualization_tool", "title": "Data Visualization Explorer", "description": "Explore different ways to visualize the same dataset", "primaryColor": "#2196F3", "secondaryColor": "#FF9800", "accentColor": "#4CAF50", "backgroundColor": "#FFFFFF", "textColor": "#212121", "dataSets": [{"title": "Student Test Scores", "description": "Test scores for a class of 5 students", "xAxisLabel": "Student", "yAxisLabel": "Score", "dataPoints": [{"x": "<PERSON>", "y": 85}, {"x": "<PERSON>", "y": 92}, {"x": "<PERSON>", "y": 78}, {"x": "<PERSON>", "y": 88}, {"x": "<PERSON>", "y": 95}]}, {"title": "Monthly Rainfall", "description": "Average rainfall by month (in mm)", "xAxisLabel": "Month", "yAxisLabel": "Rainfall (mm)", "dataPoints": [{"x": "Jan", "y": 45}, {"x": "Feb", "y": 38}, {"x": "Mar", "y": 65}, {"x": "Apr", "y": 78}, {"x": "May", "y": 91}, {"x": "Jun", "y": 52}]}], "visualizationTypes": ["Bar Chart", "Line Chart", "Pie Chart", "Scatter Plot"], "defaultVisualizationType": "Bar Chart", "showStatistics": true, "allowDataEditing": true}, "interactive_element": {"type": "multiple_choice", "question": "Which chart type is best for showing trends over time?", "options": ["Bar Chart", "Line Chart", "Pie Chart", "Scatter Plot"], "correct_option": 1, "feedback_correct": "Correct! Line charts are excellent for showing how values change over time.", "feedback_incorrect": "Not quite. Line charts are typically best for showing trends over time because they visually connect data points in sequence."}}}, {"id": "vd-screen7-data-storytelling", "type": "lesson_screen", "order": 7, "estimatedTimeSeconds": 50, "content": {"headline": "Telling Stories with Data", "body_md": "Good data visualization isn't just about making charts; it's about telling a clear story with your data. Choose the right chart, label clearly, and highlight key insights!", "visual": {"type": "giphy_search", "value": "storytelling book animation"}, "interactive_element": {"type": "button", "text": "Continue", "action": "next_screen"}}}, {"id": "vd-screen8-recap", "type": "lesson_screen", "order": 8, "estimatedTimeSeconds": 40, "content": {"headline": "Recap: Visualizing Data", "body_md": "*   Bar charts compare categories.\n*   Pie charts show parts of a whole.\n*   Line graphs track trends over time.\n*   Choose the right chart to tell a clear data story.", "interactive_element": {"type": "button", "text": "Next Lesson", "action": "next_lesson"}}}]}, {"id": "introduction-to-functions", "title": "Introduction to Functions", "description": "See how inputs transform into outputs through interactive diagrams.", "order": 5, "type": "interactive_lesson", "estimatedTimeMinutes": 10, "contentBlocks": [{"id": "itf-screen1-intro", "type": "lesson_screen", "order": 1, "estimatedTimeSeconds": 30, "content": {"headline": "Functions: The Magic Input-Output Machines!", "body_md": "Imagine a machine: you put something in (input), it does something, and something else comes out (output). That's a function! They're a core concept in math and programming.", "visual": {"type": "giphy_search", "value": "machine input output gears"}, "interactive_element": {"type": "button", "text": "How Do They Work?", "action": "next_screen"}}}, {"id": "itf-screen2-what-is-function", "type": "lesson_screen", "order": 2, "estimatedTimeSeconds": 70, "content": {"headline": "What is a Function?", "body_md": "A **function** is a rule that assigns to each input value exactly one output value.\n\nThink of it like a recipe:\n*   **Input:** Ingredients (e.g., flour, sugar, eggs)\n*   **Function (Rule):** The recipe instructions\n*   **Output:** The cake!", "visual": {"type": "local_asset", "value": "assets/images/functions/function_machine_diagram.svg"}, "interactive_element": {"type": "multiple_choice_text", "question_text": "If a function gives two different outputs for the same input, is it still a valid function?", "options": [{"id": "itf2opt1", "text": "Yes, as long as it produces an output.", "is_correct": false, "feedback_incorrect": "A key rule of functions is *exactly one* output for each input."}, {"id": "itf2opt2", "text": "No, each input must have only one unique output.", "is_correct": true, "feedback_correct": "Correct! That's the definition of a function.", "feedback_incorrect": "Think about the 'exactly one output' rule."}], "action_button_text": "Function Notation"}}}, {"id": "itf-screen3-function-notation", "type": "lesson_screen", "order": 3, "estimatedTimeSeconds": 70, "content": {"headline": "Function Notation: f(x)", "body_md": "We often use notation like **f(x)** (read \"f of x\") to represent a function.\n*   'f' is the name of the function (the rule).\n*   'x' is the input variable.\n*   f(x) is the output value for the input x.\n\nExample: If f(x) = x + 2\n*   f(3) = 3 + 2 = 5  (Input 3, Output 5)\n*   f(10) = 10 + 2 = 12 (Input 10, Output 12)", "interactive_element": {"type": "text_input", "question_text": "If g(x) = 3x - 1, what is g(4)?", "correct_answer_regex": "^11$", "feedback_correct": "Spot on! g(4) = (3 × 4) - 1 = 12 - 1 = 11.", "feedback_incorrect": "Substitute 4 for x in the rule 3x - 1.", "action_button_text": "Visualizing Functions"}}}, {"id": "itf-screen4-function-machine-interactive", "type": "lesson_screen", "order": 4, "estimatedTimeSeconds": 100, "content": {"headline": "Interactive Function Machine!", "body_md": "Let's play with a function machine! The rule is: **Output = 2 × Input + 3**\n\nEnter an input number and see what comes out.", "visual": {"type": "interactive_function_machine", "title": "Function Machine", "description": "Enter an input value and see what comes out based on the function rule.", "function_rule_display": "2x + 3", "function_logic": "output = 2 * input + 3", "primary_color": "#2196F3", "secondary_color": "#FF9800", "accent_color": "#4CAF50", "background_color": "#FFFFFF", "text_color": "#212121"}, "interactive_element": {"type": "button", "text": "Domain & Range", "action": "next_screen"}}}, {"id": "itf-screen5-domain-range", "type": "lesson_screen", "order": 5, "estimatedTimeSeconds": 70, "content": {"headline": "Domain and Range", "body_md": "*   **Domain:** The set of all possible input values (x-values) for a function.\n*   **Range:** The set of all possible output values (y-values or f(x)-values) that the function can produce.\n\nFor f(x) = x + 2, if the domain is {1, 2, 3}, the range is {3, 4, 5}.", "visual": {"type": "unsplash_search", "value": "input output arrows"}, "interactive_element": {"type": "button", "text": "Real World Functions", "action": "next_screen"}}}, {"id": "itf-screen6-real-world-examples", "type": "lesson_screen", "order": 6, "estimatedTimeSeconds": 60, "content": {"headline": "Functions in Everyday Life", "body_md": "*   A vending machine: Input money (and selection), Output snack.\n*   Converting Celsius to Fahrenheit: Input Celsius temp, Output Fahrenheit temp.\n*   Calculating distance: Input speed and time, Output distance (d = s × t).", "visual": {"type": "giphy_search", "value": "vending machine working"}, "interactive_element": {"type": "button", "text": "Got It!", "action": "next_screen"}}}, {"id": "itf-screen7-recap", "type": "lesson_screen", "order": 7, "estimatedTimeSeconds": 40, "content": {"headline": "Recap: Understanding Functions", "body_md": "*   Functions are input-output rules (one output per input).\n*   Notation: f(x).\n*   Domain = possible inputs; Range = possible outputs.\n*   Functions model many real-world relationships.", "interactive_element": {"type": "button", "text": "On to the Module Test!", "action": "next_lesson"}}}]}, {"id": "pattern-prediction-puzzle-test", "title": "Pattern Prediction Puzzle", "description": "Identify and extend visual patterns and analyze relationships between quantities.", "order": 6, "type": "module_test_interactive", "estimatedTimeMinutes": 10, "contentBlocks": [{"id": "ppp-q0-intro", "type": "question_screen", "order": 1, "estimatedTimeSeconds": 20, "content": {"headline": "Pattern Prediction Puzzle: Engage!", "body_md": "Time to flex those pattern-spotting muscles! Analyze the sequences and relationships to solve these puzzles.", "visual": {"type": "giphy_search", "value": "puzzle brain teaser"}, "interactive_element": {"type": "button", "text": "Start Puzzling!", "action": "next_screen"}}}, {"id": "ppp-q1-growing-visual", "type": "question_screen", "order": 2, "estimatedTimeSeconds": 120, "content": {"headline": "Puzzle 1: <PERSON><PERSON>", "body_md": "Observe the pattern of dots:\nTerm 1: ●\nTerm 2: ● ● ●\nTerm 3: ● ● ● ● ●\n(1 dot, then 3 dots, then 5 dots...)\n\nHow many dots will be in Term 5?", "visual": {"type": "local_asset", "value": "assets/images/patterns/growing_dots_odd.svg"}, "interactive_element": {"type": "text_input", "placeholder": "Number of dots in Term 5", "correct_answer_regex": "^9$", "feedback_correct": "Correct! The pattern is 1, 3, 5, 7, 9 (adding 2 each time, or 2n-1).", "feedback_incorrect": "Look at how many dots are added each term. Term 4 would have 7 dots.", "action_button_text": "Next Puzzle"}}}, {"id": "ppp-q2-relationship-graph", "type": "question_screen", "order": 3, "estimatedTimeSeconds": 150, "content": {"headline": "Puzzle 2: Graphing a Relationship", "body_md": "A car travels at a constant speed of 60 km/hour.\nLet 't' be time in hours (independent variable) and 'd' be distance in km (dependent variable).\nWhich graph best represents the relationship d = 60t for t from 0 to 3 hours?", "visual": {"type": "interactive_graph_selection_game", "options": [{"id": "graphA", "image_src": "assets/images/graphs/graph_d_vs_t_positive_linear.svg", "is_correct": true}, {"id": "graphB", "image_src": "assets/images/graphs/graph_d_vs_t_curve_up.svg", "is_correct": false}, {"id": "graphC", "image_src": "assets/images/graphs/graph_d_vs_t_flat.svg", "is_correct": false}]}, "interactive_element": {"type": "multiple_choice_image_from_visual", "question_text": "Select the correct graph:", "correct_option_id_from_visual": "graphA", "feedback_correct": "Correct! It's a straight line with a positive slope, starting from (0,0).", "feedback_incorrect": "Constant speed means distance increases linearly with time.", "action_button_text": "<PERSON>io Riddle!"}}}, {"id": "ppp-q3-ratio-and-proportion", "type": "question_screen", "order": 4, "estimatedTimeSeconds": 150, "content": {"headline": "Puzzle 3: <PERSON><PERSON><PERSON>", "body_md": "A recipe for cookies requires 2 cups of flour for every 1 cup of sugar. This is a ratio of 2:1 (flour to sugar).\nIf you want to make a bigger batch using 5 cups of sugar, how many cups of flour will you need?", "interactive_element": {"type": "text_input", "placeholder": "Cups of flour", "correct_answer_regex": "^10$", "feedback_correct": "Exactly! If 1 cup sugar needs 2 cups flour, then 5 cups sugar needs 5 × 2 = 10 cups flour.", "feedback_incorrect": "The ratio of flour to sugar is 2:1. If sugar is 5 cups, what's flour?", "action_button_text": "Finish Puzzle"}}}, {"id": "ppp-q4-end", "type": "lesson_screen", "order": 5, "estimatedTimeSeconds": 30, "content": {"headline": "Pattern Puzzle Master!", "body_md": "You've successfully decoded the patterns and relationships! Your analytical skills are top-notch.", "visual": {"type": "giphy_search", "value": "brain puzzle solved success"}, "interactive_element": {"type": "button", "text": "Back to Course Overview", "action": "module_complete"}}}]}]}