import 'package:flutter/material.dart';
import 'dart:math' as math;
import 'dart:ui';

class InteractiveRelationshipMapperWidget extends StatefulWidget {
  final Map<String, dynamic> data;

  const InteractiveRelationshipMapperWidget({
    Key? key,
    required this.data,
  }) : super(key: key);

  factory InteractiveRelationshipMapperWidget.fromData(Map<String, dynamic> data) {
    return InteractiveRelationshipMapperWidget(data: data);
  }

  @override
  _InteractiveRelationshipMapperWidgetState createState() =>
      _InteractiveRelationshipMapperWidgetState();
}

class _InteractiveRelationshipMapperWidgetState
    extends State<InteractiveRelationshipMapperWidget> {
  late String _title;
  late List<String> _relationshipTypes;
  late String _selectedRelationshipType;
  late List<Point> _points;
  late bool _showLine;
  late bool _showEquation;
  late bool _showCoordinates;
  late bool _isEditable;
  late bool _showNameTag;
  late Color _primaryColor;
  late Color _secondaryColor;
  late double _minX;
  late double _maxX;
  late double _minY;
  late double _maxY;
  late double _stepX;
  late double _stepY;
  late TextEditingController _equationController;
  late String _currentEquation;
  late String _relationshipDescription;

  @override
  void initState() {
    super.initState();
    _title = widget.data['title'] ?? 'Relationship Mapper';
    _relationshipTypes = List<String>.from(widget.data['relationshipTypes'] ?? [
      'Linear',
      'Quadratic',
      'Exponential',
      'Inverse',
      'Custom',
    ]);
    _selectedRelationshipType = widget.data['defaultRelationshipType'] ?? _relationshipTypes[0];
    _showLine = widget.data['showLine'] ?? true;
    _showEquation = widget.data['showEquation'] ?? true;
    _showCoordinates = widget.data['showCoordinates'] ?? true;
    _isEditable = widget.data['isEditable'] ?? true;
    _showNameTag = widget.data['showNameTag'] ?? true;
    _primaryColor = _parseColor(widget.data['primaryColor'] ?? '#2196F3');
    _secondaryColor = _parseColor(widget.data['secondaryColor'] ?? '#FF5722');
    _minX = widget.data['minX']?.toDouble() ?? -10.0;
    _maxX = widget.data['maxX']?.toDouble() ?? 10.0;
    _minY = widget.data['minY']?.toDouble() ?? -10.0;
    _maxY = widget.data['maxY']?.toDouble() ?? 10.0;
    _stepX = widget.data['stepX']?.toDouble() ?? 1.0;
    _stepY = widget.data['stepY']?.toDouble() ?? 1.0;
    _equationController = TextEditingController(text: 'y = 2x + 1');
    _currentEquation = widget.data['defaultEquation'] ?? 'y = 2x + 1';
    _relationshipDescription = '';

    _generatePoints();
    _updateRelationshipDescription();
  }

  @override
  void dispose() {
    _equationController.dispose();
    super.dispose();
  }

  Color _parseColor(String colorString) {
    if (colorString.startsWith('#')) {
      return Color(int.parse(colorString.substring(1, 7), radix: 16) + 0xFF000000);
    }
    return Colors.blue;
  }

  void _generatePoints() {
    _points = [];
    double step = (_maxX - _minX) / 20;

    for (double x = _minX; x <= _maxX; x += step) {
      double y = _calculateY(x);
      if (y >= _minY && y <= _maxY) {
        _points.add(Point(x, y));
      }
    }
  }

  double _calculateY(double x) {
    switch (_selectedRelationshipType) {
      case 'Linear':
        // y = mx + b
        double m = widget.data['linearSlope'] ?? 2.0;
        double b = widget.data['linearIntercept'] ?? 1.0;
        return m * x + b;

      case 'Quadratic':
        // y = ax² + bx + c
        double a = widget.data['quadraticA'] ?? 1.0;
        double b = widget.data['quadraticB'] ?? 0.0;
        double c = widget.data['quadraticC'] ?? 0.0;
        return a * x * x + b * x + c;

      case 'Exponential':
        // y = a * b^x
        double a = widget.data['exponentialA'] ?? 1.0;
        double b = widget.data['exponentialB'] ?? 2.0;
        return a * math.pow(b, x);

      case 'Inverse':
        // y = a / x
        double a = widget.data['inverseA'] ?? 1.0;
        if (x == 0) return x > 0 ? _maxY : _minY; // Handle division by zero
        return a / x;

      case 'Custom':
        // Try to parse the custom equation
        try {
          // This is a simplified parser for demonstration
          // In a real app, you'd want a more robust equation parser
          String equation = _currentEquation.toLowerCase();
          if (equation.contains('y=') || equation.contains('y =')) {
            equation = equation.replaceAll('y=', '').replaceAll('y =', '').trim();

            // Handle simple linear equations like "mx + b"
            if (equation.contains('x')) {
              if (equation.contains('+')) {
                List<String> parts = equation.split('+');
                double m = 1.0;
                if (parts[0].contains('x')) {
                  m = parts[0].replaceAll('x', '').trim().isEmpty
                      ? 1.0
                      : double.tryParse(parts[0].replaceAll('x', '').trim()) ?? 1.0;
                }
                double b = double.tryParse(parts[1].trim()) ?? 0.0;
                return m * x + b;
              } else if (equation.contains('-')) {
                // Simple handling for negative intercept
                List<String> parts = equation.split('-');
                double m = 1.0;
                if (parts[0].contains('x')) {
                  m = parts[0].replaceAll('x', '').trim().isEmpty
                      ? 1.0
                      : double.tryParse(parts[0].replaceAll('x', '').trim()) ?? 1.0;
                }
                double b = double.tryParse(parts[1].trim()) ?? 0.0;
                return m * x - b;
              } else {
                // Just mx
                double m = equation.replaceAll('x', '').trim().isEmpty
                    ? 1.0
                    : double.tryParse(equation.replaceAll('x', '').trim()) ?? 1.0;
                return m * x;
              }
            } else {
              // Just a constant
              return double.tryParse(equation) ?? 0.0;
            }
          }
        } catch (e) {
          // If parsing fails, return a default value
          return 0.0;
        }
        return 0.0;

      default:
        return 0.0;
    }
  }

  void _updateRelationshipDescription() {
    switch (_selectedRelationshipType) {
      case 'Linear':
        double m = widget.data['linearSlope'] ?? 2.0;
        double b = widget.data['linearIntercept'] ?? 1.0;
        _relationshipDescription = 'y = ${m}x + $b\n'
            'This is a linear relationship where y changes at a constant rate of $m units for each unit increase in x.';
        _currentEquation = 'y = ${m}x + $b';
        break;

      case 'Quadratic':
        double a = widget.data['quadraticA'] ?? 1.0;
        double b = widget.data['quadraticB'] ?? 0.0;
        double c = widget.data['quadraticC'] ?? 0.0;
        _relationshipDescription = 'y = ${a}x² + ${b}x + $c\n'
            'This is a quadratic relationship forming a parabola.';
        _currentEquation = 'y = ${a}x² + ${b}x + $c';
        break;

      case 'Exponential':
        double a = widget.data['exponentialA'] ?? 1.0;
        double b = widget.data['exponentialB'] ?? 2.0;
        _relationshipDescription = 'y = $a × $b^x\n'
            'This is an exponential relationship where y grows or decays by a factor of $b for each unit increase in x.';
        _currentEquation = 'y = $a × $b^x';
        break;

      case 'Inverse':
        double a = widget.data['inverseA'] ?? 1.0;
        _relationshipDescription = 'y = $a / x\n'
            'This is an inverse relationship where y is inversely proportional to x.';
        _currentEquation = 'y = $a / x';
        break;

      case 'Custom':
        _relationshipDescription = 'Custom equation: $_currentEquation';
        break;
    }

    if (_selectedRelationshipType != 'Custom') {
      _equationController.text = _currentEquation;
    }
  }

  void _onRelationshipTypeChanged(String? newValue) {
    if (newValue != null) {
      setState(() {
        _selectedRelationshipType = newValue;
        _updateRelationshipDescription();
        _generatePoints();
      });
    }
  }

  void _onEquationChanged(String newEquation) {
    setState(() {
      _currentEquation = newEquation;
      _generatePoints();
      _updateRelationshipDescription();
    });
  }

  @override
  Widget build(BuildContext context) {
    return Card(
      elevation: 4,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            if (_showNameTag)
              Padding(
                padding: const EdgeInsets.only(bottom: 8.0),
                child: Text(
                  _title,
                  style: Theme.of(context).textTheme.titleLarge,
                ),
              ),

            // Relationship type selector
            DropdownButton<String>(
              value: _selectedRelationshipType,
              isExpanded: true,
              onChanged: _onRelationshipTypeChanged,
              items: _relationshipTypes.map<DropdownMenuItem<String>>((String value) {
                return DropdownMenuItem<String>(
                  value: value,
                  child: Text(value),
                );
              }).toList(),
            ),

            const SizedBox(height: 16),

            // Equation input (only for custom)
            if (_selectedRelationshipType == 'Custom')
              TextField(
                controller: _equationController,
                decoration: const InputDecoration(
                  labelText: 'Enter equation (e.g., y = 2x + 1)',
                  border: OutlineInputBorder(),
                ),
                onChanged: _onEquationChanged,
              ),

            if (_selectedRelationshipType == 'Custom')
              const SizedBox(height: 16),

            // Relationship description
            Container(
              padding: const EdgeInsets.all(8),
              decoration: BoxDecoration(
                color: Colors.grey[200],
                borderRadius: BorderRadius.circular(8),
              ),
              child: Text(_relationshipDescription),
            ),

            const SizedBox(height: 16),

            // Coordinate plane visualization
            Container(
              height: 300,
              decoration: BoxDecoration(
                border: Border.all(color: Colors.grey),
                borderRadius: BorderRadius.circular(8),
              ),
              child: ClipRRect(
                borderRadius: BorderRadius.circular(8),
                child: CustomPaint(
                  size: const Size(double.infinity, double.infinity),
                  painter: CoordinatePlanePainter(
                    points: _points,
                    minX: _minX,
                    maxX: _maxX,
                    minY: _minY,
                    maxY: _maxY,
                    stepX: _stepX,
                    stepY: _stepY,
                    showLine: _showLine,
                    showCoordinates: _showCoordinates,
                    primaryColor: _primaryColor,
                    secondaryColor: _secondaryColor,
                  ),
                ),
              ),
            ),

            const SizedBox(height: 16),

            // Controls
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceEvenly,
              children: [
                ToggleButtons(
                  isSelected: [_showLine, _showCoordinates],
                  onPressed: (int index) {
                    setState(() {
                      if (index == 0) {
                        _showLine = !_showLine;
                      } else if (index == 1) {
                        _showCoordinates = !_showCoordinates;
                      }
                    });
                  },
                  children: const [
                    Padding(
                      padding: EdgeInsets.symmetric(horizontal: 8.0),
                      child: Text('Line'),
                    ),
                    Padding(
                      padding: EdgeInsets.symmetric(horizontal: 8.0),
                      child: Text('Coordinates'),
                    ),
                  ],
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }
}

class Point {
  final double x;
  final double y;

  Point(this.x, this.y);
}

class CoordinatePlanePainter extends CustomPainter {
  final List<Point> points;
  final double minX;
  final double maxX;
  final double minY;
  final double maxY;
  final double stepX;
  final double stepY;
  final bool showLine;
  final bool showCoordinates;
  final Color primaryColor;
  final Color secondaryColor;

  CoordinatePlanePainter({
    required this.points,
    required this.minX,
    required this.maxX,
    required this.minY,
    required this.maxY,
    required this.stepX,
    required this.stepY,
    required this.showLine,
    required this.showCoordinates,
    required this.primaryColor,
    required this.secondaryColor,
  });

  @override
  void paint(Canvas canvas, Size size) {
    final Paint gridPaint = Paint()
      ..color = Colors.grey[300]!
      ..strokeWidth = 1.0;

    final Paint axisPaint = Paint()
      ..color = Colors.black
      ..strokeWidth = 2.0;

    final Paint pointPaint = Paint()
      ..color = secondaryColor
      ..strokeWidth = 8.0
      ..strokeCap = StrokeCap.round;

    final Paint linePaint = Paint()
      ..color = primaryColor
      ..strokeWidth = 3.0;

    final double xScale = size.width / (maxX - minX);
    final double yScale = size.height / (maxY - minY);

    // Function to convert from math coordinates to screen coordinates
    Offset toScreen(double x, double y) {
      return Offset(
        (x - minX) * xScale,
        size.height - (y - minY) * yScale,
      );
    }

    // Draw grid lines
    for (double x = minX; x <= maxX; x += stepX) {
      final p1 = toScreen(x, minY);
      final p2 = toScreen(x, maxY);
      canvas.drawLine(p1, p2, gridPaint);
    }

    for (double y = minY; y <= maxY; y += stepY) {
      final p1 = toScreen(minX, y);
      final p2 = toScreen(maxX, y);
      canvas.drawLine(p1, p2, gridPaint);
    }

    // Draw axes
    final originScreen = toScreen(0, 0);
    canvas.drawLine(Offset(0, originScreen.dy), Offset(size.width, originScreen.dy), axisPaint);
    canvas.drawLine(Offset(originScreen.dx, 0), Offset(originScreen.dx, size.height), axisPaint);

    // Draw points and connecting line
    if (points.isNotEmpty) {
      final Path path = Path();
      final firstPoint = toScreen(points.first.x, points.first.y);
      path.moveTo(firstPoint.dx, firstPoint.dy);

      for (int i = 1; i < points.length; i++) {
        final screenPoint = toScreen(points[i].x, points[i].y);
        path.lineTo(screenPoint.dx, screenPoint.dy);
      }

      if (showLine) {
        canvas.drawPath(path, linePaint);
      }

      if (showCoordinates) {
        // Draw a subset of points to avoid overcrowding
        int step = (points.length / 10).ceil();
        step = step < 1 ? 1 : step;

        for (int i = 0; i < points.length; i += step) {
          final screenPoint = toScreen(points[i].x, points[i].y);
          canvas.drawPoints(PointMode.points, [screenPoint], pointPaint);

          // Draw coordinate labels for selected points
          if (i % (step * 2) == 0) {
            final TextPainter textPainter = TextPainter(
              text: TextSpan(
                text: '(${points[i].x.toStringAsFixed(1)}, ${points[i].y.toStringAsFixed(1)})',
                style: const TextStyle(color: Colors.black87, fontSize: 10),
              ),
              textDirection: TextDirection.ltr,
            );
            textPainter.layout();
            textPainter.paint(canvas, screenPoint.translate(5, -15));
          }
        }
      }
    }
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => true;
}
