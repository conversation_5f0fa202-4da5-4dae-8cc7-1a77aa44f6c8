import 'package:flutter/material.dart';
import 'dart:math' as math;
import 'dart:async';

/// A widget that visualizes different sorting algorithms
class InteractiveSortingAlgorithmVisualizerWidget extends StatefulWidget {
  /// The data for this widget
  final Map<String, dynamic> data;

  /// Optional callback when the widget state changes
  final Function(bool isCompleted)? onStateChanged;

  const InteractiveSortingAlgorithmVisualizerWidget({
    super.key,
    required this.data,
    this.onStateChanged,
  });

  /// Factory constructor to create the widget from JSON data
  factory InteractiveSortingAlgorithmVisualizerWidget.fromData(Map<String, dynamic> data) {
    return InteractiveSortingAlgorithmVisualizerWidget(
      data: data,
    );
  }

  @override
  State<InteractiveSortingAlgorithmVisualizerWidget> createState() => _InteractiveSortingAlgorithmVisualizerWidgetState();
}

class _InteractiveSortingAlgorithmVisualizerWidgetState extends State<InteractiveSortingAlgorithmVisualizerWidget> {
  // Sorting parameters
  List<int> _array = [];
  int _arraySize = 20;
  String _algorithm = 'bubble';
  double _sortingSpeed = 0.5; // 0.0 to 1.0
  
  // Sorting state
  bool _isSorting = false;
  bool _isSorted = false;
  int _currentIndex = -1;
  int _compareIndex = -1;
  int _sortedUpToIndex = -1;
  List<int> _originalArray = [];
  
  // Sorting statistics
  int _comparisons = 0;
  int _swaps = 0;
  int _steps = 0;
  
  // Timer for controlling animation speed
  Timer? _sortingTimer;
  
  // UI parameters
  Color _primaryColor = Colors.blue;
  Color _secondaryColor = Colors.orange;
  Color _accentColor = Colors.green;
  Color _textColor = Colors.black87;
  Color _currentIndexColor = Colors.red;
  Color _compareIndexColor = Colors.purple;
  Color _sortedColor = Colors.green;
  
  // Challenge mode parameters
  bool _challengeMode = false;
  Map<String, dynamic>? _currentChallenge;
  bool _challengeCompleted = false;
  
  // Available sorting algorithms
  final Map<String, String> _algorithms = {
    'bubble': 'Bubble Sort',
    'selection': 'Selection Sort',
    'insertion': 'Insertion Sort',
    'quick': 'Quick Sort',
    'merge': 'Merge Sort',
  };
  
  @override
  void initState() {
    super.initState();
    _initializeFromData();
    _generateRandomArray();
  }
  
  @override
  void dispose() {
    _stopSorting();
    super.dispose();
  }
  
  void _initializeFromData() {
    // Initialize parameters from widget data
    _arraySize = widget.data['initialArraySize'] ?? 20;
    _algorithm = widget.data['initialAlgorithm'] ?? 'bubble';
    _sortingSpeed = widget.data['initialSpeed']?.toDouble() ?? 0.5;
    
    _challengeMode = widget.data['challengeMode'] ?? false;
    if (_challengeMode && widget.data['challenges'] != null) {
      _currentChallenge = widget.data['challenges'][0];
    }
    
    // Initialize colors
    if (widget.data['primaryColor'] != null) {
      _primaryColor = _colorFromHex(widget.data['primaryColor']);
    }
    if (widget.data['secondaryColor'] != null) {
      _secondaryColor = _colorFromHex(widget.data['secondaryColor']);
    }
    if (widget.data['accentColor'] != null) {
      _accentColor = _colorFromHex(widget.data['accentColor']);
    }
    if (widget.data['textColor'] != null) {
      _textColor = _colorFromHex(widget.data['textColor']);
    }
    if (widget.data['currentIndexColor'] != null) {
      _currentIndexColor = _colorFromHex(widget.data['currentIndexColor']);
    }
    if (widget.data['compareIndexColor'] != null) {
      _compareIndexColor = _colorFromHex(widget.data['compareIndexColor']);
    }
    if (widget.data['sortedColor'] != null) {
      _sortedColor = _colorFromHex(widget.data['sortedColor']);
    }
  }
  
  // Helper method to convert hex color string to Color
  Color _colorFromHex(String hexColor) {
    hexColor = hexColor.replaceAll('#', '');
    if (hexColor.length == 6) {
      hexColor = 'FF$hexColor';
    }
    return Color(int.parse(hexColor, radix: 16));
  }
  
  void _generateRandomArray() {
    final random = math.Random();
    _array = List.generate(_arraySize, (_) => random.nextInt(100) + 1);
    _originalArray = List.from(_array);
    _resetSortingState();
  }
  
  void _resetSortingState() {
    setState(() {
      _isSorting = false;
      _isSorted = false;
      _currentIndex = -1;
      _compareIndex = -1;
      _sortedUpToIndex = -1;
      _comparisons = 0;
      _swaps = 0;
      _steps = 0;
      
      if (_challengeMode) {
        _challengeCompleted = false;
        if (widget.onStateChanged != null) {
          widget.onStateChanged!(false);
        }
      }
    });
  }
  
  void _resetArray() {
    setState(() {
      _array = List.from(_originalArray);
      _resetSortingState();
    });
  }
  
  void _startSorting() {
    if (_isSorting || _isSorted) return;
    
    setState(() {
      _isSorting = true;
      _currentIndex = 0;
      _compareIndex = 1;
      _sortedUpToIndex = -1;
    });
    
    // Calculate delay based on sorting speed (invert so higher speed = lower delay)
    final delay = (1000 * (1.0 - _sortingSpeed) + 50).toInt();
    
    // Start the sorting algorithm
    switch (_algorithm) {
      case 'bubble':
        _startBubbleSort(delay);
        break;
      case 'selection':
        _startSelectionSort(delay);
        break;
      case 'insertion':
        _startInsertionSort(delay);
        break;
      case 'quick':
        _startQuickSort(delay);
        break;
      case 'merge':
        _startMergeSort(delay);
        break;
    }
  }
  
  void _stopSorting() {
    _sortingTimer?.cancel();
    _sortingTimer = null;
    
    if (mounted) {
      setState(() {
        _isSorting = false;
      });
    }
  }
  
  // Bubble Sort Implementation
  void _startBubbleSort(int delay) {
    int i = 0;
    int j = 0;
    bool swapped = false;
    
    _sortingTimer = Timer.periodic(Duration(milliseconds: delay), (timer) {
      if (i >= _array.length - 1) {
        _finishSorting();
        timer.cancel();
        return;
      }
      
      setState(() {
        _steps++;
        
        if (j >= _array.length - i - 1) {
          // Move to the next pass
          j = 0;
          i++;
          _sortedUpToIndex = _array.length - i;
          if (!swapped) {
            // If no swaps were made in this pass, the array is sorted
            _finishSorting();
            timer.cancel();
            return;
          }
          swapped = false;
        }
        
        _currentIndex = j;
        _compareIndex = j + 1;
        _comparisons++;
        
        // Compare and swap if needed
        if (_array[j] > _array[j + 1]) {
          final temp = _array[j];
          _array[j] = _array[j + 1];
          _array[j + 1] = temp;
          _swaps++;
          swapped = true;
        }
        
        j++;
      });
    });
  }
  
  // Selection Sort Implementation
  void _startSelectionSort(int delay) {
    int i = 0;
    int j = 1;
    int minIndex = 0;
    
    _sortingTimer = Timer.periodic(Duration(milliseconds: delay), (timer) {
      if (i >= _array.length - 1) {
        _finishSorting();
        timer.cancel();
        return;
      }
      
      setState(() {
        _steps++;
        
        if (j >= _array.length) {
          // Swap the minimum element with the first unsorted element
          if (minIndex != i) {
            final temp = _array[i];
            _array[i] = _array[minIndex];
            _array[minIndex] = temp;
            _swaps++;
          }
          
          // Move to the next pass
          i++;
          _sortedUpToIndex = i - 1;
          if (i < _array.length) {
            j = i + 1;
            minIndex = i;
            _currentIndex = i;
            _compareIndex = j;
          } else {
            _finishSorting();
            timer.cancel();
            return;
          }
        } else {
          _currentIndex = minIndex;
          _compareIndex = j;
          _comparisons++;
          
          // Find the minimum element
          if (_array[j] < _array[minIndex]) {
            minIndex = j;
          }
          
          j++;
        }
      });
    });
  }
  
  // Insertion Sort Implementation
  void _startInsertionSort(int delay) {
    int i = 1;
    int j = i;
    bool isComparing = true;
    int key = 0;
    
    _sortingTimer = Timer.periodic(Duration(milliseconds: delay), (timer) {
      if (i >= _array.length) {
        _finishSorting();
        timer.cancel();
        return;
      }
      
      setState(() {
        _steps++;
        
        if (isComparing) {
          // Start inserting the current element
          key = _array[i];
          j = i - 1;
          _currentIndex = i;
          _compareIndex = j;
          isComparing = false;
        } else if (j >= 0 && _array[j] > key) {
          // Move elements greater than key one position ahead
          _comparisons++;
          _array[j + 1] = _array[j];
          _swaps++;
          j--;
          _compareIndex = j;
        } else {
          // Place the key in its correct position
          _array[j + 1] = key;
          _sortedUpToIndex = i;
          
          // Move to the next element
          i++;
          isComparing = true;
        }
      });
    });
  }
  
  // Quick Sort Implementation (simplified for visualization)
  void _startQuickSort(int delay) {
    List<int> stack = [0, _array.length - 1];
    int pivotIndex = -1;
    int left = -1;
    int right = -1;
    int i = -1;
    int state = 0; // 0: pop, 1: partition, 2: push
    
    _sortingTimer = Timer.periodic(Duration(milliseconds: delay), (timer) {
      if (stack.isEmpty) {
        _finishSorting();
        timer.cancel();
        return;
      }
      
      setState(() {
        _steps++;
        
        if (state == 0) {
          // Pop from stack
          right = stack.removeLast();
          left = stack.removeLast();
          
          if (left < right) {
            i = left;
            pivotIndex = right;
            _currentIndex = i;
            _compareIndex = pivotIndex;
            state = 1; // Move to partition
          }
        } else if (state == 1) {
          // Partition
          _comparisons++;
          if (_array[i] <= _array[pivotIndex]) {
            // Swap elements
            if (i != left) {
              final temp = _array[i];
              _array[i] = _array[left];
              _array[left] = temp;
              _swaps++;
            }
            left++;
          }
          
          i++;
          _currentIndex = i;
          _compareIndex = pivotIndex;
          
          if (i >= pivotIndex) {
            // Swap pivot to its final position
            final temp = _array[left];
            _array[left] = _array[pivotIndex];
            _array[pivotIndex] = temp;
            _swaps++;
            
            // Push subarrays to stack
            stack.addAll([left + 1, pivotIndex]);
            stack.addAll([pivotIndex - left > 1 ? stack.removeLast() : pivotIndex]);
            stack.addAll([stack.removeAt(0), left - 1]);
            
            state = 0; // Back to pop
          }
        }
      });
    });
  }
  
  // Merge Sort Implementation (simplified for visualization)
  void _startMergeSort(int delay) {
    // For simplicity, we'll use a bottom-up approach for visualization
    int width = 1;
    int i = 0;
    List<int> temp = List.filled(_array.length, 0);
    
    _sortingTimer = Timer.periodic(Duration(milliseconds: delay), (timer) {
      if (width >= _array.length) {
        _finishSorting();
        timer.cancel();
        return;
      }
      
      setState(() {
        _steps++;
        
        if (i >= _array.length) {
          // Move to the next width
          width *= 2;
          i = 0;
          return;
        }
        
        // Merge two subarrays
        int left = i;
        int mid = math.min(i + width, _array.length);
        int right = math.min(i + 2 * width, _array.length);
        
        // Merge the subarrays
        int leftIndex = left;
        int rightIndex = mid;
        int tempIndex = left;
        
        while (leftIndex < mid && rightIndex < right) {
          _comparisons++;
          _currentIndex = leftIndex;
          _compareIndex = rightIndex;
          
          if (_array[leftIndex] <= _array[rightIndex]) {
            temp[tempIndex++] = _array[leftIndex++];
          } else {
            temp[tempIndex++] = _array[rightIndex++];
            _swaps++;
          }
        }
        
        // Copy remaining elements
        while (leftIndex < mid) {
          temp[tempIndex++] = _array[leftIndex++];
        }
        
        while (rightIndex < right) {
          temp[tempIndex++] = _array[rightIndex++];
        }
        
        // Copy back to original array
        for (int j = left; j < right; j++) {
          _array[j] = temp[j];
        }
        
        // Move to the next pair of subarrays
        i += 2 * width;
      });
    });
  }
  
  void _finishSorting() {
    setState(() {
      _isSorting = false;
      _isSorted = true;
      _currentIndex = -1;
      _compareIndex = -1;
      _sortedUpToIndex = _array.length - 1;
      
      if (_challengeMode && _currentChallenge != null) {
        _checkChallengeCompleted();
      }
    });
  }
  
  void _checkChallengeCompleted() {
    if (!_challengeMode || _currentChallenge == null || !_isSorted) return;
    
    final targetAlgorithm = _currentChallenge!['targetAlgorithm'];
    final isCompleted = _algorithm == targetAlgorithm && _isSorted;
    
    if (isCompleted != _challengeCompleted) {
      setState(() {
        _challengeCompleted = isCompleted;
      });
      
      if (_challengeCompleted && widget.onStateChanged != null) {
        widget.onStateChanged!(true);
      }
    }
  }
  
  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withOpacity(0.2),
            spreadRadius: 1,
            blurRadius: 3,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Title
          Text(
            widget.data['title'] ?? 'Sorting Algorithm Visualizer',
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
              color: _textColor,
            ),
          ),
          
          const SizedBox(height: 8),
          
          // Description
          Text(
            widget.data['description'] ?? 'Visualize and compare different sorting algorithms.',
            style: TextStyle(
              fontSize: 14,
              color: _textColor.withOpacity(0.8),
            ),
          ),
          
          const SizedBox(height: 16),
          
          // Controls
          Row(
            children: [
              // Algorithm selector
              Expanded(
                flex: 2,
                child: DropdownButtonFormField<String>(
                  value: _algorithm,
                  decoration: InputDecoration(
                    labelText: 'Algorithm',
                    border: OutlineInputBorder(),
                    contentPadding: EdgeInsets.symmetric(horizontal: 12, vertical: 8),
                  ),
                  items: _algorithms.entries.map((entry) {
                    return DropdownMenuItem<String>(
                      value: entry.key,
                      child: Text(entry.value),
                    );
                  }).toList(),
                  onChanged: _isSorting ? null : (String? newValue) {
                    if (newValue != null) {
                      setState(() {
                        _algorithm = newValue;
                        _resetSortingState();
                      });
                    }
                  },
                ),
              ),
              
              const SizedBox(width: 8),
              
              // Array size slider
              Expanded(
                flex: 3,
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Array Size: $_arraySize',
                      style: TextStyle(
                        fontSize: 14,
                        color: _textColor,
                      ),
                    ),
                    Slider(
                      value: _arraySize.toDouble(),
                      min: 5,
                      max: 50,
                      divisions: 9,
                      onChanged: _isSorting ? null : (value) {
                        setState(() {
                          _arraySize = value.toInt();
                          _generateRandomArray();
                        });
                      },
                      activeColor: _primaryColor,
                      inactiveColor: _primaryColor.withOpacity(0.3),
                    ),
                  ],
                ),
              ),
            ],
          ),
          
          const SizedBox(height: 8),
          
          // Speed slider
          Row(
            children: [
              Text(
                'Speed: ',
                style: TextStyle(
                  fontSize: 14,
                  color: _textColor,
                ),
              ),
              Expanded(
                child: Slider(
                  value: _sortingSpeed,
                  min: 0.0,
                  max: 1.0,
                  divisions: 10,
                  onChanged: (value) {
                    setState(() {
                      _sortingSpeed = value;
                    });
                  },
                  activeColor: _secondaryColor,
                  inactiveColor: _secondaryColor.withOpacity(0.3),
                ),
              ),
              Text(
                'Slow',
                style: TextStyle(
                  fontSize: 12,
                  color: _textColor.withOpacity(0.7),
                ),
              ),
              Icon(Icons.arrow_right, size: 12, color: _textColor.withOpacity(0.7)),
              Text(
                'Fast',
                style: TextStyle(
                  fontSize: 12,
                  color: _textColor.withOpacity(0.7),
                ),
              ),
            ],
          ),
          
          const SizedBox(height: 16),
          
          // Array visualization
          Container(
            height: 150,
            decoration: BoxDecoration(
              border: Border.all(color: Colors.grey.withOpacity(0.3)),
              borderRadius: BorderRadius.circular(8),
            ),
            child: ClipRRect(
              borderRadius: BorderRadius.circular(8),
              child: CustomPaint(
                size: const Size(double.infinity, 150),
                painter: SortingVisualizationPainter(
                  array: _array,
                  currentIndex: _currentIndex,
                  compareIndex: _compareIndex,
                  sortedUpToIndex: _sortedUpToIndex,
                  primaryColor: _primaryColor,
                  currentIndexColor: _currentIndexColor,
                  compareIndexColor: _compareIndexColor,
                  sortedColor: _sortedColor,
                ),
              ),
            ),
          ),
          
          const SizedBox(height: 16),
          
          // Statistics
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceAround,
            children: [
              _buildStatistic('Comparisons', _comparisons),
              _buildStatistic('Swaps', _swaps),
              _buildStatistic('Steps', _steps),
            ],
          ),
          
          const SizedBox(height: 16),
          
          // Control buttons
          Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              ElevatedButton(
                onPressed: _isSorting ? _stopSorting : _startSorting,
                style: ElevatedButton.styleFrom(
                  backgroundColor: _isSorting ? Colors.red : _accentColor,
                  foregroundColor: Colors.white,
                ),
                child: Text(_isSorting ? 'Stop' : 'Start'),
              ),
              const SizedBox(width: 16),
              ElevatedButton(
                onPressed: _isSorting ? null : _resetArray,
                style: ElevatedButton.styleFrom(
                  backgroundColor: Colors.grey,
                  foregroundColor: Colors.white,
                ),
                child: const Text('Reset'),
              ),
              const SizedBox(width: 16),
              ElevatedButton(
                onPressed: _isSorting ? null : _generateRandomArray,
                style: ElevatedButton.styleFrom(
                  backgroundColor: _secondaryColor,
                  foregroundColor: Colors.white,
                ),
                child: const Text('New Array'),
              ),
            ],
          ),
          
          // Challenge feedback (if in challenge mode)
          if (_challengeMode && _currentChallenge != null)
            Container(
              margin: const EdgeInsets.only(top: 16),
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: _challengeCompleted
                    ? Colors.green.withOpacity(0.1)
                    : Colors.orange.withOpacity(0.1),
                borderRadius: BorderRadius.circular(8),
                border: Border.all(
                  color: _challengeCompleted
                      ? Colors.green.withOpacity(0.3)
                      : Colors.orange.withOpacity(0.3),
                ),
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    _currentChallenge!['description'] ?? 'Complete the sorting challenge.',
                    style: TextStyle(
                      fontSize: 14,
                      color: _textColor,
                    ),
                  ),
                  const SizedBox(height: 8),
                  Text(
                    _challengeCompleted
                        ? (_currentChallenge!['successMessage'] ?? 'Great job! You\'ve completed the challenge.')
                        : (_currentChallenge!['hint'] ?? 'Select the correct algorithm and sort the array.'),
                    style: TextStyle(
                      fontSize: 14,
                      fontWeight: FontWeight.bold,
                      color: _challengeCompleted ? Colors.green : Colors.orange,
                    ),
                  ),
                ],
              ),
            ),
          
          // Widget name tag at the bottom
          if (widget.data['showNameTag'] ?? true)
            Padding(
              padding: const EdgeInsets.only(top: 8),
              child: Text(
                'InteractiveSortingAlgorithmVisualizerWidget',
                style: TextStyle(
                  color: Colors.grey[600],
                  fontSize: 12,
                  fontStyle: FontStyle.italic,
                ),
              ),
            ),
        ],
      ),
    );
  }
  
  Widget _buildStatistic(String label, int value) {
    return Column(
      children: [
        Text(
          label,
          style: TextStyle(
            fontSize: 14,
            color: _textColor,
          ),
        ),
        Text(
          value.toString(),
          style: TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.bold,
            color: _primaryColor,
          ),
        ),
      ],
    );
  }
}

/// Custom painter for visualizing the sorting array
class SortingVisualizationPainter extends CustomPainter {
  final List<int> array;
  final int currentIndex;
  final int compareIndex;
  final int sortedUpToIndex;
  final Color primaryColor;
  final Color currentIndexColor;
  final Color compareIndexColor;
  final Color sortedColor;
  
  SortingVisualizationPainter({
    required this.array,
    required this.currentIndex,
    required this.compareIndex,
    required this.sortedUpToIndex,
    required this.primaryColor,
    required this.currentIndexColor,
    required this.compareIndexColor,
    required this.sortedColor,
  });
  
  @override
  void paint(Canvas canvas, Size size) {
    if (array.isEmpty) return;
    
    final barWidth = size.width / array.length;
    final maxValue = array.reduce(math.max);
    final heightScale = size.height / maxValue;
    
    for (int i = 0; i < array.length; i++) {
      final barHeight = array[i] * heightScale;
      final barLeft = i * barWidth;
      
      // Determine bar color
      Color barColor;
      if (i == currentIndex) {
        barColor = currentIndexColor;
      } else if (i == compareIndex) {
        barColor = compareIndexColor;
      } else if (i <= sortedUpToIndex) {
        barColor = sortedColor;
      } else {
        barColor = primaryColor;
      }
      
      final paint = Paint()
        ..color = barColor
        ..style = PaintingStyle.fill;
      
      canvas.drawRect(
        Rect.fromLTWH(
          barLeft + 1, // Add a small gap between bars
          size.height - barHeight,
          barWidth - 2, // Subtract for the gap
          barHeight,
        ),
        paint,
      );
    }
  }
  
  @override
  bool shouldRepaint(SortingVisualizationPainter oldDelegate) {
    return oldDelegate.array != array ||
           oldDelegate.currentIndex != currentIndex ||
           oldDelegate.compareIndex != compareIndex ||
           oldDelegate.sortedUpToIndex != sortedUpToIndex;
  }
}
