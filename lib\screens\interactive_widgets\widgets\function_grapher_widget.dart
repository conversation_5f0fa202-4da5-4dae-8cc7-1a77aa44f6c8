import 'dart:math' as math;
import 'package:flutter/material.dart';

class FunctionGrapherWidget extends StatefulWidget {
  final Map<String, dynamic> data;

  const FunctionGrapherWidget({
    super.key,
    this.data = const {},
  });

  @override
  State<FunctionGrapherWidget> createState() => _FunctionGrapherWidgetState();
}

class _FunctionGrapherWidgetState extends State<FunctionGrapherWidget> {
  List<String> _functions = ['y = x', 'y = x²', 'y = sin(x)', 'y = cos(x)'];
  List<bool> _selectedFunctions = [true, false, false, false];
  double _minX = -10;
  double _maxX = 10;
  double _minY = -10;
  double _maxY = 10;
  bool _showGrid = true;
  bool _showAxes = true;
  final TextEditingController _customFunctionController = TextEditingController();
  String? _errorMessage;

  @override
  void initState() {
    super.initState();
    _initializeFromData();
  }

  @override
  void dispose() {
    _customFunctionController.dispose();
    super.dispose();
  }

  void _initializeFromData() {
    if (widget.data['functions'] != null) {
      _functions = List<String>.from(widget.data['functions']);
      _selectedFunctions = List.generate(_functions.length, (index) => index == 0);
    }
    
    if (widget.data['xRange'] != null) {
      final xRange = widget.data['xRange'];
      _minX = (xRange[0] as num).toDouble();
      _maxX = (xRange[1] as num).toDouble();
    }
    
    if (widget.data['yRange'] != null) {
      final yRange = widget.data['yRange'];
      _minY = (yRange[0] as num).toDouble();
      _maxY = (yRange[1] as num).toDouble();
    }
    
    _showGrid = widget.data['grid'] ?? true;
    _showAxes = widget.data['axes'] ?? true;
  }

  void _addCustomFunction() {
    final function = _customFunctionController.text.trim();
    if (function.isEmpty) return;

    // Basic validation
    if (!function.contains('=')) {
      setState(() {
        _errorMessage = 'Function must contain "=" symbol';
      });
      return;
    }

    setState(() {
      _functions.add(function);
      _selectedFunctions.add(true);
      _customFunctionController.clear();
      _errorMessage = null;
    });
  }

  @override
  Widget build(BuildContext context) {
    return Card(
      elevation: 4,
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Header
            Text(
              'Interactive Function Grapher',
              style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                color: Colors.blue[700],
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 16),

            // Graph display
            Container(
              height: 250,
              width: double.infinity,
              decoration: BoxDecoration(
                color: Colors.grey[50],
                borderRadius: BorderRadius.circular(12),
                border: Border.all(color: Colors.grey[300]!),
              ),
              child: ClipRRect(
                borderRadius: BorderRadius.circular(12),
                child: CustomPaint(
                  painter: SimpleGraphPainter(
                    functions: _functions,
                    selectedFunctions: _selectedFunctions,
                    minX: _minX,
                    maxX: _maxX,
                    minY: _minY,
                    maxY: _maxY,
                    showGrid: _showGrid,
                    showAxes: _showAxes,
                  ),
                  child: Container(),
                ),
              ),
            ),

            const SizedBox(height: 20),

            // Function selector
            Text(
              'Functions:',
              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.w600,
              ),
            ),
            const SizedBox(height: 12),
            Wrap(
              spacing: 8,
              runSpacing: 8,
              children: List.generate(_functions.length, (index) {
                return FilterChip(
                  label: Text(_functions[index]),
                  selected: _selectedFunctions[index],
                  selectedColor: Colors.blue[100],
                  onSelected: (selected) {
                    setState(() {
                      _selectedFunctions[index] = selected;
                    });
                  },
                );
              }),
            ),

            const SizedBox(height: 20),

            // Add custom function
            Text(
              'Add Custom Function:',
              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.w600,
              ),
            ),
            const SizedBox(height: 12),
            Row(
              children: [
                Expanded(
                  child: TextField(
                    controller: _customFunctionController,
                    decoration: InputDecoration(
                      labelText: 'e.g., y = x^2 + 2x - 1',
                      hintText: 'Enter a mathematical function',
                      border: const OutlineInputBorder(),
                      errorText: _errorMessage,
                    ),
                    onSubmitted: (_) => _addCustomFunction(),
                  ),
                ),
                const SizedBox(width: 12),
                ElevatedButton(
                  onPressed: _addCustomFunction,
                  child: const Text('Add'),
                ),
              ],
            ),

            const SizedBox(height: 20),

            // Display options
            Text(
              'Display Options:',
              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.w600,
              ),
            ),
            const SizedBox(height: 12),
            Row(
              children: [
                Expanded(
                  child: CheckboxListTile(
                    title: const Text('Show Grid'),
                    value: _showGrid,
                    onChanged: (value) {
                      setState(() {
                        _showGrid = value ?? true;
                      });
                    },
                  ),
                ),
                Expanded(
                  child: CheckboxListTile(
                    title: const Text('Show Axes'),
                    value: _showAxes,
                    onChanged: (value) {
                      setState(() {
                        _showAxes = value ?? true;
                      });
                    },
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }
}

class SimpleGraphPainter extends CustomPainter {
  final List<String> functions;
  final List<bool> selectedFunctions;
  final double minX, maxX, minY, maxY;
  final bool showGrid, showAxes;

  SimpleGraphPainter({
    required this.functions,
    required this.selectedFunctions,
    required this.minX,
    required this.maxX,
    required this.minY,
    required this.maxY,
    required this.showGrid,
    required this.showAxes,
  });

  @override
  void paint(Canvas canvas, Size size) {
    final paint = Paint()
      ..strokeWidth = 1
      ..style = PaintingStyle.stroke;

    // Draw grid
    if (showGrid) {
      paint.color = Colors.grey[300]!;
      for (int i = 0; i <= 20; i++) {
        final x = size.width * i / 20;
        final y = size.height * i / 20;
        canvas.drawLine(Offset(x, 0), Offset(x, size.height), paint);
        canvas.drawLine(Offset(0, y), Offset(size.width, y), paint);
      }
    }

    // Draw axes
    if (showAxes) {
      paint.color = Colors.black;
      paint.strokeWidth = 2;
      
      // X-axis
      final yZero = size.height * (maxY / (maxY - minY));
      if (yZero >= 0 && yZero <= size.height) {
        canvas.drawLine(Offset(0, yZero), Offset(size.width, yZero), paint);
      }
      
      // Y-axis
      final xZero = size.width * (-minX / (maxX - minX));
      if (xZero >= 0 && xZero <= size.width) {
        canvas.drawLine(Offset(xZero, 0), Offset(xZero, size.height), paint);
      }
    }

    // Draw functions (simplified - just draw some sample curves)
    paint.strokeWidth = 2;
    final colors = [Colors.blue, Colors.red, Colors.green, Colors.orange, Colors.purple];
    
    for (int i = 0; i < functions.length && i < selectedFunctions.length; i++) {
      if (!selectedFunctions[i]) continue;
      
      paint.color = colors[i % colors.length];
      final path = Path();
      bool firstPoint = true;
      
      for (double x = minX; x <= maxX; x += (maxX - minX) / 200) {
        double y = _evaluateFunction(functions[i], x);
        
        if (y.isFinite && y >= minY && y <= maxY) {
          final screenX = size.width * (x - minX) / (maxX - minX);
          final screenY = size.height * (maxY - y) / (maxY - minY);
          
          if (firstPoint) {
            path.moveTo(screenX, screenY);
            firstPoint = false;
          } else {
            path.lineTo(screenX, screenY);
          }
        } else {
          firstPoint = true;
        }
      }
      
      canvas.drawPath(path, paint);
    }
  }

  double _evaluateFunction(String function, double x) {
    // Simplified function evaluation
    if (function.contains('x²') || function.contains('x^2')) {
      return x * x;
    } else if (function.contains('sin')) {
      return math.sin(x);
    } else if (function.contains('cos')) {
      return math.cos(x);
    } else if (function.contains('2x')) {
      return 2 * x + 1;
    } else {
      return x; // Default to y = x
    }
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => true;
}
