import 'dart:math' as math;
import 'package:flutter/material.dart';
import '../../../../models/interactive_widget_model.dart';
import '../../../../theme/widget_colors.dart';
import '../../../../theme/text_styles.dart';
import '../../../../widgets/common/loading_state.dart';
import '../../../../widgets/common/error_state.dart';
import '../../../../utils/widget_animations.dart';

class FunctionGrapherWidget extends StatefulWidget {
  final InteractiveWidgetModel widget;

  const FunctionGrapherWidget({super.key, required this.widget});

  @override
  State<FunctionGrapherWidget> createState() => _FunctionGrapherWidgetState();
}

class _FunctionGrapherWidgetState extends State<FunctionGrapherWidget> {
  late List<String> _functions;
  late List<bool> _selectedFunctions;
  late double _minX;
  late double _maxX;
  late double _minY;
  late double _maxY;
  late bool _showGrid;
  late bool _showAxes;
  final TextEditingController _customFunctionController =
      TextEditingController();
  String? _errorMessage;
  bool _isLoading = true;
  bool _hasError = false;
  String? _generalErrorMessage;

  @override
  void initState() {
    super.initState();
    _initializeGrapher();
  }

  @override
  void dispose() {
    _customFunctionController.dispose();
    super.dispose();
  }

  void _initializeGrapher() {
    try {
      setState(() {
        _isLoading = true;
        _hasError = false;
        _generalErrorMessage = null;
      });

      _functions = List<String>.from(widget.widget.data['functions'] ?? []);
      _selectedFunctions = List.generate(
        _functions.length,
        (index) => index == 0,
      );

      // Safely convert xRange and yRange values to double, handling both int and double types
      final xRangeData = widget.widget.data['xRange'] ?? [-10, 10];
      final yRangeData = widget.widget.data['yRange'] ?? [-10, 10];

      final xRange =
          xRangeData
              .map<double>(
                (value) => value is int ? value.toDouble() : value as double,
              )
              .toList();
      final yRange =
          yRangeData
              .map<double>(
                (value) => value is int ? value.toDouble() : value as double,
              )
              .toList();

      _minX = xRange[0];
      _maxX = xRange[1];
      _minY = yRange[0];
      _maxY = yRange[1];

      _showGrid = widget.widget.data['grid'] as bool? ?? true;
      _showAxes = widget.widget.data['axes'] as bool? ?? true;

      // Simulate loading delay for demonstration
      Future.delayed(const Duration(milliseconds: 500), () {
        if (mounted) {
          setState(() {
            _isLoading = false;
          });
        }
      });
    } catch (e) {
      setState(() {
        _isLoading = false;
        _hasError = true;
        _generalErrorMessage = 'Failed to initialize function grapher: ${e.toString()}';
      });
    }
  }

  void _addCustomFunction() {
    final function = _customFunctionController.text.trim();
    if (function.isEmpty) return;

    // Basic validation
    if (!function.contains('=')) {
      setState(() {
        _errorMessage = 'Function must contain "=" symbol';
      });
      return;
    }

    setState(() {
      _functions.add(function);
      _selectedFunctions.add(true);
      _customFunctionController.clear();
      _errorMessage = null;
    });
  }

  @override
  Widget build(BuildContext context) {
    // Show error state if there's a general error
    if (_hasError && _generalErrorMessage != null) {
      return ErrorState(
        message: 'Function Grapher Error',
        description: _generalErrorMessage,
        onRetry: _initializeGrapher,
        categoryId: 'mathematics',
        errorType: ErrorType.general,
      );
    }

    // Show loading state while initializing
    if (_isLoading) {
      return const MathLoadingState(
        height: 400,
        equation: 'f(x) = x²',
      );
    }

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Header
        Padding(
          padding: const EdgeInsets.only(bottom: 16.0),
          child: Text(
            'Interactive Function Grapher',
            style: WidgetTextStyles.headingMedium.copyWith(
              color: WidgetColors.getCategoryColor('mathematics'),
            ),
          ),
        ),

        // Graph display
        Container(
          height: 250,
          width: double.infinity,
          decoration: BoxDecoration(
            color: WidgetColors.backgroundPrimary,
            borderRadius: BorderRadius.circular(12),
            border: Border.all(color: WidgetColors.borderLight),
            boxShadow: [
              BoxShadow(
                color: WidgetColors.shadowLight,
                blurRadius: 4,
                offset: const Offset(0, 2),
              ),
            ],
          ),
          child: ClipRRect(
            borderRadius: BorderRadius.circular(12),
            child: CustomPaint(
              painter: GraphPainter(
                functions: _functions,
                selectedFunctions: _selectedFunctions,
                minX: _minX,
                maxX: _maxX,
                minY: _minY,
                maxY: _maxY,
                showGrid: _showGrid,
                showAxes: _showAxes,
              ),
              child: Container(),
            ),
          ),
        ),

        // Function selector
        Padding(
          padding: const EdgeInsets.only(top: 20.0),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                'Functions:',
                style: WidgetTextStyles.headingSmall,
              ),
              const SizedBox(height: 12),
              Wrap(
                spacing: 8,
                runSpacing: 8,
                children: List.generate(_functions.length, (index) {
                  return WidgetAnimations.buttonPressAnimation(
                    onPressed: () {
                      setState(() {
                        _selectedFunctions[index] = !_selectedFunctions[index];
                      });
                    },
                    child: FilterChip(
                      label: Text(
                        _functions[index],
                        style: WidgetTextStyles.bodyMedium.copyWith(
                          color: _selectedFunctions[index]
                              ? Colors.white
                              : WidgetColors.textPrimary,
                        ),
                      ),
                      selected: _selectedFunctions[index],
                      selectedColor: WidgetColors.getCategoryColor('mathematics'),
                      backgroundColor: WidgetColors.backgroundSecondary,
                      checkmarkColor: Colors.white,
                      onSelected: (selected) {
                        setState(() {
                          _selectedFunctions[index] = selected;
                        });
                      },
                    ),
                  );
                }),
              ),
            ],
          ),
        ),

        // Add custom function
        Padding(
          padding: const EdgeInsets.only(top: 20.0),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                'Add Custom Function:',
                style: WidgetTextStyles.headingSmall,
              ),
              const SizedBox(height: 12),
              Row(
                children: [
                  Expanded(
                    child: TextField(
                      controller: _customFunctionController,
                      style: WidgetTextStyles.input,
                      decoration: InputDecoration(
                        labelText: 'e.g., y = x^2 + 2x - 1',
                        labelStyle: WidgetTextStyles.inputLabel,
                        hintText: 'Enter a mathematical function',
                        hintStyle: WidgetTextStyles.inputPlaceholder,
                        border: OutlineInputBorder(
                          borderRadius: BorderRadius.circular(8),
                          borderSide: BorderSide(color: WidgetColors.inputBorder),
                        ),
                        focusedBorder: OutlineInputBorder(
                          borderRadius: BorderRadius.circular(8),
                          borderSide: BorderSide(
                            color: WidgetColors.getCategoryColor('mathematics'),
                            width: 2,
                          ),
                        ),
                        errorBorder: OutlineInputBorder(
                          borderRadius: BorderRadius.circular(8),
                          borderSide: BorderSide(color: WidgetColors.error),
                        ),
                        filled: true,
                        fillColor: WidgetColors.inputBackground,
                        errorText: _errorMessage,
                        errorStyle: WidgetTextStyles.error,
                      ),
                      onSubmitted: (_) => _addCustomFunction(),
                    ),
                  ),
                  const SizedBox(width: 12),
                  WidgetAnimations.buttonPressAnimation(
                    onPressed: _addCustomFunction,
                    child: ElevatedButton(
                      onPressed: _addCustomFunction,
                      style: ElevatedButton.styleFrom(
                        backgroundColor: WidgetColors.getCategoryColor('mathematics'),
                        foregroundColor: Colors.white,
                        padding: const EdgeInsets.symmetric(
                          horizontal: 20,
                          vertical: 16,
                        ),
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(8),
                        ),
                        elevation: 2,
                      ),
                      child: Text(
                        'Add',
                        style: WidgetTextStyles.buttonMedium,
                      ),
                    ),
                  ),
                ],
              ),
              if (_errorMessage != null) ...[
                const SizedBox(height: 8),
                WidgetAnimations.errorShakeAnimation(
                  triggerShake: _errorMessage != null,
                  child: Text(
                    _errorMessage!,
                    style: WidgetTextStyles.error,
                  ),
                ),
              ],
            ],
          ),
        ),

        // Graph controls
        Padding(
          padding: const EdgeInsets.only(top: 20.0),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                'Display Options:',
                style: WidgetTextStyles.headingSmall,
              ),
              const SizedBox(height: 12),
              Row(
                children: [
                  // Grid toggle
                  Expanded(
                    child: WidgetAnimations.buttonRippleAnimation(
                      onPressed: () {
                        setState(() {
                          _showGrid = !_showGrid;
                        });
                      },
                      rippleColor: WidgetColors.getCategoryColor('mathematics'),
                      borderRadius: BorderRadius.circular(8),
                      child: Container(
                        padding: const EdgeInsets.all(12),
                        decoration: BoxDecoration(
                          color: _showGrid
                              ? WidgetColors.getCategoryColorLight('mathematics')
                              : WidgetColors.backgroundSecondary,
                          borderRadius: BorderRadius.circular(8),
                          border: Border.all(
                            color: _showGrid
                                ? WidgetColors.getCategoryColor('mathematics')
                                : WidgetColors.borderLight,
                          ),
                        ),
                        child: Row(
                          children: [
                            Icon(
                              _showGrid ? Icons.check_box : Icons.check_box_outline_blank,
                              color: _showGrid
                                  ? WidgetColors.getCategoryColor('mathematics')
                                  : WidgetColors.textSecondary,
                              size: 20,
                            ),
                            const SizedBox(width: 8),
                            Text(
                              'Show Grid',
                              style: WidgetTextStyles.bodyMedium.copyWith(
                                color: _showGrid
                                    ? WidgetColors.getCategoryColor('mathematics')
                                    : WidgetColors.textPrimary,
                                fontWeight: _showGrid ? FontWeight.w600 : FontWeight.w400,
                              ),
                            ),
                          ],
                        ),
                      ),
                    ),
                  ),

                  const SizedBox(width: 12),

                  // Axes toggle
                  Expanded(
                    child: WidgetAnimations.buttonRippleAnimation(
                      onPressed: () {
                        setState(() {
                          _showAxes = !_showAxes;
                        });
                      },
                      rippleColor: WidgetColors.getCategoryColor('mathematics'),
                      borderRadius: BorderRadius.circular(8),
                      child: Container(
                        padding: const EdgeInsets.all(12),
                        decoration: BoxDecoration(
                          color: _showAxes
                              ? WidgetColors.getCategoryColorLight('mathematics')
                              : WidgetColors.backgroundSecondary,
                          borderRadius: BorderRadius.circular(8),
                          border: Border.all(
                            color: _showAxes
                                ? WidgetColors.getCategoryColor('mathematics')
                                : WidgetColors.borderLight,
                          ),
                        ),
                        child: Row(
                          children: [
                            Icon(
                              _showAxes ? Icons.check_box : Icons.check_box_outline_blank,
                              color: _showAxes
                                  ? WidgetColors.getCategoryColor('mathematics')
                                  : WidgetColors.textSecondary,
                              size: 20,
                            ),
                            const SizedBox(width: 8),
                            Text(
                              'Show Axes',
                              style: WidgetTextStyles.bodyMedium.copyWith(
                                color: _showAxes
                                    ? WidgetColors.getCategoryColor('mathematics')
                                    : WidgetColors.textPrimary,
                                fontWeight: _showAxes ? FontWeight.w600 : FontWeight.w400,
                              ),
                            ),
                          ],
                        ),
                      ),
                    ),
                  ),
                ],
              ),
            ],
          ),
        ),

        // Range controls
        Padding(
          padding: const EdgeInsets.only(top: 20.0),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                'Graph Range:',
                style: WidgetTextStyles.headingSmall,
              ),
              const SizedBox(height: 16),
              Container(
                padding: const EdgeInsets.all(16),
                decoration: BoxDecoration(
                  color: WidgetColors.backgroundSecondary,
                  borderRadius: BorderRadius.circular(12),
                  border: Border.all(color: WidgetColors.borderLight),
                ),
                child: Column(
                  children: [
                    // X Range
                    Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Row(
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          children: [
                            Text(
                              'X Range:',
                              style: WidgetTextStyles.bodyMedium.copyWith(
                                fontWeight: FontWeight.w600,
                              ),
                            ),
                            Text(
                              '${_minX.toStringAsFixed(1)} to ${_maxX.toStringAsFixed(1)}',
                              style: WidgetTextStyles.bodySmall.copyWith(
                                color: WidgetColors.getCategoryColor('mathematics'),
                                fontWeight: FontWeight.w600,
                              ),
                            ),
                          ],
                        ),
                        const SizedBox(height: 8),
                        SliderTheme(
                          data: SliderTheme.of(context).copyWith(
                            activeTrackColor: WidgetColors.getCategoryColor('mathematics'),
                            inactiveTrackColor: WidgetColors.borderLight,
                            thumbColor: WidgetColors.getCategoryColor('mathematics'),
                            overlayColor: WidgetColors.getCategoryColorLight('mathematics'),
                            valueIndicatorColor: WidgetColors.getCategoryColor('mathematics'),
                            valueIndicatorTextStyle: WidgetTextStyles.caption.copyWith(
                              color: Colors.white,
                            ),
                          ),
                          child: RangeSlider(
                            values: RangeValues(_minX, _maxX),
                            min: -20,
                            max: 20,
                            divisions: 40,
                            labels: RangeLabels(
                              _minX.toStringAsFixed(1),
                              _maxX.toStringAsFixed(1),
                            ),
                            onChanged: (values) {
                              setState(() {
                                _minX = values.start;
                                _maxX = values.end;
                              });
                            },
                          ),
                        ),
                      ],
                    ),

                    const SizedBox(height: 20),

                    // Y Range
                    Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Row(
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          children: [
                            Text(
                              'Y Range:',
                              style: WidgetTextStyles.bodyMedium.copyWith(
                                fontWeight: FontWeight.w600,
                              ),
                            ),
                            Text(
                              '${_minY.toStringAsFixed(1)} to ${_maxY.toStringAsFixed(1)}',
                              style: WidgetTextStyles.bodySmall.copyWith(
                                color: WidgetColors.getCategoryColor('mathematics'),
                                fontWeight: FontWeight.w600,
                              ),
                            ),
                          ],
                        ),
                        const SizedBox(height: 8),
                        SliderTheme(
                          data: SliderTheme.of(context).copyWith(
                            activeTrackColor: WidgetColors.getCategoryColor('mathematics'),
                            inactiveTrackColor: WidgetColors.borderLight,
                            thumbColor: WidgetColors.getCategoryColor('mathematics'),
                            overlayColor: WidgetColors.getCategoryColorLight('mathematics'),
                            valueIndicatorColor: WidgetColors.getCategoryColor('mathematics'),
                            valueIndicatorTextStyle: WidgetTextStyles.caption.copyWith(
                              color: Colors.white,
                            ),
                          ),
                          child: RangeSlider(
                            values: RangeValues(_minY, _maxY),
                            min: -20,
                            max: 20,
                            divisions: 40,
                            labels: RangeLabels(
                              _minY.toStringAsFixed(1),
                              _maxY.toStringAsFixed(1),
                            ),
                            onChanged: (values) {
                              setState(() {
                                _minY = values.start;
                                _maxY = values.end;
                              });
                            },
                          ),
                        ),
                      ],
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }
}

class GraphPainter extends CustomPainter {
  final List<String> functions;
  final List<bool> selectedFunctions;
  final double minX;
  final double maxX;
  final double minY;
  final double maxY;
  final bool showGrid;
  final bool showAxes;

  GraphPainter({
    required this.functions,
    required this.selectedFunctions,
    required this.minX,
    required this.maxX,
    required this.minY,
    required this.maxY,
    required this.showGrid,
    required this.showAxes,
  });

  @override
  void paint(Canvas canvas, Size size) {
    final paint =
        Paint()
          ..color = Colors.black
          ..strokeWidth = 1.0
          ..style = PaintingStyle.stroke;

    final gridPaint =
        Paint()
          ..color = Colors.grey[300]!
          ..strokeWidth = 0.5
          ..style = PaintingStyle.stroke;

    // Draw grid
    if (showGrid) {
      _drawGrid(canvas, size, gridPaint);
    }

    // Draw axes
    if (showAxes) {
      _drawAxes(canvas, size, paint);
    }

    // Draw functions
    for (int i = 0; i < functions.length; i++) {
      if (selectedFunctions[i]) {
        _drawFunction(canvas, size, functions[i], _getFunctionColor(i));
      }
    }
  }

  void _drawGrid(Canvas canvas, Size size, Paint paint) {
    final width = size.width;
    final height = size.height;

    // Calculate grid spacing
    final xStep = width / ((maxX - minX).abs() / 1);
    final yStep = height / ((maxY - minY).abs() / 1);

    // Draw vertical grid lines
    for (double x = 0; x <= width; x += xStep) {
      canvas.drawLine(Offset(x, 0), Offset(x, height), paint);
    }

    // Draw horizontal grid lines
    for (double y = 0; y <= height; y += yStep) {
      canvas.drawLine(Offset(0, y), Offset(width, y), paint);
    }
  }

  void _drawAxes(Canvas canvas, Size size, Paint paint) {
    final width = size.width;
    final height = size.height;

    // Calculate origin position
    final originX = width * (-minX) / (maxX - minX);
    final originY = height * (maxY) / (maxY - minY);

    // Draw x-axis
    canvas.drawLine(Offset(0, originY), Offset(width, originY), paint);

    // Draw y-axis
    canvas.drawLine(Offset(originX, 0), Offset(originX, height), paint);

    // Draw axis labels
    final textStyle = TextStyle(color: Colors.black, fontSize: 10);
    final textPainter = TextPainter(textDirection: TextDirection.ltr);

    // X-axis labels
    for (int i = minX.toInt(); i <= maxX.toInt(); i++) {
      if (i == 0) continue; // Skip origin

      final x = width * (i - minX) / (maxX - minX);

      textPainter.text = TextSpan(text: i.toString(), style: textStyle);
      textPainter.layout();
      textPainter.paint(canvas, Offset(x - textPainter.width / 2, originY + 5));
    }

    // Y-axis labels
    for (int i = minY.toInt(); i <= maxY.toInt(); i++) {
      if (i == 0) continue; // Skip origin

      final y = height * (maxY - i) / (maxY - minY);

      textPainter.text = TextSpan(text: i.toString(), style: textStyle);
      textPainter.layout();
      textPainter.paint(
        canvas,
        Offset(originX + 5, y - textPainter.height / 2),
      );
    }

    // Origin label
    textPainter.text = TextSpan(text: '0', style: textStyle);
    textPainter.layout();
    textPainter.paint(canvas, Offset(originX + 5, originY + 5));
  }

  void _drawFunction(Canvas canvas, Size size, String function, Color color) {
    final paint =
        Paint()
          ..color = color
          ..strokeWidth = 2.0
          ..style = PaintingStyle.stroke;

    final path = Path();
    bool isFirstPoint = true;

    // Parse function
    final parts = function.split('=');
    if (parts.length != 2) return;

    final expression = parts[1].trim();

    // Draw function
    for (double pixelX = 0; pixelX <= size.width; pixelX++) {
      final x = minX + (pixelX / size.width) * (maxX - minX);
      double? y;

      try {
        y = _evaluateExpression(expression, x);
      } catch (e) {
        // Skip invalid points
        continue;
      }

      if (y == null || y.isNaN || y.isInfinite) {
        isFirstPoint = true;
        continue;
      }

      // Convert y to pixel coordinates
      final pixelY = size.height * (maxY - y) / (maxY - minY);

      // Skip points outside the visible area
      if (pixelY < 0 || pixelY > size.height) {
        isFirstPoint = true;
        continue;
      }

      if (isFirstPoint) {
        path.moveTo(pixelX, pixelY);
        isFirstPoint = false;
      } else {
        path.lineTo(pixelX, pixelY);
      }
    }

    canvas.drawPath(path, paint);
  }

  double? _evaluateExpression(String expression, double x) {
    // Replace x with its value
    expression = expression.replaceAll('x', x.toString());

    // Handle common functions
    expression = _replaceCommonFunctions(expression);

    try {
      // Simple expression evaluator
      return _evaluateSimpleExpression(expression);
    } catch (e) {
      return null;
    }
  }

  String _replaceCommonFunctions(String expression) {
    // Replace sin(x), cos(x), etc.
    final sinRegex = RegExp(r'sin\(([\d\.\+\-\*\/\^\s]+)\)');
    final cosRegex = RegExp(r'cos\(([\d\.\+\-\*\/\^\s]+)\)');
    final tanRegex = RegExp(r'tan\(([\d\.\+\-\*\/\^\s]+)\)');
    final logRegex = RegExp(r'log\(([\d\.\+\-\*\/\^\s]+)\)');
    final sqrtRegex = RegExp(r'sqrt\(([\d\.\+\-\*\/\^\s]+)\)');

    expression = expression.replaceAllMapped(sinRegex, (match) {
      final arg = _evaluateSimpleExpression(match.group(1)!);
      return math.sin(arg).toString();
    });

    expression = expression.replaceAllMapped(cosRegex, (match) {
      final arg = _evaluateSimpleExpression(match.group(1)!);
      return math.cos(arg).toString();
    });

    expression = expression.replaceAllMapped(tanRegex, (match) {
      final arg = _evaluateSimpleExpression(match.group(1)!);
      return math.tan(arg).toString();
    });

    expression = expression.replaceAllMapped(logRegex, (match) {
      final arg = _evaluateSimpleExpression(match.group(1)!);
      return math.log(arg).toString();
    });

    expression = expression.replaceAllMapped(sqrtRegex, (match) {
      final arg = _evaluateSimpleExpression(match.group(1)!);
      return math.sqrt(arg).toString();
    });

    return expression;
  }

  double _evaluateSimpleExpression(String expression) {
    // More robust expression evaluator

    // Number pattern: matches integers, decimals, and optionally signed numbers
    const String numPattern = r'(-?\d+(?:\.\d+)?)';

    // Handle parentheses
    final parenRegex = RegExp(
      r'\(([^()]+)\)',
    ); // Matches non-nested parentheses content
    while (expression.contains('(')) {
      expression = expression.replaceAllMapped(parenRegex, (match) {
        return _evaluateSimpleExpression(match.group(1)!).toString();
      });
    }

    // Handle exponentiation (^)
    final expRegex = RegExp('$numPattern\\s*\\^\\s*$numPattern');
    while (expression.contains('^')) {
      expression = expression.replaceAllMapped(expRegex, (match) {
        final base = double.parse(match.group(1)!);
        final exponent = double.parse(match.group(2)!);
        return math.pow(base, exponent).toString();
      });
    }

    // Handle multiplication and division
    final mulDivRegex = RegExp('$numPattern\\s*([*/])\\s*$numPattern');
    while (expression.contains('*') || expression.contains('/')) {
      expression = expression.replaceAllMapped(mulDivRegex, (match) {
        final a = double.parse(match.group(1)!);
        final op = match.group(2);
        final b = double.parse(match.group(3)!);

        if (op == '*') {
          return (a * b).toString();
        } else {
          if (b == 0) throw ArgumentError('Division by zero');
          return (a / b).toString();
        }
      });
    }

    // Handle addition and subtraction
    // Ensure to handle negative numbers correctly, e.g., "5+-2" or "-5-2"
    final addSubRegex = RegExp('$numPattern\\s*([+\\-])\\s*$numPattern');
    while (RegExp(r'(?<!^)(?<![eE])[-+]').hasMatch(expression) &&
        expression.contains(RegExp(numPattern + r'\s*[+\-]\s*' + numPattern))) {
      expression = expression.replaceAllMapped(addSubRegex, (match) {
        final a = double.parse(match.group(1)!);
        final op = match.group(2);
        final b = double.parse(match.group(3)!);

        if (op == '+') {
          return (a + b).toString();
        } else {
          return (a - b).toString();
        }
      });
    }

    // At this point, expression should be a single number (possibly negative)
    return double.parse(expression);
  }

  Color _getFunctionColor(int index) {
    final colors = [
      Colors.blue,
      Colors.red,
      Colors.green,
      Colors.purple,
      Colors.orange,
      Colors.teal,
      Colors.pink,
      Colors.indigo,
    ];

    return colors[index % colors.length];
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) {
    return true;
  }
}
