{"buildFiles": ["C:\\flutter\\packages\\flutter_tools\\gradle\\src\\main\\groovy\\CMakeLists.txt"], "cleanCommandsComponents": [["D:\\AndroidSDK\\cmake\\3.22.1\\bin\\ninja.exe", "-C", "D:\\AResonance\\rn\\android\\app\\.cxx\\Debug\\3453e5e1\\armeabi-v7a", "clean"]], "buildTargetsCommandComponents": ["D:\\AndroidSDK\\cmake\\3.22.1\\bin\\ninja.exe", "-C", "D:\\AResonance\\rn\\android\\app\\.cxx\\Debug\\3453e5e1\\armeabi-v7a", "{LIST_OF_TARGETS_TO_BUILD}"], "libraries": {}, "toolchains": {"toolchain": {"cCompilerExecutable": "D:\\AndroidSDK\\ndk\\26.3.11579264\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang.exe", "cppCompilerExecutable": "D:\\AndroidSDK\\ndk\\26.3.11579264\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe"}}, "cFileExtensions": [], "cppFileExtensions": []}