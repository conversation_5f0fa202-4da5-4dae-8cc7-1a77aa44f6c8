import 'package:flutter/material.dart';
import 'dart:math' as math;

/// A widget that visualizes a function as a machine that takes inputs and produces outputs
class InteractiveFunctionMachineWidget extends StatefulWidget {
  /// The data for this widget
  final Map<String, dynamic> data;

  /// Optional callback when the widget state changes
  final Function(bool isCompleted)? onStateChanged;

  const InteractiveFunctionMachineWidget({
    Key? key,
    required this.data,
    this.onStateChanged,
  }) : super(key: key);

  /// Factory constructor to create the widget from JSON data
  factory InteractiveFunctionMachineWidget.fromData(Map<String, dynamic> data) {
    return InteractiveFunctionMachineWidget(
      data: data,
    );
  }

  @override
  State<InteractiveFunctionMachineWidget> createState() => _InteractiveFunctionMachineWidgetState();
}

class _InteractiveFunctionMachineWidgetState extends State<InteractiveFunctionMachineWidget> with SingleTickerProviderStateMixin {
  // Input and output values
  String _inputValue = '';
  String _outputValue = '';
  
  // Animation controller for the function machine
  late AnimationController _animationController;
  late Animation<double> _animation;
  
  // Text editing controller for the input field
  final TextEditingController _inputController = TextEditingController();
  
  // Function rule display
  late String _functionRuleDisplay;
  
  // Function logic
  late String _functionLogic;
  
  // Colors
  late Color _primaryColor;
  late Color _secondaryColor;
  late Color _accentColor;
  late Color _backgroundColor;
  late Color _textColor;
  
  // History of inputs and outputs
  final List<Map<String, dynamic>> _history = [];
  
  // Maximum number of history items to display
  final int _maxHistoryItems = 5;
  
  // Error message
  String? _errorMessage;
  
  // Whether the animation is running
  bool _isAnimating = false;

  @override
  void initState() {
    super.initState();
    
    // Initialize function rule display and logic
    _functionRuleDisplay = widget.data['function_rule_display'] ?? 'f(x) = 2x + 3';
    _functionLogic = widget.data['function_logic'] ?? 'output = 2 * input + 3';
    
    // Initialize colors
    _primaryColor = _parseColor(widget.data['primary_color']) ?? Colors.blue;
    _secondaryColor = _parseColor(widget.data['secondary_color']) ?? Colors.orange;
    _accentColor = _parseColor(widget.data['accent_color']) ?? Colors.green;
    _backgroundColor = _parseColor(widget.data['background_color']) ?? Colors.white;
    _textColor = _parseColor(widget.data['text_color']) ?? Colors.black87;
    
    // Initialize animation controller
    _animationController = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 1500),
    );
    
    _animation = CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeInOut,
    );
    
    // Add listener to animation controller
    _animationController.addStatusListener((status) {
      if (status == AnimationStatus.completed) {
        setState(() {
          _isAnimating = false;
        });
      }
    });
  }

  @override
  void dispose() {
    _animationController.dispose();
    _inputController.dispose();
    super.dispose();
  }
  
  // Parse color from hex string
  Color? _parseColor(String? hexString) {
    if (hexString == null) return null;
    
    hexString = hexString.replaceAll('#', '');
    if (hexString.length == 6) {
      hexString = 'FF$hexString';
    }
    
    return Color(int.parse(hexString, radix: 16));
  }
  
  // Process input and calculate output
  void _processInput() {
    if (_inputValue.isEmpty) {
      setState(() {
        _errorMessage = 'Please enter an input value';
      });
      return;
    }
    
    try {
      // Parse input value
      final double input = double.parse(_inputValue);
      
      // Calculate output based on function logic
      double output = _calculateOutput(input);
      
      // Start animation
      setState(() {
        _outputValue = output.toStringAsFixed(2);
        _errorMessage = null;
        _isAnimating = true;
        
        // Add to history
        _history.insert(0, {
          'input': input,
          'output': output,
        });
        
        // Limit history size
        if (_history.length > _maxHistoryItems) {
          _history.removeLast();
        }
      });
      
      // Reset animation and start
      _animationController.reset();
      _animationController.forward();
      
      // Notify parent of state change
      widget.onStateChanged?.call(true);
    } catch (e) {
      setState(() {
        _errorMessage = 'Invalid input. Please enter a number.';
      });
    }
  }
  
  // Calculate output based on function logic
  double _calculateOutput(double input) {
    // Default implementation for f(x) = 2x + 3
    double output = 2 * input + 3;
    
    // Try to evaluate the function logic if provided
    try {
      // This is a simple parser for basic functions
      // In a real app, you would use a more robust expression parser
      String logic = _functionLogic.toLowerCase();
      
      if (logic.contains('output =')) {
        logic = logic.replaceAll('output =', '').trim();
      }
      
      logic = logic.replaceAll('input', input.toString());
      logic = logic.replaceAll('x', input.toString());
      
      // Handle basic operations
      if (logic.contains('+')) {
        final parts = logic.split('+');
        output = double.parse(parts[0].trim()) + double.parse(parts[1].trim());
      } else if (logic.contains('-')) {
        final parts = logic.split('-');
        output = double.parse(parts[0].trim()) - double.parse(parts[1].trim());
      } else if (logic.contains('*')) {
        final parts = logic.split('*');
        output = double.parse(parts[0].trim()) * double.parse(parts[1].trim());
      } else if (logic.contains('/')) {
        final parts = logic.split('/');
        output = double.parse(parts[0].trim()) / double.parse(parts[1].trim());
      } else if (logic.contains('^')) {
        final parts = logic.split('^');
        output = math.pow(double.parse(parts[0].trim()), double.parse(parts[1].trim())).toDouble();
      } else {
        // If no operation is found, try to parse the logic as a number
        output = double.parse(logic);
      }
    } catch (e) {
      // If parsing fails, use the default implementation
      output = 2 * input + 3;
    }
    
    return output;
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: _backgroundColor,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withOpacity(0.2),
            spreadRadius: 1,
            blurRadius: 3,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Title
          Text(
            widget.data['title'] ?? 'Function Machine',
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
              color: _textColor,
            ),
          ),
          
          const SizedBox(height: 8),
          
          // Description
          if (widget.data['description'] != null)
            Text(
              widget.data['description'],
              style: TextStyle(
                fontSize: 14,
                color: _textColor.withOpacity(0.8),
              ),
            ),
          
          const SizedBox(height: 16),
          
          // Function rule display
          Container(
            padding: const EdgeInsets.all(12),
            decoration: BoxDecoration(
              color: _primaryColor.withOpacity(0.1),
              borderRadius: BorderRadius.circular(8),
              border: Border.all(color: _primaryColor),
            ),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Text(
                  'f(x) = ',
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                    color: _textColor,
                  ),
                ),
                Text(
                  _functionRuleDisplay,
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                    color: _primaryColor,
                  ),
                ),
              ],
            ),
          ),
          
          const SizedBox(height: 24),
          
          // Function machine visualization
          AnimatedBuilder(
            animation: _animation,
            builder: (context, child) {
              return _buildFunctionMachine();
            },
          ),
          
          const SizedBox(height: 16),
          
          // Input field
          Row(
            children: [
              Expanded(
                child: TextField(
                  controller: _inputController,
                  decoration: InputDecoration(
                    labelText: 'Enter input (x)',
                    border: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(8),
                    ),
                    errorText: _errorMessage,
                  ),
                  keyboardType: TextInputType.number,
                  onChanged: (value) {
                    setState(() {
                      _inputValue = value;
                    });
                  },
                  onSubmitted: (value) {
                    _processInput();
                  },
                ),
              ),
              const SizedBox(width: 16),
              ElevatedButton(
                onPressed: _isAnimating ? null : _processInput,
                style: ElevatedButton.styleFrom(
                  backgroundColor: _primaryColor,
                  padding: const EdgeInsets.symmetric(vertical: 16),
                ),
                child: const Text('Calculate'),
              ),
            ],
          ),
          
          const SizedBox(height: 16),
          
          // History
          if (_history.isNotEmpty)
            Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'History:',
                  style: TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                    color: _textColor,
                  ),
                ),
                const SizedBox(height: 8),
                Container(
                  height: 120,
                  decoration: BoxDecoration(
                    color: Colors.grey.withOpacity(0.1),
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: ListView.builder(
                    itemCount: _history.length,
                    itemBuilder: (context, index) {
                      final item = _history[index];
                      return ListTile(
                        dense: true,
                        title: Text(
                          'f(${item['input']}) = ${item['output'].toStringAsFixed(2)}',
                          style: TextStyle(
                            color: _textColor,
                          ),
                        ),
                      );
                    },
                  ),
                ),
              ],
            ),
          
          // Widget name tag
          if (widget.data['showNameTag'] ?? true)
            Padding(
              padding: const EdgeInsets.only(top: 8),
              child: Text(
                'InteractiveFunctionMachine',
                style: TextStyle(
                  color: Colors.grey[600],
                  fontSize: 12,
                  fontStyle: FontStyle.italic,
                ),
              ),
            ),
        ],
      ),
    );
  }
  
  // Build the function machine visualization
  Widget _buildFunctionMachine() {
    return SizedBox(
      height: 200,
      child: Stack(
        children: [
          // Machine body
          Positioned.fill(
            child: Container(
              margin: const EdgeInsets.symmetric(horizontal: 40),
              decoration: BoxDecoration(
                color: _secondaryColor.withOpacity(0.2),
                borderRadius: BorderRadius.circular(16),
                border: Border.all(
                  color: _secondaryColor,
                  width: 2,
                ),
              ),
              child: Center(
                child: Text(
                  _functionRuleDisplay,
                  style: TextStyle(
                    fontSize: 20,
                    fontWeight: FontWeight.bold,
                    color: _secondaryColor,
                  ),
                ),
              ),
            ),
          ),
          
          // Input arrow
          if (_isAnimating)
            Positioned(
              left: 0,
              top: 80,
              child: _buildInputArrow(),
            ),
          
          // Output arrow
          if (_isAnimating && _animation.value > 0.5)
            Positioned(
              right: 0,
              top: 80,
              child: _buildOutputArrow(),
            ),
          
          // Input value
          Positioned(
            left: 10,
            top: 100,
            child: Container(
              padding: const EdgeInsets.all(8),
              decoration: BoxDecoration(
                color: _primaryColor.withOpacity(0.2),
                borderRadius: BorderRadius.circular(8),
                border: Border.all(color: _primaryColor),
              ),
              child: Text(
                _inputValue,
                style: TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.bold,
                  color: _primaryColor,
                ),
              ),
            ),
          ),
          
          // Output value
          Positioned(
            right: 10,
            top: 100,
            child: Container(
              padding: const EdgeInsets.all(8),
              decoration: BoxDecoration(
                color: _accentColor.withOpacity(0.2),
                borderRadius: BorderRadius.circular(8),
                border: Border.all(color: _accentColor),
              ),
              child: Text(
                _outputValue,
                style: TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.bold,
                  color: _accentColor,
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }
  
  // Build the input arrow
  Widget _buildInputArrow() {
    return SizedBox(
      width: 40 + 100 * _animation.value,
      height: 40,
      child: CustomPaint(
        painter: ArrowPainter(
          color: _primaryColor,
          direction: ArrowDirection.right,
        ),
      ),
    );
  }
  
  // Build the output arrow
  Widget _buildOutputArrow() {
    return SizedBox(
      width: 40 + 100 * (_animation.value - 0.5) * 2,
      height: 40,
      child: CustomPaint(
        painter: ArrowPainter(
          color: _accentColor,
          direction: ArrowDirection.right,
        ),
      ),
    );
  }
}

// Arrow direction enum
enum ArrowDirection {
  left,
  right,
  up,
  down,
}

// Arrow painter
class ArrowPainter extends CustomPainter {
  final Color color;
  final ArrowDirection direction;
  
  ArrowPainter({
    required this.color,
    required this.direction,
  });
  
  @override
  void paint(Canvas canvas, Size size) {
    final paint = Paint()
      ..color = color
      ..style = PaintingStyle.stroke
      ..strokeWidth = 2;
    
    final path = Path();
    
    switch (direction) {
      case ArrowDirection.right:
        // Line
        path.moveTo(0, size.height / 2);
        path.lineTo(size.width - 10, size.height / 2);
        
        // Arrow head
        path.moveTo(size.width - 20, size.height / 2 - 10);
        path.lineTo(size.width - 10, size.height / 2);
        path.lineTo(size.width - 20, size.height / 2 + 10);
        break;
      case ArrowDirection.left:
        // Line
        path.moveTo(size.width, size.height / 2);
        path.lineTo(10, size.height / 2);
        
        // Arrow head
        path.moveTo(20, size.height / 2 - 10);
        path.lineTo(10, size.height / 2);
        path.lineTo(20, size.height / 2 + 10);
        break;
      case ArrowDirection.up:
        // Line
        path.moveTo(size.width / 2, size.height);
        path.lineTo(size.width / 2, 10);
        
        // Arrow head
        path.moveTo(size.width / 2 - 10, 20);
        path.lineTo(size.width / 2, 10);
        path.lineTo(size.width / 2 + 10, 20);
        break;
      case ArrowDirection.down:
        // Line
        path.moveTo(size.width / 2, 0);
        path.lineTo(size.width / 2, size.height - 10);
        
        // Arrow head
        path.moveTo(size.width / 2 - 10, size.height - 20);
        path.lineTo(size.width / 2, size.height - 10);
        path.lineTo(size.width / 2 + 10, size.height - 20);
        break;
    }
    
    canvas.drawPath(path, paint);
  }
  
  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) {
    return true;
  }
}
