import 'package:flutter/material.dart';
import 'package:flutter/services.dart'; // For HapticFeedback
import '../../../../models/course_models.dart';

class TextInputQuickElementWidget extends StatefulWidget {
  final TextInputQuickElement textInputQuickElement;
  final VoidCallback? onNextAction; // For advancing slide or lesson
  final bool isLastSlideInLesson;

  const TextInputQuickElementWidget({
    super.key,
    required this.textInputQuickElement,
    this.onNextAction,
    this.isLastSlideInLesson = false,
  });

  @override
  State<TextInputQuickElementWidget> createState() => _TextInputQuickElementWidgetState();
}

class _TextInputQuickElementWidgetState extends State<TextInputQuickElementWidget> {
  final TextEditingController _textController = TextEditingController();
  bool _isSubmitted = false;
  bool _isCorrect = false;
  String _feedbackMessage = "";

  void _handleSubmit(String value) {
    if (_isSubmitted) return; // Prevent re-submission

    HapticFeedback.lightImpact();
    bool correct = false;
    try {
      final regex = RegExp(widget.textInputQuickElement.correct_answer_regex);
      correct = regex.hasMatch(value.trim());
    } catch (e) {
      print(
        "Error with regex: ${widget.textInputQuickElement.correct_answer_regex} - $e",
      );
      // Default to false if regex is invalid
    }

    setState(() {
      _isSubmitted = true;
      _isCorrect = correct;
      _feedbackMessage = correct
          ? widget.textInputQuickElement.feedback_correct
          : "Let's try that again.";
    });

    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(_feedbackMessage),
        backgroundColor: _isCorrect ? Colors.green : Colors.redAccent,
        duration: const Duration(seconds: 2),
      ),
    );

    // If correct, automatically proceed to next screen after a short delay
    if (_isCorrect && widget.textInputQuickElement.action_on_correct == 'next_screen_auto') {
      Future.delayed(const Duration(milliseconds: 1500), () {
        if (widget.onNextAction != null && mounted) {
          widget.onNextAction!();
        }
      });
    }
  }

  @override
  void dispose() {
    _textController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final textTheme = Theme.of(context).textTheme;

    return Container(
      padding: const EdgeInsets.symmetric(
        horizontal: 8.0,
        vertical: 16.0,
      ),
      margin: const EdgeInsets.symmetric(
        vertical: 12.0,
        horizontal: 4.0,
      ),
      decoration: BoxDecoration(
        color: Colors.blue[50], // Different background color from TextInputElement
        borderRadius: BorderRadius.circular(16.0),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.stretch,
        children: [
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 8.0),
            child: TextField(
              controller: _textController,
              style: textTheme.bodyLarge?.copyWith(
                fontSize: 18,
              ),
              decoration: InputDecoration(
                hintText: "Enter your answer...",
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(10.0),
                  borderSide: BorderSide(color: Colors.grey.shade400),
                ),
                focusedBorder: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(10.0),
                  borderSide: BorderSide(
                    color: Theme.of(context).primaryColor,
                    width: 2.0,
                  ),
                ),
                filled: true,
                fillColor: Colors.white,
                contentPadding: const EdgeInsets.symmetric(
                  horizontal: 16.0,
                  vertical: 14.0,
                ),
                suffixIcon: _isSubmitted
                    ? Icon(
                        _isCorrect
                            ? Icons.check_circle_outline_rounded
                            : Icons.highlight_off_rounded,
                        color: _isCorrect
                            ? Colors.green.shade600
                            : Colors.red.shade600,
                        size: 26,
                      )
                    : null,
              ),
              onSubmitted: _handleSubmit,
              textInputAction: TextInputAction.done,
              enabled: !_isSubmitted,
            ),
          ),
          if (_isSubmitted && !_isCorrect)
            Padding(
              padding: const EdgeInsets.only(top: 24.0, left: 8.0, right: 8.0),
              child: SizedBox(
                width: double.infinity,
                height: 56,
                child: ElevatedButton.icon(
                  icon: const Icon(Icons.refresh, size: 22),
                  label: const Text("Try Again"),
                  onPressed: () {
                    setState(() {
                      _isSubmitted = false;
                      _textController.clear();
                    });
                  },
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Colors.grey.shade500,
                    foregroundColor: Colors.white,
                    padding: const EdgeInsets.symmetric(vertical: 14.0),
                    textStyle: const TextStyle(
                      fontSize: 17.0,
                      fontWeight: FontWeight.bold,
                    ),
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(12.0),
                    ),
                    elevation: 1,
                  ),
                ),
              ),
            ),
          if (_isSubmitted && _isCorrect && widget.textInputQuickElement.action_on_correct != 'next_screen_auto')
            Padding(
              padding: const EdgeInsets.only(top: 24.0, left: 8.0, right: 8.0),
              child: SizedBox(
                width: double.infinity,
                height: 56,
                child: ElevatedButton.icon(
                  icon: const Icon(Icons.check, size: 22),
                  label: const Text("Check"),
                  onPressed: widget.onNextAction,
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Theme.of(context).primaryColor,
                    foregroundColor: Colors.white,
                    padding: const EdgeInsets.symmetric(vertical: 14.0),
                    textStyle: const TextStyle(
                      fontSize: 17.0,
                      fontWeight: FontWeight.bold,
                    ),
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(12.0),
                    ),
                    elevation: 3,
                  ),
                ),
              ),
            ),
        ],
      ),
    );
  }
}
