import 'package:flutter/material.dart';

/// A widget that allows users to evaluate scientific theories based on various criteria
class InteractiveTheoryEvaluationToolWidget extends StatefulWidget {
  /// The data for this widget
  final Map<String, dynamic> data;

  /// Optional callback when the widget state changes
  final Function(bool isCompleted)? onStateChanged;

  const InteractiveTheoryEvaluationToolWidget({
    super.key,
    required this.data,
    this.onStateChanged,
  });

  /// Factory constructor to create the widget from JSON data
  factory InteractiveTheoryEvaluationToolWidget.fromData(Map<String, dynamic> data) {
    return InteractiveTheoryEvaluationToolWidget(
      data: data,
    );
  }

  @override
  State<InteractiveTheoryEvaluationToolWidget> createState() => _InteractiveTheoryEvaluationToolWidgetState();
}

class _InteractiveTheoryEvaluationToolWidgetState extends State<InteractiveTheoryEvaluationToolWidget> {
  // Theories to evaluate
  late List<Theory> _theories;
  late int _currentTheoryIndex;

  // Evaluation criteria
  late List<EvaluationCriterion> _criteria;
  late Map<String, double> _ratings;

  // UI state
  late bool _isCompleted;
  late bool _showExplanation;
  late bool _hasSubmitted;
  late String _feedback;
  late double _overallScore;

  // Colors
  late Color _primaryColor;
  late Color _secondaryColor;
  late Color _accentColor;
  late Color _backgroundColor;
  late Color _textColor;

  @override
  void initState() {
    super.initState();
    _initializeWidget();
  }

  void _initializeWidget() {
    // Initialize colors
    _primaryColor = _parseColor(widget.data['primaryColor'] ?? '#2196F3');
    _secondaryColor = _parseColor(widget.data['secondaryColor'] ?? '#FF9800');
    _accentColor = _parseColor(widget.data['accentColor'] ?? '#4CAF50');
    _backgroundColor = _parseColor(widget.data['backgroundColor'] ?? '#FFFFFF');
    _textColor = _parseColor(widget.data['textColor'] ?? '#212121');

    // Initialize theories
    final List<dynamic> theoriesData = widget.data['theories'] ?? [];
    _theories = theoriesData.map((theoryData) => Theory.fromJson(theoryData)).toList();
    _currentTheoryIndex = 0;

    // Initialize criteria
    final List<dynamic> criteriaData = widget.data['criteria'] ?? [];
    _criteria = criteriaData.map((criterionData) => EvaluationCriterion.fromJson(criterionData)).toList();

    // Initialize ratings
    _ratings = {};
    for (var criterion in _criteria) {
      _ratings[criterion.id] = 0.0;
    }

    // Initialize UI state
    _isCompleted = false;
    _showExplanation = false;
    _hasSubmitted = false;
    _feedback = '';
    _overallScore = 0.0;
  }

  Color _parseColor(String colorString) {
    if (colorString.startsWith('#')) {
      return Color(int.parse('FF${colorString.substring(1)}', radix: 16));
    }
    return Colors.blue;
  }

  void _updateRating(String criterionId, double value) {
    setState(() {
      _ratings[criterionId] = value;
    });
  }

  void _submitEvaluation() {
    // Calculate overall score
    double totalScore = 0.0;
    int totalWeight = 0;

    for (var criterion in _criteria) {
      totalScore += (_ratings[criterion.id] ?? 0.0) * criterion.weight;
      totalWeight += criterion.weight;
    }

    _overallScore = totalWeight > 0 ? totalScore / totalWeight : 0.0;

    // Determine feedback based on score
    Theory theory = _theories[_currentTheoryIndex];
    if (_overallScore >= 4.0) {
      _feedback = theory.highScoreFeedback;
    } else if (_overallScore >= 2.5) {
      _feedback = theory.mediumScoreFeedback;
    } else {
      _feedback = theory.lowScoreFeedback;
    }

    setState(() {
      _hasSubmitted = true;
    });
  }

  void _resetEvaluation() {
    setState(() {
      for (var criterion in _criteria) {
        _ratings[criterion.id] = 0.0;
      }
      _hasSubmitted = false;
      _feedback = '';
      _overallScore = 0.0;
    });
  }

  void _nextTheory() {
    if (_currentTheoryIndex < _theories.length - 1) {
      setState(() {
        _currentTheoryIndex++;
        _resetEvaluation();
        _showExplanation = false;
      });
    } else if (!_isCompleted) {
      setState(() {
        _isCompleted = true;
      });
      widget.onStateChanged?.call(true);
    }
  }

  void _toggleExplanation() {
    setState(() {
      _showExplanation = !_showExplanation;
    });
  }

  @override
  Widget build(BuildContext context) {
    if (_theories.isEmpty) {
      return const Center(child: Text('No theories to evaluate'));
    }

    Theory theory = _theories[_currentTheoryIndex];

    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
        side: BorderSide(color: _primaryColor.withOpacity(0.5), width: 1),
      ),
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Title
            Text(
              widget.data['title'] ?? 'Theory Evaluation Tool',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
                color: _textColor,
              ),
            ),

            const SizedBox(height: 8),

            // Theory navigation
            Row(
              children: [
                Text(
                  'Theory ${_currentTheoryIndex + 1} of ${_theories.length}: ${theory.name}',
                  style: TextStyle(
                    fontSize: 14,
                    fontWeight: FontWeight.bold,
                    color: _primaryColor,
                  ),
                ),
                const Spacer(),
                IconButton(
                  icon: Icon(_showExplanation ? Icons.info_outline : Icons.info),
                  onPressed: _hasSubmitted ? _toggleExplanation : null,
                  tooltip: _showExplanation ? 'Hide Explanation' : 'Show Explanation',
                  color: _secondaryColor,
                ),
              ],
            ),

            const SizedBox(height: 8),

            // Theory description
            Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: _backgroundColor,
                borderRadius: BorderRadius.circular(8),
                border: Border.all(color: _primaryColor.withOpacity(0.3)),
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'Description:',
                    style: TextStyle(
                      fontWeight: FontWeight.bold,
                      color: _textColor,
                    ),
                  ),
                  const SizedBox(height: 4),
                  Text(
                    theory.description,
                    style: TextStyle(color: _textColor.withOpacity(0.8)),
                  ),
                ],
              ),
            ),

            const SizedBox(height: 16),

            // Evaluation criteria
            Text(
              'Evaluate this theory on the following criteria:',
              style: TextStyle(
                fontWeight: FontWeight.bold,
                color: _textColor,
              ),
            ),

            const SizedBox(height: 8),

            // Criteria sliders
            ..._criteria.map((criterion) => _buildCriterionSlider(criterion)),

            const SizedBox(height: 16),

            // Submit button
            if (!_hasSubmitted)
              Center(
                child: ElevatedButton(
                  onPressed: _submitEvaluation,
                  style: ElevatedButton.styleFrom(
                    backgroundColor: _primaryColor,
                    foregroundColor: Colors.white,
                    padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(8),
                    ),
                  ),
                  child: const Text('Submit Evaluation'),
                ),
              ),

            // Results and feedback
            if (_hasSubmitted) ...[
              Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: _backgroundColor,
                  borderRadius: BorderRadius.circular(8),
                  border: Border.all(color: _accentColor),
                ),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        Text(
                          'Overall Score:',
                          style: TextStyle(
                            fontWeight: FontWeight.bold,
                            color: _textColor,
                          ),
                        ),
                        Text(
                          '${_overallScore.toStringAsFixed(1)}/5.0',
                          style: TextStyle(
                            fontWeight: FontWeight.bold,
                            color: _getScoreColor(_overallScore),
                          ),
                        ),
                      ],
                    ),
                    const SizedBox(height: 8),
                    Text(
                      'Feedback:',
                      style: TextStyle(
                        fontWeight: FontWeight.bold,
                        color: _textColor,
                      ),
                    ),
                    const SizedBox(height: 4),
                    Text(
                      _feedback,
                      style: TextStyle(color: _textColor.withOpacity(0.8)),
                    ),
                  ],
                ),
              ),

              // Explanation (if shown)
              if (_showExplanation) ...[
                const SizedBox(height: 16),
                Container(
                  padding: const EdgeInsets.all(12),
                  decoration: BoxDecoration(
                    color: _secondaryColor.withOpacity(0.1),
                    borderRadius: BorderRadius.circular(8),
                    border: Border.all(color: _secondaryColor.withOpacity(0.3)),
                  ),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'Expert Evaluation:',
                        style: TextStyle(
                          fontWeight: FontWeight.bold,
                          color: _secondaryColor,
                        ),
                      ),
                      const SizedBox(height: 4),
                      Text(
                        theory.expertEvaluation,
                        style: TextStyle(color: _textColor.withOpacity(0.8)),
                      ),
                    ],
                  ),
                ),
              ],

              const SizedBox(height: 16),

              // Navigation buttons
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                children: [
                  OutlinedButton(
                    onPressed: _resetEvaluation,
                    style: OutlinedButton.styleFrom(
                      foregroundColor: _primaryColor,
                      side: BorderSide(color: _primaryColor),
                      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                    ),
                    child: const Text('Reset'),
                  ),
                  ElevatedButton(
                    onPressed: _nextTheory,
                    style: ElevatedButton.styleFrom(
                      backgroundColor: _primaryColor,
                      foregroundColor: Colors.white,
                      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                    ),
                    child: Text(_currentTheoryIndex < _theories.length - 1
                        ? 'Next Theory'
                        : 'Finish'),
                  ),
                ],
              ),
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildCriterionSlider(EvaluationCriterion criterion) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 8.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Expanded(
                child: Text(
                  criterion.name,
                  style: TextStyle(
                    fontWeight: FontWeight.w500,
                    color: _textColor,
                  ),
                ),
              ),
              Text(
                '${(_ratings[criterion.id] ?? 0.0).toStringAsFixed(1)}/5.0',
                style: TextStyle(
                  fontWeight: FontWeight.bold,
                  color: _getScoreColor(_ratings[criterion.id] ?? 0.0),
                ),
              ),
            ],
          ),
          const SizedBox(height: 4),
          Text(
            criterion.description,
            style: TextStyle(
              fontSize: 12,
              color: _textColor.withOpacity(0.7),
            ),
          ),
          Slider(
            value: _ratings[criterion.id] ?? 0.0,
            min: 0.0,
            max: 5.0,
            divisions: 10,
            label: (_ratings[criterion.id] ?? 0.0).toStringAsFixed(1),
            onChanged: _hasSubmitted ? null : (value) => _updateRating(criterion.id, value),
            activeColor: _primaryColor,
            inactiveColor: _primaryColor.withOpacity(0.2),
          ),
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                'Poor',
                style: TextStyle(
                  fontSize: 10,
                  color: _textColor.withOpacity(0.6),
                ),
              ),
              Text(
                'Excellent',
                style: TextStyle(
                  fontSize: 10,
                  color: _textColor.withOpacity(0.6),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Color _getScoreColor(double score) {
    if (score >= 4.0) {
      return Colors.green;
    } else if (score >= 2.5) {
      return Colors.orange;
    } else {
      return Colors.red;
    }
  }
}

/// Represents a scientific theory to be evaluated
class Theory {
  final String id;
  final String name;
  final String description;
  final String expertEvaluation;
  final String highScoreFeedback;
  final String mediumScoreFeedback;
  final String lowScoreFeedback;

  Theory({
    required this.id,
    required this.name,
    required this.description,
    required this.expertEvaluation,
    required this.highScoreFeedback,
    required this.mediumScoreFeedback,
    required this.lowScoreFeedback,
  });

  factory Theory.fromJson(Map<String, dynamic> json) {
    return Theory(
      id: json['id'] as String,
      name: json['name'] as String,
      description: json['description'] as String,
      expertEvaluation: json['expertEvaluation'] as String,
      highScoreFeedback: json['highScoreFeedback'] as String,
      mediumScoreFeedback: json['mediumScoreFeedback'] as String,
      lowScoreFeedback: json['lowScoreFeedback'] as String,
    );
  }
}

/// Represents a criterion for evaluating a scientific theory
class EvaluationCriterion {
  final String id;
  final String name;
  final String description;
  final int weight;

  EvaluationCriterion({
    required this.id,
    required this.name,
    required this.description,
    required this.weight,
  });

  factory EvaluationCriterion.fromJson(Map<String, dynamic> json) {
    return EvaluationCriterion(
      id: json['id'] as String,
      name: json['name'] as String,
      description: json['description'] as String,
      weight: json['weight'] as int,
    );
  }
}
