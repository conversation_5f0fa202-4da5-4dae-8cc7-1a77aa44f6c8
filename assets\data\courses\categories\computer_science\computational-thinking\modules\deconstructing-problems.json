{"id": "deconstructing-problems", "title": "Deconstructing Problems", "description": "Master the art of breaking down complex challenges into manageable parts.", "order": 1, "lessons": [{"id": "understanding-decomposition", "title": "Understanding Decomposition", "description": "Learn what decomposition is and why it's a cornerstone of problem-solving.", "order": 1, "type": "interactive_lesson", "estimatedTimeMinutes": 10, "contentBlocks": [{"id": "screen1_intro_decomposition", "type": "lesson_screen", "order": 1, "estimatedTimeSeconds": 60, "content": {"headline": "Big Problems, Small Pieces", "body_md": "Ever faced a problem so big it felt overwhelming? Decomposition is the skill of breaking down large, complex problems into smaller, more manageable sub-problems. It's like trying to eat an elephant – you do it one bite at a time!\n\nWhy is this so powerful?", "visual": {"type": "giphy_search", "value": "puzzle pieces coming together"}, "interactive_element": {"type": "button", "button_text": "Let's Explore Why"}, "audio_narration_url": null}}, {"id": "screen2_why_decompose", "type": "lesson_screen", "order": 2, "estimatedTimeSeconds": 90, "content": {"headline": "Benefits of Decomposition", "body_md": "Decomposition helps us by:\n\n*   **Reducing Complexity:** Smaller problems are easier to understand and solve.\n*   **Allowing Parallel Work:** Different people (or parts of a program) can work on different sub-problems simultaneously.\n*   **Easier Testing & Debugging:** It's simpler to test and fix issues in smaller, isolated components.\n*   **Facilitating Reuse:** Solved sub-problems can often be reused in other contexts.\n\nWhich benefit do you think is most crucial for software development?", "visual": {"type": "unsplash_search", "value": "team working on puzzle"}, "interactive_element": {"type": "multiple_choice_text", "question_text": "Most crucial benefit for software development?", "options": [{"text": "Reducing Complexity", "is_correct": true, "feedback": "Correct! Managing complexity is a huge challenge in software, and decomposition is key."}, {"text": "Allowing Parallel Work", "is_correct": false, "feedback": "Important for team efficiency, but managing complexity is often more fundamental."}, {"text": "Easier Testing & Debugging", "is_correct": false, "feedback": "A very significant benefit, but often a consequence of reduced complexity."}, {"text": "Facilitating Reuse", "is_correct": false, "feedback": "Highly valuable for efficiency, but arguably stems from well-defined, less complex components."}]}, "audio_narration_url": null}}, {"id": "screen3_decomposition_example_irl", "type": "lesson_screen", "order": 3, "estimatedTimeSeconds": 75, "content": {"headline": "Decomposition in Everyday Life", "body_md": "We use decomposition all the time without realizing it! For example, planning a party involves breaking it down into tasks:\n\n1.  Set a date & time.\n2.  Create a guest list.\n3.  Send invitations.\n4.  Plan the menu.\n5.  Buy groceries.\n6.  Prepare food.\n7.  Decorate.\n\nCan you think of another everyday example?", "visual": {"type": "giphy_search", "value": "planning party"}, "interactive_element": {"type": "text_input", "question_text": "Another everyday example of decomposition:", "placeholder_text": "e.g., Writing a book", "correct_answer_regex": ".+", "feedback_correct": "Great example! Many complex tasks become simpler when broken down."}, "audio_narration_url": null}}, {"id": "screen4_decomposition_in_cs", "type": "lesson_screen", "order": 4, "estimatedTimeSeconds": 90, "content": {"headline": "Decomposition in Computer Science", "body_md": "In computer science, decomposition is fundamental. A large software application is broken down into:\n\n*   **Modules:** Collections of related functions or classes.\n*   **Functions/Methods:** Specific tasks within a module.\n*   **Statements:** Individual instructions within a function.\n\nThis hierarchical breakdown makes complex software buildable and maintainable.", "visual": {"type": "unsplash_search", "value": "flowchart software architecture"}, "interactive_element": {"type": "button", "button_text": "How Deep to Go?"}, "audio_narration_url": null}}, {"id": "screen5_level_of_decomposition", "type": "lesson_screen", "order": 5, "estimatedTimeSeconds": 75, "content": {"headline": "How Far Do You Decompose?", "body_md": "The goal is to break a problem down until each sub-problem is simple enough to be solved directly or easily understood.\n\n*   **Too little decomposition:** Sub-problems are still too complex.\n*   **Too much decomposition:** Can lead to an excessive number of tiny, trivial parts, making the overall structure hard to see.\n\nFinding the right balance is a skill that develops with practice.", "visual": {"type": "giphy_search", "value": "balance scale"}, "interactive_element": {"type": "button", "button_text": "Lesson Summary"}, "audio_narration_url": null}}, {"id": "screen6_lesson1_summary", "type": "lesson_screen", "order": 6, "estimatedTimeSeconds": 60, "content": {"headline": "Lesson Recap: Understanding Decomposition", "body_md": "In this lesson, we've learned:\n\n*   Decomposition is breaking large problems into smaller, manageable parts.\n*   It reduces complexity, enables parallel work, simplifies testing, and promotes reuse.\n*   We use it in everyday life and it's crucial in computer science.\n*   Finding the right level of decomposition is key.\n\nReady to learn some techniques?", "visual": {"type": "giphy_search", "value": "thumbs up success"}, "interactive_element": {"type": "button", "button_text": "Next: Techniques"}, "audio_narration_url": null}}]}, {"id": "techniques-for-breaking-down-problems", "title": "Techniques for Breaking Down Problems", "description": "Explore various methods and strategies for effective problem decomposition.", "order": 2, "type": "interactive_lesson", "estimatedTimeMinutes": 12, "contentBlocks": [{"id": "screen1_techniques_intro", "type": "lesson_screen", "order": 1, "estimatedTimeSeconds": 60, "content": {"headline": "How to Decompose Effectively", "body_md": "Knowing *why* to decompose is one thing, but *how* do you actually do it? There are several common techniques and approaches. Let's look at a few.", "visual": {"type": "giphy_search", "value": "strategy plan"}, "interactive_element": {"type": "button", "button_text": "Explore Techniques"}, "audio_narration_url": null}}, {"id": "screen2_top_down_design", "type": "lesson_screen", "order": 2, "estimatedTimeSeconds": 90, "content": {"headline": "Top-Down Design (Stepwise Refinement)", "body_md": "Start with the overall problem and break it into a few large sub-problems. Then, take each sub-problem and break it down further, and so on, until you reach manageable pieces.\n\n**Example: Building a Website**\n1.  Overall: Build Website\n2.  Sub-problems: Design UI, Develop Frontend, Develop Backend, Deploy\n3.  Further breakdown (e.g., Develop Frontend): Create Homepage, Create Product Pages, Implement Cart\n\nThis creates a hierarchy of tasks.", "visual": {"type": "unsplash_search", "value": "pyramid structure"}, "interactive_element": {"type": "multiple_choice_text", "question_text": "Top-down design starts with:", "options": [{"text": "The smallest details", "is_correct": false, "feedback": "That would be bottom-up design."}, {"text": "The overall problem", "is_correct": true, "feedback": "Correct! Top-down starts with the big picture and refines it downwards."}, {"text": "A random component", "is_correct": false, "feedback": "Effective decomposition is usually more systematic."}]}, "audio_narration_url": null}}, {"id": "screen3_functional_decomposition", "type": "lesson_screen", "order": 3, "estimatedTimeSeconds": 90, "content": {"headline": "Functional Decomposition", "body_md": "This technique involves breaking a problem down based on the **functions** or actions that need to be performed. Each function becomes a separate module or component.\n\n**Example: A Text Editor**\n*   Function: Open File\n*   Function: Save File\n*   Function: Edit Text\n*   Function: Search Text\n*   Function: Print Document\n\nThis is very common in software design.", "visual": {"type": "giphy_search", "value": "gears working"}, "interactive_element": {"type": "button", "button_text": "Another Technique?"}, "audio_narration_url": null}}, {"id": "screen4_data_decomposition", "type": "lesson_screen", "order": 4, "estimatedTimeSeconds": 90, "content": {"headline": "Data Decomposition", "body_md": "Here, you break down the problem based on the **structure of the data** being processed. This is useful when the data itself has a clear hierarchy or components.\n\n**Example: Processing Student Records**\n*   Overall: Process All Student Records\n*   Sub-problem: Process Records for Each Grade Level\n*   Further: Process Each Student's Record (Personal Info, Grades, Attendance)\n\nThis often aligns well with object-oriented programming.", "visual": {"type": "unsplash_search", "value": "data charts"}, "interactive_element": {"type": "button", "button_text": "What about Interfaces?"}, "audio_narration_url": null}}, {"id": "screen5_interface_definition", "type": "lesson_screen", "order": 5, "estimatedTimeSeconds": 75, "content": {"headline": "Defining Interfaces", "body_md": "A crucial part of decomposition is defining clear **interfaces** between the sub-problems (or modules).\n\nAn interface specifies how different parts of the system will interact: what inputs they expect and what outputs they produce, without revealing their internal workings.\n\nThis allows parts to be developed and changed independently.", "visual": {"type": "giphy_search", "value": "connecting handshake"}, "interactive_element": {"type": "button", "button_text": "Lesson Summary"}, "audio_narration_url": null}}, {"id": "screen6_lesson2_summary", "type": "lesson_screen", "order": 6, "estimatedTimeSeconds": 60, "content": {"headline": "Lesson Recap: Decomposition Techniques", "body_md": "We've explored:\n\n*   **Top-Down Design:** Breaking problems hierarchically.\n*   **Functional Decomposition:** Breaking down by actions/functions.\n*   **Data Decomposition:** Breaking down by data structure.\n*   The importance of **Defining Interfaces** between components.\n\nThese techniques help structure your thinking.", "visual": {"type": "giphy_search", "value": "tools"}, "interactive_element": {"type": "button", "button_text": "Next: Real-World Apps"}, "audio_narration_url": null}}]}, {"id": "applying-decomposition-real-world", "title": "Applying Decomposition to Real-World Scenarios", "description": "Practice deconstructing complex real-world problems.", "order": 3, "type": "interactive_lesson", "estimatedTimeMinutes": 10, "contentBlocks": [{"id": "screen1_real_world_intro", "type": "lesson_screen", "order": 1, "estimatedTimeSeconds": 60, "content": {"headline": "Decomposition in Action", "body_md": "Let's apply what we've learned to some real-world scenarios. The best way to get good at decomposition is to practice it!", "visual": {"type": "giphy_search", "value": "gears turning brain"}, "interactive_element": {"type": "button", "button_text": "First Scenario"}, "audio_narration_url": null}}, {"id": "screen2_scenario_atm", "type": "lesson_screen", "order": 2, "estimatedTimeSeconds": 120, "content": {"headline": "Scenario: Designing an ATM", "body_md": "Imagine you need to design the software for an Automated Teller Machine (ATM). How would you decompose this problem using functional decomposition?\n\nThink about the main actions an ATM performs.", "visual": {"type": "unsplash_search", "value": "ATM machine"}, "interactive_element": {"type": "text_input", "question_text": "List 3 main functions of an ATM:", "placeholder_text": "e.g., Check Balance, Withdraw Cash, ...", "correct_answer_regex": ".+", "feedback_correct": "Good start! Common functions include: Check Balance, Withdraw Cash, Deposit Funds, Transfer Funds, PIN Validation."}, "audio_narration_url": null}}, {"id": "screen3_scenario_atm_deeper", "type": "lesson_screen", "order": 3, "estimatedTimeSeconds": 90, "content": {"headline": "ATM: Deeper Dive", "body_md": "Let's take one function, 'Withdraw Cash', and decompose it further.\n\nWhat sub-tasks might be involved in withdrawing cash after a user has been authenticated?", "visual": {"type": "giphy_search", "value": "money cash"}, "interactive_element": {"type": "multiple_choice_text", "question_text": "A key sub-task for 'Withdraw Cash' is:", "options": [{"text": "Displaying advertisements", "is_correct": false, "feedback": "While ATMs might do this, it's not core to the cash withdrawal process itself."}, {"text": "Checking account balance for sufficient funds", "is_correct": true, "feedback": "Absolutely! This is a critical step."}, {"text": "Printing a list of nearby restaurants", "is_correct": false, "feedback": "That's an unrelated feature."}]}, "audio_narration_url": null}}, {"id": "screen4_scenario_social_media", "type": "lesson_screen", "order": 4, "estimatedTimeSeconds": 120, "content": {"headline": "Scenario: Building a Simple Social Media App", "body_md": "Consider building a basic social media application. Using top-down design, what would be some high-level components or modules?", "visual": {"type": "unsplash_search", "value": "social media icons"}, "interactive_element": {"type": "text_input", "question_text": "List 2-3 high-level modules for a social app:", "placeholder_text": "e.g., User Profiles, News Feed, ...", "correct_answer_regex": ".+", "feedback_correct": "Excellent! Common modules include User Authentication, User Profiles, Post Creation, News Feed, Messaging, Notifications."}, "audio_narration_url": null}}, {"id": "screen5_lesson3_summary", "type": "lesson_screen", "order": 5, "estimatedTimeSeconds": 60, "content": {"headline": "Lesson Recap: Real-World Application", "body_md": "We've seen how decomposition can be applied to complex systems like ATMs and social media apps.\n\nKey takeaways:\n*   Start by identifying major functions or components.\n*   Break these down into smaller, more detailed sub-tasks.\n*   Always think about how these components will interact (interfaces).\n\nPractice is the best way to improve this skill!", "visual": {"type": "giphy_search", "value": "practice makes perfect"}, "interactive_element": {"type": "button", "button_text": "Module Test Time!"}, "audio_narration_url": null}}]}], "moduleTest": {"id": "deconstructing-problems-test", "title": "Module Test: Deconstructing Problems", "description": "Test your understanding of problem decomposition.", "type": "module_test_interactive", "estimatedTimeMinutes": 8, "contentBlocks": [{"id": "test_q1_definition", "type": "question_screen", "order": 1, "estimatedTimeSeconds": 60, "content": {"headline": "Question 1: Definition", "body_md": "What is the primary goal of problem decomposition?", "visual": {"type": "giphy_search", "value": "question mark thinking"}, "interactive_element": {"type": "multiple_choice_text", "question_text": "Primary goal of decomposition?", "options": [{"text": "To make a problem look more complex.", "is_correct": false, "feedback": "The goal is the opposite – to reduce complexity."}, {"text": "To break a large problem into smaller, manageable sub-problems.", "is_correct": true, "feedback": "Correct! This makes the problem easier to solve."}, {"text": "To solve the entire problem in one single step.", "is_correct": false, "feedback": "Decomposition is about breaking it into multiple steps or parts."}]}, "audio_narration_url": null}}, {"id": "test_q2_benefit", "type": "question_screen", "order": 2, "estimatedTimeSeconds": 75, "content": {"headline": "Question 2: Benefits", "body_md": "Which of the following is NOT a direct benefit of decomposition?", "visual": {"type": "unsplash_search", "value": "checklist"}, "interactive_element": {"type": "multiple_choice_text", "question_text": "NOT a direct benefit of decomposition?", "options": [{"text": "Reduced complexity", "is_correct": false, "feedback": "This is a key benefit of decomposition."}, {"text": "<PERSON><PERSON><PERSON><PERSON> to find the most optimal solution", "is_correct": true, "feedback": "Correct! Decomposition helps in solving, but doesn't guarantee optimality on its own."}, {"text": "Easier testing and debugging", "is_correct": false, "feedback": "Smaller, isolated parts are easier to test and debug."}]}, "audio_narration_url": null}}, {"id": "test_q3_top_down", "type": "question_screen", "order": 3, "estimatedTimeSeconds": 75, "content": {"headline": "Question 3: Top-Down Design", "body_md": "Top-down design (stepwise refinement) involves:", "visual": {"type": "giphy_search", "value": "arrow pointing down"}, "interactive_element": {"type": "multiple_choice_text", "question_text": "Top-down design involves:", "options": [{"text": "Starting with the smallest components and building up.", "is_correct": false, "feedback": "This describes bottom-up design."}, {"text": "Breaking the overall problem into sub-problems, then refining those further.", "is_correct": true, "feedback": "Exactly! It's a hierarchical approach from general to specific."}, {"text": "Focusing only on the data structures involved.", "is_correct": false, "feedback": "That would be more aligned with data decomposition."}]}, "audio_narration_url": null}}, {"id": "test_q4_interfaces", "type": "question_screen", "order": 4, "estimatedTimeSeconds": 60, "content": {"headline": "Question 4: Interfaces", "body_md": "Why is defining clear interfaces between decomposed components important?", "visual": {"type": "unsplash_search", "value": "connection puzzle pieces"}, "interactive_element": {"type": "multiple_choice_text", "question_text": "Importance of interfaces?", "options": [{"text": "To make each component's internal workings visible to all other components.", "is_correct": false, "feedback": "Good interfaces hide internal workings (encapsulation)."}, {"text": "To allow components to be developed and changed independently.", "is_correct": true, "feedback": "Correct! Well-defined interfaces enable modularity and independent work."}, {"text": "To ensure all components use the exact same programming language.", "is_correct": false, "feedback": "Interfaces are about how components interact, not necessarily their implementation language."}]}, "audio_narration_url": null}}]}}