{"id": "mathematical-framework", "title": "THE MATHEMATICAL FRAMEWORK", "description": "Introduce the basic mathematical tools and principles used to describe quantum systems.", "order": 2, "lessons": [{"id": "wave-functions", "title": "Wave Functions: Describing Quantum States", "description": "Understand the probabilistic interpretation.", "order": 1, "type": "interactive_lesson", "estimatedTimeMinutes": 10, "contentBlocks": [{"id": "wf-screen1-intro", "type": "lesson_screen", "order": 1, "estimatedTimeSeconds": 30, "content": {"headline": "The Quantum Description of Reality", "body_md": "In the 1920s, physicists needed a new mathematical framework to describe the strange quantum world. The key breakthrough came with the concept of the **wave function**—a mathematical object that contains all the information about a quantum system.", "visual": {"type": "giphy_search", "value": "quantum wave function"}, "hook": "This mathematical tool would revolutionize physics and provide a way to describe particles that are neither purely waves nor purely particles.", "interactive_element": {"type": "button", "text": "Let's explore!", "action": "next_screen"}}}, {"id": "wf-screen2-what-is", "type": "lesson_screen", "order": 2, "estimatedTimeSeconds": 60, "content": {"headline": "What is a Wave Function?", "body_md": "A **wave function** (usually denoted by the Greek letter ψ, \"psi\") is a mathematical function that describes the quantum state of a particle or system.\n\nUnlike classical physics, where we can specify a particle's exact position and momentum, the wave function gives us the **probability** of finding a particle in different possible states.\n\nFor a single particle, the wave function ψ(x,t) depends on position (x) and time (t).", "visual": {"type": "unsplash_search", "value": "probability wave"}, "interactive_element": {"type": "button", "text": "How do we interpret it?", "action": "next_screen"}}}, {"id": "wf-screen3-born-rule", "type": "lesson_screen", "order": 3, "estimatedTimeSeconds": 70, "content": {"headline": "The Born Rule: Probability Interpretation", "body_md": "In 1926, <PERSON> proposed that the wave function has a probabilistic interpretation:\n\n**|ψ(x,t)|²** gives the probability density of finding the particle at position x at time t.\n\nThis means:\n• The wave function itself (ψ) is not directly measurable\n• We square its absolute value to get probabilities\n• The total probability of finding the particle somewhere must equal 1 (normalization)\n\nThis interpretation was revolutionary—quantum mechanics is inherently probabilistic!", "interactive_element": {"type": "multiple_choice_text", "question_text": "According to the Born rule, what does |ψ(x,t)|² represent?", "options": [{"id": "wf3opt1", "text": "The energy of the particle at position x", "is_correct": false, "feedback_incorrect": "The wave function can be used to calculate energy, but |ψ|² itself represents probability."}, {"id": "wf3opt2", "text": "The probability density of finding the particle at position x", "is_correct": true, "feedback_correct": "Correct! |ψ(x,t)|² gives the probability density for finding the particle at position x at time t.", "feedback_incorrect": "Think about what <PERSON> proposed regarding the interpretation of the wave function."}, {"id": "wf3opt3", "text": "The momentum of the particle", "is_correct": false, "feedback_incorrect": "Momentum is related to the wavelength of the wave function, not directly to |ψ|²."}], "action_button_text": "Continue"}}}, {"id": "wf-screen4-visualization", "type": "lesson_screen", "order": 4, "estimatedTimeSeconds": 60, "content": {"headline": "Visualizing Wave Functions", "body_md": "Let's visualize a simple wave function for a particle in a one-dimensional box (a particle confined between two impenetrable walls).\n\nFor this system, the wave functions are standing waves, similar to vibrations on a string fixed at both ends.\n\nEach allowed energy state has its own wave function, with:\n• n=1: The ground state (lowest energy) has no nodes (points where ψ=0)\n• n=2: The first excited state has one node\n• n=3: The second excited state has two nodes\n• And so on...", "interactive_element": {"type": "interactive", "interactiveType": "graph-plotter", "data": {"title": "Particle in a Box: Wave Functions", "xLabel": "Position", "yLabel": "ψ(x)", "xRange": [0, 1], "yRange": [-2, 2], "functions": [{"expression": "sqrt(2)*sin(1*pi*x)", "label": "n=1 (Ground State)"}, {"expression": "sqrt(2)*sin(2*pi*x)", "label": "n=2 (First Excited State)"}, {"expression": "sqrt(2)*sin(3*pi*x)", "label": "n=3 (Second Excited State)"}], "interactive": "Click on the legend to show/hide different energy states"}}}}, {"id": "wf-screen5-probability-density", "type": "lesson_screen", "order": 5, "estimatedTimeSeconds": 60, "content": {"headline": "Probability Densities", "body_md": "To find the probability density, we square the wave function (|ψ|²).\n\nFor our particle in a box:\n• In the ground state (n=1), the particle is most likely to be found in the middle of the box\n• In the first excited state (n=2), the particle is never found in the middle (there's a node there)\n• As n increases, the probability becomes more evenly distributed\n\nThis is very different from classical physics, where a particle would have a definite position!", "interactive_element": {"type": "interactive", "interactiveType": "graph-plotter", "data": {"title": "Particle in a Box: Probability Densities", "xLabel": "Position", "yLabel": "|ψ(x)|²", "xRange": [0, 1], "yRange": [0, 3], "functions": [{"expression": "2*sin(1*pi*x)^2", "label": "n=1 (Ground State)"}, {"expression": "2*sin(2*pi*x)^2", "label": "n=2 (First Excited State)"}, {"expression": "2*sin(3*pi*x)^2", "label": "n=3 (Second Excited State)"}], "interactive": "Click on the legend to show/hide different energy states"}}}}, {"id": "wf-screen6-properties", "type": "lesson_screen", "order": 6, "estimatedTimeSeconds": 50, "content": {"headline": "Key Properties of Wave Functions", "body_md": "Wave functions have several important mathematical properties:\n\n1. **Normalization**: The total probability must equal 1\n   ∫|ψ(x)|² dx = 1\n\n2. **Superposition**: Any linear combination of valid wave functions is also a valid wave function\n   ψ = c₁ψ₁ + c₂ψ₂ + ...\n\n3. **Orthogonality**: Different energy eigenfunctions are orthogonal to each other\n   ∫ψₘ*ψₙ dx = 0 (if m ≠ n)\n\n4. **Complex values**: Wave functions are generally complex-valued functions", "visual": {"type": "giphy_search", "value": "complex wave superposition"}, "interactive_element": {"type": "button", "text": "What about superposition?", "action": "next_screen"}}}, {"id": "wf-screen7-superposition", "type": "lesson_screen", "order": 7, "estimatedTimeSeconds": 60, "content": {"headline": "The Principle of Superposition", "body_md": "One of the most profound aspects of quantum mechanics is the **principle of superposition**:\n\nA quantum system can exist in multiple states simultaneously, represented by a wave function that is a combination (superposition) of different possible states.\n\nFor example, an electron can be in a superposition of \"spin up\" and \"spin down\" states:\nψ = c₁ψ₁ + c₂ψ₂\n\nWhen measured, the system \"collapses\" to one of these states with probability determined by the coefficients (|c₁|² and |c₂|²).", "interactive_element": {"type": "multiple_choice_text", "question_text": "What happens when we measure a quantum system in superposition?", "options": [{"id": "wf7opt1", "text": "We observe the superposition directly", "is_correct": false, "feedback_incorrect": "Superpositions cannot be directly observed; measurement causes collapse to a specific state."}, {"id": "wf7opt2", "text": "We get the average of all possible states", "is_correct": false, "feedback_incorrect": "Measurement doesn't give an average but a specific outcome."}, {"id": "wf7opt3", "text": "The system collapses to one of the possible states", "is_correct": true, "feedback_correct": "Correct! Measurement causes the wave function to collapse to one of the possible states, with probability determined by the wave function.", "feedback_incorrect": "Think about what the Born rule tells us about measurement outcomes."}], "action_button_text": "Continue"}}}, {"id": "wf-screen8-recap", "type": "lesson_screen", "order": 8, "estimatedTimeSeconds": 40, "content": {"headline": "Key Takeaways", "body_md": "• The **wave function** (ψ) mathematically describes quantum states\n• **|ψ|²** gives the probability density (<PERSON>'s interpretation)\n• Quantum systems can exist in **superpositions** of states\n• Measurement causes the wave function to **collapse** to a specific state\n• This probabilistic nature is fundamental to quantum mechanics, not just a limitation of measurement", "interactive_element": {"type": "button", "text": "Next Lesson: Operators and Observables", "action": "next_lesson"}}}]}, {"id": "operators-observables", "title": "Operators and Observables", "description": "Learn how physical quantities are represented.", "order": 2, "type": "interactive_lesson", "estimatedTimeMinutes": 10, "contentBlocks": [{"id": "oo-screen1-intro", "type": "lesson_screen", "order": 1, "estimatedTimeSeconds": 30, "content": {"headline": "The Mathematics of Measurement", "body_md": "In classical physics, measuring a property like position or momentum is straightforward. In quantum mechanics, it's more complex—we need special mathematical tools called **operators** to extract information from wave functions.", "visual": {"type": "giphy_search", "value": "quantum measurement"}, "hook": "These operators are the key to connecting the abstract wave function to physical quantities we can actually measure.", "interactive_element": {"type": "button", "text": "Let's learn about operators!", "action": "next_screen"}}}, {"id": "oo-screen2-what-are", "type": "lesson_screen", "order": 2, "estimatedTimeSeconds": 50, "content": {"headline": "What are Quantum Operators?", "body_md": "In quantum mechanics, an **operator** is a mathematical entity that acts on a wave function to transform it into another wave function.\n\nEach physical observable (like position, momentum, or energy) is represented by a corresponding operator.\n\nWhen we measure a physical quantity, we're essentially applying its operator to the wave function of the system.", "visual": {"type": "unsplash_search", "value": "mathematical transformation"}, "interactive_element": {"type": "button", "text": "How do operators work?", "action": "next_screen"}}}, {"id": "oo-screen3-common-operators", "type": "lesson_screen", "order": 3, "estimatedTimeSeconds": 70, "content": {"headline": "Common Quantum Operators", "body_md": "Here are some fundamental quantum operators and their corresponding observables:\n\n• **Position operator (x̂)**: Simply multiplies the wave function by x\n  x̂ψ(x) = x·ψ(x)\n\n• **Momentum operator (p̂)**: Takes the derivative of the wave function (times -iℏ)\n  p̂ψ(x) = -iℏ·(d/dx)ψ(x)\n\n• **Energy operator (Ĥ)**: The Hamiltonian operator, often involves both position and momentum\n  Ĥψ(x) = [-ℏ²/(2m)·(d²/dx²) + V(x)]ψ(x)", "interactive_element": {"type": "multiple_choice_text", "question_text": "Which of these is the correct form of the momentum operator?", "options": [{"id": "oo3opt1", "text": "p̂ψ(x) = p·ψ(x)", "is_correct": false, "feedback_incorrect": "This is similar to the position operator, but not the momentum operator."}, {"id": "oo3opt2", "text": "p̂ψ(x) = -iℏ·(d/dx)ψ(x)", "is_correct": true, "feedback_correct": "Correct! The momentum operator is -iℏ times the derivative with respect to position.", "feedback_incorrect": "The momentum operator involves the derivative of the wave function."}, {"id": "oo3opt3", "text": "p̂ψ(x) = (d²/dx²)ψ(x)", "is_correct": false, "feedback_incorrect": "The second derivative appears in the energy operator, not the momentum operator."}], "action_button_text": "Continue"}}}, {"id": "oo-screen4-eigenvalues", "type": "lesson_screen", "order": 4, "estimatedTimeSeconds": 70, "content": {"headline": "Eigenvalues and Eigenfunctions", "body_md": "When an operator acts on certain special wave functions, it simply multiplies them by a constant:\n\nÂψ = aψ\n\nIn this case:\n• ψ is called an **eigenfunction** of the operator Â\n• a is called the corresponding **eigenvalue**\n• The eigenvalue represents the value you would measure for that observable\n\nFor example, if Ĥψ = Eψ, then E is the energy you would measure for a system in state ψ.", "visual": {"type": "giphy_search", "value": "eigenvalue eigenvector"}, "interactive_element": {"type": "button", "text": "What about measurement?", "action": "next_screen"}}}, {"id": "oo-screen5-measurement", "type": "lesson_screen", "order": 5, "estimatedTimeSeconds": 60, "content": {"headline": "Measurement in Quantum Mechanics", "body_md": "When we measure an observable in quantum mechanics:\n\n1. The possible measurement outcomes are the eigenvalues of the corresponding operator\n\n2. If the system is in an eigenstate, we get the corresponding eigenvalue with 100% certainty\n\n3. If the system is in a superposition state, we get one of the possible eigenvalues with probability determined by the wave function\n\n4. After measurement, the system collapses to the eigenstate corresponding to the measured eigenvalue", "interactive_element": {"type": "interactive", "interactiveType": "<PERSON><PERSON><PERSON><PERSON>", "data": {"title": "Quantum Measurement Outcomes", "instruction": "Match each initial state with the measurement outcome:", "conditions": ["System in eigenstate ψ₁ with eigenvalue E₁", "System in eigenstate ψ₂ with eigenvalue E₂", "System in superposition state ψ = c₁ψ₁ + c₂ψ₂", "System in superposition state ψ = c₁ψ₁ + c₂ψ₂ (after measuring E₁)"], "outcomes": ["Will measure E₁ with 100% certainty", "Will measure E₂ with 100% certainty", "Will measure E₁ with probability |c₁|² or E₂ with probability |c₂|²", "Will measure E₁ with 100% certainty (system has collapsed to ψ₁)"], "correctMatches": [[0, 0], [1, 1], [2, 2], [3, 0]], "explanation": "Quantum measurement gives definite results for eigenstates, probabilistic results for superpositions, and causes the system to collapse to the eigenstate corresponding to the measured value."}}}}, {"id": "oo-screen6-uncertainty", "type": "lesson_screen", "order": 6, "estimatedTimeSeconds": 60, "content": {"headline": "Commutators and Uncertainty", "body_md": "Two operators Â and B̂ are said to **commute** if ÂB̂ = B̂Â.\n\nIf operators commute, their observables can be measured simultaneously with arbitrary precision.\n\nIf operators don't commute, their observables cannot be measured simultaneously with arbitrary precision. This leads to the famous **uncertainty principle**.\n\nFor position and momentum: [x̂,p̂] = x̂p̂ - p̂x̂ = iℏ\n\nThis non-zero commutator leads to <PERSON><PERSON><PERSON>'s uncertainty principle: ΔxΔp ≥ ℏ/2", "visual": {"type": "unsplash_search", "value": "uncertainty blur"}, "interactive_element": {"type": "multiple_choice_text", "question_text": "What does it mean if two operators don't commute?", "options": [{"id": "oo6opt1", "text": "Their observables cannot be measured at all", "is_correct": false, "feedback_incorrect": "Non-commuting observables can be measured, just not simultaneously with arbitrary precision."}, {"id": "oo6opt2", "text": "Their observables cannot be measured simultaneously with arbitrary precision", "is_correct": true, "feedback_correct": "Correct! Non-commuting operators lead to uncertainty relations between their observables.", "feedback_incorrect": "Think about what the uncertainty principle tells us about pairs of observables."}, {"id": "oo6opt3", "text": "Their eigenvalues must be the same", "is_correct": false, "feedback_incorrect": "Non-commuting operators typically have different eigenvalues and eigenfunctions."}], "action_button_text": "Continue"}}}, {"id": "oo-screen7-hermitian", "type": "lesson_screen", "order": 7, "estimatedTimeSeconds": 50, "content": {"headline": "Hermitian Operators", "body_md": "In quantum mechanics, operators representing physical observables must be **Hermitian**.\n\nA Hermitian operator has several important properties:\n\n• Its eigenvalues are always real numbers (which makes sense for physical measurements)\n\n• Its eigenfunctions form a complete orthonormal basis (any wave function can be expressed as a combination of them)\n\n• Different eigenfunctions corresponding to different eigenvalues are orthogonal to each other", "interactive_element": {"type": "button", "text": "Let's review what we've learned", "action": "next_screen"}}}, {"id": "oo-screen8-recap", "type": "lesson_screen", "order": 8, "estimatedTimeSeconds": 40, "content": {"headline": "Key Takeaways", "body_md": "• **Operators** are mathematical entities that act on wave functions\n• Each physical **observable** corresponds to a specific operator\n• **Eigenvalues** of operators represent possible measurement outcomes\n• **Eigenfunctions** are states that give definite measurement results\n• Non-commuting operators lead to **uncertainty relations**\n• Operators representing observables must be **Hermitian**", "interactive_element": {"type": "button", "text": "Next Lesson: The Schrödinger Equation", "action": "next_lesson"}}}]}, {"id": "schrodinger-equation", "title": "The Schrödinger Equation: Governing Quantum Evolution", "description": "Visualize the time evolution of quantum states.", "order": 3, "type": "interactive_lesson", "estimatedTimeMinutes": 10, "contentBlocks": [{"id": "se-screen1-intro", "type": "lesson_screen", "order": 1, "estimatedTimeSeconds": 30, "content": {"headline": "The Quantum Master Equation", "body_md": "In 1926, <PERSON> formulated an equation that would become the cornerstone of quantum mechanics. The **<PERSON><PERSON><PERSON><PERSON><PERSON> equation** describes how quantum systems evolve over time—it's the quantum equivalent of Newton's laws of motion.", "visual": {"type": "giphy_search", "value": "wave equation physics"}, "hook": "This elegant equation unlocked the quantum world and earned <PERSON><PERSON><PERSON><PERSON><PERSON> the Nobel Prize in Physics in 1933.", "interactive_element": {"type": "button", "text": "Show me the equation!", "action": "next_screen"}}}, {"id": "se-screen2-time-dependent", "type": "lesson_screen", "order": 2, "estimatedTimeSeconds": 60, "content": {"headline": "The Time-Dependent Schrödinger Equation", "body_md": "The time-dependent <PERSON><PERSON><PERSON><PERSON><PERSON> equation is:\n\niℏ ∂ψ(x,t)/∂t = Ĥψ(x,t)\n\nwhere:\n• ψ(x,t) is the wave function\n• ℏ is the reduced Planck constant (h/2π)\n• i is the imaginary unit\n• Ĥ is the Hamiltonian operator (representing the total energy)\n\nThis equation tells us how the wave function changes over time.", "visual": {"type": "unsplash_search", "value": "wave evolution"}, "interactive_element": {"type": "button", "text": "What does the Hamiltonian look like?", "action": "next_screen"}}}, {"id": "se-screen3-hamiltonian", "type": "lesson_screen", "order": 3, "estimatedTimeSeconds": 60, "content": {"headline": "The Hamiltonian Operator", "body_md": "The Hamiltonian operator represents the total energy of the system and typically has the form:\n\nĤ = T̂ + V̂\n\nwhere:\n• T̂ is the kinetic energy operator: T̂ = -ℏ²/(2m) ∇²\n• V̂ is the potential energy operator: V̂ = V(x)\n\nFor a particle in one dimension, the <PERSON><PERSON><PERSON><PERSON><PERSON> equation becomes:\n\niℏ ∂ψ(x,t)/∂t = [-ℏ²/(2m) ∂²/∂x² + V(x)]ψ(x,t)", "interactive_element": {"type": "multiple_choice_text", "question_text": "What does the Hamiltonian operator represent?", "options": [{"id": "se3opt1", "text": "The position of the particle", "is_correct": false, "feedback_incorrect": "Position is represented by the position operator, not the Hamiltonian."}, {"id": "se3opt2", "text": "The momentum of the particle", "is_correct": false, "feedback_incorrect": "Momentum is represented by the momentum operator, not the Hamiltonian."}, {"id": "se3opt3", "text": "The total energy of the system", "is_correct": true, "feedback_correct": "Correct! The Hamiltonian operator represents the total energy (kinetic plus potential) of the quantum system.", "feedback_incorrect": "Think about what quantity the Hamiltonian corresponds to in classical physics."}], "action_button_text": "Continue"}}}, {"id": "se-screen4-time-independent", "type": "lesson_screen", "order": 4, "estimatedTimeSeconds": 70, "content": {"headline": "The Time-Independent Schrödinger Equation", "body_md": "For many problems, we can separate the time and space parts of the wave function:\n\nψ(x,t) = ψ(x)e^(-iEt/ℏ)\n\nThis leads to the time-independent Schrödinger equation:\n\nĤψ(x) = Eψ(x)\n\nwhere:\n• E is the energy of the system\n• ψ(x) is the spatial part of the wave function\n\nThis is an eigenvalue equation! The solutions ψ(x) are energy eigenfunctions with eigenvalues E.", "visual": {"type": "giphy_search", "value": "stationary wave"}, "interactive_element": {"type": "button", "text": "Let's see some solutions", "action": "next_screen"}}}, {"id": "se-screen5-solutions", "type": "lesson_screen", "order": 5, "estimatedTimeSeconds": 70, "content": {"headline": "Solutions for Common Potentials", "body_md": "The <PERSON><PERSON><PERSON><PERSON><PERSON> equation can be solved exactly for several important potential energy functions:\n\n• **Free particle** (V=0): Plane waves ψ(x) = Ae^(ikx) with E = ℏ²k²/(2m)\n\n• **Particle in a box** (V=0 inside, V=∞ outside): Standing waves ψₙ(x) = √(2/L)sin(nπx/L) with Eₙ = n²π²ℏ²/(2mL²)\n\n• **Harmonic oscillator** (V=½kx²): Hermite polynomials times a Gaussian with Eₙ = (n+½)ℏω\n\n• **Hydrogen atom**: Spherical harmonics times radial functions with Eₙ = -13.6 eV/n²", "interactive_element": {"type": "interactive", "interactiveType": "graph-plotter", "data": {"title": "Quantum Harmonic Oscillator", "xLabel": "Position", "yLabel": "ψ(x) and V(x)", "xRange": [-4, 4], "yRange": [-1, 4], "functions": [{"expression": "0.5*x^2", "label": "Potential V(x) = ½x²", "color": "gray"}, {"expression": "0.5*exp(-x^2/2)", "label": "Ground State (n=0)", "color": "blue"}, {"expression": "0.5*x*exp(-x^2/2)", "label": "First Excited State (n=1)", "color": "red"}, {"expression": "0.5*(2*x^2-1)*exp(-x^2/2)", "label": "Second Excited State (n=2)", "color": "green"}], "interactive": "Click on the legend to show/hide different functions"}}}}, {"id": "se-screen6-time-evolution", "type": "lesson_screen", "order": 6, "estimatedTimeSeconds": 60, "content": {"headline": "Time Evolution of Quantum States", "body_md": "How do quantum states evolve over time?\n\n• **Energy eigenstates** evolve by acquiring a phase factor: ψ(x,t) = ψ(x)e^(-iEt/ℏ)\n  - The probability density |ψ|² remains constant (these are stationary states)\n  - Only the complex phase changes with time\n\n• **Superposition states** evolve more complexly: ψ(x,t) = Σ cₙψₙ(x)e^(-iEₙt/ℏ)\n  - Different energy components evolve at different rates\n  - The probability density can change with time (wave packet motion)\n  - This can lead to interesting phenomena like quantum beats and revivals", "visual": {"type": "giphy_search", "value": "quantum wave packet"}, "interactive_element": {"type": "button", "text": "What about measurement?", "action": "next_screen"}}}, {"id": "se-screen7-measurement-collapse", "type": "lesson_screen", "order": 7, "estimatedTimeSeconds": 50, "content": {"headline": "Measurement and Wave Function Collapse", "body_md": "The <PERSON><PERSON><PERSON><PERSON><PERSON> equation describes the smooth, deterministic evolution of quantum systems when they're not being measured.\n\nHowever, measurement causes a discontinuous change—the wave function \"collapses\" to an eigenstate of the measured observable.\n\nThis collapse is not described by the <PERSON><PERSON><PERSON><PERSON><PERSON> equation. It's an additional postulate of quantum mechanics that has led to various interpretations of quantum theory (Copenhagen, Many-Worlds, etc.).", "interactive_element": {"type": "multiple_choice_text", "question_text": "What aspect of quantum mechanics is NOT described by the <PERSON><PERSON><PERSON><PERSON><PERSON> equation?", "options": [{"id": "se7opt1", "text": "How energy eigenstates evolve over time", "is_correct": false, "feedback_incorrect": "The <PERSON><PERSON><PERSON><PERSON><PERSON> equation does describe the time evolution of energy eigenstates."}, {"id": "se7opt2", "text": "The wave function collapse during measurement", "is_correct": true, "feedback_correct": "Correct! The <PERSON><PERSON><PERSON><PERSON><PERSON> equation describes smooth evolution between measurements, but not the collapse that occurs during measurement.", "feedback_incorrect": "Think about what happens during measurement and whether the <PERSON><PERSON><PERSON><PERSON><PERSON> equation accounts for it."}, {"id": "se7opt3", "text": "The energy levels of quantum systems", "is_correct": false, "feedback_incorrect": "The time-independent <PERSON><PERSON><PERSON><PERSON><PERSON> equation is used to find the energy levels of quantum systems."}], "action_button_text": "Continue"}}}, {"id": "se-screen8-recap", "type": "lesson_screen", "order": 8, "estimatedTimeSeconds": 40, "content": {"headline": "Key Takeaways", "body_md": "• The **<PERSON><PERSON><PERSON><PERSON><PERSON> equation** governs how quantum systems evolve over time\n• The **time-dependent** form describes dynamic evolution: iℏ ∂ψ/∂t = Ĥψ\n• The **time-independent** form finds energy eigenstates: Ĥψ = Eψ\n• **Energy eigenstates** evolve by acquiring a phase factor\n• **Superposition states** show more complex evolution\n• The equation doesn't describe **measurement-induced collapse**", "interactive_element": {"type": "button", "text": "Next Lesson: Probability and Expectation Values", "action": "next_lesson"}}}]}, {"id": "probability-expectation", "title": "Probability and Expectation Values", "description": "Calculate the likelihood of measurement outcomes.", "order": 4, "type": "interactive_lesson", "estimatedTimeMinutes": 10, "contentBlocks": [{"id": "pe-screen1-intro", "type": "lesson_screen", "order": 1, "estimatedTimeSeconds": 30, "content": {"headline": "Quantum Predictions", "body_md": "Unlike classical physics, quantum mechanics doesn't predict exact measurement outcomes. Instead, it provides probabilities and average values. Understanding how to calculate these quantities is essential for connecting quantum theory to experimental results.", "visual": {"type": "giphy_search", "value": "probability dice quantum"}, "hook": "Let's explore how to extract meaningful predictions from wave functions!", "interactive_element": {"type": "button", "text": "Let's begin!", "action": "next_screen"}}}, {"id": "pe-screen2-probability-density", "type": "lesson_screen", "order": 2, "estimatedTimeSeconds": 50, "content": {"headline": "Probability Density and Normalization", "body_md": "For a particle described by wave function ψ(x,t), the probability density is:\n\nP(x,t) = |ψ(x,t)|²\n\nThe probability of finding the particle in a region [a,b] is:\n\nP(a≤x≤b) = ∫ₐᵇ |ψ(x,t)|² dx\n\nSince the particle must be somewhere, the total probability equals 1:\n\n∫₋∞^∞ |ψ(x,t)|² dx = 1\n\nThis is the **normalization condition** for wave functions.", "visual": {"type": "unsplash_search", "value": "probability distribution"}, "interactive_element": {"type": "button", "text": "What about discrete states?", "action": "next_screen"}}}, {"id": "pe-screen3-discrete-probabilities", "type": "lesson_screen", "order": 3, "estimatedTimeSeconds": 60, "content": {"headline": "Probabilities for Discrete Observables", "body_md": "For discrete observables (like energy levels or spin), we express the wave function as a superposition of eigenstates:\n\n|ψ⟩ = c₁|ψ₁⟩ + c₂|ψ₂⟩ + ... + cₙ|ψₙ⟩\n\nwhere cᵢ are complex coefficients and |ψᵢ⟩ are eigenstates.\n\nThe probability of measuring the eigenvalue corresponding to state |ψᵢ⟩ is:\n\nP(i) = |cᵢ|² = |⟨ψᵢ|ψ⟩|²\n\nThe normalization condition becomes: Σᵢ |cᵢ|² = 1", "interactive_element": {"type": "multiple_choice_text", "question_text": "If a quantum state is |ψ⟩ = (1/√3)|ψ₁⟩ + (√2/√3)|ψ₂⟩, what is the probability of measuring the system in state |ψ₂⟩?", "options": [{"id": "pe3opt1", "text": "1/3", "is_correct": false, "feedback_incorrect": "That's the probability for state |ψ₁⟩. For |ψ₂⟩, we need |c₂|² = |√2/√3|²."}, {"id": "pe3opt2", "text": "2/3", "is_correct": true, "feedback_correct": "Correct! P(2) = |c₂|² = |√2/√3|² = 2/3", "feedback_incorrect": "Calculate |c₂|² = |√2/√3|² = 2/3."}, {"id": "pe3opt3", "text": "√2/√3", "is_correct": false, "feedback_incorrect": "That's the amplitude c₂, not the probability. We need |c₂|²."}], "action_button_text": "Continue"}}}, {"id": "pe-screen4-expectation-values", "type": "lesson_screen", "order": 4, "estimatedTimeSeconds": 70, "content": {"headline": "Expectation Values", "body_md": "The **expectation value** of an observable A is the average value you would get if you measured A many times on identically prepared systems.\n\nFor a system in state |ψ⟩, the expectation value of observable A (with operator Â) is:\n\n⟨A⟩ = ⟨ψ|Â|ψ⟩\n\nIn position representation, this becomes:\n\n⟨A⟩ = ∫ ψ*(x) Â ψ(x) dx\n\nFor example, the expectation value of position is:\n\n⟨x⟩ = ∫ ψ*(x) x ψ(x) dx = ∫ x|ψ(x)|² dx", "interactive_element": {"type": "interactive", "interactiveType": "equation-solver", "data": {"title": "Calculate an Expectation Value", "instruction": "Find the expectation value of position for a particle in the ground state of a 1D box of length L.", "equation": "⟨x⟩ = ∫₀^L x|ψ(x)|² dx", "variables": {"ψ(x)": "√(2/L)sin(πx/L) (ground state wave function)", "L": "Length of the box"}, "solution": {"steps": ["Substitute the wave function: ⟨x⟩ = ∫₀^L x|(√(2/L)sin(πx/L))|² dx", "Simplify: ⟨x⟩ = ∫₀^L x(2/L)sin²(πx/L) dx", "This integral equals L/2 by symmetry (the probability density is symmetric about x=L/2)"], "final_answer": "⟨x⟩ = L/2"}}}}}, {"id": "pe-screen5-uncertainty", "type": "lesson_screen", "order": 5, "estimatedTimeSeconds": 60, "content": {"headline": "Uncertainty and Standard Deviation", "body_md": "The **uncertainty** or **standard deviation** of an observable A is a measure of how spread out the measurement results would be:\n\nΔA = √(⟨A²⟩ - ⟨A⟩²)\n\nwhere ⟨A²⟩ is the expectation value of A².\n\nFor position and momentum, the Heisenberg uncertainty principle states:\n\nΔx·Δp ≥ ℏ/2\n\nThis means the more precisely we know a particle's position, the less precisely we can know its momentum, and vice versa.", "visual": {"type": "giphy_search", "value": "uncertainty principle"}, "interactive_element": {"type": "button", "text": "Let's see some examples", "action": "next_screen"}}}, {"id": "pe-screen6-examples", "type": "lesson_screen", "order": 6, "estimatedTimeSeconds": 70, "content": {"headline": "Examples of Expectation Values", "body_md": "Let's look at some examples for a particle in the ground state of a 1D box of length L:\n\n• **Position**: ⟨x⟩ = L/2 (middle of the box)\n• **Position uncertainty**: Δx = L/√12 ≈ 0.29L\n• **Momentum**: ⟨p⟩ = 0 (equal probability of moving left or right)\n• **Momentum uncertainty**: Δp = πℏ/L\n• **Energy**: ⟨E⟩ = π²ℏ²/(2mL²) (ground state energy)\n\nNote that Δx·Δp = L/√12 · πℏ/L = πℏ/√12 ≈ 0.91ℏ > ℏ/2, satisfying the uncertainty principle.", "interactive_element": {"type": "multiple_choice_text", "question_text": "For a particle in a box, why is the expectation value of momentum zero in the ground state?", "options": [{"id": "pe6opt1", "text": "The particle isn't moving", "is_correct": false, "feedback_incorrect": "The particle is definitely moving—it has kinetic energy in the ground state."}, {"id": "pe6opt2", "text": "The particle is equally likely to be moving left or right", "is_correct": true, "feedback_correct": "Correct! The wave function is real (after normalization), so the particle has equal probability of moving in either direction, making the average momentum zero.", "feedback_incorrect": "Think about the symmetry of the wave function and what that means for the direction of motion."}, {"id": "pe6opt3", "text": "The uncertainty principle requires it", "is_correct": false, "feedback_incorrect": "The uncertainty principle constrains the product of uncertainties, not the expectation values themselves."}], "action_button_text": "Continue"}}}, {"id": "pe-screen7-time-dependence", "type": "lesson_screen", "order": 7, "estimatedTimeSeconds": 50, "content": {"headline": "Time Dependence of Expectation Values", "body_md": "How do expectation values change with time?\n\n• For a system in an energy eigenstate, expectation values of position, momentum, etc. are **constant in time**\n\n• For superposition states, expectation values can **vary with time**\n\n• The time evolution of expectation values follows **<PERSON><PERSON><PERSON>'s theorem**:\n  d⟨x⟩/dt = ⟨p⟩/m\n  d⟨p⟩/dt = -⟨∂V/∂x⟩\n\nThese equations resemble classical mechanics, showing how quantum mechanics approaches classical physics for large systems.", "visual": {"type": "unsplash_search", "value": "pendulum time"}, "interactive_element": {"type": "button", "text": "Let's review what we've learned", "action": "next_screen"}}}, {"id": "pe-screen8-recap", "type": "lesson_screen", "order": 8, "estimatedTimeSeconds": 40, "content": {"headline": "Key Takeaways", "body_md": "• The **probability density** for finding a particle at position x is |ψ(x,t)|²\n• For discrete observables, the probability of measuring eigenvalue aᵢ is |cᵢ|² = |⟨ψᵢ|ψ⟩|²\n• The **expectation value** of an observable A is ⟨A⟩ = ⟨ψ|Â|ψ⟩\n• The **uncertainty** in an observable is ΔA = √(⟨A²⟩ - ⟨A⟩²)\n• Position and momentum uncertainties are constrained by Δx·Δp ≥ ℏ/2\n• **<PERSON><PERSON><PERSON>'s theorem** connects quantum expectation values to classical mechanics", "interactive_element": {"type": "button", "text": "Next Lesson: Superposition and Linear Combinations", "action": "next_lesson"}}}]}, {"id": "superposition-linear-combinations", "title": "Superposition and Linear Combinations", "description": "Understand the combination of quantum states.", "order": 5, "type": "interactive_lesson", "estimatedTimeMinutes": 10, "contentBlocks": [{"id": "slc-screen1-intro", "type": "lesson_screen", "order": 1, "estimatedTimeSeconds": 30, "content": {"headline": "The Quantum Superposition Principle", "body_md": "One of the most profound aspects of quantum mechanics is the principle of superposition—the idea that quantum systems can exist in multiple states simultaneously.", "visual": {"type": "giphy_search", "value": "quantum superposition"}, "hook": "This principle leads to many of the counterintuitive features of quantum mechanics and is essential for quantum computing.", "interactive_element": {"type": "button", "text": "Let's explore superposition!", "action": "next_screen"}}}, {"id": "slc-screen2-principle", "type": "lesson_screen", "order": 2, "estimatedTimeSeconds": 50, "content": {"headline": "The Superposition Principle", "body_md": "The superposition principle states that if a quantum system can be in states |ψ₁⟩ and |ψ₂⟩, then it can also be in any linear combination of these states:\n\n|ψ⟩ = c₁|ψ₁⟩ + c₂|ψ₂⟩\n\nwhere c₁ and c₂ are complex numbers called probability amplitudes.\n\nThis is fundamentally different from classical physics, where a system must be in one definite state at any given time.", "visual": {"type": "unsplash_search", "value": "multiple paths choices"}, "interactive_element": {"type": "button", "text": "What does this mean physically?", "action": "next_screen"}}}, {"id": "slc-screen3-physical-meaning", "type": "lesson_screen", "order": 3, "estimatedTimeSeconds": 60, "content": {"headline": "Physical Meaning of Superposition", "body_md": "A quantum system in a superposition state doesn't have definite values for all observables.\n\nFor example, an electron in a superposition of spin-up and spin-down states:\n\n|ψ⟩ = (1/√2)|↑⟩ + (1/√2)|↓⟩\n\ndoesn't have a definite spin direction until measured.\n\nWhen measured, the system will be found in state |↑⟩ with probability |c₁|² = 1/2 or in state |↓⟩ with probability |c₂|² = 1/2.", "interactive_element": {"type": "multiple_choice_text", "question_text": "What happens when we measure a quantum system in a superposition state?", "options": [{"id": "slc3opt1", "text": "We observe the superposition directly", "is_correct": false, "feedback_incorrect": "Superpositions cannot be directly observed; measurement causes collapse to one of the component states."}, {"id": "slc3opt2", "text": "We get the average of all possible states", "is_correct": false, "feedback_incorrect": "Measurement doesn't give an average but a specific outcome."}, {"id": "slc3opt3", "text": "The system collapses to one of the component states", "is_correct": true, "feedback_correct": "Correct! Measurement causes the wave function to collapse to one of the component states, with probability determined by the squared magnitude of the corresponding coefficient.", "feedback_incorrect": "Think about what happens to a superposition state during measurement."}], "action_button_text": "Continue"}}}, {"id": "slc-screen4-basis-states", "type": "lesson_screen", "order": 4, "estimatedTimeSeconds": 70, "content": {"headline": "Basis States and Expansions", "body_md": "Any quantum state can be expressed as a linear combination of basis states.\n\nA set of basis states {|ϕᵢ⟩} must be:\n• **Complete**: Any state can be expressed as a linear combination of basis states\n• **Orthonormal**: ⟨ϕᵢ|ϕⱼ⟩ = δᵢⱼ (where δᵢⱼ is the <PERSON>ronecker delta)\n\nCommon basis sets include:\n• Position basis: {|x⟩}\n• Momentum basis: {|p⟩}\n• Energy basis: {|Eₙ⟩}\n• Spin basis: {|↑⟩, |↓⟩}", "visual": {"type": "giphy_search", "value": "basis vectors"}, "interactive_element": {"type": "button", "text": "How do we change basis?", "action": "next_screen"}}}, {"id": "slc-screen5-changing-basis", "type": "lesson_screen", "order": 5, "estimatedTimeSeconds": 60, "content": {"headline": "Changing Basis", "body_md": "We can express a state |ψ⟩ in different bases:\n\n|ψ⟩ = Σᵢ cᵢ|ϕᵢ⟩\n\nwhere cᵢ = ⟨ϕᵢ|ψ⟩ are the expansion coefficients.\n\nChanging from one basis to another involves a transformation. For example, the position and momentum bases are related by a Fourier transform:\n\nψ(x) = (1/√(2πℏ)) ∫ φ(p) e^(ipx/ℏ) dp\n\nφ(p) = (1/√(2πℏ)) ∫ ψ(x) e^(-ipx/ℏ) dx", "interactive_element": {"type": "interactive", "interactiveType": "<PERSON><PERSON><PERSON><PERSON>", "data": {"title": "Quantum Basis States", "instruction": "Match each quantum state with its appropriate basis:", "conditions": ["ψ(x) = δ(x-x₀) (Dirac delta function)", "ψ(x) = (1/√(2πℏ))e^(ipx/ℏ)", "ψₙ(x) = √(2/L)sin(nπx/L)", "ψ = (1/√2)|↑⟩ + (1/√2)|↓⟩"], "outcomes": ["Position basis eigenstate", "Momentum basis eigenstate", "Energy eigenstate (particle in a box)", "Spin basis (superposition)"], "correctMatches": [[0, 0], [1, 1], [2, 2], [3, 3]], "explanation": "Different physical situations call for different basis choices. Position eigenstates are localized, momentum eigenstates are plane waves, energy eigenstates depend on the potential, and spin states describe intrinsic angular momentum."}}}}, {"id": "slc-screen6-interference", "type": "lesson_screen", "order": 6, "estimatedTimeSeconds": 60, "content": {"headline": "Quantum Interference", "body_md": "Superposition leads to interference effects in quantum mechanics.\n\nFor a superposition state |ψ⟩ = c₁|ψ₁⟩ + c₂|ψ₂⟩, the probability density is:\n\n|ψ|² = |c₁|²|ψ₁|² + |c₂|²|ψ₂|² + 2Re(c₁*c₂ψ₁*ψ₂)\n\nThe third term represents **quantum interference**:\n• It can be positive (constructive interference) or negative (destructive interference)\n• It has no classical analog\n• It's responsible for phenomena like the double-slit experiment", "visual": {"type": "giphy_search", "value": "wave interference"}, "interactive_element": {"type": "multiple_choice_text", "question_text": "What causes the interference pattern in the double-slit experiment with single electrons?", "options": [{"id": "slc6opt1", "text": "Electrons bouncing off each other", "is_correct": false, "feedback_incorrect": "The interference occurs even when electrons are fired one at a time, so they can't be interacting with each other."}, {"id": "slc6opt2", "text": "Quantum interference from the electron's wave function", "is_correct": true, "feedback_correct": "Correct! Each electron's wave function passes through both slits and interferes with itself, creating the pattern over many measurements.", "feedback_incorrect": "Think about the wave nature of electrons and how a single electron can exhibit interference."}, {"id": "slc6opt3", "text": "Classical wave behavior of electron beams", "is_correct": false, "feedback_incorrect": "The interference occurs even with individual electrons, not just with beams, so it's a quantum effect."}], "action_button_text": "Continue"}}}, {"id": "slc-screen7-applications", "type": "lesson_screen", "order": 7, "estimatedTimeSeconds": 50, "content": {"headline": "Applications of Superposition", "body_md": "Quantum superposition has many important applications:\n\n• **Quantum Computing**: Qubits can exist in superpositions of 0 and 1, allowing quantum computers to process multiple possibilities simultaneously\n\n• **Quantum Cryptography**: Secure communication protocols use superposition states that are disturbed by eavesdropping\n\n• **Quantum Metrology**: Superposition states can be used to make extremely precise measurements\n\n• **Quantum Tunneling**: Particles can tunnel through barriers due to the wave-like nature of superposition states", "visual": {"type": "unsplash_search", "value": "quantum computer"}, "interactive_element": {"type": "button", "text": "Let's review what we've learned", "action": "next_screen"}}}, {"id": "slc-screen8-recap", "type": "lesson_screen", "order": 8, "estimatedTimeSeconds": 40, "content": {"headline": "Key Takeaways", "body_md": "• **Superposition** allows quantum systems to exist in multiple states simultaneously\n• A superposition state is a **linear combination** of basis states\n• Measurement causes the system to **collapse** to one of the component states\n• The probability of each outcome is given by the **squared magnitude** of the corresponding coefficient\n• Superposition leads to **quantum interference** effects\n• This principle enables many **quantum technologies** like quantum computing", "interactive_element": {"type": "button", "text": "Take the Module Test", "action": "next_lesson"}}}]}, {"id": "quantum-mathematician-test", "title": "The Quantum Mathematician", "description": "Understand the basic mathematical framework of quantum mechanics and interpret wave functions.", "order": 6, "type": "module_test_interactive", "estimatedTimeMinutes": 10, "contentBlocks": [{"id": "qmt-intro", "type": "question_screen", "order": 1, "estimatedTimeSeconds": 30, "content": {"headline": "Test Your Quantum Math Skills", "body_md": "Congratulations on completing the second module of Quantum Mechanics! Let's see how well you understand the mathematical framework that describes quantum systems.", "visual": {"type": "giphy_search", "value": "quantum mathematics"}, "interactive_element": {"type": "button", "text": "Begin the Test", "action": "next_screen"}}}, {"id": "qmt-q1", "type": "question_screen", "order": 2, "estimatedTimeSeconds": 60, "content": {"headline": "Question 1: Wave Functions", "body_md": "According to <PERSON>'s interpretation, what does |ψ(x,t)|² represent in quantum mechanics?", "interactive_element": {"type": "multiple_choice_text", "question_text": "Select the best answer:", "options": [{"id": "qmt1opt1", "text": "The energy of the particle at position x", "is_correct": false, "feedback_incorrect": "The wave function can be used to calculate energy, but |ψ|² itself doesn't directly represent energy."}, {"id": "qmt1opt2", "text": "The probability density of finding the particle at position x", "is_correct": true, "feedback_correct": "Correct! |ψ(x,t)|² gives the probability density for finding the particle at position x at time t.", "feedback_incorrect": "Think about <PERSON>'s probabilistic interpretation of the wave function."}, {"id": "qmt1opt3", "text": "The momentum of the particle at position x", "is_correct": false, "feedback_incorrect": "The wave function can be used to calculate momentum, but |ψ|² itself doesn't directly represent momentum."}, {"id": "qmt1opt4", "text": "The amplitude of the particle's wave at position x", "is_correct": false, "feedback_incorrect": "ψ itself is the amplitude, while |ψ|² has a probabilistic interpretation."}], "action_button_text": "Next Question"}}}, {"id": "qmt-q2", "type": "question_screen", "order": 3, "estimatedTimeSeconds": 60, "content": {"headline": "Question 2: Quantum Operators", "body_md": "Which of the following is the correct form of the momentum operator in position representation?", "interactive_element": {"type": "multiple_choice_text", "question_text": "Select the correct operator:", "options": [{"id": "qmt2opt1", "text": "p̂ = x", "is_correct": false, "feedback_incorrect": "This is similar to the position operator, not the momentum operator."}, {"id": "qmt2opt2", "text": "p̂ = -iℏ(d/dx)", "is_correct": true, "feedback_correct": "Correct! The momentum operator in position representation is -iℏ times the derivative with respect to position.", "feedback_incorrect": "The momentum operator involves the derivative of the wave function."}, {"id": "qmt2opt3", "text": "p̂ = -ℏ²(d²/dx²)", "is_correct": false, "feedback_incorrect": "This is part of the kinetic energy operator, not the momentum operator."}, {"id": "qmt2opt4", "text": "p̂ = mv", "is_correct": false, "feedback_incorrect": "This is the classical definition of momentum, not the quantum operator."}], "action_button_text": "Next Question"}}}, {"id": "qmt-q3", "type": "question_screen", "order": 4, "estimatedTimeSeconds": 70, "content": {"headline": "Question 3: The Schrödinger Equation", "body_md": "Which of the following is the time-independent <PERSON><PERSON><PERSON><PERSON><PERSON> equation?", "interactive_element": {"type": "multiple_choice_text", "question_text": "Select the correct equation:", "options": [{"id": "qmt3opt1", "text": "iℏ(∂ψ/∂t) = Ĥψ", "is_correct": false, "feedback_incorrect": "This is the time-dependent <PERSON><PERSON><PERSON><PERSON><PERSON> equation."}, {"id": "qmt3opt2", "text": "Ĥψ = Eψ", "is_correct": true, "feedback_correct": "Correct! The time-independent <PERSON><PERSON><PERSON><PERSON><PERSON> equation is an eigenvalue equation where the Hamiltonian operator acts on the wave function to give the energy times the wave function.", "feedback_incorrect": "The time-independent equation is an eigenvalue equation for energy."}, {"id": "qmt3opt3", "text": "∇²ψ + (2m/ℏ²)Eψ = 0", "is_correct": false, "feedback_incorrect": "This is a rearranged form of the time-independent equation for a free particle, but not the general form."}, {"id": "qmt3opt4", "text": "p̂ψ = pψ", "is_correct": false, "feedback_incorrect": "This is an eigenvalue equation for momentum, not the <PERSON><PERSON><PERSON><PERSON><PERSON> equation."}], "action_button_text": "Next Question"}}}, {"id": "qmt-q4", "type": "question_screen", "order": 5, "estimatedTimeSeconds": 70, "content": {"headline": "Question 4: Expectation Values", "body_md": "If a quantum state is |ψ⟩ = (3/5)|ψ₁⟩ + (4/5)|ψ₂⟩, where |ψ₁⟩ and |ψ₂⟩ are energy eigenstates with energies E₁ = 2 eV and E₂ = 5 eV, what is the expectation value of energy?", "interactive_element": {"type": "multiple_choice_text", "question_text": "Calculate ⟨E⟩:", "options": [{"id": "qmt4opt1", "text": "3.5 eV", "is_correct": true, "feedback_correct": "Correct! ⟨E⟩ = |c₁|²E₁ + |c₂|²E₂ = (3/5)²(2 eV) + (4/5)²(5 eV) = (9/25)(2 eV) + (16/25)(5 eV) = 18/25 eV + 80/25 eV = 98/25 eV = 3.92 eV", "feedback_incorrect": "Calculate ⟨E⟩ = |c₁|²E₁ + |c₂|²E₂ = (3/5)²(2 eV) + (4/5)²(5 eV)."}, {"id": "qmt4opt2", "text": "3.5 eV", "is_correct": false, "feedback_incorrect": "This would be the result if we used c₁ and c₂ directly instead of their squared magnitudes."}, {"id": "qmt4opt3", "text": "2.5 eV", "is_correct": false, "feedback_incorrect": "This is not the correct calculation of the expectation value."}, {"id": "qmt4opt4", "text": "3.92 eV", "is_correct": true, "feedback_correct": "Correct! ⟨E⟩ = |c₁|²E₁ + |c₂|²E₂ = (3/5)²(2 eV) + (4/5)²(5 eV) = (9/25)(2 eV) + (16/25)(5 eV) = 18/25 eV + 80/25 eV = 98/25 eV = 3.92 eV", "feedback_incorrect": "Calculate ⟨E⟩ = |c₁|²E₁ + |c₂|²E₂ = (3/5)²(2 eV) + (4/5)²(5 eV)."}], "action_button_text": "Next Question"}}}, {"id": "qmt-q5", "type": "question_screen", "order": 6, "estimatedTimeSeconds": 70, "content": {"headline": "Question 5: Uncertainty Principle", "body_md": "A particle is confined to a one-dimensional box of length L = 1 nm. What is the minimum uncertainty in its momentum according to the Heisenberg uncertainty principle?", "interactive_element": {"type": "multiple_choice_text", "question_text": "Calculate the minimum Δp:", "options": [{"id": "qmt5opt1", "text": "ℏ/2L", "is_correct": false, "feedback_incorrect": "This doesn't account for the uncertainty in position correctly."}, {"id": "qmt5opt2", "text": "ℏ/L", "is_correct": false, "feedback_incorrect": "This is not the correct application of the uncertainty principle."}, {"id": "qmt5opt3", "text": "ℏ/(2L)", "is_correct": false, "feedback_incorrect": "This assumes the position uncertainty is exactly L, which is not correct."}, {"id": "qmt5opt4", "text": "ℏ/(2ΔL)", "is_correct": true, "feedback_correct": "Correct! From the uncertainty principle, Δx·Δp ≥ ℏ/2. For a particle in a box, the position uncertainty Δx is at most L, so Δp ≥ ℏ/(2Δx) ≥ ℏ/(2L) = 3.3 × 10⁻²⁵ kg·m/s.", "feedback_incorrect": "Apply the uncertainty principle: Δx·Δp ≥ ℏ/2, and consider the maximum possible position uncertainty in the box."}], "action_button_text": "Final Question"}}}, {"id": "qmt-q6", "type": "question_screen", "order": 7, "estimatedTimeSeconds": 70, "content": {"headline": "Question 6: Quantum Concepts", "body_md": "Match each quantum concept with its correct description.", "interactive_element": {"type": "interactive", "interactiveType": "<PERSON><PERSON><PERSON><PERSON>", "data": {"title": "Quantum Mathematics Concepts", "instruction": "Match each concept with its correct description:", "conditions": ["Wave function", "Operator", "Eigenvalue", "Expectation value", "Superposition"], "outcomes": ["Mathematical function that contains all information about a quantum system", "Mathematical entity that acts on wave functions and corresponds to physical observables", "Possible measurement outcome for a quantum observable", "Average value obtained from many measurements on identical systems", "Quantum state that is a linear combination of other states"], "correctMatches": [[0, 0], [1, 1], [2, 2], [3, 3], [4, 4]], "explanation": "These fundamental concepts form the mathematical framework of quantum mechanics, allowing us to describe and predict the behavior of quantum systems."}}}}, {"id": "qmt-results", "type": "lesson_screen", "order": 8, "estimatedTimeSeconds": 40, "content": {"headline": "Module Complete!", "body_md": "Congratulations! You've completed the second module of Quantum Mechanics: The Mathematical Framework.\n\nYou now understand the key mathematical tools used in quantum mechanics, including:\n\n• Wave functions and their probabilistic interpretation\n• Quantum operators and observables\n• The Schrödinger equation and its solutions\n• Probability and expectation values\n• Superposition and linear combinations\n\nReady to explore the strange and counterintuitive phenomena predicted by quantum mechanics?", "visual": {"type": "giphy_search", "value": "quantum mathematics celebration"}, "interactive_element": {"type": "button", "text": "Continue to Module 3", "action": "module_complete"}}}]}]}