import 'package:flutter/material.dart';
import 'dart:math' as math;

/// A widget that demonstrates equation solving using a balance scale analogy
class InteractiveBalanceScaleAnalogyWidget extends StatefulWidget {
  /// The data for this widget
  final Map<String, dynamic> data;

  /// Optional callback when the widget state changes
  final Function(bool isCompleted)? onStateChanged;

  const InteractiveBalanceScaleAnalogyWidget({
    Key? key,
    required this.data,
    this.onStateChanged,
  }) : super(key: key);

  /// Factory constructor to create the widget from JSON data
  factory InteractiveBalanceScaleAnalogyWidget.fromData(
    Map<String, dynamic> data,
  ) {
    return InteractiveBalanceScaleAnalogyWidget(data: data);
  }

  @override
  State<InteractiveBalanceScaleAnalogyWidget> createState() =>
      _InteractiveBalanceScaleAnalogyWidgetState();
}

class _InteractiveBalanceScaleAnalogyWidgetState
    extends State<InteractiveBalanceScaleAnalogyWidget>
    with SingleTickerProviderStateMixin {
  // Equation data
  late String _equation;
  late List<EquationStep> _steps;
  late String _variableName;
  late String _solution;

  // UI state
  int _currentStepIndex = 0;
  bool _isCompleted = false;
  bool _isBalanced = true;

  // Animation controller for the balance scale
  late AnimationController _animationController;
  late Animation<double> _angleAnimation;

  // Scale state
  late List<ScaleItem> _leftItems;
  late List<ScaleItem> _rightItems;

  // UI customization
  late Color _primaryColor;
  late Color _variableColor;
  late Color _constantColor;
  late Color _operationColor;

  @override
  void initState() {
    super.initState();

    // Initialize animation controller
    _animationController = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 500),
    );

    _angleAnimation = Tween<double>(begin: 0.0, end: 0.0).animate(
      CurvedAnimation(parent: _animationController, curve: Curves.easeInOut),
    );

    // Initialize from data
    _equation = widget.data['equation'] ?? 'x + 3 = 5';
    _variableName = widget.data['variable_name'] ?? 'x';
    _solution = widget.data['solution'] ?? '2';

    // Parse steps
    _steps = [];
    final stepsData = widget.data['steps'] as List<dynamic>? ?? [];
    for (final step in stepsData) {
      if (step is Map<String, dynamic>) {
        _steps.add(
          EquationStep(
            equation: step['equation'] ?? '',
            operation: step['operation'] ?? '',
            explanation: step['explanation'] ?? '',
          ),
        );
      }
    }

    // If no steps provided, create default steps
    if (_steps.isEmpty) {
      _steps = [
        EquationStep(
          equation: 'x + 3 = 5',
          operation: 'Initial equation',
          explanation: 'We start with the given equation.',
        ),
        EquationStep(
          equation: 'x + 3 - 3 = 5 - 3',
          operation: 'Subtract 3 from both sides',
          explanation:
              'To isolate the variable, we subtract 3 from both sides of the equation.',
        ),
        EquationStep(
          equation: 'x = 2',
          operation: 'Simplify',
          explanation: 'After simplifying, we get x = 2.',
        ),
      ];
    }

    // Initialize scale items based on first step
    _initializeScaleItems();

    // Initialize colors
    _primaryColor = _parseColor(widget.data['primary_color'], Colors.blue);
    _variableColor = _parseColor(widget.data['variable_color'], Colors.purple);
    _constantColor = _parseColor(widget.data['constant_color'], Colors.orange);
    _operationColor = _parseColor(widget.data['operation_color'], Colors.green);
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  // Helper method to parse color from string
  Color _parseColor(dynamic colorValue, Color defaultColor) {
    if (colorValue == null) return defaultColor;
    if (colorValue is String) {
      try {
        return Color(int.parse(colorValue.replaceAll('#', '0xFF')));
      } catch (e) {
        return defaultColor;
      }
    }
    return defaultColor;
  }

  // Initialize scale items based on current equation
  void _initializeScaleItems() {
    final equation = _steps[_currentStepIndex].equation;
    final parts = equation.split('=');

    if (parts.length != 2) {
      _leftItems = [];
      _rightItems = [];
      return;
    }

    // Parse left side
    _leftItems = _parseExpressionToItems(parts[0].trim());

    // Parse right side
    _rightItems = _parseExpressionToItems(parts[1].trim());

    // Update balance state
    _updateBalanceState();
  }

  // Parse an expression into scale items
  List<ScaleItem> _parseExpressionToItems(String expression) {
    final items = <ScaleItem>[];

    // Simple parsing for demonstration
    // In a real implementation, you would use a proper expression parser
    final terms = expression.split(RegExp(r'(?=[-+])|(?<=[-+])'));

    for (int i = 0; i < terms.length; i += 2) {
      final term = terms[i].trim();

      if (term.isEmpty) continue;

      if (term.contains(_variableName)) {
        // Variable term
        final coefficient = term.replaceAll(_variableName, '').trim();
        final value =
            coefficient.isEmpty
                ? 1
                : coefficient == '-'
                ? -1
                : int.tryParse(coefficient) ?? 1;

        items.add(
          ScaleItem(label: term, value: value.toDouble(), isVariable: true),
        );
      } else {
        // Constant term
        final value = int.tryParse(term) ?? 0;
        items.add(
          ScaleItem(label: term, value: value.toDouble(), isVariable: false),
        );
      }
    }

    return items;
  }

  // Update the balance state and animation
  void _updateBalanceState() {
    // Calculate total weight on each side
    final leftWeight = _leftItems.fold<double>(
      0,
      (sum, item) => sum + item.value,
    );
    final rightWeight = _rightItems.fold<double>(
      0,
      (sum, item) => sum + item.value,
    );

    // Determine if balanced
    _isBalanced = (leftWeight - rightWeight).abs() < 0.01;

    // Update animation
    if (_isBalanced) {
      _angleAnimation = Tween<double>(
        begin: _angleAnimation.value,
        end: 0.0,
      ).animate(
        CurvedAnimation(parent: _animationController, curve: Curves.easeInOut),
      );
    } else {
      final angle = math.min(
        0.2,
        math.max(-0.2, (rightWeight - leftWeight) * 0.05),
      );
      _angleAnimation = Tween<double>(
        begin: _angleAnimation.value,
        end: angle,
      ).animate(
        CurvedAnimation(parent: _animationController, curve: Curves.easeInOut),
      );
    }

    _animationController.forward(from: 0.0);
  }

  // Move to the next step
  void _nextStep() {
    if (_currentStepIndex < _steps.length - 1) {
      setState(() {
        _currentStepIndex++;
        _initializeScaleItems();
      });
    } else {
      setState(() {
        _isCompleted = true;
      });

      // Notify parent about completion
      widget.onStateChanged?.call(true);
    }
  }

  // Move to the previous step
  void _previousStep() {
    if (_currentStepIndex > 0) {
      setState(() {
        _currentStepIndex--;
        _initializeScaleItems();
      });
    }
  }

  // Reset the widget
  void _reset() {
    setState(() {
      _currentStepIndex = 0;
      _isCompleted = false;
      _initializeScaleItems();
    });

    // Notify parent about reset
    widget.onStateChanged?.call(false);
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withOpacity(0.2),
            spreadRadius: 1,
            blurRadius: 3,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Title
          Text(
            'Balance Scale Equation Solver',
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
              color: _primaryColor,
            ),
          ),

          const SizedBox(height: 12),

          // Current equation
          Container(
            padding: const EdgeInsets.all(12),
            decoration: BoxDecoration(
              color: Colors.grey.shade50,
              borderRadius: BorderRadius.circular(8),
              border: Border.all(color: Colors.grey.shade300),
            ),
            child: Text(
              _steps[_currentStepIndex].equation,
              style: const TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
            ),
          ),

          const SizedBox(height: 16),

          // Balance scale visualization
          AnimatedBuilder(
            animation: _animationController,
            builder: (context, child) {
              return _buildBalanceScale();
            },
          ),

          const SizedBox(height: 16),

          // Step explanation
          Container(
            padding: const EdgeInsets.all(12),
            decoration: BoxDecoration(
              color: _primaryColor.withOpacity(0.1),
              borderRadius: BorderRadius.circular(8),
              border: Border.all(color: _primaryColor.withOpacity(0.5)),
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'Step ${_currentStepIndex + 1}: ${_steps[_currentStepIndex].operation}',
                  style: TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                    color: _operationColor,
                  ),
                ),
                const SizedBox(height: 4),
                Text(
                  _steps[_currentStepIndex].explanation,
                  style: const TextStyle(fontSize: 14),
                ),
              ],
            ),
          ),

          const SizedBox(height: 16),

          // Navigation buttons
          Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              // Previous button
              ElevatedButton(
                onPressed: _currentStepIndex > 0 ? _previousStep : null,
                style: ElevatedButton.styleFrom(
                  backgroundColor: Colors.grey.shade200,
                  foregroundColor: Colors.black87,
                  padding: const EdgeInsets.symmetric(
                    horizontal: 16,
                    vertical: 12,
                  ),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(8),
                  ),
                ),
                child: const Text('Previous'),
              ),

              const SizedBox(width: 16),

              // Next/Reset button
              ElevatedButton(
                onPressed: _isCompleted ? _reset : _nextStep,
                style: ElevatedButton.styleFrom(
                  backgroundColor: _primaryColor,
                  foregroundColor: Colors.white,
                  padding: const EdgeInsets.symmetric(
                    horizontal: 24,
                    vertical: 12,
                  ),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(8),
                  ),
                ),
                child: Text(_isCompleted ? 'Reset' : 'Next Step'),
              ),
            ],
          ),

          // Widget name tag
          if (widget.data['showNameTag'] ?? true)
            Padding(
              padding: const EdgeInsets.only(top: 8),
              child: Text(
                'InteractiveBalanceScaleAnalogyWidget',
                style: TextStyle(
                  color: Colors.grey[600],
                  fontSize: 12,
                  fontStyle: FontStyle.italic,
                ),
              ),
            ),
        ],
      ),
    );
  }

  // Build the balance scale visualization
  Widget _buildBalanceScale() {
    return SizedBox(
      height: 200,
      child: Stack(
        alignment: Alignment.center,
        children: [
          // Base
          Positioned(
            bottom: 0,
            child: Container(
              width: 120,
              height: 20,
              decoration: BoxDecoration(
                color: Colors.brown.shade800,
                borderRadius: BorderRadius.circular(4),
              ),
            ),
          ),

          // Stand
          Positioned(
            bottom: 20,
            child: Container(
              width: 20,
              height: 100,
              color: Colors.brown.shade700,
            ),
          ),

          // Beam
          Positioned(
            bottom: 120,
            child: Transform.rotate(
              angle: _angleAnimation.value,
              alignment: Alignment.center,
              child: Container(
                width: 240,
                height: 10,
                decoration: BoxDecoration(
                  color: Colors.brown.shade600,
                  borderRadius: BorderRadius.circular(2),
                ),
              ),
            ),
          ),

          // Left pan
          Positioned(
            bottom: 120 - 60 * math.sin(_angleAnimation.value),
            left: 60,
            child: Transform.rotate(
              angle: _angleAnimation.value,
              child: Container(
                width: 80,
                height: 10,
                decoration: BoxDecoration(
                  color: Colors.grey.shade400,
                  borderRadius: BorderRadius.circular(2),
                ),
                child: Stack(
                  clipBehavior: Clip.none,
                  children: [
                    // Left pan items
                    Positioned(
                      top: -50,
                      left: 0,
                      right: 0,
                      child: _buildPanItems(_leftItems, true),
                    ),
                  ],
                ),
              ),
            ),
          ),

          // Right pan
          Positioned(
            bottom: 120 + 60 * math.sin(_angleAnimation.value),
            right: 60,
            child: Transform.rotate(
              angle: _angleAnimation.value,
              child: Container(
                width: 80,
                height: 10,
                decoration: BoxDecoration(
                  color: Colors.grey.shade400,
                  borderRadius: BorderRadius.circular(2),
                ),
                child: Stack(
                  clipBehavior: Clip.none,
                  children: [
                    // Right pan items
                    Positioned(
                      top: -50,
                      left: 0,
                      right: 0,
                      child: _buildPanItems(_rightItems, false),
                    ),
                  ],
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  // Build the items on a pan
  Widget _buildPanItems(List<ScaleItem> items, bool isLeft) {
    return Column(
      mainAxisSize: MainAxisSize.min,
      children:
          items.map((item) {
            return Container(
              margin: const EdgeInsets.only(bottom: 4),
              padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
              decoration: BoxDecoration(
                color: item.isVariable ? _variableColor : _constantColor,
                borderRadius: BorderRadius.circular(4),
              ),
              child: Text(
                item.label,
                style: const TextStyle(
                  color: Colors.white,
                  fontWeight: FontWeight.bold,
                ),
              ),
            );
          }).toList(),
    );
  }
}

/// Represents a step in the equation solving process
class EquationStep {
  final String equation;
  final String operation;
  final String explanation;

  EquationStep({
    required this.equation,
    required this.operation,
    required this.explanation,
  });
}

/// Represents an item on the balance scale
class ScaleItem {
  final String label;
  final double value;
  final bool isVariable;

  ScaleItem({
    required this.label,
    required this.value,
    required this.isVariable,
  });
}
