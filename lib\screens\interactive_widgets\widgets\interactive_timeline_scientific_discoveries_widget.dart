import 'package:flutter/material.dart';
import 'dart:math' as math;

/// A widget that displays an interactive timeline of scientific discoveries
class InteractiveTimelineScientificDiscoveriesWidget extends StatefulWidget {
  /// The data for this widget
  final Map<String, dynamic> data;

  /// Optional callback when the widget state changes
  final Function(bool isCompleted)? onStateChanged;

  const InteractiveTimelineScientificDiscoveriesWidget({
    super.key,
    required this.data,
    this.onStateChanged,
  });

  /// Factory constructor to create the widget from JSON data
  factory InteractiveTimelineScientificDiscoveriesWidget.fromData(Map<String, dynamic> data) {
    return InteractiveTimelineScientificDiscoveriesWidget(
      data: data,
    );
  }

  @override
  State<InteractiveTimelineScientificDiscoveriesWidget> createState() => _InteractiveTimelineScientificDiscoveriesWidgetState();
}

class _InteractiveTimelineScientificDiscoveriesWidgetState extends State<InteractiveTimelineScientificDiscoveriesWidget> {
  // Timeline data
  late List<TimelinePeriod> _periods;
  late List<ScientificDiscovery> _discoveries;
  late int _currentPeriodIndex;
  
  // UI state
  late bool _isCompleted;
  late bool _showQuiz;
  late bool _quizCompleted;
  late int _currentQuestionIndex;
  late List<bool> _questionAnswers;
  late ScrollController _scrollController;
  
  // Colors
  late Color _primaryColor;
  late Color _secondaryColor;
  late Color _accentColor;
  late Color _backgroundColor;
  late Color _textColor;

  @override
  void initState() {
    super.initState();
    _initializeWidget();
  }

  @override
  void dispose() {
    _scrollController.dispose();
    super.dispose();
  }

  void _initializeWidget() {
    // Initialize colors
    _primaryColor = _parseColor(widget.data['primaryColor'] ?? '#2196F3');
    _secondaryColor = _parseColor(widget.data['secondaryColor'] ?? '#FF9800');
    _accentColor = _parseColor(widget.data['accentColor'] ?? '#4CAF50');
    _backgroundColor = _parseColor(widget.data['backgroundColor'] ?? '#FFFFFF');
    _textColor = _parseColor(widget.data['textColor'] ?? '#212121');

    // Initialize timeline data
    final List<dynamic> periodsData = widget.data['periods'] ?? [];
    _periods = periodsData.map((periodData) => TimelinePeriod.fromJson(periodData)).toList();
    
    final List<dynamic> discoveriesData = widget.data['discoveries'] ?? [];
    _discoveries = discoveriesData.map((discoveryData) => ScientificDiscovery.fromJson(discoveryData)).toList();
    
    // Sort discoveries by year
    _discoveries.sort((a, b) => a.year.compareTo(b.year));
    
    // Initialize UI state
    _currentPeriodIndex = 0;
    _isCompleted = false;
    _showQuiz = false;
    _quizCompleted = false;
    _currentQuestionIndex = 0;
    _questionAnswers = [];
    _scrollController = ScrollController();
  }

  Color _parseColor(String colorString) {
    if (colorString.startsWith('#')) {
      return Color(int.parse('FF${colorString.substring(1)}', radix: 16));
    }
    return Colors.blue;
  }

  void _selectPeriod(int index) {
    setState(() {
      _currentPeriodIndex = index;
    });
  }

  void _startQuiz() {
    setState(() {
      _showQuiz = true;
      _quizCompleted = false;
      _currentQuestionIndex = 0;
      _questionAnswers = List.generate(
        _periods[_currentPeriodIndex].quizQuestions.length, 
        (_) => false
      );
    });
  }

  void _answerQuestion(bool isCorrect) {
    setState(() {
      _questionAnswers[_currentQuestionIndex] = isCorrect;
      
      if (_currentQuestionIndex < _periods[_currentPeriodIndex].quizQuestions.length - 1) {
        _currentQuestionIndex++;
      } else {
        _quizCompleted = true;
        
        // Check if all periods have been viewed and quizzed
        bool allPeriodsCompleted = true;
        for (int i = 0; i < _periods.length; i++) {
          if (i != _currentPeriodIndex && !_questionAnswers.contains(true)) {
            allPeriodsCompleted = false;
            break;
          }
        }
        
        if (allPeriodsCompleted && !_isCompleted) {
          _isCompleted = true;
          widget.onStateChanged?.call(true);
        }
      }
    });
  }

  void _closeQuiz() {
    setState(() {
      _showQuiz = false;
    });
  }

  List<ScientificDiscovery> _getDiscoveriesForCurrentPeriod() {
    TimelinePeriod period = _periods[_currentPeriodIndex];
    return _discoveries.where((discovery) => 
      discovery.year >= period.startYear && 
      discovery.year <= period.endYear
    ).toList();
  }

  @override
  Widget build(BuildContext context) {
    if (_periods.isEmpty || _discoveries.isEmpty) {
      return const Center(child: Text('No timeline data available'));
    }

    TimelinePeriod currentPeriod = _periods[_currentPeriodIndex];
    List<ScientificDiscovery> periodDiscoveries = _getDiscoveriesForCurrentPeriod();

    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
        side: BorderSide(color: _primaryColor.withOpacity(0.5), width: 1),
      ),
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Title
            Text(
              widget.data['title'] ?? 'Timeline of Scientific Discoveries',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
                color: _textColor,
              ),
            ),

            const SizedBox(height: 16),

            // Period selector
            Container(
              height: 50,
              decoration: BoxDecoration(
                color: _backgroundColor,
                borderRadius: BorderRadius.circular(8),
                border: Border.all(color: _primaryColor.withOpacity(0.3)),
              ),
              child: ListView.builder(
                scrollDirection: Axis.horizontal,
                itemCount: _periods.length,
                itemBuilder: (context, index) {
                  bool isSelected = index == _currentPeriodIndex;
                  
                  return GestureDetector(
                    onTap: () => _selectPeriod(index),
                    child: Container(
                      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                      decoration: BoxDecoration(
                        color: isSelected ? _primaryColor : _backgroundColor,
                        borderRadius: BorderRadius.circular(8),
                      ),
                      child: Center(
                        child: Text(
                          _periods[index].name,
                          style: TextStyle(
                            color: isSelected ? Colors.white : _primaryColor,
                            fontWeight: isSelected ? FontWeight.bold : FontWeight.normal,
                          ),
                        ),
                      ),
                    ),
                  );
                },
              ),
            ),

            const SizedBox(height: 16),

            // Period description
            Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: _primaryColor.withOpacity(0.1),
                borderRadius: BorderRadius.circular(8),
                border: Border.all(color: _primaryColor.withOpacity(0.3)),
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    '${currentPeriod.name} (${currentPeriod.startYear} - ${currentPeriod.endYear})',
                    style: TextStyle(
                      fontWeight: FontWeight.bold,
                      color: _textColor,
                      fontSize: 16,
                    ),
                  ),
                  const SizedBox(height: 8),
                  Text(
                    currentPeriod.description,
                    style: TextStyle(
                      color: _textColor.withOpacity(0.8),
                    ),
                  ),
                ],
              ),
            ),

            const SizedBox(height: 16),

            // Timeline
            Expanded(
              child: Container(
                decoration: BoxDecoration(
                  color: _backgroundColor,
                  borderRadius: BorderRadius.circular(8),
                  border: Border.all(color: _primaryColor.withOpacity(0.3)),
                ),
                child: periodDiscoveries.isEmpty
                    ? Center(
                        child: Text(
                          'No discoveries found for this period',
                          style: TextStyle(
                            color: _textColor.withOpacity(0.6),
                            fontStyle: FontStyle.italic,
                          ),
                        ),
                      )
                    : ListView.builder(
                        controller: _scrollController,
                        itemCount: periodDiscoveries.length,
                        itemBuilder: (context, index) {
                          ScientificDiscovery discovery = periodDiscoveries[index];
                          bool isFirst = index == 0;
                          bool isLast = index == periodDiscoveries.length - 1;
                          
                          return _buildTimelineItem(
                            discovery: discovery,
                            isFirst: isFirst,
                            isLast: isLast,
                          );
                        },
                      ),
              ),
            ),

            const SizedBox(height: 16),

            // Quiz button
            if (!_showQuiz)
              Center(
                child: ElevatedButton.icon(
                  onPressed: _startQuiz,
                  icon: const Icon(Icons.quiz),
                  label: const Text('Test Your Knowledge'),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: _secondaryColor,
                    foregroundColor: Colors.white,
                    padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(8),
                    ),
                  ),
                ),
              ),

            // Quiz overlay
            if (_showQuiz) ...[
              const SizedBox(height: 16),
              _buildQuizSection(currentPeriod),
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildTimelineItem({
    required ScientificDiscovery discovery,
    required bool isFirst,
    required bool isLast,
  }) {
    return Row(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Timeline line and dot
        SizedBox(
          width: 50,
          child: Column(
            children: [
              // Top line
              if (!isFirst)
                Container(
                  width: 2,
                  height: 20,
                  color: _primaryColor,
                ),
              
              // Dot
              Container(
                width: 16,
                height: 16,
                decoration: BoxDecoration(
                  color: _primaryColor,
                  shape: BoxShape.circle,
                  border: Border.all(color: Colors.white, width: 2),
                ),
              ),
              
              // Bottom line
              if (!isLast)
                Expanded(
                  child: Container(
                    width: 2,
                    color: _primaryColor,
                  ),
                ),
            ],
          ),
        ),
        
        // Content
        Expanded(
          child: Padding(
            padding: const EdgeInsets.only(bottom: 24.0),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Year
                Text(
                  discovery.year.toString(),
                  style: TextStyle(
                    fontWeight: FontWeight.bold,
                    color: _primaryColor,
                  ),
                ),
                
                // Title
                Text(
                  discovery.title,
                  style: TextStyle(
                    fontWeight: FontWeight.bold,
                    color: _textColor,
                    fontSize: 16,
                  ),
                ),
                
                const SizedBox(height: 4),
                
                // Scientist
                Text(
                  'By ${discovery.scientist}',
                  style: TextStyle(
                    fontStyle: FontStyle.italic,
                    color: _textColor.withOpacity(0.7),
                  ),
                ),
                
                const SizedBox(height: 8),
                
                // Description
                Text(
                  discovery.description,
                  style: TextStyle(
                    color: _textColor.withOpacity(0.8),
                  ),
                ),
                
                const SizedBox(height: 8),
                
                // Impact
                if (discovery.impact.isNotEmpty)
                  Container(
                    padding: const EdgeInsets.all(8),
                    decoration: BoxDecoration(
                      color: _accentColor.withOpacity(0.1),
                      borderRadius: BorderRadius.circular(4),
                    ),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          'Impact:',
                          style: TextStyle(
                            fontWeight: FontWeight.bold,
                            color: _accentColor,
                          ),
                        ),
                        const SizedBox(height: 4),
                        Text(
                          discovery.impact,
                          style: TextStyle(
                            color: _textColor.withOpacity(0.8),
                          ),
                        ),
                      ],
                    ),
                  ),
              ],
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildQuizSection(TimelinePeriod period) {
    if (period.quizQuestions.isEmpty) {
      return Container(
        padding: const EdgeInsets.all(16),
        decoration: BoxDecoration(
          color: _backgroundColor,
          borderRadius: BorderRadius.circular(8),
          border: Border.all(color: _secondaryColor),
        ),
        child: Column(
          children: [
            Text(
              'No quiz questions available for this period',
              style: TextStyle(
                color: _textColor,
                fontStyle: FontStyle.italic,
              ),
            ),
            const SizedBox(height: 16),
            ElevatedButton(
              onPressed: _closeQuiz,
              style: ElevatedButton.styleFrom(
                backgroundColor: _secondaryColor,
                foregroundColor: Colors.white,
              ),
              child: const Text('Close'),
            ),
          ],
        ),
      );
    }

    if (_quizCompleted) {
      // Quiz results
      int correctAnswers = _questionAnswers.where((answer) => answer).length;
      double percentage = (correctAnswers / _questionAnswers.length) * 100;
      
      return Container(
        padding: const EdgeInsets.all(16),
        decoration: BoxDecoration(
          color: _backgroundColor,
          borderRadius: BorderRadius.circular(8),
          border: Border.all(color: _secondaryColor),
        ),
        child: Column(
          children: [
            Text(
              'Quiz Results',
              style: TextStyle(
                fontWeight: FontWeight.bold,
                fontSize: 18,
                color: _textColor,
              ),
            ),
            const SizedBox(height: 16),
            Text(
              'You got $correctAnswers out of ${_questionAnswers.length} questions correct (${percentage.toStringAsFixed(0)}%)',
              style: TextStyle(
                fontWeight: FontWeight.bold,
                color: percentage >= 70 ? Colors.green : Colors.red,
                fontSize: 16,
              ),
            ),
            const SizedBox(height: 24),
            ElevatedButton(
              onPressed: _closeQuiz,
              style: ElevatedButton.styleFrom(
                backgroundColor: _secondaryColor,
                foregroundColor: Colors.white,
                padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
              ),
              child: const Text('Close Quiz'),
            ),
          ],
        ),
      );
    }

    // Current question
    QuizQuestion question = period.quizQuestions[_currentQuestionIndex];
    
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: _backgroundColor,
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: _secondaryColor),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                'Question ${_currentQuestionIndex + 1} of ${period.quizQuestions.length}',
                style: TextStyle(
                  fontWeight: FontWeight.bold,
                  color: _secondaryColor,
                ),
              ),
              IconButton(
                icon: const Icon(Icons.close),
                onPressed: _closeQuiz,
                color: _textColor.withOpacity(0.6),
              ),
            ],
          ),
          const SizedBox(height: 16),
          Text(
            question.question,
            style: TextStyle(
              fontWeight: FontWeight.bold,
              fontSize: 16,
              color: _textColor,
            ),
          ),
          const SizedBox(height: 24),
          ...question.options.asMap().entries.map((entry) {
            int index = entry.key;
            String option = entry.value;
            
            return Padding(
              padding: const EdgeInsets.only(bottom: 12.0),
              child: ElevatedButton(
                onPressed: () => _answerQuestion(index == question.correctOptionIndex),
                style: ElevatedButton.styleFrom(
                  backgroundColor: _backgroundColor,
                  foregroundColor: _textColor,
                  padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
                  alignment: Alignment.centerLeft,
                  elevation: 1,
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(8),
                    side: BorderSide(color: _primaryColor.withOpacity(0.3)),
                  ),
                ),
                child: Text(
                  '${String.fromCharCode(65 + index)}. $option',
                  style: const TextStyle(
                    fontSize: 16,
                  ),
                ),
              ),
            );
          }).toList(),
        ],
      ),
    );
  }
}

/// Represents a period in the scientific timeline
class TimelinePeriod {
  final String id;
  final String name;
  final int startYear;
  final int endYear;
  final String description;
  final List<QuizQuestion> quizQuestions;

  TimelinePeriod({
    required this.id,
    required this.name,
    required this.startYear,
    required this.endYear,
    required this.description,
    required this.quizQuestions,
  });

  factory TimelinePeriod.fromJson(Map<String, dynamic> json) {
    final List<dynamic> questionsData = json['quizQuestions'] ?? [];
    final List<QuizQuestion> questions = questionsData
        .map((questionData) => QuizQuestion.fromJson(questionData))
        .toList();

    return TimelinePeriod(
      id: json['id'] as String,
      name: json['name'] as String,
      startYear: json['startYear'] as int,
      endYear: json['endYear'] as int,
      description: json['description'] as String,
      quizQuestions: questions,
    );
  }
}

/// Represents a scientific discovery
class ScientificDiscovery {
  final String id;
  final String title;
  final int year;
  final String scientist;
  final String description;
  final String impact;

  ScientificDiscovery({
    required this.id,
    required this.title,
    required this.year,
    required this.scientist,
    required this.description,
    required this.impact,
  });

  factory ScientificDiscovery.fromJson(Map<String, dynamic> json) {
    return ScientificDiscovery(
      id: json['id'] as String,
      title: json['title'] as String,
      year: json['year'] as int,
      scientist: json['scientist'] as String,
      description: json['description'] as String,
      impact: json['impact'] as String? ?? '',
    );
  }
}

/// Represents a quiz question
class QuizQuestion {
  final String question;
  final List<String> options;
  final int correctOptionIndex;
  final String explanation;

  QuizQuestion({
    required this.question,
    required this.options,
    required this.correctOptionIndex,
    required this.explanation,
  });

  factory QuizQuestion.fromJson(Map<String, dynamic> json) {
    final List<dynamic> optionsData = json['options'] as List<dynamic>;
    final List<String> options = optionsData.map((option) => option as String).toList();

    return QuizQuestion(
      question: json['question'] as String,
      options: options,
      correctOptionIndex: json['correctOptionIndex'] as int,
      explanation: json['explanation'] as String? ?? '',
    );
  }
}
