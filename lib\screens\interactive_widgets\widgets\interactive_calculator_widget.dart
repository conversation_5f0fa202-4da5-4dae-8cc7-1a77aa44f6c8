import 'package:flutter/material.dart';
import '../../../models/interactive_widget_model.dart';

class InteractiveCalculatorWidget extends StatefulWidget {
  final InteractiveWidgetModel widget;

  const InteractiveCalculatorWidget({super.key, required this.widget});

  @override
  State<InteractiveCalculatorWidget> createState() => _InteractiveCalculatorWidgetState();
}

class _InteractiveCalculatorWidgetState extends State<InteractiveCalculatorWidget> {
  String _display = '0';
  String _memory = '';
  String _operation = '';
  String _firstOperand = '';
  bool _isNewOperation = true;
  String _selectedFunction = '';
  
  @override
  void initState() {
    super.initState();
    _selectedFunction = widget.widget.data['defaultFunction'] as String? ?? 'Basic';
  }

  @override
  Widget build(BuildContext context) {
    final functions = List<String>.from(widget.widget.data['functions'] ?? ['Basic']);
    
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Calculator display
        Container(
          width: double.infinity,
          padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
          decoration: BoxDecoration(
            color: Colors.grey[200],
            borderRadius: BorderRadius.circular(8),
            border: Border.all(color: Colors.grey[300]!),
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.end,
            children: [
              // Memory indicator
              if (_memory.isNotEmpty)
                Text(
                  'M: $_memory',
                  style: TextStyle(
                    fontSize: 12,
                    color: Colors.grey[700],
                  ),
                ),
              
              // Main display
              Text(
                _display,
                style: const TextStyle(
                  fontSize: 24,
                  fontWeight: FontWeight.bold,
                ),
                textAlign: TextAlign.right,
              ),
            ],
          ),
        ),
        
        // Function selector
        Padding(
          padding: const EdgeInsets.only(top: 12.0),
          child: SingleChildScrollView(
            scrollDirection: Axis.horizontal,
            child: Row(
              children: functions.map((function) {
                return Padding(
                  padding: const EdgeInsets.only(right: 8.0),
                  child: ChoiceChip(
                    label: Text(function),
                    selected: _selectedFunction == function,
                    onSelected: (selected) {
                      if (selected) {
                        setState(() {
                          _selectedFunction = function;
                          _clear();
                        });
                      }
                    },
                  ),
                );
              }).toList(),
            ),
          ),
        ),
        
        // Calculator buttons
        Padding(
          padding: const EdgeInsets.only(top: 12.0),
          child: _buildCalculatorButtons(),
        ),
      ],
    );
  }
  
  Widget _buildCalculatorButtons() {
    switch (_selectedFunction) {
      case 'Trigonometry':
        return _buildTrigonometryButtons();
      case 'Logarithms':
        return _buildLogarithmButtons();
      case 'Statistics':
        return _buildStatisticsButtons();
      case 'Basic':
      default:
        return _buildBasicButtons();
    }
  }
  
  Widget _buildBasicButtons() {
    return Column(
      children: [
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceEvenly,
          children: [
            _buildButton('7'),
            _buildButton('8'),
            _buildButton('9'),
            _buildButton('÷', isOperation: true),
          ],
        ),
        const SizedBox(height: 8),
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceEvenly,
          children: [
            _buildButton('4'),
            _buildButton('5'),
            _buildButton('6'),
            _buildButton('×', isOperation: true),
          ],
        ),
        const SizedBox(height: 8),
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceEvenly,
          children: [
            _buildButton('1'),
            _buildButton('2'),
            _buildButton('3'),
            _buildButton('-', isOperation: true),
          ],
        ),
        const SizedBox(height: 8),
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceEvenly,
          children: [
            _buildButton('0'),
            _buildButton('.'),
            _buildButton('=', isEquals: true),
            _buildButton('+', isOperation: true),
          ],
        ),
        const SizedBox(height: 8),
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceEvenly,
          children: [
            _buildButton('C', isClear: true),
            _buildButton('M+', isMemory: true),
            _buildButton('MR', isMemory: true),
            _buildButton('MC', isMemory: true),
          ],
        ),
      ],
    );
  }
  
  Widget _buildTrigonometryButtons() {
    return Column(
      children: [
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceEvenly,
          children: [
            _buildButton('sin', isFunction: true),
            _buildButton('cos', isFunction: true),
            _buildButton('tan', isFunction: true),
          ],
        ),
        const SizedBox(height: 8),
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceEvenly,
          children: [
            _buildButton('π', isConstant: true),
            _buildButton('e', isConstant: true),
            _buildButton('C', isClear: true),
          ],
        ),
      ],
    );
  }
  
  Widget _buildLogarithmButtons() {
    return Column(
      children: [
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceEvenly,
          children: [
            _buildButton('log', isFunction: true),
            _buildButton('ln', isFunction: true),
            _buildButton('10^x', isFunction: true),
          ],
        ),
        const SizedBox(height: 8),
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceEvenly,
          children: [
            _buildButton('x²', isFunction: true),
            _buildButton('√', isFunction: true),
            _buildButton('C', isClear: true),
          ],
        ),
      ],
    );
  }
  
  Widget _buildStatisticsButtons() {
    return Column(
      children: [
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceEvenly,
          children: [
            _buildButton('Mean', isFunction: true),
            _buildButton('Median', isFunction: true),
          ],
        ),
        const SizedBox(height: 8),
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceEvenly,
          children: [
            _buildButton('Std Dev', isFunction: true),
            _buildButton('C', isClear: true),
          ],
        ),
      ],
    );
  }
  
  Widget _buildButton(String text, {
    bool isOperation = false,
    bool isEquals = false,
    bool isClear = false,
    bool isMemory = false,
    bool isFunction = false,
    bool isConstant = false,
  }) {
    Color backgroundColor;
    Color textColor;
    
    if (isOperation) {
      backgroundColor = Colors.orange;
      textColor = Colors.white;
    } else if (isEquals) {
      backgroundColor = Colors.green;
      textColor = Colors.white;
    } else if (isClear) {
      backgroundColor = Colors.red;
      textColor = Colors.white;
    } else if (isMemory) {
      backgroundColor = Colors.blue;
      textColor = Colors.white;
    } else if (isFunction || isConstant) {
      backgroundColor = Colors.purple;
      textColor = Colors.white;
    } else {
      backgroundColor = Colors.grey[300]!;
      textColor = Colors.black;
    }
    
    return SizedBox(
      width: 60,
      height: 40,
      child: ElevatedButton(
        onPressed: () => _onButtonPressed(text),
        style: ElevatedButton.styleFrom(
          backgroundColor: backgroundColor,
          foregroundColor: textColor,
          padding: EdgeInsets.zero,
        ),
        child: Text(text),
      ),
    );
  }
  
  void _onButtonPressed(String value) {
    setState(() {
      if (value == 'C') {
        _clear();
      } else if (value == '=') {
        _calculate();
      } else if (value == 'M+') {
        _memory = _display;
      } else if (value == 'MR') {
        _display = _memory;
      } else if (value == 'MC') {
        _memory = '';
      } else if ('+-×÷'.contains(value)) {
        _operation = value;
        _firstOperand = _display;
        _isNewOperation = true;
      } else {
        // Handle digits and decimal point
        if (_isNewOperation) {
          _display = value;
          _isNewOperation = false;
        } else {
          if (_display == '0' && value != '.') {
            _display = value;
          } else {
            _display += value;
          }
        }
      }
    });
  }
  
  void _clear() {
    _display = '0';
    _operation = '';
    _firstOperand = '';
    _isNewOperation = true;
  }
  
  void _calculate() {
    if (_operation.isEmpty || _firstOperand.isEmpty) return;
    
    double first = double.tryParse(_firstOperand) ?? 0;
    double second = double.tryParse(_display) ?? 0;
    double result = 0;
    
    switch (_operation) {
      case '+':
        result = first + second;
        break;
      case '-':
        result = first - second;
        break;
      case '×':
        result = first * second;
        break;
      case '÷':
        result = second != 0 ? first / second : 0;
        break;
    }
    
    _display = result.toString();
    if (_display.endsWith('.0')) {
      _display = _display.substring(0, _display.length - 2);
    }
    
    _operation = '';
    _firstOperand = '';
    _isNewOperation = true;
  }
}
