{"id": "analyzing-dc-circuits", "title": "Analyzing DC Circuits", "description": "Develop skills in analyzing and solving direct current circuits.", "order": 2, "lessons": [{"id": "series-parallel-resistors", "title": "Series and Parallel Resistor Combinations", "description": "Calculate equivalent resistance.", "order": 1, "type": "interactive_lesson", "estimatedTimeMinutes": 25, "contentBlocks": [{"id": "series-parallel-resistors-screen-1", "type": "lesson_screen", "order": 1, "estimatedTimeSeconds": 60, "content": {"headline": "Combining Resistors", "body_md": "Often, circuits use multiple resistors. How they are connected determines their combined, or **equivalent resistance (R_eq)**. This is crucial for circuit analysis!\n\nThere are two basic ways to connect resistors: **Series** and **Parallel**.", "visual": {"type": "unsplash_search", "value": "network connections"}, "interactive_element": {"type": "button", "button_text": "Let's start with Series!", "action": "next_screen"}}}, {"id": "series-parallel-resistors-screen-2", "type": "lesson_screen", "order": 2, "estimatedTimeSeconds": 120, "content": {"headline": "Resistors in Series: One Path", "body_md": "When resistors are connected in **series**, they are chained together, end-to-end, forming a single path for current.\n\n- The **same current** flows through each resistor.\n- The total voltage drop across the series combination is the **sum of individual voltage drops**.\n- The equivalent resistance is simply the **sum of their individual resistances**:\n  `R_eq = R1 + R2 + R3 + ...`", "visual": {"type": "local_asset", "value": "assets/images/course_specific/resistors_in_series.png"}, "interactive_element": {"type": "multiple_choice_text", "question_text": "If you have three resistors in series: 10Ω, 20Ω, and 30Ω, what is their equivalent resistance?", "options": [{"id": "opt1", "text": "6Ω"}, {"id": "opt2", "text": "30Ω"}, {"id": "opt3", "text": "60Ω"}, {"id": "opt4", "text": "600Ω"}], "correct_option_id": "opt3", "feedback_correct": "Correct! R_eq = 10Ω + 20Ω + 30Ω = 60Ω.", "feedback_incorrect": "For series resistors, you add their values directly."}}}, {"id": "series-parallel-resistors-screen-3", "type": "lesson_screen", "order": 3, "estimatedTimeSeconds": 75, "content": {"headline": "Series Implication: Current", "body_md": "Since the same current flows through all resistors in series, if one resistor 'breaks' (opens the circuit), the current stops for all of them. Think of old-style Christmas lights!", "visual": {"type": "giphy_search", "value": "broken chain link"}, "interactive_element": {"type": "button", "button_text": "Now for <PERSON><PERSON><PERSON>!", "action": "next_screen"}}}, {"id": "series-parallel-resistors-screen-4", "type": "lesson_screen", "order": 4, "estimatedTimeSeconds": 150, "content": {"headline": "Resistors in Parallel: Multiple Paths", "body_md": "When resistors are connected in **parallel**, they are connected across the same two points, providing multiple paths for current.\n\n- The **voltage drop across each resistor is the same**.\n- The total current is the **sum of currents through each branch**.\n- The equivalent resistance is found using the formula:\n  `1 / R_eq = 1 / R1 + 1 / R2 + 1 / R3 + ...`\n\n**Important:** R_eq in parallel is always *less* than the smallest individual resistance.", "visual": {"type": "local_asset", "value": "assets/images/course_specific/resistors_in_parallel.png"}, "interactive_element": {"type": "multiple_choice_text", "question_text": "Two 10Ω resistors are in parallel. What is their R_eq?", "options": [{"id": "opt1", "text": "20Ω"}, {"id": "opt2", "text": "10Ω"}, {"id": "opt3", "text": "5Ω"}, {"id": "opt4", "text": "0Ω"}], "correct_option_id": "opt3", "feedback_correct": "Correct! 1/R_eq = 1/10 + 1/10 = 2/10. So R_eq = 10/2 = 5Ω. (For two equal resistors in parallel, R_eq is half.)", "feedback_incorrect": "Use the formula: 1/R_eq = 1/R1 + 1/R2. Then take the reciprocal."}}}, {"id": "series-parallel-resistors-screen-5", "type": "lesson_screen", "order": 5, "estimatedTimeSeconds": 90, "content": {"headline": "Parallel Implication: Current Division", "body_md": "In a parallel circuit, current divides among the branches. More current will flow through paths of lower resistance. If one parallel branch 'breaks', current can still flow through the other branches.", "visual": {"type": "giphy_search", "value": "river splitting"}, "interactive_element": {"type": "button", "button_text": "What about combined circuits?", "action": "next_screen"}}}, {"id": "series-parallel-resistors-screen-6", "type": "lesson_screen", "order": 6, "estimatedTimeSeconds": 120, "content": {"headline": "Combination Circuits", "body_md": "Many circuits have both series and parallel combinations. To find the total R_eq, you simplify them step-by-step:\n\n1. Identify purely series or purely parallel sections.\n2. Calculate their R_eq.\n3. Redraw the circuit with the simplified equivalent resistor.\n4. Repeat until you have one single R_eq for the entire circuit.", "visual": {"type": "local_asset", "value": "assets/images/course_specific/series_parallel_combo.png"}, "interactive_element": {"type": "button", "button_text": "Let's recap!", "action": "next_screen"}}}, {"id": "series-parallel-resistors-screen-7", "type": "lesson_screen", "order": 7, "estimatedTimeSeconds": 60, "content": {"headline": "Series & Parallel Mastered!", "body_md": "You've got it!\n\n- **Series:** R_eq = R1 + R2 + ... (Same current)\n- **Parallel:** 1/R_eq = 1/R1 + 1/R2 + ... (Same voltage)\n- Combine step-by-step for complex circuits.", "visual": {"type": "giphy_search", "value": "circuit puzzle solved"}, "interactive_element": {"type": "button", "button_text": "Finish Lesson", "action": "complete_lesson"}}}]}, {"id": "voltage-dividers", "title": "Voltage Dividers: Creating Specific Voltages", "description": "Analyze and design voltage divider circuits.", "order": 2, "type": "interactive_lesson", "estimatedTimeMinutes": 20, "contentBlocks": [{"id": "voltage-dividers-screen-1", "type": "lesson_screen", "order": 1, "estimatedTimeSeconds": 60, "content": {"headline": "Sharing the Voltage!", "body_md": "A **voltage divider** is a simple but incredibly useful circuit. It uses two or more resistors in series to 'divide' a source voltage, allowing you to tap off a smaller, specific voltage.\n\nImagine a waterfall with ledges – you can get water from different heights!", "visual": {"type": "unsplash_search", "value": "waterfall with ledges"}, "interactive_element": {"type": "button", "button_text": "How does it work?", "action": "next_screen"}}}, {"id": "voltage-dividers-screen-2", "type": "lesson_screen", "order": 2, "estimatedTimeSeconds": 120, "content": {"headline": "The Voltage Divider Formula", "body_md": "For two resistors in series (R1 and R2) connected to an input voltage (V_in), the output voltage (V_out) across R2 is given by:\n\n`V_out = V_in * (R2 / (R1 + R2))`\n\n- R1 is the resistor connected to the V_in source.\n- R2 is the resistor across which V_out is measured (connected to ground or a common point).", "visual": {"type": "local_asset", "value": "assets/images/course_specific/voltage_divider_formula.png"}, "interactive_element": {"type": "button", "button_text": "Let's try an example!", "action": "next_screen"}}}, {"id": "voltage-dividers-screen-3", "type": "lesson_screen", "order": 3, "estimatedTimeSeconds": 120, "content": {"headline": "Crunching the Numbers", "body_md": "Let's say V_in = 10V, R1 = 7kΩ, and R2 = 3kΩ.\n\n`V_out = 10V * (3kΩ / (7kΩ + 3kΩ))`\n`V_out = 10V * (3kΩ / 10kΩ)`\n`V_out = 10V * 0.3`", "visual": {"type": "local_asset", "value": "assets/images/course_specific/voltage_divider_example_calc.png"}, "interactive_element": {"type": "multiple_choice_text", "question_text": "What is V_out in this example?", "options": [{"id": "opt1", "text": "3V"}, {"id": "opt2", "text": "7V"}, {"id": "opt3", "text": "0.3V"}, {"id": "opt4", "text": "10V"}], "correct_option_id": "opt1", "feedback_correct": "Correct! V_out = 10V * 0.3 = 3V.", "feedback_incorrect": "Check your calculation: V_out = V_in * (R2 / (R1 + R2))."}}}, {"id": "voltage-dividers-screen-4", "type": "lesson_screen", "order": 4, "estimatedTimeSeconds": 90, "content": {"headline": "Practical Uses", "body_md": "Voltage dividers are everywhere!\n\n- **Reference Voltages:** Providing a stable, lower voltage for sensitive components like microcontrollers or sensors.\n- **Sensor Interfacing:** Many sensors (like Light Dependent Resistors - LDRs, or thermistors) change their resistance. A voltage divider converts this resistance change into a voltage change that can be easily read by a circuit.", "visual": {"type": "unsplash_search", "value": "electronic sensor circuit"}, "interactive_element": {"type": "button", "button_text": "Is it always perfect?", "action": "next_screen"}}}, {"id": "voltage-dividers-screen-5", "type": "lesson_screen", "order": 5, "estimatedTimeSeconds": 120, "content": {"headline": "The Loading Effect", "body_md": "A key consideration! If you connect a **load** (another circuit or component) in parallel with R2, it draws current. This load has its own resistance (R_load).\n\nThe effective resistance of R2 in parallel with R_load will be *less* than R2 alone. This changes the V_out!\n\n`R_effective_2 = (R2 * R_load) / (R2 + R_load)`\n\nFor an accurate voltage divider, R_load should be much larger than R2 (ideally 10x or more).", "visual": {"type": "local_asset", "value": "assets/images/course_specific/voltage_divider_loading.png"}, "interactive_element": {"type": "multiple_choice_text", "question_text": "If R_load is much smaller than R2, what happens to V_out compared to the unloaded V_out?", "options": [{"id": "opt1", "text": "It increases significantly"}, {"id": "opt2", "text": "It decreases significantly"}, {"id": "opt3", "text": "It stays almost the same"}], "correct_option_id": "opt2", "feedback_correct": "Correct! A small R_load significantly lowers the effective resistance across which V_out is measured, thus lowering V_out.", "feedback_incorrect": "If R_load is small, it draws more current, reducing the voltage across the parallel combination."}}}, {"id": "voltage-dividers-screen-6", "type": "lesson_screen", "order": 6, "estimatedTimeSeconds": 60, "content": {"headline": "Voltage Divider Recap", "body_md": "You've divided and conquered!\n\n- Voltage dividers use series resistors to produce a lower, specific voltage.\n- Formula: `V_out = V_in * (R2 / (R1 + R2))`\n- Crucial for reference voltages and sensor circuits.\n- Be mindful of the loading effect!", "visual": {"type": "giphy_search", "value": "electrical success"}, "interactive_element": {"type": "button", "button_text": "Finish Lesson", "action": "complete_lesson"}}}]}, {"id": "current-dividers", "title": "Current Dividers: Sharing Current", "description": "Analyze how current splits in parallel branches.", "order": 3, "type": "interactive_lesson", "estimatedTimeMinutes": 20, "contentBlocks": [{"id": "current-dividers-screen-1", "type": "lesson_screen", "order": 1, "estimatedTimeSeconds": 60, "content": {"headline": "Splitting the Current!", "body_md": "Just as voltage dividers split voltage, **current dividers** split current! This happens when you have resistors in **parallel**.\n\nThe total current entering a parallel combination splits among the branches.", "visual": {"type": "unsplash_search", "value": "river splitting into streams"}, "interactive_element": {"type": "button", "button_text": "How does the current split?", "action": "next_screen"}}}, {"id": "current-dividers-screen-2", "type": "lesson_screen", "order": 2, "estimatedTimeSeconds": 120, "content": {"headline": "The Current Divider Formula (2 Resistors)", "body_md": "For two resistors in parallel (R1 and R2) with a total current (I_total) flowing into the combination, the current through R1 (I1) is:\n\n`I1 = I_total * (R2 / (R1 + R2))`\n\nAnd the current through R2 (I2) is:\n\n`I2 = I_total * (R1 / (R1 + R2))`\n\nNotice something? The current through one resistor depends on the *other* resistor's value in the numerator!", "visual": {"type": "local_asset", "value": "assets/images/course_specific/current_divider_formula.png"}, "interactive_element": {"type": "button", "button_text": "Interesting! Let's see an example.", "action": "next_screen"}}}, {"id": "current-dividers-screen-3", "type": "lesson_screen", "order": 3, "estimatedTimeSeconds": 120, "content": {"headline": "Example Calculation", "body_md": "Suppose I_total = 10A. R1 = 30Ω, R2 = 70Ω.\n\nLet's find I1 (current through R1):\n`I1 = 10A * (70Ω / (30Ω + 70Ω))`\n`I1 = 10A * (70Ω / 100Ω)`\n`I1 = 10A * 0.7`", "visual": {"type": "local_asset", "value": "assets/images/course_specific/current_divider_example_calc.png"}, "interactive_element": {"type": "multiple_choice_text", "question_text": "What is I1 (current through R1)?", "options": [{"id": "opt1", "text": "3A"}, {"id": "opt2", "text": "7A"}, {"id": "opt3", "text": "0.7A"}, {"id": "opt4", "text": "10A"}], "correct_option_id": "opt2", "feedback_correct": "Correct! I1 = 7A. This means I2 must be 3A (10A - 7A). Less resistance (R1=30Ω) gets more current!", "feedback_incorrect": "Double-check the formula: I1 = I_total * (R2 / (R1 + R2))."}}}, {"id": "current-dividers-screen-4", "type": "lesson_screen", "order": 4, "estimatedTimeSeconds": 90, "content": {"headline": "Path of Least Resistance", "body_md": "The formulas confirm a key idea: **current prefers the path of least resistance**.\n\nIn a parallel circuit:\n- The branch with the *smaller* resistance will get a *larger* share of the total current.\n- The branch with the *larger* resistance will get a *smaller* share.", "visual": {"type": "giphy_search", "value": "easy path choice"}, "interactive_element": {"type": "button", "button_text": "Makes sense!", "action": "next_screen"}}}, {"id": "current-dividers-screen-5", "type": "lesson_screen", "order": 5, "estimatedTimeSeconds": 100, "content": {"headline": "General Current Divider Rule", "body_md": "For any number of resistors in parallel, the current through any one resistor (Rx) is:\n\n`Ix = I_total * (R_eq_parallel / Rx)`\n\nWhere `R_eq_parallel` is the equivalent resistance of the entire parallel combination, and `Rx` is the resistance of the specific branch you're interested in.", "visual": {"type": "static_text", "value": "Ix = I_total * (R_eq / Rx)"}, "interactive_element": {"type": "multiple_choice_text", "question_text": "If Rx is very small compared to other parallel resistors, will Ix be large or small?", "options": [{"id": "opt1", "text": "Large"}, {"id": "opt2", "text": "Small"}, {"id": "opt3", "text": "Depends on I_total only"}], "correct_option_id": "opt1", "feedback_correct": "Correct! A small Rx means it's an easier path, so it gets a larger share of I_total.", "feedback_incorrect": "Remember, current prefers the path of least resistance. A smaller Rx means less resistance."}}}, {"id": "current-dividers-screen-6", "type": "lesson_screen", "order": 6, "estimatedTimeSeconds": 60, "content": {"headline": "Current Division Summary", "body_md": "You've successfully navigated current division!\n\n- Current splits in parallel branches.\n- More current flows through lower resistance paths.\n- You can calculate the current in each branch using the current divider formulas.", "visual": {"type": "giphy_search", "value": "water flowing multiple paths"}, "interactive_element": {"type": "button", "button_text": "Finish Lesson", "action": "complete_lesson"}}}]}, {"id": "kirchhoffs-voltage-law", "title": "<PERSON><PERSON><PERSON>'s Voltage Law (KVL)", "description": "Apply the principle of voltage conservation in loops.", "order": 4, "type": "interactive_lesson", "estimatedTimeMinutes": 25, "contentBlocks": [{"id": "kvl-screen-1", "type": "lesson_screen", "order": 1, "estimatedTimeSeconds": 60, "content": {"headline": "<PERSON><PERSON><PERSON>'s Voltage Law (KVL)", "body_md": "<PERSON> gave us two fundamental laws for circuit analysis. The first is **<PERSON><PERSON><PERSON>'s Voltage Law (KVL)**.\n\nIt's based on the principle of **conservation of energy** and states: *The algebraic sum of all voltages around any closed loop in a circuit is equal to zero.*", "visual": {"type": "unsplash_search", "value": "closed loop path"}, "interactive_element": {"type": "button", "button_text": "What does 'algebraic sum' mean?", "action": "next_screen"}}}, {"id": "kvl-screen-2", "type": "lesson_screen", "order": 2, "estimatedTimeSeconds": 120, "content": {"headline": "Voltage Rises and Drops", "body_md": "As you trace a path around a loop:\n\n- **Voltage sources** (like batteries) typically represent a **voltage rise** if you go from - to +.\n- **Voltage drops** occur across resistors (due to current flow, V = IR).\n\nKVL means: `ΣV_rises - ΣV_drops = 0`  or  `ΣV_rises = ΣV_drops`\n\nEssentially, whatever voltage is supplied by sources must be dropped by the components in the loop.", "visual": {"type": "local_asset", "value": "assets/images/course_specific/kvl_loop_diagram.png"}, "interactive_element": {"type": "button", "button_text": "Let's see an example loop.", "action": "next_screen"}}}, {"id": "kvl-screen-3", "type": "lesson_screen", "order": 3, "estimatedTimeSeconds": 150, "content": {"headline": "Applying KVL to a Simple Loop", "body_md": "Consider a loop with a 10V battery, and two resistors R1 and R2. Let the voltage drop across R1 be V1, and across R2 be V2.\n\nTracing the loop (starting from the battery's negative terminal):\n`+10V (rise) - V1 (drop) - V2 (drop) = 0`\n\nSo, `10V = V1 + V2`.\nThe sum of voltage drops across resistors equals the source voltage.", "visual": {"type": "local_asset", "value": "assets/images/course_specific/kvl_simple_example.png"}, "interactive_element": {"type": "multiple_choice_text", "question_text": "If V1 = 6V in the example above, what must V2 be?", "options": [{"id": "opt1", "text": "16V"}, {"id": "opt2", "text": "10V"}, {"id": "opt3", "text": "6V"}, {"id": "opt4", "text": "4V"}], "correct_option_id": "opt4", "feedback_correct": "Correct! 10V = 6V + V2, so V2 = 4V.", "feedback_incorrect": "Remember, the sum of drops (V1 + V2) must equal the source rise (10V)."}}}, {"id": "kvl-screen-4", "type": "lesson_screen", "order": 4, "estimatedTimeSeconds": 90, "content": {"headline": "Choosing a Loop and Direction", "body_md": "You can choose any closed loop in a circuit.\n\nYou can also choose to trace the loop clockwise or counter-clockwise. Just be consistent with your signs for rises and drops!\n\n- Going through a resistor **with** the assumed current direction = voltage drop (-IR).\n- Going through a resistor **against** the assumed current direction = voltage rise (+IR).", "visual": {"type": "giphy_search", "value": "choosing direction path"}, "interactive_element": {"type": "button", "button_text": "Why is KVL so useful?", "action": "next_screen"}}}, {"id": "kvl-screen-5", "type": "lesson_screen", "order": 5, "estimatedTimeSeconds": 100, "content": {"headline": "Power of KVL", "body_md": "KVL is extremely powerful for analyzing more complex circuits where simple series/parallel reduction isn't enough.\n\nBy applying KVL to different loops, you can create a system of equations to solve for unknown currents and voltages throughout the circuit. This is a cornerstone of **mesh analysis**.", "visual": {"type": "unsplash_search", "value": "complex network circuit"}, "interactive_element": {"type": "multiple_choice_text", "question_text": "KVL is based on the conservation of what fundamental quantity?", "options": [{"id": "opt1", "text": "Current"}, {"id": "opt2", "text": "Charge"}, {"id": "opt3", "text": "Energy"}, {"id": "opt4", "text": "Power"}], "correct_option_id": "opt3", "feedback_correct": "Correct! Voltage is related to electric potential energy.", "feedback_incorrect": "Think about what voltage represents in terms of work or potential."}}}, {"id": "kvl-screen-6", "type": "lesson_screen", "order": 6, "estimatedTimeSeconds": 60, "content": {"headline": "KVL Summary", "body_md": "You've looped through KVL!\n\n- The sum of voltages around any closed loop is zero.\n- `ΣV_rises = ΣV_drops`.\n- Essential for analyzing complex circuits and forming equations.\n- Based on conservation of energy.", "visual": {"type": "giphy_search", "value": "loop success"}, "interactive_element": {"type": "button", "button_text": "Finish Lesson", "action": "complete_lesson"}}}]}, {"id": "kirchhoffs-current-law", "title": "<PERSON><PERSON><PERSON>'s Current Law (KCL)", "description": "Apply the principle of current conservation at nodes.", "order": 5, "type": "interactive_lesson", "estimatedTimeMinutes": 25, "contentBlocks": [{"id": "kcl-screen-1", "type": "lesson_screen", "order": 1, "estimatedTimeSeconds": 60, "content": {"headline": "<PERSON><PERSON><PERSON>'s Current Law (KCL)", "body_md": "The second of <PERSON><PERSON><PERSON>'s laws is **<PERSON><PERSON><PERSON>'s Current Law (KCL)**. It's based on the principle of **conservation of charge**.\n\nKCL states: *The algebraic sum of currents entering and leaving any node (or junction) in a circuit is equal to zero.*", "visual": {"type": "unsplash_search", "value": "junction road intersection"}, "interactive_element": {"type": "button", "button_text": "What's a node?", "action": "next_screen"}}}, {"id": "kcl-screen-2", "type": "lesson_screen", "order": 2, "estimatedTimeSeconds": 120, "content": {"headline": "Nodes and Current Flow", "body_md": "A **node** (or junction) is simply a point in a circuit where two or more components connect.\n\nKCL means: `ΣI_in = ΣI_out`\n\nThink of it like water pipes: the amount of water flowing into a junction must equal the amount of water flowing out. Charge can't just disappear or be created at a node!", "visual": {"type": "local_asset", "value": "assets/images/course_specific/kcl_node_diagram.png"}, "interactive_element": {"type": "button", "button_text": "Show me an example!", "action": "next_screen"}}}, {"id": "kcl-screen-3", "type": "lesson_screen", "order": 3, "estimatedTimeSeconds": 150, "content": {"headline": "Applying KCL", "body_md": "Imagine a node where current I1 flows in, and currents I2 and I3 flow out.\n\nAccording to KCL: `I1 = I2 + I3`\n\nOr, using the algebraic sum (currents in are positive, currents out are negative):\n`I1 - I2 - I3 = 0`", "visual": {"type": "local_asset", "value": "assets/images/course_specific/kcl_simple_example.png"}, "interactive_element": {"type": "multiple_choice_text", "question_text": "If I1 = 5A and I2 = 2A flow into a node, and I3 flows out, what is I3?", "options": [{"id": "opt1", "text": "3A"}, {"id": "opt2", "text": "5A"}, {"id": "opt3", "text": "7A"}, {"id": "opt4", "text": "2A"}], "correct_option_id": "opt3", "feedback_correct": "Correct! ΣI_in = I1 + I2 = 5A + 2A = 7A. So, ΣI_out = I3 = 7A.", "feedback_incorrect": "Remember, total current in must equal total current out. Here, both I1 and I2 are flowing *in*."}}}, {"id": "kcl-screen-4", "type": "lesson_screen", "order": 4, "estimatedTimeSeconds": 90, "content": {"headline": "Sign Convention", "body_md": "It's common to assign:\n\n- **Positive (+) sign** to currents entering a node.\n- **Negative (-) sign** to currents leaving a node.\n\nWith this convention, KCL is simply: `ΣI_at_node = 0`.", "visual": {"type": "giphy_search", "value": "plus minus signs"}, "interactive_element": {"type": "button", "button_text": "How is KCL used?", "action": "next_screen"}}}, {"id": "kcl-screen-5", "type": "lesson_screen", "order": 5, "estimatedTimeSeconds": 100, "content": {"headline": "Importance of KCL", "body_md": "KCL is fundamental for:\n\n- Analyzing parallel circuits (where current splits).\n- **Nodal Analysis:** A powerful circuit analysis technique where you apply KCL at various nodes to find unknown node voltages.\n- Understanding how current is distributed in complex networks.", "visual": {"type": "unsplash_search", "value": "network node connections"}, "interactive_element": {"type": "multiple_choice_text", "question_text": "KCL is based on the conservation of what?", "options": [{"id": "opt1", "text": "Energy"}, {"id": "opt2", "text": "Voltage"}, {"id": "opt3", "text": "Charge"}, {"id": "opt4", "text": "Resistance"}], "correct_option_id": "opt3", "feedback_correct": "Correct! Current is the flow of charge, and charge is conserved.", "feedback_incorrect": "Think about what current actually is – the flow of..."}}}, {"id": "kcl-screen-6", "type": "lesson_screen", "order": 6, "estimatedTimeSeconds": 60, "content": {"headline": "KCL Wrapped Up!", "body_md": "You've mastered KCL!\n\n- The sum of currents entering a node equals the sum of currents leaving it.\n- `ΣI_in = ΣI_out` or `ΣI_at_node = 0`.\n- Essential for nodal analysis and understanding current distribution.\n- Based on conservation of charge.", "visual": {"type": "giphy_search", "value": "flow junction success"}, "interactive_element": {"type": "button", "button_text": "Finish Lesson", "action": "complete_lesson"}}}]}, {"id": "module-test-dc-circuit-solver", "title": "Module Test: DC Circuit Solver", "description": "Analyze and solve problems involving series, parallel, and combined DC circuits using <PERSON><PERSON><PERSON>'s Laws.", "order": 6, "type": "module_test_interactive", "estimatedTimeMinutes": 35, "passingScorePercentage": 70, "contentBlocks": [{"id": "test-dc-circuit-solver-q1", "type": "question_screen", "order": 1, "estimatedTimeSeconds": 75, "content": {"headline": "Question 1: Series Resistors", "body_md": "Three resistors, R1=5Ω, R2=10Ω, and R3=15Ω, are connected in series to a 30V DC source. What is the total current flowing from the source?", "visual": {"type": "local_asset", "value": "assets/images/course_specific/series_circuit_q.png"}, "interactive_element": {"type": "multiple_choice_text", "question_text": "Calculate the total current.", "options": [{"id": "opt1", "text": "0.5A"}, {"id": "opt2", "text": "1A"}, {"id": "opt3", "text": "2A"}, {"id": "opt4", "text": "30A"}], "correct_option_id": "opt2", "feedback_correct": "Correct! R_eq = 5+10+15 = 30Ω. I = V/R_eq = 30V/30Ω = 1A.", "feedback_incorrect": "First find the total equivalent resistance for series resistors, then use <PERSON><PERSON>'s Law (I = V/R)."}}}, {"id": "test-dc-circuit-solver-q2", "type": "question_screen", "order": 2, "estimatedTimeSeconds": 90, "content": {"headline": "Question 2: <PERSON><PERSON><PERSON> Resistors", "body_md": "Two resistors, R1=20Ω and R2=30Ω, are connected in parallel. What is their equivalent resistance?", "visual": {"type": "local_asset", "value": "assets/images/course_specific/parallel_circuit_q.png"}, "interactive_element": {"type": "multiple_choice_text", "question_text": "Calculate R_eq.", "options": [{"id": "opt1", "text": "50Ω"}, {"id": "opt2", "text": "25Ω"}, {"id": "opt3", "text": "12Ω"}, {"id": "opt4", "text": "10Ω"}], "correct_option_id": "opt3", "feedback_correct": "Correct! 1/R_eq = 1/20 + 1/30 = 3/60 + 2/60 = 5/60. So R_eq = 60/5 = 12Ω.", "feedback_incorrect": "Use the formula for parallel resistors: 1/R_eq = 1/R1 + 1/R2. Don't forget to take the reciprocal at the end."}}}, {"id": "test-dc-circuit-solver-q3", "type": "question_screen", "order": 3, "estimatedTimeSeconds": 75, "content": {"headline": "Question 3: Voltage Divider", "body_md": "In a voltage divider circuit, V_in = 12V, R1 = 2kΩ, and R2 = 4kΩ. What is V_out measured across R2?", "visual": {"type": "local_asset", "value": "assets/images/course_specific/voltage_divider_q.png"}, "interactive_element": {"type": "multiple_choice_text", "question_text": "Calculate V_out.", "options": [{"id": "opt1", "text": "4V"}, {"id": "opt2", "text": "6V"}, {"id": "opt3", "text": "8V"}, {"id": "opt4", "text": "12V"}], "correct_option_id": "opt3", "feedback_correct": "Correct! V_out = 12V * (4kΩ / (2kΩ + 4kΩ)) = 12V * (4/6) = 12V * (2/3) = 8V.", "feedback_incorrect": "Use the voltage divider formula: V_out = V_in * (R2 / (R1 + R2))."}}}, {"id": "test-dc-circuit-solver-q4", "type": "question_screen", "order": 4, "estimatedTimeSeconds": 90, "content": {"headline": "Question 4: KVL Application", "body_md": "In a closed loop, a 9V battery is connected to three resistors. The voltage drops across two of the resistors are 3V and 4V respectively. What is the voltage drop across the third resistor?", "visual": {"type": "static_text", "value": "ΣV_rises = ΣV_drops"}, "interactive_element": {"type": "multiple_choice_text", "question_text": "Find the third voltage drop.", "options": [{"id": "opt1", "text": "16V"}, {"id": "opt2", "text": "9V"}, {"id": "opt3", "text": "5V"}, {"id": "opt4", "text": "2V"}], "correct_option_id": "opt4", "feedback_correct": "Correct! By KVL, 9V = 3V + 4V + V3. So V3 = 9V - 7V = 2V.", "feedback_incorrect": "According to KVL, the sum of voltage drops in a loop must equal the sum of voltage rises."}}}, {"id": "test-dc-circuit-solver-q5", "type": "question_screen", "order": 5, "estimatedTimeSeconds": 75, "content": {"headline": "Question 5: KCL Application", "body_md": "At a circuit node, current I1 = 6A flows in. Currents I2 = 2A and I3 flow out. What is the value of I3?", "visual": {"type": "local_asset", "value": "assets/images/course_specific/kcl_node_q.png"}, "interactive_element": {"type": "multiple_choice_text", "question_text": "Determine I3.", "options": [{"id": "opt1", "text": "8A"}, {"id": "opt2", "text": "6A"}, {"id": "opt3", "text": "4A"}, {"id": "opt4", "text": "2A"}], "correct_option_id": "opt3", "feedback_correct": "Correct! By KCL, ΣI_in = ΣI_out. So, 6A = 2A + I3. Thus, I3 = 4A.", "feedback_incorrect": "According to KCL, the total current entering a node must equal the total current leaving it."}}}]}]}