# Course Data and Interactive Content Structure

This directory (`assets/data/`) houses the JSON-based course content for the Resonance app. Our goal is to create a highly engaging, Brilliant.org-style learning experience through rich, interactive content.

## Current Structure & Philosophy

We are moving towards a model where lessons are composed of `contentBlocks` that can include various `interactive_element` types. This allows for dynamic and engaging learning modules. The `lib/models/course_models.dart` file defines the Dart classes that parse and represent this JSON structure.

**Current Focus: Enhancing Interactivity**

The primary initiative is to make learning more active and fun by developing a suite of custom interactive widgets. These widgets are designed to be embedded directly within lesson screens and module tests.

**Key Goals:**
*   **Active Learning:** Encourage users to actively participate rather than passively consume information.
*   **Conceptual Understanding:** Design widgets that help users build intuition and grasp concepts more deeply.
*   **Engaging Experiences:** Make learning enjoyable and motivating, similar to platforms like Brilliant.org.

## Example Implementation: "The Art of Logical Deduction"

The module `assets/data/courses/categories/maths/mathematical_thinking/modules/art-of-logical-deduction.json` serves as the pioneering example of this new interactive content structure. It showcases several new custom interactive widget types, including:

*   `truth_table_builder_conditional`
*   `interactive_conditional_scenario_evaluator`
*   `guided_proof_steps`
*   `argument_chain_builder_game`
*   `fallacy_identification_quick_poll`
*   `fallacy_identifier_game_multiple_choice`
*   `interactive_base_ten_blocks` (clarified as interactive, not just visual)
*   `number_line_comparison_task` (new interactive for comparing numbers on a line)
*   `draw_line_of_symmetry_game` (new interactive for geometry)
*   `multiple_choice_image_from_visual` (new interactive to select options presented by a visual element)

And new test question types like:
*   `image_sequence_prediction`
*   `numerical_pattern_text_input`
*   `conditional_truth_evaluation`
*   `proof_by_contradiction_step_choice`
*   `argument_completion_modus_ponens`
*   `fallacy_identification_scenario`

Refer to this file and `lib/models/course_models.dart` to understand how these interactive elements are defined and structured.

## Guidance for Future Content Creation (for Humans & AIs)

When creating new lessons, modules, or courses, please adhere to the following:

1.  **Prioritize Interactivity:** Whenever possible, think about how a concept can be taught or reinforced using an interactive element rather than static text or images alone.
2.  **Leverage Existing Widgets:** Before proposing a new widget type, check `lib/models/course_models.dart` (specifically the `InteractiveElement.fromJson` factory) and the existing JSON files to see if a suitable widget already exists.
3.  **Design for Engagement:** Aim for widgets that are intuitive, provide clear feedback, and make the learning process enjoyable.
4.  **Follow JSON Structure:** Ensure your new content conforms to the established JSON schema as exemplified in `art-of-logical-deduction.json` and parsed by `lib/models/course_models.dart`.
5.  **Modular Design:** If a new, complex interactive widget is needed, define its parameters clearly in the JSON and ensure corresponding Dart models are created or proposed. The goal is to build a reusable library of interactive components.

## File Organization

Course content is organized by category and then by course, with modules for each course typically residing in a `modules` subdirectory. For example:
`assets/data/courses/categories/[category_name]/[course_name]/modules/[module_name].json`

## Future Plans (Backend Integration)

While currently stored as local JSON files, the long-term plan is to fetch this structured course data from a backend API. The data models defined in `lib/models/course_models.dart` are designed to be compatible with this future transition. The focus on a well-defined JSON structure for interactivity will facilitate this move.

## Recent Updates & Progress (as of 2025-05-18)

This section tracks recent enhancements to course interactivity and module completion status, primarily for AI-assisted development.

**Objective:** Systematically review and enhance modules within the "Mathematical Thinking" course category by ensuring all lessons have well-named, functional, and engaging interactive elements.

**Methodology:**
1.  **Read Module Content:** Sequentially process each module's JSON file.
2.  **Analyze `lib/models/course_models.dart`:** Understand available interactive and visual elements.
3.  **Identify Discrepancies/Opportunities:**
    *   Check if interactive elements used in JSON are defined in `course_models.dart`.
    *   Correct misplacements (e.g., an interactive element used in a `visual` field).
    *   Identify lessons lacking interactivity or where existing interactives could be improved.
4.  **Define New Interactive Elements:** If a required interactive type doesn't exist, define its class and add it to the `InteractiveElement.fromJson` factory in `lib/models/course_models.dart`.
5.  **Update JSON:** Modify the module's JSON file to correctly use existing or newly defined interactive elements.
6.  **Update README:** Document new interactives and progress.

**Modules Processed in "Mathematical Thinking" Category:**

*   **Status:** All modules in this category have been reviewed and updated as of 2025-05-18.
*   **`art-of-logical-deduction.json`**: (User-fixed prior to this session) Serves as the primary example for rich interactivity.
*   **`exploring-the-world-of-numbers.json`**:
    *   **Action:** Corrected usage of `interactive_base_ten_blocks` (moved from `visual` to `interactive_element`).
    *   **Action:** Replaced a placeholder visual (`interactive_number_line_comparison`) and a basic button with a new `InteractiveElement` called `number_line_comparison_task`.
    *   **New Element Defined:** `NumberLineComparisonTaskElement` (in `course_models.dart`).
*   **`number-sense-and-intuition.json`**:
    *   **Action:** Reviewed. Found to be in good shape with existing interactives like `sieve_mini_game`, `multiple_choice_text`, `text_input`, and `mental_math_challenge_sequence`. No immediate changes were needed for interactive element definitions or JSON structure for this module.
*   **`visualizing-geometry.json`**:
    *   **Action:** Identified that `draw_line_of_symmetry_game` was used in JSON but not defined in models.
    *   **New Element Defined:** `DrawLineOfSymmetryGameElement` (in `course_models.dart`). JSON now aligns with model.
    *   **Note:** This module also uses several custom `VisualElement` types (e.g., `interactive_triangle_angle_sum`, `interactive_pythagorean_theorem_viz`) that are intended to be dynamic. Their Flutter widget implementations are key to their interactivity.
*   **`power-of-patterns-and-relationships.json`**:
    *   **Action:** Identified that `multiple_choice_image_from_visual` was used in JSON but not defined in models. This is intended for scenarios where a visual element (like `interactive_graph_selection_game`) presents image-based choices.
    *   **New Element Defined:** `MultipleChoiceImageFromVisualElement` (in `course_models.dart`).
    *   **Action:** Updated the module JSON (screen `ppp-q2-relationship-graph`) to correctly use this new element and specify the `correct_option_id_from_visual`.

**Newly Defined/Clarified Interactive Elements in `lib/models/course_models.dart`:**
*   `NumberLineComparisonTaskElement`: For interactive tasks involving comparing numbers on a number line.
*   `DrawLineOfSymmetryGameElement`: For interactive tasks where users draw lines of symmetry on shapes.
*   `MultipleChoiceImageFromVisualElement`: For scenarios where choices are presented visually by another element, and this element handles the selection logic.
*   `InteractiveBaseTenBlocksElement`: Usage clarified and corrected to be an `interactive_element`.

**Newly Created Course (as of 2025-05-18): "Equations and Algebra"**

*   **Objective:** Create a new foundational algebra course with highly interactive lessons and module tests.
*   **Status (as of 2025-05-18):**
    *   Initial scaffolding complete (all module JSON files created with basic lesson structures and placeholder interactive elements).
    *   Started populating the **"The Language of Variables"** module (`language-of-variables.json`) with detailed content:
        *   Lesson 1 (`lov-l1-what-is-a-variable`): JSON updated for a richer `variable_value_explorer` interactive element. The corresponding Dart model (`VariableValueExplorerElement` and `VariableScenario`) in `lib/models/course_models.dart` was successfully updated.
        *   Lesson 2 (`lov-l2-building-expressions`): JSON updated for a richer `interactive_expression_builder` interactive element.
        *   **Roadblock:** Encountered repeated errors while attempting to update the `InteractiveExpressionBuilderElement` Dart model in `lib/models/course_models.dart`. The issues stemmed from auto-formatting changes by the editor causing `replace_in_file` SEARCH blocks to mismatch, and accidental re-declaration of the class. This is the point where development was paused. The file `lib/models/course_models.dart` was reverted by the tool after the last failed attempt and may not contain the intended `InteractiveExpressionBuilderElement` changes.
    *   Lessons 3, 4, and 5 of "The Language of Variables" module have their JSON updated with more detailed text and conceptual interactive elements, but their corresponding Dart models (`InteractiveExpressionEvaluatorElement`, `InteractiveLikeTermsCombinerElement`, `InteractiveBalanceScaleAnalogyElement`) have **not** yet been fully updated/corrected in `lib/models/course_models.dart` following the pattern of the first two lessons.
*   **Course File:** `assets/data/courses/categories/maths/equations-and-algebra/course.json`
*   **Modules Created (scaffolded):**
    *   `language-of-variables.json` (partially populated, see status above)
    *   `solving-one-step-equations.json`
    *   `tackling-two-step-equations.json`
    *   `introduction-to-inequalities.json`
    *   `exploring-algebraic-relationships.json`

**Next Steps for AI / Future Development:**
*   **Current Focus: "Equations and Algebra" Course**
    *   **User Expectation:** "Making a course" means fully populating every lesson with rich textual content AND deeply interactive elements (similar to "Mathematical Thinking"), including defining new widget types in JSON, updating/creating their Dart models in `lib/models/course_models.dart`, and (ideally) assisting with or generating the Flutter widget code for these new interactive elements.
    *   **Next Steps (for next AI):**
        1.  **Resolve `lib/models/course_models.dart` issues:** Carefully update or correctly define the `InteractiveExpressionBuilderElement` (for lesson `lov-l2`) ensuring no conflicts and accounting for auto-formatting.
        2.  Continue populating "The Language of Variables" module:
            *   For `lov-l3-evaluating-expressions`: Finalize the JSON for `interactive_expression_evaluator` and correctly update/create its Dart model (`InteractiveExpressionEvaluatorElement` and helper classes) in `lib/models/course_models.dart`.
            *   For `lov-l4-like-terms-unite`: Finalize the JSON for `interactive_like_terms_combiner` and correctly update/create its Dart model (`InteractiveLikeTermsCombinerElement` and helper classes).
            *   For `lov-l5-balance-analogy`: Finalize the JSON for `interactive_balance_scale_analogy` and correctly update/create its Dart model (`InteractiveBalanceScaleAnalogyElement` and helper classes).
        3.  Proceed to populate the remaining modules of the "Equations and Algebra" course (`solving-one-step-equations`, `tackling-two-step-equations`, etc.) following the same detailed approach: define rich interactive JSON, update/create Dart models, and prepare for widget implementation.
    *   Systematically review each lesson within the modules of the "Equations and Algebra" course.
    *   Replace placeholder `interactive_element` and `visual_element` types with actual, well-designed interactive widgets.
    *   Define any new `InteractiveElement` or `VisualElement` classes in `lib/models/course_models.dart` if required.
    *   Ensure corresponding Flutter widgets are implemented for all new interactive elements to provide the described functionality.
    *   Update this README with progress on each module within "Equations and Algebra" as interactive elements are finalized.

**Progress on "Equations and Algebra" (as of 2025-05-18, continued):**

*   **"The Language of Variables" (`language-of-variables.json`):**
    *   **Status:** All Dart models (`VariableValueExplorerElement`, `InteractiveExpressionBuilderElement`, `InteractiveExpressionEvaluatorElement`, `InteractiveLikeTermsCombinerElement`, `InteractiveBalanceScaleAnalogyElement`) reviewed/corrected. JSON for `lov-l5-balance-analogy` fixed to use `interactive_element` correctly. This module's interactive elements and Dart models are now considered complete.
*   **"Solving One-Step Equations" (`solving-one-step-equations.json`):**
    *   **Status:** All lessons populated with interactive elements.
    *   `sose-l1-undoing-addition-subtraction`: Replaced placeholder with `interactive_balance_scale_analogy`.
    *   `sose-l2-power-of-division`: Replaced placeholder with `guided_proof_steps`.
    *   `sose-l3-multiplying-to-solve`: Replaced placeholder with `text_input`.
    *   `sose-l4-visualizing-solutions`: Replaced placeholder with new `interactive_number_line_solution_plotter`.
    *   **New Element Defined:** `InteractiveNumberLineSolutionPlotterElement` in `lib/models/course_models.dart`.
    *   `sose-l5-checking-your-answers`: Replaced placeholder with new `interactive_solution_checker`.
        *   **New Element Defined:** `InteractiveSolutionCheckerElement` in `lib/models/course_models.dart`.
    *   This module's interactive elements and Dart models are now considered complete.
*   **"Tackling Two-Step Equations" (`tackling-two-step-equations.json`):**
    *   **Status:** All lessons populated with interactive elements.
    *   `ttse-l1-order-of-operations-reverse`: Replaced placeholder with `guided_proof_steps`.
    *   `ttse-l2-combining-like-terms-before-solving`: Replaced placeholder with new `interactive_simplify_then_solve`.
        *   **New Element Defined:** `InteractiveSimplifyThenSolveElement` in `lib/models/course_models.dart`.
    *   `ttse-l3-visualizing-the-steps`: Replaced placeholder with `guided_proof_steps`.
    *   `ttse-l4-equations-with-fractions`: Replaced placeholder with `guided_proof_steps`.
    *   `ttse-l5-real-world-equations`: Replaced placeholder with new `interactive_word_problem_solver`.
        *   **New Element Defined:** `InteractiveWordProblemSolverElement` in `lib/models/course_models.dart`.
    *   This module's interactive elements and Dart models are now considered complete.
*   **"Introduction to Inequalities" (`introduction-to-inequalities.json`):**
    *   **Status:** All lessons populated with interactive elements.
    *   `iti-l1-language-of-inequality`: Replaced placeholder with new `interactive_inequality_symbol_matcher`.
        *   **New Element Defined:** `InteractiveInequalitySymbolMatcherElement` (and `InequalityPair` helper) in `lib/models/course_models.dart`.
    *   `iti-l2-visualizing-inequalities-number-line`: Replaced placeholder with `interactive_number_line_solution_plotter`.
    *   `iti-l3-solving-one-step-inequalities`: Replaced placeholder with `text_input`.
    *   `iti-l4-solving-two-step-inequalities`: Replaced placeholder with `guided_proof_steps`.
    *   `iti-l5-graphing-inequalities`: Replaced placeholder with `interactive_number_line_solution_plotter`.
    *   This module's interactive elements and Dart models are now considered complete.
*   **"Exploring Algebraic Relationships" (`exploring-algebraic-relationships.json`):**
    *   **Status:** All lessons populated with interactive elements.
    *   `ear-l1-representing-relationships`: Replaced placeholder with new `interactive_relationship_to_equation`.
        *   **New Element Defined:** `InteractiveRelationshipToEquationElement` in `lib/models/course_models.dart`.
    *   `ear-l2-intro-graphing-linear-equations`: Replaced placeholder with new `interactive_linear_equation_grapher`.
        *   **New Element Defined:** `InteractiveLinearEquationGrapherElement` (and `PointToGraph` helper) in `lib/models/course_models.dart`.
    *   `ear-l3-understanding-slope`: Replaced placeholder with new `interactive_slope_explorer`.
        *   **New Element Defined:** `InteractiveSlopeExplorerElement` in `lib/models/course_models.dart`.
    *   `ear-l4-equation-of-a-line`: Replaced placeholder with new `interactive_graph_to_equation_matcher`.
        *   **New Element Defined:** `InteractiveGraphToEquationMatcherElement` in `lib/models/course_models.dart`. (Syntax error in default regex fixed).
    *   `ear-l5-solving-simple-systems-visually`: Replaced placeholder with new `interactive_system_of_equations_grapher`.
        *   **New Element Defined:** `InteractiveSystemOfEquationsGrapherElement` in `lib/models/course_models.dart`.
    *   This module's interactive elements and Dart models are now considered complete.

*   **All modules in "Equations and Algebra" course are now populated with interactive elements and their Dart models.**

    *   **Important:** After adding new course JSON files, a **full cold restart** of the Flutter application is often necessary for the app to recognize and load the new course. Hot reload/restart may not be sufficient.
*   **General:**
    *   Focus on creating "perfected" interactives: intuitive, engaging, with clear feedback, and appropriate difficulty.
    *   Refer to this README and `AI_CONTENT_GENERATION_GUIDE.md` for ongoing guidance.
