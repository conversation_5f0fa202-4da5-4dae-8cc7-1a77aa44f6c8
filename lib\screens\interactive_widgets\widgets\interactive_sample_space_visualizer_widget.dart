import 'package:flutter/material.dart';
import 'dart:math' as math;

/// A widget that helps users visualize sample spaces for different probability experiments
class InteractiveSampleSpaceVisualizerWidget extends StatefulWidget {
  /// The data for this widget
  final Map<String, dynamic> data;

  /// Optional callback when the widget state changes
  final Function(bool isCompleted)? onStateChanged;

  const InteractiveSampleSpaceVisualizerWidget({
    Key? key,
    required this.data,
    this.onStateChanged,
  }) : super(key: key);

  /// Factory constructor to create the widget from JSON data
  factory InteractiveSampleSpaceVisualizerWidget.fromData(Map<String, dynamic> data) {
    return InteractiveSampleSpaceVisualizerWidget(
      data: data,
    );
  }

  @override
  State<InteractiveSampleSpaceVisualizerWidget> createState() => _InteractiveSampleSpaceVisualizerWidgetState();
}

class _InteractiveSampleSpaceVisualizerWidgetState extends State<InteractiveSampleSpaceVisualizerWidget> {
  // Current experiment type
  String _currentExperiment = 'coin';

  // Whether the widget is completed
  bool _isCompleted = false;

  // Current example
  int _currentExampleIndex = 0;

  // Colors
  late Color _primaryColor;
  late Color _secondaryColor;
  late Color _accentColor;
  late Color _backgroundColor;
  late Color _textColor;

  // List of experiments
  late List<Map<String, dynamic>> _experiments;

  // Whether to show the example
  bool _showExample = false;

  // Whether to show explanation
  bool _showExplanation = false;

  // Coin flip parameters
  int _numCoins = 2;

  // Dice roll parameters
  int _numDice = 2;
  int _diceSides = 6;

  // Card draw parameters
  int _numCards = 1;
  bool _withReplacement = false;

  // Spinner parameters
  List<double> _spinnerSections = [0.25, 0.25, 0.25, 0.25];
  List<Color> _spinnerColors = [
    Colors.red,
    Colors.blue,
    Colors.green,
    Colors.yellow,
  ];

  // Urn parameters
  int _redBalls = 3;
  int _blueBalls = 2;
  int _greenBalls = 1;
  int _ballsToDraw = 2;
  bool _urnWithReplacement = false;

  // Event definition
  String _eventDefinition = '';

  // Selected outcomes (for highlighting favorable outcomes)
  List<int> _selectedOutcomes = [];

  // Animation controller for spinner
  double _spinnerAngle = 0.0;
  bool _isSpinning = false;

  @override
  void initState() {
    super.initState();

    // Initialize colors
    _primaryColor = _parseColor(widget.data['primary_color']) ?? Colors.blue;
    _secondaryColor = _parseColor(widget.data['secondary_color']) ?? Colors.orange;
    _accentColor = _parseColor(widget.data['accent_color']) ?? Colors.green;
    _backgroundColor = _parseColor(widget.data['background_color']) ?? Colors.white;
    _textColor = _parseColor(widget.data['text_color']) ?? Colors.black87;

    // Initialize experiments
    _experiments = widget.data['experiments'] != null
        ? List<Map<String, dynamic>>.from(widget.data['experiments'])
        : _getDefaultExperiments();
  }

  // Parse color from hex string
  Color? _parseColor(String? hexString) {
    if (hexString == null) return null;

    hexString = hexString.replaceAll('#', '');
    if (hexString.length == 6) {
      hexString = 'FF$hexString';
    }

    return Color(int.parse(hexString, radix: 16));
  }

  // Get default experiments if none provided
  List<Map<String, dynamic>> _getDefaultExperiments() {
    return [
      {
        'title': 'Coin Flips',
        'description': 'Flipping multiple coins and observing the possible outcomes.',
        'type': 'coin',
        'num_coins': 3,
        'event_definition': 'Getting exactly 2 heads',
        'explanation': 'When flipping 3 coins, the sample space consists of 8 possible outcomes: HHH, HHT, HTH, HTT, THH, THT, TTH, TTT. The event "Getting exactly 2 heads" includes the outcomes HHT, HTH, and THH. Therefore, the probability is 3/8 = 0.375 or 37.5%.',
      },
      {
        'title': 'Dice Rolls',
        'description': 'Rolling multiple dice and observing the possible outcomes.',
        'type': 'dice',
        'num_dice': 2,
        'dice_sides': 6,
        'event_definition': 'Getting a sum of 7',
        'explanation': 'When rolling 2 six-sided dice, there are 36 possible outcomes. The sum of 7 can be achieved in 6 ways: (1,6), (2,5), (3,4), (4,3), (5,2), (6,1). Therefore, the probability is 6/36 = 1/6 ≈ 0.167 or about 16.7%.',
      },
      {
        'title': 'Card Draws',
        'description': 'Drawing cards from a standard deck and observing the possible outcomes.',
        'type': 'card',
        'num_cards': 2,
        'with_replacement': false,
        'event_definition': 'Drawing two aces',
        'explanation': 'When drawing 2 cards from a standard deck without replacement, there are C(52,2) = 1,326 possible outcomes. There are 4 aces in the deck, so there are C(4,2) = 6 ways to draw 2 aces. Therefore, the probability is 6/1,326 = 1/221 ≈ 0.0045 or about 0.45%.',
      },
      {
        'title': 'Spinner',
        'description': 'Spinning a wheel divided into sections and observing where it lands.',
        'type': 'spinner',
        'spinner_sections': [0.4, 0.3, 0.2, 0.1],
        'event_definition': 'Landing on the red section',
        'explanation': 'The spinner is divided into 4 sections with probabilities 40%, 30%, 20%, and 10%. The probability of landing on the red section is 40%.',
      },
      {
        'title': 'Urn Model',
        'description': 'Drawing colored balls from an urn and observing the possible outcomes.',
        'type': 'urn',
        'red_balls': 5,
        'blue_balls': 3,
        'green_balls': 2,
        'balls_to_draw': 2,
        'with_replacement': false,
        'event_definition': 'Drawing at least one red ball',
        'explanation': 'The urn contains 5 red, 3 blue, and 2 green balls, for a total of 10 balls. When drawing 2 balls without replacement, there are C(10,2) = 45 possible outcomes. The number of ways to draw at least one red ball is the total number of outcomes minus the number of ways to draw no red balls. The number of ways to draw no red balls is C(5,2) = 10. Therefore, the probability is (45 - 10)/45 = 35/45 = 7/9 ≈ 0.778 or about 77.8%.',
      },
    ];
  }

  // Load an experiment
  void _loadExperiment(int index) {
    if (index >= 0 && index < _experiments.length) {
      final experiment = _experiments[index];

      setState(() {
        _currentExampleIndex = index;
        _currentExperiment = experiment['type'];
        _showExample = true;
        _eventDefinition = experiment['event_definition'];

        // Set parameters based on the experiment type
        switch (_currentExperiment) {
          case 'coin':
            _numCoins = experiment['num_coins'];
            break;
          case 'dice':
            _numDice = experiment['num_dice'];
            _diceSides = experiment['dice_sides'];
            break;
          case 'card':
            _numCards = experiment['num_cards'];
            _withReplacement = experiment['with_replacement'];
            break;
          case 'spinner':
            if (experiment['spinner_sections'] != null) {
              _spinnerSections = List<double>.from(experiment['spinner_sections']);
            }
            break;
          case 'urn':
            _redBalls = experiment['red_balls'];
            _blueBalls = experiment['blue_balls'];
            _greenBalls = experiment['green_balls'];
            _ballsToDraw = experiment['balls_to_draw'];
            _urnWithReplacement = experiment['with_replacement'];
            break;
        }

        // Reset selected outcomes
        _selectedOutcomes = [];
      });
    }
  }

  // Reset to default values
  void _resetToDefault() {
    setState(() {
      switch (_currentExperiment) {
        case 'coin':
          _numCoins = 2;
          break;
        case 'dice':
          _numDice = 2;
          _diceSides = 6;
          break;
        case 'card':
          _numCards = 1;
          _withReplacement = false;
          break;
        case 'spinner':
          _spinnerSections = [0.25, 0.25, 0.25, 0.25];
          break;
        case 'urn':
          _redBalls = 3;
          _blueBalls = 2;
          _greenBalls = 1;
          _ballsToDraw = 2;
          _urnWithReplacement = false;
          break;
      }

      _eventDefinition = '';
      _selectedOutcomes = [];
      _showExample = false;
      _showExplanation = false;
    });
  }

  // Toggle explanation visibility
  void _toggleExplanation() {
    setState(() {
      _showExplanation = !_showExplanation;
    });
  }

  // Change experiment type
  void _changeExperiment(String experiment) {
    setState(() {
      _currentExperiment = experiment;
      _eventDefinition = '';
      _selectedOutcomes = [];
      _showExample = false;
      _showExplanation = false;
    });
  }

  // Calculate the size of the sample space
  int _calculateSampleSpaceSize() {
    switch (_currentExperiment) {
      case 'coin':
        return math.pow(2, _numCoins).toInt();
      case 'dice':
        return math.pow(_diceSides, _numDice).toInt();
      case 'card':
        if (_withReplacement) {
          return math.pow(52, _numCards).toInt();
        } else {
          // Calculate combinations C(52, _numCards)
          if (_numCards > 52) return 0;
          return _calculateCombination(52, _numCards);
        }
      case 'spinner':
        // For a continuous spinner, the sample space is technically infinite
        // But we can represent it as the number of sections
        return _spinnerSections.length;
      case 'urn':
        final totalBalls = _redBalls + _blueBalls + _greenBalls;
        if (_urnWithReplacement) {
          // With replacement: totalBalls^ballsToDraw
          return math.pow(totalBalls, _ballsToDraw).toInt();
        } else {
          // Without replacement: C(totalBalls, ballsToDraw)
          if (_ballsToDraw > totalBalls) return 0;
          return _calculateCombination(totalBalls, _ballsToDraw);
        }
      default:
        return 0;
    }
  }

  // Calculate combination C(n, k)
  int _calculateCombination(int n, int k) {
    if (k < 0 || k > n) return 0;
    if (k == 0 || k == n) return 1;

    // Use the symmetry of binomial coefficients
    if (k > n - k) k = n - k;

    int result = 1;
    for (int i = 0; i < k; i++) {
      result = result * (n - i) ~/ (i + 1);
    }

    return result;
  }

  // Calculate the probability of the defined event
  double _calculateEventProbability() {
    if (_selectedOutcomes.isEmpty) return 0.0;

    final sampleSpaceSize = _calculateSampleSpaceSize();
    if (sampleSpaceSize <= 0) return 0.0;

    return _selectedOutcomes.length / sampleSpaceSize;
  }

  // Toggle selection of an outcome
  void _toggleOutcomeSelection(int index) {
    setState(() {
      if (_selectedOutcomes.contains(index)) {
        _selectedOutcomes.remove(index);
      } else {
        _selectedOutcomes.add(index);
      }
    });
  }

  // Spin the spinner
  void _spinSpinner() {
    if (_isSpinning) return;

    setState(() {
      _isSpinning = true;
      // Generate a random angle between 2π and 6π (1-3 full rotations)
      final randomAngle = 2 * math.pi + math.Random().nextDouble() * 4 * math.pi;
      _spinnerAngle = _spinnerAngle + randomAngle;
    });

    // Simulate spinning animation
    Future.delayed(const Duration(milliseconds: 2000), () {
      setState(() {
        _isSpinning = false;
      });
    });
  }

  // Mark the widget as completed
  void _markAsCompleted() {
    if (!_isCompleted) {
      setState(() {
        _isCompleted = true;
      });

      // Notify parent of state change
      widget.onStateChanged?.call(true);
    }
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: _backgroundColor,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withOpacity(0.2),
            spreadRadius: 1,
            blurRadius: 3,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Title
          Text(
            widget.data['title'] ?? 'Sample Space Visualizer',
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
              color: _textColor,
            ),
          ),

          const SizedBox(height: 8),

          // Description
          if (widget.data['description'] != null)
            Text(
              widget.data['description'],
              style: TextStyle(
                fontSize: 14,
                color: _textColor.withOpacity(0.8),
              ),
            ),

          const SizedBox(height: 16),

          // Visualizer and controls
          _buildVisualizerSection(),

          // Example section
          if (_showExample)
            _buildExampleSection(),

          // Widget name tag
          if (widget.data['showNameTag'] ?? true)
            Padding(
              padding: const EdgeInsets.only(top: 8),
              child: Text(
                'InteractiveSampleSpaceVisualizer',
                style: TextStyle(
                  color: Colors.grey[600],
                  fontSize: 12,
                  fontStyle: FontStyle.italic,
                ),
              ),
            ),
        ],
      ),
    );
  }
  // Build the visualizer section
  Widget _buildVisualizerSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Sample Space Visualizer',
          style: TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.bold,
            color: _textColor,
          ),
        ),
        const SizedBox(height: 12),
        // Add your visualizer implementation here
        Container(
          height: 200,
          decoration: BoxDecoration(
            border: Border.all(color: _primaryColor),
            borderRadius: BorderRadius.circular(8),
          ),
          child: Center(
            child: Text(
              'Sample Space Visualization',
              style: TextStyle(color: _textColor),
            ),
          ),
        ),
        const SizedBox(height: 16),
        // Controls
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceEvenly,
          children: [
            ElevatedButton(
              onPressed: () {
                // Add control functionality
              },
              child: const Text('Generate'),
              style: ElevatedButton.styleFrom(
                backgroundColor: _primaryColor,
              ),
            ),
            ElevatedButton(
              onPressed: () {
                // Add reset functionality
              },
              child: const Text('Reset'),
              style: ElevatedButton.styleFrom(
                backgroundColor: _secondaryColor,
              ),
            ),
          ],
        ),
      ],
    );
  }

  // Build the example section
  Widget _buildExampleSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const SizedBox(height: 16),
        Text(
          'Example',
          style: TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.bold,
            color: _textColor,
          ),
        ),
        const SizedBox(height: 8),
        Container(
          padding: const EdgeInsets.all(12),
          decoration: BoxDecoration(
            color: _backgroundColor,
            border: Border.all(color: _accentColor),
            borderRadius: BorderRadius.circular(8),
          ),
          child: Text(
            'This is an example of a sample space visualization.',
            style: TextStyle(color: _textColor),
          ),
        ),
      ],
    );
  }
}