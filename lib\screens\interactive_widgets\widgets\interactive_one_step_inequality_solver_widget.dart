import 'package:flutter/material.dart';
import 'dart:math' as math;

/// A widget that helps students solve one-step inequalities.
class InteractiveOneStepInequalitySolverWidget extends StatefulWidget {
  final Map<String, dynamic> data;
  final Function(bool)? onStateChanged;
  final Color primaryColor;
  final Color secondaryColor;
  final Color backgroundColor;
  final Color textColor;
  final Color successColor;
  final Color errorColor;

  const InteractiveOneStepInequalitySolverWidget({
    super.key,
    this.data = const {},
    this.onStateChanged,
    this.primaryColor = Colors.blue,
    this.secondaryColor = Colors.orange,
    this.backgroundColor = Colors.white,
    this.textColor = Colors.black87,
    this.successColor = Colors.green,
    this.errorColor = Colors.red,
  });

  @override
  State<InteractiveOneStepInequalitySolverWidget> createState() =>
      _InteractiveOneStepInequalitySolverWidgetState();
}

class _InteractiveOneStepInequalitySolverWidgetState
    extends State<InteractiveOneStepInequalitySolverWidget> with SingleTickerProviderStateMixin {
  // State variables
  bool _isCompleted = false;
  int _currentInequalityIndex = 0;
  List<InequalityData> _inequalities = [];
  late InequalityData _currentInequality;

  // Solution steps
  int _currentStep = 0;
  bool _showSolution = false;

  // Interactive variables
  String _userSolution = '';
  TextEditingController _solutionController = TextEditingController();
  String? _feedbackMessage;
  bool _isCorrect = false;

  // Practice mode variables
  bool _practiceMode = false;
  List<String> _userSteps = [];
  List<bool> _stepCorrectness = [];
  bool _allStepsCorrect = false;

  // Animation controller for feedback
  late AnimationController _animationController;
  late Animation<double> _animation;

  @override
  void initState() {
    super.initState();
    _initializeInequalities();
    _currentInequality = _inequalities[_currentInequalityIndex];

    // Initialize animation controller
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 300),
      vsync: this,
    );

    _animation = CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeInOut,
    );
  }

  @override
  void dispose() {
    _solutionController.dispose();
    _animationController.dispose();
    super.dispose();
  }

  void _initializeInequalities() {
    // Check if inequalities are provided in the data
    if (widget.data.containsKey('inequalities') &&
        widget.data['inequalities'] is List &&
        widget.data['inequalities'].isNotEmpty) {

      final inequalitiesData = widget.data['inequalities'] as List;
      for (final inequalityData in inequalitiesData) {
        if (inequalityData is Map<String, dynamic>) {
          final inequality = InequalityData.fromJson(inequalityData);
          _inequalities.add(inequality);
        }
      }
    }

    // If no inequalities were provided, create default ones
    if (_inequalities.isEmpty) {
      _inequalities = [
        InequalityData(
          inequality: 'x + 3 < 8',
          solution: 'x < 5',
          steps: [
            'Start with the original inequality: x + 3 < 8',
            'Subtract 3 from both sides: x + 3 - 3 < 8 - 3',
            'Simplify: x < 5',
          ],
          explanations: [
            'This is our starting inequality.',
            'To isolate the variable x, we need to subtract 3 from both sides. When we perform the same operation on both sides of an inequality, the inequality sign stays the same.',
            'After simplifying, we get x < 5, which means all values of x less than 5 satisfy the original inequality.',
          ],
          operationType: 'subtraction',
          numberLineData: {
            'boundaryValue': 5,
            'boundaryIncluded': false,
            'direction': 'left',
          },
        ),
        InequalityData(
          inequality: 'x - 4 ≤ 2',
          solution: 'x ≤ 6',
          steps: [
            'Start with the original inequality: x - 4 ≤ 2',
            'Add 4 to both sides: x - 4 + 4 ≤ 2 + 4',
            'Simplify: x ≤ 6',
          ],
          explanations: [
            'This is our starting inequality.',
            'To isolate the variable x, we need to add 4 to both sides. When we perform the same operation on both sides of an inequality, the inequality sign stays the same.',
            'After simplifying, we get x ≤ 6, which means all values of x less than or equal to 6 satisfy the original inequality.',
          ],
          operationType: 'addition',
          numberLineData: {
            'boundaryValue': 6,
            'boundaryIncluded': true,
            'direction': 'left',
          },
        ),
        InequalityData(
          inequality: '3x > 15',
          solution: 'x > 5',
          steps: [
            'Start with the original inequality: 3x > 15',
            'Divide both sides by 3: 3x ÷ 3 > 15 ÷ 3',
            'Simplify: x > 5',
          ],
          explanations: [
            'This is our starting inequality.',
            'To isolate the variable x, we need to divide both sides by 3. When we divide both sides of an inequality by a positive number, the inequality sign stays the same.',
            'After simplifying, we get x > 5, which means all values of x greater than 5 satisfy the original inequality.',
          ],
          operationType: 'division',
          numberLineData: {
            'boundaryValue': 5,
            'boundaryIncluded': false,
            'direction': 'right',
          },
        ),
        InequalityData(
          inequality: '2x ≥ 10',
          solution: 'x ≥ 5',
          steps: [
            'Start with the original inequality: 2x ≥ 10',
            'Divide both sides by 2: 2x ÷ 2 ≥ 10 ÷ 2',
            'Simplify: x ≥ 5',
          ],
          explanations: [
            'This is our starting inequality.',
            'To isolate the variable x, we need to divide both sides by 2. When we divide both sides of an inequality by a positive number, the inequality sign stays the same.',
            'After simplifying, we get x ≥ 5, which means all values of x greater than or equal to 5 satisfy the original inequality.',
          ],
          operationType: 'division',
          numberLineData: {
            'boundaryValue': 5,
            'boundaryIncluded': true,
            'direction': 'right',
          },
        ),
        InequalityData(
          inequality: '-2x < 8',
          solution: 'x > -4',
          steps: [
            'Start with the original inequality: -2x < 8',
            'Divide both sides by -2: -2x ÷ (-2) > 8 ÷ (-2)',
            'Simplify: x > -4',
          ],
          explanations: [
            'This is our starting inequality.',
            'To isolate the variable x, we need to divide both sides by -2. When we divide both sides of an inequality by a negative number, the inequality sign flips (< becomes > and vice versa).',
            'After simplifying, we get x > -4, which means all values of x greater than -4 satisfy the original inequality.',
          ],
          operationType: 'division with sign flip',
          numberLineData: {
            'boundaryValue': -4,
            'boundaryIncluded': false,
            'direction': 'right',
          },
        ),
        InequalityData(
          inequality: '-3x ≥ 12',
          solution: 'x ≤ -4',
          steps: [
            'Start with the original inequality: -3x ≥ 12',
            'Divide both sides by -3: -3x ÷ (-3) ≤ 12 ÷ (-3)',
            'Simplify: x ≤ -4',
          ],
          explanations: [
            'This is our starting inequality.',
            'To isolate the variable x, we need to divide both sides by -3. When we divide both sides of an inequality by a negative number, the inequality sign flips (≥ becomes ≤ and vice versa).',
            'After simplifying, we get x ≤ -4, which means all values of x less than or equal to -4 satisfy the original inequality.',
          ],
          operationType: 'division with sign flip',
          numberLineData: {
            'boundaryValue': -4,
            'boundaryIncluded': true,
            'direction': 'left',
          },
        ),
      ];
    }
  }

  void _checkUserSolution() {
    if (_userSolution.isEmpty) return;

    // Normalize solutions for comparison (remove spaces)
    String normalizedUserSolution = _userSolution.replaceAll(' ', '');
    String normalizedCorrectSolution = _currentInequality.solution.replaceAll(' ', '');

    // Check if the user's solution is correct
    bool isCorrect = normalizedUserSolution == normalizedCorrectSolution;

    setState(() {
      _isCorrect = isCorrect;

      if (isCorrect) {
        _feedbackMessage = 'Correct! That\'s the right solution.';

        if (_practiceMode) {
          _userSteps.add(_userSolution);
          _stepCorrectness.add(true);

          // Check if all steps are completed correctly
          if (_currentStep == _currentInequality.steps.length - 1) {
            _allStepsCorrect = true;
          } else {
            _currentStep++;
          }
        }
      } else {
        _feedbackMessage = 'Not quite. Try again or check the solution.';

        if (_practiceMode) {
          _userSteps.add(_userSolution);
          _stepCorrectness.add(false);
        }
      }

      // Clear the input field
      _userSolution = '';
      _solutionController.clear();
    });
  }

  void _toggleSolution() {
    setState(() {
      _showSolution = !_showSolution;
    });
  }

  void _togglePracticeMode() {
    setState(() {
      _practiceMode = !_practiceMode;
      _resetCurrentInequality();
    });
  }

  void _resetCurrentInequality() {
    setState(() {
      _currentStep = 0;
      _userSolution = '';
      _solutionController.clear();
      _feedbackMessage = null;
      _isCorrect = false;
      _showSolution = false;
      _userSteps = [];
      _stepCorrectness = [];
      _allStepsCorrect = false;
    });
  }

  void _nextInequality() {
    if (_currentInequalityIndex < _inequalities.length - 1) {
      setState(() {
        _currentInequalityIndex++;
        _currentInequality = _inequalities[_currentInequalityIndex];
        _resetCurrentInequality();
      });
    } else {
      // All inequalities completed
      setState(() {
        _isCompleted = true;
      });
      widget.onStateChanged?.call(true);
    }
  }

  void _previousInequality() {
    if (_currentInequalityIndex > 0) {
      setState(() {
        _currentInequalityIndex--;
        _currentInequality = _inequalities[_currentInequalityIndex];
        _resetCurrentInequality();
      });
    }
  }

  void _resetWidget() {
    setState(() {
      _currentInequalityIndex = 0;
      _currentInequality = _inequalities[_currentInequalityIndex];
      _resetCurrentInequality();
      _isCompleted = false;
    });
    widget.onStateChanged?.call(false);
  }

  @override
  Widget build(BuildContext context) {
    if (_isCompleted) {
      return _buildCompletionScreen();
    }

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Title and mode toggle
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Text(
              'One-Step Inequality Solver',
              style: TextStyle(
                fontSize: 20,
                fontWeight: FontWeight.bold,
                color: widget.textColor,
              ),
            ),
            Row(
              children: [
                Text(
                  'Practice Mode',
                  style: TextStyle(
                    fontSize: 14,
                    color: widget.textColor,
                  ),
                ),
                Switch(
                  value: _practiceMode,
                  onChanged: (value) => _togglePracticeMode(),
                  activeColor: widget.primaryColor,
                ),
              ],
            ),
          ],
        ),

        const SizedBox(height: 16),

        // Current inequality
        Container(
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            color: widget.backgroundColor,
            borderRadius: BorderRadius.circular(8),
            border: Border.all(color: widget.primaryColor),
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                'Solve for x:',
                style: TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.w500,
                  color: widget.textColor,
                ),
              ),
              const SizedBox(height: 8),
              Text(
                _currentInequality.inequality,
                style: TextStyle(
                  fontSize: 24,
                  fontWeight: FontWeight.bold,
                  color: widget.primaryColor,
                ),
              ),
            ],
          ),
        ),

        const SizedBox(height: 16),

        // Practice mode step display
        if (_practiceMode) ...[
          Text(
            'Step ${_currentStep + 1} of ${_currentInequality.steps.length}:',
            style: TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.w500,
              color: widget.textColor,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            _currentInequality.steps[_currentStep],
            style: TextStyle(
              fontSize: 16,
              color: widget.textColor,
            ),
          ),
          const SizedBox(height: 16),
        ],

        // Solution input
        TextField(
          controller: _solutionController,
          decoration: InputDecoration(
            labelText: _practiceMode
                ? 'Enter your solution for this step'
                : 'Enter your final solution',
            hintText: 'e.g., x < 5',
            border: OutlineInputBorder(
              borderRadius: BorderRadius.circular(8),
            ),
            suffixIcon: IconButton(
              icon: const Icon(Icons.check),
              onPressed: () {
                _userSolution = _solutionController.text;
                _checkUserSolution();
              },
            ),
          ),
          onChanged: (value) {
            _userSolution = value;
          },
          onSubmitted: (value) {
            _userSolution = value;
            _checkUserSolution();
          },
        ),

        const SizedBox(height: 16),

        // Feedback message
        if (_feedbackMessage != null) ...[
          AnimatedBuilder(
            animation: _animation,
            builder: (context, child) {
              return Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: _isCorrect
                      ? widget.successColor.withOpacity(0.1)
                      : widget.errorColor.withOpacity(0.1),
                  borderRadius: BorderRadius.circular(8),
                  border: Border.all(
                    color: _isCorrect ? widget.successColor : widget.errorColor,
                  ),
                ),
                child: Row(
                  children: [
                    Icon(
                      _isCorrect ? Icons.check_circle : Icons.error,
                      color: _isCorrect ? widget.successColor : widget.errorColor,
                    ),
                    const SizedBox(width: 8),
                    Expanded(
                      child: Text(
                        _feedbackMessage!,
                        style: TextStyle(
                          color: _isCorrect ? widget.successColor : widget.errorColor,
                        ),
                      ),
                    ),
                  ],
                ),
              );
            },
          ),
          const SizedBox(height: 16),
        ],

        // Solution toggle and navigation buttons
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            TextButton.icon(
              icon: Icon(_showSolution ? Icons.visibility_off : Icons.visibility),
              label: Text(_showSolution ? 'Hide Solution' : 'Show Solution'),
              onPressed: _toggleSolution,
              style: TextButton.styleFrom(
                foregroundColor: widget.secondaryColor,
              ),
            ),
            Row(
              children: [
                if (_currentInequalityIndex > 0)
                  IconButton(
                    icon: const Icon(Icons.arrow_back),
                    onPressed: _previousInequality,
                    color: widget.primaryColor,
                  ),
                IconButton(
                  icon: const Icon(Icons.arrow_forward),
                  onPressed: _isCorrect ? _nextInequality : null,
                  color: _isCorrect ? widget.primaryColor : Colors.grey,
                ),
              ],
            ),
          ],
        ),

        // Solution display
        if (_showSolution) ...[
          const SizedBox(height: 16),
          Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: widget.backgroundColor,
              borderRadius: BorderRadius.circular(8),
              border: Border.all(color: widget.secondaryColor),
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'Solution:',
                  style: TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                    color: widget.secondaryColor,
                  ),
                ),
                const SizedBox(height: 8),
                Text(
                  _currentInequality.solution,
                  style: TextStyle(
                    fontSize: 20,
                    fontWeight: FontWeight.bold,
                    color: widget.secondaryColor,
                  ),
                ),
                const SizedBox(height: 16),
                Text(
                  'Steps:',
                  style: TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                    color: widget.secondaryColor,
                  ),
                ),
                const SizedBox(height: 8),
                ...List.generate(
                  _currentInequality.steps.length,
                  (index) => Padding(
                    padding: const EdgeInsets.only(bottom: 8),
                    child: Row(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          '${index + 1}. ',
                          style: TextStyle(
                            fontWeight: FontWeight.bold,
                            color: widget.textColor,
                          ),
                        ),
                        Expanded(
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Text(
                                _currentInequality.steps[index],
                                style: TextStyle(
                                  color: widget.textColor,
                                ),
                              ),
                              if (_currentInequality.explanations.length > index)
                                Padding(
                                  padding: const EdgeInsets.only(top: 4, left: 8),
                                  child: Text(
                                    _currentInequality.explanations[index],
                                    style: TextStyle(
                                      fontSize: 12,
                                      fontStyle: FontStyle.italic,
                                      color: widget.textColor.withOpacity(0.7),
                                    ),
                                  ),
                                ),
                            ],
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
              ],
            ),
          ),
        ],
      ],
    );
  }

  Widget _buildCompletionScreen() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.check_circle,
            color: widget.successColor,
            size: 64,
          ),
          const SizedBox(height: 16),
          Text(
            'Great job!',
            style: TextStyle(
              fontSize: 24,
              fontWeight: FontWeight.bold,
              color: widget.textColor,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            'You\'ve completed all the one-step inequality problems.',
            textAlign: TextAlign.center,
            style: TextStyle(
              fontSize: 16,
              color: widget.textColor,
            ),
          ),
          const SizedBox(height: 24),
          ElevatedButton.icon(
            icon: const Icon(Icons.refresh),
            label: const Text('Start Over'),
            onPressed: _resetWidget,
            style: ElevatedButton.styleFrom(
              backgroundColor: widget.primaryColor,
              foregroundColor: Colors.white,
              padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
            ),
          ),
        ],
      ),
    );
  }
}

/// Data class for inequality problems
class InequalityData {
  final String inequality;
  final String solution;
  final List<String> steps;
  final List<String> explanations;
  final String operationType;
  final Map<String, dynamic> numberLineData;

  InequalityData({
    required this.inequality,
    required this.solution,
    required this.steps,
    required this.explanations,
    required this.operationType,
    required this.numberLineData,
  });

  factory InequalityData.fromJson(Map<String, dynamic> json) {
    return InequalityData(
      inequality: json['inequality'] ?? '',
      solution: json['solution'] ?? '',
      steps: List<String>.from(json['steps'] ?? []),
      explanations: List<String>.from(json['explanations'] ?? []),
      operationType: json['operationType'] ?? 'unknown',
      numberLineData: json['numberLineData'] ?? {},
    );
  }
}
