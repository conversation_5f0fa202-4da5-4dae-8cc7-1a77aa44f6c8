import 'package:flutter/material.dart';
import '../../../models/interactive_widget_model.dart';

class GifPlayerWidget extends StatefulWidget {
  final InteractiveWidgetModel widget;

  const GifPlayerWidget({super.key, required this.widget});

  @override
  State<GifPlayerWidget> createState() => _GifPlayerWidgetState();
}

class _GifPlayerWidgetState extends State<GifPlayerWidget> {
  bool _isPlaying = true;
  bool _isLoading = true;

  @override
  Widget build(BuildContext context) {
    final gifUrl = widget.widget.data['gifUrl'] as String;
    final caption = widget.widget.data['caption'] as String?;

    return Column(
      children: [
        Stack(
          alignment: Alignment.center,
          children: [
            // GIF Image
            Container(
              height: 200,
              width: double.infinity,
              decoration: BoxDecoration(
                color: Colors.grey[200],
                borderRadius: BorderRadius.circular(8),
              ),
              child: ClipRRect(
                borderRadius: BorderRadius.circular(8),
                child: Stack(
                  children: [
                    // The GIF image
                    Opacity(
                      opacity: _isPlaying ? 1.0 : 0.7,
                      child: Image.network(
                        gifUrl,
                        fit: BoxFit.cover,
                        width: double.infinity,
                        height: 200,
                        loadingBuilder: (context, child, loadingProgress) {
                          if (loadingProgress == null) {
                            _isLoading = false;
                            return child;
                          }
                          return Center(
                            child: CircularProgressIndicator(
                              value: loadingProgress.expectedTotalBytes != null
                                  ? loadingProgress.cumulativeBytesLoaded /
                                      loadingProgress.expectedTotalBytes!
                                  : null,
                            ),
                          );
                        },
                        errorBuilder: (context, error, stackTrace) {
                          return Center(
                            child: Column(
                              mainAxisAlignment: MainAxisAlignment.center,
                              children: [
                                const Icon(Icons.error, color: Colors.red),
                                const SizedBox(height: 8),
                                Text(
                                  'Error loading GIF',
                                  style: TextStyle(color: Colors.red[700]),
                                ),
                              ],
                            ),
                          );
                        },
                      ),
                    ),
                    
                    // Loading indicator
                    if (_isLoading)
                      const Center(
                        child: CircularProgressIndicator(),
                      ),
                  ],
                ),
              ),
            ),
            
            // Play/Pause button overlay
            if (!_isLoading)
              IconButton(
                icon: Icon(
                  _isPlaying ? Icons.pause_circle : Icons.play_circle,
                  size: 50,
                  color: Colors.white.withOpacity(0.8),
                ),
                onPressed: () {
                  setState(() {
                    _isPlaying = !_isPlaying;
                  });
                },
              ),
          ],
        ),
        
        // Caption
        if (caption != null)
          Padding(
            padding: const EdgeInsets.only(top: 8.0),
            child: Text(
              caption,
              style: TextStyle(
                fontSize: 14,
                fontStyle: FontStyle.italic,
                color: Colors.grey[700],
              ),
              textAlign: TextAlign.center,
            ),
          ),
          
        // Controls
        Padding(
          padding: const EdgeInsets.only(top: 8.0),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              IconButton(
                icon: const Icon(Icons.replay),
                onPressed: () {
                  setState(() {
                    // This is a trick to force the GIF to restart
                    // We set _isPlaying to false and then back to true
                    _isPlaying = false;
                    Future.delayed(const Duration(milliseconds: 50), () {
                      if (mounted) {
                        setState(() {
                          _isPlaying = true;
                        });
                      }
                    });
                  });
                },
                tooltip: 'Restart',
              ),
              IconButton(
                icon: Icon(_isPlaying ? Icons.pause : Icons.play_arrow),
                onPressed: () {
                  setState(() {
                    _isPlaying = !_isPlaying;
                  });
                },
                tooltip: _isPlaying ? 'Pause' : 'Play',
              ),
            ],
          ),
        ),
      ],
    );
  }
}
