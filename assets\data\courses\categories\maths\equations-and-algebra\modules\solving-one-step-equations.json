{"id": "solving-one-step-equations", "title": "Solving One-Step Equations", "description": "Master the fundamental techniques for isolating variables in simple equations.", "order": 2, "lessons": [{"id": "sose-l1-undoing-addition-subtraction", "title": "Solving by Undoing: Addition & Subtraction", "description": "Master solving one-step equations by using inverse operations (addition and subtraction) to isolate the variable, visualized with the balance scale.", "order": 1, "type": "interactive_lesson", "estimatedTimeMinutes": 9, "contentBlocks": [{"id": "sose-l1-s1", "type": "lesson_screen", "order": 1, "estimatedTimeSeconds": 45, "content": {"headline": "Isolating the Variable", "body_md": "Welcome to solving equations! Remember our balance scale? An equation tells us two expressions are equal. To **solve an equation**, our mission is to find the value of the unknown variable that makes this statement true. The main strategy is to **isolate the variable** – get it all by itself on one side of the equals sign.\n\nHow do we do this? We use **inverse operations**!", "visual": {"type": "giphy_search", "value": "balance scale puzzle"}, "hook": "Think of it as a detective mission to find the value of 'x'!", "interactive_element": {"type": "button", "text": "Tell me more about these 'inverse operations'!", "action": "next_screen"}}}, {"id": "sose-l1-s2", "type": "lesson_screen", "order": 2, "estimatedTimeSeconds": 70, "content": {"headline": "Inverse Ops: Addition & Subtraction", "body_md": "Inverse operations 'undo' each other:\n\n- The inverse operation of **addition is subtraction**.\n- The inverse operation of **subtraction is addition**.\n\nLet's see `x + 3 = 7`.\nTo isolate 'x', we 'undo' the `+ 3` by subtracting 3 from *both sides*:\n\n`x + 3 - 3 = 7 - 3`\n`x + 0 = 4`\n`x = 4`\n\nSolution: `x = 4`. Check: `4 + 3 = 7`. True!", "visual": {"type": "static_text", "value": "x + 3 = 7  =>  x = 4"}, "hook": "It's like having a mathematical undo button!", "interactive_element": {"type": "button", "text": "Let's see this on the balance scale!", "action": "next_screen"}}}, {"id": "sose-l1-s3", "type": "lesson_screen", "order": 3, "estimatedTimeSeconds": 120, "content": {"headline": "Balance Scale: y + 5 = 12", "body_md": "The scale shows `y + 5 = 12`. To find 'y', we need to isolate it. What operation should you perform on *both sides* to get 'y' by itself on the left? How many unit blocks would 'y' be equal to?", "visual": {"type": "static_text", "value": "Interactive: Balance Scale for y + 5 = 12"}, "interactive_element": {"type": "interactive_balance_scale_analogy", "left_side_setup": {"variable_blocks": [{"name": "y", "count": 1, "label": "Box 'y'"}], "unit_blocks": 5, "unit_block_label": "Unit"}, "right_side_setup": {"unit_blocks": 12, "unit_block_label": "Unit"}, "variable_name": "y", "equation_display": "y + 5 = 12", "prompt": "Adjust 'y' to balance the scale, or think: what inverse operation isolates 'y'?", "target_x_value_for_balance": 7, "feedback_on_balance": "Correct! When y = 7, (7 + 5 = 12). You isolated 'y' by subtracting 5 from both sides: `y + 5 - 5 = 12 - 5`, so `y = 7`.", "feedback_on_unbalance_template": "Not quite balanced with y = {current_x_value}. Left: {left_total}, Right: 12. Remember to perform the *same inverse operation* on both sides.", "action_button_text": "How does this work for subtraction?"}, "hook": "Time to become a master of balance!"}}, {"id": "sose-l1-s4", "type": "lesson_screen", "order": 4, "estimatedTimeSeconds": 70, "content": {"headline": "Undoing Subtraction", "body_md": "Now, what if the equation involves subtraction, like `a - 4 = 10`?\nTo isolate 'a', we 'undo' the `- 4`. The inverse operation is adding 4. So, we add 4 to *both sides*:\n\n`a - 4 + 4 = 10 + 4`\n`a + 0 = 14`\n`a = 14`\n\nThe solution is `a = 14`. Check: `14 - 4 = 10`. Correct!\n\nThe key is always applying that inverse operation to **both sides** of the equation to maintain the balance.", "visual": {"type": "unsplash_search", "value": "plus minus signs"}, "hook": "You're now equipped to solve a whole new set of puzzles!", "interactive_element": {"type": "button", "text": "Ready for multiplication and division!", "action": "next_lesson"}}}]}, {"id": "sose-l2-power-of-division", "title": "Solving with Division: Undoing Multiplication", "description": "Learn to isolate variables in equations where the variable is multiplied by a number, using division as the inverse operation.", "order": 2, "type": "interactive_lesson", "estimatedTimeMinutes": 8, "contentBlocks": [{"id": "sose-l2-s1", "type": "lesson_screen", "order": 1, "estimatedTimeSeconds": 60, "content": {"headline": "Undoing Multiplication", "body_md": "Let's look at equations involving multiplication, like `3m = 15`.\nHere, 'm' is multiplied by 3. To isolate 'm', we 'undo' this multiplication. The inverse operation of **multiplication is division**.\n\nSo, we divide *both sides* by 3:\n`3m / 3 = 15 / 3`\n`m = 5`\n\nCheck: `3 * 5 = 15`. True!", "visual": {"type": "static_text", "value": "3m = 15  =>  m = 5"}, "hook": "Time to unleash the power of division!", "interactive_element": {"type": "button", "text": "Show me how, step-by-step!", "action": "next_screen"}}}, {"id": "sose-l2-s2", "type": "lesson_screen", "order": 2, "estimatedTimeSeconds": 180, "content": {"headline": "Guided: Solve 4z = 20", "body_md": "Let's walk through solving `4z = 20`. Our goal is to find 'z'.", "visual": {"type": "static_text", "value": "Interactive: Guided Proof for 4z = 20"}, "interactive_element": {"type": "guided_proof_steps", "proof_title": "Step-by-Step: Solving 4z = 20 using Division", "introduction_text": "The equation is `4z = 20`. 'z' is multiplied by 4.", "steps": [{"id": "step1_div_identify", "prompt_text": "What is the inverse operation we need to use on both sides to isolate 'z'?", "options": [{"id": "opt_add", "text": "Addition", "is_correct": false, "feedback": "Not quite. We need to 'undo' multiplication."}, {"id": "opt_sub", "text": "Subtraction", "is_correct": false, "feedback": "Not quite. We need to 'undo' multiplication."}, {"id": "opt_mul", "text": "Multiplication", "is_correct": false, "feedback": "That's what we're trying to undo!"}, {"id": "opt_div", "text": "Division", "is_correct": true, "feedback": "Correct! Division is the inverse of multiplication."}]}, {"id": "step2_div_apply", "prompt_text": "Okay, we divide both sides by 4. Show the equation after this step:", "user_input_details": {"type": "text_equation_input", "placeholder": "e.g., 4z/4 = 20/4", "correct_answer_regex": "^\\s*4z\\s*/\\s*4\\s*=\\s*20\\s*/\\s*4\\s*$"}, "depends_on_correct": "step1_div_identify"}, {"id": "step3_div_simplify_left", "prompt_text": "Simplify the left side: `4z / 4`. Result?", "user_input_details": {"type": "text_variable_input", "placeholder": "e.g., z", "correct_answer_regex": "^\\s*z\\s*$"}, "depends_on_correct": "step2_div_apply"}, {"id": "step4_div_simplify_right", "prompt_text": "Simplify the right side: `20 / 4`. Result?", "user_input_details": {"type": "number_input", "placeholder": "e.g., 5", "correct_answer_value": 5}, "depends_on_correct": "step3_div_simplify_left"}, {"id": "step5_div_solution", "prompt_text": "So, the solution is:", "user_input_details": {"type": "text_equation_input", "placeholder": "e.g., z = 5", "correct_answer_regex": "^\\s*z\\s*=\\s*5\\s*$"}, "depends_on_correct": "step4_div_simplify_right"}], "conclusion_text_on_complete": "Great job! You've solved `4z = 20`. Always perform the same operation on both sides!", "action_button_text": "What about undoing division?"}, "hook": "Follow along and become a pro equation solver!"}}]}, {"id": "sose-l3-multiplying-to-solve", "title": "Solving with Multiplication: Undoing Division", "description": "Learn how to use multiplication as the inverse operation to solve equations where the variable is divided by a number.", "order": 3, "type": "interactive_lesson", "estimatedTimeMinutes": 7, "contentBlocks": [{"id": "sose-l3-s1", "type": "lesson_screen", "order": 1, "estimatedTimeSeconds": 60, "content": {"headline": "Undoing Division", "body_md": "What if our variable is being *divided* by a number? Consider `k / 2 = 8`.\nTo isolate 'k', we 'undo' division. The inverse of **division is multiplication**.\n\nMultiply *both sides* by 2:\n`(k / 2) * 2 = 8 * 2`\n`k = 16`\n\nCheck: `16 / 2 = 8`. True!", "visual": {"type": "giphy_search", "value": "pizza slice math"}, "hook": "Now for the flip side: undoing division with multiplication!", "interactive_element": {"type": "button", "text": "Let me try one!", "action": "next_screen"}}}, {"id": "sose-l3-s2", "type": "lesson_screen", "order": 2, "estimatedTimeSeconds": 90, "content": {"headline": "Solve p / 6 = 3", "body_md": "Consider the equation: `p / 6 = 3`. \nTo solve for 'p', we need to isolate it by undoing the division by 6. What number should you multiply *both sides* of the equation by?", "visual": {"type": "giphy_search", "value": "thinking math equation"}, "hook": "Put your multiplication thinking cap on!", "interactive_element": {"type": "text_input", "question_text": "What number should you multiply *both sides* by?", "placeholder": "Enter multiplier", "correct_answer_regex": "^\\s*6\\s*$", "feedback_correct": "Exactly! Multiply by 6. So, (p/6)*6 = 3*6, which means p = 18.", "feedback_incorrect": "To undo division by 6, you need to multiply by 6.", "action_button_text": "How do we visualize these solutions?"}}}]}, {"id": "sose-l4-visualizing-solutions", "title": "Visualizing Solutions on a Number Line", "description": "Understand what a solution to an equation means by representing it as a specific point on the number line.", "order": 4, "type": "interactive_lesson", "estimatedTimeMinutes": 7, "contentBlocks": [{"id": "sose-l4-s1", "type": "lesson_screen", "order": 1, "estimatedTimeSeconds": 60, "content": {"headline": "Solutions on a Number Line", "body_md": "What does a solution like `x = 3` actually *mean*? One great way to understand it is by visualizing it on a **number line**.\n\nA number line is a visual representation of numbers. Each point on the line corresponds to a unique number. When we find a solution like `x = 3`, it's the *specific value* for 'x' that makes the equation true, represented by a single point.", "visual": {"type": "local_asset", "value": "assets/images/algebra/number_line_x_equals_3.svg", "alt_text": "A number line with a clear dot marking the point '3', labeled as 'x = 3'."}, "hook": "A picture is worth a thousand words, especially in math!", "interactive_element": {"type": "button", "text": "Let's see it in action!", "action": "next_screen"}}}, {"id": "sose-l4-s2", "type": "lesson_screen", "order": 2, "estimatedTimeSeconds": 120, "content": {"headline": "Plot b + 2 = 5", "body_md": "If you solve the equation `b + 2 = 5`, you'll find that `b = 3`.", "visual": {"type": "static_text", "value": "Interactive: Plot the solution for b + 2 = 5"}, "interactive_element": {"type": "interactive_number_line_solution_plotter", "equation_string": "b + 2 = 5", "variable_to_solve": "b", "correct_solution_value": 3, "number_line_min": -5, "number_line_max": 10, "prompt_plot": "First, solve `b + 2 = 5` for 'b'. Then, click on the number line to mark this solution.", "feedback_correct_plot": "Excellent! You solved for 'b' (b = 3) and plotted it. This single point is the only value that makes the equation true.", "feedback_incorrect_plot_wrong_value_template": "Not quite. You plotted {plotted_value}. If b + 2 = 5, then b = 5 - 2. Try again.", "action_button_text": "Why is checking answers so important?"}, "hook": "Pinpoint that solution!"}}]}, {"id": "sose-l5-checking-your-answers", "title": "Confidence Boost: Checking Your Solutions", "description": "Learn the vital skill of verifying your equation solutions by substituting them back into the original equation to ensure accuracy.", "order": 5, "type": "interactive_lesson", "estimatedTimeMinutes": 8, "contentBlocks": [{"id": "sose-l5-s1", "type": "lesson_screen", "order": 1, "estimatedTimeSeconds": 60, "content": {"headline": "Are You Sure? Check Your Solution!", "body_md": "How can you be *absolutely sure* your answer is correct? By **checking your solution**!\n\nSubstitute the value you found for the variable back into the *original* equation. If the equation holds true (left side = right side), your solution is correct!", "visual": {"type": "giphy_search", "value": "checkmark correct"}, "hook": "Become your own math detective and verify your clues!", "interactive_element": {"type": "button", "text": "Show me the way to be sure!", "action": "next_screen"}}}, {"id": "sose-l5-s2", "type": "lesson_screen", "order": 2, "estimatedTimeSeconds": 90, "content": {"headline": "Example: Checking 2w = 10", "body_md": "Let's say we solved `2w = 10` and found `w = 5`.\n\nTo check:\n1.  Original equation: `2w = 10`\n2.  Substitute `w = 5`: `2(5) = 10`\n3.  Simplify: `10 = 10`.\n\nSince both sides are equal, `w = 5` is correct!\n\nWhat if we mistakenly thought `x = 5` for `x + 3 = 7`?\nCheck: `5 + 3 = 7`  => `8 = 7`. FALSE! This tells us `x=5` is wrong.", "visual": {"type": "static_text", "value": "2(5) = 10  =>  10 = 10 (Correct!)"}, "hook": "This simple step can save you from many mistakes.", "interactive_element": {"type": "button", "text": "Let me try checking one!", "action": "next_screen"}}}, {"id": "sose-l5-s3", "type": "lesson_screen", "order": 3, "estimatedTimeSeconds": 120, "content": {"headline": "Interactive Solution Checker", "body_md": "Let's practice. We solved `c - 7 = 3` and believe `c = 10`.\nSubstitute `c = 10` into `c - 7 = 3`. Does it balance?", "visual": {"type": "static_text", "value": "Interactive: Check if c = 10 for c - 7 = 3"}, "interactive_element": {"type": "interactive_solution_checker", "original_equation_display": "c - 7 = 3", "variable_name": "c", "proposed_solution_value_for_user_to_check": 10, "prompt_check": "Substitute c = 10 into c - 7 = 3. Does it balance?", "steps_for_user_to_verify": [{"step_text": "Original: `c - 7 = 3`"}, {"step_text": "Proposed: `c = 10`"}, {"step_text": "Substitute: `(10) - 7 = 3`"}, {"step_text": "Simplify: `3 = 3`"}], "is_proposed_solution_correct": true, "feedback_if_user_confirms_balance_and_solution_was_correct": "Excellent! c = 10 makes it `3 = 3`. Correct!", "feedback_if_user_denies_balance_but_solution_was_correct": "Hmm, if c = 10, then 10 - 7 is 3. So 3 = 3. It was correct!", "feedback_if_user_confirms_balance_but_solution_was_incorrect": "Careful! The proposed solution was wrong, but your check made it balance. Double-check arithmetic!", "feedback_if_user_denies_balance_and_solution_was_incorrect": "You're right! The proposed solution was wrong, and it doesn't balance.", "user_confirmation_prompt": "Does the equation balance with c = 10?", "confirmation_options": [{"id": "yes_balances", "text": "Yes, it balances!"}, {"id": "no_does_not_balance", "text": "No, it doesn't balance."}], "action_button_text": "Why is this habit so good?"}, "hook": "Put on your verifier hat!"}}, {"id": "sose-l5-s4", "type": "lesson_screen", "order": 4, "estimatedTimeSeconds": 40, "content": {"headline": "Habit of Checking", "body_md": "Making a habit of checking your solutions is one of the best ways to catch errors and build strong algebraic skills. It's like having a built-in quality control for your math work!", "visual": {"type": "unsplash_search", "value": "magnifying glass checkmark"}, "hook": "With this habit, you'll solve with confidence!", "interactive_element": {"type": "button", "text": "I'm ready for the One-Step Solver Test!", "action": "next_lesson"}}}]}], "moduleTest": {"id": "sose-mt1-one-step-solver", "title": "Module Test: One-Step Solver", "description": "Solve a series of one-step equations using interactive tools.", "order": 6, "type": "module_test_interactive", "estimatedTimeMinutes": 6, "contentBlocks": [{"id": "sose-mt1-s0-intro", "type": "test_screen_intro", "order": 1, "estimatedTimeSeconds": 20, "content": {"headline": "Module Test: One-Step Solver", "body_md": "Ready to solve some one-step equations?", "visual": {"type": "giphy_search", "value": "test challenge math"}, "interactive_element": {"type": "button", "text": "Start!", "action": "next_screen"}}}, {"id": "sose-mt1-s1-q1", "type": "test_screen_intro", "order": 2, "estimatedTimeSeconds": 60, "content": {"headline": "Question 1", "body_md": "Solve for x: `x + 9 = 17`", "interactive_element": {"type": "text_input", "placeholder": "Enter value of x", "correct_answer_regex": "^8$", "feedback_correct": "Correct! x = 8.", "feedback_incorrect": "Subtract 9 from both sides.", "action_button_text": "Next Question"}}}, {"id": "sose-mt1-s2-q2", "type": "test_screen_intro", "order": 3, "estimatedTimeSeconds": 60, "content": {"headline": "Question 2", "body_md": "Solve for y: `y - 5 = 11`", "interactive_element": {"type": "text_input", "placeholder": "Enter value of y", "correct_answer_regex": "^16$", "feedback_correct": "Correct! y = 16.", "feedback_incorrect": "Add 5 to both sides.", "action_button_text": "Next Question"}}}, {"id": "sose-mt1-s3-q3", "type": "test_screen_intro", "order": 4, "estimatedTimeSeconds": 60, "content": {"headline": "Question 3", "body_md": "Solve for a: `6a = 42`", "interactive_element": {"type": "text_input", "placeholder": "Enter value of a", "correct_answer_regex": "^7$", "feedback_correct": "Correct! a = 7.", "feedback_incorrect": "Divide both sides by 6.", "action_button_text": "Next Question"}}}, {"id": "sose-mt1-s4-q4", "type": "test_screen_intro", "order": 5, "estimatedTimeSeconds": 60, "content": {"headline": "Question 4", "body_md": "Solve for b: `b / 4 = 9`", "interactive_element": {"type": "text_input", "placeholder": "Enter value of b", "correct_answer_regex": "^36$", "feedback_correct": "Correct! b = 36.", "feedback_incorrect": "Multiply both sides by 4.", "action_button_text": "Finish Test"}}}, {"id": "sose-mt1-s5-conclusion", "type": "lesson_screen", "order": 6, "estimatedTimeSeconds": 30, "content": {"headline": "Test Complete!", "body_md": "Great job on the One-Step Solver test!", "visual": {"type": "giphy_search", "value": "awesome job"}, "interactive_element": {"type": "button", "text": "Back to Course", "action": "module_complete"}}}]}}