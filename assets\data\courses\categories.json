{"categories": [{"id": "maths", "title": "Mathematics", "description": "Develop your mathematical thinking and problem-solving skills.", "imageUrl": "maths_category.jpg"}, {"id": "science", "title": "Science", "description": "Explore the natural world through scientific inquiry and discovery.", "imageUrl": "science_category.jpg"}, {"id": "computer_science", "title": "Computer Science", "description": "Learn programming, algorithms, and computational thinking.", "imageUrl": "computer_science_category.jpg"}, {"id": "reasoning", "title": "Reasoning", "description": "Develop critical thinking and problem-solving skills.", "imageUrl": "reasoning_category.jpg"}, {"id": "technology", "title": "Technology", "description": "Explore modern technologies and their applications.", "imageUrl": "technology_category.jpg"}, {"id": "puzzles", "title": "Puzzles", "description": "Challenge your mind with engaging puzzles and brain teasers.", "imageUrl": "puzzles_category.jpg"}, {"id": "curiosity_corner", "title": "Curiosity Corner", "description": "Explore fascinating topics across various disciplines.", "imageUrl": "curiosity_corner_category.jpg"}, {"id": "coming_soon", "title": "Coming Soon", "description": "More categories like Music, Business, Psychology coming soon.", "imageUrl": "coming_soon_category.jpg"}]}