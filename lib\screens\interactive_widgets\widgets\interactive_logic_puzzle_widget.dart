import 'package:flutter/material.dart';

/// A widget that presents interactive logic puzzles with visual elements and feedback
class InteractiveLogicPuzzleWidget extends StatefulWidget {
  /// The data for this widget
  final Map<String, dynamic> data;

  /// Optional callback when the widget state changes
  final Function(bool isCompleted)? onStateChanged;

  const InteractiveLogicPuzzleWidget({
    Key? key,
    required this.data,
    this.onStateChanged,
  }) : super(key: key);

  /// Factory constructor to create the widget from JSON data
  factory InteractiveLogicPuzzleWidget.fromData(Map<String, dynamic> data) {
    return InteractiveLogicPuzzleWidget(
      data: data,
    );
  }

  @override
  State<InteractiveLogicPuzzleWidget> createState() => _InteractiveLogicPuzzleWidgetState();
}

class _InteractiveLogicPuzzleWidgetState extends State<InteractiveLogicPuzzleWidget> {
  // State variables
  bool _isCompleted = false;
  bool _hasSubmitted = false;
  String _feedbackText = '';
  bool _isCorrect = false;
  List<String> _selectedOptions = [];
  String? _textInput;
  
  late String _puzzleType;
  late String _puzzleTitle;
  late String _puzzleDescription;
  late List<Map<String, dynamic>> _puzzleOptions;
  late String _correctAnswer;
  late String _correctFeedback;
  late String _incorrectFeedback;
  late String _explanation;
  late bool _showHints;
  late String _hint;
  late int _maxAttempts;
  late int _attempts;

  @override
  void initState() {
    super.initState();
    // Initialize state from data
    _puzzleType = widget.data['puzzleType'] as String? ?? 'multiple_choice';
    _puzzleTitle = widget.data['title'] as String? ?? 'Logic Puzzle';
    _puzzleDescription = widget.data['description'] as String? ?? '';
    _puzzleOptions = List<Map<String, dynamic>>.from(widget.data['options'] ?? []);
    _correctAnswer = widget.data['correctAnswer'] as String? ?? '';
    _correctFeedback = widget.data['correctFeedback'] as String? ?? 'Correct!';
    _incorrectFeedback = widget.data['incorrectFeedback'] as String? ?? 'Not quite right. Try again!';
    _explanation = widget.data['explanation'] as String? ?? '';
    _showHints = widget.data['showHints'] as bool? ?? false;
    _hint = widget.data['hint'] as String? ?? '';
    _maxAttempts = widget.data['maxAttempts'] as int? ?? 3;
    _attempts = 0;
    
    if (_puzzleType == 'multiple_select') {
      _selectedOptions = [];
    }
  }

  void _submitAnswer() {
    if (_hasSubmitted) return;
    
    setState(() {
      _hasSubmitted = true;
      _attempts++;
      
      if (_puzzleType == 'multiple_choice') {
        _isCorrect = _selectedOptions.isNotEmpty && _selectedOptions[0] == _correctAnswer;
      } else if (_puzzleType == 'multiple_select') {
        final correctAnswers = _correctAnswer.split(',');
        _isCorrect = _selectedOptions.length == correctAnswers.length && 
                     correctAnswers.every((answer) => _selectedOptions.contains(answer));
      } else if (_puzzleType == 'text_input') {
        _isCorrect = _textInput != null && _textInput!.trim().toLowerCase() == _correctAnswer.toLowerCase();
      }
      
      _feedbackText = _isCorrect ? _correctFeedback : _incorrectFeedback;
      
      if (_isCorrect) {
        _isCompleted = true;
        if (widget.onStateChanged != null) {
          widget.onStateChanged!(true);
        }
      } else if (_attempts >= _maxAttempts) {
        // Max attempts reached, show explanation
        _feedbackText = '$_incorrectFeedback\n\n$_explanation';
      }
    });
  }

  void _resetPuzzle() {
    setState(() {
      _hasSubmitted = false;
      _feedbackText = '';
      if (_puzzleType == 'multiple_select') {
        _selectedOptions = [];
      } else if (_puzzleType == 'multiple_choice') {
        _selectedOptions = [];
      } else if (_puzzleType == 'text_input') {
        _textInput = null;
      }
    });
  }

  void _toggleOption(String optionId) {
    if (_hasSubmitted) return;
    
    setState(() {
      if (_puzzleType == 'multiple_choice') {
        _selectedOptions = [optionId];
      } else if (_puzzleType == 'multiple_select') {
        if (_selectedOptions.contains(optionId)) {
          _selectedOptions.remove(optionId);
        } else {
          _selectedOptions.add(optionId);
        }
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withOpacity(0.2),
            spreadRadius: 1,
            blurRadius: 3,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Title
          Text(
            _puzzleTitle,
            style: const TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 12),
          
          // Description
          Text(
            _puzzleDescription,
            style: const TextStyle(fontSize: 16),
          ),
          const SizedBox(height: 16),
          
          // Puzzle content based on type
          if (_puzzleType == 'multiple_choice' || _puzzleType == 'multiple_select')
            _buildMultipleChoiceOptions(),
          
          if (_puzzleType == 'text_input')
            _buildTextInput(),
          
          const SizedBox(height: 16),
          
          // Feedback
          if (_hasSubmitted)
            Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: _isCorrect ? Colors.green.withOpacity(0.1) : Colors.red.withOpacity(0.1),
                borderRadius: BorderRadius.circular(8),
                border: Border.all(
                  color: _isCorrect ? Colors.green.withOpacity(0.3) : Colors.red.withOpacity(0.3),
                ),
              ),
              child: Text(
                _feedbackText,
                style: TextStyle(
                  color: _isCorrect ? Colors.green[800] : Colors.red[800],
                ),
              ),
            ),
          
          const SizedBox(height: 16),
          
          // Hint
          if (_showHints && !_hasSubmitted && _attempts > 0)
            Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: Colors.blue.withOpacity(0.1),
                borderRadius: BorderRadius.circular(8),
                border: Border.all(color: Colors.blue.withOpacity(0.3)),
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'Hint:',
                    style: TextStyle(
                      fontWeight: FontWeight.bold,
                      color: Colors.blue[800],
                    ),
                  ),
                  const SizedBox(height: 4),
                  Text(
                    _hint,
                    style: const TextStyle(fontSize: 14),
                  ),
                ],
              ),
            ),
          
          const SizedBox(height: 16),
          
          // Buttons
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              if (_hasSubmitted && !_isCorrect && _attempts < _maxAttempts)
                ElevatedButton(
                  onPressed: _resetPuzzle,
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Colors.blue[700],
                  ),
                  child: const Text('Try Again'),
                ),
              const Spacer(),
              ElevatedButton(
                onPressed: (_puzzleType == 'multiple_choice' && _selectedOptions.isEmpty) ||
                           (_puzzleType == 'text_input' && (_textInput == null || _textInput!.isEmpty)) ||
                           _hasSubmitted
                    ? null
                    : _submitAnswer,
                style: ElevatedButton.styleFrom(
                  backgroundColor: Colors.green[700],
                ),
                child: Text(_hasSubmitted ? 'Submitted' : 'Submit Answer'),
              ),
            ],
          ),
          
          // Widget name tag
          if (widget.data['showNameTag'] ?? true)
            Padding(
              padding: const EdgeInsets.only(top: 8),
              child: Text(
                'InteractiveLogicPuzzleWidget',
                style: TextStyle(
                  color: Colors.grey[600],
                  fontSize: 12,
                  fontStyle: FontStyle.italic,
                ),
              ),
            ),
        ],
      ),
    );
  }
  
  Widget _buildMultipleChoiceOptions() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: _puzzleOptions.map((option) {
        final optionId = option['id'] as String;
        final optionText = option['text'] as String;
        final isSelected = _selectedOptions.contains(optionId);
        final isCorrectOption = _correctAnswer.split(',').contains(optionId);
        
        return Padding(
          padding: const EdgeInsets.only(bottom: 8),
          child: InkWell(
            onTap: _hasSubmitted ? null : () => _toggleOption(optionId),
            child: Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: _getOptionColor(isSelected, isCorrectOption),
                borderRadius: BorderRadius.circular(8),
                border: Border.all(
                  color: _getOptionBorderColor(isSelected, isCorrectOption),
                ),
              ),
              child: Row(
                children: [
                  Expanded(
                    child: Text(
                      optionText,
                      style: TextStyle(
                        color: _hasSubmitted && ((isSelected && isCorrectOption) || (!isSelected && isCorrectOption))
                            ? Colors.white
                            : Colors.black,
                      ),
                    ),
                  ),
                  if (isSelected)
                    Icon(
                      Icons.check_circle,
                      color: _hasSubmitted
                          ? (isCorrectOption ? Colors.white : Colors.white)
                          : Colors.blue,
                    ),
                ],
              ),
            ),
          ),
        );
      }).toList(),
    );
  }
  
  Widget _buildTextInput() {
    return TextField(
      decoration: InputDecoration(
        hintText: 'Enter your answer',
        border: OutlineInputBorder(
          borderRadius: BorderRadius.circular(8),
        ),
        enabled: !_hasSubmitted,
      ),
      onChanged: (value) {
        setState(() {
          _textInput = value;
        });
      },
    );
  }
  
  Color _getOptionColor(bool isSelected, bool isCorrectOption) {
    if (!_hasSubmitted) {
      return isSelected ? Colors.blue.withOpacity(0.2) : Colors.grey.withOpacity(0.1);
    } else {
      if (isSelected) {
        return isCorrectOption ? Colors.green : Colors.red;
      } else if (isCorrectOption) {
        return Colors.green;
      } else {
        return Colors.grey.withOpacity(0.1);
      }
    }
  }
  
  Color _getOptionBorderColor(bool isSelected, bool isCorrectOption) {
    if (!_hasSubmitted) {
      return isSelected ? Colors.blue : Colors.grey.withOpacity(0.3);
    } else {
      if (isSelected) {
        return isCorrectOption ? Colors.green : Colors.red;
      } else if (isCorrectOption) {
        return Colors.green;
      } else {
        return Colors.grey.withOpacity(0.3);
      }
    }
  }
}
