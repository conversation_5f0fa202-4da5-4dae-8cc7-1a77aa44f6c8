import 'package:flutter/material.dart';
import 'dart:math' as math;

/// A widget that allows users to identify logical fallacies in arguments
class InteractiveLogicalFallacyDetectorWidget extends StatefulWidget {
  /// The data for this widget
  final Map<String, dynamic> data;

  /// Optional callback when the widget state changes
  final Function(bool isCompleted)? onStateChanged;

  const InteractiveLogicalFallacyDetectorWidget({
    super.key,
    required this.data,
    this.onStateChanged,
  });

  /// Factory constructor to create the widget from JSON data
  factory InteractiveLogicalFallacyDetectorWidget.fromData(Map<String, dynamic> data) {
    return InteractiveLogicalFallacyDetectorWidget(
      data: data,
    );
  }

  @override
  State<InteractiveLogicalFallacyDetectorWidget> createState() => _InteractiveLogicalFallacyDetectorWidgetState();
}

class _InteractiveLogicalFallacyDetectorWidgetState extends State<InteractiveLogicalFallacyDetectorWidget> {
  // Arguments and fallacies
  late List<FallacyScenario> _scenarios;
  late int _currentScenarioIndex;
  late List<LogicalFallacy> _fallacies;
  
  // Analysis state
  late String _selectedFallacyId;
  late String _explanation;
  late bool _hasSubmitted;
  late bool _isCorrect;
  
  // UI state
  late bool _isCompleted;
  late bool _showExplanation;
  late TextEditingController _explanationController;
  
  // Colors
  late Color _primaryColor;
  late Color _secondaryColor;
  late Color _accentColor;
  late Color _backgroundColor;
  late Color _textColor;

  @override
  void initState() {
    super.initState();
    _initializeWidget();
  }

  @override
  void dispose() {
    _explanationController.dispose();
    super.dispose();
  }

  void _initializeWidget() {
    // Initialize colors
    _primaryColor = _parseColor(widget.data['primaryColor'] ?? '#2196F3');
    _secondaryColor = _parseColor(widget.data['secondaryColor'] ?? '#FF9800');
    _accentColor = _parseColor(widget.data['accentColor'] ?? '#4CAF50');
    _backgroundColor = _parseColor(widget.data['backgroundColor'] ?? '#FFFFFF');
    _textColor = _parseColor(widget.data['textColor'] ?? '#212121');

    // Initialize fallacies
    final List<dynamic> fallaciesData = widget.data['fallacies'] ?? [];
    _fallacies = fallaciesData.map((fallacyData) => LogicalFallacy.fromJson(fallacyData)).toList();

    // Initialize scenarios
    final List<dynamic> scenariosData = widget.data['scenarios'] ?? [];
    _scenarios = scenariosData.map((scenarioData) => FallacyScenario.fromJson(scenarioData)).toList();
    _currentScenarioIndex = 0;
    
    // Initialize analysis state
    _explanationController = TextEditingController();
    _resetAnalysis();
  }

  void _resetAnalysis() {
    _selectedFallacyId = '';
    _explanation = '';
    _explanationController.clear();
    _hasSubmitted = false;
    _isCorrect = false;
    _isCompleted = false;
    _showExplanation = false;
  }

  Color _parseColor(String colorString) {
    if (colorString.startsWith('#')) {
      return Color(int.parse('FF${colorString.substring(1)}', radix: 16));
    }
    return Colors.blue;
  }

  void _selectFallacy(String fallacyId) {
    setState(() {
      _selectedFallacyId = fallacyId;
    });
  }

  void _submitAnalysis() {
    if (_scenarios.isEmpty) return;
    
    FallacyScenario scenario = _scenarios[_currentScenarioIndex];
    _explanation = _explanationController.text;
    _isCorrect = _selectedFallacyId == scenario.correctFallacyId;
    
    setState(() {
      _hasSubmitted = true;
    });
  }

  void _nextScenario() {
    if (_currentScenarioIndex < _scenarios.length - 1) {
      setState(() {
        _currentScenarioIndex++;
        _resetAnalysis();
      });
    } else if (!_isCompleted) {
      setState(() {
        _isCompleted = true;
      });
      widget.onStateChanged?.call(true);
    }
  }

  void _toggleExplanation() {
    setState(() {
      _showExplanation = !_showExplanation;
    });
  }

  LogicalFallacy? _getFallacyById(String id) {
    try {
      return _fallacies.firstWhere((fallacy) => fallacy.id == id);
    } catch (e) {
      return null;
    }
  }

  @override
  Widget build(BuildContext context) {
    if (_scenarios.isEmpty || _fallacies.isEmpty) {
      return const Center(child: Text('No scenarios or fallacies available'));
    }

    FallacyScenario scenario = _scenarios[_currentScenarioIndex];
    LogicalFallacy? correctFallacy = _getFallacyById(scenario.correctFallacyId);

    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
        side: BorderSide(color: _primaryColor.withOpacity(0.5), width: 1),
      ),
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Title
            Text(
              widget.data['title'] ?? 'Logical Fallacy Detector',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
                color: _textColor,
              ),
            ),

            const SizedBox(height: 8),

            // Scenario navigation
            Row(
              children: [
                Text(
                  'Argument ${_currentScenarioIndex + 1} of ${_scenarios.length}',
                  style: TextStyle(
                    fontSize: 14,
                    fontWeight: FontWeight.bold,
                    color: _primaryColor,
                  ),
                ),
                const Spacer(),
                IconButton(
                  icon: Icon(_showExplanation ? Icons.info_outline : Icons.info),
                  onPressed: _hasSubmitted ? _toggleExplanation : null,
                  tooltip: _showExplanation ? 'Hide Explanation' : 'Show Explanation',
                  color: _secondaryColor,
                ),
              ],
            ),

            const SizedBox(height: 8),

            // Argument
            Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: _backgroundColor,
                borderRadius: BorderRadius.circular(8),
                border: Border.all(color: _primaryColor.withOpacity(0.3)),
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'Argument:',
                    style: TextStyle(
                      fontWeight: FontWeight.bold,
                      color: _textColor,
                    ),
                  ),
                  const SizedBox(height: 4),
                  Text(
                    scenario.argument,
                    style: TextStyle(color: _textColor.withOpacity(0.8)),
                  ),
                ],
              ),
            ),

            const SizedBox(height: 16),

            // Instructions
            Text(
              'Identify the logical fallacy in this argument:',
              style: TextStyle(
                fontWeight: FontWeight.bold,
                color: _textColor,
              ),
            ),

            const SizedBox(height: 12),

            // Fallacy selection
            Container(
              height: 200,
              decoration: BoxDecoration(
                border: Border.all(color: _primaryColor.withOpacity(0.3)),
                borderRadius: BorderRadius.circular(8),
              ),
              child: ListView.builder(
                itemCount: _fallacies.length,
                itemBuilder: (context, index) {
                  LogicalFallacy fallacy = _fallacies[index];
                  bool isSelected = _selectedFallacyId == fallacy.id;
                  
                  return ListTile(
                    title: Text(
                      fallacy.name,
                      style: TextStyle(
                        fontWeight: isSelected ? FontWeight.bold : FontWeight.normal,
                        color: _textColor,
                      ),
                    ),
                    subtitle: Text(
                      fallacy.shortDescription,
                      style: TextStyle(
                        fontSize: 12,
                        color: _textColor.withOpacity(0.7),
                      ),
                    ),
                    leading: Radio<String>(
                      value: fallacy.id,
                      groupValue: _selectedFallacyId,
                      onChanged: _hasSubmitted ? null : (value) => _selectFallacy(value ?? ''),
                      activeColor: _primaryColor,
                    ),
                    selected: isSelected,
                    enabled: !_hasSubmitted,
                    onTap: _hasSubmitted ? null : () => _selectFallacy(fallacy.id),
                  );
                },
              ),
            ),

            const SizedBox(height: 16),

            // Explanation input
            TextField(
              controller: _explanationController,
              decoration: InputDecoration(
                labelText: 'Explain why this is a fallacy',
                hintText: 'What makes this argument fallacious?',
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(8),
                ),
                enabled: !_hasSubmitted,
              ),
              maxLines: 3,
            ),

            const SizedBox(height: 16),

            // Submit button
            if (!_hasSubmitted)
              Center(
                child: ElevatedButton(
                  onPressed: _selectedFallacyId.isNotEmpty ? _submitAnalysis : null,
                  style: ElevatedButton.styleFrom(
                    backgroundColor: _primaryColor,
                    foregroundColor: Colors.white,
                    padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(8),
                    ),
                  ),
                  child: const Text('Submit Analysis'),
                ),
              ),

            // Results
            if (_hasSubmitted && correctFallacy != null) ...[
              // Feedback
              Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: _isCorrect ? Colors.green.withOpacity(0.1) : Colors.red.withOpacity(0.1),
                  borderRadius: BorderRadius.circular(8),
                  border: Border.all(color: _isCorrect ? Colors.green : Colors.red),
                ),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      children: [
                        Icon(
                          _isCorrect ? Icons.check_circle : Icons.error,
                          color: _isCorrect ? Colors.green : Colors.red,
                        ),
                        const SizedBox(width: 8),
                        Text(
                          _isCorrect ? 'Correct!' : 'Not quite right',
                          style: TextStyle(
                            fontWeight: FontWeight.bold,
                            color: _isCorrect ? Colors.green : Colors.red,
                          ),
                        ),
                      ],
                    ),
                    const SizedBox(height: 8),
                    Text(
                      'The fallacy is: ${correctFallacy.name}',
                      style: TextStyle(
                        fontWeight: FontWeight.bold,
                        color: _textColor,
                      ),
                    ),
                    const SizedBox(height: 4),
                    Text(
                      correctFallacy.shortDescription,
                      style: TextStyle(
                        fontStyle: FontStyle.italic,
                        color: _textColor.withOpacity(0.8),
                      ),
                    ),
                    const SizedBox(height: 8),
                    Text(
                      _isCorrect ? scenario.correctFeedback : scenario.incorrectFeedback,
                      style: TextStyle(color: _textColor.withOpacity(0.8)),
                    ),
                  ],
                ),
              ),

              // User explanation
              if (_explanation.isNotEmpty) ...[
                const SizedBox(height: 16),
                Container(
                  padding: const EdgeInsets.all(12),
                  decoration: BoxDecoration(
                    color: _backgroundColor,
                    borderRadius: BorderRadius.circular(8),
                    border: Border.all(color: _primaryColor.withOpacity(0.3)),
                  ),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'Your Explanation:',
                        style: TextStyle(
                          fontWeight: FontWeight.bold,
                          color: _textColor,
                        ),
                      ),
                      const SizedBox(height: 4),
                      Text(
                        _explanation,
                        style: TextStyle(
                          fontStyle: FontStyle.italic,
                          color: _textColor.withOpacity(0.8),
                        ),
                      ),
                    ],
                  ),
                ),
              ],

              // Expert explanation (if shown)
              if (_showExplanation) ...[
                const SizedBox(height: 16),
                Container(
                  padding: const EdgeInsets.all(12),
                  decoration: BoxDecoration(
                    color: _secondaryColor.withOpacity(0.1),
                    borderRadius: BorderRadius.circular(8),
                    border: Border.all(color: _secondaryColor.withOpacity(0.3)),
                  ),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'Expert Explanation:',
                        style: TextStyle(
                          fontWeight: FontWeight.bold,
                          color: _secondaryColor,
                        ),
                      ),
                      const SizedBox(height: 4),
                      Text(
                        scenario.expertExplanation,
                        style: TextStyle(color: _textColor.withOpacity(0.8)),
                      ),
                    ],
                  ),
                ),
              ],

              const SizedBox(height: 16),

              // Navigation buttons
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                children: [
                  OutlinedButton(
                    onPressed: () => setState(() => _resetAnalysis()),
                    style: OutlinedButton.styleFrom(
                      foregroundColor: _primaryColor,
                      side: BorderSide(color: _primaryColor),
                      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                    ),
                    child: const Text('Try Again'),
                  ),
                  ElevatedButton(
                    onPressed: _nextScenario,
                    style: ElevatedButton.styleFrom(
                      backgroundColor: _primaryColor,
                      foregroundColor: Colors.white,
                      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                    ),
                    child: Text(_currentScenarioIndex < _scenarios.length - 1
                        ? 'Next Argument'
                        : 'Finish'),
                  ),
                ],
              ),
            ],
          ],
        ),
      ),
    );
  }
}

/// Represents a scenario with an argument containing a logical fallacy
class FallacyScenario {
  final String id;
  final String argument;
  final String correctFallacyId;
  final String correctFeedback;
  final String incorrectFeedback;
  final String expertExplanation;

  FallacyScenario({
    required this.id,
    required this.argument,
    required this.correctFallacyId,
    required this.correctFeedback,
    required this.incorrectFeedback,
    required this.expertExplanation,
  });

  factory FallacyScenario.fromJson(Map<String, dynamic> json) {
    return FallacyScenario(
      id: json['id'] as String,
      argument: json['argument'] as String,
      correctFallacyId: json['correctFallacyId'] as String,
      correctFeedback: json['correctFeedback'] as String,
      incorrectFeedback: json['incorrectFeedback'] as String,
      expertExplanation: json['expertExplanation'] as String,
    );
  }
}

/// Represents a logical fallacy
class LogicalFallacy {
  final String id;
  final String name;
  final String shortDescription;
  final String fullDescription;
  final String example;

  LogicalFallacy({
    required this.id,
    required this.name,
    required this.shortDescription,
    required this.fullDescription,
    required this.example,
  });

  factory LogicalFallacy.fromJson(Map<String, dynamic> json) {
    return LogicalFallacy(
      id: json['id'] as String,
      name: json['name'] as String,
      shortDescription: json['shortDescription'] as String,
      fullDescription: json['fullDescription'] as String,
      example: json['example'] as String,
    );
  }
}
