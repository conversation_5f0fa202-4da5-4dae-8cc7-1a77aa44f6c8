import 'package:flutter/material.dart';
import 'dart:math' as math;
import 'dart:async';

class InteractiveConservationEnergySimulatorWidget extends StatefulWidget {
  final Map<String, dynamic>? data;

  const InteractiveConservationEnergySimulatorWidget({
    super.key,
    this.data,
  });

  factory InteractiveConservationEnergySimulatorWidget.fromData(
      Map<String, dynamic> data) {
    return InteractiveConservationEnergySimulatorWidget(
      data: data,
    );
  }

  @override
  State<InteractiveConservationEnergySimulatorWidget> createState() =>
      _InteractiveConservationEnergySimulatorWidgetState();
}

class _InteractiveConservationEnergySimulatorWidgetState
    extends State<InteractiveConservationEnergySimulatorWidget>
    with SingleTickerProviderStateMixin {
  // UI parameters
  late Color _primaryColor;
  late Color _secondaryColor;
  late Color _tertiaryColor;
  late Color _textColor;
  late Color _backgroundColor;

  // Simulation parameters
  final List<String> _scenarios = [
    'Pendulum',
    'Roller Coaster',
    'Spring',
    'Bouncing Ball'
  ];
  String _currentScenario = 'Pendulum';
  bool _isSimulating = false;
  bool _showEnergyGraph = true;
  bool _showVelocityGraph = false;
  double _timeStep = 0.016; // 60 FPS
  Timer? _simulationTimer;

  // Physics parameters
  double _gravity = 9.8;
  double _friction = 0.05;
  double _elasticity = 0.9;
  double _airResistance = 0.02;

  // Pendulum parameters
  double _pendulumLength = 100.0;
  double _pendulumMass = 1.0;
  double _pendulumAngle = math.pi / 4;
  double _pendulumAngularVelocity = 0.0;
  double _pendulumDamping = 0.05;
  List<Offset> _pendulumTrajectory = [];

  // Roller coaster parameters
  double _rollerCoasterPosition = 0.0;
  double _rollerCoasterVelocity = 0.0;
  List<Offset> _rollerCoasterTrajectory = [];
  List<Offset> _rollerCoasterTrack = [];

  // Spring parameters
  double _springPosition = -0.5;
  double _springVelocity = 0.0;
  double _springMass = 1.0;
  double _springConstant = 50.0;
  double _springDamping = 0.1;
  List<Offset> _springTrajectory = [];

  // Bouncing ball parameters
  double _ballHeight = 100.0;
  double _ballVelocity = 0.0;
  double _ballMass = 1.0;
  List<Offset> _ballTrajectory = [];

  // Energy values
  double _potentialEnergy = 0.0;
  double _kineticEnergy = 0.0;
  double _totalEnergy = 0.0;
  double _energyLoss = 0.0;
  List<double> _potentialEnergyHistory = [];
  List<double> _kineticEnergyHistory = [];
  List<double> _totalEnergyHistory = [];

  // Animation controller
  late AnimationController _animationController;
  late Animation<double> _animation;

  @override
  void initState() {
    super.initState();
    _initializeFromData();
    _animationController = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 500),
    );
    _animation = CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeInOut,
    );
    _animationController.forward();
    _initializeScenario();
  }

  @override
  void dispose() {
    _simulationTimer?.cancel();
    _animationController.dispose();
    super.dispose();
  }

  void _initializeFromData() {
    final data = widget.data;
    if (data != null) {
      _primaryColor = Color(data['primary_color'] ?? 0xFF2196F3);
      _secondaryColor = Color(data['secondary_color'] ?? 0xFFFFA000);
      _tertiaryColor = Color(data['tertiary_color'] ?? 0xFF4CAF50);
      _textColor = Color(data['text_color'] ?? 0xFF333333);
      _backgroundColor = Color(data['background_color'] ?? 0xFFF5F5F5);

      if (data['scenario'] != null) {
        _currentScenario = data['scenario'];
      }

      if (data['gravity'] != null) {
        _gravity = data['gravity'].toDouble();
      }

      if (data['friction'] != null) {
        _friction = data['friction'].toDouble();
      }

      if (data['elasticity'] != null) {
        _elasticity = data['elasticity'].toDouble();
      }

      if (data['air_resistance'] != null) {
        _airResistance = data['air_resistance'].toDouble();
      }
    } else {
      _primaryColor = Colors.blue;
      _secondaryColor = Colors.orange;
      _tertiaryColor = Colors.green;
      _textColor = Colors.black87;
      _backgroundColor = Colors.grey.shade100;
    }
  }

  void _initializeScenario() {
    setState(() {
      switch (_currentScenario) {
        case 'Pendulum':
          _pendulumAngle = math.pi / 4;
          _pendulumAngularVelocity = 0.0;
          _pendulumTrajectory = [];
          _potentialEnergy = _calculatePendulumPotentialEnergy();
          _kineticEnergy = _calculatePendulumKineticEnergy();
          _totalEnergy = _potentialEnergy + _kineticEnergy;
          break;
        case 'Roller Coaster':
          _initializeRollerCoasterTrack();
          _rollerCoasterPosition = 0.0;
          _rollerCoasterVelocity = 0.0;
          _rollerCoasterTrajectory = [];
          _potentialEnergy = _calculateRollerCoasterPotentialEnergy();
          _kineticEnergy = _calculateRollerCoasterKineticEnergy();
          _totalEnergy = _potentialEnergy + _kineticEnergy;
          break;
        case 'Spring':
          _springPosition = -0.5; // Compressed spring
          _springVelocity = 0.0;
          _springTrajectory = [];
          _potentialEnergy = _calculateSpringPotentialEnergy();
          _kineticEnergy = _calculateSpringKineticEnergy();
          _totalEnergy = _potentialEnergy + _kineticEnergy;
          break;
        case 'Bouncing Ball':
          _ballHeight = 100.0;
          _ballVelocity = 0.0;
          _ballTrajectory = [];
          _potentialEnergy = _calculateBallPotentialEnergy();
          _kineticEnergy = _calculateBallKineticEnergy();
          _totalEnergy = _potentialEnergy + _kineticEnergy;
          break;
      }
      _potentialEnergyHistory = [];
      _kineticEnergyHistory = [];
      _totalEnergyHistory = [];
      _energyLoss = 0.0;
    });
  }

  void _initializeRollerCoasterTrack() {
    _rollerCoasterTrack = [];
    final trackWidth = 300.0;
    final trackHeight = 150.0;

    // Create a roller coaster track with hills and valleys
    for (int i = 0; i <= 100; i++) {
      final x = i * trackWidth / 100;
      final normalizedX = i / 100.0;

      // Create a track with multiple hills
      final y = trackHeight * (0.5 - 0.5 * math.sin(normalizedX * 4 * math.pi));

      _rollerCoasterTrack.add(Offset(x, y));
    }
  }

  void _toggleSimulation() {
    if (_isSimulating) {
      _stopSimulation();
    } else {
      _startSimulation();
    }
  }

  void _startSimulation() {
    if (_isSimulating) return;

    setState(() {
      _isSimulating = true;
    });

    _simulationTimer = Timer.periodic(Duration(milliseconds: (_timeStep * 1000).toInt()), (timer) {
      _updateSimulation();
    });
  }

  void _stopSimulation() {
    _simulationTimer?.cancel();
    _simulationTimer = null;

    setState(() {
      _isSimulating = false;
    });
  }

  void _resetSimulation() {
    _stopSimulation();
    _initializeScenario();
  }

  void _updateSimulation() {
    setState(() {
      switch (_currentScenario) {
        case 'Pendulum':
          _updatePendulum();
          break;
        case 'Roller Coaster':
          _updateRollerCoaster();
          break;
        case 'Spring':
          _updateSpring();
          break;
        case 'Bouncing Ball':
          _updateBouncingBall();
          break;
      }

      // Update energy history
      if (_potentialEnergyHistory.length > 100) {
        _potentialEnergyHistory.removeAt(0);
        _kineticEnergyHistory.removeAt(0);
        _totalEnergyHistory.removeAt(0);
      }

      _potentialEnergyHistory.add(_potentialEnergy);
      _kineticEnergyHistory.add(_kineticEnergy);
      _totalEnergyHistory.add(_potentialEnergy + _kineticEnergy);
    });
  }

  // Pendulum physics
  void _updatePendulum() {
    // Calculate angular acceleration
    double angularAcceleration = -(_gravity / _pendulumLength) * math.sin(_pendulumAngle) -
                                _pendulumDamping * _pendulumAngularVelocity;

    // Update angular velocity
    _pendulumAngularVelocity += angularAcceleration * _timeStep;

    // Update angle
    _pendulumAngle += _pendulumAngularVelocity * _timeStep;

    // Calculate energies
    _potentialEnergy = _calculatePendulumPotentialEnergy();
    _kineticEnergy = _calculatePendulumKineticEnergy();
    _energyLoss = _totalEnergy - (_potentialEnergy + _kineticEnergy);

    // Update trajectory
    if (_pendulumTrajectory.length > 100) {
      _pendulumTrajectory.removeAt(0);
    }

    double x = _pendulumLength * math.sin(_pendulumAngle);
    double y = _pendulumLength * math.cos(_pendulumAngle);
    _pendulumTrajectory.add(Offset(x, y));
  }

  double _calculatePendulumPotentialEnergy() {
    // U = mgh = mg(L - L*cos(θ))
    return _pendulumMass * _gravity * _pendulumLength * (1 - math.cos(_pendulumAngle));
  }

  double _calculatePendulumKineticEnergy() {
    // K = (1/2)mv² = (1/2)m(Lω)²
    return 0.5 * _pendulumMass * math.pow(_pendulumLength * _pendulumAngularVelocity, 2);
  }

  // Roller coaster physics
  void _updateRollerCoaster() {
    // Get current position on track
    int currentIndex = _rollerCoasterPosition.toInt();
    if (currentIndex >= _rollerCoasterTrack.length - 1) {
      currentIndex = _rollerCoasterTrack.length - 2;
    }

    // Get current height and next height
    double currentHeight = _rollerCoasterTrack[currentIndex].dy;
    double nextHeight = _rollerCoasterTrack[currentIndex + 1].dy;

    // Calculate slope
    double slope = (nextHeight - currentHeight) / 1.0;

    // Calculate acceleration due to gravity along the slope
    double acceleration = _gravity * math.sin(math.atan(slope));

    // Apply air resistance
    acceleration -= _airResistance * _rollerCoasterVelocity * _rollerCoasterVelocity * (_rollerCoasterVelocity > 0 ? 1 : -1);

    // Update velocity
    _rollerCoasterVelocity += acceleration * _timeStep;

    // Update position
    _rollerCoasterPosition += _rollerCoasterVelocity * _timeStep;

    // Constrain position to track length
    if (_rollerCoasterPosition < 0) {
      _rollerCoasterPosition = 0;
      _rollerCoasterVelocity = 0;
    } else if (_rollerCoasterPosition >= _rollerCoasterTrack.length - 1) {
      _rollerCoasterPosition = 0; // Loop back to start
      _rollerCoasterVelocity = 0;
    }

    // Calculate energies
    _potentialEnergy = _calculateRollerCoasterPotentialEnergy();
    _kineticEnergy = _calculateRollerCoasterKineticEnergy();
    _energyLoss = _totalEnergy - (_potentialEnergy + _kineticEnergy);

    // Update trajectory
    if (_rollerCoasterTrajectory.length > 100) {
      _rollerCoasterTrajectory.removeAt(0);
    }

    // Interpolate position on track
    int pos = _rollerCoasterPosition.toInt();
    double fraction = _rollerCoasterPosition - pos;
    if (pos < _rollerCoasterTrack.length - 1) {
      double x = _rollerCoasterTrack[pos].dx + fraction * (_rollerCoasterTrack[pos + 1].dx - _rollerCoasterTrack[pos].dx);
      double y = _rollerCoasterTrack[pos].dy + fraction * (_rollerCoasterTrack[pos + 1].dy - _rollerCoasterTrack[pos].dy);
      _rollerCoasterTrajectory.add(Offset(x, y));
    }
  }

  double _calculateRollerCoasterPotentialEnergy() {
    // U = mgh
    int pos = _rollerCoasterPosition.toInt();
    if (pos >= _rollerCoasterTrack.length) {
      pos = _rollerCoasterTrack.length - 1;
    }
    double height = _rollerCoasterTrack[pos].dy;
    double maxHeight = 150.0; // Maximum height of the track
    return _ballMass * _gravity * (maxHeight - height);
  }

  double _calculateRollerCoasterKineticEnergy() {
    // K = (1/2)mv²
    return 0.5 * _ballMass * math.pow(_rollerCoasterVelocity, 2);
  }

  // Spring physics
  void _updateSpring() {
    // Calculate acceleration
    double acceleration = -(_springConstant / _springMass) * _springPosition - _springDamping * _springVelocity;

    // Update velocity
    _springVelocity += acceleration * _timeStep;

    // Update position
    _springPosition += _springVelocity * _timeStep;

    // Calculate energies
    _potentialEnergy = _calculateSpringPotentialEnergy();
    _kineticEnergy = _calculateSpringKineticEnergy();
    _energyLoss = _totalEnergy - (_potentialEnergy + _kineticEnergy);

    // Update trajectory
    if (_springTrajectory.length > 100) {
      _springTrajectory.removeAt(0);
    }

    _springTrajectory.add(Offset(_springPosition * 50, 0));
  }

  double _calculateSpringPotentialEnergy() {
    // U = (1/2)kx²
    return 0.5 * _springConstant * math.pow(_springPosition, 2);
  }

  double _calculateSpringKineticEnergy() {
    // K = (1/2)mv²
    return 0.5 * _springMass * math.pow(_springVelocity, 2);
  }

  // Bouncing ball physics
  void _updateBouncingBall() {
    // Calculate acceleration
    double acceleration = _gravity;

    // Apply air resistance
    acceleration += -_airResistance * _ballVelocity * _ballVelocity.abs();

    // Update velocity
    _ballVelocity += acceleration * _timeStep;

    // Update height
    _ballHeight -= _ballVelocity * _timeStep;

    // Check for collision with ground
    if (_ballHeight <= 0) {
      _ballHeight = 0;
      _ballVelocity = -_ballVelocity * _elasticity; // Bounce with energy loss
    }

    // Calculate energies
    _potentialEnergy = _calculateBallPotentialEnergy();
    _kineticEnergy = _calculateBallKineticEnergy();
    _energyLoss = _totalEnergy - (_potentialEnergy + _kineticEnergy);

    // Update trajectory
    if (_ballTrajectory.length > 100) {
      _ballTrajectory.removeAt(0);
    }

    _ballTrajectory.add(Offset(0, _ballHeight));
  }

  double _calculateBallPotentialEnergy() {
    // U = mgh
    return _ballMass * _gravity * _ballHeight;
  }

  double _calculateBallKineticEnergy() {
    // K = (1/2)mv²
    return 0.5 * _ballMass * math.pow(_ballVelocity, 2);
  }

  @override
  Widget build(BuildContext context) {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
      ),
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Title and description
            Text(
              'Conservation of Energy Simulator',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
                color: _textColor,
              ),
            ),
            const SizedBox(height: 8),
            Text(
              'Explore how energy transforms between potential and kinetic forms while total energy is conserved',
              style: TextStyle(
                fontSize: 14,
                color: _textColor.withOpacity(0.7),
              ),
            ),
            const SizedBox(height: 16),

            // Scenario selector and controls
            Row(
              children: [
                Text(
                  'Scenario:',
                  style: TextStyle(
                    fontSize: 14,
                    fontWeight: FontWeight.bold,
                    color: _textColor,
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: DropdownButton<String>(
                    value: _currentScenario,
                    isExpanded: true,
                    onChanged: (String? newValue) {
                      if (newValue != null) {
                        _stopSimulation();
                        setState(() {
                          _currentScenario = newValue;
                          _initializeScenario();
                        });
                      }
                    },
                    items: _scenarios
                        .map<DropdownMenuItem<String>>((String value) {
                      return DropdownMenuItem<String>(
                        value: value,
                        child: Text(value),
                      );
                    }).toList(),
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),

            // Simulation controls
            Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                ElevatedButton.icon(
                  onPressed: _toggleSimulation,
                  icon: Icon(_isSimulating ? Icons.pause : Icons.play_arrow),
                  label: Text(_isSimulating ? 'Pause' : 'Start'),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: _primaryColor,
                    foregroundColor: Colors.white,
                  ),
                ),
                const SizedBox(width: 16),
                ElevatedButton.icon(
                  onPressed: _resetSimulation,
                  icon: const Icon(Icons.refresh),
                  label: const Text('Reset'),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: _secondaryColor,
                    foregroundColor: Colors.white,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),

            // Visualization area
            Container(
              height: 200,
              width: double.infinity,
              decoration: BoxDecoration(
                color: Colors.grey.withOpacity(0.1),
                borderRadius: BorderRadius.circular(8),
              ),
              child: _buildVisualization(),
            ),
            const SizedBox(height: 16),

            // Energy bar chart
            Container(
              height: 100,
              width: double.infinity,
              decoration: BoxDecoration(
                color: Colors.grey.withOpacity(0.1),
                borderRadius: BorderRadius.circular(8),
              ),
              child: CustomPaint(
                painter: EnergyBarChartPainter(
                  potentialEnergy: _potentialEnergy,
                  kineticEnergy: _kineticEnergy,
                  totalEnergy: _totalEnergy,
                  energyLoss: _energyLoss,
                  primaryColor: _primaryColor,
                  secondaryColor: _secondaryColor,
                  tertiaryColor: _tertiaryColor,
                ),
              ),
            ),
            const SizedBox(height: 16),

            // Energy values
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceAround,
              children: [
                _buildEnergyValueDisplay('Potential', _potentialEnergy, _primaryColor),
                _buildEnergyValueDisplay('Kinetic', _kineticEnergy, _secondaryColor),
                _buildEnergyValueDisplay('Total', _potentialEnergy + _kineticEnergy, _tertiaryColor),
              ],
            ),
            const SizedBox(height: 16),

            // Energy conservation explanation
            ExpansionTile(
              title: Text(
                'Understanding Energy Conservation',
                style: TextStyle(
                  fontSize: 14,
                  fontWeight: FontWeight.bold,
                  color: _textColor,
                ),
              ),
              children: [
                Padding(
                  padding: const EdgeInsets.all(16.0),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'The law of conservation of energy states that energy cannot be created or destroyed, only transformed from one form to another.',
                        style: TextStyle(
                          fontSize: 14,
                          color: _textColor,
                        ),
                      ),
                      const SizedBox(height: 8),
                      Text(
                        'In this simulation:',
                        style: TextStyle(
                          fontSize: 14,
                          fontWeight: FontWeight.bold,
                          color: _textColor,
                        ),
                      ),
                      const SizedBox(height: 4),
                      Text(
                        '• Potential energy is energy due to position or configuration',
                        style: TextStyle(
                          fontSize: 14,
                          color: _textColor,
                        ),
                      ),
                      Text(
                        '• Kinetic energy is energy of motion',
                        style: TextStyle(
                          fontSize: 14,
                          color: _textColor,
                        ),
                      ),
                      Text(
                        '• Total energy (potential + kinetic) remains constant in an ideal system',
                        style: TextStyle(
                          fontSize: 14,
                          color: _textColor,
                        ),
                      ),
                      Text(
                        '• Energy loss occurs due to friction, air resistance, or other non-conservative forces',
                        style: TextStyle(
                          fontSize: 14,
                          color: _textColor,
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildEnergyValueDisplay(String label, double value, Color color) {
    return Column(
      children: [
        Text(
          label,
          style: TextStyle(
            fontSize: 14,
            fontWeight: FontWeight.bold,
            color: _textColor,
          ),
        ),
        const SizedBox(height: 4),
        Text(
          '${value.toStringAsFixed(1)} J',
          style: TextStyle(
            fontSize: 14,
            color: color,
            fontWeight: FontWeight.bold,
          ),
        ),
      ],
    );
  }

  Widget _buildVisualization() {
    switch (_currentScenario) {
      case 'Pendulum':
        return CustomPaint(
          painter: PendulumPainter(
            angle: _pendulumAngle,
            length: _pendulumLength,
            trajectory: _pendulumTrajectory,
            primaryColor: _primaryColor,
            secondaryColor: _secondaryColor,
            textColor: _textColor,
          ),
        );
      case 'Roller Coaster':
        return CustomPaint(
          painter: RollerCoasterPainter(
            position: _rollerCoasterPosition,
            velocity: _rollerCoasterVelocity,
            track: _rollerCoasterTrack,
            trajectory: _rollerCoasterTrajectory,
            primaryColor: _primaryColor,
            secondaryColor: _secondaryColor,
            textColor: _textColor,
          ),
        );
      case 'Spring':
        return CustomPaint(
          painter: SpringPainter(
            position: _springPosition,
            velocity: _springVelocity,
            trajectory: _springTrajectory,
            primaryColor: _primaryColor,
            secondaryColor: _secondaryColor,
            textColor: _textColor,
          ),
        );
      case 'Bouncing Ball':
        return CustomPaint(
          painter: BouncingBallPainter(
            height: _ballHeight,
            velocity: _ballVelocity,
            trajectory: _ballTrajectory,
            primaryColor: _primaryColor,
            secondaryColor: _secondaryColor,
            textColor: _textColor,
          ),
        );
      default:
        return const Center(
          child: Text('Select a scenario'),
        );
    }
  }
}

// Painter classes for visualizations
class PendulumPainter extends CustomPainter {
  final double angle;
  final double length;
  final List<Offset> trajectory;
  final Color primaryColor;
  final Color secondaryColor;
  final Color textColor;

  PendulumPainter({
    required this.angle,
    required this.length,
    required this.trajectory,
    required this.primaryColor,
    required this.secondaryColor,
    required this.textColor,
  });

  @override
  void paint(Canvas canvas, Size size) {
    final center = Offset(size.width / 2, size.height / 4);

    // Draw pendulum rod
    final bobPosition = Offset(
      center.dx + length * math.sin(angle),
      center.dy + length * math.cos(angle),
    );

    final rodPaint = Paint()
      ..color = textColor
      ..strokeWidth = 2.0
      ..style = PaintingStyle.stroke;

    canvas.drawLine(center, bobPosition, rodPaint);

    // Draw pivot point
    final pivotPaint = Paint()
      ..color = textColor
      ..style = PaintingStyle.fill;

    canvas.drawCircle(center, 4.0, pivotPaint);

    // Draw bob
    final bobPaint = Paint()
      ..color = primaryColor
      ..style = PaintingStyle.fill;

    canvas.drawCircle(bobPosition, 10.0, bobPaint);

    // Draw trajectory
    if (trajectory.isNotEmpty) {
      final trajectoryPaint = Paint()
        ..color = secondaryColor.withOpacity(0.5)
        ..strokeWidth = 1.0
        ..style = PaintingStyle.stroke;

      final path = Path();
      path.moveTo(
        center.dx + trajectory.first.dx,
        center.dy + trajectory.first.dy,
      );

      for (int i = 1; i < trajectory.length; i++) {
        path.lineTo(
          center.dx + trajectory[i].dx,
          center.dy + trajectory[i].dy,
        );
      }

      canvas.drawPath(path, trajectoryPaint);
    }

    // Draw ground line
    final groundPaint = Paint()
      ..color = textColor.withOpacity(0.3)
      ..strokeWidth = 1.0
      ..style = PaintingStyle.stroke;

    canvas.drawLine(
      Offset(0, size.height - 20),
      Offset(size.width, size.height - 20),
      groundPaint,
    );
  }

  @override
  bool shouldRepaint(covariant PendulumPainter oldDelegate) {
    return oldDelegate.angle != angle ||
        oldDelegate.length != length ||
        oldDelegate.trajectory != trajectory;
  }
}

class RollerCoasterPainter extends CustomPainter {
  final double position;
  final double velocity;
  final List<Offset> track;
  final List<Offset> trajectory;
  final Color primaryColor;
  final Color secondaryColor;
  final Color textColor;

  RollerCoasterPainter({
    required this.position,
    required this.velocity,
    required this.track,
    required this.trajectory,
    required this.primaryColor,
    required this.secondaryColor,
    required this.textColor,
  });

  @override
  void paint(Canvas canvas, Size size) {
    // Scale track to fit canvas
    final scaleX = size.width / 300.0;
    final scaleY = size.height / 150.0;

    // Draw track
    final trackPaint = Paint()
      ..color = textColor
      ..strokeWidth = 2.0
      ..style = PaintingStyle.stroke;

    final trackPath = Path();
    if (track.isNotEmpty) {
      trackPath.moveTo(track.first.dx * scaleX, track.first.dy * scaleY);

      for (int i = 1; i < track.length; i++) {
        trackPath.lineTo(track[i].dx * scaleX, track[i].dy * scaleY);
      }
    }

    canvas.drawPath(trackPath, trackPaint);

    // Draw cart
    final cartPaint = Paint()
      ..color = primaryColor
      ..style = PaintingStyle.fill;

    // Get cart position
    Offset cartPosition;
    if (track.isEmpty || position >= track.length) {
      cartPosition = Offset(size.width / 2, size.height / 2);
    } else {
      int pos = position.toInt();
      double fraction = position - pos;
      if (pos < track.length - 1) {
        double x = track[pos].dx + fraction * (track[pos + 1].dx - track[pos].dx);
        double y = track[pos].dy + fraction * (track[pos + 1].dy - track[pos].dy);
        cartPosition = Offset(x * scaleX, y * scaleY);
      } else {
        cartPosition = Offset(track[pos].dx * scaleX, track[pos].dy * scaleY);
      }
    }

    // Draw cart
    canvas.drawCircle(cartPosition, 8.0, cartPaint);

    // Draw trajectory
    if (trajectory.isNotEmpty) {
      final trajectoryPaint = Paint()
        ..color = secondaryColor.withOpacity(0.5)
        ..strokeWidth = 1.0
        ..style = PaintingStyle.stroke;

      final path = Path();
      path.moveTo(trajectory.first.dx * scaleX, trajectory.first.dy * scaleY);

      for (int i = 1; i < trajectory.length; i++) {
        path.lineTo(trajectory[i].dx * scaleX, trajectory[i].dy * scaleY);
      }

      canvas.drawPath(path, trajectoryPaint);
    }

    // Draw velocity indicator
    final velocityPaint = Paint()
      ..color = secondaryColor
      ..strokeWidth = 2.0
      ..style = PaintingStyle.stroke;

    final arrowLength = velocity * 5.0;
    if (arrowLength.abs() > 2.0) {
      // Calculate direction based on track
      double angle = 0.0;
      int pos = position.toInt();
      if (pos < track.length - 1) {
        double dx = track[pos + 1].dx - track[pos].dx;
        double dy = track[pos + 1].dy - track[pos].dy;
        angle = math.atan2(dy, dx);
      }

      // Draw arrow
      canvas.drawLine(
        cartPosition,
        Offset(
          cartPosition.dx + arrowLength * math.cos(angle),
          cartPosition.dy + arrowLength * math.sin(angle),
        ),
        velocityPaint,
      );
    }
  }

  @override
  bool shouldRepaint(covariant RollerCoasterPainter oldDelegate) {
    return oldDelegate.position != position ||
        oldDelegate.velocity != velocity ||
        oldDelegate.track != track ||
        oldDelegate.trajectory != trajectory;
  }
}

class SpringPainter extends CustomPainter {
  final double position;
  final double velocity;
  final List<Offset> trajectory;
  final Color primaryColor;
  final Color secondaryColor;
  final Color textColor;

  SpringPainter({
    required this.position,
    required this.velocity,
    required this.trajectory,
    required this.primaryColor,
    required this.secondaryColor,
    required this.textColor,
  });

  @override
  void paint(Canvas canvas, Size size) {
    final center = Offset(size.width / 2, size.height / 2);

    // Draw spring
    final springPaint = Paint()
      ..color = textColor
      ..strokeWidth = 2.0
      ..style = PaintingStyle.stroke;

    // Draw spring as a zigzag line
    final springPath = Path();
    final springWidth = 100.0;
    final springHeight = 20.0;
    final numCoils = 10;

    // Start at the wall
    final wallX = center.dx - springWidth / 2 - 50;
    springPath.moveTo(wallX, center.dy);

    // Draw zigzag
    double x = wallX;
    final coilWidth = springWidth / numCoils;
    for (int i = 0; i < numCoils; i++) {
      x += coilWidth / 2;
      springPath.lineTo(x, center.dy - springHeight);
      x += coilWidth / 2;
      springPath.lineTo(x, center.dy + springHeight);
    }

    // End at the mass
    final massX = center.dx + position * 50;
    springPath.lineTo(massX, center.dy);

    canvas.drawPath(springPath, springPaint);

    // Draw wall
    final wallPaint = Paint()
      ..color = textColor
      ..style = PaintingStyle.fill;

    canvas.drawRect(
      Rect.fromLTWH(wallX - 10, center.dy - 50, 10, 100),
      wallPaint,
    );

    // Draw mass
    final massPaint = Paint()
      ..color = primaryColor
      ..style = PaintingStyle.fill;

    canvas.drawRect(
      Rect.fromLTWH(massX, center.dy - 20, 40, 40),
      massPaint,
    );

    // Draw trajectory
    if (trajectory.isNotEmpty) {
      final trajectoryPaint = Paint()
        ..color = secondaryColor.withOpacity(0.5)
        ..strokeWidth = 1.0
        ..style = PaintingStyle.stroke;

      final path = Path();
      path.moveTo(center.dx + trajectory.first.dx, center.dy + trajectory.first.dy);

      for (int i = 1; i < trajectory.length; i++) {
        path.lineTo(center.dx + trajectory[i].dx, center.dy + trajectory[i].dy);
      }

      canvas.drawPath(path, trajectoryPaint);
    }

    // Draw ground line
    final groundPaint = Paint()
      ..color = textColor.withOpacity(0.3)
      ..strokeWidth = 1.0
      ..style = PaintingStyle.stroke;

    canvas.drawLine(
      Offset(0, center.dy + 30),
      Offset(size.width, center.dy + 30),
      groundPaint,
    );
  }

  @override
  bool shouldRepaint(covariant SpringPainter oldDelegate) {
    return oldDelegate.position != position ||
        oldDelegate.velocity != velocity ||
        oldDelegate.trajectory != trajectory;
  }
}

class BouncingBallPainter extends CustomPainter {
  final double height;
  final double velocity;
  final List<Offset> trajectory;
  final Color primaryColor;
  final Color secondaryColor;
  final Color textColor;

  BouncingBallPainter({
    required this.height,
    required this.velocity,
    required this.trajectory,
    required this.primaryColor,
    required this.secondaryColor,
    required this.textColor,
  });

  @override
  void paint(Canvas canvas, Size size) {
    final center = Offset(size.width / 2, 0);

    // Draw ground
    final groundPaint = Paint()
      ..color = textColor
      ..strokeWidth = 2.0
      ..style = PaintingStyle.stroke;

    canvas.drawLine(
      Offset(0, size.height - 20),
      Offset(size.width, size.height - 20),
      groundPaint,
    );

    // Draw ball
    final ballPaint = Paint()
      ..color = primaryColor
      ..style = PaintingStyle.fill;

    final ballPosition = Offset(center.dx, size.height - 20 - height);
    canvas.drawCircle(ballPosition, 15.0, ballPaint);

    // Draw trajectory
    if (trajectory.isNotEmpty) {
      final trajectoryPaint = Paint()
        ..color = secondaryColor.withOpacity(0.5)
        ..strokeWidth = 1.0
        ..style = PaintingStyle.stroke;

      final path = Path();
      path.moveTo(center.dx, size.height - 20 - trajectory.first.dy);

      for (int i = 1; i < trajectory.length; i++) {
        path.lineTo(center.dx, size.height - 20 - trajectory[i].dy);
      }

      canvas.drawPath(path, trajectoryPaint);
    }

    // Draw velocity indicator
    final velocityPaint = Paint()
      ..color = secondaryColor
      ..strokeWidth = 2.0
      ..style = PaintingStyle.stroke;

    final arrowLength = velocity * 2.0;
    if (arrowLength.abs() > 2.0) {
      canvas.drawLine(
        ballPosition,
        Offset(ballPosition.dx, ballPosition.dy - arrowLength),
        velocityPaint,
      );
    }
  }

  @override
  bool shouldRepaint(covariant BouncingBallPainter oldDelegate) {
    return oldDelegate.height != height ||
        oldDelegate.velocity != velocity ||
        oldDelegate.trajectory != trajectory;
  }
}

class EnergyBarChartPainter extends CustomPainter {
  final double potentialEnergy;
  final double kineticEnergy;
  final double totalEnergy;
  final double energyLoss;
  final Color primaryColor;
  final Color secondaryColor;
  final Color tertiaryColor;

  EnergyBarChartPainter({
    required this.potentialEnergy,
    required this.kineticEnergy,
    required this.totalEnergy,
    required this.energyLoss,
    required this.primaryColor,
    required this.secondaryColor,
    required this.tertiaryColor,
  });

  @override
  void paint(Canvas canvas, Size size) {
    final barWidth = size.width / 4;
    final maxHeight = size.height * 0.8;
    final bottomY = size.height * 0.9;

    // Calculate bar heights
    final potentialBarHeight = (potentialEnergy / totalEnergy) * maxHeight;
    final kineticBarHeight = (kineticEnergy / totalEnergy) * maxHeight;
    final totalBarHeight = maxHeight;
    final currentTotalBarHeight = ((potentialEnergy + kineticEnergy) / totalEnergy) * maxHeight;

    // Draw potential energy bar
    final potentialPaint = Paint()
      ..color = primaryColor
      ..style = PaintingStyle.fill;
    canvas.drawRect(
      Rect.fromLTWH(barWidth * 0.5, bottomY - potentialBarHeight, barWidth * 0.8, potentialBarHeight),
      potentialPaint,
    );

    // Draw kinetic energy bar
    final kineticPaint = Paint()
      ..color = secondaryColor
      ..style = PaintingStyle.fill;
    canvas.drawRect(
      Rect.fromLTWH(barWidth * 1.5, bottomY - kineticBarHeight, barWidth * 0.8, kineticBarHeight),
      kineticPaint,
    );

    // Draw total energy bar (initial)
    final totalPaint = Paint()
      ..color = tertiaryColor.withOpacity(0.3)
      ..style = PaintingStyle.fill;
    canvas.drawRect(
      Rect.fromLTWH(barWidth * 2.5, bottomY - totalBarHeight, barWidth * 0.8, totalBarHeight),
      totalPaint,
    );

    // Draw current total energy bar
    final currentTotalPaint = Paint()
      ..color = tertiaryColor
      ..style = PaintingStyle.fill;
    canvas.drawRect(
      Rect.fromLTWH(barWidth * 2.5, bottomY - currentTotalBarHeight, barWidth * 0.8, currentTotalBarHeight),
      currentTotalPaint,
    );

    // Draw energy loss indicator
    if (energyLoss > 0.01 * totalEnergy) {
      final lossHeight = (energyLoss / totalEnergy) * maxHeight;
      final lossPaint = Paint()
        ..color = Colors.red.withOpacity(0.7)
        ..style = PaintingStyle.fill;
      canvas.drawRect(
        Rect.fromLTWH(barWidth * 2.5, bottomY - totalBarHeight, barWidth * 0.8, lossHeight),
        lossPaint,
      );
    }

    // Draw labels
    final textStyle = TextStyle(
      color: Colors.black87,
      fontSize: 12,
    );

    _drawCenteredText(canvas, 'Potential', Offset(barWidth * 0.9, bottomY + 10), textStyle);
    _drawCenteredText(canvas, 'Kinetic', Offset(barWidth * 1.9, bottomY + 10), textStyle);
    _drawCenteredText(canvas, 'Total', Offset(barWidth * 2.9, bottomY + 10), textStyle);
  }

  void _drawCenteredText(Canvas canvas, String text, Offset position, TextStyle style) {
    final textSpan = TextSpan(
      text: text,
      style: style,
    );
    final textPainter = TextPainter(
      text: textSpan,
      textDirection: TextDirection.ltr,
    );
    textPainter.layout();
    textPainter.paint(
      canvas,
      Offset(position.dx - textPainter.width / 2, position.dy),
    );
  }

  @override
  bool shouldRepaint(covariant EnergyBarChartPainter oldDelegate) {
    return oldDelegate.potentialEnergy != potentialEnergy ||
        oldDelegate.kineticEnergy != kineticEnergy ||
        oldDelegate.totalEnergy != totalEnergy ||
        oldDelegate.energyLoss != energyLoss;
  }
}