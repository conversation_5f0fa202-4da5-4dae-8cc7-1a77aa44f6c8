import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import '../../../../models/course_models.dart';

class LocalAssetSequenceVisualWidget extends StatefulWidget {
  final LocalAssetSequenceVisual sequenceVisual;
  final double? height;
  final double? width;

  const LocalAssetSequenceVisualWidget({
    super.key,
    required this.sequenceVisual,
    this.height,
    this.width,
  });

  @override
  State<LocalAssetSequenceVisualWidget> createState() => _LocalAssetSequenceVisualWidgetState();
}

class _LocalAssetSequenceVisualWidgetState extends State<LocalAssetSequenceVisualWidget> {
  int _currentIndex = 0;
  final PageController _pageController = PageController();

  @override
  void dispose() {
    _pageController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    if (widget.sequenceVisual.value.isEmpty) {
      return const SizedBox.shrink();
    }

    return Container(
      width: double.infinity,
      constraints: BoxConstraints(
        maxHeight: widget.height ?? MediaQuery.of(context).size.height * 0.4,
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          // Image display area
          Expanded(
            child: PageView.builder(
              controller: _pageController,
              itemCount: widget.sequenceVisual.value.length,
              onPageChanged: (index) {
                setState(() {
                  _currentIndex = index;
                });
              },
              itemBuilder: (context, index) {
                final assetPath = widget.sequenceVisual.value[index];
                return _buildAssetWidget(assetPath);
              },
            ),
          ),
          
          // Navigation controls
          if (widget.sequenceVisual.value.length > 1)
            Padding(
              padding: const EdgeInsets.only(top: 12.0),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  // Previous button
                  IconButton(
                    icon: const Icon(Icons.arrow_back_ios_rounded),
                    onPressed: _currentIndex > 0
                        ? () {
                            _pageController.previousPage(
                              duration: const Duration(milliseconds: 300),
                              curve: Curves.easeInOut,
                            );
                          }
                        : null,
                    color: _currentIndex > 0
                        ? Theme.of(context).primaryColor
                        : Colors.grey.shade400,
                  ),
                  
                  // Page indicator
                  Padding(
                    padding: const EdgeInsets.symmetric(horizontal: 16.0),
                    child: Text(
                      '${_currentIndex + 1}/${widget.sequenceVisual.value.length}',
                      style: TextStyle(
                        fontSize: 14,
                        fontWeight: FontWeight.w500,
                        color: Colors.grey.shade700,
                      ),
                    ),
                  ),
                  
                  // Next button
                  IconButton(
                    icon: const Icon(Icons.arrow_forward_ios_rounded),
                    onPressed: _currentIndex < widget.sequenceVisual.value.length - 1
                        ? () {
                            _pageController.nextPage(
                              duration: const Duration(milliseconds: 300),
                              curve: Curves.easeInOut,
                            );
                          }
                        : null,
                    color: _currentIndex < widget.sequenceVisual.value.length - 1
                        ? Theme.of(context).primaryColor
                        : Colors.grey.shade400,
                  ),
                ],
              ),
            ),
          
          // Dots indicator
          if (widget.sequenceVisual.value.length > 1)
            Padding(
              padding: const EdgeInsets.only(top: 8.0),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: List.generate(
                  widget.sequenceVisual.value.length,
                  (index) => Container(
                    width: 8,
                    height: 8,
                    margin: const EdgeInsets.symmetric(horizontal: 4),
                    decoration: BoxDecoration(
                      shape: BoxShape.circle,
                      color: index == _currentIndex
                          ? Theme.of(context).primaryColor
                          : Colors.grey.shade300,
                    ),
                  ),
                ),
              ),
            ),
        ],
      ),
    );
  }

  Widget _buildAssetWidget(String assetPath) {
    Widget imageWidget;
    bool isSvg = assetPath.toLowerCase().endsWith('.svg');

    try {
      if (isSvg) {
        imageWidget = SvgPicture.asset(
          assetPath,
          fit: BoxFit.contain,
          placeholderBuilder: (BuildContext context) => Container(
            padding: const EdgeInsets.all(8.0),
            child: const Center(
              child: Icon(Icons.broken_image, size: 40, color: Colors.grey),
            ),
          ),
        );
      } else {
        imageWidget = Image.asset(
          assetPath,
          fit: BoxFit.contain,
          errorBuilder: (
            BuildContext context,
            Object exception,
            StackTrace? stackTrace,
          ) {
            return Container(
              padding: const EdgeInsets.all(8.0),
              child: Center(
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    const Icon(
                      Icons.error_outline,
                      size: 40,
                      color: Colors.redAccent,
                    ),
                    const SizedBox(height: 8),
                    Text(
                      'Error loading: ${assetPath.split('/').last}',
                      textAlign: TextAlign.center,
                      style: const TextStyle(
                        fontSize: 12,
                        color: Colors.redAccent,
                      ),
                    ),
                  ],
                ),
              ),
            );
          },
        );
      }
    } catch (e) {
      // Fallback for any other error during asset loading attempt
      imageWidget = Container(
        padding: const EdgeInsets.all(8.0),
        child: Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              const Icon(
                Icons.hide_image_outlined,
                size: 40,
                color: Colors.grey,
              ),
              const SizedBox(height: 8),
              Text(
                'Asset not found: ${assetPath.split('/').last}',
                textAlign: TextAlign.center,
                style: const TextStyle(fontSize: 12, color: Colors.grey),
              ),
            ],
          ),
        ),
      );
    }

    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 16.0),
      child: Center(child: imageWidget),
    );
  }
}
