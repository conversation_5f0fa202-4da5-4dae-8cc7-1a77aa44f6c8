{"id": "rfr_modal_logic", "title": "Modal Logic: Reasoning About Possibility and Necessity", "description": "Explore logical systems that allow reasoning about what is possible, necessary, or obligatory.", "order": 3, "lessons": [{"id": "rfr-mol-l1-introducing-modalities", "title": "Introducing Modalities: Possibility, Necessity, Obligation", "description": "Understand the basic concepts.", "order": 1, "type": "interactive_lesson", "estimatedTimeMinutes": 15, "xp_reward": 100, "contentBlocks": [{"id": "rfr-mol-l1-s1", "type": "lesson_screen", "order": 1, "estimatedTimeSeconds": 30, "content": {"headline": "Beyond True and False", "body_md": "So far, we've dealt with statements that are simply true or false. But what about statements like:\n*   'It **might** rain tomorrow.'\n*   'It is **necessary** that 2+2=4.'\n*   'You **ought** to tell the truth.'\n\nThese involve concepts of possibility, necessity, and obligation. **Modal Logic** gives us tools to reason about them!", "visual": {"type": "giphy_search", "value": "thinking cap"}, "interactive_element": {"type": "button", "button_text": "Let's Explore!"}}}, {"id": "rfr-mol-l1-s2", "type": "lesson_screen", "order": 2, "estimatedTimeSeconds": 60, "content": {"headline": "The Modal Operators", "body_md": "Modal logic introduces special operators:\n\n*   **Possibility (Possibly P):** Symbol: **◇P** (diamond P)\n    *   Means: 'It is possible that P is true.'\n    *   Example: `◇(I win the lottery)`\n\n*   **Necessity (Necessarily P):** Symbol: **□P** (box P)\n    *   Means: 'It is necessary that P is true.' / 'P must be true.'\n    *   Example: `□(All bachelors are unmarried)`", "visual": {"type": "static_text", "value": "◇P  (Possibly P)\n□P  (Necessarily P)"}, "interactive_element": {"type": "button", "button_text": "How are they related?"}}}, {"id": "rfr-mol-l1-s3", "type": "lesson_screen", "order": 3, "estimatedTimeSeconds": 75, "content": {"headline": "Relating Possibility and Necessity", "body_md": "Possibility and necessity are interdefinable, much like ∀ and ∃ in predicate logic:\n\n*   `□P` is equivalent to `¬◇¬P`\n    *   'P is necessary' means 'It is NOT possible that P is NOT true.'\n    *   Example: 'It must rain' means 'It's not possible that it won't rain.'\n\n*   `◇P` is equivalent to `¬□¬P`\n    *   'P is possible' means 'It is NOT necessary that P is NOT true.'\n    *   Example: 'It might rain' means 'It's not necessary that it won't rain.'", "visual": {"type": "giphy_search", "value": "connected ideas"}, "interactive_element": {"type": "multiple_choice_text", "question_text": "If 'It is necessary that all squares have four sides' (□S), what is an equivalent statement using possibility (◇)?", "options": [{"id": "opt1", "text": "◇(Squares have four sides)", "is_correct": false, "feedback": "This just says it's possible, not that it's equivalent to the necessary statement."}, {"id": "opt2", "text": "¬◇¬(Squares have four sides)", "is_correct": true, "feedback": "Correct! 'It is necessary that S' is the same as 'It is not possible that not-S'."}, {"id": "opt3", "text": "◇¬(Squares have four sides)", "is_correct": false, "feedback": "This means 'It is possible that squares do not have four sides,' which contradicts the original necessary statement."}], "action_button_text": "Check Answer"}}}, {"id": "rfr-mol-l1-s4", "type": "lesson_screen", "order": 4, "estimatedTimeSeconds": 60, "content": {"headline": "Other Modalities: Deontic Logic", "body_md": "Modal logic isn't just about possibility and necessity (these are called **alethic** modalities).\n\n**Deontic Logic** deals with obligation and permission:\n* **O(P):** 'It is obligatory that P.' (e.g., `O(You pay taxes)`)\n* **P(P):** 'It is permissible that P.' (e.g., `P(<PERSON> park here)`)\n* **F(P):** 'It is forbidden that P.' (equivalent to `O(¬P)` or `¬P(P)`)\n\nThese also have interrelations, e.g., `P(P)` is equivalent to `¬O(¬P)` ('It's permissible to P' means 'It's not obligatory not to P').", "visual": {"type": "giphy_search", "value": "rules law"}, "interactive_element": {"type": "button", "button_text": "Interesting! Next?"}}}]}, {"id": "rfr-mol-l2-possible-worlds", "title": "Possible Worlds Semantics (Intuitive)", "description": "Visualize different scenarios.", "order": 2, "type": "interactive_lesson", "estimatedTimeMinutes": 20, "xp_reward": 120, "contentBlocks": [{"id": "rfr-mol-l2-s1", "type": "lesson_screen", "order": 1, "estimatedTimeSeconds": 45, "content": {"headline": "What are 'Possible Worlds'?", "body_md": "To make sense of modal claims, logicians often use the idea of **possible worlds**. \n\nThink of a 'possible world' as a complete way things *could have been*. \n*   The **actual world** is the one we live in.\n*   Other possible worlds are alternative scenarios (e.g., a world where you chose a different breakfast, a world where dinosaurs still exist).", "visual": {"type": "unsplash_search", "value": "multiple universes fantasy"}, "interactive_element": {"type": "button", "button_text": "How does this help?"}}}, {"id": "rfr-mol-l2-s2", "type": "lesson_screen", "order": 2, "estimatedTimeSeconds": 75, "content": {"headline": "Modality via Possible Worlds", "body_md": "Using possible worlds, we can define modal operators:\n\n*   **`◇P` (P is possible):** 'P is true in **at least one** possible world accessible from the actual world.'\n    *   _Example: 'It's possible I'll win the lottery' means there's some accessible possible world where I do._\n\n*   **`□P` (P is necessary):** 'P is true in **all** possible worlds accessible from the actual world.'\n    *   _Example: 'It's necessary that 2+2=4' means 2+2=4 in all accessible possible worlds._", "visual": {"type": "static_text", "value": "◇P: True in SOME world\n□P: True in ALL worlds"}, "interactive_element": {"type": "button", "button_text": "Accessibility?"}}}, {"id": "rfr-mol-l2-s3", "type": "lesson_screen", "order": 3, "estimatedTimeSeconds": 90, "content": {"headline": "The Accessibility Relation", "body_md": "The concept of an **accessibility relation** (R) between worlds is crucial. It tells us which worlds are 'possible alternatives' relative to a given world.\n\n*   `w1 R w2` means world `w2` is accessible from world `w1`.\n*   The properties of this relation (e.g., reflexive, symmetric, transitive) define different modal systems and affect what is considered possible or necessary.\n\nFor example, if we're talking about logical necessity, a world is accessible if it's logically consistent. If we're talking about physical necessity, a world is accessible if it obeys the same laws of physics.", "visual": {"type": "giphy_search", "value": "network connection"}, "interactive_element": {"type": "interactive_possible_worlds_explorer", "prompt": "Imagine 3 worlds: W1 (actual), W2, W3. \nAccessibility: W1 can see W2. W2 can see W3. \nIf P is true only in W3, is ◇P true in W1? (Assume transitivity for 'can see')", "worlds": [{"id": "w1", "name": "Actual World (W1)"}, {"id": "w2", "name": "World 2 (W2)"}, {"id": "w3", "name": "World 3 (W3)"}], "accessibility_relations": [{"from": "w1", "to": "w2"}, {"from": "w2", "to": "w3"}], "statement_truth_conditions": [{"world_id": "w3", "statement": "P", "is_true": true}], "question": "If P is true only in W3, and accessibility is transitive (if W1 can 'see' W2, and W2 can 'see' W3, then W1 can 'see' W3), is ◇P true in W1?", "correct_answer": true, "explanation_correct": "Yes! If accessibility is transitive, W1 can access W3 (via W2). Since P is true in W3, ◇P is true in W1.", "explanation_incorrect": "Consider transitivity. If W1 accesses W2, and W2 accesses W3, then W1 accesses W3. P is true in W3."}}}, {"id": "rfr-mol-l2-s4", "type": "lesson_screen", "order": 4, "estimatedTimeSeconds": 60, "content": {"headline": "Visualizing Scenarios", "body_md": "Possible worlds semantics gives us a powerful way to visualize and analyze modal statements. It helps clarify the conditions under which something is considered possible, necessary, or obligatory.", "visual": {"type": "unsplash_search", "value": "crystal ball future"}, "interactive_element": {"type": "button", "button_text": "On to Axioms!"}}}]}, {"id": "rfr-mol-l3-axioms-rules", "title": "Basic Axioms and Rules of Modal Logic (Conceptual)", "description": "Understand the foundations of modal systems.", "order": 3, "type": "interactive_lesson", "estimatedTimeMinutes": 20, "xp_reward": 130, "contentBlocks": [{"id": "rfr-mol-l3-s1", "type": "lesson_screen", "order": 1, "estimatedTimeSeconds": 45, "content": {"headline": "Building Modal Systems", "body_md": "Different modal logics (systems) are built by adding specific axioms and rules to classical propositional logic. These axioms govern the behavior of the modal operators □ and ◇.", "visual": {"type": "giphy_search", "value": "building blocks"}, "interactive_element": {"type": "button", "button_text": "Show Me Some Axioms"}}}, {"id": "rfr-mol-l3-s2", "type": "lesson_screen", "order": 2, "estimatedTimeSeconds": 90, "content": {"headline": "Common Modal Axioms", "body_md": "Here are some foundational axioms (schemata):\n\n*   **K (<PERSON><PERSON><PERSON> Axiom):** `□(P → Q) → (□P → □Q)`\n    *   _If it's necessary that P implies Q, then if P is necessary, Q is also necessary._ (This is fundamental to most modal logics).\n*   **T:** `□P → P`\n    *   _If P is necessary, then P is true (in the actual world)._ This axiom is characteristic of systems where necessity implies truth.\n*   **D:** `□P → ◇P`\n    *   _If P is necessary, then P is possible._ (Ensures the world is not contradictory, i.e. something necessary isn't impossible).", "visual": {"type": "static_text", "value": "K: □(P→Q)→(□P→□Q)\nT: □P→P"}, "interactive_element": {"type": "button", "button_text": "More Axioms!"}}}, {"id": "rfr-mol-l3-s3", "type": "lesson_screen", "order": 3, "estimatedTimeSeconds": 90, "content": {"headline": "More Modal Axioms", "body_md": "*   **Axiom 4 (or B4):** `□P → □□P`\n    *   _If P is necessary, then it is necessarily necessary._ (Characteristic of systems like S4, relates to transitive accessibility relations).\n*   **Axiom 5 (or E):** `◇P → □◇P`\n    *   _If P is possible, then it is necessarily possible._ (Characteristic of systems like S5, relates to Euclidean/symmetric & transitive accessibility).\n*   **B (Brouwerian Axiom):** `P → □◇P`\n    *   _If P is true, then it is necessarily possible._ (Relates to symmetric accessibility relations).", "visual": {"type": "giphy_search", "value": "complex formula"}, "interactive_element": {"type": "button", "button_text": "What about Rules?"}}}, {"id": "rfr-mol-l3-s4", "type": "lesson_screen", "order": 4, "estimatedTimeSeconds": 75, "content": {"headline": "Rule of Necessitation (N)", "body_md": "Besides axioms, modal systems use rules of inference. A key one is the **Rule of Necessitation (N)**:\n\n*   If `P` is a theorem (i.e., provable from axioms alone, a logical truth), then `□P` is also a theorem.\n*   Symbolically: If `⊢ P`, then `⊢ □P`.\n\n_Intuition: If something is a fundamental logical truth, it must be necessarily true._\n\nStandard propositional rules like Mo<PERSON>nens (`P, P → Q ⊢ Q`) are also part of modal systems.", "visual": {"type": "static_text", "value": "If ⊢ P, then ⊢ □P"}, "interactive_element": {"type": "multiple_choice_text", "question_text": "If `(A ∧ B) → A` is a propositional tautology (a theorem), what can we infer using the Rule of Necessitation?", "options": [{"id": "n_opt1", "text": "◇((A ∧ B) → A)", "is_correct": false, "feedback": "Necessitation allows us to infer necessity (□), not just possibility (◇)."}, {"id": "n_opt2", "text": "□((A ∧ B) → A)", "is_correct": true, "feedback": "Correct! Since (A ∧ B) → A is a theorem of logic, it must be necessarily true."}, {"id": "n_opt3", "text": "□(A ∧ B) → □A", "is_correct": false, "feedback": "This looks like axiom K, but necessitation applies to already proven theorems."}], "action_button_text": "Check Inference"}}}, {"id": "rfr-mol-l3-s5", "type": "lesson_screen", "order": 5, "estimatedTimeSeconds": 45, "content": {"headline": "Different Systems, Different Strengths", "body_md": "By combining different axioms (like K, T, D, 4, 5, B) with necessitation and propositional logic, we get various modal systems (e.g., K, T, S4, S5). Each system captures a different notion of necessity/possibility and corresponds to different properties of the accessibility relation in possible worlds semantics.", "visual": {"type": "giphy_search", "value": "network diagram"}, "interactive_element": {"type": "button", "button_text": "Next Topic!"}}}]}, {"id": "rfr-mol-l4-epistemic-logic", "title": "Reasoning About Knowledge and Belief (Epistemic Logic - Introduction)", "description": "Explore the logic of what we know.", "order": 4, "type": "interactive_lesson", "estimatedTimeMinutes": 15, "xp_reward": 110, "contentBlocks": [{"id": "rfr-mol-l4-s1", "type": "lesson_screen", "order": 1, "estimatedTimeSeconds": 45, "content": {"headline": "Logic of Knowing", "body_md": "**Epistemic Logic** is a branch of modal logic that deals with reasoning about knowledge and belief.\n\nInstead of `□` for necessity, we often use `K` for 'knows' or `B` for 'believes'.\n*   `K_a P` means 'Agent *a* knows that P.'\n*   `B_a P` means 'Agent *a* believes that P.'", "visual": {"type": "unsplash_search", "value": "person thinking brain"}, "interactive_element": {"type": "button", "button_text": "How does 'knows' work?"}}}, {"id": "rfr-mol-l4-s2", "type": "lesson_screen", "order": 2, "estimatedTimeSeconds": 75, "content": {"headline": "Axioms for Knowledge", "body_md": "A common axiom for knowledge is the **Truth Axiom (T for knowledge):**\n`K_a P → P`\n*   _If agent *a* knows P, then P must be true._ You can't know something false!\n\nAnother is the **Positive Introspection Axiom (4 for knowledge):**\n`K_a P → K_a K_a P`\n*   _If agent *a* knows P, then agent *a* knows that they know P._ (The 'KK' principle).\n\nAnd the **Negative Introspection Axiom (5 for knowledge):**\n`¬K_a P → K_a ¬K_a P`\n*   _If agent *a* doesn't know P, then agent *a* knows that they don't know P._ (This one is more controversial).", "visual": {"type": "static_text", "value": "KaP → P\nKaP → KaKaP"}, "interactive_element": {"type": "button", "button_text": "What about belief?"}}}, {"id": "rfr-mol-l4-s3", "type": "lesson_screen", "order": 3, "estimatedTimeSeconds": 60, "content": {"headline": "Axioms for Belief", "body_md": "For belief, the Truth Axiom (`B_a P → P`) is generally NOT accepted, because people can have false beliefs.\n\nHowever, a common axiom for belief is **Consistency (D for belief):**\n`B_a P → ¬B_a ¬P`\n*   _If agent *a* believes P, then agent *a* does not believe not-P._ (Assumes rational believers don't hold contradictory beliefs simultaneously).", "visual": {"type": "giphy_search", "value": "false belief"}, "interactive_element": {"type": "multiple_choice_text", "question_text": "If <PERSON> believes it will rain (`<PERSON>_<PERSON> Rain`), does the Consistency Axiom for belief imply it *will* rain?", "options": [{"id": "b_opt1", "text": "Yes, if she believes it, it must be true.", "is_correct": false, "feedback": "The Consistency Axiom for belief (D) doesn't guarantee truth, only that she doesn't also believe it *won't* rain."}, {"id": "b_opt2", "text": "No, it only implies she doesn't also believe it *won't* rain.", "is_correct": true, "feedback": "Correct! `<PERSON>_<PERSON> → ¬B_<PERSON> ¬Rain`. Her belief doesn't make it true."}], "action_button_text": "Check Implication"}}}, {"id": "rfr-mol-l4-s4", "type": "lesson_screen", "order": 4, "estimatedTimeSeconds": 45, "content": {"headline": "Applications of Epistemic Logic", "body_md": "Epistemic logic is used in:\n*   **Artificial Intelligence:** Modeling knowledge of agents in multi-agent systems.\n*   **Philosophy:** Analyzing concepts of knowledge, belief, and justification.\n*   **Economics and Game Theory:** Reasoning about what players know about each other's knowledge.\n*   **Computer Security:** Analyzing security protocols by reasoning about what an attacker might know.", "visual": {"type": "unsplash_search", "value": "AI robot thinking"}, "interactive_element": {"type": "button", "button_text": "Fascinating!"}}}]}, {"id": "rfr-mol-l5-applications-modal-logic", "title": "Applications of Modal Logic", "description": "Explore uses in philosophy, computer science, and linguistics.", "order": 5, "type": "interactive_lesson", "estimatedTimeMinutes": 10, "xp_reward": 90, "contentBlocks": [{"id": "rfr-mol-l5-s1", "type": "lesson_screen", "order": 1, "estimatedTimeSeconds": 45, "content": {"headline": "Modal Logic in the Wild", "body_md": "Modal logic, with its various branches, has found applications in a surprisingly wide range of fields. Its ability to formalize nuanced concepts like possibility, necessity, knowledge, obligation, and time makes it very versatile.", "visual": {"type": "giphy_search", "value": "global connections"}, "interactive_element": {"type": "button", "button_text": "List Some Applications"}}}, {"id": "rfr-mol-l5-s2", "type": "lesson_screen", "order": 2, "estimatedTimeSeconds": 75, "content": {"headline": "Key Application Areas", "body_md": "*   **Philosophy:** Analyzing metaphysical arguments about necessity and possibility, ethics (deontic logic), epistemology (epistemic logic).\n*   **Computer Science:**\n    *   _AI:_ Knowledge representation, planning.\n    *   _Verification:_ Proving correctness of hardware/software (temporal logic, a type of modal logic for time).\n    *   _Databases:_ Modeling constraints and possibilities.\n*   **Linguistics:** Analyzing the semantics of modal verbs (can, must, may, should) and modal adverbs (possibly, necessarily).", "visual": {"type": "unsplash_search", "value": "diverse group working"}, "interactive_element": {"type": "button", "button_text": "One More Example?"}}}, {"id": "rfr-mol-l5-s3", "type": "lesson_screen", "order": 3, "estimatedTimeSeconds": 60, "content": {"headline": "Temporal Logic in CS", "body_md": "**Temporal Logic** is a specific kind of modal logic used to reason about propositions qualified in terms of time (e.g., 'P will eventually be true', 'P will always be true').\n\nIt's crucial in computer science for specifying and verifying the behavior of concurrent systems, reactive systems, and hardware circuits over time. For example, ensuring a system never enters an unsafe state.", "visual": {"type": "giphy_search", "value": "time clock"}, "interactive_element": {"type": "multiple_choice_text", "question_text": "Which branch of modal logic is particularly useful for verifying that a computer program will eventually terminate?", "options": [{"id": "app_opt1", "text": "Deontic Logic", "is_correct": false, "feedback": "Deontic logic deals with obligation and permission, not directly with time-based properties like termination."}, {"id": "app_opt2", "text": "Temporal Logic", "is_correct": true, "feedback": "Correct! Temporal logic is designed to reason about properties over time, such as 'eventually P' (liveness properties like termination)."}, {"id": "app_opt3", "text": "Alethic Logic", "is_correct": false, "feedback": "Alethic logic deals with general possibility and necessity, not specifically with time-ordered events."}], "action_button_text": "Check Answer"}}}]}], "module_test": {"id": "rfr-mol-mt1-modal-explorer", "title": "Modal Explorer", "description": "Understand the basic concepts and applications of modal logic.", "order": 1, "type": "module_test_interactive", "estimatedTimeMinutes": 15, "xp_reward": 200, "contentBlocks": [{"id": "rfr-mol-mt1-q1", "type": "question_screen", "order": 1, "estimatedTimeSeconds": 90, "content": {"headline": "Question 1: Necessity and Possibility", "body_md": "If a statement `P` is **necessary** (`□P`), what can we definitively say about its possibility (`◇P`) in standard modal systems (like system D, T, S4, S5)?", "visual": {"type": "static_text", "value": "□P → ?"}, "interactive_element": {"type": "multiple_choice_text", "question_text": "If `□P` is true, then:", "options": [{"id": "q1_opt1", "text": "`◇P` must be true.", "is_correct": true, "feedback": "Correct! If something is necessary, it must also be possible. This is captured by axiom D (□P → ◇P), which is part of many standard systems."}, {"id": "q1_opt2", "text": "`◇P` must be false.", "is_correct": false, "feedback": "Incorrect. If something is necessary, it can't be impossible."}, {"id": "q1_opt3", "text": "`◇P` could be true or false, we don't know.", "is_correct": false, "feedback": "Incorrect. There's a definite relationship in standard systems."}], "action_button_text": "Submit Answer"}}}, {"id": "rfr-mol-mt1-q2", "type": "question_screen", "order": 2, "estimatedTimeSeconds": 120, "content": {"headline": "Question 2: Possible Worlds", "body_md": "Consider three possible worlds: W1 (our actual world), W2, and W3.\nThe accessibility relation is as follows: W1 can access W2. W2 can access W3. W1 CANNOT directly access W3 (unless transitivity is assumed).\n\nA statement 'P' is true ONLY in W3.\n\nIs `◇P` ('P is possible') true in W1, assuming the accessibility relation is NOT necessarily transitive?", "visual": {"type": "local_asset", "value": "assets/images/reasoning/possible_worlds_q2.svg"}, "interactive_element": {"type": "multiple_choice_text", "question_text": "Is ◇P true in W1 (without assuming transitivity)?", "options": [{"id": "q2_opt1", "text": "Yes, because P is true in some world.", "is_correct": false, "feedback": "Not necessarily from W1's perspective. ◇P is true in W1 if P is true in a world *accessible from W1*."}, {"id": "q2_opt2", "text": "No, because W3 (where P is true) is not directly accessible from W1.", "is_correct": true, "feedback": "Correct! For ◇P to be true in W1, P must be true in a world *directly accessible* from W1. Since W3 is not directly accessible and P is false in W2 (the only world W1 directly accesses), ◇P is false in W1."}, {"id": "q2_opt3", "text": "Maybe, it depends on other factors.", "is_correct": false, "feedback": "Based on the information given, we can determine this."}], "action_button_text": "Submit Answer"}}}, {"id": "rfr-mol-mt1-q3", "type": "question_screen", "order": 3, "estimatedTimeSeconds": 90, "content": {"headline": "Question 3: Epistemic Logic", "body_md": "In epistemic logic, the axiom `K_a P → P` (if agent *a* knows P, then P is true) is known as:", "visual": {"type": "giphy_search", "value": "knowledge brain"}, "interactive_element": {"type": "multiple_choice_text", "question_text": "What is the name of the axiom `K_a P → P`?", "options": [{"id": "q3_opt1", "text": "The Consistency Axiom", "is_correct": false, "feedback": "The Consistency Axiom (D) is typically `B_a P → ¬B_a ¬P` for belief, or `□P → ◇P` for alethic logic."}, {"id": "q3_opt2", "text": "The Introspection Axiom", "is_correct": false, "feedback": "Introspection axioms are like `K_a P → K_a K_a P` (positive) or `¬K_a P → K_a ¬K_a P` (negative)."}, {"id": "q3_opt3", "text": "The Truth Axiom (or Axiom T)", "is_correct": true, "feedback": "Correct! This is the Truth Axiom (T), stating that knowledge implies truth."}], "action_button_text": "Submit Answer"}}}]}}