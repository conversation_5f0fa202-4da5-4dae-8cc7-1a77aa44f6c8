import 'package:flutter/material.dart';
import '../../../models/interactive_widget_model.dart';

class InteractiveArgumentAnalyzerWidget extends StatefulWidget {
  final InteractiveWidgetModel widget;
  final Function(bool)? onStateChanged;

  const InteractiveArgumentAnalyzerWidget({
    Key? key,
    required this.widget,
    this.onStateChanged,
  }) : super(key: key);

  static InteractiveArgumentAnalyzerWidget fromData(Map<String, dynamic> data) {
    return InteractiveArgumentAnalyzerWidget(
      widget: InteractiveWidgetModel(
        id: data['id'] ?? 'interactive_argument_analyzer_1',
        name: data['name'] ?? 'Argument Analyzer',
        type: 'interactive_argument_analyzer',
        category: data['category'] ?? 'reasoning',
        description: data['description'] ?? 'Analyze and evaluate different types of arguments',
        data: data,
      ),
    );
  }

  @override
  State<InteractiveArgumentAnalyzerWidget> createState() => _InteractiveArgumentAnalyzerWidgetState();
}

class _InteractiveArgumentAnalyzerWidgetState extends State<InteractiveArgumentAnalyzerWidget> {
  // UI state
  late String _title;
  late String _description;
  late Color _primaryColor;
  late Color _secondaryColor;
  late Color _accentColor;
  late bool _isCompleted;
  late bool _showExplanation;
  late String _feedbackText;

  // Argument state
  late List<Argument> _arguments;
  late int _currentArgumentIndex;
  late Map<String, dynamic> _userAnalysis;
  late bool _hasSubmitted;

  // Form controllers
  late TextEditingController _premisesController;
  late TextEditingController _conclusionController;
  late String _selectedArgumentType;
  late String _selectedArgumentStrength;
  late List<String> _selectedFallacies;

  @override
  void initState() {
    super.initState();
    _initializeFromData();

    _premisesController = TextEditingController();
    _conclusionController = TextEditingController();
  }

  @override
  void dispose() {
    _premisesController.dispose();
    _conclusionController.dispose();
    super.dispose();
  }

  void _initializeFromData() {
    // Initialize UI state
    _title = widget.widget.data['title'] ?? 'Argument Analyzer';
    _description = widget.widget.data['description'] ?? 'Analyze and evaluate different types of arguments';
    _primaryColor = _colorFromHex(widget.widget.data['primaryColor'] ?? '#2196F3');
    _secondaryColor = _colorFromHex(widget.widget.data['secondaryColor'] ?? '#4CAF50');
    _accentColor = _colorFromHex(widget.widget.data['accentColor'] ?? '#FF9800');
    _isCompleted = false;
    _showExplanation = false;
    _feedbackText = '';
    _hasSubmitted = false;

    // Initialize argument state
    final List<dynamic> argumentsData = widget.widget.data['arguments'] ?? [];
    _arguments = argumentsData.map((data) => Argument.fromJson(data)).toList();
    _currentArgumentIndex = 0;
    _userAnalysis = {};

    // Initialize form state
    _selectedArgumentType = '';
    _selectedArgumentStrength = '';
    _selectedFallacies = [];

    // If no arguments provided, add a sample one
    if (_arguments.isEmpty) {
      _arguments.add(Argument(
        id: 'sample_argument',
        text: 'All humans need oxygen to survive. Socrates is human. Therefore, Socrates needs oxygen to survive.',
        premises: ['All humans need oxygen to survive', 'Socrates is human'],
        conclusion: 'Socrates needs oxygen to survive',
        argumentType: 'deductive',
        argumentStrength: 'strong',
        fallacies: [],
        explanation: 'This is a valid deductive argument in the form of a syllogism. The premises logically lead to the conclusion.',
      ));
    }
  }

  Color _colorFromHex(String hexColor) {
    hexColor = hexColor.replaceAll('#', '');
    if (hexColor.length == 6) {
      hexColor = 'FF$hexColor';
    }
    return Color(int.parse(hexColor, radix: 16));
  }

  void _selectArgumentType(String type) {
    setState(() {
      _selectedArgumentType = type;
    });
  }

  void _selectArgumentStrength(String strength) {
    setState(() {
      _selectedArgumentStrength = strength;
    });
  }

  void _toggleFallacy(String fallacy) {
    setState(() {
      if (_selectedFallacies.contains(fallacy)) {
        _selectedFallacies.remove(fallacy);
      } else {
        _selectedFallacies.add(fallacy);
      }
    });
  }

  void _submitAnalysis() {
    if (_hasSubmitted) return;

    final currentArgument = _arguments[_currentArgumentIndex];

    // Check if analysis is correct
    final bool typeCorrect = _selectedArgumentType == currentArgument.argumentType;
    final bool strengthCorrect = _selectedArgumentStrength == currentArgument.argumentStrength;

    // Check fallacies
    bool fallaciesCorrect = true;
    if (currentArgument.fallacies.isEmpty) {
      fallaciesCorrect = _selectedFallacies.isEmpty;
    } else {
      // All selected fallacies must be in the correct list and all correct fallacies must be selected
      fallaciesCorrect = _selectedFallacies.length == currentArgument.fallacies.length &&
                         _selectedFallacies.every((f) => currentArgument.fallacies.contains(f));
    }

    final bool isCorrect = typeCorrect && strengthCorrect && fallaciesCorrect;

    // Store user's analysis
    _userAnalysis = {
      'argumentType': _selectedArgumentType,
      'argumentStrength': _selectedArgumentStrength,
      'fallacies': List<String>.from(_selectedFallacies),
      'isCorrect': isCorrect,
    };

    setState(() {
      _hasSubmitted = true;
      _feedbackText = isCorrect
          ? 'Great job! Your analysis is correct.'
          : 'Your analysis has some errors. Review the explanation and try again.';

      if (_currentArgumentIndex == _arguments.length - 1) {
        _isCompleted = true;
        widget.onStateChanged?.call(true);
      }
    });
  }

  void _nextArgument() {
    if (_currentArgumentIndex < _arguments.length - 1) {
      setState(() {
        _currentArgumentIndex++;
        _resetForm();
      });
    }
  }

  void _resetForm() {
    setState(() {
      _premisesController.text = '';
      _conclusionController.text = '';
      _selectedArgumentType = '';
      _selectedArgumentStrength = '';
      _selectedFallacies = [];
      _hasSubmitted = false;
      _showExplanation = false;
      _feedbackText = '';
    });
  }

  void _toggleExplanation() {
    setState(() {
      _showExplanation = !_showExplanation;
    });
  }

  @override
  Widget build(BuildContext context) {
    final currentArgument = _arguments[_currentArgumentIndex];

    return Card(
      elevation: 4,
      margin: const EdgeInsets.all(16),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              _title,
              style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                color: _primaryColor,
              ),
            ),
            const SizedBox(height: 8),
            Text(
              _description,
              style: Theme.of(context).textTheme.bodyMedium,
            ),
            const SizedBox(height: 16),

            // Argument text
            Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: _primaryColor.withOpacity(0.1),
                borderRadius: BorderRadius.circular(8),
                border: Border.all(color: _primaryColor.withOpacity(0.3)),
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'Argument ${_currentArgumentIndex + 1}/${_arguments.length}',
                    style: TextStyle(
                      fontWeight: FontWeight.bold,
                      color: _primaryColor,
                    ),
                  ),
                  const SizedBox(height: 8),
                  Text(
                    currentArgument.text,
                    style: const TextStyle(
                      fontSize: 16,
                      fontStyle: FontStyle.italic,
                    ),
                  ),
                ],
              ),
            ),
            const SizedBox(height: 24),

            // Analysis form
            Text(
              'Analyze the Argument',
              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 16),

            // Argument type selection
            Text(
              'What type of argument is this?',
              style: const TextStyle(fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 8),
            Wrap(
              spacing: 8,
              children: [
                _buildSelectionChip('Deductive', 'deductive'),
                _buildSelectionChip('Inductive', 'inductive'),
                _buildSelectionChip('Abductive', 'abductive'),
              ],
            ),
            const SizedBox(height: 16),

            // Argument strength selection
            Text(
              'How would you rate this argument?',
              style: const TextStyle(fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 8),
            Wrap(
              spacing: 8,
              children: [
                _buildStrengthChip('Strong', 'strong'),
                _buildStrengthChip('Moderate', 'moderate'),
                _buildStrengthChip('Weak', 'weak'),
              ],
            ),
            const SizedBox(height: 16),

            // Fallacy selection
            Text(
              'Does this argument contain any fallacies? (Select all that apply)',
              style: const TextStyle(fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 8),
            Wrap(
              spacing: 8,
              runSpacing: 8,
              children: [
                _buildFallacyChip('None', 'none'),
                _buildFallacyChip('Ad Hominem', 'ad_hominem'),
                _buildFallacyChip('Straw Man', 'straw_man'),
                _buildFallacyChip('False Dichotomy', 'false_dichotomy'),
                _buildFallacyChip('Slippery Slope', 'slippery_slope'),
                _buildFallacyChip('Appeal to Authority', 'appeal_to_authority'),
                _buildFallacyChip('Circular Reasoning', 'circular_reasoning'),
              ],
            ),
            const SizedBox(height: 24),

            // Submit button
            Center(
              child: ElevatedButton(
                onPressed: _hasSubmitted ? null : _submitAnalysis,
                style: ElevatedButton.styleFrom(
                  backgroundColor: _primaryColor,
                  padding: const EdgeInsets.symmetric(horizontal: 32, vertical: 12),
                ),
                child: const Text('Submit Analysis'),
              ),
            ),

            // Feedback section
            if (_feedbackText.isNotEmpty)
              Container(
                margin: const EdgeInsets.only(top: 16),
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: _userAnalysis['isCorrect'] ? Colors.green.shade100 : Colors.orange.shade100,
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      children: [
                        Icon(
                          _userAnalysis['isCorrect'] ? Icons.check_circle : Icons.info,
                          color: _userAnalysis['isCorrect'] ? Colors.green : Colors.orange,
                        ),
                        const SizedBox(width: 8),
                        Expanded(
                          child: Text(
                            _feedbackText,
                            style: const TextStyle(fontWeight: FontWeight.bold),
                          ),
                        ),
                      ],
                    ),
                    if (_hasSubmitted)
                      Padding(
                        padding: const EdgeInsets.only(top: 8),
                        child: Row(
                          mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                          children: [
                            TextButton.icon(
                              onPressed: _toggleExplanation,
                              icon: Icon(_showExplanation ? Icons.visibility_off : Icons.visibility),
                              label: Text(_showExplanation ? 'Hide Explanation' : 'Show Explanation'),
                            ),
                            if (_currentArgumentIndex < _arguments.length - 1)
                              TextButton.icon(
                                onPressed: _nextArgument,
                                icon: const Icon(Icons.arrow_forward),
                                label: const Text('Next Argument'),
                              ),
                          ],
                        ),
                      ),
                  ],
                ),
              ),

            // Explanation section
            if (_showExplanation && _hasSubmitted)
              Container(
                margin: const EdgeInsets.only(top: 16),
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: Colors.blue.shade50,
                  borderRadius: BorderRadius.circular(8),
                  border: Border.all(color: Colors.blue.shade200),
                ),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Explanation',
                      style: TextStyle(
                        fontWeight: FontWeight.bold,
                        color: _primaryColor,
                        fontSize: 16,
                      ),
                    ),
                    const SizedBox(height: 8),
                    Text(currentArgument.explanation),
                    const SizedBox(height: 12),
                    Text(
                      'Correct Analysis:',
                      style: const TextStyle(fontWeight: FontWeight.bold),
                    ),
                    const SizedBox(height: 4),
                    Text('Type: ${_capitalizeFirstLetter(currentArgument.argumentType)}'),
                    Text('Strength: ${_capitalizeFirstLetter(currentArgument.argumentStrength)}'),
                    Text(
                      'Fallacies: ${currentArgument.fallacies.isEmpty ? "None" : currentArgument.fallacies.map(_capitalizeFirstLetter).join(", ")}',
                    ),
                  ],
                ),
              ),
          ],
        ),
      ),
    );
  }

  Widget _buildSelectionChip(String label, String value) {
    final bool isSelected = _selectedArgumentType == value;
    final bool isCorrect = _hasSubmitted && value == _arguments[_currentArgumentIndex].argumentType;
    final bool isIncorrect = _hasSubmitted && isSelected && !isCorrect;

    return ChoiceChip(
      label: Text(label),
      selected: isSelected,
      onSelected: _hasSubmitted ? null : (selected) {
        if (selected) {
          _selectArgumentType(value);
        }
      },
      backgroundColor: Colors.grey.shade200,
      selectedColor: _hasSubmitted
          ? (isCorrect ? Colors.green.shade300 : Colors.red.shade300)
          : _primaryColor.withOpacity(0.7),
      labelStyle: TextStyle(
        color: isSelected ? Colors.white : Colors.black,
        fontWeight: isSelected ? FontWeight.bold : FontWeight.normal,
      ),
    );
  }

  Widget _buildStrengthChip(String label, String value) {
    final bool isSelected = _selectedArgumentStrength == value;
    final bool isCorrect = _hasSubmitted && value == _arguments[_currentArgumentIndex].argumentStrength;
    final bool isIncorrect = _hasSubmitted && isSelected && !isCorrect;

    return ChoiceChip(
      label: Text(label),
      selected: isSelected,
      onSelected: _hasSubmitted ? null : (selected) {
        if (selected) {
          _selectArgumentStrength(value);
        }
      },
      backgroundColor: Colors.grey.shade200,
      selectedColor: _hasSubmitted
          ? (isCorrect ? Colors.green.shade300 : Colors.red.shade300)
          : _secondaryColor.withOpacity(0.7),
      labelStyle: TextStyle(
        color: isSelected ? Colors.white : Colors.black,
        fontWeight: isSelected ? FontWeight.bold : FontWeight.normal,
      ),
    );
  }

  Widget _buildFallacyChip(String label, String value) {
    // Special case for "None" - it should be exclusive
    if (value == 'none') {
      final bool isSelected = _selectedFallacies.isEmpty;
      final bool isCorrect = _hasSubmitted && _arguments[_currentArgumentIndex].fallacies.isEmpty;
      final bool isIncorrect = _hasSubmitted && isSelected && !isCorrect;

      return FilterChip(
        label: Text(label),
        selected: isSelected,
        onSelected: _hasSubmitted ? null : (selected) {
          if (selected) {
            setState(() {
              _selectedFallacies = [];
            });
          }
        },
        backgroundColor: Colors.grey.shade200,
        selectedColor: _hasSubmitted
            ? (isCorrect ? Colors.green.shade300 : Colors.red.shade300)
            : _accentColor.withOpacity(0.7),
        labelStyle: TextStyle(
          color: isSelected ? Colors.white : Colors.black,
          fontWeight: isSelected ? FontWeight.bold : FontWeight.normal,
        ),
      );
    }

    // Regular fallacy chips
    final bool isSelected = _selectedFallacies.contains(value);
    final bool isCorrect = _hasSubmitted &&
                          (_arguments[_currentArgumentIndex].fallacies.contains(value) && isSelected) ||
                          (!_arguments[_currentArgumentIndex].fallacies.contains(value) && !isSelected);
    final bool isIncorrect = _hasSubmitted && !isCorrect;

    return FilterChip(
      label: Text(label),
      selected: isSelected,
      onSelected: _hasSubmitted ? null : (selected) {
        if (_selectedFallacies.isEmpty && !selected) return; // At least one must be selected
        _toggleFallacy(value);
      },
      backgroundColor: Colors.grey.shade200,
      selectedColor: _hasSubmitted
          ? (isCorrect ? Colors.green.shade300 : Colors.red.shade300)
          : _accentColor.withOpacity(0.7),
      labelStyle: TextStyle(
        color: isSelected ? Colors.white : Colors.black,
        fontWeight: isSelected ? FontWeight.bold : FontWeight.normal,
      ),
    );
  }

  String _capitalizeFirstLetter(String text) {
    if (text.isEmpty) return text;
    return text[0].toUpperCase() + text.substring(1).replaceAll('_', ' ');
  }
}

class Argument {
  final String id;
  final String text;
  final List<String> premises;
  final String conclusion;
  final String argumentType;
  final String argumentStrength;
  final List<String> fallacies;
  final String explanation;

  Argument({
    required this.id,
    required this.text,
    required this.premises,
    required this.conclusion,
    required this.argumentType,
    required this.argumentStrength,
    required this.fallacies,
    required this.explanation,
  });

  factory Argument.fromJson(Map<String, dynamic> json) {
    return Argument(
      id: json['id'] ?? '',
      text: json['text'] ?? '',
      premises: List<String>.from(json['premises'] ?? []),
      conclusion: json['conclusion'] ?? '',
      argumentType: json['argumentType'] ?? '',
      argumentStrength: json['argumentStrength'] ?? '',
      fallacies: List<String>.from(json['fallacies'] ?? []),
      explanation: json['explanation'] ?? '',
    );
  }
}
