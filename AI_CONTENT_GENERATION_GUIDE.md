# AI Content Generation & Codebase Guide for Interactive Lessons

This document outlines the process used for generating interactive course content, the relevant codebase structure, and guidelines for future AI-driven content creation.

## 1. Content Generation Process Overview

The primary goal was to replace placeholder lessons with rich, interactive, AI-generated content, mimicking the style of platforms like Brilliant.org.

**Key Steps Taken:**

1.  **Requirement Analysis:**
    *   Understood the desired course structure (categories, courses, modules, lessons, tests) provided by the user.
    *   Analyzed the example "Spotting Patterns" lesson to grasp the desired screen-by-screen flow, interactivity, and visual style.

2.  **Data Structure Investigation:**
    *   Examined existing `course.json` files and module-specific JSON files to understand the initial data model.
    *   Identified the need for a more granular and descriptive JSON schema to support complex interactions.

3.  **Defining a New JSON Schema:**
    *   A new primary content block type, `lesson_screen` (and `question_screen` for tests), was conceptualized. Each represents a single interactive step for the user.
    *   Each `lesson_screen` contains:
        *   `headline`: Optional title for the screen.
        *   `body_md`: Main textual content, supporting Markdown.
        *   `visual`: An object to define visual elements.
            *   `type`: Can be `giphy_search`, `unsplash_search` (using keywords for API calls via `AssetManager`), `local_asset` (for pre-existing assets), `static_text`, or more complex custom interactive visual types (e.g., `interactive_triangle_angle_sum`).
            *   `value`: The search term, asset path, or parameters for the visual.
        *   `interactive_element`: An object defining the user interaction.
            *   `type`: Specifies the kind of interaction (e.g., `button`, `multiple_choice_text`, `text_input`, `sieve_mini_game`, `drag_drop_angle_sorter`, `interactive_plotting_game`, etc.).
            *   Additional properties specific to each interactive type (e.g., `options` for MCQs, `correct_answer_regex` for inputs, game parameters).
        *   `estimatedTimeSeconds`: Approximate time for the user to complete the screen.
    *   Lessons (`type: "interactive_lesson"`) and Tests (`type: "module_test_interactive"`) in module JSON files now contain an array of these `contentBlocks`.

4.  **Iterative Content Creation:**
    *   For each lesson and test outlined by the user:
        *   The topic was broken down into 5-10 logical, interactive screens.
        *   For each screen, educational text, visual specifications, and interactive element definitions were generated.
        *   The "Spotting Patterns" lesson was transcribed from the user's detailed example into this new JSON format.
        *   Other lessons/tests were AI-generated following this detailed screen-based structure.

5.  **File Management:**
    *   The main `assets/data/courses/categories/maths/mathematical_thinking/course.json` was updated to reflect the new module structure.
    *   New JSON files were created for each module (e.g., `art-of-logical-deduction.json`, `number-sense-and-intuition.json`) in the `assets/data/courses/categories/maths/mathematical_thinking/modules/` directory.
    *   These files were populated with the generated lesson and test content.
    *   Obsolete placeholder module JSON files were deleted.

## 2. Relevant Codebase Structure

The system relies on a combination of JSON data files and Dart services/models to load, parse, and display course content.

*   **Data Storage Path:**
    *   Course definitions: `assets/data/courses/categories/[category_id]/[course_id]/course.json`
    *   Module content: `assets/data/courses/categories/[category_id]/[course_id]/modules/[module_id].json`

*   **Key JSON Fields (Illustrative - refer to generated files for full detail):**
    *   **`course.json`:**
        *   `id`, `title`, `description`, `categoryId`, `thumbnailPath`, `difficulty`
        *   `modules`: Array of module metadata objects (`id`, `title`, `description`, `order`).
    *   **Module JSON File (e.g., `art-of-logical-deduction.json`):**
        *   `id`, `title`, `description`, `order` (for the module itself)
        *   `lessons`: Array of lesson/test objects.
            *   Lesson/Test Object: `id`, `title`, `description`, `order`, `type` (`interactive_lesson` or `module_test_interactive`), `estimatedTimeMinutes`, `contentBlocks`.
            *   `contentBlocks`: Array of `lesson_screen` or `question_screen` objects.
                *   `lesson_screen` / `question_screen`:
                    *   `id` (unique screen ID)
                    *   `type` (`lesson_screen` or `question_screen`)
                    *   `order` (screen order within the lesson/test)
                    *   `estimatedTimeSeconds`
                    *   `content`: {
                        *   `headline`: "Optional String"
                        *   `body_md`: "Markdown content..."
                        *   `visual`: { `type`: "...", `value`: "..." } (see section 1.3)
                        *   `interactive_element`: { `type`: "...", ...properties } (see section 1.3)
                        *   `audio_narration_url`: "Optional URL"
                        }

*   **Dart Services & Models:**
    *   `lib/models/course_models.dart`: This file **will require significant updates** to define Dart classes that accurately represent the new rich JSON structure (e.g., classes for `LessonScreen`, `VisualElement`, `InteractiveElement`, and all their subtypes and properties).
    *   `lib/services/course_service.dart`: Likely loads `course.json` and provides access to course/module lists. May need minor adjustments if module loading logic changes.
    *   `lib/services/json_course_content_service.dart`: Responsible for fetching and parsing the module-specific JSON files. This service will need to be updated to correctly deserialize the new `contentBlocks` structure and map it to the new Dart models.
    *   `lib/services/asset_manager.dart`: Handles fetching visuals. It already supports Giphy/Unsplash via API keys and local assets. It should be compatible with the `visual` object structure used.

*   **UI Rendering (Conceptual for Flutter Development):**
    *   `lib/screens/courses/lesson_screen.dart`: This screen will be the core renderer for the interactive lessons. It will need to:
        *   Iterate through the `contentBlocks` (screens) of a lesson.
        *   For each `lesson_screen`, dynamically render its components (headline, body, visual, interactive element) based on their types and properties.
    *   **Reusable Widgets:** A crucial part of the implementation will be developing a library of Flutter widgets to handle the various `visual.type` and `interactive_element.type` values. Examples:
        *   `VisualDisplayWidget`: Handles `giphy_search`, `unsplash_search`, `local_asset`, `static_text`, and custom interactive visuals like `interactive_triangle_angle_sum_widget`.
        *   `InteractiveElementWidget`: A base or dispatcher for different interaction types.
        *   Specific interaction widgets: `MultipleChoiceWidget`, `TextInputWidget`, `ButtonWidget`, `SieveMiniGameWidget`, `DragDropAngleSorterWidget`, `InteractivePlottingGameWidget`, `MultiplicationFactsGameWidget`, etc.
        *   Each custom game widget will encapsulate its own logic and UI.

## 3. Guidelines for Future AI Content Generation

To maintain consistency and quality, any AI continuing this work should adhere to the following:

### Course-by-Course Focus

We are now implementing a strict course-by-course approach, focusing exclusively on completing one full course before moving to the next. The current priority is to complete the Mathematical Thinking course, followed by other mathematics courses.

1. **Course Completion Sequence:**
   * Complete Mathematical Thinking course first
   * Then proceed to Equations and Algebra, Functions and Probability, and Calculus
   * Only after completing all mathematics courses, move to other categories

2. **Module Completion:**
   * Complete all interactive widgets for a module before moving to the next
   * Ensure all lessons within a module are enhanced with appropriate interactive elements
   * Test each module thoroughly before proceeding

### Content Design Guidelines

1. **Text Density:**
   * **CRITICAL:** Keep text concise and minimal - no more than 2-3 short paragraphs per screen
   * Break content into smaller, more digestible chunks
   * Use bullet points and short sentences
   * Brilliant.org style emphasizes visual learning over text-heavy explanations

2. **Interactive Elements:**
   * **ALWAYS use multiple choice** instead of input fields (except in very specific cases)
   * Prefer visual interactions over text-based ones
   * Each interactive element should have clear instructions
   * Ensure feedback is concise and helpful

3. **Navigation:**
   * Implement a single universal continue button
   * Avoid multiple buttons on the same screen
   * Ensure consistent button styling and behavior
   * Button text should be generic ("Continue" or "Next") rather than specific

4. **Visual Elements:**
   * Every screen must have an appropriate visual element
   * Visuals should directly support the concept being taught
   * Ensure proper sizing and positioning of visuals
   * Use animations and interactive visuals whenever possible

### Technical Implementation

1.  **Understand the Established JSON Schema:**
    *   Thoroughly review the structure of the newly generated JSON files, particularly the `contentBlocks`, `visual`, and `interactive_element` objects and their various `type` properties.
    *   The schema is designed to be extensible. If new interaction types are conceived, they should be clearly defined with necessary parameters.

2.  **Content Creation Strategy:**
    *   **Screen-by-Screen Flow:** Continue designing lessons as a sequence of distinct interactive screens.
    *   **Engaging Content:** Aim for a "Brilliant.org-like" style: clear explanations, engaging questions, and meaningful interactions.
    *   **Comprehensive Screen Definition:** For each screen, provide:
        *   `headline` (if applicable) - keep it short and engaging
        *   `body_md` (clear, concise, Markdown formatted) - no more than 2-3 paragraphs
        *   `visual` specification:
            *   `type`: Choose an appropriate existing type or propose a new one.
            *   `value`: Provide relevant Giphy/Unsplash search terms, paths to (conceptual) local assets, or parameters for interactive visuals.
        *   `interactive_element` specification:
            *   `type`: Prefer multiple choice over text input
            *   Include all necessary parameters for that type (e.g., options for MCQs, correct answers, feedback text, game rules/limits).
    *   **New Interactive Types:** If a novel interaction is desired that doesn't fit existing types, the AI should:
        *   Propose a new `type` name (e.g., `interactive_timeline_scrubber`).
        *   Clearly describe its intended behavior and the JSON parameters it would require. This information is vital for the Flutter developer who will implement the corresponding widget.

3.  **File and Course Management:**
    *   Store new module content in `assets/data/courses/categories/[category_id]/[course_id]/modules/[module_id].json`.
    *   If adding new courses or modules, update the relevant `course.json` file to include metadata for the new items, ensuring correct `id` and `order`.

4.  **Tool Usage (for AI interacting with a development environment):**
    *   Use `write_to_file` for creating new JSON files.
    *   Use `replace_in_file` for adding new lessons/tests to existing module files or making targeted modifications. Ensure `SEARCH` blocks are precise and based on the current file content.

5.  **Developer Collaboration:**
    *   The JSON content generated by the AI directly drives the UI. Any new `visual.type` or `interactive_element.type` will require a corresponding Flutter widget to be built. Documenting these new types and their expected functionality is crucial for the development team.

By following these guidelines, future AI contributions can seamlessly integrate with the existing structure and maintain a high standard of interactive educational content.
