import 'package:flutter/material.dart';
import 'dart:async';

class InteractivePatternGalleryWidget extends StatefulWidget {
  final List<Map<String, String>> patterns;
  final double height;
  final Duration autoScrollDuration;
  final bool enableAutoScroll;
  final bool showDescription;
  final bool showControls;

  const InteractivePatternGalleryWidget({
    Key? key,
    required this.patterns,
    this.height = 200,
    this.autoScrollDuration = const Duration(seconds: 3),
    this.enableAutoScroll = true,
    this.showDescription = true,
    this.showControls = true,
  }) : super(key: key);

  factory InteractivePatternGalleryWidget.fromData(Map<String, dynamic> data) {
    final patternsList = List<Map<String, dynamic>>.from(data['patterns'] ?? []);
    final patterns = patternsList.map((pattern) {
      return {
        'name': pattern['name'] as String? ?? '',
        'image_path': pattern['image_path'] as String? ?? '',
        'description': pattern['description'] as String? ?? '',
      };
    }).toList();

    return InteractivePatternGalleryWidget(
      patterns: patterns,
      height: data['height']?.toDouble() ?? 200,
      autoScrollDuration: Duration(
        milliseconds: data['auto_scroll_duration_ms'] ?? 3000,
      ),
      enableAutoScroll: data['enable_auto_scroll'] ?? true,
      showDescription: data['show_description'] ?? true,
      showControls: data['show_controls'] ?? true,
    );
  }

  @override
  State<InteractivePatternGalleryWidget> createState() =>
      _InteractivePatternGalleryWidgetState();
}

class _InteractivePatternGalleryWidgetState
    extends State<InteractivePatternGalleryWidget> {
  late PageController _pageController;
  int _currentPage = 0;
  Timer? _autoScrollTimer;

  @override
  void initState() {
    super.initState();
    _pageController = PageController(initialPage: 0, viewportFraction: 0.85);
    
    if (widget.enableAutoScroll) {
      _startAutoScroll();
    }

    _pageController.addListener(() {
      int next = _pageController.page!.round();
      if (_currentPage != next) {
        setState(() {
          _currentPage = next;
        });
      }
    });
  }

  void _startAutoScroll() {
    _autoScrollTimer = Timer.periodic(widget.autoScrollDuration, (timer) {
      if (_currentPage < widget.patterns.length - 1) {
        _pageController.nextPage(
          duration: const Duration(milliseconds: 500),
          curve: Curves.easeInOut,
        );
      } else {
        _pageController.animateToPage(
          0,
          duration: const Duration(milliseconds: 500),
          curve: Curves.easeInOut,
        );
      }
    });
  }

  @override
  void dispose() {
    _pageController.dispose();
    _autoScrollTimer?.cancel();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      mainAxisSize: MainAxisSize.min,
      children: [
        // Gallery
        SizedBox(
          height: widget.height,
          child: PageView.builder(
            controller: _pageController,
            itemCount: widget.patterns.length,
            itemBuilder: (context, index) {
              final pattern = widget.patterns[index];
              return _buildPatternCard(pattern, index);
            },
          ),
        ),

        // Page indicator
        if (widget.showControls && widget.patterns.length > 1)
          Padding(
            padding: const EdgeInsets.only(top: 16.0),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                // Previous button
                IconButton(
                  icon: const Icon(Icons.arrow_back_ios),
                  onPressed: _currentPage > 0
                      ? () {
                          _pageController.previousPage(
                            duration: const Duration(milliseconds: 500),
                            curve: Curves.easeInOut,
                          );
                        }
                      : null,
                  color: _currentPage > 0 ? Colors.blue : Colors.grey,
                ),

                // Page indicators
                ...List.generate(
                  widget.patterns.length,
                  (index) => Container(
                    margin: const EdgeInsets.symmetric(horizontal: 4),
                    width: 10,
                    height: 10,
                    decoration: BoxDecoration(
                      shape: BoxShape.circle,
                      color: _currentPage == index
                          ? Colors.blue
                          : Colors.grey[300],
                    ),
                  ),
                ),

                // Next button
                IconButton(
                  icon: const Icon(Icons.arrow_forward_ios),
                  onPressed: _currentPage < widget.patterns.length - 1
                      ? () {
                          _pageController.nextPage(
                            duration: const Duration(milliseconds: 500),
                            curve: Curves.easeInOut,
                          );
                        }
                      : null,
                  color: _currentPage < widget.patterns.length - 1
                      ? Colors.blue
                      : Colors.grey,
                ),
              ],
            ),
          ),
      ],
    );
  }

  Widget _buildPatternCard(Map<String, String> pattern, int index) {
    final isCurrentPage = index == _currentPage;
    
    return AnimatedContainer(
      duration: const Duration(milliseconds: 300),
      margin: EdgeInsets.symmetric(
        horizontal: 8,
        vertical: isCurrentPage ? 0 : 16,
      ),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.1),
            blurRadius: 8,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.stretch,
        children: [
          // Pattern image
          Expanded(
            child: ClipRRect(
              borderRadius: const BorderRadius.vertical(top: Radius.circular(16)),
              child: Image.asset(
                pattern['image_path'] ?? '',
                fit: BoxFit.cover,
                errorBuilder: (context, error, stackTrace) {
                  return Container(
                    color: Colors.grey[200],
                    child: Center(
                      child: Icon(
                        Icons.image_not_supported,
                        size: 48,
                        color: Colors.grey[400],
                      ),
                    ),
                  );
                },
              ),
            ),
          ),

          // Pattern name and description
          if (widget.showDescription)
            Padding(
              padding: const EdgeInsets.all(12.0),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    pattern['name'] ?? '',
                    style: const TextStyle(
                      fontSize: 18,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  if (pattern['description'] != null &&
                      pattern['description']!.isNotEmpty)
                    Padding(
                      padding: const EdgeInsets.only(top: 4.0),
                      child: Text(
                        pattern['description'] ?? '',
                        style: TextStyle(
                          fontSize: 14,
                          color: Colors.grey[600],
                        ),
                      ),
                    ),
                ],
              ),
            ),
        ],
      ),
    );
  }
}
