# Interactive Widgets Testing Guide

This guide explains how to test and verify the interactive widgets in the Brilliant-style learning app.

## How to Test Interactive Widgets

### Method 1: Using the Widget Showcase Screen

The app includes a dedicated screen for testing all interactive widgets:

1. **Launch the app** in debug mode
2. **Navigate to the Widget Showcase Screen**:
   - From the home screen, tap on the menu icon (≡)
   - Select "Widget Showcase" from the navigation drawer
   - Or use the direct route: `/widget-showcase`

3. **Browse widgets by category**:
   - All widgets are organized by subject categories (Mathematics, Science, etc.)
   - Each widget has a card with its name and description
   - Tap on any widget card to interact with it

4. **Test widget functionality**:
   - Interact with the widget controls
   - Verify that animations and transitions work correctly
   - Test edge cases (min/max values, invalid inputs)
   - Check that the widget provides appropriate feedback

### Method 2: Running Widget Tests

For automated testing of widgets:

1. **Run the widget test suite**:
   ```bash
   flutter test test/widget_test.dart
   ```

2. **Run tests for a specific widget**:
   ```bash
   flutter test test/widgets/interactive_ratio_visualizer_test.dart
   ```

### Method 3: Viewing Widgets in Lessons

To see widgets in their actual lesson context:

1. **Navigate to a specific course and lesson**:
   - From the home screen, select a course (e.g., "Mathematical Thinking")
   - Choose a module (e.g., "The Art of Logical Deduction")
   - Select a lesson (e.g., "Spotting Patterns")

2. **Scroll through the lesson** to find interactive widgets
3. **Interact with the widgets** as they appear in the lesson flow

## Widget Implementation Status

To check which widgets have been implemented:

1. **Check the `COURSE_PROGRESS.md` file**:
   - ✅ indicates a fully implemented widget
   - ⚠️ indicates a widget that needs implementation

2. **Check the `INTERACTIVE_WIDGETS_SHOWCASE.md` file** for a complete list of implemented widgets

3. **Examine the codebase**:
   - All widget implementations are in `lib/screens/interactive_widgets/widgets/`
   - The widget factory is in `lib/screens/interactive_widgets/interactive_widget_factory.dart`
   - Widget data models are in `lib/models/interactive_widget_model.dart`

## Recently Implemented Widgets

### Mathematical Thinking Module 4

1. **Interactive Growing Patterns Visualizer**
   - File: `lib/screens/interactive_widgets/widgets/interactive_growing_patterns_visualizer_widget.dart`
   - Features:
     - Visualizes arithmetic, geometric, Fibonacci, triangular, and square sequences
     - Animated step-by-step progression
     - Formula display and sequence visualization
     - Multiple visualization types (bars, triangular, square)

2. **Interactive Relationship Mapper**
   - File: `lib/screens/interactive_widgets/widgets/interactive_relationship_mapper_widget.dart`
   - Features:
     - Visualizes different types of mathematical relationships (linear, quadratic, exponential, inverse)
     - Interactive coordinate plane with adjustable parameters
     - Equation display and relationship description
     - Custom equation input option

3. **Interactive Ratio Visualizer**
   - File: `lib/screens/interactive_widgets/widgets/interactive_ratio_visualizer_widget.dart`
   - Features:
     - Visualizes ratios using different representations (blocks, pie chart, bar chart, grid)
     - Interactive controls to adjust numerator and denominator
     - Multiple display formats (fraction, decimal, percentage)
     - Animated transitions between states

## Troubleshooting Widget Issues

If you encounter issues with widgets:

1. **Check the console for error messages**
2. **Verify widget registration**:
   - Make sure the widget is properly registered in `interactive_widget_factory.dart`
   - Check that the widget type string matches between factory and implementation

3. **Inspect widget data**:
   - Verify that the widget data model contains all required fields
   - Check for null values or incorrect data types

4. **Debug widget rendering**:
   - Use Flutter DevTools to inspect the widget tree
   - Add debug prints to track widget lifecycle and state changes

## Adding New Widgets to the Showcase

To add a new widget to the showcase for testing:

1. **Create the widget implementation** in `lib/screens/interactive_widgets/widgets/`
2. **Register the widget** in `interactive_widget_factory.dart`
3. **Add widget data** to `interactive_widget_service.dart`
4. **Update the showcase screen** to include the new widget

## Next Steps for Widget Development

The current priority is to complete all widgets for the Mathematical Thinking course:

1. **Module 4: The Power of Patterns and Relationships**
   - Complete Interactive Data Visualization Tool
   - Complete Interactive Function Machine
   - Complete Interactive Pattern Prediction Puzzle (Module Test)

2. **Module 5: Exploring the World of Numbers**
   - Implement all 6 required widgets

After completing Mathematical Thinking, move on to the Equations and Algebra course widgets.

## Best Practices for Widget Testing

1. **Test on multiple screen sizes** to ensure responsive design
2. **Verify accessibility features** (contrast, text scaling, screen reader compatibility)
3. **Test performance** with Flutter DevTools
4. **Check for memory leaks** during widget disposal
5. **Validate educational effectiveness** with test users
