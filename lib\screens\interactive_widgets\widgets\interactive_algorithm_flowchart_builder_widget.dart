import 'package:flutter/material.dart';
import '../../../models/interactive_widget_model.dart';

class InteractiveAlgorithmFlowchartBuilderWidget extends StatefulWidget {
  final InteractiveWidgetModel widget;
  final Function(bool)? onStateChanged;

  const InteractiveAlgorithmFlowchartBuilderWidget({
    Key? key,
    required this.widget,
    this.onStateChanged,
  }) : super(key: key);

  static InteractiveAlgorithmFlowchartBuilderWidget fromData(Map<String, dynamic> data) {
    return InteractiveAlgorithmFlowchartBuilderWidget(
      widget: InteractiveWidgetModel(
        id: data['id'] ?? 'interactive_algorithm_flowchart_builder_1',
        name: data['name'] ?? 'Algorithm Flowchart Builder',
        type: 'interactive_algorithm_flowchart_builder',
        category: data['category'] ?? 'Computer Science',
        description: data['description'] ?? 'Build and visualize algorithm flowcharts',
        data: data,
      ),
    );
  }

  @override
  State<InteractiveAlgorithmFlowchartBuilderWidget> createState() => _InteractiveAlgorithmFlowchartBuilderWidgetState();
}

class _InteractiveAlgorithmFlowchartBuilderWidgetState extends State<InteractiveAlgorithmFlowchartBuilderWidget> with SingleTickerProviderStateMixin {
  // UI state
  late String _title;
  late String _description;
  late Color _primaryColor;
  late Color _secondaryColor;
  late Color _accentColor;
  late bool _isCompleted;
  late bool _showSolution;
  late bool _showHint;
  late String _feedbackText;

  // Flowchart state
  late List<FlowchartNode> _availableNodes;
  late List<FlowchartNode> _placedNodes;
  late List<FlowchartConnection> _connections;
  late FlowchartNode? _selectedNode;
  late FlowchartNode? _draggedNode;
  late FlowchartNode? _connectionStartNode;
  late Offset _dragPosition;

  // Challenge state
  late String _challengeTitle;
  late String _challengeDescription;
  late List<FlowchartNode> _solutionNodes;
  late List<FlowchartConnection> _solutionConnections;

  // Animation controller
  late AnimationController _animationController;

  @override
  void initState() {
    super.initState();
    _initializeFromData();

    // Initialize animation controller
    _animationController = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 500),
    );
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  void _initializeFromData() {
    // Initialize UI state
    _title = widget.widget.data['title'] ?? 'Algorithm Flowchart Builder';
    _description = widget.widget.data['description'] ?? 'Build and visualize algorithm flowcharts';
    _primaryColor = _colorFromHex(widget.widget.data['primaryColor'] ?? '#2196F3');
    _secondaryColor = _colorFromHex(widget.widget.data['secondaryColor'] ?? '#4CAF50');
    _accentColor = _colorFromHex(widget.widget.data['accentColor'] ?? '#FF9800');
    _isCompleted = false;
    _showSolution = false;
    _showHint = false;
    _feedbackText = '';

    // Initialize flowchart state
    _availableNodes = _parseNodes(widget.widget.data['availableNodes'] ?? []);
    _placedNodes = [];
    _connections = [];
    _selectedNode = null;
    _draggedNode = null;
    _connectionStartNode = null;
    _dragPosition = Offset.zero;

    // Initialize challenge state
    _challengeTitle = widget.widget.data['challengeTitle'] ?? 'Build a Sorting Algorithm';
    _challengeDescription = widget.widget.data['challengeDescription'] ?? 'Create a flowchart for a simple sorting algorithm';
    _solutionNodes = _parseNodes(widget.widget.data['solutionNodes'] ?? []);
    _solutionConnections = _parseConnections(widget.widget.data['solutionConnections'] ?? []);
  }

  Color _colorFromHex(String hexColor) {
    hexColor = hexColor.replaceAll('#', '');
    if (hexColor.length == 6) {
      hexColor = 'FF$hexColor';
    }
    return Color(int.parse(hexColor, radix: 16));
  }

  List<FlowchartNode> _parseNodes(List<dynamic> nodesData) {
    return nodesData.map((nodeData) {
      return FlowchartNode(
        id: nodeData['id'] ?? 'node_${DateTime.now().millisecondsSinceEpoch}',
        type: _parseNodeType(nodeData['type'] ?? 'process'),
        text: nodeData['text'] ?? 'Node',
        position: Offset(
          nodeData['x']?.toDouble() ?? 0.0,
          nodeData['y']?.toDouble() ?? 0.0,
        ),
      );
    }).toList();
  }

  FlowchartNodeType _parseNodeType(String type) {
    switch (type.toLowerCase()) {
      case 'start':
        return FlowchartNodeType.start;
      case 'end':
        return FlowchartNodeType.end;
      case 'decision':
        return FlowchartNodeType.decision;
      case 'input':
        return FlowchartNodeType.input;
      case 'output':
        return FlowchartNodeType.output;
      default:
        return FlowchartNodeType.process;
    }
  }

  List<FlowchartConnection> _parseConnections(List<dynamic> connectionsData) {
    return connectionsData.map((connectionData) {
      return FlowchartConnection(
        fromNodeId: connectionData['fromNodeId'] ?? '',
        toNodeId: connectionData['toNodeId'] ?? '',
        label: connectionData['label'] ?? '',
      );
    }).toList();
  }

  void _addNodeToCanvas(FlowchartNode node) {
    setState(() {
      // Create a copy of the node with a new position
      final newNode = FlowchartNode(
        id: 'node_${DateTime.now().millisecondsSinceEpoch}',
        type: node.type,
        text: node.text,
        position: _dragPosition,
      );

      _placedNodes.add(newNode);
      _selectedNode = newNode;
    });
  }

  void _startDraggingNode(FlowchartNode node, Offset position) {
    setState(() {
      _draggedNode = node;
      _dragPosition = position;
    });
  }

  void _updateDragPosition(Offset position) {
    setState(() {
      _dragPosition = position;
    });
  }

  void _endDragging() {
    if (_draggedNode != null) {
      // If dragging from palette, add a new node
      if (_availableNodes.contains(_draggedNode)) {
        _addNodeToCanvas(_draggedNode!);
      } else {
        // Update position of existing node
        setState(() {
          final index = _placedNodes.indexWhere((node) => node.id == _draggedNode!.id);
          if (index >= 0) {
            _placedNodes[index] = _placedNodes[index].copyWith(position: _dragPosition);
          }
        });
      }
    }

    setState(() {
      _draggedNode = null;
    });
  }

  void _selectNode(FlowchartNode? node) {
    setState(() {
      _selectedNode = node;
    });
  }

  void _startConnection(FlowchartNode node) {
    setState(() {
      _connectionStartNode = node;
    });
  }

  void _completeConnection(FlowchartNode endNode) {
    if (_connectionStartNode != null && _connectionStartNode!.id != endNode.id) {
      setState(() {
        _connections.add(FlowchartConnection(
          fromNodeId: _connectionStartNode!.id,
          toNodeId: endNode.id,
          label: '',
        ));
        _connectionStartNode = null;
      });
    }
  }

  void _cancelConnection() {
    setState(() {
      _connectionStartNode = null;
    });
  }

  void _removeNode(FlowchartNode node) {
    setState(() {
      _placedNodes.removeWhere((n) => n.id == node.id);
      _connections.removeWhere((c) => c.fromNodeId == node.id || c.toNodeId == node.id);
      if (_selectedNode?.id == node.id) {
        _selectedNode = null;
      }
    });
  }

  void _removeConnection(FlowchartConnection connection) {
    setState(() {
      _connections.removeWhere((c) =>
        c.fromNodeId == connection.fromNodeId && c.toNodeId == connection.toNodeId);
    });
  }

  void _editNodeText(FlowchartNode node, String newText) {
    setState(() {
      final index = _placedNodes.indexWhere((n) => n.id == node.id);
      if (index >= 0) {
        _placedNodes[index] = _placedNodes[index].copyWith(text: newText);
      }
    });
  }

  void _editConnectionLabel(FlowchartConnection connection, String newLabel) {
    setState(() {
      final index = _connections.indexWhere((c) =>
        c.fromNodeId == connection.fromNodeId && c.toNodeId == connection.toNodeId);
      if (index >= 0) {
        _connections[index] = _connections[index].copyWith(label: newLabel);
      }
    });
  }

  void _checkSolution() {
    // Simple solution checking - compare node types and connections
    bool isCorrect = _placedNodes.length == _solutionNodes.length &&
                     _connections.length == _solutionConnections.length;

    if (isCorrect) {
      // Check that all required node types are present
      final placedNodeTypes = _placedNodes.map((node) => node.type).toList();
      final solutionNodeTypes = _solutionNodes.map((node) => node.type).toList();

      for (var type in solutionNodeTypes) {
        if (!placedNodeTypes.contains(type)) {
          isCorrect = false;
          break;
        }
      }
    }

    setState(() {
      _isCompleted = isCorrect;
      _feedbackText = isCorrect
          ? 'Great job! Your flowchart correctly represents the algorithm.'
          : 'Not quite right. Try reviewing your flowchart structure.';
    });

    // Notify parent of completion status
    widget.onStateChanged?.call(_isCompleted);
  }

  void _toggleSolution() {
    setState(() {
      _showSolution = !_showSolution;
    });
  }

  void _toggleHint() {
    setState(() {
      _showHint = !_showHint;
    });
  }

  void _resetFlowchart() {
    setState(() {
      _placedNodes = [];
      _connections = [];
      _selectedNode = null;
      _connectionStartNode = null;
      _isCompleted = false;
      _showSolution = false;
      _showHint = false;
      _feedbackText = '';
    });
  }

  @override
  Widget build(BuildContext context) {
    return Card(
      elevation: 4,
      margin: const EdgeInsets.all(16),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              _title,
              style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                color: _primaryColor,
              ),
            ),
            const SizedBox(height: 8),
            Text(
              _description,
              style: Theme.of(context).textTheme.bodyMedium,
            ),
            const SizedBox(height: 16),

            // Challenge section
            Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: _primaryColor.withOpacity(0.1),
                borderRadius: BorderRadius.circular(8),
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    _challengeTitle,
                    style: Theme.of(context).textTheme.titleMedium?.copyWith(
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  const SizedBox(height: 4),
                  Text(_challengeDescription),
                ],
              ),
            ),
            const SizedBox(height: 16),

            // Placeholder for the actual flowchart builder UI
            Container(
              height: 400,
              decoration: BoxDecoration(
                border: Border.all(color: Colors.grey.shade300),
                borderRadius: BorderRadius.circular(8),
              ),
              child: Center(
                child: Text(
                  'Flowchart Canvas\n(Implementation would include drag-and-drop nodes, connections, etc.)',
                  textAlign: TextAlign.center,
                ),
              ),
            ),
            const SizedBox(height: 16),

            // Controls
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceEvenly,
              children: [
                ElevatedButton.icon(
                  onPressed: _checkSolution,
                  icon: const Icon(Icons.check),
                  label: const Text('Check Solution'),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: _primaryColor,
                  ),
                ),
                ElevatedButton.icon(
                  onPressed: _toggleHint,
                  icon: const Icon(Icons.lightbulb_outline),
                  label: const Text('Hint'),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: _secondaryColor,
                  ),
                ),
                ElevatedButton.icon(
                  onPressed: _resetFlowchart,
                  icon: const Icon(Icons.refresh),
                  label: const Text('Reset'),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Colors.grey,
                  ),
                ),
              ],
            ),

            // Feedback section
            if (_feedbackText.isNotEmpty)
              Container(
                margin: const EdgeInsets.only(top: 16),
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: _isCompleted ? Colors.green.shade100 : Colors.orange.shade100,
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Row(
                  children: [
                    Icon(
                      _isCompleted ? Icons.check_circle : Icons.info,
                      color: _isCompleted ? Colors.green : Colors.orange,
                    ),
                    const SizedBox(width: 8),
                    Expanded(
                      child: Text(_feedbackText),
                    ),
                  ],
                ),
              ),
          ],
        ),
      ),
    );
  }
}

// Model classes

enum FlowchartNodeType {
  start,
  end,
  process,
  decision,
  input,
  output,
}

class FlowchartNode {
  final String id;
  final FlowchartNodeType type;
  final String text;
  final Offset position;

  FlowchartNode({
    required this.id,
    required this.type,
    required this.text,
    required this.position,
  });

  FlowchartNode copyWith({
    String? id,
    FlowchartNodeType? type,
    String? text,
    Offset? position,
  }) {
    return FlowchartNode(
      id: id ?? this.id,
      type: type ?? this.type,
      text: text ?? this.text,
      position: position ?? this.position,
    );
  }
}

class FlowchartConnection {
  final String fromNodeId;
  final String toNodeId;
  final String label;

  FlowchartConnection({
    required this.fromNodeId,
    required this.toNodeId,
    required this.label,
  });

  FlowchartConnection copyWith({
    String? fromNodeId,
    String? toNodeId,
    String? label,
  }) {
    return FlowchartConnection(
      fromNodeId: fromNodeId ?? this.fromNodeId,
      toNodeId: toNodeId ?? this.toNodeId,
      label: label ?? this.label,
    );
  }
}
