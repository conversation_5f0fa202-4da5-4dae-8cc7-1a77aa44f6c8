import 'package:flutter/material.dart';
import 'dart:math' as math;

/// A widget that allows users to build and visualize compound inequalities
class InteractiveCompoundInequalityBuilderWidget extends StatefulWidget {
  /// The data for this widget
  final Map<String, dynamic> data;

  /// Optional callback when the widget state changes
  final Function(bool isCompleted)? onStateChanged;

  const InteractiveCompoundInequalityBuilderWidget({
    super.key,
    required this.data,
    this.onStateChanged,
  });

  /// Factory constructor to create the widget from JSON data
  factory InteractiveCompoundInequalityBuilderWidget.fromData(Map<String, dynamic> data) {
    return InteractiveCompoundInequalityBuilderWidget(
      data: data,
    );
  }

  @override
  State<InteractiveCompoundInequalityBuilderWidget> createState() => _InteractiveCompoundInequalityBuilderWidgetState();
}

class _InteractiveCompoundInequalityBuilderWidgetState extends State<InteractiveCompoundInequalityBuilderWidget> {
  // Inequality parameters
  List<InequalityComponent> _inequalities = [];
  String _logicalOperator = 'AND'; // 'AND' or 'OR'

  // Number line parameters
  double _minValue = -10.0;
  double _maxValue = 10.0;

  // UI parameters
  Color _primaryColor = Colors.blue;
  Color _secondaryColor = Colors.orange;
  Color _accentColor = Colors.green;
  Color _textColor = Colors.black87;

  // Challenge mode parameters
  bool _challengeMode = false;
  Map<String, dynamic>? _currentChallenge;
  bool _challengeCompleted = false;

  @override
  void initState() {
    super.initState();
    _initializeFromData();
  }

  void _initializeFromData() {
    // Initialize parameters from widget data
    _minValue = widget.data['minValue']?.toDouble() ?? -10.0;
    _maxValue = widget.data['maxValue']?.toDouble() ?? 10.0;
    _logicalOperator = widget.data['initialLogicalOperator'] ?? 'AND';

    // Initialize inequalities
    final initialInequalities = widget.data['initialInequalities'] as List<dynamic>?;
    if (initialInequalities != null && initialInequalities.isNotEmpty) {
      _inequalities = initialInequalities.map((ineq) {
        return InequalityComponent(
          leftValue: ineq['leftValue']?.toDouble() ?? 0.0,
          rightValue: ineq['rightValue']?.toDouble() ?? 0.0,
          operator: ineq['operator'] ?? '<',
          isLeftVariable: ineq['isLeftVariable'] ?? true,
          isRightVariable: ineq['isRightVariable'] ?? false,
        );
      }).toList();
    } else {
      // Default inequality if none provided
      _inequalities = [
        InequalityComponent(
          leftValue: 0.0,
          rightValue: 5.0,
          operator: '<',
          isLeftVariable: true,
          isRightVariable: false,
        ),
      ];
    }

    _challengeMode = widget.data['challengeMode'] ?? false;
    if (_challengeMode && widget.data['challenges'] != null) {
      _currentChallenge = widget.data['challenges'][0];
    }

    // Initialize colors
    if (widget.data['primaryColor'] != null) {
      _primaryColor = _colorFromHex(widget.data['primaryColor']);
    }
    if (widget.data['secondaryColor'] != null) {
      _secondaryColor = _colorFromHex(widget.data['secondaryColor']);
    }
    if (widget.data['accentColor'] != null) {
      _accentColor = _colorFromHex(widget.data['accentColor']);
    }
    if (widget.data['textColor'] != null) {
      _textColor = _colorFromHex(widget.data['textColor']);
    }
  }

  // Helper method to convert hex color string to Color
  Color _colorFromHex(String hexColor) {
    hexColor = hexColor.replaceAll('#', '');
    if (hexColor.length == 6) {
      hexColor = 'FF$hexColor';
    }
    return Color(int.parse(hexColor, radix: 16));
  }

  // Add a new inequality component
  void _addInequality() {
    setState(() {
      _inequalities.add(
        InequalityComponent(
          leftValue: 0.0,
          rightValue: 5.0,
          operator: '<',
          isLeftVariable: true,
          isRightVariable: false,
        ),
      );
    });
  }

  // Remove an inequality component
  void _removeInequality(int index) {
    if (_inequalities.length <= 1) return; // Keep at least one inequality

    setState(() {
      _inequalities.removeAt(index);
    });
  }

  // Update an inequality component
  void _updateInequality(int index, InequalityComponent newValue) {
    setState(() {
      _inequalities[index] = newValue;

      if (_challengeMode) {
        _checkChallengeCompleted();
      }
    });
  }

  // Toggle the logical operator between AND and OR
  void _toggleLogicalOperator() {
    setState(() {
      _logicalOperator = _logicalOperator == 'AND' ? 'OR' : 'AND';

      if (_challengeMode) {
        _checkChallengeCompleted();
      }
    });
  }

  // Check if a value satisfies an inequality
  bool _satisfiesInequality(double value, InequalityComponent inequality) {
    double leftSide = inequality.isLeftVariable ? value : inequality.leftValue;
    double rightSide = inequality.isRightVariable ? value : inequality.rightValue;

    switch (inequality.operator) {
      case '<':
        return leftSide < rightSide;
      case '<=':
        return leftSide <= rightSide;
      case '>':
        return leftSide > rightSide;
      case '>=':
        return leftSide >= rightSide;
      default:
        return false;
    }
  }

  // Check if a value satisfies the compound inequality
  bool _satisfiesCompoundInequality(double value) {
    if (_inequalities.isEmpty) return false;

    if (_logicalOperator == 'AND') {
      // All inequalities must be satisfied
      return _inequalities.every((ineq) => _satisfiesInequality(value, ineq));
    } else {
      // At least one inequality must be satisfied
      return _inequalities.any((ineq) => _satisfiesInequality(value, ineq));
    }
  }

  // Get the solution set as a string
  String _getSolutionSetString() {
    // For simplicity, we'll just check a range of values and describe the solution set
    List<double> solutionPoints = [];

    // Check for solution boundaries by testing points
    for (double x = _minValue; x <= _maxValue; x += 0.1) {
      bool currentSatisfies = _satisfiesCompoundInequality(x);
      bool previousSatisfies = x > _minValue ? _satisfiesCompoundInequality(x - 0.1) : !currentSatisfies;

      if (currentSatisfies != previousSatisfies) {
        // Found a boundary point
        solutionPoints.add(double.parse(x.toStringAsFixed(1)));
      }
    }

    // Add endpoints if they're part of the solution
    if (_satisfiesCompoundInequality(_minValue)) {
      solutionPoints.insert(0, _minValue);
    }
    if (_satisfiesCompoundInequality(_maxValue)) {
      solutionPoints.add(_maxValue);
    }

    // Format the solution set
    if (solutionPoints.isEmpty) {
      return "No solution";
    } else if (solutionPoints.length == 1) {
      return "x = ${solutionPoints[0]}";
    } else {
      // Check if the solution is continuous or disjoint
      List<String> intervals = [];
      for (int i = 0; i < solutionPoints.length; i += 2) {
        if (i + 1 < solutionPoints.length) {
          intervals.add("${solutionPoints[i]} ≤ x ≤ ${solutionPoints[i + 1]}");
        } else {
          // Odd number of points, the last one is a single point
          intervals.add("x = ${solutionPoints[i]}");
        }
      }

      return intervals.join(" OR ");
    }
  }

  void _checkChallengeCompleted() {
    if (!_challengeMode || _currentChallenge == null) return;

    final targetOperator = _currentChallenge!['targetOperator'];
    final targetInequalities = _currentChallenge!['targetInequalities'] as List<dynamic>?;

    if (targetOperator != _logicalOperator || targetInequalities == null ||
        targetInequalities.length != _inequalities.length) {
      setState(() {
        _challengeCompleted = false;
      });
      return;
    }

    // Check each inequality
    bool allMatch = true;
    for (int i = 0; i < targetInequalities.length; i++) {
      final targetIneq = targetInequalities[i];
      final currentIneq = _inequalities[i];

      if (targetIneq['operator'] != currentIneq.operator ||
          targetIneq['isLeftVariable'] != currentIneq.isLeftVariable ||
          targetIneq['isRightVariable'] != currentIneq.isRightVariable) {
        allMatch = false;
        break;
      }

      // Check values with some tolerance for floating point
      const epsilon = 0.1;
      if ((targetIneq['leftValue'] - currentIneq.leftValue).abs() > epsilon ||
          (targetIneq['rightValue'] - currentIneq.rightValue).abs() > epsilon) {
        allMatch = false;
        break;
      }
    }

    if (allMatch != _challengeCompleted) {
      setState(() {
        _challengeCompleted = allMatch;
      });

      if (_challengeCompleted && widget.onStateChanged != null) {
        widget.onStateChanged!(true);
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withOpacity(0.2),
            spreadRadius: 1,
            blurRadius: 3,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Title
          Text(
            widget.data['title'] ?? 'Compound Inequality Builder',
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
              color: _textColor,
            ),
          ),

          const SizedBox(height: 8),

          // Description
          Text(
            widget.data['description'] ?? 'Build and visualize compound inequalities using AND/OR operators.',
            style: TextStyle(
              fontSize: 14,
              color: _textColor.withOpacity(0.8),
            ),
          ),

          const SizedBox(height: 16),

          // Inequality builder
          ..._buildInequalityComponents(),

          // Logical operator toggle
          _buildLogicalOperatorToggle(),

          const SizedBox(height: 16),

          // Solution visualization
          _buildSolutionVisualization(),

          const SizedBox(height: 16),

          // Add inequality button
          if (_inequalities.length < 3) // Limit to 3 inequalities for simplicity
            Center(
              child: ElevatedButton.icon(
                onPressed: _addInequality,
                icon: Icon(Icons.add, color: Colors.white),
                label: Text('Add Inequality'),
                style: ElevatedButton.styleFrom(
                  backgroundColor: _accentColor,
                  foregroundColor: Colors.white,
                ),
              ),
            ),

          // Challenge feedback (if in challenge mode)
          if (_challengeMode && _currentChallenge != null)
            Container(
              margin: const EdgeInsets.only(top: 16),
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: _challengeCompleted
                    ? Colors.green.withOpacity(0.1)
                    : Colors.orange.withOpacity(0.1),
                borderRadius: BorderRadius.circular(8),
                border: Border.all(
                  color: _challengeCompleted
                      ? Colors.green.withOpacity(0.3)
                      : Colors.orange.withOpacity(0.3),
                ),
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    _currentChallenge!['description'] ?? 'Build the compound inequality as described.',
                    style: TextStyle(
                      fontSize: 14,
                      color: _textColor,
                    ),
                  ),
                  const SizedBox(height: 8),
                  Text(
                    _challengeCompleted
                        ? (_currentChallenge!['successMessage'] ?? 'Great job! You\'ve built the correct compound inequality.')
                        : (_currentChallenge!['hint'] ?? 'Check the inequality components and logical operator.'),
                    style: TextStyle(
                      fontSize: 14,
                      fontWeight: FontWeight.bold,
                      color: _challengeCompleted ? Colors.green : Colors.orange,
                    ),
                  ),
                ],
              ),
            ),

          // Widget name tag at the bottom
          if (widget.data['showNameTag'] ?? true)
            Padding(
              padding: const EdgeInsets.only(top: 8),
              child: Text(
                'InteractiveCompoundInequalityBuilderWidget',
                style: TextStyle(
                  color: Colors.grey[600],
                  fontSize: 12,
                  fontStyle: FontStyle.italic,
                ),
              ),
            ),
        ],
      ),
    );
  }

  List<Widget> _buildInequalityComponents() {
    List<Widget> components = [];

    for (int i = 0; i < _inequalities.length; i++) {
      components.add(
        Container(
          margin: const EdgeInsets.only(bottom: 12),
          padding: const EdgeInsets.all(8),
          decoration: BoxDecoration(
            color: _primaryColor.withOpacity(0.05),
            borderRadius: BorderRadius.circular(8),
            border: Border.all(color: _primaryColor.withOpacity(0.2)),
          ),
          child: Row(
            children: [
              // Inequality component
              Expanded(
                child: _buildInequalityEditor(i, _inequalities[i]),
              ),

              // Remove button
              if (_inequalities.length > 1)
                IconButton(
                  icon: Icon(Icons.remove_circle, color: Colors.red),
                  onPressed: () => _removeInequality(i),
                  tooltip: 'Remove inequality',
                ),
            ],
          ),
        ),
      );

      // Add logical operator between inequalities
      if (i < _inequalities.length - 1) {
        components.add(
          Padding(
            padding: const EdgeInsets.symmetric(vertical: 8),
            child: Center(
              child: Container(
                padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 4),
                decoration: BoxDecoration(
                  color: _secondaryColor.withOpacity(0.2),
                  borderRadius: BorderRadius.circular(16),
                ),
                child: Text(
                  _logicalOperator,
                  style: TextStyle(
                    fontWeight: FontWeight.bold,
                    color: _secondaryColor,
                  ),
                ),
              ),
            ),
          ),
        );
      }
    }

    return components;
  }

  Widget _buildInequalityEditor(int index, InequalityComponent inequality) {
    return Row(
      children: [
        // Left side
        Expanded(
          child: _buildSideEditor(
            value: inequality.leftValue,
            isVariable: inequality.isLeftVariable,
            onValueChanged: (value) {
              final newInequality = inequality.copyWith(leftValue: value);
              _updateInequality(index, newInequality);
            },
            onVariableToggled: () {
              final newInequality = inequality.copyWith(isLeftVariable: !inequality.isLeftVariable);
              _updateInequality(index, newInequality);
            },
          ),
        ),

        // Operator
        Padding(
          padding: const EdgeInsets.symmetric(horizontal: 8),
          child: DropdownButton<String>(
            value: inequality.operator,
            items: ['<', '<=', '>', '>='].map((String op) {
              return DropdownMenuItem<String>(
                value: op,
                child: Text(
                  op == '<=' ? '≤' : op == '>=' ? '≥' : op,
                  style: TextStyle(fontWeight: FontWeight.bold),
                ),
              );
            }).toList(),
            onChanged: (String? newValue) {
              if (newValue != null) {
                final newInequality = inequality.copyWith(operator: newValue);
                _updateInequality(index, newInequality);
              }
            },
          ),
        ),

        // Right side
        Expanded(
          child: _buildSideEditor(
            value: inequality.rightValue,
            isVariable: inequality.isRightVariable,
            onValueChanged: (value) {
              final newInequality = inequality.copyWith(rightValue: value);
              _updateInequality(index, newInequality);
            },
            onVariableToggled: () {
              final newInequality = inequality.copyWith(isRightVariable: !inequality.isRightVariable);
              _updateInequality(index, newInequality);
            },
          ),
        ),
      ],
    );
  }

  Widget _buildSideEditor({
    required double value,
    required bool isVariable,
    required ValueChanged<double> onValueChanged,
    required VoidCallback onVariableToggled,
  }) {
    return Row(
      children: [
        // Variable/Value toggle
        InkWell(
          onTap: onVariableToggled,
          child: Container(
            padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
            decoration: BoxDecoration(
              color: isVariable ? _primaryColor : Colors.grey.withOpacity(0.2),
              borderRadius: BorderRadius.circular(4),
            ),
            child: Text(
              isVariable ? 'x' : '#',
              style: TextStyle(
                color: isVariable ? Colors.white : _textColor,
                fontWeight: FontWeight.bold,
              ),
            ),
          ),
        ),

        const SizedBox(width: 8),

        // Value input (disabled if it's a variable)
        Expanded(
          child: TextField(
            controller: TextEditingController(text: value.toString()),
            keyboardType: TextInputType.numberWithOptions(decimal: true, signed: true),
            decoration: InputDecoration(
              isDense: true,
              contentPadding: EdgeInsets.symmetric(horizontal: 8, vertical: 8),
              border: OutlineInputBorder(),
              enabled: !isVariable,
            ),
            enabled: !isVariable,
            onChanged: (String newValue) {
              final parsedValue = double.tryParse(newValue);
              if (parsedValue != null) {
                onValueChanged(parsedValue);
              }
            },
          ),
        ),
      ],
    );
  }

  Widget _buildLogicalOperatorToggle() {
    return Center(
      child: Container(
        margin: const EdgeInsets.symmetric(vertical: 8),
        child: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            Text(
              'Logical Operator:',
              style: TextStyle(
                fontWeight: FontWeight.bold,
                color: _textColor,
              ),
            ),
            const SizedBox(width: 8),
            ToggleButtons(
              isSelected: [_logicalOperator == 'AND', _logicalOperator == 'OR'],
              onPressed: (int index) {
                setState(() {
                  _logicalOperator = index == 0 ? 'AND' : 'OR';

                  if (_challengeMode) {
                    _checkChallengeCompleted();
                  }
                });
              },
              children: [
                Padding(
                  padding: const EdgeInsets.symmetric(horizontal: 16),
                  child: Text('AND'),
                ),
                Padding(
                  padding: const EdgeInsets.symmetric(horizontal: 16),
                  child: Text('OR'),
                ),
              ],
              color: _secondaryColor,
              selectedColor: Colors.white,
              fillColor: _secondaryColor,
              borderRadius: BorderRadius.circular(8),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildSolutionVisualization() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Solution set description
        Container(
          width: double.infinity,
          padding: const EdgeInsets.all(8),
          decoration: BoxDecoration(
            color: _primaryColor.withOpacity(0.1),
            borderRadius: BorderRadius.circular(8),
            border: Border.all(color: _primaryColor.withOpacity(0.3)),
          ),
          child: Text(
            'Solution Set: ${_getSolutionSetString()}',
            style: TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.bold,
              color: _primaryColor,
            ),
            textAlign: TextAlign.center,
          ),
        ),

        const SizedBox(height: 16),

        // Number line visualization
        Container(
          height: 80,
          decoration: BoxDecoration(
            border: Border.all(color: Colors.grey.withOpacity(0.3)),
            borderRadius: BorderRadius.circular(8),
          ),
          child: ClipRRect(
            borderRadius: BorderRadius.circular(8),
            child: CustomPaint(
              size: const Size(double.infinity, 80),
              painter: NumberLinePainter(
                minValue: _minValue,
                maxValue: _maxValue,
                satisfiesFunction: _satisfiesCompoundInequality,
                primaryColor: _primaryColor,
                secondaryColor: _secondaryColor,
                textColor: _textColor,
              ),
            ),
          ),
        ),
      ],
    );
  }
}

/// Class to represent an inequality component
class InequalityComponent {
  final double leftValue;
  final double rightValue;
  final String operator; // '<', '<=', '>', '>='
  final bool isLeftVariable;
  final bool isRightVariable;

  InequalityComponent({
    required this.leftValue,
    required this.rightValue,
    required this.operator,
    required this.isLeftVariable,
    required this.isRightVariable,
  });

  InequalityComponent copyWith({
    double? leftValue,
    double? rightValue,
    String? operator,
    bool? isLeftVariable,
    bool? isRightVariable,
  }) {
    return InequalityComponent(
      leftValue: leftValue ?? this.leftValue,
      rightValue: rightValue ?? this.rightValue,
      operator: operator ?? this.operator,
      isLeftVariable: isLeftVariable ?? this.isLeftVariable,
      isRightVariable: isRightVariable ?? this.isRightVariable,
    );
  }
}

/// Custom painter for drawing the number line
class NumberLinePainter extends CustomPainter {
  final double minValue;
  final double maxValue;
  final Function(double) satisfiesFunction;
  final Color primaryColor;
  final Color secondaryColor;
  final Color textColor;

  NumberLinePainter({
    required this.minValue,
    required this.maxValue,
    required this.satisfiesFunction,
    required this.primaryColor,
    required this.secondaryColor,
    required this.textColor,
  });

  @override
  void paint(Canvas canvas, Size size) {
    final double width = size.width;
    final double height = size.height;
    final double centerY = height / 2;

    // Draw the number line
    final linePaint = Paint()
      ..color = textColor.withOpacity(0.7)
      ..strokeWidth = 2.0
      ..style = PaintingStyle.stroke;

    canvas.drawLine(
      Offset(0, centerY),
      Offset(width, centerY),
      linePaint,
    );

    // Draw tick marks and labels
    final tickPaint = Paint()
      ..color = textColor.withOpacity(0.7)
      ..strokeWidth = 1.0;

    final textStyle = TextStyle(
      color: textColor.withOpacity(0.7),
      fontSize: 12,
    );
    final textPainter = TextPainter(
      textDirection: TextDirection.ltr,
    );

    // Calculate tick interval
    final range = maxValue - minValue;
    final tickInterval = _calculateTickInterval(range);
    final firstTick = (minValue / tickInterval).ceil() * tickInterval;

    for (double value = firstTick; value <= maxValue; value += tickInterval) {
      final x = _valueToX(value, width);

      // Draw tick mark
      canvas.drawLine(
        Offset(x, centerY - 5),
        Offset(x, centerY + 5),
        tickPaint,
      );

      // Draw label
      textPainter.text = TextSpan(
        text: value.toStringAsFixed(value.truncateToDouble() == value ? 0 : 1),
        style: textStyle,
      );
      textPainter.layout();
      textPainter.paint(
        canvas,
        Offset(x - textPainter.width / 2, centerY + 8),
      );
    }

    // Draw the solution set
    final solutionPaint = Paint()
      ..color = secondaryColor
      ..strokeWidth = 6.0
      ..strokeCap = StrokeCap.round;

    // Check points along the number line
    final step = (maxValue - minValue) / width;
    double? startX;
    double? endX;

    for (double value = minValue; value <= maxValue; value += step) {
      final x = _valueToX(value, width);
      final satisfies = satisfiesFunction(value);

      if (satisfies) {
        if (startX == null) {
          startX = x;
        }
        endX = x;
      } else if (startX != null) {
        // Draw the segment
        canvas.drawLine(
          Offset(startX, centerY),
          Offset(endX!, centerY),
          solutionPaint,
        );
        startX = null;
        endX = null;
      }
    }

    // Draw the last segment if needed
    if (startX != null) {
      canvas.drawLine(
        Offset(startX, centerY),
        Offset(endX!, centerY),
        solutionPaint,
      );
    }
  }

  double _valueToX(double value, double width) {
    return (value - minValue) / (maxValue - minValue) * width;
  }

  double _calculateTickInterval(double range) {
    final rawInterval = range / 10;
    final magnitude = math.pow(10, (math.log(rawInterval) / math.ln10).floor());
    final normalized = rawInterval / magnitude;

    if (normalized < 1.5) return magnitude.toDouble();
    if (normalized < 3.5) return (2 * magnitude).toDouble();
    if (normalized < 7.5) return (5 * magnitude).toDouble();
    return (10 * magnitude).toDouble();
  }

  @override
  bool shouldRepaint(NumberLinePainter oldDelegate) {
    return oldDelegate.minValue != minValue ||
           oldDelegate.maxValue != maxValue ||
           oldDelegate.primaryColor != primaryColor ||
           oldDelegate.secondaryColor != secondaryColor ||
           oldDelegate.textColor != textColor;
  }
}
