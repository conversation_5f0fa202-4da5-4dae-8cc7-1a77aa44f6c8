import 'package:flutter/material.dart';
import 'dart:math' as math;

/// A widget that allows users to explore number lines and inequalities
class InteractiveNumberLineExplorerWidget extends StatefulWidget {
  /// The data for this widget
  final Map<String, dynamic> data;

  /// Optional callback when the widget state changes
  final Function(bool isCompleted)? onStateChanged;

  const InteractiveNumberLineExplorerWidget({
    Key? key,
    required this.data,
    this.onStateChanged,
  }) : super(key: key);

  /// Factory constructor to create the widget from JSON data
  factory InteractiveNumberLineExplorerWidget.fromData(Map<String, dynamic> data) {
    return InteractiveNumberLineExplorerWidget(
      data: data,
    );
  }

  @override
  State<InteractiveNumberLineExplorerWidget> createState() => _InteractiveNumberLineExplorerWidgetState();
}

class _InteractiveNumberLineExplorerWidgetState extends State<InteractiveNumberLineExplorerWidget> {
  // State variables
  bool _isCompleted = false;
  double _minValue = -10;
  double _maxValue = 10;
  double _leftValue = -5;
  double _rightValue = 5;
  bool _leftInclusive = true;
  bool _rightInclusive = true;
  String _currentExpression = '';
  int _currentChallengeIndex = 0;
  List<Map<String, dynamic>> _challenges = [];
  
  // Colors
  late Color _primaryColor;
  late Color _secondaryColor;
  late Color _accentColor;
  late Color _textColor;
  late Color _successColor;
  late Color _errorColor;

  @override
  void initState() {
    super.initState();
    _initializeFromData();
  }

  void _initializeFromData() {
    // Initialize colors
    _primaryColor = _parseColor(widget.data['primaryColor'] ?? '#2196F3');
    _secondaryColor = _parseColor(widget.data['secondaryColor'] ?? '#4CAF50');
    _accentColor = _parseColor(widget.data['accentColor'] ?? '#FF9800');
    _textColor = _parseColor(widget.data['textColor'] ?? '#212121');
    _successColor = _parseColor(widget.data['successColor'] ?? '#4CAF50');
    _errorColor = _parseColor(widget.data['errorColor'] ?? '#F44336');
    
    // Initialize number line range
    _minValue = widget.data['minValue']?.toDouble() ?? -10;
    _maxValue = widget.data['maxValue']?.toDouble() ?? 10;
    
    // Initialize challenges
    _challenges = List<Map<String, dynamic>>.from(widget.data['challenges'] ?? []);
    
    if (_challenges.isNotEmpty) {
      _loadChallenge(0);
    }
  }

  void _loadChallenge(int index) {
    if (index < 0 || index >= _challenges.length) return;
    
    final challenge = _challenges[index];
    
    setState(() {
      _currentChallengeIndex = index;
      _leftValue = challenge['leftValue']?.toDouble() ?? -5;
      _rightValue = challenge['rightValue']?.toDouble() ?? 5;
      _leftInclusive = challenge['leftInclusive'] ?? true;
      _rightInclusive = challenge['rightInclusive'] ?? true;
      _currentExpression = challenge['expression'] ?? '';
      _isCompleted = false;
    });
  }

  Color _parseColor(String hexColor) {
    hexColor = hexColor.replaceAll('#', '');
    if (hexColor.length == 6) {
      hexColor = 'FF$hexColor';
    }
    return Color(int.parse(hexColor, radix: 16));
  }

  void _updateLeftValue(double value) {
    setState(() {
      _leftValue = value;
      _checkCompletion();
    });
  }

  void _updateRightValue(double value) {
    setState(() {
      _rightValue = value;
      _checkCompletion();
    });
  }

  void _toggleLeftInclusive() {
    setState(() {
      _leftInclusive = !_leftInclusive;
      _checkCompletion();
    });
  }

  void _toggleRightInclusive() {
    setState(() {
      _rightInclusive = !_rightInclusive;
      _checkCompletion();
    });
  }

  void _checkCompletion() {
    final challenge = _challenges[_currentChallengeIndex];
    
    final targetLeftValue = challenge['targetLeftValue']?.toDouble() ?? 0;
    final targetRightValue = challenge['targetRightValue']?.toDouble() ?? 0;
    final targetLeftInclusive = challenge['targetLeftInclusive'] ?? true;
    final targetRightInclusive = challenge['targetRightInclusive'] ?? true;
    final tolerance = challenge['tolerance']?.toDouble() ?? 0.1;
    
    final leftMatch = (targetLeftValue - _leftValue).abs() <= tolerance && 
                      targetLeftInclusive == _leftInclusive;
    final rightMatch = (targetRightValue - _rightValue).abs() <= tolerance && 
                       targetRightInclusive == _rightInclusive;
    
    final isCorrect = leftMatch && rightMatch;
    
    if (isCorrect != _isCompleted) {
      setState(() {
        _isCompleted = isCorrect;
      });
      widget.onStateChanged?.call(isCorrect);
    }
  }

  void _resetChallenge() {
    _loadChallenge(_currentChallengeIndex);
  }

  void _nextChallenge() {
    if (_currentChallengeIndex < _challenges.length - 1) {
      _loadChallenge(_currentChallengeIndex + 1);
    }
  }

  void _previousChallenge() {
    if (_currentChallengeIndex > 0) {
      _loadChallenge(_currentChallengeIndex - 1);
    }
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withOpacity(0.2),
            spreadRadius: 1,
            blurRadius: 3,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Title
          Text(
            widget.data['title'] ?? 'Number Line Explorer',
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
              color: _textColor,
            ),
          ),
          
          const SizedBox(height: 12),
          
          // Challenge description
          if (_challenges.isNotEmpty)
            Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: _primaryColor.withOpacity(0.1),
                borderRadius: BorderRadius.circular(8),
                border: Border.all(color: _primaryColor.withOpacity(0.3)),
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'Challenge ${_currentChallengeIndex + 1}/${_challenges.length}',
                    style: TextStyle(
                      fontWeight: FontWeight.bold,
                      color: _primaryColor,
                    ),
                  ),
                  const SizedBox(height: 4),
                  Text(
                    _challenges[_currentChallengeIndex]['description'] ?? '',
                    style: TextStyle(
                      fontSize: 14,
                      color: _textColor,
                    ),
                  ),
                  if (_currentExpression.isNotEmpty)
                    Padding(
                      padding: const EdgeInsets.only(top: 8),
                      child: Text(
                        'Represent: $_currentExpression',
                        style: TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.bold,
                          color: _textColor,
                        ),
                      ),
                    ),
                ],
              ),
            ),
          
          const SizedBox(height: 16),
          
          // Number line visualization
          Container(
            height: 100,
            width: double.infinity,
            decoration: BoxDecoration(
              color: Colors.grey.shade50,
              borderRadius: BorderRadius.circular(8),
              border: Border.all(
                color: _isCompleted ? _successColor : Colors.grey.shade300,
                width: _isCompleted ? 2 : 1,
              ),
            ),
            child: CustomPaint(
              painter: NumberLinePainter(
                minValue: _minValue,
                maxValue: _maxValue,
                leftValue: _leftValue,
                rightValue: _rightValue,
                leftInclusive: _leftInclusive,
                rightInclusive: _rightInclusive,
                primaryColor: _primaryColor,
                accentColor: _accentColor,
              ),
            ),
          ),
          
          const SizedBox(height: 16),
          
          // Controls
          Row(
            children: [
              // Left endpoint controls
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Left Endpoint',
                      style: TextStyle(
                        fontWeight: FontWeight.bold,
                        color: _textColor,
                      ),
                    ),
                    const SizedBox(height: 8),
                    Row(
                      children: [
                        Text(
                          _leftValue.toStringAsFixed(1),
                          style: TextStyle(
                            color: _primaryColor,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                        const SizedBox(width: 8),
                        GestureDetector(
                          onTap: _toggleLeftInclusive,
                          child: Container(
                            padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                            decoration: BoxDecoration(
                              color: _leftInclusive ? _primaryColor : Colors.grey.shade200,
                              borderRadius: BorderRadius.circular(4),
                            ),
                            child: Text(
                              _leftInclusive ? 'Inclusive' : 'Exclusive',
                              style: TextStyle(
                                color: _leftInclusive ? Colors.white : Colors.black87,
                                fontSize: 12,
                              ),
                            ),
                          ),
                        ),
                      ],
                    ),
                    Slider(
                      value: _leftValue,
                      min: _minValue,
                      max: _maxValue,
                      divisions: (_maxValue - _minValue).toInt() * 2,
                      label: _leftValue.toStringAsFixed(1),
                      activeColor: _primaryColor,
                      onChanged: (value) {
                        if (value <= _rightValue) {
                          _updateLeftValue(value);
                        }
                      },
                    ),
                  ],
                ),
              ),
              
              const SizedBox(width: 16),
              
              // Right endpoint controls
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Right Endpoint',
                      style: TextStyle(
                        fontWeight: FontWeight.bold,
                        color: _textColor,
                      ),
                    ),
                    const SizedBox(height: 8),
                    Row(
                      children: [
                        Text(
                          _rightValue.toStringAsFixed(1),
                          style: TextStyle(
                            color: _accentColor,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                        const SizedBox(width: 8),
                        GestureDetector(
                          onTap: _toggleRightInclusive,
                          child: Container(
                            padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                            decoration: BoxDecoration(
                              color: _rightInclusive ? _accentColor : Colors.grey.shade200,
                              borderRadius: BorderRadius.circular(4),
                            ),
                            child: Text(
                              _rightInclusive ? 'Inclusive' : 'Exclusive',
                              style: TextStyle(
                                color: _rightInclusive ? Colors.white : Colors.black87,
                                fontSize: 12,
                              ),
                            ),
                          ),
                        ),
                      ],
                    ),
                    Slider(
                      value: _rightValue,
                      min: _minValue,
                      max: _maxValue,
                      divisions: (_maxValue - _minValue).toInt() * 2,
                      label: _rightValue.toStringAsFixed(1),
                      activeColor: _accentColor,
                      onChanged: (value) {
                        if (value >= _leftValue) {
                          _updateRightValue(value);
                        }
                      },
                    ),
                  ],
                ),
              ),
            ],
          ),
          
          const SizedBox(height: 16),
          
          // Feedback
          if (_isCompleted)
            Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: _successColor.withOpacity(0.1),
                borderRadius: BorderRadius.circular(8),
                border: Border.all(color: _successColor),
              ),
              child: Row(
                children: [
                  Icon(Icons.check_circle, color: _successColor),
                  const SizedBox(width: 8),
                  Expanded(
                    child: Text(
                      _challenges[_currentChallengeIndex]['successMessage'] ?? 'Correct! You\'ve represented the inequality correctly.',
                      style: TextStyle(
                        color: _successColor,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                  ),
                ],
              ),
            ),
          
          const SizedBox(height: 16),
          
          // Navigation buttons
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              // Previous/Reset buttons
              Row(
                children: [
                  // Previous challenge button
                  if (_currentChallengeIndex > 0)
                    ElevatedButton.icon(
                      onPressed: _previousChallenge,
                      icon: const Icon(Icons.arrow_back),
                      label: const Text('Previous'),
                      style: ElevatedButton.styleFrom(
                        backgroundColor: Colors.grey.shade200,
                        foregroundColor: Colors.black87,
                      ),
                    ),
                  
                  const SizedBox(width: 8),
                  
                  // Reset button
                  ElevatedButton.icon(
                    onPressed: _resetChallenge,
                    icon: const Icon(Icons.refresh),
                    label: const Text('Reset'),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.grey.shade200,
                      foregroundColor: Colors.black87,
                    ),
                  ),
                ],
              ),
              
              // Next challenge button
              if (_isCompleted && _currentChallengeIndex < _challenges.length - 1)
                ElevatedButton.icon(
                  onPressed: _nextChallenge,
                  icon: const Icon(Icons.arrow_forward),
                  label: const Text('Next Challenge'),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: _primaryColor,
                    foregroundColor: Colors.white,
                  ),
                ),
            ],
          ),
          
          // Widget name tag at the bottom
          if (widget.data['showNameTag'] ?? true)
            Padding(
              padding: const EdgeInsets.only(top: 8),
              child: Text(
                'InteractiveNumberLineExplorer',
                style: TextStyle(
                  color: Colors.grey[600],
                  fontSize: 12,
                  fontStyle: FontStyle.italic,
                ),
              ),
            ),
        ],
      ),
    );
  }
}

/// Custom painter for drawing the number line
class NumberLinePainter extends CustomPainter {
  final double minValue;
  final double maxValue;
  final double leftValue;
  final double rightValue;
  final bool leftInclusive;
  final bool rightInclusive;
  final Color primaryColor;
  final Color accentColor;
  
  NumberLinePainter({
    required this.minValue,
    required this.maxValue,
    required this.leftValue,
    required this.rightValue,
    required this.leftInclusive,
    required this.rightInclusive,
    required this.primaryColor,
    required this.accentColor,
  });
  
  @override
  void paint(Canvas canvas, Size size) {
    final paint = Paint()
      ..color = Colors.grey.shade400
      ..strokeWidth = 2
      ..style = PaintingStyle.stroke;
    
    final highlightPaint = Paint()
      ..color = primaryColor.withOpacity(0.3)
      ..strokeWidth = 6
      ..style = PaintingStyle.stroke;
    
    final leftPointPaint = Paint()
      ..color = primaryColor
      ..strokeWidth = 2
      ..style = PaintingStyle.fill;
    
    final rightPointPaint = Paint()
      ..color = accentColor
      ..strokeWidth = 2
      ..style = PaintingStyle.fill;
    
    final textPainter = TextPainter(
      textDirection: TextDirection.ltr,
      textAlign: TextAlign.center,
    );
    
    // Draw the number line
    final lineY = size.height / 2;
    canvas.drawLine(
      Offset(20, lineY),
      Offset(size.width - 20, lineY),
      paint,
    );
    
    // Draw tick marks and labels
    final range = maxValue - minValue;
    final pixelsPerUnit = (size.width - 40) / range;
    
    // Draw major ticks every integer
    for (int i = minValue.floor(); i <= maxValue.ceil(); i++) {
      final x = 20 + (i - minValue) * pixelsPerUnit;
      
      // Draw tick
      canvas.drawLine(
        Offset(x, lineY - 10),
        Offset(x, lineY + 10),
        paint,
      );
      
      // Draw label
      textPainter.text = TextSpan(
        text: i.toString(),
        style: TextStyle(
          color: Colors.black87,
          fontSize: 12,
        ),
      );
      textPainter.layout();
      textPainter.paint(
        canvas,
        Offset(x - textPainter.width / 2, lineY + 15),
      );
    }
    
    // Draw the highlighted interval
    final leftX = 20 + (leftValue - minValue) * pixelsPerUnit;
    final rightX = 20 + (rightValue - minValue) * pixelsPerUnit;
    
    canvas.drawLine(
      Offset(leftX, lineY),
      Offset(rightX, lineY),
      highlightPaint,
    );
    
    // Draw the left endpoint
    final leftRadius = 8.0;
    if (leftInclusive) {
      // Filled circle for inclusive
      canvas.drawCircle(
        Offset(leftX, lineY),
        leftRadius,
        leftPointPaint,
      );
    } else {
      // Empty circle for exclusive
      canvas.drawCircle(
        Offset(leftX, lineY),
        leftRadius,
        Paint()
          ..color = primaryColor
          ..strokeWidth = 2
          ..style = PaintingStyle.stroke,
      );
    }
    
    // Draw the right endpoint
    final rightRadius = 8.0;
    if (rightInclusive) {
      // Filled circle for inclusive
      canvas.drawCircle(
        Offset(rightX, lineY),
        rightRadius,
        rightPointPaint,
      );
    } else {
      // Empty circle for exclusive
      canvas.drawCircle(
        Offset(rightX, lineY),
        rightRadius,
        Paint()
          ..color = accentColor
          ..strokeWidth = 2
          ..style = PaintingStyle.stroke,
      );
    }
  }
  
  @override
  bool shouldRepaint(NumberLinePainter oldDelegate) {
    return oldDelegate.minValue != minValue ||
           oldDelegate.maxValue != maxValue ||
           oldDelegate.leftValue != leftValue ||
           oldDelegate.rightValue != rightValue ||
           oldDelegate.leftInclusive != leftInclusive ||
           oldDelegate.rightInclusive != rightInclusive;
  }
}
