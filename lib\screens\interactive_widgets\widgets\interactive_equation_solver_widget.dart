import 'package:flutter/material.dart';
import 'dart:math' as math;

/// A widget that allows users to solve equations step by step
class InteractiveEquationSolverWidget extends StatefulWidget {
  /// The data for this widget
  final Map<String, dynamic> data;

  /// Optional callback when the widget state changes
  final Function(bool isCompleted)? onStateChanged;

  const InteractiveEquationSolverWidget({
    Key? key,
    required this.data,
    this.onStateChanged,
  }) : super(key: key);

  /// Factory constructor to create the widget from JSON data
  factory InteractiveEquationSolverWidget.fromData(Map<String, dynamic> data) {
    return InteractiveEquationSolverWidget(
      data: data,
    );
  }

  @override
  State<InteractiveEquationSolverWidget> createState() => _InteractiveEquationSolverWidgetState();
}

class _InteractiveEquationSolverWidgetState extends State<InteractiveEquationSolverWidget> {
  // Equation data
  late String _initialEquation;
  late String _variableName;
  late String _solution;
  late List<String> _availableOperations;
  
  // Current state
  late String _currentEquation;
  List<EquationStep> _steps = [];
  bool _isCompleted = false;
  bool _showHint = false;
  String? _errorMessage;
  
  // User input
  String _selectedOperation = '';
  TextEditingController _equationController = TextEditingController();
  
  // UI customization
  late Color _primaryColor;
  late Color _successColor;
  late Color _errorColor;
  late Color _hintColor;

  @override
  void initState() {
    super.initState();
    
    // Initialize from data
    _initialEquation = widget.data['initial_equation'] ?? '2x + 5 = 15';
    _variableName = widget.data['variable_name'] ?? 'x';
    _solution = widget.data['solution'] ?? '5';
    
    // Available operations
    _availableOperations = [];
    final operationsData = widget.data['available_operations'] as List<dynamic>? ?? [];
    for (final operation in operationsData) {
      if (operation is String) {
        _availableOperations.add(operation);
      }
    }
    
    // If no operations provided, use default set
    if (_availableOperations.isEmpty) {
      _availableOperations = [
        'Add to both sides',
        'Subtract from both sides',
        'Multiply both sides',
        'Divide both sides',
        'Combine like terms',
        'Distribute',
        'Simplify',
      ];
    }
    
    // Initialize current equation
    _currentEquation = _initialEquation;
    
    // Add initial step
    _steps.add(EquationStep(
      equation: _initialEquation,
      operation: 'Initial equation',
      isCorrect: true,
    ));
    
    // Initialize colors
    _primaryColor = _parseColor(widget.data['primary_color'], Colors.blue);
    _successColor = _parseColor(widget.data['success_color'], Colors.green);
    _errorColor = _parseColor(widget.data['error_color'], Colors.red);
    _hintColor = _parseColor(widget.data['hint_color'], Colors.orange);
    
    // Initialize equation controller
    _equationController.text = _currentEquation;
  }

  @override
  void dispose() {
    _equationController.dispose();
    super.dispose();
  }

  // Helper method to parse color from string
  Color _parseColor(dynamic colorValue, Color defaultColor) {
    if (colorValue == null) return defaultColor;
    if (colorValue is String) {
      try {
        return Color(int.parse(colorValue.replaceAll('#', '0xFF')));
      } catch (e) {
        return defaultColor;
      }
    }
    return defaultColor;
  }
  
  // Apply the selected operation and update the equation
  void _applyOperation() {
    if (_selectedOperation.isEmpty) {
      setState(() {
        _errorMessage = 'Please select an operation';
      });
      return;
    }
    
    if (_equationController.text.isEmpty) {
      setState(() {
        _errorMessage = 'Please enter the resulting equation';
      });
      return;
    }
    
    final newEquation = _equationController.text.trim();
    
    // Check if the equation is valid
    if (!_isValidEquation(newEquation)) {
      setState(() {
        _errorMessage = 'Invalid equation format';
      });
      return;
    }
    
    // Check if the equation is mathematically equivalent
    final isCorrect = _isEquivalentEquation(newEquation);
    
    // Add the step
    setState(() {
      _steps.add(EquationStep(
        equation: newEquation,
        operation: _selectedOperation,
        isCorrect: isCorrect,
      ));
      
      if (isCorrect) {
        _currentEquation = newEquation;
        _errorMessage = null;
        
        // Check if the equation is solved
        if (_isSolved(newEquation)) {
          _isCompleted = true;
          widget.onStateChanged?.call(true);
        }
      } else {
        _errorMessage = 'The equation is not mathematically equivalent';
      }
      
      // Reset selection and input
      _selectedOperation = '';
      _equationController.text = isCorrect ? newEquation : _currentEquation;
    });
  }
  
  // Check if the equation is in valid format (contains =)
  bool _isValidEquation(String equation) {
    return equation.contains('=');
  }
  
  // Check if the new equation is mathematically equivalent to the current one
  // This is a simplified implementation - in a real app, you would use a proper
  // equation parser and solver to check equivalence
  bool _isEquivalentEquation(String newEquation) {
    // For demo purposes, we'll just check if the solution is still valid
    // In a real implementation, you would need to parse and evaluate both equations
    
    // For now, we'll just check if the equation contains the variable and equals sign
    if (!newEquation.contains(_variableName) || !newEquation.contains('=')) {
      return false;
    }
    
    // For demo purposes, we'll also accept if the equation is in the form "x = solution"
    if (newEquation.replaceAll(' ', '') == '$_variableName=$_solution') {
      return true;
    }
    
    // In a real implementation, you would evaluate both sides of the equation
    // and check if they are equal for the given solution
    
    // For now, we'll just return true to allow progression
    return true;
  }
  
  // Check if the equation is solved (variable isolated)
  bool _isSolved(String equation) {
    // Split by equals sign
    final parts = equation.split('=');
    if (parts.length != 2) return false;
    
    // Check if one side is just the variable and the other is the solution
    final leftSide = parts[0].trim();
    final rightSide = parts[1].trim();
    
    return (leftSide == _variableName && rightSide == _solution) ||
           (rightSide == _variableName && leftSide == _solution);
  }
  
  // Show a hint for the next step
  void _showNextStepHint() {
    setState(() {
      _showHint = true;
    });
  }
  
  // Reset the widget
  void _reset() {
    setState(() {
      _currentEquation = _initialEquation;
      _steps = [
        EquationStep(
          equation: _initialEquation,
          operation: 'Initial equation',
          isCorrect: true,
        ),
      ];
      _isCompleted = false;
      _showHint = false;
      _errorMessage = null;
      _selectedOperation = '';
      _equationController.text = _initialEquation;
    });
    
    widget.onStateChanged?.call(false);
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withOpacity(0.2),
            spreadRadius: 1,
            blurRadius: 3,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Title
          Text(
            'Equation Solver',
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
              color: _primaryColor,
            ),
          ),
          
          const SizedBox(height: 16),
          
          // Steps history
          Container(
            padding: const EdgeInsets.all(12),
            decoration: BoxDecoration(
              color: Colors.grey.shade50,
              borderRadius: BorderRadius.circular(8),
              border: Border.all(color: Colors.grey.shade300),
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'Steps:',
                  style: TextStyle(
                    fontWeight: FontWeight.bold,
                    color: _primaryColor,
                  ),
                ),
                const SizedBox(height: 8),
                ...List.generate(_steps.length, (index) {
                  final step = _steps[index];
                  return Padding(
                    padding: const EdgeInsets.only(bottom: 8),
                    child: Row(
                      children: [
                        Text(
                          '${index + 1}. ',
                          style: const TextStyle(fontWeight: FontWeight.bold),
                        ),
                        Expanded(
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Text(
                                step.operation,
                                style: TextStyle(
                                  fontStyle: FontStyle.italic,
                                  color: step.isCorrect ? _primaryColor : _errorColor,
                                ),
                              ),
                              Text(
                                step.equation,
                                style: TextStyle(
                                  fontWeight: FontWeight.bold,
                                  color: step.isCorrect ? Colors.black : _errorColor,
                                ),
                              ),
                            ],
                          ),
                        ),
                        if (step.isCorrect)
                          Icon(Icons.check_circle, color: _successColor, size: 16)
                        else
                          Icon(Icons.error, color: _errorColor, size: 16),
                      ],
                    ),
                  );
                }),
              ],
            ),
          ),
          
          if (_isCompleted) ...[
            const SizedBox(height: 16),
            Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: _successColor.withOpacity(0.1),
                borderRadius: BorderRadius.circular(8),
                border: Border.all(color: _successColor),
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    children: [
                      Icon(Icons.check_circle, color: _successColor),
                      const SizedBox(width: 8),
                      Text(
                        'Equation Solved!',
                        style: TextStyle(
                          fontWeight: FontWeight.bold,
                          color: _successColor,
                          fontSize: 16,
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 8),
                  Text(
                    'You have successfully solved the equation. The solution is $_solution.',
                    style: const TextStyle(fontSize: 14),
                  ),
                ],
              ),
            ),
          ] else ...[
            const SizedBox(height: 16),
            
            // Operation selection
            Text(
              'Select an operation:',
              style: const TextStyle(fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 8),
            Wrap(
              spacing: 8,
              runSpacing: 8,
              children: _availableOperations.map((operation) {
                final isSelected = operation == _selectedOperation;
                return ChoiceChip(
                  label: Text(operation),
                  selected: isSelected,
                  onSelected: (selected) {
                    setState(() {
                      _selectedOperation = selected ? operation : '';
                    });
                  },
                  backgroundColor: Colors.grey.shade200,
                  selectedColor: _primaryColor.withOpacity(0.2),
                  labelStyle: TextStyle(
                    color: isSelected ? _primaryColor : Colors.black,
                    fontWeight: isSelected ? FontWeight.bold : FontWeight.normal,
                  ),
                );
              }).toList(),
            ),
            
            const SizedBox(height: 16),
            
            // Equation input
            Text(
              'Enter the resulting equation:',
              style: const TextStyle(fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 8),
            TextField(
              controller: _equationController,
              decoration: InputDecoration(
                hintText: 'e.g., x = 5',
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(8),
                ),
                focusedBorder: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(8),
                  borderSide: BorderSide(color: _primaryColor, width: 2),
                ),
              ),
            ),
            
            if (_errorMessage != null) ...[
              const SizedBox(height: 8),
              Text(
                _errorMessage!,
                style: TextStyle(color: _errorColor, fontStyle: FontStyle.italic),
              ),
            ],
            
            const SizedBox(height: 16),
            
            // Action buttons
            Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                // Apply button
                ElevatedButton(
                  onPressed: _applyOperation,
                  style: ElevatedButton.styleFrom(
                    backgroundColor: _primaryColor,
                    foregroundColor: Colors.white,
                    padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(8),
                    ),
                  ),
                  child: const Text('Apply'),
                ),
                
                const SizedBox(width: 16),
                
                // Hint button
                OutlinedButton(
                  onPressed: _showNextStepHint,
                  style: OutlinedButton.styleFrom(
                    foregroundColor: _hintColor,
                    side: BorderSide(color: _hintColor),
                    padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(8),
                    ),
                  ),
                  child: const Text('Hint'),
                ),
              ],
            ),
            
            if (_showHint) ...[
              const SizedBox(height: 16),
              Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: _hintColor.withOpacity(0.1),
                  borderRadius: BorderRadius.circular(8),
                  border: Border.all(color: _hintColor),
                ),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      children: [
                        Icon(Icons.lightbulb, color: _hintColor),
                        const SizedBox(width: 8),
                        Text(
                          'Hint',
                          style: TextStyle(
                            fontWeight: FontWeight.bold,
                            color: _hintColor,
                            fontSize: 16,
                          ),
                        ),
                      ],
                    ),
                    const SizedBox(height: 8),
                    Text(
                      _getHintForCurrentEquation(),
                      style: const TextStyle(fontSize: 14),
                    ),
                  ],
                ),
              ),
            ],
          ],
          
          const SizedBox(height: 16),
          
          // Reset button
          if (_steps.length > 1 || _isCompleted)
            Center(
              child: TextButton.icon(
                onPressed: _reset,
                icon: const Icon(Icons.refresh),
                label: const Text('Start Over'),
                style: TextButton.styleFrom(
                  foregroundColor: Colors.grey.shade700,
                ),
              ),
            ),
          
          // Widget name tag
          if (widget.data['showNameTag'] ?? true)
            Padding(
              padding: const EdgeInsets.only(top: 8),
              child: Text(
                'InteractiveEquationSolverWidget',
                style: TextStyle(
                  color: Colors.grey[600],
                  fontSize: 12,
                  fontStyle: FontStyle.italic,
                ),
              ),
            ),
        ],
      ),
    );
  }
  
  // Get a hint based on the current equation
  String _getHintForCurrentEquation() {
    // This is a simplified implementation - in a real app, you would analyze
    // the equation and provide specific hints based on its structure
    
    if (_currentEquation.contains('+')) {
      return 'Try subtracting the constant from both sides to isolate the variable.';
    } else if (_currentEquation.contains('-')) {
      return 'Try adding the constant to both sides to isolate the variable.';
    } else if (_currentEquation.contains('*') || _currentEquation.contains('×')) {
      return 'Try dividing both sides by the coefficient to isolate the variable.';
    } else if (_currentEquation.contains('/') || _currentEquation.contains('÷')) {
      return 'Try multiplying both sides by the denominator to eliminate the fraction.';
    } else {
      return 'Try to isolate the variable by performing the same operation on both sides of the equation.';
    }
  }
}

/// Represents a step in the equation solving process
class EquationStep {
  final String equation;
  final String operation;
  final bool isCorrect;
  
  EquationStep({
    required this.equation,
    required this.operation,
    required this.isCorrect,
  });
}
