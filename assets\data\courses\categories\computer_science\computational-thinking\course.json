{"id": "computational-thinking", "title": "Computational Thinking", "description": "Learn to break down complex problems, find patterns, design algorithms, and develop solutions like a computer scientist.", "categoryId": "computer_science", "thumbnailPath": "assets/images/cs_icon.svg", "difficulty": "<PERSON><PERSON><PERSON>", "modules": [{"id": "deconstructing-problems", "title": "Deconstructing Problems", "description": "Master the art of breaking down complex challenges into manageable parts.", "order": 1}, {"id": "recognizing-patterns-abstraction", "title": "Recognizing Patterns and Abstraction", "description": "Identify recurring themes and simplify complex systems by focusing on essential details.", "order": 2}, {"id": "designing-algorithms", "title": "Designing Algorithms", "description": "Learn to create step-by-step instructions to solve problems efficiently.", "order": 3}, {"id": "evaluating-refining-solutions", "title": "Evaluating and Refining Solutions", "description": "Assess the effectiveness of your algorithms and improve them for better performance.", "order": 4}, {"id": "computational-thinking-real-world", "title": "Computational Thinking in the Real World", "description": "Apply computational thinking skills to solve everyday problems and understand complex systems.", "order": 5}]}