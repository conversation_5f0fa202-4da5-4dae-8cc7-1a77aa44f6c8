import 'package:flutter/material.dart';
import 'package:resonance_app/models/interactive_widget_model.dart'; // Fix import path

class InteractiveLinkedListVisualizer extends StatelessWidget {
  final InteractiveWidgetModel widgetModel;

  const InteractiveLinkedListVisualizer({super.key, required this.widgetModel});

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(16.0),
      decoration: BoxDecoration(
        color: Colors.blue.shade50,
        borderRadius: BorderRadius.circular(8.0),
        border: Border.all(color: Colors.blue.shade200),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            widgetModel.name,
            style: const TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.bold,
              color: Colors.blueAccent,
            ),
          ),
          const SizedBox(height: 8.0),
          Text(
            widgetModel.description,
            style: TextStyle(
              fontSize: 14,
              color: Colors.grey.shade700,
            ),
          ),
          const SizedBox(height: 16.0),
          const Center(
            child: Text(
              'Linked List Visualization Placeholder',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.w500,
                color: Colors.blueGrey,
              ),
            ),
          ),
          // Add your linked list visualization logic here
        ],
      ),
    );
  }
}
