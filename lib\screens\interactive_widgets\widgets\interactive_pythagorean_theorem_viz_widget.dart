import 'package:flutter/material.dart';
import 'dart:math' as math;

/// A widget that visualizes the Pythagorean theorem (a² + b² = c²)
class InteractivePythagoreanTheoremVizWidget extends StatefulWidget {
  final Map<String, dynamic> data;
  final Function(bool)? onStateChanged;

  const InteractivePythagoreanTheoremVizWidget({
    Key? key,
    required this.data,
    this.onStateChanged,
  }) : super(key: key);

  /// Creates the widget from JSON data
  static InteractivePythagoreanTheoremVizWidget fromData(Map<String, dynamic> data) {
    return InteractivePythagoreanTheoremVizWidget(
      data: data,
    );
  }

  @override
  State<InteractivePythagoreanTheoremVizWidget> createState() => _InteractivePythagoreanTheoremVizWidgetState();
}

class _InteractivePythagoreanTheoremVizWidgetState extends State<InteractivePythagoreanTheoremVizWidget> {
  // Triangle vertices
  late List<Offset> _vertices;
  
  // Colors
  late Color _triangleColor;
  late Color _squareAColor;
  late Color _squareBColor;
  late Color _squareCColor;
  late Color _textColor;
  
  // Interaction state
  int? _draggedVertexIndex;
  bool _showSquares = true;
  bool _showAreas = true;
  bool _showFormula = true;
  bool _animateSquares = false;
  
  // Animation controller
  double _animationProgress = 0.0;
  bool _isAnimating = false;

  @override
  void initState() {
    super.initState();
    _initializeWidget();
  }

  void _initializeWidget() {
    // Initialize triangle vertices (right-angled triangle)
    _vertices = [
      Offset(100, 250), // A (right angle)
      Offset(300, 250), // B
      Offset(100, 100), // C
    ];
    
    // Set colors
    _triangleColor = _parseColor(widget.data['triangleColor']) ?? Colors.blue.shade200;
    _squareAColor = _parseColor(widget.data['squareAColor']) ?? Colors.red.shade200;
    _squareBColor = _parseColor(widget.data['squareBColor']) ?? Colors.green.shade200;
    _squareCColor = _parseColor(widget.data['squareCColor']) ?? Colors.purple.shade200;
    _textColor = _parseColor(widget.data['textColor']) ?? Colors.black87;
    
    // Set display options
    _showSquares = widget.data['showSquares'] ?? true;
    _showAreas = widget.data['showAreas'] ?? true;
    _showFormula = widget.data['showFormula'] ?? true;
    _animateSquares = widget.data['animateSquares'] ?? false;
  }

  // Parse color from hex string
  Color? _parseColor(String? hexString) {
    if (hexString == null) return null;
    
    hexString = hexString.replaceAll('#', '');
    if (hexString.length == 6) {
      hexString = 'FF$hexString';
    }
    
    return Color(int.parse(hexString, radix: 16));
  }

  // Calculate side lengths
  double _getSideA() {
    return (_vertices[1] - _vertices[0]).distance;
  }
  
  double _getSideB() {
    return (_vertices[2] - _vertices[0]).distance;
  }
  
  double _getSideC() {
    return (_vertices[2] - _vertices[1]).distance;
  }
  
  // Calculate areas
  double _getSquareAreaA() {
    return math.pow(_getSideA(), 2).toDouble();
  }
  
  double _getSquareAreaB() {
    return math.pow(_getSideB(), 2).toDouble();
  }
  
  double _getSquareAreaC() {
    return math.pow(_getSideC(), 2).toDouble();
  }
  
  // Start animation
  void _startAnimation() {
    if (_isAnimating) return;
    
    setState(() {
      _isAnimating = true;
      _animationProgress = 0.0;
    });
    
    _animateStep();
  }
  
  void _animateStep() {
    if (!_isAnimating || _animationProgress >= 1.0) {
      setState(() {
        _isAnimating = false;
      });
      return;
    }
    
    setState(() {
      _animationProgress += 0.02;
    });
    
    Future.delayed(const Duration(milliseconds: 50), _animateStep);
  }

  @override
  Widget build(BuildContext context) {
    final sideA = _getSideA();
    final sideB = _getSideB();
    final sideC = _getSideC();
    
    final areaA = _getSquareAreaA();
    final areaB = _getSquareAreaB();
    final areaC = _getSquareAreaC();
    
    // Check if the theorem holds (with some tolerance for floating point)
    final difference = (areaA + areaB) - areaC;
    final isTheorem = difference.abs() < 1.0;
    
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withOpacity(0.2),
            spreadRadius: 1,
            blurRadius: 3,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Title
          Text(
            widget.data['title'] ?? 'Pythagorean Theorem Visualizer',
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
              color: _textColor,
            ),
          ),
          
          const SizedBox(height: 8),
          
          // Description
          if (widget.data['description'] != null)
            Text(
              widget.data['description'],
              style: TextStyle(
                fontSize: 14,
                color: _textColor.withOpacity(0.8),
              ),
            ),
          
          const SizedBox(height: 16),
          
          // Controls
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceEvenly,
            children: [
              ElevatedButton.icon(
                onPressed: () {
                  setState(() {
                    _showSquares = !_showSquares;
                  });
                },
                icon: Icon(_showSquares ? Icons.visibility : Icons.visibility_off),
                label: const Text('Squares'),
                style: ElevatedButton.styleFrom(
                  backgroundColor: _showSquares ? Colors.blue : Colors.grey,
                ),
              ),
              ElevatedButton.icon(
                onPressed: () {
                  setState(() {
                    _showAreas = !_showAreas;
                  });
                },
                icon: Icon(_showAreas ? Icons.calculate : Icons.calculate_outlined),
                label: const Text('Areas'),
                style: ElevatedButton.styleFrom(
                  backgroundColor: _showAreas ? Colors.blue : Colors.grey,
                ),
              ),
              ElevatedButton.icon(
                onPressed: _startAnimation,
                icon: const Icon(Icons.play_arrow),
                label: const Text('Animate'),
                style: ElevatedButton.styleFrom(
                  backgroundColor: _isAnimating ? Colors.green : Colors.blue,
                ),
              ),
            ],
          ),
          
          const SizedBox(height: 16),
          
          // Pythagorean theorem visualization
          SizedBox(
            height: 300,
            child: GestureDetector(
              onPanDown: (details) {
                final localPosition = details.localPosition;
                // Find if a vertex was tapped
                for (int i = 0; i < _vertices.length; i++) {
                  if ((localPosition - _vertices[i]).distance < 20) {
                    setState(() {
                      _draggedVertexIndex = i;
                    });
                    break;
                  }
                }
              },
              onPanUpdate: (details) {
                if (_draggedVertexIndex != null) {
                  setState(() {
                    _vertices[_draggedVertexIndex!] += details.delta;
                    
                    // Ensure vertex 0 remains the right angle (90 degrees)
                    if (_draggedVertexIndex == 0) {
                      // Adjust vertices 1 and 2 to maintain right angle
                      final v0 = _vertices[0];
                      final v1 = _vertices[1];
                      final v2 = _vertices[2];
                      
                      // Make v0-v1 horizontal
                      _vertices[1] = Offset(v1.dx, v0.dy);
                      
                      // Make v0-v2 vertical
                      _vertices[2] = Offset(v0.dx, v2.dy);
                    } else if (_draggedVertexIndex == 1) {
                      // Keep v1 at same y as v0
                      _vertices[1] = Offset(_vertices[1].dx, _vertices[0].dy);
                    } else if (_draggedVertexIndex == 2) {
                      // Keep v2 at same x as v0
                      _vertices[2] = Offset(_vertices[0].dx, _vertices[2].dy);
                    }
                  });
                }
              },
              onPanEnd: (details) {
                setState(() {
                  _draggedVertexIndex = null;
                });
              },
              child: CustomPaint(
                painter: PythagoreanTheoremPainter(
                  vertices: _vertices,
                  triangleColor: _triangleColor,
                  squareAColor: _squareAColor,
                  squareBColor: _squareBColor,
                  squareCColor: _squareCColor,
                  textColor: _textColor,
                  showSquares: _showSquares,
                  showAreas: _showAreas,
                  animationProgress: _animationProgress,
                  isAnimating: _isAnimating,
                ),
                child: Container(),
              ),
            ),
          ),
          
          const SizedBox(height: 16),
          
          // Formula display
          if (_showFormula)
            Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: Colors.grey.withOpacity(0.1),
                borderRadius: BorderRadius.circular(8),
              ),
              child: Column(
                children: [
                  Text(
                    'Pythagorean Theorem: a² + b² = c²',
                    style: TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.bold,
                      color: _textColor,
                    ),
                  ),
                  const SizedBox(height: 8),
                  Text(
                    '${areaA.toStringAsFixed(1)} + ${areaB.toStringAsFixed(1)} = ${areaC.toStringAsFixed(1)}',
                    style: TextStyle(
                      fontSize: 16,
                      color: isTheorem ? Colors.green : Colors.red,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  Text(
                    '${sideA.toStringAsFixed(1)}² + ${sideB.toStringAsFixed(1)}² = ${sideC.toStringAsFixed(1)}²',
                    style: TextStyle(
                      fontSize: 14,
                      color: _textColor.withOpacity(0.8),
                    ),
                  ),
                ],
              ),
            ),
          
          // Widget name tag
          if (widget.data['showNameTag'] ?? true)
            Padding(
              padding: const EdgeInsets.only(top: 8),
              child: Text(
                'InteractivePythagoreanTheoremVizWidget',
                style: TextStyle(
                  color: Colors.grey[600],
                  fontSize: 12,
                  fontStyle: FontStyle.italic,
                ),
              ),
            ),
        ],
      ),
    );
  }
}

/// Custom painter for the Pythagorean theorem visualization
class PythagoreanTheoremPainter extends CustomPainter {
  final List<Offset> vertices;
  final Color triangleColor;
  final Color squareAColor;
  final Color squareBColor;
  final Color squareCColor;
  final Color textColor;
  final bool showSquares;
  final bool showAreas;
  final double animationProgress;
  final bool isAnimating;
  
  PythagoreanTheoremPainter({
    required this.vertices,
    required this.triangleColor,
    required this.squareAColor,
    required this.squareBColor,
    required this.squareCColor,
    required this.textColor,
    required this.showSquares,
    required this.showAreas,
    required this.animationProgress,
    required this.isAnimating,
  });
  
  @override
  void paint(Canvas canvas, Size size) {
    final paint = Paint()
      ..color = triangleColor
      ..style = PaintingStyle.fill;
    
    final outlinePaint = Paint()
      ..color = Colors.black
      ..style = PaintingStyle.stroke
      ..strokeWidth = 2;
    
    // Draw the right-angled triangle
    final path = Path()
      ..moveTo(vertices[0].dx, vertices[0].dy)
      ..lineTo(vertices[1].dx, vertices[1].dy)
      ..lineTo(vertices[2].dx, vertices[2].dy)
      ..close();
    
    canvas.drawPath(path, paint);
    canvas.drawPath(path, outlinePaint);
    
    // Draw right angle marker
    final rightAngleSize = 15.0;
    final rightAnglePath = Path()
      ..moveTo(vertices[0].dx + rightAngleSize, vertices[0].dy)
      ..lineTo(vertices[0].dx + rightAngleSize, vertices[0].dy - rightAngleSize)
      ..lineTo(vertices[0].dx, vertices[0].dy - rightAngleSize);
    
    canvas.drawPath(rightAnglePath, outlinePaint);
    
    // Draw squares on each side if enabled
    if (showSquares) {
      _drawSquareA(canvas);
      _drawSquareB(canvas);
      _drawSquareC(canvas);
    }
  }
  
  void _drawSquareA(Canvas canvas) {
    final v0 = vertices[0];
    final v1 = vertices[1];
    
    final dx = v1.dx - v0.dx;
    final dy = v1.dy - v0.dy;
    
    final paint = Paint()
      ..color = squareAColor
      ..style = PaintingStyle.fill;
    
    final outlinePaint = Paint()
      ..color = Colors.black
      ..style = PaintingStyle.stroke
      ..strokeWidth = 2;
    
    final path = Path()
      ..moveTo(v0.dx, v0.dy)
      ..lineTo(v1.dx, v1.dy)
      ..lineTo(v1.dx - dy, v1.dy + dx)
      ..lineTo(v0.dx - dy, v0.dy + dx)
      ..close();
    
    canvas.drawPath(path, paint);
    canvas.drawPath(path, outlinePaint);
    
    // Draw area text if enabled
    if (showAreas) {
      final center = Offset(
        (v0.dx + v1.dx + v1.dx - dy + v0.dx - dy) / 4,
        (v0.dy + v1.dy + v1.dy + dx + v0.dy + dx) / 4,
      );
      
      final textSpan = TextSpan(
        text: 'a²',
        style: TextStyle(
          color: textColor,
          fontSize: 18,
          fontWeight: FontWeight.bold,
        ),
      );
      
      final textPainter = TextPainter(
        text: textSpan,
        textDirection: TextDirection.ltr,
      );
      
      textPainter.layout();
      textPainter.paint(
        canvas,
        center - Offset(textPainter.width / 2, textPainter.height / 2),
      );
    }
  }
  
  void _drawSquareB(Canvas canvas) {
    final v0 = vertices[0];
    final v2 = vertices[2];
    
    final dx = v2.dx - v0.dx;
    final dy = v2.dy - v0.dy;
    
    final paint = Paint()
      ..color = squareBColor
      ..style = PaintingStyle.fill;
    
    final outlinePaint = Paint()
      ..color = Colors.black
      ..style = PaintingStyle.stroke
      ..strokeWidth = 2;
    
    final path = Path()
      ..moveTo(v0.dx, v0.dy)
      ..lineTo(v2.dx, v2.dy)
      ..lineTo(v2.dx + dy, v2.dy - dx)
      ..lineTo(v0.dx + dy, v0.dy - dx)
      ..close();
    
    canvas.drawPath(path, paint);
    canvas.drawPath(path, outlinePaint);
    
    // Draw area text if enabled
    if (showAreas) {
      final center = Offset(
        (v0.dx + v2.dx + v2.dx + dy + v0.dx + dy) / 4,
        (v0.dy + v2.dy + v2.dy - dx + v0.dy - dx) / 4,
      );
      
      final textSpan = TextSpan(
        text: 'b²',
        style: TextStyle(
          color: textColor,
          fontSize: 18,
          fontWeight: FontWeight.bold,
        ),
      );
      
      final textPainter = TextPainter(
        text: textSpan,
        textDirection: TextDirection.ltr,
      );
      
      textPainter.layout();
      textPainter.paint(
        canvas,
        center - Offset(textPainter.width / 2, textPainter.height / 2),
      );
    }
  }
  
  void _drawSquareC(Canvas canvas) {
    final v1 = vertices[1];
    final v2 = vertices[2];
    
    final dx = v2.dx - v1.dx;
    final dy = v2.dy - v1.dy;
    
    final paint = Paint()
      ..color = squareCColor
      ..style = PaintingStyle.fill;
    
    final outlinePaint = Paint()
      ..color = Colors.black
      ..style = PaintingStyle.stroke
      ..strokeWidth = 2;
    
    final path = Path()
      ..moveTo(v1.dx, v1.dy)
      ..lineTo(v2.dx, v2.dy)
      ..lineTo(v2.dx + dx, v2.dy + dy)
      ..lineTo(v1.dx + dx, v1.dy + dy)
      ..close();
    
    canvas.drawPath(path, paint);
    canvas.drawPath(path, outlinePaint);
    
    // Draw area text if enabled
    if (showAreas) {
      final center = Offset(
        (v1.dx + v2.dx + v2.dx + dx + v1.dx + dx) / 4,
        (v1.dy + v2.dy + v2.dy + dy + v1.dy + dy) / 4,
      );
      
      final textSpan = TextSpan(
        text: 'c²',
        style: TextStyle(
          color: textColor,
          fontSize: 18,
          fontWeight: FontWeight.bold,
        ),
      );
      
      final textPainter = TextPainter(
        text: textSpan,
        textDirection: TextDirection.ltr,
      );
      
      textPainter.layout();
      textPainter.paint(
        canvas,
        center - Offset(textPainter.width / 2, textPainter.height / 2),
      );
    }
  }
  
  @override
  bool shouldRepaint(covariant PythagoreanTheoremPainter oldDelegate) {
    return oldDelegate.vertices != vertices ||
           oldDelegate.showSquares != showSquares ||
           oldDelegate.showAreas != showAreas ||
           oldDelegate.animationProgress != animationProgress ||
           oldDelegate.isAnimating != isAnimating;
  }
}
