import 'package:flutter/material.dart';
import 'dart:math' as math;

/// A widget that allows users to transform functions by shifting, stretching, and reflecting
class InteractiveFunctionTransformerWidget extends StatefulWidget {
  /// The data for this widget
  final Map<String, dynamic> data;

  /// Optional callback when the widget state changes
  final Function(bool isCompleted)? onStateChanged;

  const InteractiveFunctionTransformerWidget({
    Key? key,
    required this.data,
    this.onStateChanged,
  }) : super(key: key);

  /// Factory constructor to create the widget from JSON data
  factory InteractiveFunctionTransformerWidget.fromData(Map<String, dynamic> data) {
    return InteractiveFunctionTransformerWidget(
      data: data,
    );
  }

  @override
  _InteractiveFunctionTransformerWidgetState createState() =>
      _InteractiveFunctionTransformerWidgetState();
}

class _InteractiveFunctionTransformerWidgetState extends State<InteractiveFunctionTransformerWidget> {
  // Function parameters
  late String _selectedFunctionType;
  late List<String> _availableFunctionTypes;

  // Transformation parameters
  double _horizontalShift = 0.0;
  double _verticalShift = 0.0;
  double _horizontalStretch = 1.0;
  double _verticalStretch = 1.0;
  bool _reflectX = false;
  bool _reflectY = false;

  // Graph parameters
  late double _minX;
  late double _maxX;
  late double _minY;
  late double _maxY;
  late int _gridLines;

  // Colors
  late Color _primaryColor;
  late Color _secondaryColor;
  late Color _accentColor;
  late Color _backgroundColor;
  late Color _textColor;

  // Predefined examples
  late List<Map<String, dynamic>> _examples;

  // Completion tracking
  bool _isCompleted = false;

  @override
  void initState() {
    super.initState();

    // Initialize colors
    _primaryColor = widget.data['primaryColor'] != null
        ? Color(int.parse(widget.data['primaryColor'], radix: 16))
        : Colors.blue;
    _secondaryColor = widget.data['secondaryColor'] != null
        ? Color(int.parse(widget.data['secondaryColor'], radix: 16))
        : Colors.lightBlue;
    _accentColor = widget.data['accentColor'] != null
        ? Color(int.parse(widget.data['accentColor'], radix: 16))
        : Colors.orange;
    _backgroundColor = widget.data['backgroundColor'] != null
        ? Color(int.parse(widget.data['backgroundColor'], radix: 16))
        : Colors.white;
    _textColor = widget.data['textColor'] != null
        ? Color(int.parse(widget.data['textColor'], radix: 16))
        : Colors.black87;

    // Initialize function types
    _availableFunctionTypes = widget.data['functionTypes'] != null
        ? List<String>.from(widget.data['functionTypes'])
        : ['linear', 'quadratic', 'cubic', 'absolute', 'sine', 'cosine'];
    _selectedFunctionType = _availableFunctionTypes.first;

    // Initialize graph parameters
    _minX = widget.data['minX'] != null ? widget.data['minX'].toDouble() : -10.0;
    _maxX = widget.data['maxX'] != null ? widget.data['maxX'].toDouble() : 10.0;
    _minY = widget.data['minY'] != null ? widget.data['minY'].toDouble() : -10.0;
    _maxY = widget.data['maxY'] != null ? widget.data['maxY'].toDouble() : 10.0;
    _gridLines = widget.data['gridLines'] != null ? widget.data['gridLines'] : 10;

    // Initialize examples
    _examples = widget.data['examples'] != null
        ? List<Map<String, dynamic>>.from(widget.data['examples'])
        : _getDefaultExamples();
  }

  // Get default examples if none provided
  List<Map<String, dynamic>> _getDefaultExamples() {
    return [
      {
        'title': 'Vertical Shift',
        'description': 'f(x) = x² + 3 (Shift up 3 units)',
        'functionType': 'quadratic',
        'horizontalShift': 0.0,
        'verticalShift': 3.0,
        'horizontalStretch': 1.0,
        'verticalStretch': 1.0,
        'reflectX': false,
        'reflectY': false,
      },
      {
        'title': 'Horizontal Shift',
        'description': 'f(x) = (x - 2)² (Shift right 2 units)',
        'functionType': 'quadratic',
        'horizontalShift': 2.0,
        'verticalShift': 0.0,
        'horizontalStretch': 1.0,
        'verticalStretch': 1.0,
        'reflectX': false,
        'reflectY': false,
      },
      {
        'title': 'Vertical Stretch',
        'description': 'f(x) = 3x² (Stretch vertically by factor of 3)',
        'functionType': 'quadratic',
        'horizontalShift': 0.0,
        'verticalShift': 0.0,
        'horizontalStretch': 1.0,
        'verticalStretch': 3.0,
        'reflectX': false,
        'reflectY': false,
      },
      {
        'title': 'Reflection',
        'description': 'f(x) = -x² (Reflect across x-axis)',
        'functionType': 'quadratic',
        'horizontalShift': 0.0,
        'verticalShift': 0.0,
        'horizontalStretch': 1.0,
        'verticalStretch': 1.0,
        'reflectX': true,
        'reflectY': false,
      },
      {
        'title': 'Combined Transformations',
        'description': 'f(x) = 2(x - 1)² + 3 (Multiple transformations)',
        'functionType': 'quadratic',
        'horizontalShift': 1.0,
        'verticalShift': 3.0,
        'horizontalStretch': 1.0,
        'verticalStretch': 2.0,
        'reflectX': false,
        'reflectY': false,
      },
    ];
  }

  // Load an example
  void _loadExample(Map<String, dynamic> example) {
    setState(() {
      _selectedFunctionType = example['functionType'];
      _horizontalShift = example['horizontalShift'].toDouble();
      _verticalShift = example['verticalShift'].toDouble();
      _horizontalStretch = example['horizontalStretch'].toDouble();
      _verticalStretch = example['verticalStretch'].toDouble();
      _reflectX = example['reflectX'];
      _reflectY = example['reflectY'];

      // Mark as completed when user interacts with examples
      if (!_isCompleted) {
        _isCompleted = true;
        widget.onStateChanged?.call(true);
      }
    });
  }

  // Reset transformations
  void _resetTransformations() {
    setState(() {
      _horizontalShift = 0.0;
      _verticalShift = 0.0;
      _horizontalStretch = 1.0;
      _verticalStretch = 1.0;
      _reflectX = false;
      _reflectY = false;
    });
  }

  // Evaluate the function with transformations
  double _evaluateFunction(double x) {
    // Apply horizontal transformations
    double transformedX = x;

    // Horizontal reflection
    if (_reflectY) {
      transformedX = -transformedX;
    }

    // Horizontal stretch/compression
    transformedX = transformedX / _horizontalStretch;

    // Horizontal shift
    transformedX = transformedX - _horizontalShift;

    // Evaluate the base function
    double y = 0;
    switch (_selectedFunctionType) {
      case 'linear':
        y = transformedX;
        break;
      case 'quadratic':
        y = transformedX * transformedX;
        break;
      case 'cubic':
        y = transformedX * transformedX * transformedX;
        break;
      case 'absolute':
        y = transformedX.abs();
        break;
      case 'sine':
        y = math.sin(transformedX);
        break;
      case 'cosine':
        y = math.cos(transformedX);
        break;
      default:
        y = transformedX;
    }

    // Apply vertical transformations

    // Vertical stretch/compression
    y = y * _verticalStretch;

    // Vertical reflection
    if (_reflectX) {
      y = -y;
    }

    // Vertical shift
    y = y + _verticalShift;

    return y;
  }

  @override
  Widget build(BuildContext context) {
    return Card(
      elevation: 4,
      margin: const EdgeInsets.all(8),
      color: _backgroundColor,
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Title
            Text(
              widget.data['title'] ?? 'Function Transformer',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
                color: _textColor,
              ),
            ),

            const SizedBox(height: 8),

            // Description
            Text(
              widget.data['description'] ?? 'Explore how transformations affect the graphs of functions.',
              style: TextStyle(
                fontSize: 14,
                color: _textColor.withOpacity(0.7),
              ),
            ),

            const SizedBox(height: 16),

            // Function selection
            Row(
              children: [
                Text(
                  'Function Type:',
                  style: TextStyle(
                    fontSize: 14,
                    fontWeight: FontWeight.bold,
                    color: _textColor,
                  ),
                ),
                const SizedBox(width: 16),
                DropdownButton<String>(
                  value: _selectedFunctionType,
                  onChanged: (String? newValue) {
                    if (newValue != null) {
                      setState(() {
                        _selectedFunctionType = newValue;
                      });
                    }
                  },
                  items: _availableFunctionTypes
                      .map<DropdownMenuItem<String>>((String value) {
                    return DropdownMenuItem<String>(
                      value: value,
                      child: Text(value.capitalize()),
                    );
                  }).toList(),
                ),
              ],
            ),

            const SizedBox(height: 16),

            // Graph area (placeholder)
            Container(
              height: 200,
              width: double.infinity,
              decoration: BoxDecoration(
                border: Border.all(color: _primaryColor.withOpacity(0.3)),
                borderRadius: BorderRadius.circular(8),
              ),
              child: CustomPaint(
                painter: FunctionGraphPainter(
                  minX: _minX,
                  maxX: _maxX,
                  minY: _minY,
                  maxY: _maxY,
                  gridLines: _gridLines,
                  evaluateFunction: _evaluateFunction,
                  primaryColor: _primaryColor,
                  secondaryColor: _secondaryColor,
                  accentColor: _accentColor,
                  backgroundColor: _backgroundColor,
                ),
              ),
            ),

            const SizedBox(height: 16),

            // Transformation controls
            Text(
              'Transformations:',
              style: TextStyle(
                fontSize: 14,
                fontWeight: FontWeight.bold,
                color: _textColor,
              ),
            ),

            const SizedBox(height: 8),

            // Horizontal shift slider
            Row(
              children: [
                SizedBox(
                  width: 120,
                  child: Text(
                    'Horizontal Shift:',
                    style: TextStyle(
                      fontSize: 12,
                      color: _textColor,
                    ),
                  ),
                ),
                Expanded(
                  child: Slider(
                    value: _horizontalShift,
                    min: -5.0,
                    max: 5.0,
                    divisions: 20,
                    label: _horizontalShift.toStringAsFixed(1),
                    onChanged: (double value) {
                      setState(() {
                        _horizontalShift = value;
                      });
                    },
                    activeColor: _primaryColor,
                    inactiveColor: _primaryColor.withOpacity(0.3),
                  ),
                ),
                SizedBox(
                  width: 40,
                  child: Text(
                    _horizontalShift.toStringAsFixed(1),
                    style: TextStyle(
                      fontSize: 12,
                      color: _textColor,
                    ),
                  ),
                ),
              ],
            ),

            // Vertical shift slider
            Row(
              children: [
                SizedBox(
                  width: 120,
                  child: Text(
                    'Vertical Shift:',
                    style: TextStyle(
                      fontSize: 12,
                      color: _textColor,
                    ),
                  ),
                ),
                Expanded(
                  child: Slider(
                    value: _verticalShift,
                    min: -5.0,
                    max: 5.0,
                    divisions: 20,
                    label: _verticalShift.toStringAsFixed(1),
                    onChanged: (double value) {
                      setState(() {
                        _verticalShift = value;
                      });
                    },
                    activeColor: _primaryColor,
                    inactiveColor: _primaryColor.withOpacity(0.3),
                  ),
                ),
                SizedBox(
                  width: 40,
                  child: Text(
                    _verticalShift.toStringAsFixed(1),
                    style: TextStyle(
                      fontSize: 12,
                      color: _textColor,
                    ),
                  ),
                ),
              ],
            ),

            // Horizontal stretch slider
            Row(
              children: [
                SizedBox(
                  width: 120,
                  child: Text(
                    'Horizontal Stretch:',
                    style: TextStyle(
                      fontSize: 12,
                      color: _textColor,
                    ),
                  ),
                ),
                Expanded(
                  child: Slider(
                    value: _horizontalStretch,
                    min: 0.1,
                    max: 3.0,
                    divisions: 29,
                    label: _horizontalStretch.toStringAsFixed(1),
                    onChanged: (double value) {
                      setState(() {
                        _horizontalStretch = value;
                      });
                    },
                    activeColor: _primaryColor,
                    inactiveColor: _primaryColor.withOpacity(0.3),
                  ),
                ),
                SizedBox(
                  width: 40,
                  child: Text(
                    _horizontalStretch.toStringAsFixed(1),
                    style: TextStyle(
                      fontSize: 12,
                      color: _textColor,
                    ),
                  ),
                ),
              ],
            ),

            // Vertical stretch slider
            Row(
              children: [
                SizedBox(
                  width: 120,
                  child: Text(
                    'Vertical Stretch:',
                    style: TextStyle(
                      fontSize: 12,
                      color: _textColor,
                    ),
                  ),
                ),
                Expanded(
                  child: Slider(
                    value: _verticalStretch,
                    min: 0.1,
                    max: 3.0,
                    divisions: 29,
                    label: _verticalStretch.toStringAsFixed(1),
                    onChanged: (double value) {
                      setState(() {
                        _verticalStretch = value;
                      });
                    },
                    activeColor: _primaryColor,
                    inactiveColor: _primaryColor.withOpacity(0.3),
                  ),
                ),
                SizedBox(
                  width: 40,
                  child: Text(
                    _verticalStretch.toStringAsFixed(1),
                    style: TextStyle(
                      fontSize: 12,
                      color: _textColor,
                    ),
                  ),
                ),
              ],
            ),

            // Reflection checkboxes
            Row(
              children: [
                SizedBox(
                  width: 120,
                  child: Text(
                    'Reflections:',
                    style: TextStyle(
                      fontSize: 12,
                      color: _textColor,
                    ),
                  ),
                ),
                Row(
                  children: [
                    Checkbox(
                      value: _reflectX,
                      onChanged: (bool? value) {
                        setState(() {
                          _reflectX = value ?? false;
                        });
                      },
                      activeColor: _primaryColor,
                    ),
                    Text(
                      'Reflect across x-axis',
                      style: TextStyle(
                        fontSize: 12,
                        color: _textColor,
                      ),
                    ),
                  ],
                ),
                const SizedBox(width: 16),
                Row(
                  children: [
                    Checkbox(
                      value: _reflectY,
                      onChanged: (bool? value) {
                        setState(() {
                          _reflectY = value ?? false;
                        });
                      },
                      activeColor: _primaryColor,
                    ),
                    Text(
                      'Reflect across y-axis',
                      style: TextStyle(
                        fontSize: 12,
                        color: _textColor,
                      ),
                    ),
                  ],
                ),
              ],
            ),

            const SizedBox(height: 16),

            // Reset button
            Center(
              child: ElevatedButton(
                onPressed: _resetTransformations,
                style: ElevatedButton.styleFrom(
                  backgroundColor: _secondaryColor,
                  foregroundColor: Colors.white,
                ),
                child: const Text('Reset Transformations'),
              ),
            ),

            const SizedBox(height: 16),

            // Examples section
            Text(
              'Examples:',
              style: TextStyle(
                fontSize: 14,
                fontWeight: FontWeight.bold,
                color: _textColor,
              ),
            ),

            const SizedBox(height: 8),

            // Example cards
            SizedBox(
              height: 120,
              child: ListView.builder(
                scrollDirection: Axis.horizontal,
                itemCount: _examples.length,
                itemBuilder: (context, index) {
                  final example = _examples[index];
                  return Card(
                    margin: const EdgeInsets.only(right: 8),
                    color: _secondaryColor.withOpacity(0.1),
                    child: InkWell(
                      onTap: () => _loadExample(example),
                      child: Container(
                        width: 200,
                        padding: const EdgeInsets.all(8),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              example['title'],
                              style: TextStyle(
                                fontSize: 14,
                                fontWeight: FontWeight.bold,
                                color: _textColor,
                              ),
                            ),
                            const SizedBox(height: 4),
                            Text(
                              example['description'],
                              style: TextStyle(
                                fontSize: 12,
                                color: _textColor.withOpacity(0.7),
                              ),
                            ),
                          ],
                        ),
                      ),
                    ),
                  );
                },
              ),
            ),
          ],
        ),
      ),
    );
  }
}

// Extension to capitalize strings
extension StringExtension on String {
  String capitalize() {
    return "${this[0].toUpperCase()}${this.substring(1)}";
  }
}

// Custom painter for drawing the function graph
class FunctionGraphPainter extends CustomPainter {
  final double minX;
  final double maxX;
  final double minY;
  final double maxY;
  final int gridLines;
  final Function(double) evaluateFunction;
  final Color primaryColor;
  final Color secondaryColor;
  final Color accentColor;
  final Color backgroundColor;

  FunctionGraphPainter({
    required this.minX,
    required this.maxX,
    required this.minY,
    required this.maxY,
    required this.gridLines,
    required this.evaluateFunction,
    required this.primaryColor,
    required this.secondaryColor,
    required this.accentColor,
    required this.backgroundColor,
  });

  @override
  void paint(Canvas canvas, Size size) {
    final Paint gridPaint = Paint()
      ..color = secondaryColor.withOpacity(0.3)
      ..strokeWidth = 0.5;

    final Paint axisPaint = Paint()
      ..color = secondaryColor
      ..strokeWidth = 1.0;

    final Paint functionPaint = Paint()
      ..color = primaryColor
      ..strokeWidth = 2.0
      ..style = PaintingStyle.stroke;

    // Draw grid
    _drawGrid(canvas, size, gridPaint);

    // Draw axes
    _drawAxes(canvas, size, axisPaint);

    // Draw function
    _drawFunction(canvas, size, functionPaint);
  }

  void _drawGrid(Canvas canvas, Size size, Paint paint) {
    // Draw vertical grid lines
    double stepX = size.width / gridLines;
    for (int i = 0; i <= gridLines; i++) {
      double x = i * stepX;
      canvas.drawLine(
        Offset(x, 0),
        Offset(x, size.height),
        paint,
      );
    }

    // Draw horizontal grid lines
    double stepY = size.height / gridLines;
    for (int i = 0; i <= gridLines; i++) {
      double y = i * stepY;
      canvas.drawLine(
        Offset(0, y),
        Offset(size.width, y),
        paint,
      );
    }
  }

  void _drawAxes(Canvas canvas, Size size, Paint paint) {
    // Draw x-axis
    double yAxis = size.height * (maxY / (maxY - minY));
    if (yAxis >= 0 && yAxis <= size.height) {
      canvas.drawLine(
        Offset(0, yAxis),
        Offset(size.width, yAxis),
        paint,
      );
    }

    // Draw y-axis
    double xAxis = size.width * (-minX / (maxX - minX));
    if (xAxis >= 0 && xAxis <= size.width) {
      canvas.drawLine(
        Offset(xAxis, 0),
        Offset(xAxis, size.height),
        paint,
      );
    }
  }

  void _drawFunction(Canvas canvas, Size size, Paint paint) {
    Path path = Path();
    bool isFirstPoint = true;

    // Calculate points
    for (int i = 0; i < size.width; i++) {
      // Convert from screen coordinates to graph coordinates
      double x = minX + (i / size.width) * (maxX - minX);

      // Evaluate function
      double y = evaluateFunction(x);

      // Check if y is within bounds
      if (y.isNaN || y.isInfinite || y < minY || y > maxY) {
        isFirstPoint = true;
        continue;
      }

      // Convert from graph coordinates to screen coordinates
      double screenY = size.height - ((y - minY) / (maxY - minY)) * size.height;

      if (isFirstPoint) {
        path.moveTo(i.toDouble(), screenY);
        isFirstPoint = false;
      } else {
        path.lineTo(i.toDouble(), screenY);
      }
    }

    canvas.drawPath(path, paint);
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) {
    return true;
  }
}
