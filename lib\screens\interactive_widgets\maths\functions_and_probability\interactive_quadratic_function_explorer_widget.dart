import 'package:flutter/material.dart';
import 'dart:math' as math;

/// A widget that helps users explore quadratic functions by visualizing how changes to parameters affect the graph
class InteractiveQuadraticFunctionExplorerWidget extends StatefulWidget {
  /// The data for this widget
  final Map<String, dynamic> data;

  /// Optional callback when the widget state changes
  final Function(bool isCompleted)? onStateChanged;

  const InteractiveQuadraticFunctionExplorerWidget({
    Key? key,
    required this.data,
    this.onStateChanged,
  }) : super(key: key);

  /// Factory constructor to create the widget from JSON data
  factory InteractiveQuadraticFunctionExplorerWidget.fromData(Map<String, dynamic> data) {
    return InteractiveQuadraticFunctionExplorerWidget(
      data: data,
    );
  }

  @override
  State<InteractiveQuadraticFunctionExplorerWidget> createState() => _InteractiveQuadraticFunctionExplorerWidgetState();
}

class _InteractiveQuadraticFunctionExplorerWidgetState extends State<InteractiveQuadraticFunctionExplorerWidget> {
  // Coefficients of the quadratic function f(x) = ax² + bx + c
  double _a = 1.0;
  double _b = 0.0;
  double _c = 0.0;

  // Whether the widget is completed
  bool _isCompleted = false;

  // Current form of the quadratic function
  String _currentForm = 'standard';

  // Current example
  int _currentExampleIndex = 0;

  // Colors
  late Color _primaryColor;
  late Color _secondaryColor;
  late Color _accentColor;
  late Color _backgroundColor;
  late Color _textColor;

  // List of real-world examples
  late List<Map<String, dynamic>> _examples;

  // Whether to show key points
  bool _showKeyPoints = true;

  // Whether to show grid
  bool _showGrid = true;

  // Whether to show x and y intercepts
  bool _showIntercepts = true;

  // Whether to show the equation
  bool _showEquation = true;

  // Whether to show the example
  bool _showExample = false;

  // Whether to show the form selector
  bool _showFormSelector = true;

  // Vertex coordinates (for vertex form)
  double _h = 0.0;
  double _k = 0.0;

  // Roots (for factored form)
  double? _root1;
  double? _root2;

  @override
  void initState() {
    super.initState();

    // Initialize colors
    _primaryColor = _parseColor(widget.data['primary_color']) ?? Colors.blue;
    _secondaryColor = _parseColor(widget.data['secondary_color']) ?? Colors.orange;
    _accentColor = _parseColor(widget.data['accent_color']) ?? Colors.green;
    _backgroundColor = _parseColor(widget.data['background_color']) ?? Colors.white;
    _textColor = _parseColor(widget.data['text_color']) ?? Colors.black87;

    // Initialize examples
    _examples = widget.data['examples'] != null
        ? List<Map<String, dynamic>>.from(widget.data['examples'])
        : _getDefaultExamples();

    // Calculate vertex and roots
    _updateVertexAndRoots();
  }

  // Parse color from hex string
  Color? _parseColor(String? hexString) {
    if (hexString == null) return null;

    hexString = hexString.replaceAll('#', '');
    if (hexString.length == 6) {
      hexString = 'FF$hexString';
    }

    return Color(int.parse(hexString, radix: 16));
  }

  // Get default examples if none provided
  List<Map<String, dynamic>> _getDefaultExamples() {
    return [
      {
        'title': 'Projectile Motion',
        'description': 'A ball is thrown upward with an initial velocity of 20 m/s from a height of 1.5 m. The height h(t) of the ball after t seconds is given by h(t) = -4.9t² + 20t + 1.5.',
        'a': -4.9,
        'b': 20.0,
        'c': 1.5,
        'a_meaning': 'The coefficient -4.9 represents half the acceleration due to gravity (-9.8 m/s²).',
        'b_meaning': 'The coefficient 20 represents the initial velocity in m/s.',
        'c_meaning': 'The constant 1.5 represents the initial height in meters.',
        'x_axis': 'Time (seconds)',
        'y_axis': 'Height (meters)',
      },
      {
        'title': 'Revenue Model',
        'description': 'A company\'s revenue R(p) in thousands of dollars depends on the price p of their product according to R(p) = -2p^2 + 60p - 100.',
        'a': -2.0,
        'b': 60.0,
        'c': -100.0,
        'a_meaning': 'The negative coefficient -2 indicates diminishing returns as price increases.',
        'b_meaning': 'The coefficient 60 relates to the optimal price point.',
        'c_meaning': 'The constant -100 represents fixed costs.',
        'x_axis': 'Price (\$)',
        'y_axis': 'Revenue (\$1000s)',
      },
      {
        'title': 'Bridge Arch',
        'description': 'The shape of a parabolic bridge arch can be modeled by h(x) = 0.05x^2 - 5x + 125, where h is the height in meters and x is the horizontal distance in meters from one end.',
        'a': 0.05,
        'b': -5.0,
        'c': 125.0,
        'a_meaning': 'The coefficient 0.05 determines how steep the arch is.',
        'b_meaning': 'The coefficient -5 determines the horizontal shift of the arch.',
        'c_meaning': 'The constant 125 represents the maximum height of the arch in meters.',
        'x_axis': 'Horizontal distance (meters)',
        'y_axis': 'Height (meters)',
      },
      {
        'title': 'Profit Function',
        'description': 'A company\'s profit P(x) in dollars when producing x items is given by P(x) = -0.5x^2 + 100x - 1000.',
        'a': -0.5,
        'b': 100.0,
        'c': -1000.0,
        'a_meaning': 'The negative coefficient -0.5 represents diminishing returns due to increasing costs.',
        'b_meaning': 'The coefficient 100 represents the revenue per item.',
        'c_meaning': 'The constant -1000 represents fixed costs.',
        'x_axis': 'Number of items',
        'y_axis': 'Profit (\$)',
      },
    ];
  }

  // Update vertex and roots based on current coefficients
  void _updateVertexAndRoots() {
    // Calculate vertex
    _h = -_b / (2 * _a);
    _k = _c - _b * _b / (4 * _a);

    // Calculate discriminant
    double discriminant = _b * _b - 4 * _a * _c;

    // Calculate roots if they exist
    if (discriminant >= 0) {
      _root1 = (-_b + math.sqrt(discriminant)) / (2 * _a);
      _root2 = (-_b - math.sqrt(discriminant)) / (2 * _a);

      // If roots are very close, consider them equal
      if ((_root1! - _root2!).abs() < 1e-10) {
        _root2 = _root1;
      }
    } else {
      _root1 = null;
      _root2 = null;
    }
  }

  // Load an example
  void _loadExample(int index) {
    if (index >= 0 && index < _examples.length) {
      setState(() {
        _currentExampleIndex = index;
        _a = _examples[index]['a'];
        _b = _examples[index]['b'];
        _c = _examples[index]['c'];
        _updateVertexAndRoots();
        _showExample = true;
      });
    }
  }

  // Reset to default values
  void _resetToDefault() {
    setState(() {
      _a = 1.0;
      _b = 0.0;
      _c = 0.0;
      _updateVertexAndRoots();
      _showExample = false;
    });
  }

  // Toggle key points visibility
  void _toggleKeyPoints() {
    setState(() {
      _showKeyPoints = !_showKeyPoints;
    });
  }

  // Toggle grid visibility
  void _toggleGrid() {
    setState(() {
      _showGrid = !_showGrid;
    });
  }

  // Toggle intercepts visibility
  void _toggleIntercepts() {
    setState(() {
      _showIntercepts = !_showIntercepts;
    });
  }

  // Toggle equation visibility
  void _toggleEquation() {
    setState(() {
      _showEquation = !_showEquation;
    });
  }

  // Change the form of the quadratic function
  void _changeForm(String form) {
    setState(() {
      _currentForm = form;
    });
  }

  // Get the equation text based on the current form
  String _getEquationText() {
    switch (_currentForm) {
      case 'standard':
        String aText = _a == 1 ? '' : _a == -1 ? '-' : '${_a.toStringAsFixed(1)}';
        String bSign = _b >= 0 ? '+' : '';
        String bText = _b == 0 ? '' : '$bSign ${_b.toStringAsFixed(1)}';
        String cSign = _c >= 0 ? '+' : '';
        String cText = _c == 0 ? '' : '$cSign ${_c.toStringAsFixed(1)}';
        return 'f(x) = ${aText}x² $bText$cText';
      case 'vertex':
        String aText = _a == 1 ? '' : _a == -1 ? '-' : '${_a.toStringAsFixed(1)}';
        String hSign = _h >= 0 ? '-' : '+';
        String hText = _h == 0 ? '' : ' $hSign ${_h.abs().toStringAsFixed(1)}';
        String kSign = _k >= 0 ? '+' : '';
        String kText = _k == 0 ? '' : ' $kSign ${_k.toStringAsFixed(1)}';
        return 'f(x) = ${aText}(x$hText)² $kText';
      case 'factored':
        if (_root1 == null || _root2 == null) {
          return 'f(x) = ${_a.toStringAsFixed(1)}x² ${_b >= 0 ? '+' : ''} ${_b.toStringAsFixed(1)}x ${_c >= 0 ? '+' : ''} ${_c.toStringAsFixed(1)} (No real roots)';
        } else {
          String aText = _a == 1 ? '' : _a == -1 ? '-' : '${_a.toStringAsFixed(1)}';
          String r1Sign = _root1! >= 0 ? '-' : '+';
          String r2Sign = _root2! >= 0 ? '-' : '+';
          return 'f(x) = ${aText}(x $r1Sign ${_root1!.abs().toStringAsFixed(1)})(x $r2Sign ${_root2!.abs().toStringAsFixed(1)})';
        }
      default:
        return 'f(x) = ${_a.toStringAsFixed(1)}x² ${_b >= 0 ? '+' : ''} ${_b.toStringAsFixed(1)}x ${_c >= 0 ? '+' : ''} ${_c.toStringAsFixed(1)}';
    }
  }

  // Mark the widget as completed
  void _markAsCompleted() {
    if (!_isCompleted) {
      setState(() {
        _isCompleted = true;
      });

      // Notify parent of state change
      widget.onStateChanged?.call(true);
    }
  }

  // Build the graph and controls section
  Widget _buildGraphAndControls() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Function form selector
        if (_showFormSelector)
          Container(
            padding: const EdgeInsets.all(8),
            decoration: BoxDecoration(
              color: Colors.grey[100],
              borderRadius: BorderRadius.circular(8),
              border: Border.all(color: Colors.grey[300]!),
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'Function Form:',
                  style: TextStyle(
                    fontSize: 14,
                    fontWeight: FontWeight.bold,
                    color: _textColor,
                  ),
                ),
                const SizedBox(height: 8),
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                  children: [
                    _buildFormButton('standard', 'Standard'),
                    _buildFormButton('vertex', 'Vertex'),
                    _buildFormButton('factored', 'Factored'),
                  ],
                ),
              ],
            ),
          ),

        const SizedBox(height: 16),

        // Equation display
        if (_showEquation)
          Container(
            width: double.infinity,
            padding: const EdgeInsets.all(12),
            decoration: BoxDecoration(
              color: _primaryColor.withOpacity(0.1),
              borderRadius: BorderRadius.circular(8),
              border: Border.all(color: _primaryColor),
            ),
            child: Text(
              _getEquationText(),
              textAlign: TextAlign.center,
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
                color: _primaryColor,
              ),
            ),
          ),

        const SizedBox(height: 16),

        // Graph
        Container(
          height: 250,
          width: double.infinity,
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(8),
            border: Border.all(color: Colors.grey[300]!),
          ),
          child: ClipRRect(
            borderRadius: BorderRadius.circular(8),
            child: CustomPaint(
              painter: QuadraticFunctionGraphPainter(
                a: _a,
                b: _b,
                c: _c,
                h: _h,
                k: _k,
                root1: _root1,
                root2: _root2,
                showGrid: _showGrid,
                showKeyPoints: _showKeyPoints,
                showIntercepts: _showIntercepts,
                primaryColor: _primaryColor,
                secondaryColor: _secondaryColor,
                accentColor: _accentColor,
              ),
            ),
          ),
        ),

        const SizedBox(height: 16),

        // Graph controls
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceEvenly,
          children: [
            _buildControlButton(
              icon: Icons.grid_on,
              label: 'Grid',
              isActive: _showGrid,
              onPressed: _toggleGrid,
            ),
            _buildControlButton(
              icon: Icons.location_on,
              label: 'Key Points',
              isActive: _showKeyPoints,
              onPressed: _toggleKeyPoints,
            ),
            _buildControlButton(
              icon: Icons.change_history,
              label: 'Intercepts',
              isActive: _showIntercepts,
              onPressed: _toggleIntercepts,
            ),
            _buildControlButton(
              icon: Icons.functions,
              label: 'Equation',
              isActive: _showEquation,
              onPressed: _toggleEquation,
            ),
          ],
        ),

        const SizedBox(height: 16),

        // Coefficient a slider
        Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Coefficient a: ${_a.toStringAsFixed(1)}',
              style: TextStyle(
                fontSize: 14,
                fontWeight: FontWeight.bold,
                color: _textColor,
              ),
            ),
            Slider(
              value: _a,
              min: -5.0,
              max: 5.0,
              divisions: 100,
              activeColor: _primaryColor,
              inactiveColor: _primaryColor.withOpacity(0.3),
              onChanged: (value) {
                // Prevent a from being exactly 0 (not a quadratic function)
                if (value.abs() < 0.1) {
                  value = value >= 0 ? 0.1 : -0.1;
                }
                setState(() {
                  _a = value;
                  _updateVertexAndRoots();
                });
              },
            ),
          ],
        ),

        // Coefficient b slider
        Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Coefficient b: ${_b.toStringAsFixed(1)}',
              style: TextStyle(
                fontSize: 14,
                fontWeight: FontWeight.bold,
                color: _textColor,
              ),
            ),
            Slider(
              value: _b,
              min: -10.0,
              max: 10.0,
              divisions: 100,
              activeColor: _secondaryColor,
              inactiveColor: _secondaryColor.withOpacity(0.3),
              onChanged: (value) {
                setState(() {
                  _b = value;
                  _updateVertexAndRoots();
                });
              },
            ),
          ],
        ),

        // Coefficient c slider
        Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Coefficient c: ${_c.toStringAsFixed(1)}',
              style: TextStyle(
                fontSize: 14,
                fontWeight: FontWeight.bold,
                color: _textColor,
              ),
            ),
            Slider(
              value: _c,
              min: -10.0,
              max: 10.0,
              divisions: 100,
              activeColor: _accentColor,
              inactiveColor: _accentColor.withOpacity(0.3),
              onChanged: (value) {
                setState(() {
                  _c = value;
                  _updateVertexAndRoots();
                });
              },
            ),
          ],
        ),

        const SizedBox(height: 16),

        // Key points information
        Container(
          padding: const EdgeInsets.all(8),
          decoration: BoxDecoration(
            color: Colors.grey[100],
            borderRadius: BorderRadius.circular(8),
            border: Border.all(color: Colors.grey[300]!),
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                'Key Points:',
                style: TextStyle(
                  fontSize: 14,
                  fontWeight: FontWeight.bold,
                  color: _textColor,
                ),
              ),
              const SizedBox(height: 4),
              Text(
                'Vertex: (${_h.toStringAsFixed(2)}, ${_k.toStringAsFixed(2)})',
                style: TextStyle(
                  fontSize: 14,
                  color: _secondaryColor,
                ),
              ),
              Text(
                'Y-intercept: (0, ${_c.toStringAsFixed(2)})',
                style: TextStyle(
                  fontSize: 14,
                  color: _accentColor,
                ),
              ),
              if (_root1 != null && _root2 != null)
                Text(
                  'X-intercepts: (${_root1!.toStringAsFixed(2)}, 0), (${_root2!.toStringAsFixed(2)}, 0)',
                  style: TextStyle(
                    fontSize: 14,
                    color: _accentColor,
                  ),
                )
              else
                Text(
                  'X-intercepts: None (no real roots)',
                  style: TextStyle(
                    fontSize: 14,
                    color: _accentColor,
                  ),
                ),
            ],
          ),
        ),

        const SizedBox(height: 16),

        // Examples and reset buttons
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            ElevatedButton(
              onPressed: _resetToDefault,
              style: ElevatedButton.styleFrom(
                backgroundColor: _secondaryColor,
                foregroundColor: Colors.white,
              ),
              child: const Text('Reset'),
            ),
            ElevatedButton(
              onPressed: () => _showExamplesDialog(context),
              style: ElevatedButton.styleFrom(
                backgroundColor: _primaryColor,
                foregroundColor: Colors.white,
              ),
              child: const Text('Examples'),
            ),
            ElevatedButton(
              onPressed: _markAsCompleted,
              style: ElevatedButton.styleFrom(
                backgroundColor: _accentColor,
                foregroundColor: Colors.white,
              ),
              child: const Text('Complete'),
            ),
          ],
        ),
      ],
    );
  }

  // Build a form selection button
  Widget _buildFormButton(String form, String label) {
    final isSelected = _currentForm == form;

    return ElevatedButton(
      onPressed: () => _changeForm(form),
      style: ElevatedButton.styleFrom(
        backgroundColor: isSelected ? _primaryColor : Colors.grey[300],
        foregroundColor: isSelected ? Colors.white : _textColor,
        padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      ),
      child: Text(label),
    );
  }

  // Build a control button
  Widget _buildControlButton({
    required IconData icon,
    required String label,
    required bool isActive,
    required VoidCallback onPressed,
  }) {
    return Column(
      children: [
        IconButton(
          icon: Icon(icon),
          color: isActive ? _primaryColor : Colors.grey,
          onPressed: onPressed,
        ),
        Text(
          label,
          style: TextStyle(
            fontSize: 12,
            color: isActive ? _primaryColor : Colors.grey,
          ),
        ),
      ],
    );
  }

  // Build the example section
  Widget _buildExampleSection() {
    final example = _examples[_currentExampleIndex];

    return Container(
      margin: const EdgeInsets.only(top: 16),
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: _accentColor.withOpacity(0.1),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: _accentColor),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Example: ${example['title']}',
            style: TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.bold,
              color: _accentColor,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            example['description'],
            style: TextStyle(
              fontSize: 14,
              color: _textColor,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            'Coefficient a meaning: ${example['a_meaning']}',
            style: TextStyle(
              fontSize: 14,
              color: _textColor,
            ),
          ),
          const SizedBox(height: 4),
          Text(
            'Coefficient b meaning: ${example['b_meaning']}',
            style: TextStyle(
              fontSize: 14,
              color: _textColor,
            ),
          ),
          const SizedBox(height: 4),
          Text(
            'Coefficient c meaning: ${example['c_meaning']}',
            style: TextStyle(
              fontSize: 14,
              color: _textColor,
            ),
          ),
          const SizedBox(height: 8),
          Row(
            children: [
              Text(
                'X-axis: ${example['x_axis']}',
                style: TextStyle(
                  fontSize: 14,
                  fontStyle: FontStyle.italic,
                  color: _textColor,
                ),
              ),
              const SizedBox(width: 16),
              Text(
                'Y-axis: ${example['y_axis']}',
                style: TextStyle(
                  fontSize: 14,
                  fontStyle: FontStyle.italic,
                  color: _textColor,
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  // Show examples dialog
  void _showExamplesDialog(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(
          'Real-World Examples',
          style: TextStyle(
            color: _primaryColor,
            fontWeight: FontWeight.bold,
          ),
        ),
        content: Container(
          width: double.maxFinite,
          child: ListView.builder(
            shrinkWrap: true,
            itemCount: _examples.length,
            itemBuilder: (context, index) {
              final example = _examples[index];
              return ListTile(
                title: Text(example['title']),
                subtitle: Text(
                  example['description'],
                  maxLines: 2,
                  overflow: TextOverflow.ellipsis,
                ),
                onTap: () {
                  Navigator.of(context).pop();
                  _loadExample(index);
                },
              );
            },
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Cancel'),
          ),
        ],
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: _backgroundColor,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withOpacity(0.2),
            spreadRadius: 1,
            blurRadius: 3,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Title
          Text(
            widget.data['title'] ?? 'Quadratic Function Explorer',
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
              color: _textColor,
            ),
          ),

          const SizedBox(height: 8),

          // Description
          if (widget.data['description'] != null)
            Text(
              widget.data['description'],
              style: TextStyle(
                fontSize: 14,
                color: _textColor.withOpacity(0.8),
              ),
            ),

          const SizedBox(height: 16),

          // Graph and controls
          _buildGraphAndControls(),

          // Example section
          if (_showExample)
            _buildExampleSection(),

          // Widget name tag
          if (widget.data['showNameTag'] ?? true)
            Padding(
              padding: const EdgeInsets.only(top: 8),
              child: Text(
                'InteractiveQuadraticFunctionExplorer',
                style: TextStyle(
                  color: Colors.grey[600],
                  fontSize: 12,
                  fontStyle: FontStyle.italic,
                ),
              ),
            ),
        ],
      ),
    );
  }
}

/// Custom painter for drawing quadratic function graphs
class QuadraticFunctionGraphPainter extends CustomPainter {
  final double a;
  final double b;
  final double c;
  final double h;
  final double k;
  final double? root1;
  final double? root2;
  final bool showGrid;
  final bool showKeyPoints;
  final bool showIntercepts;
  final Color primaryColor;
  final Color secondaryColor;
  final Color accentColor;

  // Constants for graph scaling
  final double minX = -5.0;
  final double maxX = 5.0;
  final double minY = -5.0;
  final double maxY = 5.0;

  QuadraticFunctionGraphPainter({
    required this.a,
    required this.b,
    required this.c,
    required this.h,
    required this.k,
    required this.root1,
    required this.root2,
    required this.showGrid,
    required this.showKeyPoints,
    required this.showIntercepts,
    required this.primaryColor,
    required this.secondaryColor,
    required this.accentColor,
  });

  @override
  void paint(Canvas canvas, Size size) {
    final paint = Paint()
      ..color = primaryColor
      ..strokeWidth = 2.0
      ..style = PaintingStyle.stroke;

    final gridPaint = Paint()
      ..color = Colors.grey[300]!
      ..strokeWidth = 0.5
      ..style = PaintingStyle.stroke;

    final axisPaint = Paint()
      ..color = Colors.black87
      ..strokeWidth = 1.0
      ..style = PaintingStyle.stroke;

    final vertexPaint = Paint()
      ..color = secondaryColor
      ..strokeWidth = 4.0
      ..style = PaintingStyle.fill;

    final interceptPaint = Paint()
      ..color = accentColor
      ..strokeWidth = 4.0
      ..style = PaintingStyle.fill;

    // Draw grid
    if (showGrid) {
      _drawGrid(canvas, size, gridPaint);
    }

    // Draw axes
    _drawAxes(canvas, size, axisPaint);

    // Draw function
    _drawFunction(canvas, size, paint);

    // Draw key points
    if (showKeyPoints) {
      // Draw vertex
      final vertexScreenX = _mapXToScreen(h, size);
      final vertexScreenY = _mapYToScreen(k, size);

      canvas.drawCircle(
        Offset(vertexScreenX, vertexScreenY),
        6,
        vertexPaint,
      );
    }

    // Draw intercepts
    if (showIntercepts) {
      // Draw y-intercept
      final yInterceptScreenX = _mapXToScreen(0, size);
      final yInterceptScreenY = _mapYToScreen(c, size);

      canvas.drawCircle(
        Offset(yInterceptScreenX, yInterceptScreenY),
        6,
        interceptPaint,
      );

      // Draw x-intercepts if they exist within the visible range
      if (root1 != null && root1! >= minX && root1! <= maxX) {
        final root1ScreenX = _mapXToScreen(root1!, size);
        final root1ScreenY = _mapYToScreen(0, size);

        canvas.drawCircle(
          Offset(root1ScreenX, root1ScreenY),
          6,
          interceptPaint,
        );
      }

      if (root2 != null && root2! >= minX && root2! <= maxX && (root1 == null || (root1! - root2!).abs() > 0.01)) {
        final root2ScreenX = _mapXToScreen(root2!, size);
        final root2ScreenY = _mapYToScreen(0, size);

        canvas.drawCircle(
          Offset(root2ScreenX, root2ScreenY),
          6,
          interceptPaint,
        );
      }
    }

    // Draw labels
    _drawLabels(canvas, size);
  }

  void _drawGrid(Canvas canvas, Size size, Paint paint) {
    // Draw vertical grid lines
    for (double x = minX; x <= maxX; x += 1) {
      final screenX = _mapXToScreen(x, size);
      canvas.drawLine(
        Offset(screenX, 0),
        Offset(screenX, size.height),
        paint,
      );
    }

    // Draw horizontal grid lines
    for (double y = minY; y <= maxY; y += 1) {
      final screenY = _mapYToScreen(y, size);
      canvas.drawLine(
        Offset(0, screenY),
        Offset(size.width, screenY),
        paint,
      );
    }
  }

  void _drawAxes(Canvas canvas, Size size, Paint paint) {
    // Draw x-axis
    final yZero = _mapYToScreen(0, size);
    canvas.drawLine(
      Offset(0, yZero),
      Offset(size.width, yZero),
      paint,
    );

    // Draw y-axis
    final xZero = _mapXToScreen(0, size);
    canvas.drawLine(
      Offset(xZero, 0),
      Offset(xZero, size.height),
      paint,
    );

    // Draw axis ticks
    for (double x = minX; x <= maxX; x += 1) {
      if (x == 0) continue; // Skip origin
      final screenX = _mapXToScreen(x, size);
      canvas.drawLine(
        Offset(screenX, yZero - 5),
        Offset(screenX, yZero + 5),
        paint,
      );
    }

    for (double y = minY; y <= maxY; y += 1) {
      if (y == 0) continue; // Skip origin
      final screenY = _mapYToScreen(y, size);
      canvas.drawLine(
        Offset(xZero - 5, screenY),
        Offset(xZero + 5, screenY),
        paint,
      );
    }
  }

  void _drawFunction(Canvas canvas, Size size, Paint paint) {
    final path = Path();
    bool pathStarted = false;

    // Draw the quadratic function as a smooth curve
    for (double x = minX; x <= maxX; x += 0.1) {
      final y = a * x * x + b * x + c;

      // Skip points outside the visible y range
      if (y < minY - 5 || y > maxY + 5) {
        pathStarted = false;
        continue;
      }

      final screenX = _mapXToScreen(x, size);
      final screenY = _mapYToScreen(y, size);

      if (!pathStarted) {
        path.moveTo(screenX, screenY);
        pathStarted = true;
      } else {
        path.lineTo(screenX, screenY);
      }
    }

    canvas.drawPath(path, paint);
  }

  void _drawLabels(Canvas canvas, Size size) {
    final textStyle = TextStyle(
      color: Colors.black87,
      fontSize: 10,
    );
    final textPainter = TextPainter(
      textDirection: TextDirection.ltr,
    );

    // Draw axis labels
    final yZero = _mapYToScreen(0, size);
    final xZero = _mapXToScreen(0, size);

    // X-axis labels
    for (int i = minX.toInt(); i <= maxX.toInt(); i++) {
      if (i == 0) continue; // Skip zero as it's the origin
      final x = i.toDouble();
      final screenX = _mapXToScreen(x, size);

      textPainter.text = TextSpan(
        text: i.toString(),
        style: textStyle,
      );
      textPainter.layout();
      textPainter.paint(
        canvas,
        Offset(screenX - textPainter.width / 2, yZero + 10),
      );
    }

    // Y-axis labels
    for (int i = minY.toInt(); i <= maxY.toInt(); i++) {
      if (i == 0) continue; // Skip zero as it's the origin
      final y = i.toDouble();
      final screenY = _mapYToScreen(y, size);

      textPainter.text = TextSpan(
        text: i.toString(),
        style: textStyle,
      );
      textPainter.layout();
      textPainter.paint(
        canvas,
        Offset(xZero + 10, screenY - textPainter.height / 2),
      );
    }

    // Origin label
    textPainter.text = TextSpan(
      text: "0",
      style: textStyle,
    );
    textPainter.layout();
    textPainter.paint(
      canvas,
      Offset(xZero + 5, yZero + 5),
    );

    // Draw key point labels if showing key points
    if (showKeyPoints) {
      // Vertex label
      textPainter.text = TextSpan(
        text: "V(${h.toStringAsFixed(1)}, ${k.toStringAsFixed(1)})",
        style: TextStyle(
          color: secondaryColor,
          fontSize: 10,
          fontWeight: FontWeight.bold,
        ),
      );
      textPainter.layout();
      textPainter.paint(
        canvas,
        Offset(
          _mapXToScreen(h, size) + 10,
          _mapYToScreen(k, size) - 15,
        ),
      );
    }

    // Draw intercept labels if showing intercepts
    if (showIntercepts) {
      // Y-intercept label
      textPainter.text = TextSpan(
        text: "(0, ${c.toStringAsFixed(1)})",
        style: TextStyle(
          color: accentColor,
          fontSize: 10,
          fontWeight: FontWeight.bold,
        ),
      );
      textPainter.layout();
      textPainter.paint(
        canvas,
        Offset(
          _mapXToScreen(0, size) + 10,
          _mapYToScreen(c, size) - 15,
        ),
      );

      // X-intercept labels if they exist within the visible range
      if (root1 != null && root1! >= minX && root1! <= maxX) {
        textPainter.text = TextSpan(
          text: "(${root1!.toStringAsFixed(1)}, 0)",
          style: TextStyle(
            color: accentColor,
            fontSize: 10,
            fontWeight: FontWeight.bold,
          ),
        );
        textPainter.layout();
        textPainter.paint(
          canvas,
          Offset(
            _mapXToScreen(root1!, size) + 10,
            _mapYToScreen(0, size) + 10,
          ),
        );
      }

      if (root2 != null && root2! >= minX && root2! <= maxX && (root1 == null || (root1! - root2!).abs() > 0.01)) {
        textPainter.text = TextSpan(
          text: "(${root2!.toStringAsFixed(1)}, 0)",
          style: TextStyle(
            color: accentColor,
            fontSize: 10,
            fontWeight: FontWeight.bold,
          ),
        );
        textPainter.layout();
        textPainter.paint(
          canvas,
          Offset(
            _mapXToScreen(root2!, size) + 10,
            _mapYToScreen(0, size) + 10,
          ),
        );
      }
    }
  }

  // Map x coordinate from math space to screen space
  double _mapXToScreen(double x, Size size) {
    return size.width * (x - minX) / (maxX - minX);
  }

  // Map y coordinate from math space to screen space
  double _mapYToScreen(double y, Size size) {
    // Note: Screen coordinates have y increasing downward, math has y increasing upward
    return size.height * (1 - (y - minY) / (maxY - minY));
  }

  @override
  bool shouldRepaint(covariant QuadraticFunctionGraphPainter oldDelegate) {
    return oldDelegate.a != a ||
        oldDelegate.b != b ||
        oldDelegate.c != c ||
        oldDelegate.h != h ||
        oldDelegate.k != k ||
        oldDelegate.root1 != root1 ||
        oldDelegate.root2 != root2 ||
        oldDelegate.showGrid != showGrid ||
        oldDelegate.showKeyPoints != showKeyPoints ||
        oldDelegate.showIntercepts != showIntercepts ||
        oldDelegate.primaryColor != primaryColor ||
        oldDelegate.secondaryColor != secondaryColor ||
        oldDelegate.accentColor != accentColor;
  }
}