import 'package:flutter/material.dart';
import '../../models/interactive_widget_model.dart';
import '../../services/service_provider.dart';
import 'interactive_widget_factory.dart';
import 'widgets/gif_player_widget.dart';
import 'widgets/multiple_choice_widget.dart';
import 'widgets/interactive_diagram_widget.dart';
import 'widgets/mini_game_widget.dart';
import 'widgets/interactive_calculator_widget.dart';
import 'widgets/geometry_calculator_widget.dart';
import 'maths/functions_and_probability/function_grapher_widget.dart';
import 'widgets/math_whiteboard_widget.dart';
import 'widgets/truth_table_explorer_widget.dart';
import 'widgets/interactive_proof_contradiction_widget.dart';
import 'widgets/interactive_syllogism_builder_widget.dart';
import 'maths/equations_and_algebra/interactive_variable_explorer_widget.dart';
import 'maths/equations_and_algebra/interactive_expression_builder_widget.dart';
import 'widgets/interactive_number_line_explorer_widget.dart';
import 'maths/equations_and_algebra/interactive_inequality_visualizer_widget.dart';
import 'widgets/interactive_absolute_value_explorer_widget.dart';
import 'widgets/interactive_pendulum_simulation_widget.dart';
import 'widgets/interactive_unit_converter_widget.dart';
import 'widgets/interactive_sorting_algorithm_visualizer_widget.dart';
import 'maths/equations_and_algebra/interactive_compound_inequality_builder_widget.dart';
import 'widgets/interactive_scientific_method_flowchart_widget.dart';
import 'maths/equations_and_algebra/interactive_one_step_inequality_solver_widget.dart';
import 'maths/equations_and_algebra/interactive_two_step_inequality_solver_widget.dart';
import 'maths/equations_and_algebra/interactive_inequality_grapher_widget.dart';
import 'maths/equations_and_algebra/interactive_inequality_investigator_widget.dart';
import 'widgets/interactive_hypothesis_builder_widget.dart';
import 'widgets/interactive_experimental_design_tool_widget.dart';
import 'widgets/interactive_variable_identifier_widget.dart';
import 'widgets/interactive_data_visualization_tool_widget.dart';
import 'widgets/interactive_statistical_analysis_calculator_widget.dart';
import 'widgets/interactive_measurement_error_simulator_widget.dart';
import 'widgets/interactive_linked_list_visualizer_widget.dart'; // Add this import

import 'widgets/interactive_model_builder_widget.dart';
import 'widgets/interactive_theory_evaluation_tool_widget.dart';
import 'widgets/interactive_prediction_generator_widget.dart';
import 'widgets/interactive_model_comparison_tool_widget.dart';
import 'widgets/interactive_evidence_evaluator_widget.dart';
import 'widgets/interactive_argument_strength_analyzer_widget.dart';
import 'widgets/interactive_correlation_causation_explorer_widget.dart';
import 'widgets/interactive_logical_fallacy_detector_widget.dart';
import 'widgets/interactive_timeline_scientific_discoveries_widget.dart';
import 'widgets/interactive_emerging_technology_explorer_widget.dart';
import 'widgets/interactive_pythagorean_theorem_viz_widget.dart';
import 'widgets/interactive_circle_properties_viz_widget.dart';
import 'widgets/interactive_transformation_identification_game_widget.dart';
import 'widgets/rotation_interactive_game_widget.dart';
import 'widgets/interactive_function_machine_widget.dart';
import 'widgets/interactive_function_representation_tool_widget.dart';
import 'widgets/interactive_function_identifier_widget.dart';
import 'widgets/interactive_domain_range_explorer_widget.dart';
import 'widgets/interactive_function_notation_practice_widget.dart';
import 'widgets/interactive_function_fundamentals_test_widget.dart';
import 'widgets/interactive_linear_function_explorer_widget.dart';
import 'maths/functions_and_probability/interactive_quadratic_function_explorer_widget.dart';
import 'widgets/interactive_exponential_function_explorer_widget.dart';
import 'maths/functions_and_probability/interactive_probability_calculator_widget.dart';
import 'widgets/interactive_arithmetic_sequence_explorer_widget.dart';
import 'widgets/interactive_geometric_sequence_explorer_widget.dart';
import 'widgets/interactive_ratio_visualizer_widget.dart';
import 'widgets/interactive_algorithm_flowchart_builder_widget.dart';
import 'widgets/interactive_argument_analyzer_widget.dart';
import 'widgets/interactive_addition_subtraction_visualizer_widget.dart';
import 'widgets/interactive_multiplication_array_visualizer_widget.dart';
import 'widgets/interactive_division_visualizer_widget.dart';
import 'widgets/interactive_probability_distribution_visualizer_widget.dart';
import 'computational_thinking/interactive_problem_decomposition_tool.dart';
import 'computational_thinking/interactive_pattern_recognition_trainer.dart';
import 'computational_thinking/interactive_abstraction_builder.dart';
import 'computational_thinking/interactive_algorithm_designer.dart';
import 'computational_thinking/interactive_problem_solving_flowchart.dart';
import 'computational_thinking/interactive_problem_decomposition_challenge_test.dart';
import 'computational_thinking/interactive_pattern_identifier.dart';
import 'computational_thinking/interactive_abstraction_level_explorer.dart';
import 'computational_thinking/interactive_data_pattern_analyzer.dart';
import 'computational_thinking/interactive_model_abstraction_tool.dart';
import 'computational_thinking/interactive_pattern_application_simulator.dart';
import 'computational_thinking/interactive_pattern_recognition_challenge_test.dart';
import 'computational_thinking/interactive_pseudocode_generator.dart';
import 'computational_thinking/interactive_algorithm_visualization_tool.dart';
import 'computational_thinking/interactive_algorithm_comparison_tool.dart';
import 'computational_thinking/interactive_edge_case_analyzer.dart';
import 'computational_thinking/interactive_solution_refinement_tool.dart';
import 'computational_thinking/interactive_real_world_problem_solver.dart';
import 'computational_thinking/interactive_computational_model_builder.dart';
import 'computational_thinking/interactive_data_driven_decision_maker.dart';
import 'computational_thinking/interactive_systems_thinking_tool.dart';
import 'computational_thinking/interactive_computational_ethics_explorer.dart';
import 'computational_thinking/interactive_real_world_application_challenge_test.dart';
import 'computer_science/algorithms_data_structures/interactive_array_explorer.dart';
import 'physics/interactive_rotational_kinematics_calculator.dart';
import 'physics/interactive_moment_of_inertia_calculator.dart';
import 'physics/interactive_angular_momentum_conservation_demonstrator.dart';
import 'physics/interactive_rotational_kinetic_energy_calculator.dart';
import 'physics/interactive_rotational_motion_challenge.dart';

class InteractiveWidgetsShowcase extends StatefulWidget {
  const InteractiveWidgetsShowcase({super.key});

  @override
  State<InteractiveWidgetsShowcase> createState() =>
      _InteractiveWidgetsShowcaseState();
}

class _InteractiveWidgetsShowcaseState extends State<InteractiveWidgetsShowcase>
    with SingleTickerProviderStateMixin {
  List<String> _categories = [];
  List<String> _widgetTypes = [];
  String _selectedCategory = 'All';
  final TextEditingController _searchController = TextEditingController();
  String _searchQuery = '';
  TabController? _tabController;

  @override
  void initState() {
    super.initState();
    _searchController.addListener(_onSearchChanged);
  }

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
    _loadWidgets();
  }

  @override
  void dispose() {
    _searchController.removeListener(_onSearchChanged);
    _searchController.dispose();
    _tabController?.dispose();
    super.dispose();
  }

  void _onSearchChanged() {
    setState(() {
      _searchQuery = _searchController.text;
    });
  }

  void _loadWidgets() {
    final interactiveWidgetService =
        ServiceProvider.of(context).interactiveWidgetService;
    final courseService = ServiceProvider.of(context).courseService;

    setState(() {
      // Get course categories and add them to the list
      final List<String> courseCategories =
          courseService.categories.map((category) => category.id).toList();

      // Add 'All' at the beginning and sort the rest
      _categories = ['All', ...courseCategories];
      _widgetTypes = interactiveWidgetService.getWidgetTypes();

      // Initialize tab controller after getting categories
      _tabController?.dispose();
      _tabController = TabController(length: _categories.length, vsync: this);

      _tabController!.addListener(() {
        if (!_tabController!.indexIsChanging) {
          setState(() {
            _selectedCategory = _categories[_tabController!.index];
          });
        }
      });
    });
  }

  List<InteractiveWidgetModel> get _filteredWidgets {
    final interactiveWidgetService =
        ServiceProvider.of(context).interactiveWidgetService;
    List<InteractiveWidgetModel> widgets = [];

    // Get widgets based on selected category
    if (_selectedCategory == 'All') {
      widgets = interactiveWidgetService.getInteractiveWidgets();
    } else {
      widgets = interactiveWidgetService.getWidgetsByCategory(
        _selectedCategory,
      );
    }

    // Apply search filter
    if (_searchQuery.isNotEmpty) {
      widgets =
          widgets.where((widget) {
            return widget.name.toLowerCase().contains(
                  _searchQuery.toLowerCase(),
                ) ||
                widget.description.toLowerCase().contains(
                  _searchQuery.toLowerCase(),
                ) ||
                widget.type.toLowerCase().contains(_searchQuery.toLowerCase());
          }).toList();
    }

    return widgets;
  }

  Widget _buildWidgetCard(InteractiveWidgetModel widget) {
    final categoryColor = _getCategoryColor(widget.category);

    return Card(
      elevation: 3,
      margin: const EdgeInsets.symmetric(vertical: 8, horizontal: 16),
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
        side: BorderSide(
          color:
              widget.isImplemented
                  ? categoryColor.withAlpha(128)
                  : Colors.grey.shade300,
          width: 1,
        ),
      ),
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Header with name, type, and implementation status
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Expanded(
                  child: Text(
                    widget.name,
                    style: const TextStyle(
                      fontSize: 18,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
                if (widget.isImplemented)
                  Chip(
                    label: const Text(
                      'Implemented',
                      style: TextStyle(fontSize: 12, color: Colors.white),
                    ),
                    backgroundColor: Colors.green,
                  )
                else
                  Chip(
                    label: const Text(
                      'Not Implemented',
                      style: TextStyle(fontSize: 12),
                    ),
                    backgroundColor: Colors.grey.shade200,
                  ),
              ],
            ),

            // Category, type and subcategory tags
            Wrap(
              spacing: 8,
              children: [
                // Category chip
                Chip(
                  label: Text(
                    _getCategoryDisplayName(widget.category),
                    style: TextStyle(
                      fontSize: 12,
                      color: _getCategoryColor(widget.category),
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  backgroundColor: _getCategoryColor(
                    widget.category,
                  ).withAlpha(40),
                ),
                // Type chip
                Chip(
                  label: Text(
                    widget.type,
                    style: const TextStyle(fontSize: 12),
                  ),
                  backgroundColor: Colors.blue.shade100,
                ),
                // Subcategory chip (if available)
                if (widget.data.containsKey('subcategory'))
                  Chip(
                    label: Text(
                      widget.data['subcategory'] as String,
                      style: const TextStyle(fontSize: 12),
                    ),
                    backgroundColor: Colors.purple.shade100,
                  ),
              ],
            ),

            const SizedBox(height: 8),

            // Description
            Text(
              widget.description,
              style: TextStyle(fontSize: 14, color: Colors.grey[700]),
            ),

            const SizedBox(height: 16),

            // Widget preview
            if (widget.isImplemented)
              _buildWidgetPreview(widget)
            else
              Container(
                height: 100,
                width: double.infinity,
                decoration: BoxDecoration(
                  color: Colors.grey[200],
                  borderRadius: BorderRadius.circular(8),
                  border: Border.all(color: Colors.grey[300]!),
                ),
                child: const Center(
                  child: Text(
                    'Not implemented yet',
                    style: TextStyle(color: Colors.grey),
                  ),
                ),
              ),

            // Widget ID for reference
            Padding(
              padding: const EdgeInsets.only(top: 8.0),
              child: Text(
                'ID: ${widget.id}',
                style: TextStyle(
                  fontSize: 10,
                  color: Colors.grey[500],
                  fontStyle: FontStyle.italic,
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildWidgetPreview(InteractiveWidgetModel widget) {
    // Return the appropriate widget based on the type
    switch (widget.type) {
      case 'GIF Player':
        return GifPlayerWidget(widget: widget);
      case 'Multiple Choice':
        return MultipleChoiceWidget(widget: widget);
      case 'Interactive Diagram':
        return InteractiveDiagramWidget(widget: widget);
      case 'Mini-Game':
        return MiniGameWidget(widget: widget);
      case 'Interactive Calculator':
        return InteractiveCalculatorWidget(widget: widget);
      case 'Interactive Tool':
        // Check if it's the Geometry Calculator
        if (widget.id == 'math_interactive_tool_1') {
          return GeometryCalculatorWidget(widget: widget);
        }
        return _buildPlaceholderWidget(widget, Icons.build, Colors.orange);
      case 'Interactive Grapher':
        // Check if it's the Function Grapher
        if (widget.id == 'math_interactive_grapher_1') {
          return FunctionGrapherWidget(widget: widget);
        }
        return _buildPlaceholderWidget(widget, Icons.show_chart, Colors.purple);
      case 'Interactive Converter':
        return _buildPlaceholderWidget(widget, Icons.swap_horiz, Colors.blue);
      case 'Interactive Visualizer':
        return _buildPlaceholderWidget(widget, Icons.visibility, Colors.teal);
      case 'Interactive Simulation':
        return _buildPlaceholderWidget(widget, Icons.science, Colors.amber);
      case 'Interactive Whiteboard':
        // Check if it's the Math Whiteboard
        if (widget.id == 'math_interactive_whiteboard_1') {
          return MathWhiteboardWidget(widget: widget);
        }
        return _buildPlaceholderWidget(widget, Icons.edit, Colors.indigo);

      case 'interactive_model_builder':
        // Check if it's the Interactive Model Builder
        if (widget.id == 'interactive_model_builder_1') {
          return InteractiveModelBuilderWidget(
            data: widget.data,
            onStateChanged: (isCompleted) {
              // Optional callback
            },
          );
        }
        return _buildPlaceholderWidget(widget, Icons.model_training, Colors.green);

      case 'interactive_theory_evaluation_tool':
        // Check if it's the Interactive Theory Evaluation Tool
        if (widget.id == 'interactive_theory_evaluation_tool_1') {
          return InteractiveTheoryEvaluationToolWidget(
            data: widget.data,
            onStateChanged: (isCompleted) {
              // Optional callback
            },
          );
        }
        return _buildPlaceholderWidget(widget, Icons.science, Colors.indigo);

      case 'interactive_prediction_generator':
        // Check if it's the Interactive Prediction Generator
        if (widget.id == 'interactive_prediction_generator_1') {
          return InteractivePredictionGeneratorWidget(
            data: widget.data,
            onStateChanged: (isCompleted) {
              // Optional callback
            },
          );
        }
        return _buildPlaceholderWidget(widget, Icons.science, Colors.deepPurple);

      case 'interactive_model_comparison_tool':
        // Check if it's the Interactive Model Comparison Tool
        if (widget.id == 'interactive_model_comparison_tool_1') {
          return InteractiveModelComparisonToolWidget(
            data: widget.data,
            onStateChanged: (isCompleted) {
              // Optional callback
            },
          );
        }
        return _buildPlaceholderWidget(widget, Icons.compare_arrows, Colors.teal);

      case 'interactive_evidence_evaluator':
        // Check if it's the Interactive Evidence Evaluator
        if (widget.id == 'interactive_evidence_evaluator_1') {
          return InteractiveEvidenceEvaluatorWidget(
            data: widget.data,
            onStateChanged: (isCompleted) {
              // Optional callback
            },
          );
        }
        return _buildPlaceholderWidget(widget, Icons.fact_check, Colors.indigo);

      case 'interactive_argument_strength_analyzer':
        // Check if it's the Interactive Argument Strength Analyzer
        if (widget.id == 'interactive_argument_strength_analyzer_1') {
          return InteractiveArgumentStrengthAnalyzerWidget(
            data: widget.data,
            onStateChanged: (isCompleted) {
              // Optional callback
            },
          );
        }
        return _buildPlaceholderWidget(widget, Icons.analytics, Colors.deepPurple);

      case 'interactive_correlation_causation_explorer':
        // Check if it's the Interactive Correlation vs. Causation Explorer
        if (widget.id == 'interactive_correlation_causation_explorer_1') {
          return InteractiveCorrelationCausationExplorerWidget(
            data: widget.data,
            onStateChanged: (isCompleted) {
              // Optional callback
            },
          );
        }
        return _buildPlaceholderWidget(widget, Icons.compare_arrows, Colors.teal);

      case 'interactive_logical_fallacy_detector':
        // Check if it's the Interactive Logical Fallacy Detector
        if (widget.id == 'interactive_logical_fallacy_detector_1') {
          return InteractiveLogicalFallacyDetectorWidget(
            data: widget.data,
            onStateChanged: (isCompleted) {
              // Optional callback
            },
          );
        }
        return _buildPlaceholderWidget(widget, Icons.error_outline, Colors.deepPurple);

      case 'interactive_timeline_scientific_discoveries':
        // Check if it's the Interactive Timeline of Scientific Discoveries
        if (widget.id == 'interactive_timeline_scientific_discoveries_1') {
          return InteractiveTimelineScientificDiscoveriesWidget(
            data: widget.data,
            onStateChanged: (isCompleted) {
              // Optional callback
            },
          );
        }
        return _buildPlaceholderWidget(widget, Icons.timeline, Colors.indigo);

      case 'interactive_emerging_technology_explorer':
        // Check if it's the Interactive Emerging Technology Explorer
        if (widget.id == 'interactive_emerging_technology_explorer_1') {
          return InteractiveEmergingTechnologyExplorerWidget(
            data: widget.data,
            onStateChanged: (isCompleted) {
              // Optional callback
            },
          );
        }
        return _buildPlaceholderWidget(widget, Icons.science, Colors.teal);

      case 'interactive_pythagorean_theorem_viz':
        // Check if it's the Interactive Pythagorean Theorem Visualizer
        if (widget.id == 'interactive_pythagorean_theorem_viz_1') {
          return InteractivePythagoreanTheoremVizWidget(
            data: widget.data,
            onStateChanged: (isCompleted) {
              // Optional callback
            },
          );
        }
        return _buildPlaceholderWidget(widget, Icons.square_foot, Colors.indigo);

      case 'interactive_circle_properties_viz':
        // Check if it's the Interactive Circle Properties Visualizer
        if (widget.id == 'interactive_circle_properties_viz_1') {
          return InteractiveCirclePropertiesVizWidget(
            data: widget.data,
            onStateChanged: (isCompleted) {
              // Optional callback
            },
          );
        }
        return _buildPlaceholderWidget(widget, Icons.circle_outlined, Colors.purple);

      case 'interactive_probability_distribution_visualizer':
        // Check if it's the Interactive Probability Distribution Visualizer
        if (widget.id == 'interactive_probability_distribution_visualizer_1') {
          return InteractiveProbabilityDistributionVisualizerWidget(
            data: widget.data,
            onStateChanged: (isCompleted) {
              // Optional callback
            },
          );
        }
        return _buildPlaceholderWidget(widget, Icons.bar_chart, Colors.blue);

      case 'interactive_transformation_identification_game':
        // Check if it's the Interactive Transformation Identification Game
        if (widget.id == 'interactive_transformation_identification_game_1') {
          return InteractiveTransformationIdentificationGameWidget(
            data: widget.data,
            onStateChanged: (isCompleted) {
              // Optional callback
            },
          );
        }
        return _buildPlaceholderWidget(widget, Icons.transform, Colors.teal);

      case 'rotation_interactive_game':
        // Check if it's the Rotation Interactive Game
        if (widget.id == 'rotation_interactive_game_1') {
          return RotationInteractiveGameWidget(
            data: widget.data,
            onStateChanged: (isCompleted) {
              // Optional callback
            },
          );
        }
        return _buildPlaceholderWidget(widget, Icons.rotate_right, Colors.orange);

      case 'interactive_addition_subtraction_visualizer':
        // Check if it's the Interactive Addition/Subtraction Visualizer
        if (widget.id == 'interactive_addition_subtraction_visualizer_1') {
          return InteractiveAdditionSubtractionVisualizerWidget(
            data: widget.data,
            onStateChanged: (isCompleted) {
              // Optional callback
            },
          );
        }
        return _buildPlaceholderWidget(widget, Icons.calculate, Colors.blue);

      case 'interactive_multiplication_array_visualizer':
        // Check if it's the Interactive Multiplication Array Visualizer
        if (widget.id == 'interactive_multiplication_array_visualizer_1') {
          return InteractiveMultiplicationArrayVisualizerWidget(
            data: widget.data,
            onStateChanged: (isCompleted) {
              // Optional callback
            },
          );
        }
        return _buildPlaceholderWidget(widget, Icons.grid_on, Colors.indigo);

      case 'interactive_division_visualizer':
        // Check if it's the Interactive Division Visualizer
        if (widget.id == 'interactive_division_visualizer_1') {
          return InteractiveDivisionVisualizerWidget(
            data: widget.data,
            onStateChanged: (isCompleted) {
              // Optional callback
            },
          );
        }
        return _buildPlaceholderWidget(widget, Icons.pie_chart_outline, Colors.green);

      case 'interactive_function_machine':
        // Check if it's the Interactive Function Machine
        if (widget.id == 'interactive_function_machine_1') {
          return InteractiveFunctionMachineWidget(
            data: widget.data,
            onStateChanged: (isCompleted) {
              // Optional callback
            },
          );
        }
        return _buildPlaceholderWidget(widget, Icons.functions, Colors.indigo);

      case 'interactive_arithmetic_sequence_explorer':
        // Check if it's the Interactive Arithmetic Sequence Explorer
        if (widget.id == 'interactive_arithmetic_sequence_explorer_1') {
          return InteractiveArithmeticSequenceExplorerWidget(
            data: widget.data,
            onStateChanged: (isCompleted) {
              // Optional callback
            },
          );
        }
        return _buildPlaceholderWidget(widget, Icons.trending_up, Colors.teal);

      case 'interactive_geometric_sequence_explorer':
        // Check if it's the Interactive Geometric Sequence Explorer
        if (widget.id == 'interactive_geometric_sequence_explorer_1') {
          return InteractiveGeometricSequenceExplorerWidget(
            data: widget.data,
            onStateChanged: (isCompleted) {
              // Optional callback
            },
          );
        }
        return _buildPlaceholderWidget(widget, Icons.show_chart, Colors.purple);

      case 'interactive_ratio_visualizer':
        // Check if it's the Interactive Ratio Visualizer
        if (widget.id == 'interactive_ratio_visualizer_1') {
          return InteractiveRatioVisualizerWidget(
            data: widget.data,
            onStateChanged: (isCompleted) {
              // Optional callback
            },
          );
        }
        return _buildPlaceholderWidget(widget, Icons.pie_chart, Colors.orange);

      case 'interactive_algorithm_flowchart_builder':
        // Check if it's the Interactive Algorithm Flowchart Builder
        if (widget.id == 'interactive_algorithm_flowchart_builder_1') {
          return InteractiveAlgorithmFlowchartBuilderWidget(
            widget: InteractiveWidgetModel(
              id: widget.id,
              name: widget.name,
              type: widget.type,
              category: widget.category,
              description: widget.description,
              data: widget.data,
              isImplemented: true,
            ),
            onStateChanged: (isCompleted) {
              // Optional callback
            },
          );
        }
        return _buildPlaceholderWidget(widget, Icons.account_tree, Colors.blue);

      case 'interactive_argument_analyzer':
        // Check if it's the Interactive Argument Analyzer
        if (widget.id == 'interactive_argument_analyzer_1') {
          return InteractiveArgumentAnalyzerWidget(
            widget: InteractiveWidgetModel(
              id: widget.id,
              name: widget.name,
              type: widget.type,
              category: widget.category,
              description: widget.description,
              data: widget.data,
              isImplemented: true,
            ),
            onStateChanged: (isCompleted) {
              // Optional callback
            },
          );
        }
        return _buildPlaceholderWidget(widget, Icons.psychology, Colors.purple);

      case 'Computational_Thinking':
        // Check if it's the Interactive Problem Decomposition Tool
        if (widget.id == 'interactive_problem_decomposition_tool_1') {
          return InteractiveProblemDecompositionTool(
            // data: widget.data,
            // onStateChanged: (isCompleted) {},
          );
        }
        // Check if it's the Interactive Pattern Recognition Trainer
        if (widget.id == 'interactive_pattern_recognition_trainer_1') {
          return InteractivePatternRecognitionTrainer(
            // data: widget.data,
            // onStateChanged: (isCompleted) {},
          );
        }
        // Check if it's the Interactive Abstraction Builder
        if (widget.id == 'interactive_abstraction_builder_1') {
          return InteractiveAbstractionBuilder(
            // data: widget.data,
            // onStateChanged: (isCompleted) {},
          );
        }
        // Check if it's the Interactive Algorithm Designer
        if (widget.id == 'interactive_algorithm_designer_1') {
          return InteractiveAlgorithmDesigner(
            // data: widget.data,
            // onStateChanged: (isCompleted) {},
          );
        }
        // Check if it's the Interactive Problem-Solving Flowchart
        if (widget.id == 'interactive_problem_solving_flowchart_1') {
          return InteractiveProblemSolvingFlowchart(
            // data: widget.data,
            // onStateChanged: (isCompleted) {},
          );
        }
        // Check if it's the Interactive Problem Decomposition Challenge (Module Test)
        if (widget.id == 'interactive_problem_decomposition_challenge_test_1') {
          return InteractiveProblemDecompositionChallengeTest(
            // data: widget.data,
            // onStateChanged: (isCompleted) {},
          );
        }
        // Check if it's the Interactive Pattern Identifier
        if (widget.id == 'interactive_pattern_identifier_1') {
          return InteractivePatternIdentifier(
            // data: widget.data,
            // onStateChanged: (isCompleted) {},
          );
        }
        // Check if it's the Interactive Abstraction Level Explorer
        if (widget.id == 'interactive_abstraction_level_explorer_1') {
          return InteractiveAbstractionLevelExplorer(
            // data: widget.data,
            // onStateChanged: (isCompleted) {},
          );
        }
        // Check if it's the Interactive Data Pattern Analyzer
        if (widget.id == 'interactive_data_pattern_analyzer_1') {
          return InteractiveDataPatternAnalyzer(
            // data: widget.data,
            // onStateChanged: (isCompleted) {},
          );
        }
        // Check if it's the Interactive Model Abstraction Tool
        if (widget.id == 'interactive_model_abstraction_tool_1') {
          return InteractiveModelAbstractionTool(
            // data: widget.data,
            // onStateChanged: (isCompleted) {},
          );
        }
        // Check if it's the Interactive Pattern Application Simulator
        if (widget.id == 'interactive_pattern_application_simulator_1') {
          return InteractivePatternApplicationSimulator(
            // data: widget.data,
            // onStateChanged: (isCompleted) {},
          );
        }
        // Check if it's the Interactive Pattern Recognition Challenge (Module Test)
        if (widget.id == 'interactive_pattern_recognition_challenge_test_1') {
          return InteractivePatternRecognitionChallengeTest(
            // data: widget.data,
            // onStateChanged: (isCompleted) {},
          );
        }
        // Check if it's the Interactive Pseudocode Generator
        if (widget.id == 'interactive_pseudocode_generator_1') {
          return InteractivePseudocodeGenerator(
            // data: widget.data,
            // onStateChanged: (isCompleted) {},
          );
        }
        // Check if it's the Interactive Algorithm Visualization Tool
        if (widget.id == 'interactive_algorithm_visualization_tool_1') {
          return InteractiveAlgorithmVisualizationTool(
            // data: widget.data,
            // onStateChanged: (isCompleted) {},
          );
        }
        // Check if it's the Interactive Algorithm Comparison Tool
        if (widget.id == 'interactive_algorithm_comparison_tool_1') {
          return InteractiveAlgorithmComparisonTool(
            // data: widget.data,
            // onStateChanged: (isCompleted) {},
          );
        }
        // Check if it's the Interactive Edge Case Analyzer
        if (widget.id == 'interactive_edge_case_analyzer_1') {
          return InteractiveEdgeCaseAnalyzer(
            // data: widget.data,
            // onStateChanged: (isCompleted) {},
          );
        }
        // Check if it's the Interactive Solution Refinement Tool
        if (widget.id == 'interactive_solution_refinement_tool_1') {
          return InteractiveSolutionRefinementTool(
            // data: widget.data,
            // onStateChanged: (isCompleted) {},
          );
        }
        // Check if it's the Interactive Real-World Problem Solver
        if (widget.id == 'interactive_real_world_problem_solver') {
          return InteractiveRealWorldProblemSolver(
            // data: widget.data,
            // onStateChanged: (isCompleted) {},
          );
        }
        // Check if it's the Interactive Computational Model Builder
        if (widget.id == 'interactive_computational_model_builder') {
          return InteractiveComputationalModelBuilder(
            // data: widget.data,
            // onStateChanged: (isCompleted) {},
          );
        }
        // Check if it's the Interactive Data-Driven Decision Maker
        if (widget.id == 'interactive_data_driven_decision_maker') {
          return InteractiveDataDrivenDecisionMaker(
            // data: widget.data,
            // onStateChanged: (isCompleted) {},
          );
        }
        // Check if it's the Interactive Systems Thinking Tool
        if (widget.id == 'interactive_systems_thinking_tool') {
          return InteractiveSystemsThinkingTool(
            // data: widget.data,
            // onStateChanged: (isCompleted) {},
          );
        }
        // Check if it's the Interactive Computational Ethics Explorer
        if (widget.id == 'interactive_computational_ethics_explorer') {
          return InteractiveComputationalEthicsExplorer(
            // data: widget.data,
            // onStateChanged: (isCompleted) {},
          );
        }
        // Check if it's the Interactive Real-World Application Challenge (Module Test)
        if (widget.id == 'interactive_real_world_application_challenge_test') {
          return InteractiveRealWorldApplicationChallengeTest(
            // data: widget.data,
            // onStateChanged: (isCompleted) {},
          );
        }
        // Check if it's the Interactive Array Explorer
        if (widget.id == 'interactive_array_explorer_1') {
          return InteractiveArrayExplorer(
            widgetModel: widget,
          );
        }
        // Check if it's the Interactive Linked List Visualizer
        if (widget.id == 'interactive_linked_list_visualizer_1') {
          return InteractiveLinkedListVisualizer(
            widgetModel: widget,
          );
        }
        return _buildPlaceholderWidget(widget, Icons.computer, Colors.blueGrey);


      case 'Logic':
        // Check if it's the Truth Table Explorer
        if (widget.id == 'truth_table_explorer_1') {
          return TruthTableExplorerWidget(
            data: widget.data,
            onStateChanged: (isCompleted) {
              // Optional callback
            },
          );
        }
        // Check if it's the Interactive Proof by Contradiction
        if (widget.id == 'interactive_proof_contradiction_1') {
          return InteractiveProofContradictionWidget(
            data: widget.data,
            onStateChanged: (isCompleted) {
              // Optional callback
            },
          );
        }
        // Check if it's the Interactive Syllogism Builder
        if (widget.id == 'interactive_syllogism_builder_1') {
          return InteractiveSyllogismBuilderWidget(
            data: widget.data,
            onStateChanged: (isCompleted) {
              // Optional callback
            },
          );
        }
        // Check if it's the Interactive Variable Explorer
        if (widget.id == 'interactive_variable_explorer_1') {
          return InteractiveVariableExplorerWidget(
            data: widget.data,
            onStateChanged: (isCompleted) {
              // Optional callback
            },
          );
        }
        // Check if it's the Interactive Expression Builder
        if (widget.id == 'interactive_expression_builder_1') {
          return InteractiveExpressionBuilderWidget(
            data: widget.data,
            onStateChanged: (isCompleted) {
              // Optional callback
            },
          );
        }
        // Check if it's the Interactive Number Line Explorer
        if (widget.id == 'interactive_number_line_explorer_1') {
          return InteractiveNumberLineExplorerWidget(
            data: widget.data,
            onStateChanged: (isCompleted) {
              // Optional callback
            },
          );
        }
        // Check if it's the Interactive Inequality Visualizer
        if (widget.id == 'interactive_inequality_visualizer_1') {
          return InteractiveInequalityVisualizerWidget(
            data: widget.data,
            onStateChanged: (isCompleted) {
              // Optional callback
            },
          );
        }
        // Check if it's the Interactive Absolute Value Explorer
        if (widget.id == 'interactive_absolute_value_explorer_1') {
          return InteractiveAbsoluteValueExplorerWidget(
            data: widget.data,
            onStateChanged: (isCompleted) {
              // Optional callback
            },
          );
        }

        // Check if it's the Interactive Pendulum Simulation
        if (widget.id == 'interactive_pendulum_simulation_1') {
          return InteractivePendulumSimulationWidget(
            data: widget.data,
            onStateChanged: (isCompleted) {
              // Optional callback
            },
          );
        }

        // Check if it's the Interactive Unit Converter
        if (widget.id == 'interactive_unit_converter_1') {
          return InteractiveUnitConverterWidget(
            data: widget.data,
            onStateChanged: (isCompleted) {
              // Optional callback
            },
          );
        }

        // Check if it's the Interactive Sorting Algorithm Visualizer
        if (widget.id == 'interactive_sorting_algorithm_visualizer_1') {
          return InteractiveSortingAlgorithmVisualizerWidget(
            data: widget.data,
            onStateChanged: (isCompleted) {
              // Optional callback
            },
          );
        }

        // Check if it's the Interactive Compound Inequality Builder
        if (widget.id == 'interactive_compound_inequality_builder_1') {
          return InteractiveCompoundInequalityBuilderWidget(
            data: widget.data,
            onStateChanged: (isCompleted) {
              // Optional callback
            },
          );
        }

        // Check if it's the Interactive One-Step Inequality Solver
        if (widget.id == 'interactive_one_step_inequality_solver_1') {
          return InteractiveOneStepInequalitySolverWidget(
            data: widget.data,
            primaryColor: Colors.blue,
            secondaryColor: Colors.orange,
            backgroundColor: Colors.white,
            textColor: Colors.black87,
            successColor: Colors.green,
            errorColor: Colors.red,
            onStateChanged: (isCompleted) {
              // Optional callback
            },
          );
        }

        // Check if it's the Interactive Two-Step Inequality Solver
        if (widget.id == 'interactive_two_step_inequality_solver_1') {
          return InteractiveTwoStepInequalitySolverWidget(
            data: widget.data,
            primaryColor: Colors.blue,
            secondaryColor: Colors.orange,
            backgroundColor: Colors.white,
            textColor: Colors.black87,
            successColor: Colors.green,
            errorColor: Colors.red,
            onStateChanged: (isCompleted) {
              // Optional callback
            },
          );
        }

        // Check if it's the Interactive Inequality Grapher
        if (widget.id == 'interactive_inequality_grapher_1') {
          return InteractiveInequalityGrapherWidget(
            data: widget.data,
            primaryColor: Colors.blue,
            secondaryColor: Colors.orange,
            backgroundColor: Colors.white,
            textColor: Colors.black87,
            successColor: Colors.green,
            errorColor: Colors.red,
            shadingColor: Colors.blue,
            onStateChanged: (isCompleted) {
              // Optional callback
            },
          );
        }

        // Check if it's the Interactive Inequality Investigator
        if (widget.id == 'interactive_inequality_investigator_1') {
          return InteractiveInequalityInvestigatorWidget(
            data: widget.data,
            primaryColor: Colors.blue,
            secondaryColor: Colors.orange,
            backgroundColor: Colors.white,
            textColor: Colors.black87,
            successColor: Colors.green,
            errorColor: Colors.red,
            onStateChanged: (isCompleted) {
              // Optional callback
            },
          );
        }

        // Check if it's the Interactive Scientific Method Flowchart
        if (widget.id == 'interactive_scientific_method_flowchart_1') {
          return InteractiveScientificMethodFlowchartWidget(
            data: widget.data,
            onStateChanged: (isCompleted) {
              // Optional callback
            },
          );
        }

        // Check if it's the Interactive Hypothesis Builder
        if (widget.id == 'interactive_hypothesis_builder_1') {
          return InteractiveHypothesisBuilderWidget(
            data: widget.data,
            onStateChanged: (isCompleted) {
              // Optional callback
            },
          );
        }

        // Check if it's the Interactive Experimental Design Tool
        if (widget.id == 'interactive_experimental_design_tool_1') {
          return InteractiveExperimentalDesignToolWidget(
            data: widget.data,
            onStateChanged: (isCompleted) {
              // Optional callback
            },
          );
        }

        // Check if it's the Interactive Variable Identifier
        if (widget.id == 'interactive_variable_identifier_1') {
          return InteractiveVariableIdentifierWidget(
            data: widget.data,
            onStateChanged: (isCompleted) {
              // Optional callback
            },
          );
        }

        // Check if it's the Interactive Data Visualization Tool
        if (widget.id == 'interactive_data_visualization_tool_1') {
          return InteractiveDataVisualizationToolWidget(
            data: widget.data,
            onStateChanged: (isCompleted) {
              // Optional callback
            },
          );
        }

        // Check if it's the Interactive Statistical Analysis Calculator
        if (widget.id == 'interactive_statistical_analysis_calculator_1') {
          return InteractiveStatisticalAnalysisCalculatorWidget(
            data: widget.data,
            onStateChanged: (isCompleted) {
              // Optional callback
            },
          );
        }

        // Check if it's the Interactive Measurement Error Simulator
        if (widget.id == 'interactive_measurement_error_simulator_1') {
          return InteractiveMeasurementErrorSimulatorWidget(
            data: widget.data,
            onStateChanged: (isCompleted) {
              // Optional callback
            },
          );
        }

        // Check if it's the Interactive Graph Interpretation Exercise
        if (widget.id == 'interactive_graph_interpretation_exercise_1') {
          return InteractiveWidgetFactory.createWidget(
            'interactive_graph_interpretation_exercise',
            widget.data,
          );
        }

        // Check if it's the Interactive Function Identifier
        if (widget.id == 'interactive_function_identifier_1') {
          return InteractiveFunctionIdentifierWidget(
            data: widget.data,
            onStateChanged: (isCompleted) {
              // Optional callback
            },
          );
        }

        // Check if it's the Interactive Domain Range Explorer
        if (widget.id == 'interactive_domain_range_explorer_1') {
          return InteractiveDomainRangeExplorerWidget(
            data: widget.data,
            onStateChanged: (isCompleted) {
              // Optional callback
            },
          );
        }

        // Check if it's the Interactive Function Notation Practice
        if (widget.id == 'interactive_function_notation_practice_1') {
          return InteractiveFunctionNotationPracticeWidget(
            data: widget.data,
            onStateChanged: (isCompleted) {
              // Optional callback
            },
          );
        }

        // Check if it's the Interactive Function Fundamentals Test
        if (widget.id == 'interactive_function_fundamentals_test_1') {
          return InteractiveFunctionFundamentalsTestWidget(
            data: widget.data,
            onStateChanged: (isCompleted) {
              // Optional callback
            },
          );
        }

        // Check if it's the Interactive Linear Function Explorer
        if (widget.id == 'interactive_linear_function_explorer_1') {
          return InteractiveLinearFunctionExplorerWidget(
            data: widget.data,
            onStateChanged: (isCompleted) {
              // Optional callback
            },
          );
        }

        // Check if it's the Interactive Quadratic Function Explorer
        if (widget.id == 'interactive_quadratic_function_explorer_1') {
          return InteractiveQuadraticFunctionExplorerWidget(
            data: widget.data,
            onStateChanged: (isCompleted) {
              // Optional callback
            },
          );
        }

        // Check if it's the Interactive Exponential Function Explorer
        if (widget.id == 'interactive_exponential_function_explorer_1') {
          return InteractiveExponentialFunctionExplorerWidget(
            data: widget.data,
            onStateChanged: (isCompleted) {
              // Optional callback
            },
          );
        }

        // Check if it's the Interactive Probability Calculator
        if (widget.id == 'interactive_probability_calculator_1') {
          return InteractiveProbabilityCalculatorWidget(
            data: widget.data,
            onStateChanged: (isCompleted) {
              // Optional callback
            },
          );
        }

        return _buildPlaceholderWidget(
          widget,
          Icons.table_chart,
          Colors.deepPurple,
        );
      case 'interactive_rotational_kinematics_calculator':
        if (widget.id == 'interactive_rotational_kinematics_calculator_1') {
          return InteractiveRotationalKinematicsCalculator(
            // data: widget.data, // No data parameter needed for this widget
            // onStateChanged: (isCompleted) {}, // No state change callback needed
          );
        }
        return _buildPlaceholderWidget(widget, Icons.rotate_right, Colors.deepPurple);
      case 'interactive_moment_of_inertia_calculator':
        if (widget.id == 'interactive_moment_of_inertia_calculator_1') {
          return InteractiveMomentOfInertiaCalculator();
        }
        return _buildPlaceholderWidget(widget, Icons.square_foot, Colors.deepPurple);
      case 'interactive_angular_momentum_conservation_demonstrator':
        if (widget.id == 'interactive_angular_momentum_conservation_demonstrator_1') {
          return InteractiveAngularMomentumConservationDemonstrator();
        }
        return _buildPlaceholderWidget(widget, Icons.track_changes, Colors.deepPurple);
      case 'interactive_rotational_kinetic_energy_calculator':
        if (widget.id == 'interactive_rotational_kinetic_energy_calculator_1') {
          return InteractiveRotationalKineticEnergyCalculator();
        }
        return _buildPlaceholderWidget(widget, Icons.speed, Colors.deepPurple);
      case 'Module Test':
        if (widget.id == 'interactive_rotational_motion_challenge_1') {
          return InteractiveRotationalMotionChallenge();
        }
        return _buildPlaceholderWidget(widget, Icons.quiz, Colors.deepPurple);
      default:
        return Container(
          height: 100,
          decoration: BoxDecoration(
            color: Colors.grey[200],
            borderRadius: BorderRadius.circular(8),
          ),
          child: Center(
            child: Text(
              'Preview for ${widget.type} (Not implemented yet)',
              textAlign: TextAlign.center,
            ),
          ),
        );
    }
  }

  Widget _buildPlaceholderWidget(
    InteractiveWidgetModel widget,
    IconData icon,
    Color color,
  ) {
    return Container(
      height: 150,
      width: double.infinity,
      decoration: BoxDecoration(
        color: Colors.grey[200],
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: Colors.grey[300]!),
      ),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(icon, size: 48, color: color),
          const SizedBox(height: 16),
          Text(
            widget.name,
            style: const TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 8),
          Text(
            'Coming soon',
            style: TextStyle(
              fontSize: 14,
              color: Colors.grey[700],
              fontStyle: FontStyle.italic,
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Interactive Widgets Showcase'),
        actions: [
          IconButton(
            icon: const Icon(Icons.info_outline),
            onPressed: () {
              showDialog(
                context: context,
                builder:
                    (context) => AlertDialog(
                      title: const Text('About Interactive Widgets'),
                      content: const Text(
                        'This screen showcases all the interactive widgets available in the app. '
                        'Use the category tabs to browse widgets by subject area. '
                        'Search to find specific widgets. '
                        'Tap on a widget to see a preview and interact with it.',
                      ),
                      actions: [
                        TextButton(
                          onPressed: () => Navigator.pop(context),
                          child: const Text('OK'),
                        ),
                      ],
                    ),
              );
            },
          ),
        ],
        bottom: TabBar(
          controller: _tabController,
          isScrollable: true,
          tabs:
              _categories.map((category) {
                return Tab(
                  child: Row(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      if (category != 'All')
                        Container(
                          width: 12,
                          height: 12,
                          margin: const EdgeInsets.only(right: 6),
                          decoration: BoxDecoration(
                            color: _getCategoryColor(category),
                            shape: BoxShape.circle,
                          ),
                        ),
                      Text(_getCategoryDisplayName(category)),
                    ],
                  ),
                );
              }).toList(),
          labelColor: Theme.of(context).primaryColor,
          unselectedLabelColor: Colors.grey[600],
          indicatorColor: Theme.of(context).primaryColor,
        ),
      ),
      body: Column(
        children: [
          // Search bar
          Padding(
            padding: const EdgeInsets.all(16.0),
            child: TextField(
              controller: _searchController,
              decoration: InputDecoration(
                hintText: 'Search widgets...',
                prefixIcon: const Icon(Icons.search),
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(12),
                ),
                contentPadding: const EdgeInsets.symmetric(vertical: 12),
              ),
              onChanged: (value) {
                setState(() {
                  _searchQuery = value;
                });
              },
            ),
          ),

          // Widget type filter
          SizedBox(
            height: 50,
            child: ListView.builder(
              scrollDirection: Axis.horizontal,
              padding: const EdgeInsets.symmetric(horizontal: 16),
              itemCount: _widgetTypes.length,
              itemBuilder: (context, index) {
                final type = _widgetTypes[index];
                return Padding(
                  padding: const EdgeInsets.only(right: 8),
                  child: FilterChip(
                    label: Text(type),
                    selected: false, // We'll implement type filtering later
                    onSelected: (selected) {
                      // We'll implement type filtering later
                    },
                  ),
                );
              },
            ),
          ),

          // Implementation status summary
          Padding(
            padding: const EdgeInsets.symmetric(
              horizontal: 16.0,
              vertical: 8.0,
            ),
            child: _buildImplementationStatusSummary(),
          ),

          // Widget list
          Expanded(
            child:
                _filteredWidgets.isEmpty
                    ? const Center(
                      child: Text(
                        'No widgets found matching your criteria',
                        style: TextStyle(fontSize: 16),
                      ),
                    )
                    : ListView.builder(
                      itemCount: _filteredWidgets.length,
                      itemBuilder: (context, index) {
                        return _buildWidgetCard(_filteredWidgets[index]);
                      },
                    ),
          ),
        ],
      ),
    );
  }

  Widget _buildImplementationStatusSummary() {
    final interactiveWidgetService =
        ServiceProvider.of(context).interactiveWidgetService;
    final allWidgets = interactiveWidgetService.getInteractiveWidgets();
    final implementedWidgets = interactiveWidgetService.getImplementedWidgets();

    return Row(
      children: [
        Expanded(
          child: Card(
            child: Padding(
              padding: const EdgeInsets.all(12.0),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'Implementation Status',
                    style: TextStyle(
                      fontWeight: FontWeight.bold,
                      fontSize: 14,
                      color: Colors.grey[800],
                    ),
                  ),
                  const SizedBox(height: 8),
                  LinearProgressIndicator(
                    value:
                        allWidgets.isEmpty
                            ? 0
                            : implementedWidgets.length / allWidgets.length,
                    backgroundColor: Colors.grey[300],
                    valueColor: AlwaysStoppedAnimation<Color>(Colors.green),
                  ),
                  const SizedBox(height: 8),
                  Text(
                    '${implementedWidgets.length}/${allWidgets.length} widgets implemented',
                    style: TextStyle(fontSize: 12, color: Colors.grey[600]),
                  ),
                ],
              ),
            ),
          ),
        ),
      ],
    );
  }

  // Helper method to get category color
  Color _getCategoryColor(String categoryId) {
    switch (categoryId.toLowerCase()) {
      case 'maths':
        return const Color(0xFF4285F4); // Blue
      case 'science':
        return const Color(0xFFFFB300); // Amber
      case 'computer_science':
        return const Color(0xFF9C27B0); // Purple
      case 'reasoning':
        return const Color(0xFFFF5722); // Deep Orange
      case 'technology':
        return const Color(0xFF00BCD4); // Cyan
      case 'puzzles':
        return const Color(0xFF8BC34A); // Light Green
      case 'curiosity_corner':
        return const Color(0xFFFF9800); // Orange
      case 'computational_thinking':
        return const Color(0xFF607D8B); // Blue Grey
      case 'coming_soon':
        return const Color(0xFF9E9E9E); // Grey
      default:
        return Theme.of(context).primaryColor;
    }
  }

  // Helper method to get category display name
  String _getCategoryDisplayName(String categoryId) {
    switch (categoryId.toLowerCase()) {
      case 'maths':
        return 'Mathematics';
      case 'science':
        return 'Science';
      case 'computer_science':
        return 'Computer Science';
      case 'reasoning':
        return 'Reasoning';
      case 'technology':
        return 'Technology';
      case 'puzzles':
        return 'Puzzles';
      case 'curiosity_corner':
        return 'Curiosity Corner';
      case 'calculus':
        return 'Calculus';
      case 'computational_thinking':
        return 'Computational Thinking';
      case 'coming_soon':
        return 'Coming Soon';
      case 'all':
        return 'All Widgets';
      default:
        return categoryId;
    }
  }
}
