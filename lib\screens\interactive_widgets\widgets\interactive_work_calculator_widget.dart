import 'dart:math' as math;
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';

class InteractiveWorkCalculatorWidget extends StatefulWidget {
  final Map<String, dynamic> data;
  final Function(bool)? onStateChanged;

  const InteractiveWorkCalculatorWidget({
    Key? key,
    required this.data,
    this.onStateChanged,
  }) : super(key: key);

  @override
  State<InteractiveWorkCalculatorWidget> createState() =>
      _InteractiveWorkCalculatorWidgetState();
}

class _InteractiveWorkCalculatorWidgetState
    extends State<InteractiveWorkCalculatorWidget> with SingleTickerProviderStateMixin {
  // Animation controller
  late AnimationController _animationController;
  late Animation<double> _animation;

  // Work calculation parameters
  String _currentCalculationType = 'Force and Distance';
  double _force = 10.0; // Newtons
  double _distance = 5.0; // Meters
  double _angle = 0.0; // Degrees
  double _mass = 1.0; // kg
  double _height = 1.0; // Meters
  double _initialVelocity = 0.0; // m/s
  double _finalVelocity = 10.0; // m/s
  double _work = 0.0; // Joules

  // UI state
  bool _isAnimating = false;
  bool _showResult = false;
  String? _errorMessage;

  // UI colors
  final Color _primaryColor = Colors.blue;
  final Color _secondaryColor = Colors.orange;
  final Color _textColor = Colors.black87;
  final Color _backgroundColor = Colors.white;

  // Calculation type options
  final List<String> _calculationTypeOptions = [
    'Force and Distance',
    'Gravitational Potential Energy',
    'Kinetic Energy Change',
    'Spring Work',
  ];

  // Text controllers
  final TextEditingController _forceController = TextEditingController();
  final TextEditingController _distanceController = TextEditingController();
  final TextEditingController _angleController = TextEditingController();
  final TextEditingController _massController = TextEditingController();
  final TextEditingController _heightController = TextEditingController();
  final TextEditingController _initialVelocityController = TextEditingController();
  final TextEditingController _finalVelocityController = TextEditingController();
  final TextEditingController _springConstantController = TextEditingController();
  final TextEditingController _displacementController = TextEditingController();

  // Spring parameters
  double _springConstant = 100.0; // N/m
  double _displacement = 0.2; // m

  @override
  void initState() {
    super.initState();

    // Initialize animation controller
    _animationController = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 1000),
    );

    _animation = CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeInOut,
    );

    // Initialize text controllers
    _forceController.text = _force.toString();
    _distanceController.text = _distance.toString();
    _angleController.text = _angle.toString();
    _massController.text = _mass.toString();
    _heightController.text = _height.toString();
    _initialVelocityController.text = _initialVelocity.toString();
    _finalVelocityController.text = _finalVelocity.toString();
    _springConstantController.text = _springConstant.toString();
    _displacementController.text = _displacement.toString();

    // Calculate initial work
    _calculateWork();
  }

  @override
  void dispose() {
    _animationController.dispose();
    _forceController.dispose();
    _distanceController.dispose();
    _angleController.dispose();
    _massController.dispose();
    _heightController.dispose();
    _initialVelocityController.dispose();
    _finalVelocityController.dispose();
    _springConstantController.dispose();
    _displacementController.dispose();
    super.dispose();
  }

  void _calculateWork() {
    setState(() {
      _errorMessage = null;
      _showResult = false;
    });

    try {
      switch (_currentCalculationType) {
        case 'Force and Distance':
          _force = double.parse(_forceController.text);
          _distance = double.parse(_distanceController.text);
          _angle = double.parse(_angleController.text);

          // W = F * d * cos(θ)
          double angleInRadians = _angle * math.pi / 180;
          _work = _force * _distance * math.cos(angleInRadians);
          break;

        case 'Gravitational Potential Energy':
          _mass = double.parse(_massController.text);
          _height = double.parse(_heightController.text);

          // W = m * g * h
          const double gravity = 9.8; // m/s²
          _work = _mass * gravity * _height;
          break;

        case 'Kinetic Energy Change':
          _mass = double.parse(_massController.text);
          _initialVelocity = double.parse(_initialVelocityController.text);
          _finalVelocity = double.parse(_finalVelocityController.text);

          // W = (1/2) * m * (v_f² - v_i²)
          _work = 0.5 * _mass * (_finalVelocity * _finalVelocity - _initialVelocity * _initialVelocity);
          break;

        case 'Spring Work':
          _springConstant = double.parse(_springConstantController.text);
          _displacement = double.parse(_displacementController.text);

          // W = (1/2) * k * x²
          _work = 0.5 * _springConstant * _displacement * _displacement;
          break;
      }

      setState(() {
        _showResult = true;
      });

      // Start animation
      _animationController.reset();
      _animationController.forward();

      // Notify parent of state change
      widget.onStateChanged?.call(true);
    } catch (e) {
      setState(() {
        _errorMessage = 'Invalid input. Please enter valid numbers.';
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
      ),
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Work Calculator',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
                color: _textColor,
              ),
            ),
            const SizedBox(height: 16),

            // Calculation type selector
            DropdownButton<String>(
              value: _currentCalculationType,
              onChanged: (String? newValue) {
                if (newValue != null && newValue != _currentCalculationType) {
                  setState(() {
                    _currentCalculationType = newValue;
                    _showResult = false;
                  });
                }
              },
              items: _calculationTypeOptions.map<DropdownMenuItem<String>>((String value) {
                return DropdownMenuItem<String>(
                  value: value,
                  child: Text(value),
                );
              }).toList(),
            ),

            const SizedBox(height: 16),

            // Input fields based on calculation type
            _buildInputFields(),

            const SizedBox(height: 16),

            // Calculate button
            Center(
              child: ElevatedButton(
                onPressed: _calculateWork,
                style: ElevatedButton.styleFrom(
                  backgroundColor: _primaryColor,
                  padding: const EdgeInsets.symmetric(horizontal: 32, vertical: 12),
                ),
                child: const Text('Calculate Work'),
              ),
            ),

            const SizedBox(height: 16),

            // Error message
            if (_errorMessage != null)
              Center(
                child: Text(
                  _errorMessage!,
                  style: const TextStyle(
                    color: Colors.red,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ),

            // Result
            if (_showResult)
              AnimatedBuilder(
                animation: _animation,
                builder: (context, child) {
                  return Opacity(
                    opacity: _animation.value,
                    child: Container(
                      width: double.infinity,
                      padding: const EdgeInsets.all(16),
                      decoration: BoxDecoration(
                        color: _primaryColor.withOpacity(0.1),
                        borderRadius: BorderRadius.circular(8),
                        border: Border.all(
                          color: _primaryColor,
                          width: 1,
                        ),
                      ),
                      child: Column(
                        children: [
                          Text(
                            'Work = ${_work.toStringAsFixed(2)} Joules',
                            style: TextStyle(
                              fontSize: 18,
                              fontWeight: FontWeight.bold,
                              color: _primaryColor,
                            ),
                          ),
                          const SizedBox(height: 8),
                          Text(
                            _getWorkExplanation(),
                            style: TextStyle(
                              fontSize: 14,
                              color: _textColor,
                            ),
                            textAlign: TextAlign.center,
                          ),
                        ],
                      ),
                    ),
                  );
                },
              ),
          ],
        ),
      ),
    );
  }

  Widget _buildInputFields() {
    switch (_currentCalculationType) {
      case 'Force and Distance':
        return Column(
          children: [
            _buildNumberInput(
              label: 'Force (N)',
              controller: _forceController,
              hint: 'Enter force in Newtons',
            ),
            const SizedBox(height: 8),
            _buildNumberInput(
              label: 'Distance (m)',
              controller: _distanceController,
              hint: 'Enter distance in meters',
            ),
            const SizedBox(height: 8),
            _buildNumberInput(
              label: 'Angle (degrees)',
              controller: _angleController,
              hint: 'Enter angle in degrees',
            ),
            const SizedBox(height: 16),
            _buildForceDistanceVisualization(),
          ],
        );

      case 'Gravitational Potential Energy':
        return Column(
          children: [
            _buildNumberInput(
              label: 'Mass (kg)',
              controller: _massController,
              hint: 'Enter mass in kilograms',
            ),
            const SizedBox(height: 8),
            _buildNumberInput(
              label: 'Height (m)',
              controller: _heightController,
              hint: 'Enter height in meters',
            ),
            const SizedBox(height: 16),
            _buildGravitationalPotentialVisualization(),
          ],
        );

      case 'Kinetic Energy Change':
        return Column(
          children: [
            _buildNumberInput(
              label: 'Mass (kg)',
              controller: _massController,
              hint: 'Enter mass in kilograms',
            ),
            const SizedBox(height: 8),
            _buildNumberInput(
              label: 'Initial Velocity (m/s)',
              controller: _initialVelocityController,
              hint: 'Enter initial velocity',
            ),
            const SizedBox(height: 8),
            _buildNumberInput(
              label: 'Final Velocity (m/s)',
              controller: _finalVelocityController,
              hint: 'Enter final velocity',
            ),
            const SizedBox(height: 16),
            _buildKineticEnergyVisualization(),
          ],
        );

      case 'Spring Work':
        return Column(
          children: [
            _buildNumberInput(
              label: 'Spring Constant (N/m)',
              controller: _springConstantController,
              hint: 'Enter spring constant',
            ),
            const SizedBox(height: 8),
            _buildNumberInput(
              label: 'Displacement (m)',
              controller: _displacementController,
              hint: 'Enter displacement in meters',
            ),
            const SizedBox(height: 16),
            _buildSpringWorkVisualization(),
          ],
        );

      default:
        return const SizedBox.shrink();
    }
  }

  Widget _buildNumberInput({
    required String label,
    required TextEditingController controller,
    required String hint,
  }) {
    return TextField(
      controller: controller,
      keyboardType: const TextInputType.numberWithOptions(decimal: true),
      decoration: InputDecoration(
        labelText: label,
        hintText: hint,
        border: OutlineInputBorder(
          borderRadius: BorderRadius.circular(8),
        ),
      ),
      inputFormatters: [
        FilteringTextInputFormatter.allow(RegExp(r'[0-9.-]')),
      ],
    );
  }

  Widget _buildForceDistanceVisualization() {
    return Container(
      height: 120,
      width: double.infinity,
      decoration: BoxDecoration(
        color: Colors.grey.withOpacity(0.1),
        borderRadius: BorderRadius.circular(8),
      ),
      child: CustomPaint(
        painter: ForceDistancePainter(
          force: double.tryParse(_forceController.text) ?? _force,
          distance: double.tryParse(_distanceController.text) ?? _distance,
          angle: double.tryParse(_angleController.text) ?? _angle,
          primaryColor: _primaryColor,
          secondaryColor: _secondaryColor,
          textColor: _textColor,
        ),
      ),
    );
  }

  Widget _buildGravitationalPotentialVisualization() {
    return Container(
      height: 120,
      width: double.infinity,
      decoration: BoxDecoration(
        color: Colors.grey.withOpacity(0.1),
        borderRadius: BorderRadius.circular(8),
      ),
      child: CustomPaint(
        painter: GravitationalPotentialPainter(
          mass: double.tryParse(_massController.text) ?? _mass,
          height: double.tryParse(_heightController.text) ?? _height,
          primaryColor: _primaryColor,
          secondaryColor: _secondaryColor,
          textColor: _textColor,
        ),
      ),
    );
  }

  Widget _buildKineticEnergyVisualization() {
    return Container(
      height: 120,
      width: double.infinity,
      decoration: BoxDecoration(
        color: Colors.grey.withOpacity(0.1),
        borderRadius: BorderRadius.circular(8),
      ),
      child: CustomPaint(
        painter: KineticEnergyPainter(
          mass: double.tryParse(_massController.text) ?? _mass,
          initialVelocity: double.tryParse(_initialVelocityController.text) ?? _initialVelocity,
          finalVelocity: double.tryParse(_finalVelocityController.text) ?? _finalVelocity,
          primaryColor: _primaryColor,
          secondaryColor: _secondaryColor,
          textColor: _textColor,
        ),
      ),
    );
  }

  Widget _buildSpringWorkVisualization() {
    return Container(
      height: 120,
      width: double.infinity,
      decoration: BoxDecoration(
        color: Colors.grey.withOpacity(0.1),
        borderRadius: BorderRadius.circular(8),
      ),
      child: CustomPaint(
        painter: SpringWorkPainter(
          springConstant: double.tryParse(_springConstantController.text) ?? _springConstant,
          displacement: double.tryParse(_displacementController.text) ?? _displacement,
          primaryColor: _primaryColor,
          secondaryColor: _secondaryColor,
          textColor: _textColor,
        ),
      ),
    );
  }

  String _getWorkExplanation() {
    switch (_currentCalculationType) {
      case 'Force and Distance':
        return 'Work is calculated as Force × Distance × cos(Angle)';
      case 'Gravitational Potential Energy':
        return 'Work against gravity is calculated as Mass × g × Height';
      case 'Kinetic Energy Change':
        return 'Work is calculated as the change in kinetic energy: ½ × Mass × (Final Velocity² - Initial Velocity²)';
      case 'Spring Work':
        return 'Work done by a spring is calculated as ½ × Spring Constant × Displacement²';
      default:
        return '';
    }
  }
}

// Painters for different visualizations
class ForceDistancePainter extends CustomPainter {
  final double force;
  final double distance;
  final double angle;
  final Color primaryColor;
  final Color secondaryColor;
  final Color textColor;

  ForceDistancePainter({
    required this.force,
    required this.distance,
    required this.angle,
    required this.primaryColor,
    required this.secondaryColor,
    required this.textColor,
  });

  @override
  void paint(Canvas canvas, Size size) {
    final center = Offset(size.width * 0.3, size.height * 0.5);
    final scale = size.width * 0.4 / math.max(10, distance);

    // Draw ground
    final groundPaint = Paint()
      ..color = Colors.brown
      ..style = PaintingStyle.fill;
    canvas.drawRect(
      Rect.fromLTWH(0, size.height * 0.7, size.width, size.height * 0.1),
      groundPaint,
    );

    // Draw object
    final objectPaint = Paint()
      ..color = primaryColor
      ..style = PaintingStyle.fill;
    final objectRect = Rect.fromCenter(
      center: center,
      width: 40,
      height: 40,
    );
    canvas.drawRect(objectRect, objectPaint);

    // Calculate force vector
    final angleInRadians = angle * math.pi / 180;
    final forceScale = math.min(30, force) / 10;
    final forceX = forceScale * math.cos(angleInRadians) * 50;
    final forceY = -forceScale * math.sin(angleInRadians) * 50;

    // Draw force vector
    final forcePaint = Paint()
      ..color = secondaryColor
      ..strokeWidth = 3
      ..style = PaintingStyle.stroke;

    final forceStart = Offset(center.dx, center.dy);
    final forceEnd = Offset(center.dx + forceX, center.dy + forceY);

    // Draw arrow line
    canvas.drawLine(forceStart, forceEnd, forcePaint);

    // Draw arrow head
    final arrowHeadSize = 10.0;
    final angle1 = math.atan2(forceY, forceX) + math.pi / 6;
    final angle2 = math.atan2(forceY, forceX) - math.pi / 6;

    final arrowHead1 = Offset(
      forceEnd.dx - arrowHeadSize * math.cos(angle1),
      forceEnd.dy - arrowHeadSize * math.sin(angle1),
    );

    final arrowHead2 = Offset(
      forceEnd.dx - arrowHeadSize * math.cos(angle2),
      forceEnd.dy - arrowHeadSize * math.sin(angle2),
    );

    canvas.drawLine(forceEnd, arrowHead1, forcePaint);
    canvas.drawLine(forceEnd, arrowHead2, forcePaint);

    // Draw distance line
    final distancePaint = Paint()
      ..color = Colors.grey
      ..strokeWidth = 1
      ..style = PaintingStyle.stroke;

    final distanceStart = Offset(center.dx, size.height * 0.65);
    final distanceEnd = Offset(center.dx + distance * scale, size.height * 0.65);

    canvas.drawLine(distanceStart, distanceEnd, distancePaint);

    // Draw distance label
    final distanceText = '${distance.toStringAsFixed(1)} m';
    final distanceTextSpan = TextSpan(
      text: distanceText,
      style: TextStyle(color: textColor, fontSize: 12),
    );
    final distanceTextPainter = TextPainter(
      text: distanceTextSpan,
      textDirection: TextDirection.ltr,
    );
    distanceTextPainter.layout();
    distanceTextPainter.paint(
      canvas,
      Offset(
        (distanceStart.dx + distanceEnd.dx) / 2 - distanceTextPainter.width / 2,
        distanceStart.dy + 5,
      ),
    );

    // Draw force label
    final forceText = '${force.toStringAsFixed(1)} N';
    final forceTextSpan = TextSpan(
      text: forceText,
      style: TextStyle(color: secondaryColor, fontSize: 12),
    );
    final forceTextPainter = TextPainter(
      text: forceTextSpan,
      textDirection: TextDirection.ltr,
    );
    forceTextPainter.layout();
    forceTextPainter.paint(
      canvas,
      Offset(
        (forceStart.dx + forceEnd.dx) / 2 - forceTextPainter.width / 2,
        (forceStart.dy + forceEnd.dy) / 2 - forceTextPainter.height - 5,
      ),
    );

    // Draw angle arc
    if (angle != 0) {
      final anglePaint = Paint()
        ..color = Colors.purple
        ..strokeWidth = 1
        ..style = PaintingStyle.stroke;

      final angleRadius = 20.0;
      final startAngle = 0;
      final sweepAngle = -angleInRadians;

      canvas.drawArc(
        Rect.fromCircle(center: center, radius: angleRadius),
        startAngle.toDouble(),
        sweepAngle,
        false,
        anglePaint,
      );

      // Draw angle label
      final angleText = '${angle.toStringAsFixed(0)}°';
      final angleTextSpan = TextSpan(
        text: angleText,
        style: TextStyle(color: Colors.purple, fontSize: 10),
      );
      final angleTextPainter = TextPainter(
        text: angleTextSpan,
        textDirection: TextDirection.ltr,
      );
      angleTextPainter.layout();

      final angleTextX = center.dx + angleRadius * math.cos(sweepAngle / 2);
      final angleTextY = center.dy + angleRadius * math.sin(sweepAngle / 2);

      angleTextPainter.paint(
        canvas,
        Offset(angleTextX, angleTextY),
      );
    }
  }

  @override
  bool shouldRepaint(covariant ForceDistancePainter oldDelegate) {
    return force != oldDelegate.force ||
           distance != oldDelegate.distance ||
           angle != oldDelegate.angle;
  }
}

class GravitationalPotentialPainter extends CustomPainter {
  final double mass;
  final double height;
  final Color primaryColor;
  final Color secondaryColor;
  final Color textColor;

  GravitationalPotentialPainter({
    required this.mass,
    required this.height,
    required this.primaryColor,
    required this.secondaryColor,
    required this.textColor,
  });

  @override
  void paint(Canvas canvas, Size size) {
    // TODO: Implement gravitational potential visualization
    final textSpan = TextSpan(
      text: 'Gravitational Potential Energy: m × g × h',
      style: TextStyle(color: textColor, fontSize: 14),
    );
    final textPainter = TextPainter(
      text: textSpan,
      textDirection: TextDirection.ltr,
    );
    textPainter.layout(minWidth: 0, maxWidth: size.width);
    textPainter.paint(canvas, Offset(size.width / 2 - textPainter.width / 2, size.height / 2 - 20));

    final valueSpan = TextSpan(
      text: '${mass.toStringAsFixed(1)} kg × 9.8 m/s² × ${height.toStringAsFixed(1)} m',
      style: TextStyle(color: primaryColor, fontSize: 12, fontWeight: FontWeight.bold),
    );
    final valuePainter = TextPainter(
      text: valueSpan,
      textDirection: TextDirection.ltr,
    );
    valuePainter.layout(minWidth: 0, maxWidth: size.width);
    valuePainter.paint(canvas, Offset(size.width / 2 - valuePainter.width / 2, size.height / 2 + 10));
  }

  @override
  bool shouldRepaint(covariant GravitationalPotentialPainter oldDelegate) {
    return mass != oldDelegate.mass || height != oldDelegate.height;
  }
}

class KineticEnergyPainter extends CustomPainter {
  final double mass;
  final double initialVelocity;
  final double finalVelocity;
  final Color primaryColor;
  final Color secondaryColor;
  final Color textColor;

  KineticEnergyPainter({
    required this.mass,
    required this.initialVelocity,
    required this.finalVelocity,
    required this.primaryColor,
    required this.secondaryColor,
    required this.textColor,
  });

  @override
  void paint(Canvas canvas, Size size) {
    // TODO: Implement kinetic energy visualization
    final textSpan = TextSpan(
      text: 'Kinetic Energy Change: ½ × m × (v₂² - v₁²)',
      style: TextStyle(color: textColor, fontSize: 14),
    );
    final textPainter = TextPainter(
      text: textSpan,
      textDirection: TextDirection.ltr,
    );
    textPainter.layout(minWidth: 0, maxWidth: size.width);
    textPainter.paint(canvas, Offset(size.width / 2 - textPainter.width / 2, size.height / 2 - 20));

    final valueSpan = TextSpan(
      text: '½ × ${mass.toStringAsFixed(1)} kg × (${finalVelocity.toStringAsFixed(1)}² - ${initialVelocity.toStringAsFixed(1)}²) m²/s²',
      style: TextStyle(color: primaryColor, fontSize: 12, fontWeight: FontWeight.bold),
    );
    final valuePainter = TextPainter(
      text: valueSpan,
      textDirection: TextDirection.ltr,
    );
    valuePainter.layout(minWidth: 0, maxWidth: size.width);
    valuePainter.paint(canvas, Offset(size.width / 2 - valuePainter.width / 2, size.height / 2 + 10));
  }

  @override
  bool shouldRepaint(covariant KineticEnergyPainter oldDelegate) {
    return mass != oldDelegate.mass ||
           initialVelocity != oldDelegate.initialVelocity ||
           finalVelocity != oldDelegate.finalVelocity;
  }
}

class SpringWorkPainter extends CustomPainter {
  final double springConstant;
  final double displacement;
  final Color primaryColor;
  final Color secondaryColor;
  final Color textColor;

  SpringWorkPainter({
    required this.springConstant,
    required this.displacement,
    required this.primaryColor,
    required this.secondaryColor,
    required this.textColor,
  });

  @override
  void paint(Canvas canvas, Size size) {
    // TODO: Implement spring work visualization
    final textSpan = TextSpan(
      text: 'Spring Work: ½ × k × x²',
      style: TextStyle(color: textColor, fontSize: 14),
    );
    final textPainter = TextPainter(
      text: textSpan,
      textDirection: TextDirection.ltr,
    );
    textPainter.layout(minWidth: 0, maxWidth: size.width);
    textPainter.paint(canvas, Offset(size.width / 2 - textPainter.width / 2, size.height / 2 - 20));

    final valueSpan = TextSpan(
      text: '½ × ${springConstant.toStringAsFixed(1)} N/m × ${displacement.toStringAsFixed(2)}² m²',
      style: TextStyle(color: primaryColor, fontSize: 12, fontWeight: FontWeight.bold),
    );
    final valuePainter = TextPainter(
      text: valueSpan,
      textDirection: TextDirection.ltr,
    );
    valuePainter.layout(minWidth: 0, maxWidth: size.width);
    valuePainter.paint(canvas, Offset(size.width / 2 - valuePainter.width / 2, size.height / 2 + 10));
  }

  @override
  bool shouldRepaint(covariant SpringWorkPainter oldDelegate) {
    return springConstant != oldDelegate.springConstant ||
           displacement != oldDelegate.displacement;
  }
}
