import 'dart:async';
import 'package:flutter/material.dart';
import '../../../models/interactive_widget_model.dart';

class InteractiveDiagramWidget extends StatefulWidget {
  final InteractiveWidgetModel widget;

  const InteractiveDiagramWidget({super.key, required this.widget});

  @override
  State<InteractiveDiagramWidget> createState() =>
      _InteractiveDiagramWidgetState();
}

class _InteractiveDiagramWidgetState extends State<InteractiveDiagramWidget> {
  final Map<String, bool> _toggleStates = {};
  bool _isLoading = false;
  bool _hasError = false;
  String _errorMessage = '';

  @override
  void initState() {
    super.initState();
    _initializeToggleStates();
  }

  void _initializeToggleStates() {
    final interactivePoints = List<Map<String, dynamic>>.from(
      widget.widget.data['interactivePoints'] ?? [],
    );

    for (final point in interactivePoints) {
      if (point['type'] == 'toggle') {
        _toggleStates[point['label']] = false;
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    final imagePath =
        widget.widget.data['imagePath'] as String? ??
        'assets/images/placeholder.png';
    final interactivePoints = List<Map<String, dynamic>>.from(
      widget.widget.data['interactivePoints'] ?? [],
    );

    return Column(
      children: [
        // Diagram container
        Container(
          height: 250,
          width: double.infinity,
          decoration: BoxDecoration(
            color: Colors.grey[200],
            borderRadius: BorderRadius.circular(8),
            border: Border.all(color: Colors.grey[300]!),
          ),
          child: Stack(
            children: [
              // Base image
              Positioned.fill(
                child: ClipRRect(
                  borderRadius: BorderRadius.circular(7),
                  child:
                      _hasError
                          ? Center(
                            child: Column(
                              mainAxisAlignment: MainAxisAlignment.center,
                              children: [
                                const Icon(
                                  Icons.broken_image,
                                  color: Colors.red,
                                  size: 40,
                                ),
                                const SizedBox(height: 8),
                                Text(
                                  _errorMessage,
                                  style: TextStyle(color: Colors.red[700]),
                                  textAlign: TextAlign.center,
                                ),
                              ],
                            ),
                          )
                          : FutureBuilder(
                            future: () async {
                              try {
                                // Use a safer approach that doesn't use BuildContext across async gaps
                                final image = AssetImage(imagePath);
                                final config = createLocalImageConfiguration(
                                  context,
                                );
                                final imageStream = image.resolve(config);
                                final completer = Completer<bool>();

                                final listener = ImageStreamListener(
                                  (_, __) {
                                    completer.complete(true);
                                  },
                                  onError: (exception, stackTrace) {
                                    completer.complete(false);
                                  },
                                );

                                imageStream.addListener(listener);
                                final result = await completer.future;
                                imageStream.removeListener(listener);

                                if (!result) {
                                  _hasError = true;
                                  _errorMessage = 'Image not found: $imagePath';
                                }

                                return result;
                              } catch (e) {
                                _hasError = true;
                                _errorMessage = 'Error loading image: $e';
                                return false;
                              }
                            }(),
                            builder: (context, snapshot) {
                              if (snapshot.connectionState ==
                                  ConnectionState.waiting) {
                                return const Center(
                                  child: CircularProgressIndicator(),
                                );
                              } else if (snapshot.hasError ||
                                  snapshot.data == false) {
                                return Center(
                                  child: Column(
                                    mainAxisAlignment: MainAxisAlignment.center,
                                    children: [
                                      const Icon(
                                        Icons.broken_image,
                                        color: Colors.red,
                                        size: 40,
                                      ),
                                      const SizedBox(height: 8),
                                      Text(
                                        _errorMessage.isEmpty
                                            ? 'Failed to load image'
                                            : _errorMessage,
                                        style: TextStyle(
                                          color: Colors.red[700],
                                        ),
                                        textAlign: TextAlign.center,
                                      ),
                                    ],
                                  ),
                                );
                              } else {
                                return Image.asset(
                                  imagePath,
                                  fit: BoxFit.contain,
                                );
                              }
                            },
                          ),
                ),
              ),

              // Interactive points
              if (!_isLoading && !_hasError)
                for (final point in interactivePoints)
                  Positioned(
                    left:
                        (point['x'] as double) *
                            MediaQuery.of(context).size.width -
                        20,
                    top: (point['y'] as double) * 250 - 20,
                    child: _buildInteractivePoint(
                      point['type'] as String,
                      point['label'] as String,
                    ),
                  ),

              // Loading indicator
              if (_isLoading) const Center(child: CircularProgressIndicator()),
            ],
          ),
        ),

        // Legend
        if (interactivePoints.isNotEmpty)
          Padding(
            padding: const EdgeInsets.only(top: 16.0),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                const Text(
                  'Interactive Elements:',
                  style: TextStyle(fontWeight: FontWeight.bold, fontSize: 14),
                ),
                const SizedBox(height: 8),
                Wrap(
                  spacing: 16,
                  runSpacing: 8,
                  children: [
                    for (final point in interactivePoints)
                      Row(
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          _buildLegendIcon(point['type'] as String),
                          const SizedBox(width: 4),
                          Text(
                            point['label'] as String,
                            style: const TextStyle(fontSize: 12),
                          ),
                        ],
                      ),
                  ],
                ),
              ],
            ),
          ),
      ],
    );
  }

  Widget _buildInteractivePoint(String type, String label) {
    switch (type) {
      case 'toggle':
        final isActive = _toggleStates[label] ?? false;
        return GestureDetector(
          onTap: () {
            setState(() {
              _toggleStates[label] = !isActive;
            });
          },
          child: Container(
            width: 40,
            height: 40,
            decoration: BoxDecoration(
              color: isActive ? Colors.green : Colors.red,
              shape: BoxShape.circle,
              border: Border.all(color: Colors.white, width: 2),
            ),
            child: Icon(
              isActive ? Icons.power_settings_new : Icons.power_off,
              color: Colors.white,
              size: 20,
            ),
          ),
        );

      case 'indicator':
        // Check if any toggle is active
        final anyToggleActive = _toggleStates.values.any(
          (isActive) => isActive,
        );
        return Container(
          width: 40,
          height: 40,
          decoration: BoxDecoration(
            color: anyToggleActive ? Colors.amber : Colors.grey,
            shape: BoxShape.circle,
            border: Border.all(color: Colors.white, width: 2),
          ),
          child: Icon(
            anyToggleActive ? Icons.lightbulb : Icons.lightbulb_outline,
            color: Colors.white,
            size: 20,
          ),
        );

      default:
        return Container(
          width: 40,
          height: 40,
          decoration: BoxDecoration(
            color: Colors.blue,
            shape: BoxShape.circle,
            border: Border.all(color: Colors.white, width: 2),
          ),
          child: const Icon(Icons.touch_app, color: Colors.white, size: 20),
        );
    }
  }

  Widget _buildLegendIcon(String type) {
    switch (type) {
      case 'toggle':
        return Container(
          width: 16,
          height: 16,
          decoration: const BoxDecoration(
            color: Colors.red,
            shape: BoxShape.circle,
          ),
          child: const Icon(Icons.power_off, color: Colors.white, size: 10),
        );

      case 'indicator':
        return Container(
          width: 16,
          height: 16,
          decoration: const BoxDecoration(
            color: Colors.grey,
            shape: BoxShape.circle,
          ),
          child: const Icon(
            Icons.lightbulb_outline,
            color: Colors.white,
            size: 10,
          ),
        );

      default:
        return Container(
          width: 16,
          height: 16,
          decoration: const BoxDecoration(
            color: Colors.blue,
            shape: BoxShape.circle,
          ),
          child: const Icon(Icons.touch_app, color: Colors.white, size: 10),
        );
    }
  }
}
