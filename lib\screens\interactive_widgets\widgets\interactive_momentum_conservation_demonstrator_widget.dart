import 'package:flutter/material.dart';
import 'dart:math' as math;
import 'dart:async';

class MomentumObject {
  Offset position;
  Offset velocity;
  final double mass;
  final double radius;
  final Color color;
  final bool fixed;
  bool exploding;
  bool isRocket;
  double? lifetime;

  MomentumObject({
    required this.position,
    required this.velocity,
    required this.mass,
    required this.radius,
    required this.color,
    required this.fixed,
    this.exploding = false,
    this.isRocket = false,
    this.lifetime,
  });
}

class InteractiveMomentumConservationDemonstratorWidget extends StatefulWidget {
  final Map<String, dynamic>? data;

  const InteractiveMomentumConservationDemonstratorWidget({
    super.key,
    this.data,
  });

  factory InteractiveMomentumConservationDemonstratorWidget.fromData(
      Map<String, dynamic> data) {
    return InteractiveMomentumConservationDemonstratorWidget(
      data: data,
    );
  }

  @override
  State<InteractiveMomentumConservationDemonstratorWidget> createState() =>
      _InteractiveMomentumConservationDemonstratorWidgetState();
}

class _InteractiveMomentumConservationDemonstratorWidgetState
    extends State<InteractiveMomentumConservationDemonstratorWidget>
    with SingleTickerProviderStateMixin {
  // UI parameters
  late Color _primaryColor;
  late Color _secondaryColor;
  late Color _tertiaryColor;
  late Color _textColor;
  late Color _backgroundColor;

  // Simulation parameters
  String _scenarioType = 'linear'; // linear, explosion, rocket
  bool _isSimulating = false;
  bool _showVelocityVectors = true;
  bool _showMomentumVectors = true;
  bool _showCenterOfMass = true;
  Timer? _simulationTimer;
  final double _timeStep = 0.016; // 60 FPS
  double _elapsedTime = 0.0;
  double _simulationSpeed = 1.0;

  // Physics objects
  late List<MomentumObject> _objects;

  // Boundaries
  final double _minX = 0;
  final double _maxX = 100;
  final double _minY = 0;
  final double _maxY = 100;

  // Tracking
  List<Offset> _totalMomentumHistory = [];
  List<Offset> _centerOfMassHistory = [];

  // Animation controller
  late AnimationController _animationController;
  late Animation<double> _animation;

  @override
  void initState() {
    super.initState();
    _initializeFromData();
    _animationController = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 500),
    );
    _animation = CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeInOut,
    );
    _animationController.forward();
    _initializeSimulation();
  }

  @override
  void dispose() {
    _simulationTimer?.cancel();
    _animationController.dispose();
    super.dispose();
  }

  void _initializeFromData() {
    final data = widget.data;
    if (data != null) {
      _primaryColor = Color(data['primary_color'] ?? 0xFF2196F3);
      _secondaryColor = Color(data['secondary_color'] ?? 0xFFFFA000);
      _tertiaryColor = Color(data['tertiary_color'] ?? 0xFF4CAF50);
      _textColor = Color(data['text_color'] ?? 0xFF333333);
      _backgroundColor = Color(data['background_color'] ?? 0xFFF5F5F5);

      if (data['scenario_type'] != null) {
        _scenarioType = data['scenario_type'];
      }
    } else {
      _primaryColor = Colors.blue;
      _secondaryColor = Colors.orange;
      _tertiaryColor = Colors.green;
      _textColor = Colors.black87;
      _backgroundColor = Colors.grey.shade100;
    }
  }

  void _initializeSimulation() {
    // Reset tracking
    _totalMomentumHistory = [];
    _centerOfMassHistory = [];
    _elapsedTime = 0.0;

    // Create physics objects based on scenario
    switch (_scenarioType) {
      case 'linear':
        _initializeLinearScenario();
        break;
      case 'explosion':
        _initializeExplosionScenario();
        break;
      case 'rocket':
        _initializeRocketScenario();
        break;
    }

    // Calculate initial values
    _calculateTotalMomentum();
    _calculateCenterOfMass();
  }

  void _initializeLinearScenario() {
    _objects = [
      MomentumObject(
        position: Offset(30, 50),
        velocity: Offset(10, 0),
        mass: 10.0,
        radius: 5.0,
        color: _primaryColor,
        fixed: false,
      ),
      MomentumObject(
        position: Offset(70, 50),
        velocity: Offset(-5, 0),
        mass: 5.0,
        radius: 3.0,
        color: _secondaryColor,
        fixed: false,
      ),
    ];
  }

  void _initializeExplosionScenario() {
    _objects = [
      MomentumObject(
        position: Offset(50, 50),
        velocity: Offset(0, 0),
        mass: 20.0,
        radius: 8.0,
        color: _primaryColor,
        fixed: false,
        exploding: true,
      ),
    ];
  }

  void _initializeRocketScenario() {
    _objects = [
      MomentumObject(
        position: Offset(50, 50),
        velocity: Offset(0, 0),
        mass: 10.0,
        radius: 5.0,
        color: _primaryColor,
        fixed: false,
        isRocket: true,
      ),
    ];
  }

  void _resetSimulation() {
    _stopSimulation();
    _initializeSimulation();
    setState(() {});
  }

  void _toggleSimulation() {
    if (_isSimulating) {
      _stopSimulation();
    } else {
      _startSimulation();
    }
  }

  void _startSimulation() {
    if (_isSimulating) return;

    setState(() {
      _isSimulating = true;
    });

    _simulationTimer = Timer.periodic(Duration(milliseconds: (_timeStep * 1000 ~/ _simulationSpeed)), (timer) {
      _updateSimulation();
    });
  }

  void _stopSimulation() {
    _simulationTimer?.cancel();
    _simulationTimer = null;

    setState(() {
      _isSimulating = false;
    });
  }

  void _updateSimulation() {
    setState(() {
      // Update elapsed time
      _elapsedTime += _timeStep;

      // Handle special scenarios
      if (_scenarioType == 'explosion' && _objects.length == 1 && _objects[0].exploding && _elapsedTime > 1.0) {
        _triggerExplosion();
      }

      if (_scenarioType == 'rocket') {
        _updateRocket();
      }

      // Move objects
      for (var object in _objects) {
        if (!object.fixed) {
          object.position = Offset(
            object.position.dx + object.velocity.dx * _timeStep,
            object.position.dy + object.velocity.dy * _timeStep,
          );
        }
      }

      // Check for collisions with boundaries
      for (var object in _objects) {
        if (!object.fixed) {
          // X boundaries
          if (object.position.dx - object.radius < _minX) {
            object.position = Offset(_minX + object.radius, object.position.dy);
            object.velocity = Offset(-object.velocity.dx, object.velocity.dy);
          } else if (object.position.dx + object.radius > _maxX) {
            object.position = Offset(_maxX - object.radius, object.position.dy);
            object.velocity = Offset(-object.velocity.dx, object.velocity.dy);
          }

          // Y boundaries
          if (object.position.dy - object.radius < _minY) {
            object.position = Offset(object.position.dx, _minY + object.radius);
            object.velocity = Offset(object.velocity.dx, -object.velocity.dy);
          } else if (object.position.dy + object.radius > _maxY) {
            object.position = Offset(object.position.dx, _maxY - object.radius);
            object.velocity = Offset(object.velocity.dx, -object.velocity.dy);
          }
        }
      }

      // Check for collisions between objects
      for (int i = 0; i < _objects.length; i++) {
        for (int j = i + 1; j < _objects.length; j++) {
          if (_checkCollision(_objects[i], _objects[j])) {
            _resolveCollision(_objects[i], _objects[j]);
          }
        }
      }

      // Update tracking
      _calculateTotalMomentum();
      _calculateCenterOfMass();
    });
  }

  void _triggerExplosion() {
    final originalObject = _objects[0];
    final numFragments = 5;
    final totalMass = originalObject.mass;
    final fragmentMass = totalMass / numFragments;

    // Create fragments
    List<MomentumObject> fragments = [];
    for (int i = 0; i < numFragments; i++) {
      final angle = 2 * math.pi * i / numFragments;
      final speed = 10.0; // Explosion speed

      fragments.add(
        MomentumObject(
          position: originalObject.position,
          velocity: Offset(
            speed * math.cos(angle),
            speed * math.sin(angle),
          ),
          mass: fragmentMass,
          radius: originalObject.radius / math.sqrt(numFragments),
          color: _secondaryColor,
          fixed: false,
        ),
      );
    }

    // Replace original object with fragments
    _objects = fragments;
  }

  void _updateRocket() {
    for (var object in _objects) {
      if (object.isRocket) {
        // Apply rocket thrust
        final thrustMagnitude = 20.0;
        final thrustDirection = Offset(1.0, 0.0); // Thrust to the right

        // Calculate thrust force
        final thrustForce = Offset(
          thrustDirection.dx * thrustMagnitude,
          thrustDirection.dy * thrustMagnitude,
        );

        // Apply acceleration (F = ma, so a = F/m)
        final acceleration = Offset(
          thrustForce.dx / object.mass,
          thrustForce.dy / object.mass,
        );

        // Update velocity
        object.velocity = Offset(
          object.velocity.dx + acceleration.dx * _timeStep,
          object.velocity.dy + acceleration.dy * _timeStep,
        );

        // Create exhaust particle occasionally
        if (math.Random().nextDouble() < 0.2) {
          final exhaustParticle = MomentumObject(
            position: Offset(
              object.position.dx - object.radius - 1,
              object.position.dy,
            ),
            velocity: Offset(
              -20.0 + math.Random().nextDouble() * 5.0 - 2.5,
              math.Random().nextDouble() * 6.0 - 3.0,
            ),
            mass: 0.1,
            radius: 1.0,
            color: _secondaryColor,
            fixed: false,
            lifetime: 1.0, // Seconds
          );

          _objects.add(exhaustParticle);
        }

        // Remove expired exhaust particles
        _objects.removeWhere((obj) =>
          obj.lifetime != null &&
          obj.lifetime! <= 0
        );

        // Decrease lifetime of remaining particles
        for (var obj in _objects) {
          if (obj.lifetime != null) {
            obj.lifetime = obj.lifetime! - _timeStep;
          }
        }
      }
    }
  }

  bool _checkCollision(MomentumObject a, MomentumObject b) {
    if (a.fixed && b.fixed) return false;

    final distance = (a.position - b.position).distance;
    return distance <= a.radius + b.radius;
  }

  void _resolveCollision(MomentumObject a, MomentumObject b) {
    // Calculate normal vector
    final normalVector = b.position - a.position;
    final distance = normalVector.distance;
    // Normalize the vector manually
    final normal = Offset(
      normalVector.dx / distance,
      normalVector.dy / distance,
    );

    // Calculate relative velocity
    final relativeVelocity = b.velocity - a.velocity;

    // Calculate relative velocity along normal
    final velocityAlongNormal = relativeVelocity.dx * normal.dx + relativeVelocity.dy * normal.dy;

    // Do not resolve if objects are moving away from each other
    if (velocityAlongNormal > 0) return;

    // Calculate impulse scalar (perfectly elastic collision)
    final restitution = 1.0; // Coefficient of restitution (1.0 for elastic)

    // Handle fixed objects
    final massFactorA = a.fixed ? 0 : 1 / a.mass;
    final massFactorB = b.fixed ? 0 : 1 / b.mass;

    final impulseScalar = -(1 + restitution) * velocityAlongNormal /
        (massFactorA + massFactorB);

    // Apply impulse
    final impulse = Offset(normal.dx * impulseScalar, normal.dy * impulseScalar);

    if (!a.fixed) {
      a.velocity = Offset(
        a.velocity.dx - impulse.dx / a.mass,
        a.velocity.dy - impulse.dy / a.mass,
      );
    }

    if (!b.fixed) {
      b.velocity = Offset(
        b.velocity.dx + impulse.dx / b.mass,
        b.velocity.dy + impulse.dy / b.mass,
      );
    }

    // Separate objects to prevent sticking
    final overlap = a.radius + b.radius - distance;
    final separationVector = Offset(normal.dx * overlap / 2, normal.dy * overlap / 2);

    if (!a.fixed) {
      a.position = Offset(
        a.position.dx - separationVector.dx,
        a.position.dy - separationVector.dy,
      );
    }

    if (!b.fixed) {
      b.position = Offset(
        b.position.dx + separationVector.dx,
        b.position.dy + separationVector.dy,
      );
    }
  }

  void _calculateTotalMomentum() {
    double totalMomentumX = 0;
    double totalMomentumY = 0;

    for (var object in _objects) {
      totalMomentumX += object.mass * object.velocity.dx;
      totalMomentumY += object.mass * object.velocity.dy;
    }

    final totalMomentum = Offset(totalMomentumX, totalMomentumY);
    _totalMomentumHistory.add(totalMomentum);

    // Keep history at a reasonable size
    if (_totalMomentumHistory.length > 100) {
      _totalMomentumHistory.removeAt(0);
    }
  }

  void _calculateCenterOfMass() {
    double totalMass = 0;
    double weightedSumX = 0;
    double weightedSumY = 0;

    for (var object in _objects) {
      totalMass += object.mass;
      weightedSumX += object.mass * object.position.dx;
      weightedSumY += object.mass * object.position.dy;
    }

    if (totalMass > 0) {
      final centerOfMass = Offset(
        weightedSumX / totalMass,
        weightedSumY / totalMass,
      );

      _centerOfMassHistory.add(centerOfMass);

      // Keep history at a reasonable size
      if (_centerOfMassHistory.length > 100) {
        _centerOfMassHistory.removeAt(0);
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
      ),
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Title and description
            Text(
              'Momentum Conservation Demonstrator',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
                color: _textColor,
              ),
            ),
            const SizedBox(height: 8),
            Text(
              'Explore the conservation of momentum in different scenarios',
              style: TextStyle(
                fontSize: 14,
                color: _textColor.withOpacity(0.7),
              ),
            ),
            const SizedBox(height: 16),

            // Scenario selector
            Text(
              'Scenario:',
              style: TextStyle(
                fontSize: 14,
                fontWeight: FontWeight.bold,
                color: _textColor,
              ),
            ),
            const SizedBox(height: 8),
            SegmentedButton<String>(
              segments: const [
                ButtonSegment(
                  value: 'linear',
                  label: Text('Linear Collision'),
                  icon: Icon(Icons.compare_arrows),
                ),
                ButtonSegment(
                  value: 'explosion',
                  label: Text('Explosion'),
                  icon: Icon(Icons.flare),
                ),
                ButtonSegment(
                  value: 'rocket',
                  label: Text('Rocket'),
                  icon: Icon(Icons.rocket),
                ),
              ],
              selected: {_scenarioType},
              onSelectionChanged: (Set<String> selection) {
                setState(() {
                  _scenarioType = selection.first;
                  _resetSimulation();
                });
              },
            ),
            const SizedBox(height: 16),

            // Simulation controls
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                // Start/Stop button
                ElevatedButton.icon(
                  onPressed: _toggleSimulation,
                  icon: Icon(_isSimulating ? Icons.pause : Icons.play_arrow),
                  label: Text(_isSimulating ? 'Pause' : 'Start'),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: _primaryColor,
                    foregroundColor: Colors.white,
                  ),
                ),

                // Reset button
                ElevatedButton.icon(
                  onPressed: _resetSimulation,
                  icon: const Icon(Icons.refresh),
                  label: const Text('Reset'),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: _secondaryColor,
                    foregroundColor: Colors.white,
                  ),
                ),

                // Simulation speed
                Row(
                  children: [
                    const Text('Speed:'),
                    const SizedBox(width: 8),
                    DropdownButton<double>(
                      value: _simulationSpeed,
                      items: [0.5, 1.0, 2.0, 4.0].map((speed) {
                        return DropdownMenuItem<double>(
                          value: speed,
                          child: Text('${speed}x'),
                        );
                      }).toList(),
                      onChanged: (value) {
                        if (value != null) {
                          setState(() {
                            _simulationSpeed = value;
                            if (_isSimulating) {
                              _stopSimulation();
                              _startSimulation();
                            }
                          });
                        }
                      },
                    ),
                  ],
                ),
              ],
            ),
            const SizedBox(height: 16),

            // Display options
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceAround,
              children: [
                // Velocity vectors
                Row(
                  children: [
                    Checkbox(
                      value: _showVelocityVectors,
                      onChanged: (value) {
                        setState(() {
                          _showVelocityVectors = value ?? true;
                        });
                      },
                      activeColor: _primaryColor,
                    ),
                    const Text('Velocity'),
                  ],
                ),

                // Momentum vectors
                Row(
                  children: [
                    Checkbox(
                      value: _showMomentumVectors,
                      onChanged: (value) {
                        setState(() {
                          _showMomentumVectors = value ?? true;
                        });
                      },
                      activeColor: _secondaryColor,
                    ),
                    const Text('Momentum'),
                  ],
                ),

                // Center of mass
                Row(
                  children: [
                    Checkbox(
                      value: _showCenterOfMass,
                      onChanged: (value) {
                        setState(() {
                          _showCenterOfMass = value ?? true;
                        });
                      },
                      activeColor: _tertiaryColor,
                    ),
                    const Text('Center of Mass'),
                  ],
                ),
              ],
            ),
            const SizedBox(height: 16),

            // Simulation area
            Container(
              height: 200,
              width: double.infinity,
              decoration: BoxDecoration(
                color: Colors.grey.withOpacity(0.1),
                borderRadius: BorderRadius.circular(8),
                border: Border.all(color: Colors.grey.withOpacity(0.3)),
              ),
              child: ClipRRect(
                borderRadius: BorderRadius.circular(8),
                child: CustomPaint(
                  painter: MomentumSimulationPainter(
                    objects: _objects,
                    minX: _minX,
                    maxX: _maxX,
                    minY: _minY,
                    maxY: _maxY,
                    showVelocityVectors: _showVelocityVectors,
                    showMomentumVectors: _showMomentumVectors,
                    showCenterOfMass: _showCenterOfMass,
                    centerOfMassHistory: _centerOfMassHistory,
                    primaryColor: _primaryColor,
                    secondaryColor: _secondaryColor,
                    tertiaryColor: _tertiaryColor,
                    textColor: _textColor,
                    animationValue: _animation.value,
                  ),
                ),
              ),
            ),
            const SizedBox(height: 16),

            // Momentum graph
            Container(
              height: 100,
              width: double.infinity,
              decoration: BoxDecoration(
                color: Colors.grey.withOpacity(0.1),
                borderRadius: BorderRadius.circular(8),
                border: Border.all(color: Colors.grey.withOpacity(0.3)),
              ),
              child: CustomPaint(
                painter: MomentumGraphPainter(
                  momentumHistory: _totalMomentumHistory,
                  primaryColor: _primaryColor,
                  secondaryColor: _secondaryColor,
                  textColor: _textColor,
                ),
              ),
            ),
            const SizedBox(height: 16),

            // Physics explanation
            ExpansionTile(
              title: Text(
                'Understanding Momentum Conservation',
                style: TextStyle(
                  fontSize: 14,
                  fontWeight: FontWeight.bold,
                  color: _textColor,
                ),
              ),
              children: [
                Padding(
                  padding: const EdgeInsets.all(16.0),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'Conservation of Momentum:',
                        style: TextStyle(
                          fontSize: 14,
                          fontWeight: FontWeight.bold,
                          color: _textColor,
                        ),
                      ),
                      const SizedBox(height: 4),
                      Text(
                        'In a closed system, the total momentum remains constant. This is true even during collisions or explosions.',
                        style: TextStyle(
                          fontSize: 14,
                          color: _textColor,
                        ),
                      ),
                      const SizedBox(height: 8),
                      Text(
                        'Linear Momentum:',
                        style: TextStyle(
                          fontSize: 14,
                          fontWeight: FontWeight.bold,
                          color: _textColor,
                        ),
                      ),
                      const SizedBox(height: 4),
                      Text(
                        'p = mv, where p is momentum, m is mass, and v is velocity',
                        style: TextStyle(
                          fontSize: 14,
                          color: _textColor,
                        ),
                      ),
                      const SizedBox(height: 8),
                      Text(
                        'Scenarios:',
                        style: TextStyle(
                          fontSize: 14,
                          fontWeight: FontWeight.bold,
                          color: _textColor,
                        ),
                      ),
                      const SizedBox(height: 4),
                      Text(
                        '• Linear Collision: Objects collide and exchange momentum',
                        style: TextStyle(
                          fontSize: 14,
                          color: _textColor,
                        ),
                      ),
                      Text(
                        '• Explosion: A single object breaks into multiple fragments with the same total momentum',
                        style: TextStyle(
                          fontSize: 14,
                          color: _textColor,
                        ),
                      ),
                      Text(
                        '• Rocket: Propulsion demonstrates how ejecting mass creates thrust (action-reaction)',
                        style: TextStyle(
                          fontSize: 14,
                          color: _textColor,
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }
}

class MomentumSimulationPainter extends CustomPainter {
  final List<MomentumObject> objects;
  final double minX;
  final double maxX;
  final double minY;
  final double maxY;
  final bool showVelocityVectors;
  final bool showMomentumVectors;
  final bool showCenterOfMass;
  final List<Offset> centerOfMassHistory;
  final Color primaryColor;
  final Color secondaryColor;
  final Color tertiaryColor;
  final Color textColor;
  final double animationValue;

  MomentumSimulationPainter({
    required this.objects,
    required this.minX,
    required this.maxX,
    required this.minY,
    required this.maxY,
    required this.showVelocityVectors,
    required this.showMomentumVectors,
    required this.showCenterOfMass,
    required this.centerOfMassHistory,
    required this.primaryColor,
    required this.secondaryColor,
    required this.tertiaryColor,
    required this.textColor,
    required this.animationValue,
  });

  @override
  void paint(Canvas canvas, Size size) {
    // Draw background grid
    _drawGrid(canvas, size);

    // Scale factors to map simulation coordinates to canvas coordinates
    final scaleX = size.width / (maxX - minX);
    final scaleY = size.height / (maxY - minY);

    // Draw center of mass trail if enabled
    if (showCenterOfMass && centerOfMassHistory.isNotEmpty) {
      _drawCenterOfMassTrail(canvas, size, scaleX, scaleY);
    }

    // Draw objects
    for (var object in objects) {
      // Map object position to canvas coordinates
      final canvasX = (object.position.dx - minX) * scaleX;
      final canvasY = (object.position.dy - minY) * scaleY;
      final canvasPosition = Offset(canvasX, canvasY);
      final canvasRadius = object.radius * scaleX;

      // Draw object
      final paint = Paint()
        ..color = object.color
        ..style = PaintingStyle.fill;

      canvas.drawCircle(
        canvasPosition,
        canvasRadius,
        paint,
      );

      // Draw border
      final borderPaint = Paint()
        ..color = Colors.black.withOpacity(0.3)
        ..style = PaintingStyle.stroke
        ..strokeWidth = 1.0;

      canvas.drawCircle(
        canvasPosition,
        canvasRadius,
        borderPaint,
      );

      // Draw velocity vector if enabled
      if (showVelocityVectors) {
        final velocityMagnitude = math.sqrt(
          object.velocity.dx * object.velocity.dx +
          object.velocity.dy * object.velocity.dy,
        );

        if (velocityMagnitude > 0.1) {
          final velocityDirection = Offset(
            object.velocity.dx / velocityMagnitude,
            object.velocity.dy / velocityMagnitude,
          );

          final vectorLength = math.min(velocityMagnitude * 2, 20.0);
          final endPoint = Offset(
            canvasPosition.dx + velocityDirection.dx * vectorLength,
            canvasPosition.dy + velocityDirection.dy * vectorLength,
          );

          final velocityPaint = Paint()
            ..color = primaryColor
            ..strokeWidth = 2.0
            ..style = PaintingStyle.stroke;

          // Draw line
          canvas.drawLine(
            canvasPosition,
            endPoint,
            velocityPaint,
          );

          // Draw arrowhead
          final arrowSize = 5.0;
          final angle = math.atan2(
            endPoint.dy - canvasPosition.dy,
            endPoint.dx - canvasPosition.dx,
          );

          final arrowPoint1 = Offset(
            endPoint.dx - arrowSize * math.cos(angle - math.pi / 6),
            endPoint.dy - arrowSize * math.sin(angle - math.pi / 6),
          );

          final arrowPoint2 = Offset(
            endPoint.dx - arrowSize * math.cos(angle + math.pi / 6),
            endPoint.dy - arrowSize * math.sin(angle + math.pi / 6),
          );

          final arrowPath = Path()
            ..moveTo(endPoint.dx, endPoint.dy)
            ..lineTo(arrowPoint1.dx, arrowPoint1.dy)
            ..lineTo(arrowPoint2.dx, arrowPoint2.dy)
            ..close();

          canvas.drawPath(arrowPath, velocityPaint..style = PaintingStyle.fill);
        }
      }

      // Draw momentum vector if enabled
      if (showMomentumVectors) {
        final momentumX = object.mass * object.velocity.dx;
        final momentumY = object.mass * object.velocity.dy;
        final momentumMagnitude = math.sqrt(
          momentumX * momentumX + momentumY * momentumY,
        );

        if (momentumMagnitude > 0.1) {
          final momentumDirection = Offset(
            momentumX / momentumMagnitude,
            momentumY / momentumMagnitude,
          );

          final vectorLength = math.min(momentumMagnitude / 2, 30.0);
          final endPoint = Offset(
            canvasPosition.dx + momentumDirection.dx * vectorLength,
            canvasPosition.dy + momentumDirection.dy * vectorLength,
          );

          final momentumPaint = Paint()
            ..color = secondaryColor
            ..strokeWidth = 2.0
            ..style = PaintingStyle.stroke;

          // Draw dashed line
          final dashLength = 4.0;
          final gapLength = 2.0;
          final dx = endPoint.dx - canvasPosition.dx;
          final dy = endPoint.dy - canvasPosition.dy;
          final distance = math.sqrt(dx * dx + dy * dy);
          final unitX = dx / distance;
          final unitY = dy / distance;

          var currentDistance = 0.0;
          var isDrawing = true;

          while (currentDistance < distance) {
            final remainingDistance = distance - currentDistance;
            final segmentLength = math.min(
              isDrawing ? dashLength : gapLength,
              remainingDistance,
            );

            if (isDrawing) {
              final startX = canvasPosition.dx + unitX * currentDistance;
              final startY = canvasPosition.dy + unitY * currentDistance;
              final endX = canvasPosition.dx + unitX * (currentDistance + segmentLength);
              final endY = canvasPosition.dy + unitY * (currentDistance + segmentLength);

              canvas.drawLine(
                Offset(startX, startY),
                Offset(endX, endY),
                momentumPaint,
              );
            }

            currentDistance += segmentLength;
            isDrawing = !isDrawing;
          }

          // Draw arrowhead
          final arrowSize = 5.0;
          final angle = math.atan2(
            endPoint.dy - canvasPosition.dy,
            endPoint.dx - canvasPosition.dx,
          );

          final arrowPoint1 = Offset(
            endPoint.dx - arrowSize * math.cos(angle - math.pi / 6),
            endPoint.dy - arrowSize * math.sin(angle - math.pi / 6),
          );

          final arrowPoint2 = Offset(
            endPoint.dx - arrowSize * math.cos(angle + math.pi / 6),
            endPoint.dy - arrowSize * math.sin(angle + math.pi / 6),
          );

          final arrowPath = Path()
            ..moveTo(endPoint.dx, endPoint.dy)
            ..lineTo(arrowPoint1.dx, arrowPoint1.dy)
            ..lineTo(arrowPoint2.dx, arrowPoint2.dy)
            ..close();

          canvas.drawPath(arrowPath, momentumPaint..style = PaintingStyle.fill);
        }
      }

      // Draw mass label
      final textStyle = TextStyle(
        color: textColor,
        fontSize: 10,
        fontWeight: FontWeight.bold,
      );

      final textSpan = TextSpan(
        text: '${object.mass.toStringAsFixed(1)} kg',
        style: textStyle,
      );

      final textPainter = TextPainter(
        text: textSpan,
        textDirection: TextDirection.ltr,
      );

      textPainter.layout();
      textPainter.paint(
        canvas,
        Offset(
          canvasPosition.dx - textPainter.width / 2,
          canvasPosition.dy - canvasRadius - textPainter.height - 2,
        ),
      );
    }

    // Draw current center of mass if enabled
    if (showCenterOfMass && centerOfMassHistory.isNotEmpty) {
      final currentCenterOfMass = centerOfMassHistory.last;
      final canvasCenterX = (currentCenterOfMass.dx - minX) * scaleX;
      final canvasCenterY = (currentCenterOfMass.dy - minY) * scaleY;

      final centerPaint = Paint()
        ..color = tertiaryColor
        ..style = PaintingStyle.stroke
        ..strokeWidth = 2.0;

      // Draw crosshair
      final crosshairSize = 8.0;
      canvas.drawLine(
        Offset(canvasCenterX - crosshairSize, canvasCenterY),
        Offset(canvasCenterX + crosshairSize, canvasCenterY),
        centerPaint,
      );

      canvas.drawLine(
        Offset(canvasCenterX, canvasCenterY - crosshairSize),
        Offset(canvasCenterX, canvasCenterY + crosshairSize),
        centerPaint,
      );

      // Draw circle
      canvas.drawCircle(
        Offset(canvasCenterX, canvasCenterY),
        5.0,
        centerPaint,
      );

      // Draw label
      final textStyle = TextStyle(
        color: tertiaryColor,
        fontSize: 10,
        fontWeight: FontWeight.bold,
      );

      final textSpan = TextSpan(
        text: 'Center of Mass',
        style: textStyle,
      );

      final textPainter = TextPainter(
        text: textSpan,
        textDirection: TextDirection.ltr,
      );

      textPainter.layout();
      textPainter.paint(
        canvas,
        Offset(
          canvasCenterX - textPainter.width / 2,
          canvasCenterY + 10,
        ),
      );
    }
  }

  void _drawGrid(Canvas canvas, Size size) {
    final gridPaint = Paint()
      ..color = Colors.grey.withOpacity(0.2)
      ..strokeWidth = 0.5;

    // Draw horizontal grid lines
    final horizontalSpacing = size.height / 10;
    for (int i = 0; i <= 10; i++) {
      final y = i * horizontalSpacing;
      canvas.drawLine(
        Offset(0, y),
        Offset(size.width, y),
        gridPaint,
      );
    }

    // Draw vertical grid lines
    final verticalSpacing = size.width / 10;
    for (int i = 0; i <= 10; i++) {
      final x = i * verticalSpacing;
      canvas.drawLine(
        Offset(x, 0),
        Offset(x, size.height),
        gridPaint,
      );
    }
  }

  void _drawCenterOfMassTrail(Canvas canvas, Size size, double scaleX, double scaleY) {
    if (centerOfMassHistory.length < 2) return;

    final trailPaint = Paint()
      ..color = tertiaryColor.withOpacity(0.5)
      ..strokeWidth = 1.5
      ..style = PaintingStyle.stroke;

    final path = Path();

    // Start path at first point
    final firstPoint = centerOfMassHistory.first;
    final firstCanvasX = (firstPoint.dx - minX) * scaleX;
    final firstCanvasY = (firstPoint.dy - minY) * scaleY;
    path.moveTo(firstCanvasX, firstCanvasY);

    // Add all other points
    for (int i = 1; i < centerOfMassHistory.length; i++) {
      final point = centerOfMassHistory[i];
      final canvasX = (point.dx - minX) * scaleX;
      final canvasY = (point.dy - minY) * scaleY;
      path.lineTo(canvasX, canvasY);
    }

    canvas.drawPath(path, trailPaint);
  }

  @override
  bool shouldRepaint(covariant MomentumSimulationPainter oldDelegate) {
    return oldDelegate.objects != objects ||
        oldDelegate.showVelocityVectors != showVelocityVectors ||
        oldDelegate.showMomentumVectors != showMomentumVectors ||
        oldDelegate.showCenterOfMass != showCenterOfMass ||
        oldDelegate.centerOfMassHistory != centerOfMassHistory ||
        oldDelegate.animationValue != animationValue;
  }
}

class MomentumGraphPainter extends CustomPainter {
  final List<Offset> momentumHistory;
  final Color primaryColor;
  final Color secondaryColor;
  final Color textColor;

  MomentumGraphPainter({
    required this.momentumHistory,
    required this.primaryColor,
    required this.secondaryColor,
    required this.textColor,
  });

  @override
  void paint(Canvas canvas, Size size) {
    // Draw axes
    final axesPaint = Paint()
      ..color = textColor.withOpacity(0.5)
      ..strokeWidth = 1.0;

    // X-axis (time)
    canvas.drawLine(
      Offset(0, size.height - 20),
      Offset(size.width, size.height - 20),
      axesPaint,
    );

    // Y-axis (value)
    canvas.drawLine(
      Offset(20, 0),
      Offset(20, size.height - 20),
      axesPaint,
    );

    // Draw labels
    final labelStyle = TextStyle(
      color: textColor,
      fontSize: 10,
    );

    // X-axis label
    final xLabelSpan = TextSpan(
      text: 'Time',
      style: labelStyle,
    );

    final xLabelPainter = TextPainter(
      text: xLabelSpan,
      textDirection: TextDirection.ltr,
    );

    xLabelPainter.layout();
    xLabelPainter.paint(
      canvas,
      Offset(
        size.width - xLabelPainter.width - 5,
        size.height - xLabelPainter.height - 2,
      ),
    );

    // Y-axis label
    final yLabelSpan = TextSpan(
      text: 'Momentum',
      style: labelStyle,
    );

    final yLabelPainter = TextPainter(
      text: yLabelSpan,
      textDirection: TextDirection.ltr,
    );

    yLabelPainter.layout();
    yLabelPainter.paint(
      canvas,
      Offset(
        5,
        5,
      ),
    );

    // Draw momentum graph
    if (momentumHistory.isNotEmpty) {
      _drawMomentumGraph(canvas, size);
    }

    // Add note about momentum conservation
    final noteStyle = TextStyle(
      color: textColor.withOpacity(0.7),
      fontSize: 9,
      fontStyle: FontStyle.italic,
    );

    final noteText = 'Conservation of Momentum: Total momentum of a closed system remains constant';

    final noteSpan = TextSpan(
      text: noteText,
      style: noteStyle,
    );

    final notePainter = TextPainter(
      text: noteSpan,
      textDirection: TextDirection.ltr,
    );

    notePainter.layout(maxWidth: size.width - 40);
    notePainter.paint(
      canvas,
      Offset(
        30,
        size.height - 15,
      ),
    );
  }

  void _drawMomentumGraph(Canvas canvas, Size size) {
    if (momentumHistory.isEmpty) return;

    // Calculate momentum magnitudes
    final magnitudes = momentumHistory.map((momentum) {
      return momentum.distance;
    }).toList();

    // Find max value for scaling
    final maxValue = magnitudes.reduce((a, b) => math.max(a, b));

    // Calculate scale factors
    final graphWidth = size.width - 30;
    final graphHeight = size.height - 30;
    final xScale = graphWidth / (magnitudes.length - 1 > 0 ? magnitudes.length - 1 : 1);
    final yScale = maxValue > 0 ? (graphHeight - 20) / maxValue : 1;

    // Draw graph line
    final path = Path();
    final graphPaint = Paint()
      ..color = secondaryColor
      ..style = PaintingStyle.stroke
      ..strokeWidth = 2.0;

    for (int i = 0; i < magnitudes.length; i++) {
      final x = 20 + i * xScale;
      final y = size.height - 20 - magnitudes[i] * yScale;

      if (i == 0) {
        path.moveTo(x, y);
      } else {
        path.lineTo(x, y);
      }
    }

    canvas.drawPath(path, graphPaint);

    // Draw current momentum value
    final currentMomentum = magnitudes.last;
    final labelStyle = TextStyle(
      color: secondaryColor,
      fontSize: 10,
      fontWeight: FontWeight.bold,
    );

    final labelSpan = TextSpan(
      text: 'Total Momentum: ${currentMomentum.toStringAsFixed(1)} kg·m/s',
      style: labelStyle,
    );

    final labelPainter = TextPainter(
      text: labelSpan,
      textDirection: TextDirection.ltr,
    );

    labelPainter.layout();
    labelPainter.paint(
      canvas,
      Offset(
        25,
        20,
      ),
    );

    // Draw horizontal line at current momentum value
    final horizontalLinePaint = Paint()
      ..color = secondaryColor.withOpacity(0.3)
      ..strokeWidth = 1.0
      ..style = PaintingStyle.stroke;

    final y = size.height - 20 - currentMomentum * yScale;
    canvas.drawLine(
      Offset(20, y),
      Offset(size.width - 10, y),
      horizontalLinePaint,
    );
  }

  @override
  bool shouldRepaint(covariant MomentumGraphPainter oldDelegate) {
    return oldDelegate.momentumHistory != momentumHistory;
  }
}
