import 'package:flutter/material.dart';

/// A widget that allows users to explore variables in algebra by manipulating values
/// and seeing how they affect expressions
class InteractiveVariableExplorerWidget extends StatefulWidget {
  /// The data for this widget
  final Map<String, dynamic> data;

  /// Optional callback when the widget state changes
  final Function(bool isCompleted)? onStateChanged;

  const InteractiveVariableExplorerWidget({
    Key? key,
    required this.data,
    this.onStateChanged,
  }) : super(key: key);

  /// Factory constructor to create the widget from JSON data
  factory InteractiveVariableExplorerWidget.fromData(Map<String, dynamic> data) {
    return InteractiveVariableExplorerWidget(
      data: data,
    );
  }

  @override
  State<InteractiveVariableExplorerWidget> createState() => _InteractiveVariableExplorerWidgetState();
}

class _InteractiveVariableExplorerWidgetState extends State<InteractiveVariableExplorerWidget> {
  // State variables
  bool _isCompleted = false;
  Map<String, double> _variableValues = {};
  List<Map<String, dynamic>> _expressions = [];
  List<double> _expressionResults = [];
  String _selectedVariable = '';
  double _sliderValue = 0.0;
  
  // Colors
  late Color _primaryColor;
  late Color _secondaryColor;
  late Color _accentColor;
  late Color _textColor;

  @override
  void initState() {
    super.initState();
    _initializeFromData();
  }

  void _initializeFromData() {
    // Initialize colors
    _primaryColor = _parseColor(widget.data['primaryColor'] ?? '#2196F3');
    _secondaryColor = _parseColor(widget.data['secondaryColor'] ?? '#4CAF50');
    _accentColor = _parseColor(widget.data['accentColor'] ?? '#FF9800');
    _textColor = _parseColor(widget.data['textColor'] ?? '#212121');
    
    // Initialize variables
    final variables = widget.data['variables'] as List<dynamic>;
    for (var variable in variables) {
      _variableValues[variable['name']] = variable['initialValue'].toDouble();
    }
    
    // Set selected variable to the first one
    if (_variableValues.isNotEmpty) {
      _selectedVariable = _variableValues.keys.first;
      _sliderValue = _variableValues[_selectedVariable]!;
    }
    
    // Initialize expressions
    _expressions = (widget.data['expressions'] as List<dynamic>)
        .map((e) => e as Map<String, dynamic>)
        .toList();
    
    // Calculate initial expression results
    _calculateExpressionResults();
  }

  Color _parseColor(String hexColor) {
    hexColor = hexColor.replaceAll('#', '');
    if (hexColor.length == 6) {
      hexColor = 'FF$hexColor';
    }
    return Color(int.parse(hexColor, radix: 16));
  }

  void _calculateExpressionResults() {
    _expressionResults = [];
    
    for (var expression in _expressions) {
      final formula = expression['formula'] as String;
      double result = _evaluateExpression(formula);
      _expressionResults.add(result);
    }
    
    // Check if the user has completed the challenge
    if (widget.data.containsKey('targetValues')) {
      final targetValues = widget.data['targetValues'] as List<dynamic>;
      bool allTargetsReached = true;
      
      for (int i = 0; i < targetValues.length; i++) {
        if (i < _expressionResults.length) {
          final target = targetValues[i].toDouble();
          final result = _expressionResults[i];
          final tolerance = widget.data['tolerance']?.toDouble() ?? 0.001;
          
          if ((target - result).abs() > tolerance) {
            allTargetsReached = false;
            break;
          }
        }
      }
      
      if (allTargetsReached != _isCompleted) {
        setState(() {
          _isCompleted = allTargetsReached;
        });
        widget.onStateChanged?.call(_isCompleted);
      }
    }
  }

  double _evaluateExpression(String formula) {
    // Replace variable names with their values
    String evaluableFormula = formula;
    for (var entry in _variableValues.entries) {
      evaluableFormula = evaluableFormula.replaceAll(
        entry.key, 
        entry.value.toString()
      );
    }
    
    // Simple expression evaluator for basic operations
    // Note: In a real implementation, you would use a more robust expression parser
    try {
      // This is a simplified implementation that handles basic operations
      // For a production app, use a proper expression parser library
      return _simpleEvaluate(evaluableFormula);
    } catch (e) {
      print('Error evaluating expression: $e');
      return 0.0;
    }
  }

  double _simpleEvaluate(String expression) {
    // Very simple evaluator for demo purposes
    // Only handles basic operations with proper parentheses
    // In a real app, use a proper expression parser library
    
    // Remove all spaces
    expression = expression.replaceAll(' ', '');
    
    // Handle parentheses first
    while (expression.contains('(')) {
      final openIndex = expression.lastIndexOf('(');
      final closeIndex = expression.indexOf(')', openIndex);
      if (closeIndex == -1) throw Exception('Mismatched parentheses');
      
      final subExpression = expression.substring(openIndex + 1, closeIndex);
      final result = _simpleEvaluate(subExpression);
      
      expression = expression.substring(0, openIndex) + 
                  result.toString() + 
                  expression.substring(closeIndex + 1);
    }
    
    // Handle multiplication and division
    while (expression.contains('*') || expression.contains('/')) {
      final mulIndex = expression.contains('*') ? expression.indexOf('*') : -1;
      final divIndex = expression.contains('/') ? expression.indexOf('/') : -1;
      
      final opIndex = (mulIndex != -1 && (divIndex == -1 || mulIndex < divIndex)) 
          ? mulIndex : divIndex;
      
      // Find the numbers on either side of the operator
      int leftStart = opIndex - 1;
      while (leftStart >= 0 && 
             (RegExp(r'[0-9.]').hasMatch(expression[leftStart]) || 
              (leftStart == 0 && expression[leftStart] == '-'))) {
        leftStart--;
      }
      leftStart++;
      
      int rightEnd = opIndex + 1;
      while (rightEnd < expression.length && 
             (RegExp(r'[0-9.]').hasMatch(expression[rightEnd]) || 
              (rightEnd == opIndex + 1 && expression[rightEnd] == '-'))) {
        rightEnd++;
      }
      
      final leftNum = double.parse(expression.substring(leftStart, opIndex));
      final rightNum = double.parse(expression.substring(opIndex + 1, rightEnd));
      
      double result;
      if (expression[opIndex] == '*') {
        result = leftNum * rightNum;
      } else {
        result = leftNum / rightNum;
      }
      
      expression = expression.substring(0, leftStart) + 
                  result.toString() + 
                  expression.substring(rightEnd);
    }
    
    // Handle addition and subtraction
    while (expression.contains('+') || (expression.contains('-') && expression.indexOf('-') > 0)) {
      final addIndex = expression.contains('+') ? expression.indexOf('+') : -1;
      int subIndex = -1;
      
      // Find the first subtraction that's not a negative sign at the start
      for (int i = 1; i < expression.length; i++) {
        if (expression[i] == '-' && 
            RegExp(r'[0-9.]').hasMatch(expression[i-1])) {
          subIndex = i;
          break;
        }
      }
      
      final opIndex = (addIndex != -1 && (subIndex == -1 || addIndex < subIndex)) 
          ? addIndex : subIndex;
      
      if (opIndex == -1) break; // No more operations
      
      // Find the numbers on either side of the operator
      int leftStart = opIndex - 1;
      while (leftStart >= 0 && RegExp(r'[0-9.]').hasMatch(expression[leftStart])) {
        leftStart--;
      }
      leftStart++;
      
      int rightEnd = opIndex + 1;
      while (rightEnd < expression.length && 
             (RegExp(r'[0-9.]').hasMatch(expression[rightEnd]) || 
              (rightEnd == opIndex + 1 && expression[rightEnd] == '-'))) {
        rightEnd++;
      }
      
      final leftNum = double.parse(expression.substring(leftStart, opIndex));
      final rightNum = double.parse(expression.substring(opIndex + 1, rightEnd));
      
      double result;
      if (expression[opIndex] == '+') {
        result = leftNum + rightNum;
      } else {
        result = leftNum - rightNum;
      }
      
      expression = expression.substring(0, leftStart) + 
                  result.toString() + 
                  expression.substring(rightEnd);
    }
    
    // At this point, the expression should be a single number
    return double.parse(expression);
  }

  void _updateVariableValue(double value) {
    setState(() {
      _variableValues[_selectedVariable] = value;
      _sliderValue = value;
      _calculateExpressionResults();
    });
  }

  void _selectVariable(String variable) {
    setState(() {
      _selectedVariable = variable;
      _sliderValue = _variableValues[variable]!;
    });
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withOpacity(0.2),
            spreadRadius: 1,
            blurRadius: 3,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Title
          Text(
            widget.data['title'] ?? 'Variable Explorer',
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
              color: _textColor,
            ),
          ),
          
          const SizedBox(height: 16),
          
          // Description
          if (widget.data.containsKey('description'))
            Padding(
              padding: const EdgeInsets.only(bottom: 16),
              child: Text(
                widget.data['description'],
                style: TextStyle(
                  fontSize: 14,
                  color: _textColor.withOpacity(0.7),
                ),
              ),
            ),
          
          // Variable selection
          Wrap(
            spacing: 8,
            children: _variableValues.keys.map((variable) {
              return ChoiceChip(
                label: Text(variable),
                selected: _selectedVariable == variable,
                onSelected: (selected) {
                  if (selected) {
                    _selectVariable(variable);
                  }
                },
                selectedColor: _primaryColor,
                labelStyle: TextStyle(
                  color: _selectedVariable == variable ? Colors.white : _textColor,
                ),
              );
            }).toList(),
          ),
          
          const SizedBox(height: 16),
          
          // Variable slider
          if (_selectedVariable.isNotEmpty)
            Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Text(
                      '$_selectedVariable = ${_sliderValue.toStringAsFixed(1)}',
                      style: TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.bold,
                        color: _primaryColor,
                      ),
                    ),
                    Text(
                      'Range: ${_getVariableMin(_selectedVariable).toStringAsFixed(1)} to ${_getVariableMax(_selectedVariable).toStringAsFixed(1)}',
                      style: TextStyle(
                        fontSize: 12,
                        color: _textColor.withOpacity(0.6),
                      ),
                    ),
                  ],
                ),
                Slider(
                  value: _sliderValue,
                  min: _getVariableMin(_selectedVariable),
                  max: _getVariableMax(_selectedVariable),
                  divisions: _getVariableDivisions(_selectedVariable),
                  label: _sliderValue.toStringAsFixed(1),
                  activeColor: _primaryColor,
                  inactiveColor: _primaryColor.withOpacity(0.3),
                  onChanged: _updateVariableValue,
                ),
              ],
            ),
          
          const SizedBox(height: 16),
          
          // Expressions and results
          ...List.generate(_expressions.length, (index) {
            final expression = _expressions[index];
            final result = index < _expressionResults.length 
                ? _expressionResults[index] 
                : 0.0;
            
            bool hasTarget = false;
            bool targetReached = false;
            
            if (widget.data.containsKey('targetValues') && 
                index < (widget.data['targetValues'] as List).length) {
              hasTarget = true;
              final target = (widget.data['targetValues'] as List)[index].toDouble();
              final tolerance = widget.data['tolerance']?.toDouble() ?? 0.001;
              targetReached = (target - result).abs() <= tolerance;
            }
            
            return Card(
              margin: const EdgeInsets.only(bottom: 8),
              color: hasTarget && targetReached 
                  ? _secondaryColor.withOpacity(0.1) 
                  : Colors.white,
              child: Padding(
                padding: const EdgeInsets.all(12),
                child: Row(
                  children: [
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            expression['display'] ?? expression['formula'],
                            style: TextStyle(
                              fontSize: 16,
                              fontWeight: FontWeight.w500,
                              color: _textColor,
                            ),
                          ),
                          const SizedBox(height: 4),
                          Text(
                            'Result: ${result.toStringAsFixed(2)}',
                            style: TextStyle(
                              fontSize: 14,
                              color: _textColor.withOpacity(0.7),
                            ),
                          ),
                          if (hasTarget)
                            Text(
                              'Target: ${(widget.data['targetValues'] as List)[index].toDouble().toStringAsFixed(2)}',
                              style: TextStyle(
                                fontSize: 12,
                                color: targetReached ? _secondaryColor : _accentColor,
                                fontWeight: FontWeight.w500,
                              ),
                            ),
                        ],
                      ),
                    ),
                    if (hasTarget)
                      Icon(
                        targetReached ? Icons.check_circle : Icons.radio_button_unchecked,
                        color: targetReached ? _secondaryColor : _accentColor,
                      ),
                  ],
                ),
              ),
            );
          }),
          
          const SizedBox(height: 16),
          
          // Challenge completion message
          if (_isCompleted && widget.data.containsKey('completionMessage'))
            Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: _secondaryColor.withOpacity(0.1),
                borderRadius: BorderRadius.circular(8),
                border: Border.all(color: _secondaryColor),
              ),
              child: Row(
                children: [
                  Icon(Icons.check_circle, color: _secondaryColor),
                  const SizedBox(width: 8),
                  Expanded(
                    child: Text(
                      widget.data['completionMessage'],
                      style: TextStyle(
                        color: _secondaryColor,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                  ),
                ],
              ),
            ),
          
          // Widget name tag at the bottom
          if (widget.data['showNameTag'] ?? true)
            Padding(
              padding: const EdgeInsets.only(top: 8),
              child: Text(
                'InteractiveVariableExplorer',
                style: TextStyle(
                  color: Colors.grey[600],
                  fontSize: 12,
                  fontStyle: FontStyle.italic,
                ),
              ),
            ),
        ],
      ),
    );
  }
  
  double _getVariableMin(String variable) {
    final variables = widget.data['variables'] as List<dynamic>;
    for (var v in variables) {
      if (v['name'] == variable) {
        return v['min'].toDouble();
      }
    }
    return 0.0;
  }
  
  double _getVariableMax(String variable) {
    final variables = widget.data['variables'] as List<dynamic>;
    for (var v in variables) {
      if (v['name'] == variable) {
        return v['max'].toDouble();
      }
    }
    return 10.0;
  }
  
  int _getVariableDivisions(String variable) {
    final variables = widget.data['variables'] as List<dynamic>;
    for (var v in variables) {
      if (v['name'] == variable) {
        return v['divisions'] ?? 10;
      }
    }
    return 10;
  }
}
