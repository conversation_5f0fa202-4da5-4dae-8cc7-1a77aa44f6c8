{"id": "introduction-to-inequalities", "title": "Introduction to Inequalities", "description": "Explore mathematical statements that compare values rather than equating them.", "order": 4, "lessons": [{"id": "iti-l1-language-of-inequality", "title": "Speaking Inequality: Understanding the Symbols", "description": "Learn to read, write, and understand the fundamental symbols of inequality: <, >, ≤, and ≥, and how they compare quantities.", "order": 1, "type": "interactive_lesson", "estimatedTimeMinutes": 8, "contentBlocks": [{"id": "iti-l1-s1", "type": "lesson_screen", "order": 1, "estimatedTimeSeconds": 60, "content": {"headline": "Beyond Equality: Inequalities", "body_md": "So far, we've focused on equations, where two expressions are strictly equal. But in mathematics, and in real life, we often need to compare quantities that *aren't* necessarily equal. This is where **inequalities** come in. An inequality is a mathematical statement that shows a relationship between two expressions that are not equal, or might not be equal.", "visual": {"type": "giphy_search", "value": "comparing different sizes"}, "hook": "Life isn't always equal, and neither is math! Let's explore.", "interactive_element": {"type": "button", "text": "Introduce me to these new symbols!", "action": "next_screen"}}}, {"id": "iti-l1-s2", "type": "lesson_screen", "order": 2, "estimatedTimeSeconds": 90, "content": {"headline": "The Symbols of Inequality", "body_md": "We use special symbols to express these relationships:\n\n-   **<** : **Less than**. Example: `3 < 5` (3 is less than 5).\n-   **>** : **Greater than**. Example: `7 > 2` (7 is greater than 2).\n-   **≤** : **Less than or equal to**. Example: `x ≤ 4` (x can be 4, or any number smaller).\n-   **≥** : **Greater than or equal to**. Example: `y ≥ 0` (y can be 0, or any number larger).\n\nThink of `<` and `>` like an alligator's mouth – it always wants to eat the bigger number!", "visual": {"type": "local_asset", "value": "assets/images/algebra/inequality_alligator.svg", "alt_text": "Alligator mouth for < and > symbols."}, "hook": "These symbols are your new tools for comparing.", "interactive_element": {"type": "button", "text": "Let's get friendly with these symbols!", "action": "next_screen"}}}, {"id": "iti-l1-s3", "type": "lesson_screen", "order": 3, "estimatedTimeSeconds": 150, "content": {"headline": "Interactive: Match the Symbol", "body_md": "Let's get comfortable with these symbols. For each scenario or pair of values, choose the correct inequality symbol that describes the relationship.", "visual": {"type": "static_text", "value": "Interactive: Inequality Symbol Matcher"}, "interactive_element": {"type": "interactive_inequality_symbol_matcher", "overall_prompt": "Choose the correct inequality symbol.", "pairs": [{"id": "pair1", "value1_text": "5", "value2_text": "10", "options_symbols": ["<", ">", "≤", "≥", "="], "correct_symbol": "<", "feedback_correct": "Correct! 5 < 10.", "feedback_incorrect_template": "Is 5 smaller or larger than 10?"}, {"id": "pair2", "value1_text": "8", "value2_text": "3", "options_symbols": ["<", ">", "≤", "≥", "="], "correct_symbol": ">", "feedback_correct": "Spot on! 8 > 3.", "feedback_incorrect_template": "Is 8 smaller or larger than 3?"}, {"id": "pair3", "value1_text": "x can be 6 or smaller", "value2_text": "6", "options_symbols": ["<", ">", "≤", "≥", "="], "correct_symbol": "≤", "feedback_correct": "Exactly! x ≤ 6.", "feedback_incorrect_template": "Which symbol includes 'less than' AND 'equal to'?"}, {"id": "pair4", "value1_text": "y can be -2 or larger", "value2_text": "-2", "options_symbols": ["<", ">", "≤", "≥", "="], "correct_symbol": "≥", "feedback_correct": "Perfect! y ≥ -2.", "feedback_incorrect_template": "Which symbol includes 'greater than' AND 'equal to'?"}, {"id": "pair5", "value1_text": "Days in a week", "value2_text": "7", "options_symbols": ["<", ">", "≤", "≥", "="], "correct_symbol": "=", "feedback_correct": "You got it! Exactly 7.", "feedback_incorrect_template": "How many days in a week vs 7?"}], "check_button_text": "Check My Symbols", "action_button_text": "How do we draw these on a line?"}, "hook": "Time to test your symbol recognition!"}}]}, {"id": "iti-l2-visualizing-inequalities-number-line", "title": "Drawing the Line: Graphing Inequalities", "description": "Learn to visually represent the solution sets of inequalities on a number line using open/closed circles and arrows.", "order": 2, "type": "interactive_lesson", "estimatedTimeMinutes": 8, "contentBlocks": [{"id": "iti-l2-s1", "type": "lesson_screen", "order": 1, "estimatedTimeSeconds": 70, "content": {"headline": "Graphing Inequalities", "body_md": "Inequalities usually describe a whole *range* of values. A number line is perfect for visualizing these.\n\n**Key Components:**\n1.  **Circles (at boundary):**\n    *   **Open (o)** for `<` or `>` (boundary NOT included).\n    *   **Closed (●)** for `≤` or `≥` (boundary IS included).\n2.  **Arrows:** Points left for 'less than', right for 'greater than'.", "visual": {"type": "local_asset", "value": "assets/images/algebra/inequality_number_line_examples.svg", "alt_text": "Number lines for x > 2 and a <= -1."}, "hook": "A number line can tell a whole story about an inequality.", "interactive_element": {"type": "button", "text": "Show me how to graph these!", "action": "next_screen"}}}, {"id": "iti-l2-s2", "type": "lesson_screen", "order": 2, "estimatedTimeSeconds": 120, "content": {"headline": "Interactive: Graph y ≤ -1", "body_md": "Let's graph the inequality `y ≤ -1`.\n1. Should the circle at -1 be open or closed?\n2. In which direction should the arrow point?", "visual": {"type": "static_text", "value": "Interactive: Number Line Plotter for y ≤ -1"}, "interactive_element": {"type": "interactive_number_line_solution_plotter", "inequality_string": "y ≤ -1", "variable_to_solve": "y", "boundary_point_value": -1, "is_boundary_inclusive": true, "direction_is_greater": false, "number_line_min": -5, "number_line_max": 5, "prompt_plot": "Graph `y ≤ -1`. Choose circle type and arrow direction.", "feedback_correct_plot": "Great! Closed circle at -1, arrow left.", "feedback_incorrect_plot_circle_type": "For '≤', the boundary is included. Use a closed circle.", "feedback_incorrect_plot_direction": "For '≤', values are less than or equal. <PERSON> left.", "feedback_incorrect_plot_both": "For `y ≤ -1`: closed circle at -1, arrow left.", "action_button_text": "Why is graphing so helpful?"}, "hook": "Your turn to draw the line (literally!)."}}, {"id": "iti-l2-s3", "type": "lesson_screen", "order": 3, "estimatedTimeSeconds": 40, "content": {"headline": "Visualizing Solutions", "body_md": "Graphing inequalities helps you see the entire set of solutions at a glance. It's a very useful skill, especially as inequalities become more complex!", "visual": {"type": "giphy_search", "value": "visualize data graph"}, "hook": "You're now a master of inequality art!", "interactive_element": {"type": "button", "text": "Ready to solve some inequalities!", "action": "next_lesson"}}}]}, {"id": "iti-l3-solving-one-step-inequalities", "title": "One-Step Inequalities: The Big Twist!", "description": "Learn to solve one-step inequalities using inverse operations, and master the crucial rule for multiplying or dividing by negative numbers.", "order": 3, "type": "interactive_lesson", "estimatedTimeMinutes": 9, "contentBlocks": [{"id": "iti-l3-s1", "type": "lesson_screen", "order": 1, "estimatedTimeSeconds": 70, "content": {"headline": "Solving One-Step Inequalities", "body_md": "Good news! Solving basic one-step inequalities is almost identical to solving one-step equations. Use inverse operations.\n\n- `x + 5 > 10`  =>  `x > 5`\n- `y - 3 ≤ 7`  =>  `y ≤ 10`\n- `2k < 8`  =>  `k < 4`\n- `m/3 ≥ 2`  =>  `m ≥ 6`\n\nFeels familiar, right? But there's **ONE VERY IMPORTANT TWIST**...", "visual": {"type": "giphy_search", "value": "plot twist surprise"}, "hook": "You'll find this very familiar, with one crucial difference...", "interactive_element": {"type": "button", "text": "Is there a catch?", "action": "next_screen"}}}, {"id": "iti-l3-s2", "type": "lesson_screen", "order": 2, "estimatedTimeSeconds": 90, "content": {"headline": "The Golden Rule (Twist!)", "body_md": "**If you multiply or divide both sides of an inequality by a NEGATIVE number, you MUST FLIP THE DIRECTION of the inequality sign.**\n\n- `<` becomes `>`\n- `>` becomes `<`\n- `≤` becomes `≥`\n- `≥` becomes `≤`\n\nWhy? Consider `-2 < 4` (true). Multiply by `-1` *without* flipping: `2 < -4` (FALSE!). Flip it: `2 > -4` (true).", "visual": {"type": "static_text", "value": "Warning: Multiply/Divide by Negative => FLIP SIGN!"}, "hook": "This rule is super important – don't forget it!", "interactive_element": {"type": "button", "text": "Show me this 'flip' in action!", "action": "next_screen"}}}, {"id": "iti-l3-s3", "type": "lesson_screen", "order": 3, "estimatedTimeSeconds": 120, "content": {"headline": "Example: Solve -2x < 6", "body_md": "1. To isolate 'x', divide both sides by -2.\n2. **Because we divide by a negative, FLIP the sign!**\n   `(-2x) / -2 > 6 / -2`\n   `x > -3`\n\nIf we didn't flip (`x < -3`), test `x = -4`: `-2(-4) < 6` => `8 < 6` (FALSE). Our flipped `x > -3` is correct (test `x = -1`: `-2(-1) < 6` => `2 < 6`, TRUE).", "visual": {"type": "static_text", "value": "-2x < 6  =>  x > -3"}, "hook": "Watch carefully how the sign behaves.", "interactive_element": {"type": "button", "text": "My turn to try the twist!", "action": "next_screen"}}}, {"id": "iti-l3-s4", "type": "lesson_screen", "order": 4, "estimatedTimeSeconds": 100, "content": {"headline": "Interactive: Solve -3a ≥ 12", "body_md": "Solve for 'a': `-3a ≥ 12`.\nPay close attention to the operation and whether the sign needs to flip.", "visual": {"type": "static_text", "value": "Interactive: Solve -3a ≥ 12"}, "interactive_element": {"type": "interactive_one_step_inequality_solver", "inequalities": [{"inequality": "-3a ≥ 12", "solution": "a ≤ -4", "steps": ["Start with the original inequality: -3a ≥ 12", "Divide both sides by -3: -3a ÷ (-3) ≤ 12 ÷ (-3)", "When dividing by a negative number, flip the inequality sign: a ≤ -4"], "explanations": ["This is our starting inequality.", "To isolate the variable a, we need to divide both sides by -3.", "When dividing both sides of an inequality by a negative number, we must flip the inequality sign. '≥' becomes '≤'."], "operationType": "division with sign flip", "numberLineData": {"boundaryValue": -4, "boundaryIncluded": true, "direction": "left"}}], "action_button_text": "Why is this flip rule so vital?"}, "hook": "Remember the golden rule of negative multiplication/division!"}}, {"id": "iti-l3-s5", "type": "lesson_screen", "order": 5, "estimatedTimeSeconds": 40, "content": {"headline": "Master the Flip!", "body_md": "This sign-flipping rule is the main difference between solving one-step equations and inequalities. Master it!", "visual": {"type": "giphy_search", "value": "gymnastics flip"}, "hook": "You've got the key to unlocking inequalities!", "interactive_element": {"type": "button", "text": "Ready for two-step inequalities!", "action": "next_lesson"}}}]}, {"id": "iti-l4-solving-two-step-inequalities", "title": "Two-Step Inequalities: Combining the Rules", "description": "Extend your solving skills to two-step inequalities, remembering to apply SADMEP and the crucial sign-flip rule for negative multiplication/division.", "order": 4, "type": "interactive_lesson", "estimatedTimeMinutes": 10, "contentBlocks": [{"id": "iti-l4-s1", "type": "lesson_screen", "order": 1, "estimatedTimeSeconds": 90, "content": {"headline": "Solving Two-Step Inequalities", "body_md": "Apply inverse operations in reverse order (SADMEP), and watch for the sign-flip rule!\n\n**Example 1 (No flip):** `2k - 5 > 7`\n1. Add 5: `2k > 12`\n2. Divide by 2: `k > 6`\n\n**Example 2 (Flip needed!):** `10 - 3p ≤ 1`\n1. Subtract 10: `-3p ≤ -9`\n2. Divide by -3 (FLIP!): `p ≥ 3`", "visual": {"type": "giphy_search", "value": "detective rules"}, "hook": "Now we combine our skills: two steps AND the flip rule!", "interactive_element": {"type": "button", "text": "Let's try a guided two-step inequality!", "action": "next_screen"}}}, {"id": "iti-l4-s2", "type": "lesson_screen", "order": 2, "estimatedTimeSeconds": 180, "content": {"headline": "Guided: Solve 4 - (x/2) < 2", "body_md": "Let's carefully solve `4 - (x/2) < 2`.", "visual": {"type": "static_text", "value": "Interactive: Solve 4 - (x/2) < 2"}, "interactive_element": {"type": "interactive_two_step_inequality_solver", "inequalities": [{"inequality": "4 - (x/2) < 2", "solution": "x > 4", "steps": ["Start with the original inequality: 4 - (x/2) < 2", "Subtract 4 from both sides: 4 - (x/2) - 4 < 2 - 4", "Simplify: -(x/2) < -2", "Multiply both sides by -2: -(x/2) × (-2) > -2 × (-2)", "When multiplying by a negative number, flip the inequality sign", "Simplify: x > 4"], "explanations": ["This is our starting inequality.", "To isolate the term with the variable, we first subtract 4 from both sides.", "After simplifying, we get -(x/2) < -2.", "To isolate the variable x, we need to multiply both sides by -2.", "When multiplying both sides of an inequality by a negative number, we must flip the inequality sign. '<' becomes '>'.", "After simplifying, we get x > 4, which means all values of x greater than 4 satisfy the original inequality."], "operationType": "subtraction, multiplication with sign flip", "numberLineData": {"boundaryValue": 4, "boundaryIncluded": false, "direction": "right"}}], "action_button_text": "How do we graph these solved inequalities?"}, "hook": "Careful with each step, especially that flip!"}}]}, {"id": "iti-l5-graphing-inequalities", "title": "Picture This: Graphing Solved Inequalities", "description": "Practice solving multi-step inequalities and then accurately representing their solution sets on a number line.", "order": 5, "type": "interactive_lesson", "estimatedTimeMinutes": 8, "contentBlocks": [{"id": "iti-l5-s1", "type": "lesson_screen", "order": 1, "estimatedTimeSeconds": 90, "content": {"headline": "Solve and Graph: 3b + 7 ≤ 1", "body_md": "After solving an inequality, graph its solution set.\n\n**Example:** `3b + 7 ≤ 1`\n1.  **Solve:**\n    -   Subtract 7: `3b ≤ -6`\n    -   Divide by 3: `b ≤ -2`\n2.  **Graph `b ≤ -2`:**\n    -   **Closed circle (●)** at -2 (due to `≤`).\n    -   Arrow to the **left** (for 'less than').", "visual": {"type": "local_asset", "value": "assets/images/algebra/inequality_graph_b_leq_neg2.svg", "alt_text": "Graph of b <= -2"}, "hook": "Putting it all together: solve first, then graph!", "interactive_element": {"type": "button", "text": "Let me try solving and graphing!", "action": "next_screen"}}}, {"id": "iti-l5-s2", "type": "lesson_screen", "order": 2, "estimatedTimeSeconds": 150, "content": {"headline": "Interactive: Solve & Graph 2x - 3 > 5", "body_md": "Let's try one from start to finish!", "visual": {"type": "static_text", "value": "Interactive: Solve and Graph 2x - 3 > 5"}, "interactive_element": {"type": "interactive_inequality_grapher", "inequalities": [{"inequality": "2x - 3 > 5", "description": "Solve and graph the inequality 2x - 3 > 5", "slope": 0, "yIntercept": 4, "inequalityType": ">", "isLineInclusive": false, "isUpperHalfShaded": true}], "action_button_text": "Why is this two-step process useful?"}, "hook": "Show off your solving and graphing skills!"}}, {"id": "iti-l5-s3", "type": "lesson_screen", "order": 3, "estimatedTimeSeconds": 40, "content": {"headline": "Graphing Power", "body_md": "Graphing is a powerful way to confirm your understanding of what an inequality's solution truly represents – a whole set of numbers!", "visual": {"type": "unsplash_search", "value": "map direction compass"}, "hook": "You can now visually represent complex solutions!", "interactive_element": {"type": "button", "text": "Ready for the Inequality Investigator Test!", "action": "next_lesson"}}}]}], "moduleTest": {"id": "iti-mt1-inequality-investigator", "title": "Module Test: Inequality Investigator", "description": "Solve and represent inequalities graphically.", "order": 6, "type": "module_test_interactive", "estimatedTimeMinutes": 7, "contentBlocks": [{"id": "iti-mt1-s0-intro", "type": "test_screen_intro", "order": 1, "estimatedTimeSeconds": 20, "content": {"headline": "Module Test: Inequality Investigator", "body_md": "Time to investigate some inequalities!", "visual": {"type": "giphy_search", "value": "detective magnifying glass"}, "interactive_element": {"type": "button", "text": "Start Investigation!", "action": "next_screen"}}}, {"id": "iti-mt1-s1-test", "type": "test_screen_interactive", "order": 2, "estimatedTimeSeconds": 300, "content": {"headline": "Inequality Investigator Challenge", "body_md": "Test your inequality skills with this comprehensive challenge!", "visual": {"type": "static_text", "value": "Interactive: Inequality Investigator"}, "interactive_element": {"type": "interactive_inequality_investigator", "questions": [{"questionText": "Solve for x: x - 7 > 3", "answerType": "text_input", "correctAnswer": "x > 10", "options": [], "explanation": "Add 7 to both sides: x > 10", "hint": "Add 7 to both sides."}, {"questionText": "Which symbol means 'less than or equal to'?", "answerType": "multiple_choice", "correctAnswer": "≤", "options": ["<", ">", "≤", "≥"], "explanation": "≤ means less than or equal to.", "hint": "Think about which symbol includes equality."}, {"questionText": "Solve for y: -5y ≤ 20", "answerType": "text_input", "correctAnswer": "y ≥ -4", "options": [], "explanation": "Divide by -5 and <PERSON><PERSON> the inequality sign: y ≥ -4", "hint": "Remember to flip the sign when dividing by a negative number."}, {"questionText": "Which graph represents x < 1?", "answerType": "multiple_choice", "correctAnswer": "Open circle at 1, arrow to the left", "options": ["Open circle at 1, arrow to the right", "Closed circle at 1, arrow to the right", "Open circle at 1, arrow to the left", "Closed circle at 1, arrow to the left"], "explanation": "For x < 1, we use an open circle at 1 (not included) and an arrow pointing left (less than).", "hint": "Think about whether 1 is included in the solution and which direction 'less than' points."}, {"questionText": "Solve for z: (z/2) + 3 ≥ 5", "answerType": "text_input", "correctAnswer": "z ≥ 4", "options": [], "explanation": "Subtract 3 from both sides: z/2 ≥ 2. Then multiply by 2: z ≥ 4", "hint": "First isolate the term with z, then multiply to isolate z."}, {"questionText": "When solving -3x > 9, why do we flip the inequality sign?", "answerType": "multiple_choice", "correctAnswer": "Because we're dividing by a negative number", "options": ["Because we're dividing by a negative number", "Because we're multiplying by a negative number", "Because the variable is negative", "We don't need to flip the sign"], "explanation": "When dividing or multiplying both sides of an inequality by a negative number, we must flip the inequality sign.", "hint": "Think about what happens to the direction of an inequality when you multiply or divide by a negative number."}]}}}, {"id": "iti-mt1-s2-conclusion", "type": "lesson_screen", "order": 3, "estimatedTimeSeconds": 30, "content": {"headline": "Investigation Complete!", "body_md": "Great work investigating those inequalities!", "visual": {"type": "giphy_search", "value": "detective solved case"}, "interactive_element": {"type": "button", "text": "Back to Course", "action": "module_complete"}}}]}}