                        -HC:\flutter\packages\flutter_tools\gradle\src\main\groovy
-DCMAKE_SYSTEM_NAME=Android
-DCMAKE_EXPORT_COMPILE_COMMANDS=ON
-DCMAKE_SYSTEM_VERSION=21
-DANDROID_PLATFORM=android-21
-DANDROID_ABI=x86_64
-DCMAKE_ANDROID_ARCH_ABI=x86_64
-DANDROID_NDK=D:\AndroidSDK\ndk\29.0.13113456
-DCMAKE_ANDROID_NDK=D:\AndroidSDK\ndk\29.0.13113456
-DCMAKE_TOOLCHAIN_FILE=D:\AndroidSDK\ndk\29.0.13113456\build\cmake\android.toolchain.cmake
-DCMAKE_MAKE_PROGRAM=D:\AndroidSDK\cmake\3.22.1\bin\ninja.exe
-DCMAKE_LIBRARY_OUTPUT_DIRECTORY=D:\AResonance\rn\build\app\intermediates\cxx\Debug\5e5t3o49\obj\x86_64
-DCMAKE_RUNTIME_OUTPUT_DIRECTORY=D:\AResonance\rn\build\app\intermediates\cxx\Debug\5e5t3o49\obj\x86_64
-DCMAKE_BUILD_TYPE=Debug
-BD:\AResonance\rn\android\app\.cxx\Debug\5e5t3o49\x86_64
-GNinja
-Wno-dev
--no-warn-unused-cli
                        Build command args: []
                        Version: 2