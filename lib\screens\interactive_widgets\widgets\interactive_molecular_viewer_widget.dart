import 'package:flutter/material.dart';
import 'dart:math' as math;

/// Custom painter for molecular visualization
class MolecularPainter extends CustomPainter {
  final Molecule molecule;
  final bool showLabels;
  final double zoom;
  final Color primaryColor;
  final Color textColor;

  MolecularPainter({
    required this.molecule,
    required this.showLabels,
    required this.zoom,
    required this.primaryColor,
    required this.textColor,
  });

  @override
  void paint(Canvas canvas, Size size) {
    final width = size.width;
    final height = size.height;

    // Calculate center of the canvas
    final centerX = width / 2;
    final centerY = height / 2;

    // Scale factor for atom sizes and positions
    final scaleFactor = 50.0 * zoom;

    // Sort atoms by z-coordinate for proper depth rendering
    final sortedAtoms = List<MapEntry<int, Atom>>.from(
      molecule.atoms.asMap().entries.toList()
        ..sort((a, b) => b.value.z.compareTo(a.value.z)),
    );

    // Draw bonds first (behind atoms)
    for (final bond in molecule.bonds) {
      final atom1 = molecule.atoms[bond.atom1Index];
      final atom2 = molecule.atoms[bond.atom2Index];

      // Calculate screen positions
      final x1 = centerX + atom1.x * scaleFactor;
      final y1 = centerY + atom1.y * scaleFactor;
      final x2 = centerX + atom2.x * scaleFactor;
      final y2 = centerY + atom2.y * scaleFactor;

      // Draw bond
      final bondPaint = Paint()
        ..color = Colors.grey.shade700
        ..style = PaintingStyle.stroke
        ..strokeWidth = bond.bondType == 1 ? 3.0 : 6.0;

      if (bond.bondType == 1) {
        // Single bond
        canvas.drawLine(
          Offset(x1, y1),
          Offset(x2, y2),
          bondPaint,
        );
      } else if (bond.bondType == 2) {
        // Double bond - draw two parallel lines
        final dx = x2 - x1;
        final dy = y2 - y1;
        final length = math.sqrt(dx * dx + dy * dy);

        // Calculate perpendicular vector
        final perpX = -dy / length * 2.0;
        final perpY = dx / length * 2.0;

        // Draw first line
        canvas.drawLine(
          Offset(x1 + perpX, y1 + perpY),
          Offset(x2 + perpX, y2 + perpY),
          bondPaint..strokeWidth = 2.0,
        );

        // Draw second line
        canvas.drawLine(
          Offset(x1 - perpX, y1 - perpY),
          Offset(x2 - perpX, y2 - perpY),
          bondPaint..strokeWidth = 2.0,
        );
      } else if (bond.bondType == 3) {
        // Triple bond - draw three parallel lines
        final dx = x2 - x1;
        final dy = y2 - y1;
        final length = math.sqrt(dx * dx + dy * dy);

        // Calculate perpendicular vector
        final perpX = -dy / length * 3.0;
        final perpY = dx / length * 3.0;

        // Draw center line
        canvas.drawLine(
          Offset(x1, y1),
          Offset(x2, y2),
          bondPaint..strokeWidth = 2.0,
        );

        // Draw first outer line
        canvas.drawLine(
          Offset(x1 + perpX, y1 + perpY),
          Offset(x2 + perpX, y2 + perpY),
          bondPaint..strokeWidth = 2.0,
        );

        // Draw second outer line
        canvas.drawLine(
          Offset(x1 - perpX, y1 - perpY),
          Offset(x2 - perpX, y2 - perpY),
          bondPaint..strokeWidth = 2.0,
        );
      }
    }

    // Draw atoms
    for (final entry in sortedAtoms) {
      final index = entry.key;
      final atom = entry.value;

      // Calculate screen position
      final x = centerX + atom.x * scaleFactor;
      final y = centerY + atom.y * scaleFactor;

      // Calculate atom radius based on z-coordinate for perspective effect
      final perspectiveFactor = 1.0 + atom.z * 0.1;
      final radius = atom.radius * scaleFactor * perspectiveFactor;

      // Draw atom
      final atomPaint = Paint()
        ..color = atom.color
        ..style = PaintingStyle.fill;
      canvas.drawCircle(Offset(x, y), radius, atomPaint);

      // Draw atom border
      final borderPaint = Paint()
        ..color = atom.color.withAlpha(200)
        ..style = PaintingStyle.stroke
        ..strokeWidth = 1.0;
      canvas.drawCircle(Offset(x, y), radius, borderPaint);

      // Draw atom label
      if (showLabels) {
        final textStyle = TextStyle(
          color: Colors.white,
          fontSize: 12 * zoom,
          fontWeight: FontWeight.bold,
        );
        final textSpan = TextSpan(
          text: atom.symbol,
          style: textStyle,
        );
        final textPainter = TextPainter(
          text: textSpan,
          textDirection: TextDirection.ltr,
        );
        textPainter.layout();
        textPainter.paint(
          canvas,
          Offset(
            x - textPainter.width / 2,
            y - textPainter.height / 2,
          ),
        );
      }
    }
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => true;
}

/// A widget that provides an interactive 3D molecular viewer
class InteractiveMolecularViewerWidget extends StatefulWidget {
  /// The data for this widget
  final Map<String, dynamic> data;

  /// Optional callback when the widget state changes
  final Function(bool isCompleted)? onStateChanged;

  const InteractiveMolecularViewerWidget({
    super.key,
    required this.data,
    this.onStateChanged,
  });

  /// Factory constructor to create the widget from JSON data
  factory InteractiveMolecularViewerWidget.fromData(
      Map<String, dynamic> data) {
    return InteractiveMolecularViewerWidget(
      data: data,
    );
  }

  @override
  State<InteractiveMolecularViewerWidget> createState() =>
      _InteractiveMolecularViewerWidgetState();
}

/// Atom model for molecular visualization
class Atom {
  final String element;
  final String symbol;
  final Color color;
  final double radius;
  final double x;
  final double y;
  final double z;

  Atom({
    required this.element,
    required this.symbol,
    required this.color,
    required this.radius,
    required this.x,
    required this.y,
    required this.z,
  });

  /// Create a copy of the atom with new coordinates
  Atom copyWith({double? x, double? y, double? z}) {
    return Atom(
      element: element,
      symbol: symbol,
      color: color,
      radius: radius,
      x: x ?? this.x,
      y: y ?? this.y,
      z: z ?? this.z,
    );
  }
}

/// Bond model for molecular visualization
class Bond {
  final int atom1Index;
  final int atom2Index;
  final int bondType; // 1 = single, 2 = double, 3 = triple

  Bond({
    required this.atom1Index,
    required this.atom2Index,
    this.bondType = 1,
  });
}

/// Molecule model for visualization
class Molecule {
  final String name;
  final String formula;
  final List<Atom> atoms;
  final List<Bond> bonds;
  final String description;

  Molecule({
    required this.name,
    required this.formula,
    required this.atoms,
    required this.bonds,
    required this.description,
  });

  /// Create a rotated copy of the molecule
  Molecule rotated(double angleX, double angleY, double angleZ) {
    final rotatedAtoms = atoms.map((atom) {
      // Apply rotation around X axis
      double y1 = atom.y * math.cos(angleX) - atom.z * math.sin(angleX);
      double z1 = atom.y * math.sin(angleX) + atom.z * math.cos(angleX);

      // Apply rotation around Y axis
      double x2 = atom.x * math.cos(angleY) + z1 * math.sin(angleY);
      double z2 = -atom.x * math.sin(angleY) + z1 * math.cos(angleY);

      // Apply rotation around Z axis
      double x3 = x2 * math.cos(angleZ) - y1 * math.sin(angleZ);
      double y3 = x2 * math.sin(angleZ) + y1 * math.cos(angleZ);

      return atom.copyWith(x: x3, y: y3, z: z2);
    }).toList();

    return Molecule(
      name: name,
      formula: formula,
      atoms: rotatedAtoms,
      bonds: bonds,
      description: description,
    );
  }
}

class _InteractiveMolecularViewerWidgetState
    extends State<InteractiveMolecularViewerWidget> {
  // Colors
  late Color _primaryColor;
  late Color _textColor;
  late Color _backgroundColor;

  // Molecules
  late List<Molecule> _molecules;
  late int _currentMoleculeIndex;

  // Rotation angles
  double _rotationX = 0.0;
  double _rotationY = 0.0;
  double _rotationZ = 0.0;

  // UI state
  bool _showLabels = true;
  bool _showDescription = true;
  double _zoom = 1.0;
  Offset _dragStart = Offset.zero;
  bool _isDragging = false;

  @override
  void initState() {
    super.initState();

    // Initialize colors
    _primaryColor = _getColorFromHex(
        widget.data['primaryColor'] ?? '#2196F3'); // Blue
    _textColor =
        _getColorFromHex(widget.data['textColor'] ?? '#212121'); // Dark Grey
    _backgroundColor = _getColorFromHex(
        widget.data['backgroundColor'] ?? '#FFFFFF'); // White

    // Initialize molecules
    _molecules = _createMolecules();
    _currentMoleculeIndex = 0;
  }

  // Convert hex color string to Color
  Color _getColorFromHex(String hexColor) {
    hexColor = hexColor.replaceAll('#', '');
    if (hexColor.length == 6) {
      hexColor = 'FF$hexColor';
    }
    return Color(int.parse(hexColor, radix: 16));
  }

  // Create predefined molecules
  List<Molecule> _createMolecules() {
    return [
      // Water (H2O)
      Molecule(
        name: 'Water',
        formula: 'H₂O',
        atoms: [
          Atom(
            element: 'Oxygen',
            symbol: 'O',
            color: Colors.red,
            radius: 0.6,
            x: 0,
            y: 0,
            z: 0,
          ),
          Atom(
            element: 'Hydrogen',
            symbol: 'H',
            color: Colors.white,
            radius: 0.4,
            x: 0.8,
            y: 0.6,
            z: 0,
          ),
          Atom(
            element: 'Hydrogen',
            symbol: 'H',
            color: Colors.white,
            radius: 0.4,
            x: -0.8,
            y: 0.6,
            z: 0,
          ),
        ],
        bonds: [
          Bond(atom1Index: 0, atom2Index: 1),
          Bond(atom1Index: 0, atom2Index: 2),
        ],
        description:
            'Water is a polar inorganic compound with the chemical formula H₂O. '
            'It has a bent molecular geometry due to the lone pairs on the oxygen atom.',
      ),

      // Carbon Dioxide (CO2)
      Molecule(
        name: 'Carbon Dioxide',
        formula: 'CO₂',
        atoms: [
          Atom(
            element: 'Carbon',
            symbol: 'C',
            color: Colors.grey,
            radius: 0.6,
            x: 0,
            y: 0,
            z: 0,
          ),
          Atom(
            element: 'Oxygen',
            symbol: 'O',
            color: Colors.red,
            radius: 0.6,
            x: 1.2,
            y: 0,
            z: 0,
          ),
          Atom(
            element: 'Oxygen',
            symbol: 'O',
            color: Colors.red,
            radius: 0.6,
            x: -1.2,
            y: 0,
            z: 0,
          ),
        ],
        bonds: [
          Bond(atom1Index: 0, atom2Index: 1, bondType: 2),
          Bond(atom1Index: 0, atom2Index: 2, bondType: 2),
        ],
        description:
            'Carbon dioxide is a colorless gas with the chemical formula CO₂. '
            'It has a linear molecular geometry with two double bonds between carbon and oxygen atoms.',
      ),

      // Methane (CH4)
      Molecule(
        name: 'Methane',
        formula: 'CH₄',
        atoms: [
          Atom(
            element: 'Carbon',
            symbol: 'C',
            color: Colors.grey,
            radius: 0.6,
            x: 0,
            y: 0,
            z: 0,
          ),
          Atom(
            element: 'Hydrogen',
            symbol: 'H',
            color: Colors.white,
            radius: 0.4,
            x: 0.6,
            y: 0.6,
            z: 0.6,
          ),
          Atom(
            element: 'Hydrogen',
            symbol: 'H',
            color: Colors.white,
            radius: 0.4,
            x: -0.6,
            y: 0.6,
            z: -0.6,
          ),
          Atom(
            element: 'Hydrogen',
            symbol: 'H',
            color: Colors.white,
            radius: 0.4,
            x: 0.6,
            y: -0.6,
            z: -0.6,
          ),
          Atom(
            element: 'Hydrogen',
            symbol: 'H',
            color: Colors.white,
            radius: 0.4,
            x: -0.6,
            y: -0.6,
            z: 0.6,
          ),
        ],
        bonds: [
          Bond(atom1Index: 0, atom2Index: 1),
          Bond(atom1Index: 0, atom2Index: 2),
          Bond(atom1Index: 0, atom2Index: 3),
          Bond(atom1Index: 0, atom2Index: 4),
        ],
        description:
            'Methane is a colorless gas with the chemical formula CH₄. '
            'It has a tetrahedral molecular geometry with four single bonds between carbon and hydrogen atoms.',
      ),

      // Ammonia (NH3)
      Molecule(
        name: 'Ammonia',
        formula: 'NH₃',
        atoms: [
          Atom(
            element: 'Nitrogen',
            symbol: 'N',
            color: Colors.blue,
            radius: 0.6,
            x: 0,
            y: 0,
            z: 0,
          ),
          Atom(
            element: 'Hydrogen',
            symbol: 'H',
            color: Colors.white,
            radius: 0.4,
            x: 0.6,
            y: 0.6,
            z: 0.6,
          ),
          Atom(
            element: 'Hydrogen',
            symbol: 'H',
            color: Colors.white,
            radius: 0.4,
            x: -0.6,
            y: 0.6,
            z: -0.6,
          ),
          Atom(
            element: 'Hydrogen',
            symbol: 'H',
            color: Colors.white,
            radius: 0.4,
            x: 0.6,
            y: -0.6,
            z: -0.6,
          ),
        ],
        bonds: [
          Bond(atom1Index: 0, atom2Index: 1),
          Bond(atom1Index: 0, atom2Index: 2),
          Bond(atom1Index: 0, atom2Index: 3),
        ],
        description:
            'Ammonia is a colorless gas with the chemical formula NH₃. '
            'It has a trigonal pyramidal molecular geometry due to the lone pair on the nitrogen atom.',
      ),
    ];
  }

  // Build molecule selector
  Widget _buildMoleculeSelector() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Select Molecule',
          style: TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.bold,
            color: _textColor,
          ),
        ),
        const SizedBox(height: 8),
        SingleChildScrollView(
          scrollDirection: Axis.horizontal,
          child: Row(
            children: List.generate(_molecules.length, (index) {
              final isSelected = index == _currentMoleculeIndex;
              return Padding(
                padding: const EdgeInsets.only(right: 8),
                child: ChoiceChip(
                  label: Text(_molecules[index].name),
                  selected: isSelected,
                  onSelected: (selected) {
                    if (selected) {
                      setState(() {
                        _currentMoleculeIndex = index;
                        // Reset rotation when changing molecules
                        _rotationX = 0.0;
                        _rotationY = 0.0;
                        _rotationZ = 0.0;
                      });
                    }
                  },
                  backgroundColor: Colors.grey.withAlpha(50),
                  selectedColor: _primaryColor.withAlpha(100),
                ),
              );
            }),
          ),
        ),
      ],
    );
  }

  // Build molecular viewer
  Widget _buildMolecularViewer(Molecule molecule) {
    return Container(
      height: 300,
      width: double.infinity,
      decoration: BoxDecoration(
        color: _backgroundColor,
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: Colors.grey.withAlpha(100)),
      ),
      child: GestureDetector(
        onPanStart: (details) {
          setState(() {
            _dragStart = details.localPosition;
            _isDragging = true;
          });
        },
        onPanUpdate: (details) {
          if (_isDragging) {
            final dx = details.localPosition.dx - _dragStart.dx;
            final dy = details.localPosition.dy - _dragStart.dy;

            setState(() {
              // Rotate around Y axis when dragging horizontally
              _rotationY += dx * 0.01;

              // Rotate around X axis when dragging vertically
              _rotationX += dy * 0.01;

              _dragStart = details.localPosition;
            });
          }
        },
        onPanEnd: (details) {
          setState(() {
            _isDragging = false;
          });
        },
        child: ClipRRect(
          borderRadius: BorderRadius.circular(8),
          child: CustomPaint(
            painter: MolecularPainter(
              molecule: molecule,
              showLabels: _showLabels,
              zoom: _zoom,
              primaryColor: _primaryColor,
              textColor: _textColor,
            ),
            size: const Size(double.infinity, 300),
          ),
        ),
      ),
    );
  }

  // Build controls
  Widget _buildControls() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Controls',
          style: TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.bold,
            color: _textColor,
          ),
        ),
        const SizedBox(height: 8),
        Row(
          children: [
            // Reset rotation button
            ElevatedButton.icon(
              onPressed: () {
                setState(() {
                  _rotationX = 0.0;
                  _rotationY = 0.0;
                  _rotationZ = 0.0;
                });
              },
              icon: const Icon(Icons.refresh),
              label: const Text('Reset View'),
              style: ElevatedButton.styleFrom(
                backgroundColor: _primaryColor,
                foregroundColor: Colors.white,
              ),
            ),
            const SizedBox(width: 16),
            // Toggle labels button
            OutlinedButton.icon(
              onPressed: () {
                setState(() {
                  _showLabels = !_showLabels;
                });
              },
              icon: Icon(_showLabels ? Icons.label_off : Icons.label),
              label: Text(_showLabels ? 'Hide Labels' : 'Show Labels'),
              style: OutlinedButton.styleFrom(
                foregroundColor: _primaryColor,
              ),
            ),
            const SizedBox(width: 16),
            // Toggle description button
            OutlinedButton.icon(
              onPressed: () {
                setState(() {
                  _showDescription = !_showDescription;
                });
              },
              icon: Icon(_showDescription ? Icons.visibility_off : Icons.visibility),
              label: Text(_showDescription ? 'Hide Info' : 'Show Info'),
              style: OutlinedButton.styleFrom(
                foregroundColor: _primaryColor,
              ),
            ),
          ],
        ),
        const SizedBox(height: 16),
        // Zoom slider
        Row(
          children: [
            const Text('Zoom:'),
            const SizedBox(width: 8),
            Expanded(
              child: Slider(
                value: _zoom,
                min: 0.5,
                max: 2.0,
                divisions: 15,
                label: _zoom.toStringAsFixed(1),
                onChanged: (value) {
                  setState(() {
                    _zoom = value;
                  });
                },
                activeColor: _primaryColor,
              ),
            ),
          ],
        ),
      ],
    );
  }

  // Build molecule information
  Widget _buildMoleculeInfo(Molecule molecule) {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: Colors.grey.withAlpha(20),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: Colors.grey.withAlpha(100)),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                molecule.name,
                style: TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                  color: _textColor,
                ),
              ),
              Text(
                molecule.formula,
                style: TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                  color: _primaryColor,
                ),
              ),
            ],
          ),
          const SizedBox(height: 8),
          Text(
            molecule.description,
            style: TextStyle(
              color: _textColor,
              fontSize: 14,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            'Atoms: ${molecule.atoms.length}',
            style: TextStyle(
              color: _textColor,
              fontSize: 14,
            ),
          ),
          Text(
            'Bonds: ${molecule.bonds.length}',
            style: TextStyle(
              color: _textColor,
              fontSize: 14,
            ),
          ),
          const SizedBox(height: 8),
          Align(
            alignment: Alignment.centerRight,
            child: TextButton(
              onPressed: () {
                widget.onStateChanged?.call(true);
              },
              child: const Text('Mark as Completed'),
            ),
          ),
        ],
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    final currentMolecule = _molecules[_currentMoleculeIndex].rotated(
      _rotationX,
      _rotationY,
      _rotationZ,
    );

    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
        side: BorderSide(color: _primaryColor.withAlpha(77)),
      ),
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Title and description
            Text(
              widget.data['title'] ?? 'Molecular Viewer',
              style: TextStyle(
                fontSize: 20,
                fontWeight: FontWeight.bold,
                color: _textColor,
              ),
            ),
            const SizedBox(height: 8),
            Text(
              widget.data['description'] ??
                  'Explore 3D molecular structures and their properties',
              style: TextStyle(
                fontSize: 14,
                color: _textColor.withAlpha(179),
              ),
            ),
            const SizedBox(height: 16),

            // Molecule selector
            _buildMoleculeSelector(),

            const SizedBox(height: 16),

            // Molecular viewer
            _buildMolecularViewer(currentMolecule),

            const SizedBox(height: 16),

            // Controls
            _buildControls(),

            const SizedBox(height: 16),

            // Molecule information
            if (_showDescription) _buildMoleculeInfo(currentMolecule),
          ],
        ),
      ),
    );
  }
}
