import 'package:flutter/material.dart';
import 'dart:math' as math;

/// Custom painter for visualizing measurements
class MeasurementsPainter extends CustomPainter {
  final List<double> measurements;
  final double trueValue;
  final double min;
  final double max;
  final Color primaryColor;
  final Color secondaryColor;
  final Color accentColor;

  MeasurementsPainter({
    required this.measurements,
    required this.trueValue,
    required this.min,
    required this.max,
    required this.primaryColor,
    required this.secondaryColor,
    required this.accentColor,
  });

  @override
  void paint(Canvas canvas, Size size) {
    final width = size.width;
    final height = size.height;

    // Draw horizontal grid lines
    final gridPaint = Paint()
      ..color = Colors.grey.withAlpha(30)
      ..strokeWidth = 1;

    for (int i = 0; i <= 4; i++) {
      final y = i * height / 4;
      canvas.drawLine(
        Offset(0, y),
        Offset(width, y),
        gridPaint,
      );
    }

    // Draw vertical grid lines
    for (int i = 0; i <= 4; i++) {
      final x = i * width / 4;
      canvas.drawLine(
        Offset(x, 0),
        Offset(x, height),
        gridPaint,
      );
    }

    // Draw measurements
    final measurementPaint = Paint()
      ..color = primaryColor
      ..strokeWidth = 2;

    final range = max - min;

    for (final measurement in measurements) {
      final x = (measurement - min) / range * width;

      // Draw vertical line for each measurement
      canvas.drawLine(
        Offset(x, height),
        Offset(x, height * 0.2),
        measurementPaint,
      );

      // Draw dot at the top of each line
      canvas.drawCircle(
        Offset(x, height * 0.2),
        3,
        measurementPaint,
      );
    }

    // Draw true value line
    final truePaint = Paint()
      ..color = accentColor
      ..strokeWidth = 2;

    final trueX = (trueValue - min) / range * width;

    canvas.drawLine(
      Offset(trueX, 0),
      Offset(trueX, height),
      truePaint,
    );

    // Draw mean value line
    if (measurements.isNotEmpty) {
      final meanPaint = Paint()
        ..color = secondaryColor
        ..strokeWidth = 2
        ..style = PaintingStyle.stroke;

      final mean = measurements.reduce((a, b) => a + b) / measurements.length;
      final meanX = (mean - min) / range * width;

      canvas.drawLine(
        Offset(meanX, 0),
        Offset(meanX, height),
        meanPaint,
      );
    }
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => true;
}

/// Model class for a simulation scenario
class SimulationScenario {
  final String title;
  final String description;
  final double trueValue;
  final double defaultRandomError;
  final double defaultSystematicError;
  final int defaultMeasurements;
  final String randomErrorDescription;
  final String systematicErrorDescription;
  final String explanation;
  final String unit;

  SimulationScenario({
    required this.title,
    required this.description,
    required this.trueValue,
    required this.defaultRandomError,
    required this.defaultSystematicError,
    required this.defaultMeasurements,
    required this.randomErrorDescription,
    required this.systematicErrorDescription,
    required this.explanation,
    required this.unit,
  });
}

/// A widget that simulates measurement errors and their effects on data collection
/// Users can explore different types of errors and learn how to minimize them
class InteractiveMeasurementErrorSimulatorWidget extends StatefulWidget {
  /// The data for this widget
  final Map<String, dynamic> data;

  /// Optional callback when the widget state changes
  final Function(bool isCompleted)? onStateChanged;

  const InteractiveMeasurementErrorSimulatorWidget({
    super.key,
    required this.data,
    this.onStateChanged,
  });

  /// Factory constructor to create the widget from JSON data
  factory InteractiveMeasurementErrorSimulatorWidget.fromData(Map<String, dynamic> data) {
    return InteractiveMeasurementErrorSimulatorWidget(
      data: data,
    );
  }

  @override
  State<InteractiveMeasurementErrorSimulatorWidget> createState() => _InteractiveMeasurementErrorSimulatorWidgetState();
}

class _InteractiveMeasurementErrorSimulatorWidgetState extends State<InteractiveMeasurementErrorSimulatorWidget> {
  // Simulation scenarios
  late List<SimulationScenario> _scenarios;
  late int _currentScenarioIndex;

  // Simulation parameters
  late double _randomErrorMagnitude;
  late double _systematicErrorMagnitude;
  late int _numberOfMeasurements;

  // Simulation results
  late List<double> _measurements;
  late double _trueValue;
  late double _mean;
  late double _standardDeviation;
  late double _accuracy;
  late double _precision;

  // UI state
  late bool _isCompleted;
  late bool _showExplanation;
  late bool _hasSimulated;

  // Colors
  late Color _primaryColor;
  late Color _secondaryColor;
  late Color _accentColor;
  late Color _backgroundColor;
  late Color _textColor;

  @override
  void initState() {
    super.initState();

    // Initialize colors
    _primaryColor = _getColorFromHex(widget.data['primaryColor'] ?? '#2196F3');
    _secondaryColor = _getColorFromHex(widget.data['secondaryColor'] ?? '#FF9800');
    _accentColor = _getColorFromHex(widget.data['accentColor'] ?? '#4CAF50');
    _backgroundColor = _getColorFromHex(widget.data['backgroundColor'] ?? '#FFFFFF');
    _textColor = _getColorFromHex(widget.data['textColor'] ?? '#212121');

    // Initialize scenarios
    _initializeScenarios();

    // Initialize state
    _currentScenarioIndex = 0;
    _isCompleted = false;
    _showExplanation = false;
    _hasSimulated = false;

    // Initialize simulation parameters with default values
    _resetSimulation();
  }

  // Initialize the simulation scenarios
  void _initializeScenarios() {
    final List<dynamic> scenariosData = widget.data['scenarios'] ?? _getDefaultScenarios();

    _scenarios = scenariosData.map((scenarioData) {
      return SimulationScenario(
        title: scenarioData['title'] ?? '',
        description: scenarioData['description'] ?? '',
        trueValue: scenarioData['trueValue'] ?? 0.0,
        defaultRandomError: scenarioData['defaultRandomError'] ?? 0.1,
        defaultSystematicError: scenarioData['defaultSystematicError'] ?? 0.0,
        defaultMeasurements: scenarioData['defaultMeasurements'] ?? 10,
        randomErrorDescription: scenarioData['randomErrorDescription'] ?? '',
        systematicErrorDescription: scenarioData['systematicErrorDescription'] ?? '',
        explanation: scenarioData['explanation'] ?? '',
        unit: scenarioData['unit'] ?? '',
      );
    }).toList();
  }

  // Get default scenarios if none are provided
  List<Map<String, dynamic>> _getDefaultScenarios() {
    return [
      {
        'title': 'Length Measurement',
        'description': 'Measure the length of a metal rod using a ruler.',
        'trueValue': 25.0,
        'defaultRandomError': 0.2,
        'defaultSystematicError': 0.0,
        'defaultMeasurements': 10,
        'randomErrorDescription': 'Random errors in length measurement can occur due to slight variations in how you align the ruler, read the scale, or position the object.',
        'systematicErrorDescription': 'Systematic errors in length measurement can occur if the ruler is damaged, poorly calibrated, or if there is a consistent error in how you use it (e.g., always starting from the 1cm mark instead of 0).',
        'explanation': 'Length measurements are subject to both random and systematic errors. Random errors cause measurements to scatter around the true value, while systematic errors cause a consistent offset. Increasing the number of measurements helps reduce the effect of random errors but does not affect systematic errors.',
        'unit': 'cm',
      },
      {
        'title': 'Temperature Measurement',
        'description': 'Measure the temperature of a water bath using a thermometer.',
        'trueValue': 37.0,
        'defaultRandomError': 0.3,
        'defaultSystematicError': 0.0,
        'defaultMeasurements': 10,
        'randomErrorDescription': 'Random errors in temperature measurement can occur due to fluctuations in the water bath, reading the thermometer at different angles, or momentary changes in the environment.',
        'systematicErrorDescription': 'Systematic errors in temperature measurement can occur if the thermometer is poorly calibrated, if there is a consistent heat source nearby, or if the thermometer is consistently read incorrectly.',
        'explanation': 'Temperature measurements are particularly sensitive to environmental factors. Random errors can be reduced by taking multiple measurements and averaging them. Systematic errors require recalibration of the instrument or adjustment of the measurement technique.',
        'unit': '°C',
      },
      {
        'title': 'Mass Measurement',
        'description': 'Measure the mass of a small object using a digital scale.',
        'trueValue': 15.0,
        'defaultRandomError': 0.1,
        'defaultSystematicError': 0.0,
        'defaultMeasurements': 10,
        'randomErrorDescription': 'Random errors in mass measurement can occur due to air currents, vibrations, or slight variations in how the object is placed on the scale.',
        'systematicErrorDescription': 'Systematic errors in mass measurement can occur if the scale is not properly calibrated, if there is a consistent error in taring (zeroing) the scale, or if there is a consistent external force affecting the measurements.',
        'explanation': 'Mass measurements with digital scales are generally precise but can still be affected by environmental factors. Random errors can be minimized by ensuring stable conditions and taking multiple measurements. Systematic errors require proper calibration of the scale.',
        'unit': 'g',
      },
      {
        'title': 'Time Measurement',
        'description': 'Measure the time it takes for a pendulum to complete 10 oscillations using a stopwatch.',
        'trueValue': 20.0,
        'defaultRandomError': 0.4,
        'defaultSystematicError': 0.0,
        'defaultMeasurements': 10,
        'randomErrorDescription': 'Random errors in time measurement can occur due to human reaction time variations when starting and stopping the stopwatch, or slight variations in identifying the exact moment of an oscillation.',
        'systematicErrorDescription': 'Systematic errors in time measurement can occur if there is a consistent delay in starting or stopping the stopwatch, or if the stopwatch itself runs consistently fast or slow.',
        'explanation': 'Time measurements involving human reaction time are particularly prone to random errors. These can be reduced by taking multiple measurements or using automated timing systems. Systematic errors in timing can be identified by calibrating against a known standard.',
        'unit': 's',
      },
    ];
  }

  // Get color from hex string
  Color _getColorFromHex(String hexColor) {
    hexColor = hexColor.replaceAll('#', '');
    if (hexColor.length == 6) {
      hexColor = 'FF$hexColor';
    }
    return Color(int.parse(hexColor, radix: 16));
  }

  // Reset simulation parameters to default values for the current scenario
  void _resetSimulation() {
    final scenario = _scenarios[_currentScenarioIndex];
    _randomErrorMagnitude = scenario.defaultRandomError;
    _systematicErrorMagnitude = scenario.defaultSystematicError;
    _numberOfMeasurements = scenario.defaultMeasurements;
    _trueValue = scenario.trueValue;
    _measurements = [];
    _mean = 0;
    _standardDeviation = 0;
    _accuracy = 0;
    _precision = 0;
    _hasSimulated = false;
  }

  // Run the simulation with current parameters
  void _runSimulation() {
    final random = math.Random();
    final scenario = _scenarios[_currentScenarioIndex];

    // Generate measurements with random and systematic errors
    _measurements = List.generate(_numberOfMeasurements, (index) {
      // Random error (normally distributed)
      double randomError = 0;
      for (int i = 0; i < 12; i++) {
        randomError += random.nextDouble() * 2 - 1;
      }
      randomError = (randomError / 6) * _randomErrorMagnitude; // Approximate normal distribution

      // Systematic error (constant offset)
      double systematicError = _systematicErrorMagnitude;

      // Final measurement
      return scenario.trueValue + randomError + systematicError;
    });

    // Calculate statistics
    _calculateStatistics();

    setState(() {
      _hasSimulated = true;
    });
  }

  // Calculate statistics from measurements
  void _calculateStatistics() {
    if (_measurements.isEmpty) {
      _mean = 0;
      _standardDeviation = 0;
      _accuracy = 0;
      _precision = 0;
      return;
    }

    // Calculate mean
    _mean = _measurements.reduce((a, b) => a + b) / _measurements.length;

    // Calculate standard deviation
    double sumSquaredDiff = 0;
    for (final measurement in _measurements) {
      sumSquaredDiff += math.pow(measurement - _mean, 2);
    }
    _standardDeviation = math.sqrt(sumSquaredDiff / _measurements.length);

    // Calculate accuracy (how close the mean is to the true value)
    _accuracy = 100 * (1 - (_mean - _trueValue).abs() / _trueValue);
    _accuracy = _accuracy.clamp(0, 100); // Clamp to 0-100%

    // Calculate precision (inverse of relative standard deviation)
    _precision = 100 * (1 - _standardDeviation / _mean);
    _precision = _precision.clamp(0, 100); // Clamp to 0-100%
  }

  // Go to the next scenario
  void _nextScenario() {
    if (_currentScenarioIndex < _scenarios.length - 1) {
      setState(() {
        _currentScenarioIndex++;
        _resetSimulation();
        _showExplanation = false;
      });
    } else if (!_isCompleted) {
      setState(() {
        _isCompleted = true;
      });
      widget.onStateChanged?.call(true);
    }
  }

  // Go to the previous scenario
  void _previousScenario() {
    if (_currentScenarioIndex > 0) {
      setState(() {
        _currentScenarioIndex--;
        _resetSimulation();
        _showExplanation = false;
      });
    }
  }

  // Toggle explanation visibility
  void _toggleExplanation() {
    setState(() {
      _showExplanation = !_showExplanation;
    });
  }

  // Reset the widget
  void _resetWidget() {
    setState(() {
      _currentScenarioIndex = 0;
      _resetSimulation();
      _isCompleted = false;
      _showExplanation = false;
    });
  }

  // Build the simulation results display
  Widget _buildSimulationResults(SimulationScenario scenario) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Measurements visualization
        Container(
          height: 120,
          decoration: BoxDecoration(
            border: Border.all(color: _primaryColor.withAlpha(100)),
            borderRadius: BorderRadius.circular(8),
          ),
          child: CustomPaint(
            size: const Size(double.infinity, 120),
            painter: MeasurementsPainter(
              measurements: _measurements,
              trueValue: _trueValue,
              min: _trueValue - math.max(1, _randomErrorMagnitude * 3 + _systematicErrorMagnitude.abs()),
              max: _trueValue + math.max(1, _randomErrorMagnitude * 3 + _systematicErrorMagnitude.abs()),
              primaryColor: _primaryColor,
              secondaryColor: _secondaryColor,
              accentColor: _accentColor,
            ),
          ),
        ),

        const SizedBox(height: 8),

        // Legend
        Row(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            _buildLegendItem('Measurements', _primaryColor),
            const SizedBox(width: 16),
            _buildLegendItem('True Value', _accentColor),
            const SizedBox(width: 16),
            _buildLegendItem('Mean', _secondaryColor),
          ],
        ),

        const SizedBox(height: 16),

        // Statistics
        Row(
          children: [
            Expanded(
              child: _buildStatisticCard(
                'True Value',
                '${_trueValue.toStringAsFixed(2)} ${scenario.unit}',
                _accentColor,
              ),
            ),
            const SizedBox(width: 8),
            Expanded(
              child: _buildStatisticCard(
                'Mean',
                '${_mean.toStringAsFixed(2)} ${scenario.unit}',
                _secondaryColor,
              ),
            ),
          ],
        ),

        const SizedBox(height: 8),

        Row(
          children: [
            Expanded(
              child: _buildStatisticCard(
                'Standard Deviation',
                '${_standardDeviation.toStringAsFixed(2)} ${scenario.unit}',
                _primaryColor,
              ),
            ),
            const SizedBox(width: 8),
            Expanded(
              child: _buildStatisticCard(
                'Error',
                '${(_mean - _trueValue).toStringAsFixed(2)} ${scenario.unit}',
                Colors.red,
              ),
            ),
          ],
        ),

        const SizedBox(height: 8),

        Row(
          children: [
            Expanded(
              child: _buildStatisticCard(
                'Accuracy',
                '${_accuracy.toStringAsFixed(1)}%',
                _getAccuracyColor(_accuracy),
              ),
            ),
            const SizedBox(width: 8),
            Expanded(
              child: _buildStatisticCard(
                'Precision',
                '${_precision.toStringAsFixed(1)}%',
                _getPrecisionColor(_precision),
              ),
            ),
          ],
        ),
      ],
    );
  }

  // Build a legend item
  Widget _buildLegendItem(String label, Color color) {
    return Row(
      children: [
        Container(
          width: 12,
          height: 12,
          color: color,
        ),
        const SizedBox(width: 4),
        Text(
          label,
          style: TextStyle(
            fontSize: 12,
            color: _textColor,
          ),
        ),
      ],
    );
  }

  // Build a statistic card
  Widget _buildStatisticCard(String label, String value, Color color) {
    return Container(
      padding: const EdgeInsets.all(8),
      decoration: BoxDecoration(
        color: color.withAlpha(30),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: color.withAlpha(100)),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            label,
            style: TextStyle(
              fontSize: 12,
              color: _textColor,
            ),
          ),
          Text(
            value,
            style: TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.bold,
              color: color,
            ),
          ),
        ],
      ),
    );
  }

  // Get color based on accuracy
  Color _getAccuracyColor(double accuracy) {
    if (accuracy >= 95) return Colors.green;
    if (accuracy >= 80) return Colors.lightGreen;
    if (accuracy >= 60) return Colors.amber;
    if (accuracy >= 40) return Colors.orange;
    return Colors.red;
  }

  // Get color based on precision
  Color _getPrecisionColor(double precision) {
    if (precision >= 95) return Colors.blue;
    if (precision >= 80) return Colors.lightBlue;
    if (precision >= 60) return Colors.cyan;
    if (precision >= 40) return Colors.teal;
    return Colors.red;
  }

  @override
  Widget build(BuildContext context) {
    final scenario = _scenarios[_currentScenarioIndex];

    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: _backgroundColor,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withAlpha(50),
            spreadRadius: 1,
            blurRadius: 3,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Title
          Text(
            widget.data['title'] ?? 'Measurement Error Simulator',
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
              color: _textColor,
            ),
          ),

          const SizedBox(height: 8),

          // Scenario navigation
          Row(
            children: [
              Text(
                'Scenario ${_currentScenarioIndex + 1} of ${_scenarios.length}: ${scenario.title}',
                style: TextStyle(
                  fontSize: 14,
                  fontWeight: FontWeight.bold,
                  color: _primaryColor,
                ),
              ),
              const Spacer(),
              IconButton(
                icon: Icon(_showExplanation ? Icons.info_outline : Icons.info),
                onPressed: _toggleExplanation,
                tooltip: _showExplanation ? 'Hide Explanation' : 'Show Explanation',
                color: _secondaryColor,
              ),
            ],
          ),

          const SizedBox(height: 8),

          // Scenario description
          Text(
            scenario.description,
            style: TextStyle(
              fontSize: 14,
              fontStyle: FontStyle.italic,
              color: _textColor.withAlpha(180),
            ),
          ),

          const SizedBox(height: 16),

          // Simulation parameters
          Text(
            'Simulation Parameters:',
            style: TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.bold,
              color: _primaryColor,
            ),
          ),

          const SizedBox(height: 8),

          // Random error slider
          Row(
            children: [
              Expanded(
                flex: 3,
                child: Text(
                  'Random Error:',
                  style: TextStyle(
                    fontSize: 14,
                    color: _textColor,
                  ),
                ),
              ),
              Expanded(
                flex: 7,
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Slider(
                      value: _randomErrorMagnitude,
                      min: 0,
                      max: 1,
                      divisions: 20,
                      label: _randomErrorMagnitude.toStringAsFixed(2),
                      onChanged: (value) {
                        setState(() {
                          _randomErrorMagnitude = value;
                          _hasSimulated = false;
                        });
                      },
                    ),
                    Text(
                      'Low (0.0) to High (1.0)',
                      style: TextStyle(
                        fontSize: 12,
                        color: _textColor.withAlpha(150),
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),

          // Tooltip for random error
          Padding(
            padding: const EdgeInsets.only(left: 16, bottom: 8),
            child: Text(
              scenario.randomErrorDescription,
              style: TextStyle(
                fontSize: 12,
                fontStyle: FontStyle.italic,
                color: _textColor.withAlpha(150),
              ),
            ),
          ),

          // Systematic error slider
          Row(
            children: [
              Expanded(
                flex: 3,
                child: Text(
                  'Systematic Error:',
                  style: TextStyle(
                    fontSize: 14,
                    color: _textColor,
                  ),
                ),
              ),
              Expanded(
                flex: 7,
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Slider(
                      value: _systematicErrorMagnitude,
                      min: -1,
                      max: 1,
                      divisions: 20,
                      label: _systematicErrorMagnitude.toStringAsFixed(2),
                      onChanged: (value) {
                        setState(() {
                          _systematicErrorMagnitude = value;
                          _hasSimulated = false;
                        });
                      },
                    ),
                    Text(
                      'Negative (-1.0) to Positive (1.0)',
                      style: TextStyle(
                        fontSize: 12,
                        color: _textColor.withAlpha(150),
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),

          // Tooltip for systematic error
          Padding(
            padding: const EdgeInsets.only(left: 16, bottom: 8),
            child: Text(
              scenario.systematicErrorDescription,
              style: TextStyle(
                fontSize: 12,
                fontStyle: FontStyle.italic,
                color: _textColor.withAlpha(150),
              ),
            ),
          ),

          // Number of measurements slider
          Row(
            children: [
              Expanded(
                flex: 3,
                child: Text(
                  'Number of Measurements:',
                  style: TextStyle(
                    fontSize: 14,
                    color: _textColor,
                  ),
                ),
              ),
              Expanded(
                flex: 7,
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Slider(
                      value: _numberOfMeasurements.toDouble(),
                      min: 1,
                      max: 50,
                      divisions: 49,
                      label: _numberOfMeasurements.toString(),
                      onChanged: (value) {
                        setState(() {
                          _numberOfMeasurements = value.round();
                          _hasSimulated = false;
                        });
                      },
                    ),
                    Text(
                      'Few (1) to Many (50)',
                      style: TextStyle(
                        fontSize: 12,
                        color: _textColor.withAlpha(150),
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),

          const SizedBox(height: 16),

          // Run simulation button
          Center(
            child: ElevatedButton.icon(
              onPressed: _runSimulation,
              icon: const Icon(Icons.play_arrow),
              label: const Text('Run Simulation'),
              style: ElevatedButton.styleFrom(
                backgroundColor: _accentColor,
                foregroundColor: Colors.white,
                padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
              ),
            ),
          ),

          const SizedBox(height: 16),

          // Simulation results
          if (_hasSimulated) ...[
            Text(
              'Simulation Results:',
              style: TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.bold,
                color: _primaryColor,
              ),
            ),

            const SizedBox(height: 8),

            // Results grid
            Expanded(
              child: _buildSimulationResults(scenario),
            ),
          ] else ...[
            Expanded(
              child: Center(
                child: Text(
                  'Adjust the parameters and run the simulation to see results.',
                  style: TextStyle(
                    fontSize: 14,
                    fontStyle: FontStyle.italic,
                    color: _textColor.withAlpha(150),
                  ),
                  textAlign: TextAlign.center,
                ),
              ),
            ),
          ],

          // Explanation
          if (_showExplanation)
            Container(
              padding: const EdgeInsets.all(12),
              margin: const EdgeInsets.only(bottom: 16),
              decoration: BoxDecoration(
                color: _secondaryColor.withAlpha(25),
                borderRadius: BorderRadius.circular(8),
                border: Border.all(color: _secondaryColor.withAlpha(75)),
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'Explanation:',
                    style: TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.bold,
                      color: _secondaryColor,
                    ),
                  ),
                  const SizedBox(height: 8),
                  Text(
                    scenario.explanation,
                    style: TextStyle(
                      fontSize: 14,
                      color: _textColor,
                    ),
                  ),
                ],
              ),
            ),

          // Navigation buttons
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              ElevatedButton(
                onPressed: _currentScenarioIndex > 0 ? _previousScenario : null,
                style: ElevatedButton.styleFrom(
                  backgroundColor: _secondaryColor,
                  foregroundColor: Colors.white,
                ),
                child: const Text('Previous'),
              ),
              ElevatedButton(
                onPressed: _currentScenarioIndex < _scenarios.length - 1 ? _nextScenario : (_isCompleted ? _resetWidget : _nextScenario),
                style: ElevatedButton.styleFrom(
                  backgroundColor: _primaryColor,
                  foregroundColor: Colors.white,
                ),
                child: Text(_currentScenarioIndex < _scenarios.length - 1 ? 'Next' : (_isCompleted ? 'Restart' : 'Complete')),
              ),
            ],
          ),

          // Widget name tag at the bottom
          if (widget.data['showNameTag'] ?? true)
            Padding(
              padding: const EdgeInsets.only(top: 8),
              child: Text(
                'InteractiveMeasurementErrorSimulatorWidget',
                style: TextStyle(
                  color: Colors.grey[600],
                  fontSize: 12,
                  fontStyle: FontStyle.italic,
                ),
              ),
            ),
        ],
      ),
    );
  }
}
