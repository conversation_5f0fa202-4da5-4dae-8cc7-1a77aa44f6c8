import 'package:flutter/material.dart';
import 'dart:math';
import 'dart:math' as math;

/// A comprehensive module test for two-step equations.
class InteractiveTwoStepChallengeWidget extends StatefulWidget {
  final Map<String, dynamic> data;
  final Function(bool)? onStateChanged;
  final Color primaryColor;
  final Color secondaryColor;
  final Color backgroundColor;
  final Color textColor;

  const InteractiveTwoStepChallengeWidget({
    super.key,
    this.data = const {},
    this.onStateChanged,
    this.primaryColor = Colors.blue,
    this.secondaryColor = Colors.orange,
    this.backgroundColor = Colors.white,
    this.textColor = Colors.black87,
  });

  @override
  State<InteractiveTwoStepChallengeWidget> createState() =>
      _InteractiveTwoStepChallengeWidgetState();
}

class _InteractiveTwoStepChallengeWidgetState
    extends State<InteractiveTwoStepChallengeWidget> {
  // State variables
  bool _isCompleted = false;
  int _currentQuestionIndex = 0;
  int _score = 0;
  int _totalQuestions = 10;
  bool _showResults = false;
  String? _selectedAnswer;
  String? _feedbackMessage;
  List<Map<String, dynamic>> _questions = [];

  // Timer variables
  int _timeRemaining = 900; // 15 minutes in seconds
  bool _timerActive = false;
  late DateTime _timerStartTime;

  @override
  void initState() {
    super.initState();
    _generateQuestions();
  }

  void _generateQuestions() {
    // Clear existing questions
    _questions = [];

    // Generate a mix of different question types
    Random random = Random();

    // Ensure we have at least one of each type
    _addOrderOfOperationsQuestion();
    _addLikeTermsQuestion();
    _addTwoStepEquationQuestion();
    _addFractionEquationQuestion();
    _addWordProblemQuestion();

    // Add more random questions to reach total
    while (_questions.length < _totalQuestions) {
      int questionType = random.nextInt(5);

      switch (questionType) {
        case 0:
          _addOrderOfOperationsQuestion();
          break;
        case 1:
          _addLikeTermsQuestion();
          break;
        case 2:
          _addTwoStepEquationQuestion();
          break;
        case 3:
          _addFractionEquationQuestion();
          break;
        case 4:
          _addWordProblemQuestion();
          break;
      }
    }

    // Shuffle the questions
    _questions.shuffle();
  }

  void _addOrderOfOperationsQuestion() {
    List<Map<String, dynamic>> orderOfOperationsQuestions = [
      {
        'question': 'What is the value of 2 + 3 × 4?',
        'options': ['14', '20', '11', '24'],
        'correctAnswer': '14',
        'explanation': 'Following PEMDAS, we first calculate 3 × 4 = 12, then 2 + 12 = 14.'
      },
      {
        'question': 'What is the value of 24 ÷ (3 + 3)?',
        'options': ['4', '8', '12', '2'],
        'correctAnswer': '4',
        'explanation': 'Following PEMDAS, we first calculate (3 + 3) = 6, then 24 ÷ 6 = 4.'
      },
      {
        'question': 'What is the value of 5 × (2 + 3) - 4?',
        'options': ['21', '25', '9', '16'],
        'correctAnswer': '21',
        'explanation': 'Following PEMDAS, we first calculate (2 + 3) = 5, then 5 × 5 = 25, and finally 25 - 4 = 21.'
      },
      {
        'question': 'What is the value of 18 ÷ 3 + 2 × 4?',
        'options': ['14', '10', '6', '24'],
        'correctAnswer': '14',
        'explanation': 'Following PEMDAS, we first calculate 18 ÷ 3 = 6 and 2 × 4 = 8, then 6 + 8 = 14.'
      },
    ];

    Map<String, dynamic> selectedQuestion = orderOfOperationsQuestions[math.Random().nextInt(orderOfOperationsQuestions.length)];
    selectedQuestion['type'] = 'order_of_operations';

    _questions.add(selectedQuestion);
  }

  void _addLikeTermsQuestion() {
    List<Map<String, dynamic>> likeTermsQuestions = [
      {
        'question': 'Simplify the expression: 3x + 5x - 2',
        'options': ['8x - 2', '8x + 2', '8x', '3x + 5x - 2'],
        'correctAnswer': '8x - 2',
        'explanation': 'Combining like terms: 3x + 5x - 2 = (3 + 5)x - 2 = 8x - 2'
      },
      {
        'question': 'Simplify the expression: 4y - 7 + 2y + 3',
        'options': ['6y - 4', '6y + 4', '6y - 10', '6y - 7 + 3'],
        'correctAnswer': '6y - 4',
        'explanation': 'Combining like terms: 4y - 7 + 2y + 3 = (4 + 2)y + (-7 + 3) = 6y - 4'
      },
      {
        'question': 'Simplify the expression: 5z + 3 - 2z - 8',
        'options': ['3z - 5', '3z + 5', '7z - 5', '3z - 11'],
        'correctAnswer': '3z - 5',
        'explanation': 'Combining like terms: 5z + 3 - 2z - 8 = (5 - 2)z + (3 - 8) = 3z - 5'
      },
      {
        'question': 'Simplify the expression: 2(x + 3) + 4x',
        'options': ['6x + 6', '6x + 3', '2x + 6 + 4x', '2x + 4x + 6'],
        'correctAnswer': '6x + 6',
        'explanation': 'First distribute: 2(x + 3) + 4x = 2x + 6 + 4x. Then combine like terms: 2x + 6 + 4x = (2 + 4)x + 6 = 6x + 6'
      },
    ];

    Map<String, dynamic> selectedQuestion = likeTermsQuestions[math.Random().nextInt(likeTermsQuestions.length)];
    selectedQuestion['type'] = 'like_terms';

    _questions.add(selectedQuestion);
  }

  void _addTwoStepEquationQuestion() {
    List<Map<String, dynamic>> twoStepEquationQuestions = [
      {
        'question': 'Solve for x: 3x + 5 = 20',
        'options': ['x = 5', 'x = 15', 'x = 7.5', 'x = 8'],
        'correctAnswer': 'x = 5',
        'explanation': 'Step 1: Subtract 5 from both sides: 3x = 15. Step 2: Divide both sides by 3: x = 5.'
      },
      {
        'question': 'Solve for x: 2x - 7 = 9',
        'options': ['x = 8', 'x = 1', 'x = 16', 'x = 4.5'],
        'correctAnswer': 'x = 8',
        'explanation': 'Step 1: Add 7 to both sides: 2x = 16. Step 2: Divide both sides by 2: x = 8.'
      },
      {
        'question': 'Solve for x: 5x + 10 = 3x + 20',
        'options': ['x = 5', 'x = 10', 'x = 15', 'x = 2.5'],
        'correctAnswer': 'x = 5',
        'explanation': 'Step 1: Subtract 3x from both sides: 2x + 10 = 20. Step 2: Subtract 10 from both sides: 2x = 10. Step 3: Divide both sides by 2: x = 5.'
      },
      {
        'question': 'Solve for x: 4(x + 2) = 36',
        'options': ['x = 7', 'x = 9', 'x = 8', 'x = 6'],
        'correctAnswer': 'x = 7',
        'explanation': 'Step 1: Divide both sides by 4: x + 2 = 9. Step 2: Subtract 2 from both sides: x = 7.'
      },
    ];

    Map<String, dynamic> selectedQuestion = twoStepEquationQuestions[math.Random().nextInt(twoStepEquationQuestions.length)];
    selectedQuestion['type'] = 'two_step_equation';

    _questions.add(selectedQuestion);
  }

  void _addFractionEquationQuestion() {
    List<Map<String, dynamic>> fractionEquationQuestions = [
      {
        'question': 'Solve for x: \\frac{x}{3} = 4',
        'options': ['x = 12', 'x = 7', 'x = 1.33', 'x = 4/3'],
        'correctAnswer': 'x = 12',
        'explanation': 'Multiply both sides by 3: x = 12.'
      },
      {
        'question': 'Solve for x: \\frac{2x}{5} = 8',
        'options': ['x = 20', 'x = 16', 'x = 10', 'x = 4'],
        'correctAnswer': 'x = 20',
        'explanation': 'Step 1: Multiply both sides by 5: 2x = 40. Step 2: Divide both sides by 2: x = 20.'
      },
      {
        'question': 'Solve for x: \\frac{x+2}{4} = 5',
        'options': ['x = 18', 'x = 22', 'x = 20', 'x = 3'],
        'correctAnswer': 'x = 18',
        'explanation': 'Step 1: Multiply both sides by 4: x + 2 = 20. Step 2: Subtract 2 from both sides: x = 18.'
      },
      {
        'question': 'Solve for x: \\frac{3x-1}{2} = 7',
        'options': ['x = 5', 'x = 15', 'x = 10', 'x = 21'],
        'correctAnswer': 'x = 5',
        'explanation': 'Step 1: Multiply both sides by 2: 3x - 1 = 14. Step 2: Add 1 to both sides: 3x = 15. Step 3: Divide both sides by 3: x = 5.'
      },
    ];

    Map<String, dynamic> selectedQuestion = fractionEquationQuestions[math.Random().nextInt(fractionEquationQuestions.length)];
    selectedQuestion['type'] = 'fraction_equation';

    _questions.add(selectedQuestion);
  }

  void _addWordProblemQuestion() {
    List<Map<String, dynamic>> wordProblemQuestions = [
      {
        'question': 'A number plus 7 equals 15. What is the number?',
        'options': ['8', '22', '7', '15'],
        'correctAnswer': '8',
        'explanation': 'Let x be the unknown number. Then x + 7 = 15. Subtract 7 from both sides to get x = 8.'
      },
      {
        'question': 'When 5 is subtracted from a number, the result is 12. What is the number?',
        'options': ['17', '7', '5', '12'],
        'correctAnswer': '17',
        'explanation': 'Let x be the unknown number. Then x - 5 = 12. Add 5 to both sides to get x = 17.'
      },
      {
        'question': 'Three times a number is 27. What is the number?',
        'options': ['9', '3', '27', '81'],
        'correctAnswer': '9',
        'explanation': 'Let x be the unknown number. Then 3x = 27. Divide both sides by 3 to get x = 9.'
      },
      {
        'question': 'Half of a number is 14. What is the number?',
        'options': ['28', '7', '14', '56'],
        'correctAnswer': '28',
        'explanation': 'Let x be the unknown number. Then x/2 = 14. Multiply both sides by 2 to get x = 28.'
      },
    ];

    Map<String, dynamic> selectedQuestion = wordProblemQuestions[math.Random().nextInt(wordProblemQuestions.length)];
    selectedQuestion['type'] = 'word_problem';

    _questions.add(selectedQuestion);
  }

  void _startChallenge() {
    setState(() {
      _currentQuestionIndex = 0;
      _score = 0;
      _showResults = false;
      _selectedAnswer = null;
      _feedbackMessage = null;
      _isCompleted = false;
      _timerActive = true;
      _timeRemaining = 900; // Reset to 15 minutes
      _timerStartTime = DateTime.now();
    });

    // Start timer
    _startTimer();

    // Notify parent
    widget.onStateChanged?.call(false);
  }

  void _startTimer() {
    Future.delayed(const Duration(seconds: 1), () {
      if (!mounted) return;

      if (_timerActive) {
        setState(() {
          _timeRemaining = math.max(0, _timeRemaining - 1);

          // Check if time is up
          if (_timeRemaining <= 0) {
            _endChallenge();
          }
        });

        // Continue timer
        if (_timeRemaining > 0 && _timerActive) {
          _startTimer();
        }
      }
    });
  }

  void _checkAnswer(String answer) {
    final currentQuestion = _questions[_currentQuestionIndex];
    final bool isCorrect = answer == currentQuestion['correctAnswer'];

    setState(() {
      _selectedAnswer = answer;

      if (isCorrect) {
        _score++;
        _feedbackMessage = 'Correct! ${currentQuestion['explanation']}';
      } else {
        _feedbackMessage = 'Incorrect. ${currentQuestion['explanation']}';
      }
    });

    // Move to next question after a delay
    Future.delayed(const Duration(seconds: 2), () {
      if (!mounted) return;

      setState(() {
        if (_currentQuestionIndex < _questions.length - 1) {
          _currentQuestionIndex++;
          _selectedAnswer = null;
          _feedbackMessage = null;
        } else {
          _endChallenge();
        }
      });
    });
  }

  void _endChallenge() {
    setState(() {
      _timerActive = false;
      _showResults = true;

      // Calculate completion status (pass if score >= 70%)
      _isCompleted = (_score / _questions.length) >= 0.7;
    });

    // Notify parent of completion status
    widget.onStateChanged?.call(_isCompleted);
  }

  String _formatTime(int seconds) {
    int minutes = seconds ~/ 60;
    int remainingSeconds = seconds % 60;
    return '$minutes:${remainingSeconds.toString().padLeft(2, '0')}';
  }

  void _resetWidget() {
    setState(() {
      _generateQuestions();
      _currentQuestionIndex = 0;
      _score = 0;
      _showResults = false;
      _selectedAnswer = null;
      _feedbackMessage = null;
      _isCompleted = false;
      _timerActive = false;
    });
    widget.onStateChanged?.call(false);
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: widget.backgroundColor,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withAlpha(50),
            spreadRadius: 1,
            blurRadius: 3,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: _showResults ? _buildResultsScreen() :
             _timerActive ? _buildCurrentQuestion() : _buildStartScreen(),
    );
  }

  Widget _buildStartScreen() {
    return Column(
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        Text(
          'Two-Step Equation Master Challenge',
          style: TextStyle(
            fontSize: 24,
            fontWeight: FontWeight.bold,
            color: widget.primaryColor,
          ),
        ),
        const SizedBox(height: 24),
        Container(
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            color: widget.primaryColor.withOpacity(0.1),
            borderRadius: BorderRadius.circular(12),
          ),
          child: Column(
            children: [
              Text(
                'Test your mastery of two-step equations!',
                style: TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.w500,
                  color: widget.textColor,
                ),
                textAlign: TextAlign.center,
              ),
              const SizedBox(height: 16),
              Text(
                'This challenge includes:',
                style: TextStyle(
                  fontSize: 16,
                  color: widget.textColor,
                ),
              ),
              const SizedBox(height: 8),
              _buildFeatureItem('Order of operations questions'),
              _buildFeatureItem('Like terms simplification'),
              _buildFeatureItem('Two-step equation solving'),
              _buildFeatureItem('Fraction equations'),
              _buildFeatureItem('Word problems'),
              const SizedBox(height: 16),
              Text(
                'You\'ll have 15 minutes to answer $_totalQuestions questions.',
                style: TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.w500,
                  color: widget.textColor,
                ),
                textAlign: TextAlign.center,
              ),
            ],
          ),
        ),
        const SizedBox(height: 32),
        ElevatedButton.icon(
          onPressed: _startChallenge,
          icon: const Icon(Icons.play_arrow),
          label: const Text('Start Challenge'),
          style: ElevatedButton.styleFrom(
            backgroundColor: widget.primaryColor,
            padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
          ),
        ),
      ],
    );
  }

  Widget _buildFeatureItem(String text) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4),
      child: Row(
        children: [
          Icon(
            Icons.check_circle,
            color: widget.primaryColor,
            size: 20,
          ),
          const SizedBox(width: 8),
          Expanded(
            child: Text(
              text,
              style: TextStyle(
                fontSize: 14,
                color: widget.textColor,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildCurrentQuestion() {
    if (_currentQuestionIndex >= _questions.length) {
      return Container();
    }

    final question = _questions[_currentQuestionIndex];

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Question number and timer
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Text(
              'Question ${_currentQuestionIndex + 1} of ${_questions.length}',
              style: TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.bold,
                color: widget.textColor,
              ),
            ),
            Container(
              padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
              decoration: BoxDecoration(
                color: _timeRemaining < 60 ? Colors.red : widget.primaryColor,
                borderRadius: BorderRadius.circular(16),
              ),
              child: Row(
                children: [
                  Icon(
                    Icons.timer,
                    color: Colors.white,
                    size: 18,
                  ),
                  const SizedBox(width: 4),
                  Text(
                    _formatTime(_timeRemaining),
                    style: TextStyle(
                      color: Colors.white,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),

        const SizedBox(height: 8),

        // Question type badge
        Container(
          padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
          decoration: BoxDecoration(
            color: _getQuestionTypeColor(question['type']),
            borderRadius: BorderRadius.circular(12),
          ),
          child: Text(
            _getQuestionTypeLabel(question['type']),
            style: TextStyle(
              color: Colors.white,
              fontSize: 12,
              fontWeight: FontWeight.bold,
            ),
          ),
        ),

        const SizedBox(height: 16),

        // Question text
        Container(
          width: double.infinity,
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            color: widget.primaryColor.withOpacity(0.1),
            borderRadius: BorderRadius.circular(8),
            border: Border.all(color: widget.primaryColor.withOpacity(0.3)),
          ),
          child: Text(
            question['question'],
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
              color: widget.textColor,
            ),
          ),
        ),

        const SizedBox(height: 24),

        // Answer options
        ...(question['options'] as List<String>).map<Widget>((option) {
          bool isSelected = _selectedAnswer == option;
          bool isCorrect = option == question['correctAnswer'];

          Color buttonColor = isSelected
              ? (isCorrect ? Colors.green : Colors.red)
              : widget.primaryColor;

          return Padding(
            padding: const EdgeInsets.only(bottom: 12.0),
            child: SizedBox(
              width: double.infinity,
              child: ElevatedButton(
                onPressed: _selectedAnswer == null ? () => _checkAnswer(option) : null,
                style: ElevatedButton.styleFrom(
                  backgroundColor: buttonColor,
                  padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
                  alignment: Alignment.centerLeft,
                ),
                child: Text(
                  option,
                  style: TextStyle(
                    fontSize: 16,
                    color: Colors.white,
                  ),
                ),
              ),
            ),
          );
        }).toList(),

        if (_feedbackMessage != null)
          Padding(
            padding: const EdgeInsets.only(top: 16.0),
            child: Text(
              _feedbackMessage!,
              style: TextStyle(
                color: _selectedAnswer == question['correctAnswer']
                    ? Colors.green
                    : Colors.red,
                fontWeight: FontWeight.bold,
                fontSize: 16,
              ),
            ),
          ),
      ],
    );
  }

  Color _getQuestionTypeColor(String type) {
    switch (type) {
      case 'order_of_operations':
        return Colors.purple;
      case 'like_terms':
        return Colors.teal;
      case 'two_step_equation':
        return Colors.blue;
      case 'fraction_equation':
        return Colors.orange;
      case 'word_problem':
        return Colors.green;
      default:
        return Colors.grey;
    }
  }

  String _getQuestionTypeLabel(String type) {
    switch (type) {
      case 'order_of_operations':
        return 'Order of Operations';
      case 'like_terms':
        return 'Like Terms';
      case 'two_step_equation':
        return 'Two-Step Equation';
      case 'fraction_equation':
        return 'Fraction Equation';
      case 'word_problem':
        return 'Word Problem';
      default:
        return 'Unknown';
    }
  }

  Widget _buildResultsScreen() {
    // Calculate percentage score
    final percentage = (_score / _questions.length * 100).round();
    final isPassing = percentage >= 70;

    return Column(
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        // Trophy icon for passing, or try again icon for failing
        Icon(
          isPassing ? Icons.emoji_events : Icons.refresh,
          size: 80,
          color: isPassing ? Colors.amber : Colors.grey,
        ),

        const SizedBox(height: 24),

        // Result title
        Text(
          isPassing ? 'Congratulations!' : 'Keep Practicing!',
          style: TextStyle(
            fontSize: 24,
            fontWeight: FontWeight.bold,
            color: isPassing ? widget.primaryColor : Colors.red,
          ),
        ),

        const SizedBox(height: 16),

        // Score
        Text(
          'Your Score: $_score out of ${_questions.length} ($percentage%)',
          style: TextStyle(
            fontSize: 18,
            fontWeight: FontWeight.bold,
            color: widget.textColor,
          ),
        ),

        const SizedBox(height: 8),

        // Time taken
        Text(
          'Time Taken: ${_formatTime(900 - _timeRemaining)}',
          style: TextStyle(
            fontSize: 16,
            color: widget.textColor,
          ),
        ),

        const SizedBox(height: 24),

        // Feedback message
        Text(
          isPassing
              ? 'Great job! You\'ve demonstrated a solid understanding of two-step equations.'
              : 'You need a score of at least 70% to pass. Review the concepts and try again!',
          textAlign: TextAlign.center,
          style: TextStyle(
            fontSize: 16,
            color: widget.textColor,
          ),
        ),

        const SizedBox(height: 32),

        // Try again button
        ElevatedButton.icon(
          onPressed: _resetWidget,
          icon: Icon(Icons.refresh),
          label: Text('Try Again'),
          style: ElevatedButton.styleFrom(
            backgroundColor: widget.secondaryColor,
            padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
          ),
        ),
      ],
    );
  }
}
