{"id": "exploring-the-world-of-numbers", "title": "Exploring the World of Numbers", "description": "Build a strong foundation in number operations and their visual interpretations.", "order": 5, "lessons": [{"id": "visualizing-addition-subtraction", "title": "Visualizing Addition and Subtraction", "description": "See how quantities combine and are taken away.", "order": 1, "type": "interactive_lesson", "estimatedTimeMinutes": 9, "contentBlocks": [{"id": "vas-screen1-intro", "type": "lesson_screen", "order": 1, "estimatedTimeSeconds": 30, "content": {"headline": "Numbers in Action: Adding & Subtracting!", "body_md": "Addition and subtraction are the first steps into the world of calculation. Let's see them in action, visually!", "visual": {"type": "giphy_search", "value": "plus minus signs animation"}, "interactive_element": {"type": "button", "text": "Let's Count!", "action": "next_screen"}}}, {"id": "vas-screen2-visual-addition", "type": "lesson_screen", "order": 2, "estimatedTimeSeconds": 70, "content": {"headline": "Visualizing Addition: Combining Groups", "body_md": "Addition is about combining quantities.\nIf you have 3 apples 🍎🍎🍎 and get 2 more apples 🍎🍎, how many do you have in total?", "visual": {"type": "local_asset", "value": "assets/images/numbers/addition_apples_3_plus_2.svg"}, "interactive_element": {"type": "text_input", "question_text": "3 apples + 2 apples = ?", "correct_answer_regex": "^5$", "feedback_correct": "Correct! You have 5 apples. 🍎🍎🍎🍎🍎", "feedback_incorrect": "Count all the apples together!", "action_button_text": "Subtraction Time!"}}}, {"id": "vas-screen3-visual-subtraction", "type": "lesson_screen", "order": 3, "estimatedTimeSeconds": 70, "content": {"headline": "Visualizing Subtraction: Taking Away", "body_md": "Subtraction is about taking away a quantity from another, or finding the difference.\nIf you have 7 cookies 🍪🍪🍪🍪🍪🍪🍪 and eat 3 cookies, how many are left?", "visual": {"type": "local_asset", "value": "assets/images/numbers/subtraction_cookies_7_minus_3.svg"}, "interactive_element": {"type": "text_input", "question_text": "7 cookies - 3 cookies = ?", "correct_answer_regex": "^4$", "feedback_correct": "Yum! You have 4 cookies left. 🍪🍪🍪🍪", "feedback_incorrect": "Count how many cookies are *not* eaten.", "action_button_text": "Number Lines!"}}}, {"id": "vas-screen4-number-line-addition", "type": "lesson_screen", "order": 4, "estimatedTimeSeconds": 90, "content": {"headline": "Addition on the Number Line", "body_md": "A number line can help visualize addition. Start at the first number, then 'hop' forward by the second number.\n\nExample: 2 + 4. Start at 2, hop 4 units to the right.", "visual": {"type": "interactive_number_line_jump", "operation": "addition", "start_val": 2, "hop_val": 4, "correct_end_val": 6}, "interactive_element": {"type": "text_input", "question_text": "Using a number line, what is 3 + 5?", "correct_answer_regex": "^8$", "feedback_correct": "Correct! Start at 3, hop 5 to the right, you land on 8.", "feedback_incorrect": "Start at the first number and make 'hops' to the right.", "action_button_text": "Subtract on Number Line"}}}, {"id": "vas-screen5-number-line-subtraction", "type": "lesson_screen", "order": 5, "estimatedTimeSeconds": 90, "content": {"headline": "Subtraction on the Number Line", "body_md": "For subtraction, start at the first number and 'hop' backward (left) by the second number.\n\nExample: 9 - 3. Start at 9, hop 3 units to the left.", "visual": {"type": "interactive_number_line_jump", "operation": "subtraction", "start_val": 9, "hop_val": 3, "correct_end_val": 6}, "interactive_element": {"type": "text_input", "question_text": "Using a number line, what is 7 - 4?", "correct_answer_regex": "^3$", "feedback_correct": "Correct! Start at 7, hop 4 to the left, you land on 3.", "feedback_incorrect": "Start at the first number and make 'hops' to the left.", "action_button_text": "Word Problems!"}}}, {"id": "vas-screen6-word-problems", "type": "lesson_screen", "order": 6, "estimatedTimeSeconds": 70, "content": {"headline": "Solving Word Problems", "body_md": "Addition and subtraction help solve real-world problems!\n\n\"<PERSON> has 8 balloons. She gives 2 to her friend. How many does she have left?\" \nThis is a subtraction problem: 8 - 2.", "visual": {"type": "giphy_search", "value": "kids sharing balloons"}, "interactive_element": {"type": "button", "text": "I Can Solve These!", "action": "next_screen"}}}, {"id": "vas-screen7-recap", "type": "lesson_screen", "order": 7, "estimatedTimeSeconds": 40, "content": {"headline": "Recap: Adding & Subtracting Visually", "body_md": "*   Addition combines groups or moves forward on a number line.\n*   Subtraction takes away or moves backward on a number line.\n*   Visualizing helps understand these basic operations!", "interactive_element": {"type": "button", "text": "Next Lesson", "action": "next_lesson"}}}]}, {"id": "concept-of-multiplication", "title": "The Concept of Multiplication", "description": "Understand repeated addition through arrays and diagrams.", "order": 2, "type": "interactive_lesson", "estimatedTimeMinutes": 9, "contentBlocks": [{"id": "com-screen1-intro", "type": "lesson_screen", "order": 1, "estimatedTimeSeconds": 30, "content": {"headline": "Multiplication: Fast Adding!", "body_md": "What if you need to add the same number many times? That's where multiplication comes in – it's like a super-fast way of adding!", "visual": {"type": "giphy_search", "value": "fast forward speed"}, "interactive_element": {"type": "button", "text": "Show Me the Shortcut!", "action": "next_screen"}}}, {"id": "com-screen2-repeated-addition", "type": "lesson_screen", "order": 2, "estimatedTimeSeconds": 70, "content": {"headline": "Multiplication as Repeated Addition", "body_md": "3 × 4 means adding 4 three times: 4 + 4 + 4 = 12.\nOr, it can mean adding 3 four times: 3 + 3 + 3 + 3 = 12.\n\nThe result is the same!", "visual": {"type": "local_asset", "value": "assets/images/numbers/multiplication_repeated_addition.svg"}, "interactive_element": {"type": "text_input", "question_text": "What is 5 × 2 as repeated addition?", "correct_answer_regex": "^(2\\s*\\+\\s*2\\s*\\+\\s*2\\s*\\+\\s*2\\s*\\+\\s*2|5\\s*\\+\\s*5)$", "feedback_correct": "Correct! It's 2+2+2+2+2 (which is 10) or 5+5 (which is also 10).", "feedback_incorrect": "Think of it as '5 groups of 2' or '2 groups of 5'.", "action_button_text": "Arrays!"}}}, {"id": "com-screen3-arrays", "type": "lesson_screen", "order": 3, "estimatedTimeSeconds": 80, "content": {"headline": "Visualizing with Arrays", "body_md": "An **array** is an arrangement of objects in rows and columns.\n3 × 4 can be shown as 3 rows of 4 items (or 4 columns of 3 items).\n\nExample: A chocolate bar with 3 rows and 4 squares per row has 3 × 4 = 12 squares.", "visual": {"type": "local_asset", "value": "assets/images/numbers/array_3_by_4.svg"}, "interactive_element": {"type": "multiple_choice_text", "question_text": "How many items in an array with 2 rows and 5 columns?", "options": [{"id": "com3opt1", "text": "7", "is_correct": false, "feedback_incorrect": "That would be adding. Arrays use multiplication."}, {"id": "com3opt2", "text": "10", "is_correct": true, "feedback_correct": "Correct! 2 rows × 5 columns = 10 items.", "feedback_incorrect": "Multiply rows by columns."}], "action_button_text": "Multiplication Facts"}}}, {"id": "com-screen4-multiplication-table-intro", "type": "lesson_screen", "order": 4, "estimatedTimeSeconds": 60, "content": {"headline": "The Multiplication Table: Your Friend", "body_md": "The multiplication table helps you quickly find the product of two numbers. Learning it makes many calculations much faster!", "visual": {"type": "local_asset", "value": "assets/images/numbers/multiplication_table_snippet.svg"}, "interactive_element": {"type": "button", "text": "Let's Practice!", "action": "next_screen"}}}, {"id": "com-screen5-multiplication-practice", "type": "lesson_screen", "order": 5, "estimatedTimeSeconds": 90, "content": {"headline": "Quick Multiplication Challenge!", "body_md": "Solve these multiplication facts:", "interactive_element": {"type": "multiplication_facts_game", "questions": [{"q": "6 × 7 = ?", "a": "42"}, {"q": "8 × 4 = ?", "a": "32"}, {"q": "9 × 6 = ?", "a": "54"}], "feedback_correct_all": "Great job with your multiplication facts!", "action_button_text": "Why is it useful?"}}}, {"id": "com-screen6-real-world", "type": "lesson_screen", "order": 6, "estimatedTimeSeconds": 50, "content": {"headline": "Multiplication in the Real World", "body_md": "*   Calculating total cost: 3 items × $5 each = $15 total.\n*   Finding area: Length × Width.\n*   Scaling recipes: Doubling all ingredients (multiplying by 2).", "visual": {"type": "giphy_search", "value": "shopping cart items"}, "interactive_element": {"type": "button", "text": "I See!", "action": "next_screen"}}}, {"id": "com-screen7-recap", "type": "lesson_screen", "order": 7, "estimatedTimeSeconds": 40, "content": {"headline": "Recap: The Power of Multiplication", "body_md": "*   Multiplication is repeated addition.\n*   A<PERSON>ys help visualize it.\n*   Knowing multiplication facts is super helpful!\n*   It's used for many real-world calculations.", "interactive_element": {"type": "button", "text": "Next Lesson", "action": "next_lesson"}}}]}, {"id": "sharing-equally-division", "title": "Sharing Equally: Division", "description": "Visualize how items are distributed into equal groups.", "order": 3, "type": "interactive_lesson", "estimatedTimeMinutes": 9, "contentBlocks": [{"id": "sed-screen1-intro", "type": "lesson_screen", "order": 1, "estimatedTimeSeconds": 30, "content": {"headline": "Division: Fair Shares for All!", "body_md": "How do you share 12 cookies among 3 friends so everyone gets the same amount? That's division! It's about splitting into equal groups or finding how many times one number fits into another.", "visual": {"type": "giphy_search", "value": "sharing cookies equally"}, "interactive_element": {"type": "button", "text": "Let's Divide!", "action": "next_screen"}}}, {"id": "sed-screen2-what-is-division", "type": "lesson_screen", "order": 2, "estimatedTimeSeconds": 70, "content": {"headline": "Understanding Division: Two Ways", "body_md": "1. **Sharing (Partitive Division):** Splitting a total into a known number of equal groups.\n   Example: Share 12 apples among 4 friends. How many apples per friend? (12 ÷ 4 = 3)\n2. **Grouping (Quotitive/Measurement Division):** Finding how many equal groups of a certain size fit into a total.\n   Example: You have 12 apples. How many bags of 3 apples can you make? (12 ÷ 3 = 4 bags)", "visual": {"type": "local_asset", "value": "assets/images/numbers/division_sharing_grouping.svg"}, "interactive_element": {"type": "button", "text": "Visualizing Division", "action": "next_screen"}}}, {"id": "sed-screen3-visual-sharing", "type": "lesson_screen", "order": 3, "estimatedTimeSeconds": 80, "content": {"headline": "Visualizing Sharing", "body_md": "Imagine 8 candies to be shared equally between 2 children.\n🍬🍬🍬🍬 🍬🍬🍬🍬\n\nEach child gets 4 candies. So, 8 ÷ 2 = 4.", "interactive_element": {"type": "drag_drop_sharing_game", "items_to_share": 6, "groups_to_share_among": 3, "item_image_src": "assets/icons/star_icon.svg", "feedback_correct": "Perfect sharing! Each group got 2.", "action_button_text": "Grouping View"}}}, {"id": "sed-screen4-visual-grouping", "type": "lesson_screen", "order": 4, "estimatedTimeSeconds": 80, "content": {"headline": "Visualizing Grouping", "body_md": "You have 10 balls. How many groups of 2 balls can you make?\n●● | ●● | ●● | ●● | ●●\n\nYou can make 5 groups. So, 10 ÷ 2 = 5.", "interactive_element": {"type": "grouping_items_game", "total_items": 9, "group_size": 3, "item_image_src": "assets/icons/circle_icon.svg", "feedback_correct": "Exactly! You made 3 groups of 3.", "action_button_text": "Division & Multiplication"}}}, {"id": "sed-screen5-division-multiplication-link", "type": "lesson_screen", "order": 5, "estimatedTimeSeconds": 70, "content": {"headline": "The Division-Multiplication Connection", "body_md": "Division is the inverse (opposite) of multiplication!\nIf 4 × 5 = 20, then:\n*   20 ÷ 5 = 4\n*   20 ÷ 4 = 5\n\nKnowing your multiplication facts helps a LOT with division.", "visual": {"type": "giphy_search", "value": "yin yang balance opposite"}, "interactive_element": {"type": "text_input", "question_text": "If 6 × 7 = 42, what is 42 ÷ 7?", "correct_answer_regex": "^6$", "feedback_correct": "Correct!", "feedback_incorrect": "Think: 7 times what number equals 42?", "action_button_text": "Remainders?"}}}, {"id": "sed-screen6-remainders", "type": "lesson_screen", "order": 6, "estimatedTimeSeconds": 70, "content": {"headline": "What About Leftovers? Remainders!", "body_md": "Sometimes numbers don't divide perfectly.\nThe amount left over is called the **remainder**.\n\nExample: 13 ÷ 4\n*   4 fits into 13 three times (3 × 4 = 12).\n*   There's 1 left over.\n*   So, 13 ÷ 4 = 3 with a remainder of 1 (or 3 R1).", "visual": {"type": "local_asset", "value": "assets/images/numbers/division_remainder.svg"}, "interactive_element": {"type": "button", "text": "Got it!", "action": "next_screen"}}}, {"id": "sed-screen7-recap", "type": "lesson_screen", "order": 7, "estimatedTimeSeconds": 40, "content": {"headline": "Recap: Sharing is Caring (and Calculating!)", "body_md": "*   Division is about sharing or grouping equally.\n*   It's the inverse of multiplication.\n*   Sometimes there's a remainder (leftover).\n*   Visualizing helps understand the concept!", "interactive_element": {"type": "button", "text": "Next Lesson", "action": "next_lesson"}}}]}, {"id": "understanding-place-value", "title": "Understanding Place Value", "description": "Explore the significance of digit positions using visual aids.", "order": 4, "type": "interactive_lesson", "estimatedTimeMinutes": 9, "contentBlocks": [{"id": "upv-screen1-intro", "type": "lesson_screen", "order": 1, "estimatedTimeSeconds": 30, "content": {"headline": "Digits Have Power: Place Value!", "body_md": "Why is the '2' in 25 different from the '2' in 52? It's all about place value! Each digit's position in a number gives it a specific value.", "visual": {"type": "giphy_search", "value": "numbers shifting places"}, "interactive_element": {"type": "button", "text": "Uncover the Power!", "action": "next_screen"}}}, {"id": "upv-screen2-ones-tens-hundreds", "type": "lesson_screen", "order": 2, "estimatedTimeSeconds": 70, "content": {"headline": "Ones, Tens, Hundreds", "body_md": "In a number like **345**:\n*   **5** is in the Ones place (5 × 1 = 5)\n*   **4** is in the Tens place (4 × 10 = 40)\n*   **3** is in the Hundreds place (3 × 100 = 300)\n\nSo, 345 = 300 + 40 + 5.", "visual": {"type": "local_asset", "value": "assets/images/numbers/place_value_blocks_345.svg"}, "interactive_element": {"type": "text_input", "question_text": "In the number 728, what is the value of the digit 2?", "correct_answer_regex": "^20$", "feedback_correct": "Correct! The 2 is in the tens place, so its value is 20.", "feedback_incorrect": "Think about which place the '2' is in (ones, tens, or hundreds).", "action_button_text": "Bigger Numbers!"}}}, {"id": "upv-screen3-larger-numbers", "type": "lesson_screen", "order": 3, "estimatedTimeSeconds": 80, "content": {"headline": "Beyond Hundreds: Thousands and More!", "body_md": "The pattern continues: Thousands, Ten Thousands, Hundred Thousands, Millions...\nEach place is 10 times greater than the place to its right.\n\nExample: **6,789**\n*   9 × 1 (Ones)\n*   8 × 10 (Tens)\n*   7 × 100 (Hundreds)\n*   6 × 1000 (Thousands)", "visual": {"type": "unsplash_search", "value": "large number display"}, "interactive_element": {"type": "multiple_choice_text", "question_text": "In 12,345, what place value does the '1' hold?", "options": [{"id": "upv3opt1", "text": "Thousands", "is_correct": false, "feedback_incorrect": "Count from the right: Ones, Tens, Hundreds, Thousands..."}, {"id": "upv3opt2", "text": "Ten Thousands", "is_correct": true, "feedback_correct": "Correct!", "feedback_incorrect": "The '2' is in Thousands. What's next to the left?"}], "action_button_text": "Visualizing with Blocks"}}}, {"id": "upv-screen4-base-ten-blocks-interactive", "type": "lesson_screen", "order": 4, "estimatedTimeSeconds": 100, "content": {"headline": "Building Numbers with Base-Ten Blocks", "body_md": "Base-ten blocks help visualize place value:\n*   Small cube = 1 (One)\n*   Rod = 10 (Ten)\n*   Flat = 100 (Hundred)\n*   Large cube = 1000 (Thousand)\n\nUse the blocks to build the number **234**.", "visual": {"type": "static_text", "value": "Build 234 with Base Ten Blocks"}, "interactive_element": {"type": "interactive_base_ten_blocks", "target_number": 234, "block_options": ["ones", "tens", "hundreds"]}}}, {"id": "upv-screen5-decimals-place-value", "type": "lesson_screen", "order": 5, "estimatedTimeSeconds": 80, "content": {"headline": "Place Value with Decimals", "body_md": "To the right of the decimal point, we have:\n*   Tenths (1/10 or 0.1)\n*   Hundredths (1/100 or 0.01)\n*   Thousandths (1/1000 or 0.001)\n\nExample: **12.345**\n*   3 is in the tenths place (3 × 0.1 = 0.3)\n*   4 is in the hundredths place (4 × 0.01 = 0.04)", "visual": {"type": "local_asset", "value": "assets/images/numbers/decimal_place_value_chart.svg"}, "interactive_element": {"type": "text_input", "question_text": "In 6.78, what is the value of the digit 8?", "correct_answer_regex": "^(0.08|8/100)$", "feedback_correct": "Correct! It's 8 hundredths.", "feedback_incorrect": "It's two places to the right of the decimal.", "action_button_text": "Why is it important?"}}}, {"id": "upv-screen6-importance", "type": "lesson_screen", "order": 6, "estimatedTimeSeconds": 50, "content": {"headline": "Why Place Value Matters", "body_md": "Understanding place value is crucial for:\n*   Reading and writing large numbers.\n*   Performing addition, subtraction, multiplication, and division correctly.\n*   Understanding decimals and money.", "visual": {"type": "giphy_search", "value": "money counting coins"}, "interactive_element": {"type": "button", "text": "I Understand!", "action": "next_screen"}}}, {"id": "upv-screen7-recap", "type": "lesson_screen", "order": 7, "estimatedTimeSeconds": 40, "content": {"headline": "Recap: The Power of Position", "body_md": "*   Each digit in a number has a value based on its position.\n*   Places increase by powers of 10 to the left, and decrease by powers of 10 to the right of the decimal.\n*   Essential for all number operations!", "interactive_element": {"type": "button", "text": "Next Lesson", "action": "next_lesson"}}}]}, {"id": "comparing-ordering-numbers", "title": "Comparing and Ordering Numbers", "description": "Develop number sense through visual scales and comparisons.", "order": 5, "type": "interactive_lesson", "estimatedTimeMinutes": 9, "contentBlocks": [{"id": "con-screen1-intro", "type": "lesson_screen", "order": 1, "estimatedTimeSeconds": 30, "content": {"headline": "Bigger or Smaller? Let's Compare!", "body_md": "Which number is larger, 73 or 37? How do you put a list of numbers in order? Comparing and ordering numbers is a fundamental skill!", "visual": {"type": "giphy_search", "value": "comparing sizes scale balance"}, "interactive_element": {"type": "button", "text": "Let's Get Started!", "action": "next_screen"}}}, {"id": "con-screen2-greater-less-equal", "type": "lesson_screen", "order": 2, "estimatedTimeSeconds": 60, "content": {"headline": "Symbols of Comparison: >, <, =", "body_md": "*   **>** means 'Greater Than' (e.g., 5 > 2)\n*   **<** means 'Less Than' (e.g., 3 < 7)\n*   **=** means 'Equal To' (e.g., 4 = 4)\n\nThink of the symbol as an alligator's mouth, always wanting to eat the bigger number!", "visual": {"type": "local_asset", "value": "assets/images/numbers/comparison_symbols_alligator.svg"}, "interactive_element": {"type": "multiple_choice_text", "question_text": "Which symbol makes this true: 12 ___ 21?", "options": [{"id": "con2opt1", "text": ">", "is_correct": false, "feedback_incorrect": "Does the alligator want to eat 12 or 21?"}, {"id": "con2opt2", "text": "<", "is_correct": true, "feedback_correct": "Correct! 12 is less than 21.", "feedback_incorrect": "12 is smaller than 21."}, {"id": "con2opt3", "text": "=", "is_correct": false, "feedback_incorrect": "Are 12 and 21 the same?"}], "action_button_text": "Number Line Comparison"}}}, {"id": "con-screen3-number-line-comparison", "type": "lesson_screen", "order": 3, "estimatedTimeSeconds": 70, "content": {"headline": "Comparing on the Number Line", "body_md": "On a number line, numbers to the right are always greater than numbers to the left. Use the options below to identify the true statement about the numbers shown on a number line.", "visual": {"type": "local_asset", "value": "assets/images/numbers/number_line_generic_comparison.svg"}, "interactive_element": {"type": "number_line_comparison_task", "question_text": "Which statement correctly compares numbers typically found on a number line?", "numbers_on_line": [-5, -2, 0, 3, 6], "options": [{"id": "nlc_opt1", "text": "-5 > -2", "is_correct": false, "feedback_incorrect": "Think about their positions. -2 is to the right of -5."}, {"id": "nlc_opt2", "text": "6 < 3", "is_correct": false, "feedback_incorrect": "6 is to the right of 3, so it's greater."}, {"id": "nlc_opt3", "text": "0 > -2", "is_correct": true, "feedback_correct": "Correct! 0 is to the right of -2."}, {"id": "nlc_opt4", "text": "3 < 0", "is_correct": false, "feedback_incorrect": "3 is to the right of 0."}], "action_button_text": "Check Comparison"}}}, {"id": "con-screen4-comparing-multi-digit", "type": "lesson_screen", "order": 4, "estimatedTimeSeconds": 90, "content": {"headline": "Comparing Multi-Digit Numbers", "body_md": "To compare numbers like 452 and 425:\n1. Start from the leftmost digit (largest place value).\n2. Compare digits in the same place. If they're different, the number with the larger digit is greater.\n3. If they're the same, move to the next digit to the right and repeat.\n\n452 vs 425: Hundreds are same (4). Tens: 5 > 2. So, 452 > 425.", "interactive_element": {"type": "text_input", "question_text": "Which is greater: 789 or 798? (Type the greater number)", "correct_answer_regex": "^798$", "feedback_correct": "Correct! Hundreds are same, but for tens, 9 > 8.", "feedback_incorrect": "Compare hundreds, then tens, then ones.", "action_button_text": "Ordering Numbers"}}}, {"id": "con-screen5-ordering-numbers", "type": "lesson_screen", "order": 5, "estimatedTimeSeconds": 90, "content": {"headline": "Ordering Numbers: Smallest to Largest", "body_md": "To order numbers (e.g., 34, 12, 56, 9) from smallest to largest (ascending order):\n1. Compare them pair by pair using place value.\n2. Find the smallest, then the next smallest, and so on.", "visual": {"type": "giphy_search", "value": "sorting organizing numbers"}, "interactive_element": {"type": "interactive_number_ordering_game", "numbers_to_order": [34, 12, 56, 9], "order_type": "ascending"}}}, {"id": "con-screen6-real-world", "type": "lesson_screen", "order": 6, "estimatedTimeSeconds": 50, "content": {"headline": "Comparing in Real Life", "body_md": "*   Shopping: Comparing prices to find the best deal.\n*   Sports: Ordering scores from highest to lowest.\n*   Timelines: Arranging events chronologically.", "visual": {"type": "giphy_search", "value": "shopping price comparison"}, "interactive_element": {"type": "button", "text": "I Can Compare!", "action": "next_screen"}}}, {"id": "con-screen7-recap", "type": "lesson_screen", "order": 7, "estimatedTimeSeconds": 40, "content": {"headline": "Recap: Comparing & Ordering", "body_md": "*   Symbols: >, <, =.\n*   Number line: Right is greater, left is smaller.\n*   Multi-digit: Compare place values from left to right.\n*   Ordering arranges numbers based on their value.", "interactive_element": {"type": "button", "text": "On to the Module Test!", "action": "next_lesson"}}}]}, {"id": "number-navigator-challenge-test", "title": "Number Navigator Challenge", "description": "Solve interactive puzzles involving basic operations and number comparisons.", "order": 6, "type": "module_test_interactive", "estimatedTimeMinutes": 10, "contentBlocks": [{"id": "nnc-q0-intro", "type": "question_screen", "order": 1, "estimatedTimeSeconds": 20, "content": {"headline": "Number Navigator Challenge: Set Sail!", "body_md": "Navigate these numerical puzzles using your knowledge of operations, place value, and comparisons!", "visual": {"type": "giphy_search", "value": "navigator compass map"}, "interactive_element": {"type": "button", "text": "Start Navigating!", "action": "next_screen"}}}, {"id": "nnc-q1-mixed-operations", "type": "question_screen", "order": 2, "estimatedTimeSeconds": 120, "content": {"headline": "Puzzle 1: Operation Order", "body_md": "Calculate: 5 + 3 × 4 - 10 ÷ 2\n\nRemember the order of operations (PEMDAS/BODMAS: Parentheses/Brackets, Exponents/Orders, Multiplication/Division, Addition/Subtraction).", "interactive_element": {"type": "text_input", "placeholder": "Enter your answer", "correct_answer_regex": "^12$", "feedback_correct": "Correct! 5 + (3×4) - (10÷2) = 5 + 12 - 5 = 12.", "feedback_incorrect": "Multiplication and Division come before Addition and Subtraction. Work from left to right for operations of the same precedence.", "action_button_text": "Next Puzzle"}}}, {"id": "nnc-q2-place-value-mystery", "type": "question_screen", "order": 3, "estimatedTimeSeconds": 150, "content": {"headline": "Puzzle 2: Place Value Detective", "body_md": "I am a 3-digit number.\n*   My hundreds digit is the result of 15 ÷ 5.\n*   My tens digit is the smallest prime number.\n*   My ones digit is the number of sides in a hexagon.\n\nWhat number am I?", "interactive_element": {"type": "text_input", "placeholder": "Enter the 3-digit number", "correct_answer_regex": "^326$", "feedback_correct": "You cracked it! The number is 326.", "feedback_incorrect": "Hundreds digit: 15÷5. Tens digit: smallest prime. Ones digit: sides of a hexagon.", "action_button_text": "Comparison Conundrum"}}}, {"id": "nnc-q3-comparing-decimals-fractions", "type": "question_screen", "order": 4, "estimatedTimeSeconds": 150, "content": {"headline": "Puzzle 3: Comparison Conundrum", "body_md": "Which of the following is the largest value?\n\nA) 0.75\nB) 3/5\nC) 0.089\nD) 7/10", "interactive_element": {"type": "multiple_choice_text", "options": [{"id": "nncq3optA", "text": "A) 0.75", "is_correct": true, "feedback_correct": "Correct! 0.75 is the largest. (3/5=0.6, 7/10=0.7)", "feedback_incorrect": "Convert all to decimals or common fractions to compare easily."}, {"id": "nncq3optB", "text": "B) 3/5", "is_correct": false, "feedback_incorrect": "3/5 is 0.6. Is there a larger value?"}, {"id": "nncq3optC", "text": "C) 0.089", "is_correct": false, "feedback_incorrect": "This is quite small compared to the others."}, {"id": "nncq3optD", "text": "D) 7/10", "is_correct": false, "feedback_incorrect": "7/10 is 0.7. Is there a larger value?"}], "action_button_text": "Finish Challenge"}}}, {"id": "nnc-q4-end", "type": "lesson_screen", "order": 5, "estimatedTimeSeconds": 30, "content": {"headline": "Number Navigator - Course Charted!", "body_md": "Well done, Navigator! You've expertly handled these numerical challenges. Your understanding of basic operations and number properties is strong!", "visual": {"type": "giphy_search", "value": "treasure map solved"}, "interactive_element": {"type": "button", "text": "Back to Course Overview", "action": "module_complete"}}}]}]}