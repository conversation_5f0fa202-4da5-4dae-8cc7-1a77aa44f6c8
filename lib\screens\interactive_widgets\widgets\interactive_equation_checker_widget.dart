import 'package:flutter/material.dart';
import 'dart:math' as math;

/// A widget that allows students to check if their equation solving steps are correct.
class InteractiveEquationCheckerWidget extends StatefulWidget {
  final Map<String, dynamic> data;
  final Function(bool)? onStateChanged;
  final Color primaryColor;
  final Color secondaryColor;
  final Color backgroundColor;
  final Color textColor;

  const InteractiveEquationCheckerWidget({
    super.key,
    this.data = const {},
    this.onStateChanged,
    this.primaryColor = Colors.blue,
    this.secondaryColor = Colors.orange,
    this.backgroundColor = Colors.white,
    this.textColor = Colors.black87,
  });

  @override
  State<InteractiveEquationCheckerWidget> createState() =>
      _InteractiveEquationCheckerWidgetState();
}

class _InteractiveEquationCheckerWidgetState
    extends State<InteractiveEquationCheckerWidget> {
  // State variables
  bool _isCompleted = false;
  int _currentProblemIndex = 0;
  List<EquationProblem> _problems = [];
  late EquationProblem _currentProblem;

  // User input
  List<String> _userSteps = [];
  List<bool> _stepCorrectness = [];
  String _currentStepInput = '';
  bool _showHint = false;
  String? _feedbackMessage;
  bool _allStepsCorrect = false;

  // Controllers
  final TextEditingController _stepController = TextEditingController();
  final ScrollController _scrollController = ScrollController();

  @override
  void initState() {
    super.initState();
    _initializeProblems();
    _currentProblem = _problems[_currentProblemIndex];
    _resetProblem();
  }

  @override
  void dispose() {
    _stepController.dispose();
    _scrollController.dispose();
    super.dispose();
  }

  void _initializeProblems() {
    // Check if problems are provided in the data
    if (widget.data.containsKey('problems') &&
        widget.data['problems'] is List &&
        widget.data['problems'].isNotEmpty) {

      final problemsData = widget.data['problems'] as List;
      for (final probData in problemsData) {
        if (probData is Map<String, dynamic>) {
          final problem = EquationProblem.fromJson(probData);
          _problems.add(problem);
        }
      }
    }

    // If no problems were provided, create default ones
    if (_problems.isEmpty) {
      _problems = [
        EquationProblem(
          initialEquation: 'x + 5 = 12',
          variableName: 'x',
          solution: '7',
          correctSteps: [
            'x + 5 = 12',
            'x + 5 - 5 = 12 - 5',
            'x = 7'
          ],
          hints: [
            'Start with the original equation.',
            'Subtract 5 from both sides to isolate the variable.',
            'Simplify to get the solution.'
          ],
          explanations: [
            'This is the original equation.',
            'To isolate the variable, we subtract 5 from both sides.',
            'After simplifying, we get x = 7.'
          ],
        ),
        EquationProblem(
          initialEquation: 'x - 3 = 8',
          variableName: 'x',
          solution: '11',
          correctSteps: [
            'x - 3 = 8',
            'x - 3 + 3 = 8 + 3',
            'x = 11'
          ],
          hints: [
            'Start with the original equation.',
            'Add 3 to both sides to isolate the variable.',
            'Simplify to get the solution.'
          ],
          explanations: [
            'This is the original equation.',
            'To isolate the variable, we add 3 to both sides.',
            'After simplifying, we get x = 11.'
          ],
        ),
        EquationProblem(
          initialEquation: '3x = 15',
          variableName: 'x',
          solution: '5',
          correctSteps: [
            '3x = 15',
            '3x ÷ 3 = 15 ÷ 3',
            'x = 5'
          ],
          hints: [
            'Start with the original equation.',
            'Divide both sides by 3 to isolate the variable.',
            'Simplify to get the solution.'
          ],
          explanations: [
            'This is the original equation.',
            'To isolate the variable, we divide both sides by 3.',
            'After simplifying, we get x = 5.'
          ],
        ),
        EquationProblem(
          initialEquation: 'x/4 = 5',
          variableName: 'x',
          solution: '20',
          correctSteps: [
            'x/4 = 5',
            'x/4 × 4 = 5 × 4',
            'x = 20'
          ],
          hints: [
            'Start with the original equation.',
            'Multiply both sides by 4 to isolate the variable.',
            'Simplify to get the solution.'
          ],
          explanations: [
            'This is the original equation.',
            'To isolate the variable, we multiply both sides by 4.',
            'After simplifying, we get x = 20.'
          ],
        ),
        EquationProblem(
          initialEquation: '7 = y + 2',
          variableName: 'y',
          solution: '5',
          correctSteps: [
            '7 = y + 2',
            '7 - 2 = y + 2 - 2',
            '5 = y'
          ],
          hints: [
            'Start with the original equation.',
            'Subtract 2 from both sides to isolate the variable.',
            'Simplify to get the solution.'
          ],
          explanations: [
            'This is the original equation.',
            'To isolate the variable, we subtract 2 from both sides.',
            'After simplifying, we get y = 5.'
          ],
        ),
      ];
    }
  }

  void _resetProblem() {
    setState(() {
      _userSteps = [];
      _stepCorrectness = [];
      _currentStepInput = '';
      _showHint = false;
      _feedbackMessage = null;
      _allStepsCorrect = false;
      _stepController.clear();
    });
  }

  void _addStep() {
    if (_currentStepInput.trim().isEmpty) return;

    final currentStep = _userSteps.length;
    final isCorrect = currentStep < _currentProblem.correctSteps.length &&
        _isStepEquivalent(_currentStepInput.trim(), _currentProblem.correctSteps[currentStep]);

    setState(() {
      _userSteps.add(_currentStepInput.trim());
      _stepCorrectness.add(isCorrect);
      _currentStepInput = '';
      _stepController.clear();
      _showHint = false;

      // Check if all steps are correct and complete
      if (_userSteps.length == _currentProblem.correctSteps.length) {
        _allStepsCorrect = !_stepCorrectness.contains(false);

        if (_allStepsCorrect) {
          _feedbackMessage = 'Great job! You\'ve correctly solved the equation.';
        } else {
          _feedbackMessage = 'Some steps are incorrect. Check your work and try again.';
        }
      }
    });

    // Scroll to bottom
    Future.delayed(const Duration(milliseconds: 100), () {
      if (_scrollController.hasClients) {
        _scrollController.animateTo(
          _scrollController.position.maxScrollExtent,
          duration: const Duration(milliseconds: 300),
          curve: Curves.easeOut,
        );
      }
    });
  }

  bool _isStepEquivalent(String userStep, String correctStep) {
    // Remove all whitespace and make case-insensitive
    final normalizedUserStep = userStep.replaceAll(RegExp(r'\s+'), '').toLowerCase();
    final normalizedCorrectStep = correctStep.replaceAll(RegExp(r'\s+'), '').toLowerCase();

    // Check for exact match
    if (normalizedUserStep == normalizedCorrectStep) {
      return true;
    }

    // Check for equivalent expressions (could be expanded with more sophisticated checks)
    // For example: "x = 5" is equivalent to "5 = x"
    if (normalizedUserStep.contains('=') && normalizedCorrectStep.contains('=')) {
      final userParts = normalizedUserStep.split('=');
      final correctParts = normalizedCorrectStep.split('=');

      if (userParts.length == 2 && correctParts.length == 2) {
        // Check if the equation is the same but sides are swapped
        return (userParts[0] == correctParts[1] && userParts[1] == correctParts[0]);
      }
    }

    return false;
  }

  void _showHintForCurrentStep() {
    setState(() {
      _showHint = true;
    });
  }

  void _removeLastStep() {
    if (_userSteps.isEmpty) return;

    setState(() {
      _userSteps.removeLast();
      _stepCorrectness.removeLast();
      _feedbackMessage = null;
      _allStepsCorrect = false;
    });
  }

  void _nextProblem() {
    if (_currentProblemIndex < _problems.length - 1) {
      setState(() {
        _currentProblemIndex++;
        _currentProblem = _problems[_currentProblemIndex];
        _resetProblem();
      });
    } else {
      // All problems completed
      setState(() {
        _isCompleted = true;
      });
      widget.onStateChanged?.call(true);
    }
  }

  void _previousProblem() {
    if (_currentProblemIndex > 0) {
      setState(() {
        _currentProblemIndex--;
        _currentProblem = _problems[_currentProblemIndex];
        _resetProblem();
      });
    }
  }

  void _resetWidget() {
    setState(() {
      _currentProblemIndex = 0;
      _currentProblem = _problems[_currentProblemIndex];
      _resetProblem();
      _isCompleted = false;
    });
    widget.onStateChanged?.call(false);
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: widget.backgroundColor,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withOpacity(0.2),
            spreadRadius: 1,
            blurRadius: 3,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: _isCompleted ? _buildCompletionScreen() : _buildMainContent(),
    );
  }

  Widget _buildMainContent() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Title and progress indicator
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Text(
              'Equation Checker',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
                color: widget.primaryColor,
              ),
            ),
            Text(
              'Problem ${_currentProblemIndex + 1}/${_problems.length}',
              style: TextStyle(
                fontSize: 14,
                color: widget.textColor.withOpacity(0.7),
              ),
            ),
          ],
        ),

        const SizedBox(height: 16),

        // Current equation
        Container(
          width: double.infinity,
          padding: const EdgeInsets.symmetric(vertical: 12, horizontal: 16),
          decoration: BoxDecoration(
            color: widget.primaryColor.withOpacity(0.1),
            borderRadius: BorderRadius.circular(8),
            border: Border.all(color: widget.primaryColor.withOpacity(0.3)),
          ),
          child: Column(
            children: [
              Text(
                'Solve the equation:',
                style: TextStyle(
                  fontSize: 14,
                  color: widget.textColor.withOpacity(0.8),
                ),
              ),
              const SizedBox(height: 8),
              Text(
                _currentProblem.initialEquation,
                style: TextStyle(
                  fontSize: 24,
                  fontWeight: FontWeight.bold,
                  color: widget.textColor,
                ),
                textAlign: TextAlign.center,
              ),
              const SizedBox(height: 4),
              Text(
                'Find the value of ${_currentProblem.variableName}',
                style: TextStyle(
                  fontSize: 14,
                  fontStyle: FontStyle.italic,
                  color: widget.textColor.withOpacity(0.8),
                ),
              ),
            ],
          ),
        ),

        const SizedBox(height: 16),

        // Instructions
        Text(
          'Enter each step of your solution:',
          style: TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.w500,
            color: widget.textColor,
          ),
        ),

        const SizedBox(height: 8),

        // User steps list
        Expanded(
          child: Container(
            decoration: BoxDecoration(
              color: Colors.grey.shade50,
              borderRadius: BorderRadius.circular(8),
              border: Border.all(color: Colors.grey.shade300),
            ),
            child: _userSteps.isEmpty
                ? Center(
                    child: Text(
                      'Start by entering the original equation',
                      style: TextStyle(
                        color: widget.textColor.withOpacity(0.6),
                        fontStyle: FontStyle.italic,
                      ),
                    ),
                  )
                : ListView.builder(
                    controller: _scrollController,
                    padding: const EdgeInsets.all(8),
                    itemCount: _userSteps.length,
                    itemBuilder: (context, index) {
                      return Container(
                        margin: const EdgeInsets.only(bottom: 8),
                        padding: const EdgeInsets.all(12),
                        decoration: BoxDecoration(
                          color: _stepCorrectness[index]
                              ? Colors.green.withOpacity(0.1)
                              : Colors.red.withOpacity(0.1),
                          borderRadius: BorderRadius.circular(8),
                          border: Border.all(
                            color: _stepCorrectness[index]
                                ? Colors.green.withOpacity(0.3)
                                : Colors.red.withOpacity(0.3),
                          ),
                        ),
                        child: Row(
                          children: [
                            Text(
                              'Step ${index + 1}: ',
                              style: TextStyle(
                                fontWeight: FontWeight.bold,
                                color: widget.textColor,
                              ),
                            ),
                            Expanded(
                              child: Text(
                                _userSteps[index],
                                style: TextStyle(
                                  color: widget.textColor,
                                ),
                              ),
                            ),
                            Icon(
                              _stepCorrectness[index]
                                  ? Icons.check_circle
                                  : Icons.error,
                              color: _stepCorrectness[index]
                                  ? Colors.green
                                  : Colors.red,
                              size: 20,
                            ),
                          ],
                        ),
                      );
                    },
                  ),
          ),
        ),

        const SizedBox(height: 16),

        // Hint
        if (_showHint && _userSteps.length < _currentProblem.hints.length)
          Container(
            width: double.infinity,
            padding: const EdgeInsets.all(12),
            margin: const EdgeInsets.only(bottom: 16),
            decoration: BoxDecoration(
              color: widget.secondaryColor.withOpacity(0.1),
              borderRadius: BorderRadius.circular(8),
              border: Border.all(color: widget.secondaryColor.withOpacity(0.3)),
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'Hint:',
                  style: TextStyle(
                    fontWeight: FontWeight.bold,
                    color: widget.secondaryColor,
                  ),
                ),
                const SizedBox(height: 4),
                Text(
                  _currentProblem.hints[_userSteps.length],
                  style: TextStyle(
                    color: widget.textColor,
                  ),
                ),
              ],
            ),
          ),

        // Feedback message
        if (_feedbackMessage != null)
          Container(
            width: double.infinity,
            padding: const EdgeInsets.all(12),
            margin: const EdgeInsets.only(bottom: 16),
            decoration: BoxDecoration(
              color: _allStepsCorrect
                  ? Colors.green.withOpacity(0.1)
                  : Colors.red.withOpacity(0.1),
              borderRadius: BorderRadius.circular(8),
              border: Border.all(
                color: _allStepsCorrect
                    ? Colors.green.withOpacity(0.3)
                    : Colors.red.withOpacity(0.3),
              ),
            ),
            child: Text(
              _feedbackMessage!,
              style: TextStyle(
                fontWeight: FontWeight.bold,
                color: _allStepsCorrect ? Colors.green : Colors.red,
              ),
            ),
          ),

        // Input area
        Row(
          children: [
            Expanded(
              child: TextField(
                controller: _stepController,
                decoration: InputDecoration(
                  labelText: 'Enter step ${_userSteps.length + 1}',
                  hintText: 'e.g., x + 5 = 12',
                  border: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(8),
                  ),
                ),
                onChanged: (value) {
                  setState(() {
                    _currentStepInput = value;
                  });
                },
                onSubmitted: (value) {
                  if (value.isNotEmpty) {
                    _addStep();
                  }
                },
              ),
            ),
            const SizedBox(width: 8),
            IconButton(
              onPressed: _currentStepInput.isNotEmpty ? _addStep : null,
              icon: const Icon(Icons.add_circle),
              color: widget.primaryColor,
              tooltip: 'Add step',
            ),
            IconButton(
              onPressed: _userSteps.isNotEmpty ? _removeLastStep : null,
              icon: const Icon(Icons.remove_circle),
              color: Colors.red,
              tooltip: 'Remove last step',
            ),
            IconButton(
              onPressed: _showHintForCurrentStep,
              icon: const Icon(Icons.lightbulb),
              color: widget.secondaryColor,
              tooltip: 'Show hint',
            ),
          ],
        ),

        const SizedBox(height: 16),

        // Navigation buttons
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            // Previous button
            ElevatedButton(
              onPressed: _currentProblemIndex > 0 ? _previousProblem : null,
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.grey.shade200,
                foregroundColor: Colors.black87,
              ),
              child: const Text('Previous'),
            ),

            // Next button
            ElevatedButton(
              onPressed: _allStepsCorrect ? _nextProblem : null,
              style: ElevatedButton.styleFrom(
                backgroundColor: widget.primaryColor,
                foregroundColor: Colors.white,
              ),
              child: Text(_currentProblemIndex < _problems.length - 1 ? 'Next' : 'Finish'),
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildCompletionScreen() {
    return Column(
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        Icon(
          Icons.check_circle,
          size: 80,
          color: Colors.green,
        ),
        const SizedBox(height: 24),
        Text(
          'Congratulations!',
          style: TextStyle(
            fontSize: 24,
            fontWeight: FontWeight.bold,
            color: widget.primaryColor,
          ),
        ),
        const SizedBox(height: 16),
        Text(
          'You\'ve successfully solved all the equation problems!',
          textAlign: TextAlign.center,
          style: TextStyle(
            fontSize: 16,
            color: widget.textColor,
          ),
        ),
        const SizedBox(height: 32),
        ElevatedButton.icon(
          onPressed: _resetWidget,
          icon: const Icon(Icons.refresh),
          label: const Text('Practice Again'),
          style: ElevatedButton.styleFrom(
            backgroundColor: widget.secondaryColor,
            padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
          ),
        ),
      ],
    );
  }
}

/// Data class for equation problems
class EquationProblem {
  final String initialEquation;
  final String variableName;
  final String solution;
  final List<String> correctSteps;
  final List<String> hints;
  final List<String> explanations;

  EquationProblem({
    required this.initialEquation,
    required this.variableName,
    required this.solution,
    required this.correctSteps,
    required this.hints,
    required this.explanations,
  });

  factory EquationProblem.fromJson(Map<String, dynamic> json) {
    return EquationProblem(
      initialEquation: json['initialEquation'] ?? 'x + 5 = 10',
      variableName: json['variableName'] ?? 'x',
      solution: json['solution'] ?? '5',
      correctSteps: List<String>.from(json['correctSteps'] ?? ['x + 5 = 10', 'x = 5']),
      hints: List<String>.from(json['hints'] ?? ['Start with the equation', 'Solve for x']),
      explanations: List<String>.from(json['explanations'] ?? ['This is the equation', 'This is the solution']),
    );
  }
}
