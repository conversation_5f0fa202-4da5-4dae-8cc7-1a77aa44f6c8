import 'package:flutter/material.dart';
import 'dart:async';
import 'dart:math' as math;

class InteractiveShapeSequenceWidget extends StatefulWidget {
  final List<String> sequence;
  final String animationSpeed;
  final double shapeSize;
  final Map<String, Color> shapeColors;
  final bool autoPlay;
  final bool showControls;

  const InteractiveShapeSequenceWidget({
    super.key,
    required this.sequence,
    this.animationSpeed = "medium",
    this.shapeSize = 50.0,
    this.shapeColors = const {
      'circle': Colors.red,
      'square': Colors.blue,
      'triangle': Colors.green,
      'pentagon': Colors.purple,
      'hexagon': Colors.orange,
      'star': Colors.amber,
    },
    this.autoPlay = true,
    this.showControls = true,
  });

  factory InteractiveShapeSequenceWidget.fromData(Map<String, dynamic> data) {
    // Parse shape colors if provided
    Map<String, Color> colors = {
      'circle': Colors.red,
      'square': Colors.blue,
      'triangle': Colors.green,
      'pentagon': Colors.purple,
      'hexagon': Colors.orange,
      'star': Colors.amber,
    };

    if (data['shape_colors'] != null && data['shape_colors'] is Map) {
      final colorData = data['shape_colors'] as Map;
      colorData.forEach((key, value) {
        if (key is String && value is String) {
          try {
            colors[key] = Color(int.parse(value.replaceAll('#', '0xFF')));
          } catch (e) {
            // Use default color if parsing fails
          }
        }
      });
    }

    return InteractiveShapeSequenceWidget(
      sequence: List<String>.from(data['sequence'] ?? []),
      animationSpeed: data['animation_speed'] ?? "medium",
      shapeSize: data['shape_size']?.toDouble() ?? 50.0,
      shapeColors: colors,
      autoPlay: data['auto_play'] ?? true,
      showControls: data['show_controls'] ?? true,
    );
  }

  @override
  State<InteractiveShapeSequenceWidget> createState() =>
      _InteractiveShapeSequenceWidgetState();
}

class _InteractiveShapeSequenceWidgetState
    extends State<InteractiveShapeSequenceWidget>
    with SingleTickerProviderStateMixin {
  late AnimationController _controller;
  int _currentIndex = 0;
  Timer? _animationTimer;
  bool _isPlaying = false;

  @override
  void initState() {
    super.initState();

    // Set up animation controller
    _controller = AnimationController(
      vsync: this,
      duration: _getAnimationDuration(),
    );

    // Start auto-play if enabled
    if (widget.autoPlay) {
      _startAnimation();
    }
  }

  Duration _getAnimationDuration() {
    switch (widget.animationSpeed) {
      case "slow":
        return const Duration(milliseconds: 1000);
      case "fast":
        return const Duration(milliseconds: 300);
      case "medium":
      default:
        return const Duration(milliseconds: 600);
    }
  }

  void _startAnimation() {
    setState(() {
      _isPlaying = true;
      _currentIndex = 0;
    });
    _animateSequence();
  }

  void _animateSequence() {
    if (!mounted || !_isPlaying) return;

    _controller.forward(from: 0.0).then((_) {
      if (mounted && _isPlaying) {
        _animationTimer = Timer(const Duration(milliseconds: 500), () {
          if (mounted && _isPlaying) {
            setState(() {
              _currentIndex = (_currentIndex + 1) % widget.sequence.length;
            });
            _animateSequence();
          }
        });
      }
    });
  }

  void _togglePlayPause() {
    setState(() {
      _isPlaying = !_isPlaying;
      if (_isPlaying) {
        _animateSequence();
      } else {
        _animationTimer?.cancel();
      }
    });
  }

  void _restart() {
    _animationTimer?.cancel();
    setState(() {
      _currentIndex = 0;
      _isPlaying = true;
    });
    _animateSequence();
  }

  @override
  void dispose() {
    _controller.dispose();
    _animationTimer?.cancel();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      mainAxisSize: MainAxisSize.min,
      children: [
        // Shape sequence display
        Container(
          height: widget.shapeSize * 1.5,
          padding: const EdgeInsets.symmetric(vertical: 16),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: List.generate(
              widget.sequence.length,
              (index) => _buildShapeItem(index),
            ),
          ),
        ),

        // Controls
        if (widget.showControls)
          Padding(
            padding: const EdgeInsets.only(top: 8.0),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                IconButton(
                  icon: Icon(_isPlaying ? Icons.pause : Icons.play_arrow),
                  onPressed: _togglePlayPause,
                  color: Colors.blue,
                ),
                IconButton(
                  icon: const Icon(Icons.replay),
                  onPressed: _restart,
                  color: Colors.blue,
                ),
              ],
            ),
          ),
      ],
    );
  }

  Widget _buildShapeItem(int index) {
    final isHighlighted = index == _currentIndex;
    final shape = widget.sequence[index];
    final color = widget.shapeColors[shape] ?? Colors.grey;

    return AnimatedBuilder(
      animation: _controller,
      builder: (context, child) {
        final scale = isHighlighted ? 1.0 + (_controller.value * 0.3) : 1.0;

        return Container(
          margin: const EdgeInsets.symmetric(horizontal: 8),
          child: Transform.scale(
            scale: scale,
            child: Container(
              width: widget.shapeSize,
              height: widget.shapeSize,
              decoration: BoxDecoration(
                color:
                    isHighlighted ? color : color.withAlpha(76), // 0.3 opacity
                border: Border.all(
                  color: isHighlighted ? color : Colors.grey[400]!,
                  width: isHighlighted ? 2 : 1,
                ),
                shape: shape == 'circle' ? BoxShape.circle : BoxShape.rectangle,
                borderRadius:
                    shape == 'square' ? null : BorderRadius.circular(8),
              ),
              child:
                  shape == 'triangle' ||
                          shape == 'pentagon' ||
                          shape == 'hexagon' ||
                          shape == 'star'
                      ? CustomPaint(
                        painter: _ShapePainter(
                          shape: shape,
                          color:
                              isHighlighted
                                  ? color
                                  : color.withAlpha(76), // 0.3 opacity
                          borderColor:
                              isHighlighted ? color : Colors.grey[400]!,
                          borderWidth: isHighlighted ? 2 : 1,
                        ),
                      )
                      : null,
            ),
          ),
        );
      },
    );
  }
}

class _ShapePainter extends CustomPainter {
  final String shape;
  final Color color;
  final Color borderColor;
  final double borderWidth;

  _ShapePainter({
    required this.shape,
    required this.color,
    required this.borderColor,
    this.borderWidth = 1.0,
  });

  @override
  void paint(Canvas canvas, Size size) {
    final paint =
        Paint()
          ..color = color
          ..style = PaintingStyle.fill;

    final borderPaint =
        Paint()
          ..color = borderColor
          ..style = PaintingStyle.stroke
          ..strokeWidth = borderWidth;

    final center = Offset(size.width / 2, size.height / 2);
    final radius = size.width / 2 - borderWidth;

    switch (shape) {
      case 'triangle':
        _drawTriangle(canvas, center, radius, paint, borderPaint);
        break;
      case 'pentagon':
        _drawRegularPolygon(canvas, center, radius, 5, paint, borderPaint);
        break;
      case 'hexagon':
        _drawRegularPolygon(canvas, center, radius, 6, paint, borderPaint);
        break;
      case 'star':
        _drawStar(canvas, center, radius, 5, paint, borderPaint);
        break;
    }
  }

  void _drawTriangle(
    Canvas canvas,
    Offset center,
    double radius,
    Paint paint,
    Paint borderPaint,
  ) {
    final path = Path();

    // Start at top
    path.moveTo(center.dx, center.dy - radius);

    // Bottom right
    path.lineTo(center.dx + radius * 0.866, center.dy + radius * 0.5);

    // Bottom left
    path.lineTo(center.dx - radius * 0.866, center.dy + radius * 0.5);

    // Close path
    path.close();

    canvas.drawPath(path, paint);
    canvas.drawPath(path, borderPaint);
  }

  void _drawRegularPolygon(
    Canvas canvas,
    Offset center,
    double radius,
    int sides,
    Paint paint,
    Paint borderPaint,
  ) {
    final path = Path();
    final angle = (2 * math.pi) / sides;

    // Start at top
    path.moveTo(
      center.dx + radius * math.cos(0 - math.pi / 2),
      center.dy + radius * math.sin(0 - math.pi / 2),
    );

    for (int i = 1; i < sides; i++) {
      path.lineTo(
        center.dx + radius * math.cos(angle * i - math.pi / 2),
        center.dy + radius * math.sin(angle * i - math.pi / 2),
      );
    }

    path.close();

    canvas.drawPath(path, paint);
    canvas.drawPath(path, borderPaint);
  }

  void _drawStar(
    Canvas canvas,
    Offset center,
    double radius,
    int points,
    Paint paint,
    Paint borderPaint,
  ) {
    final path = Path();
    final outerRadius = radius;
    final innerRadius = radius * 0.4;
    final angle = (2 * math.pi) / (points * 2);

    // Start at top
    path.moveTo(
      center.dx + outerRadius * math.cos(0 - math.pi / 2),
      center.dy + outerRadius * math.sin(0 - math.pi / 2),
    );

    for (int i = 1; i < points * 2; i++) {
      final currentRadius = i % 2 == 0 ? outerRadius : innerRadius;
      path.lineTo(
        center.dx + currentRadius * math.cos(angle * i - math.pi / 2),
        center.dy + currentRadius * math.sin(angle * i - math.pi / 2),
      );
    }

    path.close();

    canvas.drawPath(path, paint);
    canvas.drawPath(path, borderPaint);
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => true;
}
