import 'package:flutter/material.dart';
import 'services/interactive_widget_service.dart';
import 'services/service_provider.dart';
import 'services/course_service.dart';
import 'screens/interactive_widgets/interactive_widgets_showcase.dart';

void main() async {
  // Ensure Flutter is initialized
  WidgetsFlutterBinding.ensureInitialized();

  // Initialize services
  final courseService = CourseService();
  await courseService.initialize();
  
  final interactiveWidgetService = InteractiveWidgetService();
  await interactiveWidgetService.initialize();
  
  // Print the number of widgets loaded for debugging
  debugPrint('Loaded ${interactiveWidgetService.getInteractiveWidgets().length} interactive widgets');

  // Run the app
  runApp(
    InteractiveWidgetsApp(
      courseService: courseService,
      interactiveWidgetService: interactiveWidgetService,
    ),
  );
}

class InteractiveWidgetsApp extends StatelessWidget {
  final CourseService courseService;
  final InteractiveWidgetService interactiveWidgetService;

  const InteractiveWidgetsApp({
    super.key, 
    required this.courseService,
    required this.interactiveWidgetService,
  });

  @override
  Widget build(BuildContext context) {
    return ServiceProvider(
      courseService: courseService,
      interactiveWidgetService: interactiveWidgetService,
      child: MaterialApp(
        title: 'Interactive Widgets Showcase',
        debugShowCheckedModeBanner: false,
        theme: ThemeData(
          primarySwatch: Colors.blue,
          scaffoldBackgroundColor: Colors.grey[100],
          fontFamily: 'WorkSans',
          appBarTheme: AppBarTheme(
            backgroundColor: Colors.grey[100],
            elevation: 0,
            iconTheme: IconThemeData(
              color: Colors.grey[800],
            ),
            titleTextStyle: TextStyle(
              color: Colors.grey[800],
              fontSize: 18,
              fontWeight: FontWeight.w500,
              fontFamily: 'WorkSans',
            ),
          ),
          chipTheme: ChipThemeData(
            backgroundColor: Colors.green[100],
            labelStyle: TextStyle(
              color: Colors.green[800],
              fontWeight: FontWeight.bold,
              fontFamily: 'WorkSans',
            ),
            padding: const EdgeInsets.symmetric(horizontal: 8.0, vertical: 2.0),
          ),
          useMaterial3: true,
        ),
        home: const InteractiveWidgetsShowcase(),
      ),
    );
  }
}
