{"id": "trees-and-heaps", "title": "Trees and Heaps", "description": "Dive into hierarchical tree structures, binary search trees, and efficient heap data structures.", "order": 4, "lessons": [{"id": "intro-to-trees", "title": "Introduction to Tree Structures", "description": "Understand the basic concepts of trees, their terminology, and types.", "order": 1, "type": "interactive_lesson", "estimatedTimeMinutes": 12, "contentBlocks": [{"id": "trees_screen1_what_are_trees", "type": "lesson_screen", "order": 1, "estimatedTimeSeconds": 75, "content": {"headline": "Trees: Hierarchical Data", "body_md": "A tree is a widely used hierarchical data structure that simulates a tree structure with a root value and subtrees of children with a parent node, represented as a set of linked nodes.\n\nUnlike arrays or linked lists (linear structures), trees are non-linear.\n\nThink of a family tree or an organization chart.", "visual": {"type": "giphy_search", "value": "tree structure diagram"}, "interactive_element": {"type": "button", "button_text": "Tree Terminology"}, "audio_narration_url": null}}, {"id": "trees_screen2_terminology", "type": "lesson_screen", "order": 2, "estimatedTimeSeconds": 100, "content": {"headline": "Key Tree Terminology", "body_md": "*   **Root:** The topmost node of the tree.\n*   **Node (Vertex):** An entity that contains a key or value and pointers to its child nodes.\n*   **Edge:** The link between two nodes.\n*   **Parent:** A node that has child nodes.\n*   **Child:** A node that has a parent node.\n*   **Leaf Node:** A node with no children.\n*   **Height/Depth:** Length of the longest path from root to a leaf / from root to a node.\n\nIn a family tree, what would a 'leaf node' represent?", "visual": {"type": "unsplash_search", "value": "organization chart"}, "interactive_element": {"type": "text_input", "question_text": "Leaf node in a family tree?", "placeholder_text": "e.g., A person with no children", "correct_answer_regex": ".+", "feedback_correct": "Exactly! A person with no children in that lineage."}, "audio_narration_url": null}}, {"id": "trees_screen3_binary_trees", "type": "lesson_screen", "order": 3, "estimatedTimeSeconds": 90, "content": {"headline": "Binary Trees", "body_md": "A **Binary Tree** is a special type of tree where each node can have at most two children: a left child and a right child.\n\nBinary trees are fundamental in computer science and form the basis for many other structures like Binary Search Trees and Heaps.", "visual": {"type": "giphy_search", "value": "binary code tree"}, "interactive_element": {"type": "button", "button_text": "Binary Search Trees?"}, "audio_narration_url": null}}, {"id": "trees_screen4_bst", "type": "lesson_screen", "order": 4, "estimatedTimeSeconds": 100, "content": {"headline": "Binary Search Trees (BSTs)", "body_md": "A Binary Search Tree (BST) is a binary tree with a special property:\n\n*   For any node, all values in its **left subtree** are less than the node's value.\n*   All values in its **right subtree** are greater than the node's value.\n\nThis property allows for efficient searching, insertion, and deletion (often O(log n) on average for balanced trees).", "visual": {"type": "unsplash_search", "value": "balanced tree structure"}, "interactive_element": {"type": "multiple_choice_text", "question_text": "In a BST, where would you find values larger than the root?", "options": [{"text": "In the left subtree", "is_correct": false, "feedback": "The left subtree contains smaller values."}, {"text": "In the right subtree", "is_correct": true, "feedback": "Correct! Larger values are in the right subtree."}, {"text": "Can be in either", "is_correct": false, "feedback": "The BST property strictly defines where values go."}]}, "audio_narration_url": null}}, {"id": "trees_screen5_summary", "type": "lesson_screen", "order": 5, "estimatedTimeSeconds": 60, "content": {"headline": "Tree Introduction Recap", "body_md": "*   Trees are hierarchical data structures.\n*   Binary Trees: Max two children per node.\n*   Binary Search Trees (BSTs): Ordered for efficient operations.\n\nTrees are used for representing hierarchies, efficient searching, and more.", "visual": {"type": "giphy_search", "value": "growing tree animation"}, "interactive_element": {"type": "button", "button_text": "Let's Learn About Heaps"}, "audio_narration_url": null}}]}, {"id": "introduction-to-heaps", "title": "Introduction to <PERSON><PERSON><PERSON>", "description": "Understand heap data structures, their properties (min-heap, max-heap), and applications like priority queues.", "order": 2, "type": "interactive_lesson", "estimatedTimeMinutes": 10, "contentBlocks": [{"id": "heaps_screen1_what_are_heaps", "type": "lesson_screen", "order": 1, "estimatedTimeSeconds": 75, "content": {"headline": "Heaps: Priority Matters", "body_md": "A Heap is a specialized tree-based data structure that satisfies the **heap property**.\n\n*   **Min-<PERSON>ap:** The value of each node is less than or equal to the values of its children. So, the root is the minimum value.\n*   **Max-Heap:** The value of each node is greater than or equal to the values of its children. So, the root is the maximum value.\n\nHeaps are commonly implemented as binary trees, often stored in an array for efficiency.", "visual": {"type": "giphy_search", "value": "pyramid top"}, "interactive_element": {"type": "button", "button_text": "Heap Properties?"}, "audio_narration_url": null}}, {"id": "heaps_screen2_properties", "type": "lesson_screen", "order": 2, "estimatedTimeSeconds": 90, "content": {"headline": "Key Heap Properties", "body_md": "*   **Heap Property:** Min-heap or Max-heap ordering.\n*   **Complete Binary Tree:** A binary tree where all levels are completely filled except possibly the last level, which is filled from left to right.\n\nThis completeness allows heaps to be efficiently stored in arrays. The parent/child relationships can be calculated from indices.", "visual": {"type": "unsplash_search", "value": "perfectly balanced structure"}, "interactive_element": {"type": "multiple_choice_text", "question_text": "In a Max-Heap, the root node contains:", "options": [{"text": "The smallest element.", "is_correct": false, "feedback": "That's for a Min-Heap."}, {"text": "The largest element.", "is_correct": true, "feedback": "Correct! The Max-Heap property ensures the largest element is at the root."}, {"text": "A randomly chosen element.", "is_correct": false, "feedback": "The root's value is determined by the heap property."}]}, "audio_narration_url": null}}, {"id": "heaps_screen3_operations", "type": "lesson_screen", "order": 3, "estimatedTimeSeconds": 90, "content": {"headline": "Common Heap Operations", "body_md": "*   **Insert:** Add a new element while maintaining the heap property (often involves 'bubbling up').\n*   **Extract-Min/Max:** Remove and return the root element (min or max) and restore the heap property (often involves 'sinking down').\n*   **Peek:** View the min/max element (root) without removing it.\n*   **Heapify:** Convert an arbitrary array into a heap.\n\nThese operations are typically O(log n).", "visual": {"type": "giphy_search", "value": "gears turning efficiently"}, "interactive_element": {"type": "button", "button_text": "Heap Applications?"}, "audio_narration_url": null}}, {"id": "heaps_screen4_applications", "type": "lesson_screen", "order": 4, "estimatedTimeSeconds": 75, "content": {"headline": "Applications of Heaps", "body_md": "*   **Priority Queues:** Heaps are the most common way to implement priority queues, where items are processed based on priority rather than just arrival order.\n*   **Heap Sort:** An efficient O(n log n) sorting algorithm.\n*   Graph algorithms like <PERSON>jk<PERSON>'s shortest path and <PERSON><PERSON>'s minimum spanning tree.\n\nWhen would you need a priority queue in real life?", "visual": {"type": "unsplash_search", "value": "emergency room priority"}, "interactive_element": {"type": "text_input", "question_text": "Real-life priority queue example:", "placeholder_text": "e.g., Hospital ER triage", "correct_answer_regex": ".+", "feedback_correct": "Great example! Patients are treated based on urgency (priority)."}, "audio_narration_url": null}}, {"id": "heaps_screen5_summary", "type": "lesson_screen", "order": 5, "estimatedTimeSeconds": 60, "content": {"headline": "<PERSON><PERSON><PERSON> Recap", "body_md": "*   Heaps are tree-based structures satisfying the min-heap or max-heap property.\n*   Usually complete binary trees, often stored in arrays.\n*   Efficient O(log n) insertions and extractions of min/max.\n*   Key for implementing priority queues and Heap Sort.\n\nTrees and Heaps are powerful tools for organizing and accessing data!", "visual": {"type": "giphy_search", "value": "organized tools"}, "interactive_element": {"type": "button", "button_text": "Module Test Time!"}, "audio_narration_url": null}}]}], "moduleTest": {"id": "trees-and-heaps-test", "title": "Module Test: Trees and Heaps", "description": "Test your knowledge of tree structures, BSTs, and heaps.", "type": "module_test_interactive", "estimatedTimeMinutes": 8, "contentBlocks": [{"id": "tree_heap_test_q1_bst_property", "type": "question_screen", "order": 1, "estimatedTimeSeconds": 75, "content": {"headline": "Question 1: BST Property", "body_md": "In a Binary Search Tree (BST), for any given node, where would you find all nodes with values SMALLER than that node's value?", "visual": {"type": "giphy_search", "value": "binary tree diagram"}, "interactive_element": {"type": "multiple_choice_text", "question_text": "Smaller values in a BST are in the:", "options": [{"text": "Right subtree", "is_correct": false, "feedback": "The right subtree contains larger values."}, {"text": "Left subtree", "is_correct": true, "feedback": "Correct! The BST property dictates smaller values go to the left."}, {"text": "Parent node", "is_correct": false, "feedback": "The parent node's relationship depends on whether the current node is a left or right child."}]}, "audio_narration_url": null}}, {"id": "tree_heap_test_q2_leaf_node", "type": "question_screen", "order": 2, "estimatedTimeSeconds": 60, "content": {"headline": "Question 2: Leaf Node", "body_md": "What defines a 'leaf node' in a tree structure?", "visual": {"type": "unsplash_search", "value": "single leaf on tree branch"}, "interactive_element": {"type": "multiple_choice_text", "question_text": "A leaf node is a node that:", "options": [{"text": "Is the root of the tree.", "is_correct": false, "feedback": "The root is the topmost node."}, {"text": "Has no children.", "is_correct": true, "feedback": "Correct! Leaf nodes are the terminal nodes of any path."}, {"text": "Has exactly one child.", "is_correct": false, "feedback": "A node with one child is not necessarily a leaf node."}]}, "audio_narration_url": null}}, {"id": "tree_heap_test_q3_max_heap", "type": "question_screen", "order": 3, "estimatedTimeSeconds": 75, "content": {"headline": "Question 3: <PERSON><PERSON><PERSON><PERSON>", "body_md": "In a Max-Heap, what is true about the value of any parent node relative to its children?", "visual": {"type": "giphy_search", "value": "king on throne"}, "interactive_element": {"type": "multiple_choice_text", "question_text": "In a Max-Heap, a parent's value is:", "options": [{"text": "Always less than its children's values.", "is_correct": false, "feedback": "This describes a Min-Heap."}, {"text": "Always greater than or equal to its children's values.", "is_correct": true, "feedback": "Correct! This is the Max-Heap property."}, {"text": "Unrelated to its children's values.", "is_correct": false, "feedback": "The heap property defines this relationship."}]}, "audio_narration_url": null}}, {"id": "tree_heap_test_q4_heap_application", "type": "question_screen", "order": 4, "estimatedTimeSeconds": 60, "content": {"headline": "Question 4: <PERSON><PERSON> Application", "body_md": "Heaps are commonly used to implement which abstract data type efficiently?", "visual": {"type": "unsplash_search", "value": "priority list"}, "interactive_element": {"type": "multiple_choice_text", "question_text": "Heaps are ideal for implementing:", "options": [{"text": "Stacks", "is_correct": false, "feedback": "Stacks are LIFO and can be simply implemented with arrays or linked lists."}, {"text": "Queues (standard FIFO)", "is_correct": false, "feedback": "Standard queues are FIFO; heaps are for priority-based ordering."}, {"text": "Priority Queues", "is_correct": true, "feedback": "Correct! Heaps allow efficient extraction of the highest (or lowest) priority item."}]}, "audio_narration_url": null}}]}}