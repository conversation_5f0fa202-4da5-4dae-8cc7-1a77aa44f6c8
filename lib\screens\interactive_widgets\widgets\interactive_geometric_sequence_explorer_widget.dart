import 'package:flutter/material.dart';
import 'dart:math' as math;

/// A widget that allows users to explore geometric sequences
class InteractiveGeometricSequenceExplorerWidget extends StatefulWidget {
  /// The data for this widget
  final Map<String, dynamic> data;

  /// Optional callback when the widget state changes
  final Function(bool isCompleted)? onStateChanged;

  const InteractiveGeometricSequenceExplorerWidget({
    Key? key,
    required this.data,
    this.onStateChanged,
  }) : super(key: key);

  /// Factory constructor to create the widget from JSON data
  factory InteractiveGeometricSequenceExplorerWidget.fromData(Map<String, dynamic> data) {
    return InteractiveGeometricSequenceExplorerWidget(
      data: data,
    );
  }

  @override
  State<InteractiveGeometricSequenceExplorerWidget> createState() => _InteractiveGeometricSequenceExplorerWidgetState();
}

class _InteractiveGeometricSequenceExplorerWidgetState extends State<InteractiveGeometricSequenceExplorerWidget> with SingleTickerProviderStateMixin {
  // Sequence parameters
  late double _firstTerm;
  late double _commonRatio;
  late int _numberOfTerms;
  
  // Sequence values
  late List<double> _sequenceValues;
  
  // Animation controller
  late AnimationController _animationController;
  late Animation<double> _animation;
  
  // Colors
  late Color _primaryColor;
  late Color _secondaryColor;
  late Color _tertiaryColor;
  late Color _backgroundColor;
  late Color _textColor;
  
  // Display options
  late bool _showFormula;
  late bool _showRatios;
  late bool _showGraph;
  late bool _showTable;
  late bool _useLogScale;
  
  // Sliders
  late double _minFirstTerm;
  late double _maxFirstTerm;
  late double _minCommonRatio;
  late double _maxCommonRatio;
  
  // Animation state
  bool _isAnimating = false;

  @override
  void initState() {
    super.initState();
    
    // Initialize sequence parameters
    _firstTerm = widget.data['first_term']?.toDouble() ?? 1.0;
    _commonRatio = widget.data['common_ratio']?.toDouble() ?? 2.0;
    _numberOfTerms = widget.data['number_of_terms'] ?? 10;
    
    // Initialize colors
    _primaryColor = _parseColor(widget.data['primary_color']) ?? Colors.blue;
    _secondaryColor = _parseColor(widget.data['secondary_color']) ?? Colors.orange;
    _tertiaryColor = _parseColor(widget.data['tertiary_color']) ?? Colors.green;
    _backgroundColor = _parseColor(widget.data['background_color']) ?? Colors.white;
    _textColor = _parseColor(widget.data['text_color']) ?? Colors.black87;
    
    // Initialize display options
    _showFormula = widget.data['show_formula'] ?? true;
    _showRatios = widget.data['show_ratios'] ?? true;
    _showGraph = widget.data['show_graph'] ?? true;
    _showTable = widget.data['show_table'] ?? true;
    _useLogScale = widget.data['use_log_scale'] ?? true;
    
    // Initialize slider ranges
    _minFirstTerm = widget.data['min_first_term']?.toDouble() ?? 0.1;
    _maxFirstTerm = widget.data['max_first_term']?.toDouble() ?? 10.0;
    _minCommonRatio = widget.data['min_common_ratio']?.toDouble() ?? 0.1;
    _maxCommonRatio = widget.data['max_common_ratio']?.toDouble() ?? 5.0;
    
    // Calculate sequence values
    _calculateSequence();
    
    // Initialize animation controller
    _animationController = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 1500),
    );
    
    _animation = CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeInOut,
    );
    
    // Add listener to animation controller
    _animationController.addStatusListener((status) {
      if (status == AnimationStatus.completed) {
        setState(() {
          _isAnimating = false;
        });
      }
    });
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }
  
  // Parse color from hex string
  Color? _parseColor(String? hexString) {
    if (hexString == null) return null;
    
    hexString = hexString.replaceAll('#', '');
    if (hexString.length == 6) {
      hexString = 'FF$hexString';
    }
    
    try {
      return Color(int.parse(hexString, radix: 16));
    } catch (e) {
      // Log the error or handle it as appropriate
      // For now, return null to use the default color
      return null;
    }
  }
  
  // Calculate sequence values
  void _calculateSequence() {
    _sequenceValues = List.generate(
      _numberOfTerms,
      (index) => _firstTerm * math.pow(_commonRatio, index),
    );
  }
  
  // Start animation
  void _startAnimation() {
    if (_isAnimating) return;
    
    setState(() {
      _isAnimating = true;
    });
    
    _animationController.reset();
    _animationController.forward();
  }
  
  // Toggle log scale
  void _toggleLogScale() {
    setState(() {
      _useLogScale = !_useLogScale;
    });
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: _backgroundColor,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withOpacity(0.2),
            spreadRadius: 1,
            blurRadius: 3,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Title
          Text(
            widget.data['title'] ?? 'Geometric Sequence Explorer',
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
              color: _textColor,
            ),
          ),
          
          const SizedBox(height: 8),
          
          // Description
          if (widget.data['description'] != null)
            Text(
              widget.data['description'],
              style: TextStyle(
                fontSize: 14,
                color: _textColor.withOpacity(0.8),
              ),
            ),
          
          const SizedBox(height: 16),
          
          // Formula display
          if (_showFormula)
            Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: _primaryColor.withOpacity(0.1),
                borderRadius: BorderRadius.circular(8),
                border: Border.all(color: _primaryColor),
              ),
              child: Column(
                children: [
                  Text(
                    'Geometric Sequence Formula:',
                    style: TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.bold,
                      color: _textColor,
                    ),
                  ),
                  const SizedBox(height: 8),
                  Text(
                    'a_n = a₁ × r^(n-1)',
                    style: TextStyle(
                      fontSize: 18,
                      fontWeight: FontWeight.bold,
                      color: _primaryColor,
                    ),
                  ),
                  const SizedBox(height: 8),
                  Text(
                    'Where a₁ = $_firstTerm and r = $_commonRatio',
                    style: TextStyle(
                      fontSize: 14,
                      color: _textColor.withOpacity(0.8),
                    ),
                  ),
                ],
              ),
            ),
          
          const SizedBox(height: 16),
          
          // Sliders for adjusting parameters
          Text(
            'First Term (a₁):',
            style: TextStyle(
              fontSize: 14,
              fontWeight: FontWeight.bold,
              color: _textColor,
            ),
          ),
          Row(
            children: [
              Expanded(
                child: Slider(
                  value: _firstTerm,
                  min: _minFirstTerm,
                  max: _maxFirstTerm,
                  divisions: ((_maxFirstTerm - _minFirstTerm) * 10).toInt(),
                  label: _firstTerm.toStringAsFixed(1),
                  activeColor: _primaryColor,
                  onChanged: (value) {
                    setState(() {
                      _firstTerm = value;
                      _calculateSequence();
                    });
                  },
                ),
              ),
              SizedBox(
                width: 50,
                child: Text(
                  _firstTerm.toStringAsFixed(1),
                  style: TextStyle(
                    color: _textColor,
                  ),
                ),
              ),
            ],
          ),
          
          Text(
            'Common Ratio (r):',
            style: TextStyle(
              fontSize: 14,
              fontWeight: FontWeight.bold,
              color: _textColor,
            ),
          ),
          Row(
            children: [
              Expanded(
                child: Slider(
                  value: _commonRatio,
                  min: _minCommonRatio,
                  max: _maxCommonRatio,
                  divisions: ((_maxCommonRatio - _minCommonRatio) * 10).toInt(),
                  label: _commonRatio.toStringAsFixed(1),
                  activeColor: _secondaryColor,
                  onChanged: (value) {
                    setState(() {
                      _commonRatio = value;
                      _calculateSequence();
                    });
                  },
                ),
              ),
              SizedBox(
                width: 50,
                child: Text(
                  _commonRatio.toStringAsFixed(1),
                  style: TextStyle(
                    color: _textColor,
                  ),
                ),
              ),
            ],
          ),
          
          const SizedBox(height: 16),

          Text(
            'Number of Terms:',
            style: TextStyle(
              fontSize: 14,
              fontWeight: FontWeight.bold,
              color: _textColor,
            ),
          ),
          Row(
            children: [
              Expanded(
                child: Slider(
                  value: _numberOfTerms.toDouble(),
                  min: 2,
                  max: 20,
                  divisions: 18, // (20 - 2)
                  label: _numberOfTerms.toString(),
                  activeColor: _tertiaryColor,
                  onChanged: (value) {
                    setState(() {
                      _numberOfTerms = value.toInt();
                      _calculateSequence();
                    });
                  },
                ),
              ),
              SizedBox(
                width: 50,
                child: Text(
                  _numberOfTerms.toString(),
                  style: TextStyle(
                    color: _textColor,
                  ),
                ),
              ),
            ],
          ),
          
          const SizedBox(height: 16),
          
          // Graph scale toggle
          if (_showGraph)
            Row(
              children: [
                Text(
                  'Graph Scale:',
                  style: TextStyle(
                    fontSize: 14,
                    fontWeight: FontWeight.bold,
                    color: _textColor,
                  ),
                ),
                const SizedBox(width: 8),
                ChoiceChip(
                  label: const Text('Linear'),
                  selected: !_useLogScale,
                  onSelected: (_) => _toggleLogScale(),
                ),
                const SizedBox(width: 8),
                ChoiceChip(
                  label: const Text('Logarithmic'),
                  selected: _useLogScale,
                  onSelected: (_) => _toggleLogScale(),
                ),
              ],
            ),
          
          const SizedBox(height: 8),
          
          // Sequence visualization
          if (_showGraph)
            Container(
              height: 200,
              decoration: BoxDecoration(
                color: Colors.grey.withOpacity(0.1),
                borderRadius: BorderRadius.circular(8),
              ),
              child: CustomPaint(
                painter: GeometricSequencePainter(
                  sequenceValues: _sequenceValues,
                  primaryColor: _primaryColor,
                  secondaryColor: _secondaryColor,
                  tertiaryColor: _tertiaryColor,
                  showRatios: _showRatios,
                  useLogScale: _useLogScale,
                  animationValue: _isAnimating ? _animation.value : 1.0,
                ),
                child: Container(),
              ),
            ),
          
          const SizedBox(height: 16),
          
          // Sequence table
          if (_showTable)
            Container(
              height: 150,
              decoration: BoxDecoration(
                color: Colors.grey.withOpacity(0.1),
                borderRadius: BorderRadius.circular(8),
              ),
              child: ListView(
                scrollDirection: Axis.horizontal,
                children: [
                  DataTable(
                    columns: [
                      DataColumn(
                        label: Text(
                          'n',
                          style: TextStyle(
                            fontWeight: FontWeight.bold,
                            color: _textColor,
                          ),
                        ),
                      ),
                      DataColumn(
                        label: Text(
                          'a_n',
                          style: TextStyle(
                            fontWeight: FontWeight.bold,
                            color: _textColor,
                          ),
                        ),
                      ),
                      if (_showRatios)
                        DataColumn(
                          label: Text(
                            'Ratio',
                            style: TextStyle(
                              fontWeight: FontWeight.bold,
                              color: _textColor,
                            ),
                          ),
                        ),
                    ],
                    rows: List.generate(
                      _numberOfTerms,
                      (index) => DataRow(
                        cells: [
                          DataCell(
                            Text(
                              (index + 1).toString(),
                              style: TextStyle(
                                color: _textColor,
                              ),
                            ),
                          ),
                          DataCell(
                            Text(
                              _sequenceValues[index].toStringAsFixed(1),
                              style: TextStyle(
                                color: _primaryColor,
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                          ),
                          if (_showRatios)
                            DataCell(
                              index > 0
                                  ? Text(
                                      (_sequenceValues[index] / _sequenceValues[index - 1]).toStringAsFixed(1),
                                      style: TextStyle(
                                        color: _secondaryColor,
                                      ),
                                    )
                                  : const Text('-'),
                            ),
                        ],
                      ),
                    ),
                  ),
                ],
              ),
            ),
          
          const SizedBox(height: 16),
          
          // Animation button
          Center(
            child: ElevatedButton.icon(
              onPressed: _isAnimating ? null : _startAnimation,
              icon: const Icon(Icons.play_arrow),
              label: const Text('Animate Sequence'),
              style: ElevatedButton.styleFrom(
                backgroundColor: _primaryColor,
                padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
              ),
            ),
          ),
          
          // Widget name tag
          if (widget.data['showNameTag'] ?? true)
            Padding(
              padding: const EdgeInsets.only(top: 8),
              child: Text(
                'InteractiveGeometricSequenceExplorer',
                style: TextStyle(
                  color: Colors.grey[600],
                  fontSize: 12,
                  fontStyle: FontStyle.italic,
                ),
              ),
            ),
        ],
      ),
    );
  }
}

/// Custom painter for geometric sequence visualization
class GeometricSequencePainter extends CustomPainter {
  final List<double> sequenceValues;
  final Color primaryColor;
  final Color secondaryColor;
  final Color tertiaryColor;
  final bool showRatios;
  final bool useLogScale;
  final double animationValue;
  
  GeometricSequencePainter({
    required this.sequenceValues,
    required this.primaryColor,
    required this.secondaryColor,
    required this.tertiaryColor,
    required this.showRatios,
    required this.useLogScale,
    required this.animationValue,
  });
  
  @override
  void paint(Canvas canvas, Size size) {
    // Calculate min and max values for scaling
    double minValue = sequenceValues.reduce(math.min);
    double maxValue = sequenceValues.reduce(math.max);
    
    // Add some padding
    if (useLogScale) {
      // For log scale, ensure all values are positive
      minValue = math.max(minValue, 0.1);
      minValue = math.log(minValue) / math.ln10;
      maxValue = math.log(maxValue) / math.ln10;
      
      // Add padding
      minValue = minValue - (maxValue - minValue) * 0.1;
      maxValue = maxValue + (maxValue - minValue) * 0.1;
      
      // Convert back to linear scale
      minValue = math.pow(10, minValue).toDouble();
      maxValue = math.pow(10, maxValue).toDouble();
    } else {
      // Linear scale padding
      minValue = minValue - (maxValue - minValue) * 0.1;
      maxValue = maxValue + (maxValue - minValue) * 0.1;
      
      // Ensure min is not negative if all values are positive
      if (sequenceValues.every((value) => value >= 0) && minValue < 0) {
        minValue = 0;
      }
    }
    
    // Ensure min and max are different
    if (minValue == maxValue) {
      minValue = minValue / 2;
      maxValue = maxValue * 2;
    }
    
    // Calculate visible terms based on animation
    int visibleTerms = (sequenceValues.length * animationValue).ceil();
    
    // Draw axes
    final axesPaint = Paint()
      ..color = Colors.grey
      ..style = PaintingStyle.stroke
      ..strokeWidth = 1;
    
    // X-axis
    canvas.drawLine(
      Offset(0, size.height - 30),
      Offset(size.width, size.height - 30),
      axesPaint,
    );
    
    // Y-axis
    canvas.drawLine(
      Offset(50, 0),
      Offset(50, size.height),
      axesPaint,
    );
    
    // Draw points and lines
    final pointPaint = Paint()
      ..color = primaryColor
      ..style = PaintingStyle.fill;
    
    final linePaint = Paint()
      ..color = primaryColor
      ..style = PaintingStyle.stroke
      ..strokeWidth = 2;
    
    final ratioPaint = Paint()
      ..color = secondaryColor
      ..style = PaintingStyle.stroke
      ..strokeWidth = 1
      ..strokeCap = StrokeCap.round;
    
    // Calculate x and y spacing
    final xSpacing = (size.width - 60) / (sequenceValues.length - 1);
    
    // Draw points and lines
    final points = <Offset>[];
    
    for (int i = 0; i < visibleTerms; i++) {
      final x = 50 + i * xSpacing;
      double y;
      
      if (useLogScale) {
        // Logarithmic scale
        final logValue = math.log(sequenceValues[i]) / math.ln10;
        final logMin = math.log(minValue) / math.ln10;
        final logMax = math.log(maxValue) / math.ln10;
        
        y = size.height - 30 - (logValue - logMin) / (logMax - logMin) * (size.height - 60);
      } else {
        // Linear scale
        y = size.height - 30 - (sequenceValues[i] - minValue) / (maxValue - minValue) * (size.height - 60);
      }
      
      points.add(Offset(x, y));
      
      // Draw point
      canvas.drawCircle(
        Offset(x, y),
        5,
        pointPaint,
      );
      
      // Draw term number
      final textSpan = TextSpan(
        text: (i + 1).toString(),
        style: TextStyle(
          color: Colors.grey[600],
          fontSize: 12,
        ),
      );
      
      final textPainter = TextPainter(
        text: textSpan,
        textDirection: TextDirection.ltr,
      );
      
      textPainter.layout();
      textPainter.paint(
        canvas,
        Offset(x - textPainter.width / 2, size.height - 20),
      );
      
      // Draw term value
      final valueSpan = TextSpan(
        text: sequenceValues[i].toStringAsFixed(1),
        style: TextStyle(
          color: primaryColor,
          fontSize: 12,
          fontWeight: FontWeight.bold,
        ),
      );
      
      final valuePainter = TextPainter(
        text: valueSpan,
        textDirection: TextDirection.ltr,
      );
      
      valuePainter.layout();
      valuePainter.paint(
        canvas,
        Offset(x - valuePainter.width / 2, y - 20),
      );
    }
    
    // Draw lines connecting points
    if (points.length > 1) {
      final path = Path();
      path.moveTo(points[0].dx, points[0].dy);
      
      for (int i = 1; i < points.length; i++) {
        path.lineTo(points[i].dx, points[i].dy);
      }
      
      canvas.drawPath(path, linePaint);
    }
    
    // Draw ratios
    if (showRatios && points.length > 1) {
      for (int i = 0; i < points.length - 1; i++) {
        final startPoint = points[i];
        final endPoint = points[i + 1];
        
        // Draw ratio arrow
        final midX = (startPoint.dx + endPoint.dx) / 2;
        final midY = (startPoint.dy + endPoint.dy) / 2;
        
        // Draw ratio value
        final ratio = sequenceValues[i + 1] / sequenceValues[i];
        final ratioSpan = TextSpan(
          text: ratio.toStringAsFixed(1),
          style: TextStyle(
            color: secondaryColor,
            fontSize: 12,
            fontWeight: FontWeight.bold,
          ),
        );
        
        final ratioPainter = TextPainter(
          text: ratioSpan,
          textDirection: TextDirection.ltr,
        );
        
        ratioPainter.layout();
        
        // Draw ratio indicator
        canvas.drawLine(
          Offset(startPoint.dx, startPoint.dy),
          Offset(startPoint.dx + 20, startPoint.dy - 20),
          ratioPaint,
        );
        
        canvas.drawLine(
          Offset(startPoint.dx + 20, startPoint.dy - 20),
          Offset(endPoint.dx - 20, endPoint.dy - 20),
          ratioPaint,
        );
        
        canvas.drawLine(
          Offset(endPoint.dx - 20, endPoint.dy - 20),
          Offset(endPoint.dx, endPoint.dy),
          ratioPaint,
        );
        
        ratioPainter.paint(
          canvas,
          Offset(midX - ratioPainter.width / 2, midY - 30),
        );
      }
    }
    
    // Draw scale type indicator
    final scaleSpan = TextSpan(
      text: useLogScale ? 'Logarithmic Scale' : 'Linear Scale',
      style: TextStyle(
        color: Colors.grey[600],
        fontSize: 12,
        fontStyle: FontStyle.italic,
      ),
    );
    
    final scalePainter = TextPainter(
      text: scaleSpan,
      textDirection: TextDirection.ltr,
    );
    
    scalePainter.layout();
    scalePainter.paint(
      canvas,
      Offset(size.width - scalePainter.width - 10, 10),
    );
  }
  
  @override
  bool shouldRepaint(covariant GeometricSequencePainter oldDelegate) {
    return oldDelegate.sequenceValues != sequenceValues ||
           oldDelegate.animationValue != animationValue ||
           oldDelegate.showRatios != showRatios ||
           oldDelegate.useLogScale != useLogScale;
  }
}
