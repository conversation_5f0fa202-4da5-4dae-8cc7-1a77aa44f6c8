import 'package:flutter/material.dart';
import 'dart:math' as math;

// Extension to capitalize strings
extension StringExtension on String {
  String capitalize() {
    if (this.isEmpty) return this;
    return "${this[0].toUpperCase()}${this.substring(1)}";
  }
}

/// A widget that allows users to calculate conditional probabilities
class InteractiveConditionalProbabilityCalculatorWidget extends StatefulWidget {
  /// The data for this widget
  final Map<String, dynamic> data;

  /// Optional callback when the widget state changes
  final Function(bool isCompleted)? onStateChanged;

  const InteractiveConditionalProbabilityCalculatorWidget({
    Key? key,
    required this.data,
    this.onStateChanged,
  }) : super(key: key);

  /// Factory constructor to create the widget from JSON data
  factory InteractiveConditionalProbabilityCalculatorWidget.fromData(Map<String, dynamic> data) {
    return InteractiveConditionalProbabilityCalculatorWidget(
      data: data,
    );
  }

  @override
  State<InteractiveConditionalProbabilityCalculatorWidget> createState() =>
      _InteractiveConditionalProbabilityCalculatorWidgetState();
}

class _InteractiveConditionalProbabilityCalculatorWidgetState 
    extends State<InteractiveConditionalProbabilityCalculatorWidget> {
  // Scenario parameters
  late String _selectedScenarioType;
  late List<String> _availableScenarioTypes;

  // Card scenario parameters
  int _totalCards = 52;
  int _redCards = 26;
  int _blackCards = 26;
  int _hearts = 13;
  int _diamonds = 13;
  int _clubs = 13;
  int _spades = 13;
  int _aces = 4;
  int _kings = 4;
  int _queens = 4;
  int _jacks = 4;
  int _faceCards = 12;
  int _numberCards = 36;

  // Dice scenario parameters
  int _numDice = 2;
  int _diceSum = 7;
  int _diceValue = 6;

  // Marble scenario parameters
  int _redMarbles = 5;
  int _blueMarbles = 7;
  int _greenMarbles = 3;
  int _yellowMarbles = 2;

  // Event selection
  String _eventA = '';
  String _eventB = '';

  // Calculation results
  double _probabilityA = 0.0;
  double _probabilityB = 0.0;
  double _probabilityAandB = 0.0;
  double _probabilityBgivenA = 0.0;
  bool _isIndependent = false;

  // Colors
  late Color _primaryColor;
  late Color _secondaryColor;
  late Color _accentColor;
  late Color _backgroundColor;
  late Color _textColor;

  // Predefined scenarios
  late List<Map<String, dynamic>> _scenarios;

  // Completion tracking
  bool _isCompleted = false;

  @override
  void initState() {
    super.initState();

    // Initialize colors
    _primaryColor = widget.data['primaryColor'] != null
        ? Color(int.parse(widget.data['primaryColor'], radix: 16))
        : Colors.blue;
    _secondaryColor = widget.data['secondaryColor'] != null
        ? Color(int.parse(widget.data['secondaryColor'], radix: 16))
        : Colors.lightBlue;
    _accentColor = widget.data['accentColor'] != null
        ? Color(int.parse(widget.data['accentColor'], radix: 16))
        : Colors.orange;
    _backgroundColor = widget.data['backgroundColor'] != null
        ? Color(int.parse(widget.data['backgroundColor'], radix: 16))
        : Colors.white;
    _textColor = widget.data['textColor'] != null
        ? Color(int.parse(widget.data['textColor'], radix: 16))
        : Colors.black87;

    // Initialize scenario types
    _availableScenarioTypes = widget.data['scenarioTypes'] != null
        ? List<String>.from(widget.data['scenarioTypes'])
        : ['cards', 'dice', 'marbles'];
    _selectedScenarioType = _availableScenarioTypes.first;

    // Initialize scenarios
    _scenarios = widget.data['scenarios'] != null
        ? List<Map<String, dynamic>>.from(widget.data['scenarios'])
        : _getDefaultScenarios();

    // Set default events
    _setDefaultEvents();
  }

  // Get default scenarios if none provided
  List<Map<String, dynamic>> _getDefaultScenarios() {
    return [
      {
        'title': 'Drawing Cards',
        'description': 'Drawing a heart given that the card is red',
        'type': 'cards',
        'eventA': 'red card',
        'eventB': 'heart',
      },
      {
        'title': 'Drawing Cards',
        'description': 'Drawing a face card given that the card is a spade',
        'type': 'cards',
        'eventA': 'spade',
        'eventB': 'face card',
      },
      {
        'title': 'Rolling Dice',
        'description': 'Rolling a sum of 7 given that at least one die shows 4',
        'type': 'dice',
        'eventA': 'at least one 4',
        'eventB': 'sum equals 7',
      },
      {
        'title': 'Drawing Marbles',
        'description': 'Drawing a red marble after drawing a blue marble (without replacement)',
        'type': 'marbles',
        'eventA': 'first draw is blue',
        'eventB': 'second draw is red',
      },
    ];
  }

  // Set default events based on scenario type
  void _setDefaultEvents() {
    switch (_selectedScenarioType) {
      case 'cards':
        _eventA = 'red card';
        _eventB = 'heart';
        break;
      case 'dice':
        _eventA = 'at least one 6';
        _eventB = 'sum equals 7';
        break;
      case 'marbles':
        _eventA = 'first draw is blue';
        _eventB = 'second draw is red';
        break;
      default:
        _eventA = 'Event A';
        _eventB = 'Event B';
    }

    // Calculate probabilities
    _calculateProbabilities();
  }

  // Load a scenario
  void _loadScenario(Map<String, dynamic> scenario) {
    setState(() {
      _selectedScenarioType = scenario['type'];
      _eventA = scenario['eventA'];
      _eventB = scenario['eventB'];

      // Calculate probabilities
      _calculateProbabilities();

      // Mark as completed when user interacts with scenarios
      if (!_isCompleted) {
        _isCompleted = true;
        widget.onStateChanged?.call(true);
      }
    });
  }

  // Calculate probabilities based on scenario type and events
  void _calculateProbabilities() {
    switch (_selectedScenarioType) {
      case 'cards':
        _calculateCardProbabilities();
        break;
      case 'dice':
        _calculateDiceProbabilities();
        break;
      case 'marbles':
        _calculateMarbleProbabilities();
        break;
      default:
        _probabilityA = 0.0;
        _probabilityB = 0.0;
        _probabilityAandB = 0.0;
        _probabilityBgivenA = 0.0;
        _isIndependent = false;
    }
  }

  // Placeholder for event selection section
  Widget _buildEventSelectionSection() {
    return Container(
      padding: const EdgeInsets.all(8),
      decoration: BoxDecoration(
        color: Colors.grey[100],
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: Colors.grey[300]!),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Select Events:',
            style: TextStyle(
              fontSize: 14,
              fontWeight: FontWeight.bold,
              color: _textColor,
            ),
          ),
          const SizedBox(height: 8),
          Text('Event A: $_eventA'),
          const SizedBox(height: 4),
          Text('Event B: $_eventB'),
        ],
      ),
    );
  }

  // Placeholder for results section
  Widget _buildResultsSection() {
    return Container(
      padding: const EdgeInsets.all(8),
      decoration: BoxDecoration(
        color: _primaryColor.withOpacity(0.1),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: _primaryColor),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Results:',
            style: TextStyle(
              fontSize: 14,
              fontWeight: FontWeight.bold,
              color: _textColor,
            ),
          ),
          const SizedBox(height: 8),
          _buildProbabilityRow('P(A)', _probabilityA),
          _buildProbabilityRow('P(B)', _probabilityB),
          _buildProbabilityRow('P(A ∩ B)', _probabilityAandB),
          _buildProbabilityRow('P(B|A)', _probabilityBgivenA),
          const SizedBox(height: 8),
          Text(
            'Events are ${_isIndependent ? 'independent' : 'dependent'}',
            style: TextStyle(
              fontSize: 14,
              fontWeight: FontWeight.bold,
              color: _isIndependent ? Colors.green : Colors.red,
            ),
          ),
        ],
      ),
    );
  }

  // Helper to build probability row
  Widget _buildProbabilityRow(String label, double value) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 2),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(
            label,
            style: TextStyle(
              fontSize: 14,
              color: _textColor,
            ),
          ),
          Text(
            '${(value * 100).toStringAsFixed(2)}%',
            style: TextStyle(
              fontSize: 14,
              fontWeight: FontWeight.bold,
              color: _textColor,
            ),
          ),
        ],
      ),
    );
  }

  // Calculate card probabilities
  void _calculateCardProbabilities() {
    // Implementation omitted for brevity
    _probabilityA = 0.5;
    _probabilityB = 0.25;
    _probabilityAandB = 0.25;
    _probabilityBgivenA = 0.5;
    _isIndependent = true;
  }

  // Calculate dice probabilities
  void _calculateDiceProbabilities() {
    // Implementation omitted for brevity
    _probabilityA = 0.3;
    _probabilityB = 0.17;
    _probabilityAandB = 0.05;
    _probabilityBgivenA = 0.17;
    _isIndependent = true;
  }

  // Calculate marble probabilities
  void _calculateMarbleProbabilities() {
    // Implementation omitted for brevity
    _probabilityA = 0.4;
    _probabilityB = 0.3;
    _probabilityAandB = 0.12;
    _probabilityBgivenA = 0.3;
    _isIndependent = false;
  }

  @override
  Widget build(BuildContext context) {
    return Card(
      elevation: 4,
      margin: const EdgeInsets.all(8),
      color: _backgroundColor,
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Title
            Text(
              widget.data['title'] ?? 'Conditional Probability Calculator',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
                color: _textColor,
              ),
            ),
            const SizedBox(height: 8),
            // Description
            Text(
              widget.data['description'] ?? 'Calculate conditional probabilities for different scenarios.',
              style: TextStyle(
                fontSize: 14,
                color: _textColor.withOpacity(0.7),
              ),
            ),
            const SizedBox(height: 16),
            // Scenario selection
            Row(
              children: [
                Text(
                  'Scenario Type:',
                  style: TextStyle(
                    fontSize: 14,
                    fontWeight: FontWeight.bold,
                    color: _textColor,
                  ),
                ),
                const SizedBox(width: 16),
                DropdownButton<String>(
                  value: _selectedScenarioType,
                  onChanged: (String? newValue) {
                    if (newValue != null) {
                      setState(() {
                        _selectedScenarioType = newValue;
                        _setDefaultEvents();
                      });
                    }
                  },
                  items: _availableScenarioTypes
                      .map<DropdownMenuItem<String>>((String value) {
                    return DropdownMenuItem<String>(
                      value: value,
                      child: Text(value.capitalize()),
                    );
                  }).toList(),
                ),
              ],
            ),
            const SizedBox(height: 16),
            // Event selection
            _buildEventSelectionSection(),
            const SizedBox(height: 16),
            // Results section
            _buildResultsSection(),
            const SizedBox(height: 16),
            // Complete button
            ElevatedButton(
              onPressed: () {
                if (!_isCompleted) {
                  setState(() {
                    _isCompleted = true;
                  });
                  widget.onStateChanged?.call(true);
                }
              },
              style: ElevatedButton.styleFrom(
                backgroundColor: _accentColor,
                foregroundColor: Colors.white,
              ),
              child: const Text('Complete'),
            ),
          ],
        ),
      ),
    );
  }
}
