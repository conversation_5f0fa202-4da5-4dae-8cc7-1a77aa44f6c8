import 'package:flutter/material.dart';
import 'dart:math' as math;

/// A widget that helps users explore linear functions by visualizing how changes to parameters affect the graph
class InteractiveLinearFunctionExplorerWidget extends StatefulWidget {
  /// The data for this widget
  final Map<String, dynamic> data;

  /// Optional callback when the widget state changes
  final Function(bool isCompleted)? onStateChanged;

  const InteractiveLinearFunctionExplorerWidget({
    Key? key,
    required this.data,
    this.onStateChanged,
  }) : super(key: key);

  /// Factory constructor to create the widget from JSON data
  factory InteractiveLinearFunctionExplorerWidget.fromData(Map<String, dynamic> data) {
    return InteractiveLinearFunctionExplorerWidget(
      data: data,
    );
  }

  @override
  State<InteractiveLinearFunctionExplorerWidget> createState() => _InteractiveLinearFunctionExplorerWidgetState();
}

class _InteractiveLinearFunctionExplorerWidgetState extends State<InteractiveLinearFunctionExplorerWidget> {
  // Slope (m) of the linear function
  double _slope = 1.0;

  // Y-intercept (b) of the linear function
  double _yIntercept = 0.0;

  // Whether the widget is completed
  bool _isCompleted = false;

  // Current form of the linear function
  String _currentForm = 'slope-intercept';

  // Current example
  int _currentExampleIndex = 0;

  // Colors
  late Color _primaryColor;
  late Color _secondaryColor;
  late Color _accentColor;
  late Color _backgroundColor;
  late Color _textColor;

  // List of real-world examples
  late List<Map<String, dynamic>> _examples;

  // Whether to show key points
  bool _showKeyPoints = true;

  // Whether to show grid
  bool _showGrid = true;

  // Whether to show x and y intercepts
  bool _showIntercepts = true;

  // Whether to show the equation
  bool _showEquation = true;

  // Whether to show the example
  bool _showExample = false;

  // Whether to show the form selector
  bool _showFormSelector = true;

  // Point for point-slope form
  double _pointX = 2.0;
  double _pointY = 0.0;

  @override
  void initState() {
    super.initState();

    // Initialize colors
    _primaryColor = _parseColor(widget.data['primary_color']) ?? Colors.blue;
    _secondaryColor = _parseColor(widget.data['secondary_color']) ?? Colors.orange;
    _accentColor = _parseColor(widget.data['accent_color']) ?? Colors.green;
    _backgroundColor = _parseColor(widget.data['background_color']) ?? Colors.white;
    _textColor = _parseColor(widget.data['text_color']) ?? Colors.black87;

    // Initialize examples
    _examples = widget.data['examples'] != null
        ? List<Map<String, dynamic>>.from(widget.data['examples'])
        : _getDefaultExamples();

    // Initialize point for point-slope form
    _updatePointY();
  }

  // Parse color from hex string
  Color? _parseColor(String? hexString) {
    if (hexString == null) return null;

    hexString = hexString.replaceAll('#', '');
    if (hexString.length == 6) {
      hexString = 'FF$hexString';
    }

    return Color(int.parse(hexString, radix: 16));
  }

  // Get default examples if none provided
  List<Map<String, dynamic>> _getDefaultExamples() {
    return [
      {
        'title': 'Distance vs. Time',
        'description': 'A car travels at a constant speed of 60 km/h. The linear function d(t) = 60t represents the distance traveled (d) in kilometers after t hours.',
        'slope': 60.0,
        'y_intercept': 0.0,
        'slope_meaning': 'The slope (60) represents the speed of the car in km/h.',
        'y_intercept_meaning': 'The y-intercept (0) means the car starts at the origin (0 km).',
        'x_axis': 'Time (hours)',
        'y_axis': 'Distance (km)',
      },
      {
        'title': 'Temperature Conversion',
        'description': 'The linear function C(F) = (5/9)(F - 32) converts temperature from Fahrenheit (F) to Celsius (C).',
        'slope': 5.0/9.0,
        'y_intercept': -32.0 * 5.0/9.0,
        'slope_meaning': 'The slope (5/9) is the conversion factor between the two temperature scales.',
        'y_intercept_meaning': 'The y-intercept (-17.78) is the Celsius equivalent of 0°F.',
        'x_axis': 'Fahrenheit (°F)',
        'y_axis': 'Celsius (°C)',
      },
      {
        'title': 'Cost of Taxi Ride',
        'description': 'A taxi charges a base fare of \$3 plus \$2 per mile. The linear function C(m) = 2m + 3 represents the cost (C) in dollars for a ride of m miles.',
        'slope': 2.0,
        'y_intercept': 3.0,
        'slope_meaning': 'The slope (2) represents the rate per mile in dollars.',
        'y_intercept_meaning': 'The y-intercept (3) represents the base fare in dollars.',
        'x_axis': 'Distance (miles)',
        'y_axis': 'Cost (\$)',
      },
      {
        'title': 'Depreciation of a Car',
        'description': 'A new car worth \$25,000 depreciates by \$3,000 per year. The linear function V(t) = -3000t + 25000 represents the value (V) in dollars after t years.',
        'slope': -3000.0,
        'y_intercept': 25000.0,
        'slope_meaning': 'The slope (-3000) represents the depreciation rate in dollars per year.',
        'y_intercept_meaning': 'The y-intercept (25000) represents the initial value of the car in dollars.',
        'x_axis': 'Time (years)',
        'y_axis': 'Value (\$)',
      },
    ];
  }

  // Update the point Y value based on the current slope and y-intercept
  void _updatePointY() {
    _pointY = _slope * _pointX + _yIntercept;
  }

  // Load an example
  void _loadExample(int index) {
    if (index >= 0 && index < _examples.length) {
      setState(() {
        _currentExampleIndex = index;
        _slope = _examples[index]['slope'];
        _yIntercept = _examples[index]['y_intercept'];
        _updatePointY();
        _showExample = true;
      });
    }
  }

  // Reset to default values
  void _resetToDefault() {
    setState(() {
      _slope = 1.0;
      _yIntercept = 0.0;
      _updatePointY();
      _showExample = false;
    });
  }

  // Toggle key points visibility
  void _toggleKeyPoints() {
    setState(() {
      _showKeyPoints = !_showKeyPoints;
    });
  }

  // Toggle grid visibility
  void _toggleGrid() {
    setState(() {
      _showGrid = !_showGrid;
    });
  }

  // Toggle intercepts visibility
  void _toggleIntercepts() {
    setState(() {
      _showIntercepts = !_showIntercepts;
    });
  }

  // Toggle equation visibility
  void _toggleEquation() {
    setState(() {
      _showEquation = !_showEquation;
    });
  }

  // Change the form of the linear function
  void _changeForm(String form) {
    setState(() {
      _currentForm = form;
    });
  }

  // Get the equation text based on the current form
  String _getEquationText() {
    switch (_currentForm) {
      case 'slope-intercept':
        return 'y = ${_slope.toStringAsFixed(1)}x + ${_yIntercept.toStringAsFixed(1)}';
      case 'point-slope':
        return 'y - ${_pointY.toStringAsFixed(1)} = ${_slope.toStringAsFixed(1)}(x - ${_pointX.toStringAsFixed(1)})';
      case 'standard':
        if (_slope == 0) {
          return 'y = ${_yIntercept.toStringAsFixed(1)}';
        } else {
          final A = _slope;
          final B = -1.0;
          final C = _yIntercept;
          return '${A.toStringAsFixed(1)}x + ${B.toStringAsFixed(1)}y + ${C.toStringAsFixed(1)} = 0';
        }
      default:
        return 'y = ${_slope.toStringAsFixed(1)}x + ${_yIntercept.toStringAsFixed(1)}';
    }
  }

  // Calculate the x-intercept
  double? _calculateXIntercept() {
    if (_slope == 0) {
      if (_yIntercept == 0) {
        // The line is y = 0 (the x-axis), so all x values are x-intercepts
        return null;
      } else {
        // The line is y = b where b ≠ 0, so there is no x-intercept
        return null;
      }
    } else {
      // Solve for x when y = 0: 0 = mx + b => x = -b/m
      return -_yIntercept / _slope;
    }
  }

  // Mark the widget as completed
  void _markAsCompleted() {
    if (!_isCompleted) {
      setState(() {
        _isCompleted = true;
      });

      // Notify parent of state change
      widget.onStateChanged?.call(true);
    }
  }

  // Build the graph and controls section
  Widget _buildGraphAndControls() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Function form selector
        if (_showFormSelector)
          Container(
            padding: const EdgeInsets.all(8),
            decoration: BoxDecoration(
              color: Colors.grey[100],
              borderRadius: BorderRadius.circular(8),
              border: Border.all(color: Colors.grey[300]!),
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'Function Form:',
                  style: TextStyle(
                    fontSize: 14,
                    fontWeight: FontWeight.bold,
                    color: _textColor,
                  ),
                ),
                const SizedBox(height: 8),
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                  children: [
                    _buildFormButton('slope-intercept', 'Slope-Intercept'),
                    _buildFormButton('point-slope', 'Point-Slope'),
                    _buildFormButton('standard', 'Standard'),
                  ],
                ),
              ],
            ),
          ),

        const SizedBox(height: 16),

        // Equation display
        if (_showEquation)
          Container(
            width: double.infinity,
            padding: const EdgeInsets.all(12),
            decoration: BoxDecoration(
              color: _primaryColor.withOpacity(0.1),
              borderRadius: BorderRadius.circular(8),
              border: Border.all(color: _primaryColor),
            ),
            child: Text(
              _getEquationText(),
              textAlign: TextAlign.center,
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
                color: _primaryColor,
              ),
            ),
          ),

        const SizedBox(height: 16),

        // Graph
        Container(
          height: 250,
          width: double.infinity,
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(8),
            border: Border.all(color: Colors.grey[300]!),
          ),
          child: ClipRRect(
            borderRadius: BorderRadius.circular(8),
            child: CustomPaint(
              painter: LinearFunctionGraphPainter(
                slope: _slope,
                yIntercept: _yIntercept,
                pointX: _pointX,
                pointY: _pointY,
                showGrid: _showGrid,
                showKeyPoints: _showKeyPoints,
                showIntercepts: _showIntercepts,
                primaryColor: _primaryColor,
                secondaryColor: _secondaryColor,
                accentColor: _accentColor,
              ),
            ),
          ),
        ),

        const SizedBox(height: 16),

        // Graph controls
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceEvenly,
          children: [
            _buildControlButton(
              icon: Icons.grid_on,
              label: 'Grid',
              isActive: _showGrid,
              onPressed: _toggleGrid,
            ),
            _buildControlButton(
              icon: Icons.location_on,
              label: 'Key Points',
              isActive: _showKeyPoints,
              onPressed: _toggleKeyPoints,
            ),
            _buildControlButton(
              icon: Icons.change_history,
              label: 'Intercepts',
              isActive: _showIntercepts,
              onPressed: _toggleIntercepts,
            ),
            _buildControlButton(
              icon: Icons.functions,
              label: 'Equation',
              isActive: _showEquation,
              onPressed: _toggleEquation,
            ),
          ],
        ),

        const SizedBox(height: 16),

        // Slope slider
        Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Slope (m): ${_slope.toStringAsFixed(1)}',
              style: TextStyle(
                fontSize: 14,
                fontWeight: FontWeight.bold,
                color: _textColor,
              ),
            ),
            Slider(
              value: _slope,
              min: -5.0,
              max: 5.0,
              divisions: 100,
              activeColor: _primaryColor,
              inactiveColor: _primaryColor.withOpacity(0.3),
              onChanged: (value) {
                setState(() {
                  _slope = value;
                  _updatePointY();
                });
              },
            ),
          ],
        ),

        // Y-intercept slider
        Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Y-intercept (b): ${_yIntercept.toStringAsFixed(1)}',
              style: TextStyle(
                fontSize: 14,
                fontWeight: FontWeight.bold,
                color: _textColor,
              ),
            ),
            Slider(
              value: _yIntercept,
              min: -5.0,
              max: 5.0,
              divisions: 100,
              activeColor: _secondaryColor,
              inactiveColor: _secondaryColor.withOpacity(0.3),
              onChanged: (value) {
                setState(() {
                  _yIntercept = value;
                  _updatePointY();
                });
              },
            ),
          ],
        ),

        // Point X slider (for point-slope form)
        if (_currentForm == 'point-slope')
          Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                'Point X: ${_pointX.toStringAsFixed(1)}',
                style: TextStyle(
                  fontSize: 14,
                  fontWeight: FontWeight.bold,
                  color: _textColor,
                ),
              ),
              Slider(
                value: _pointX,
                min: -5.0,
                max: 5.0,
                divisions: 100,
                activeColor: _accentColor,
                inactiveColor: _accentColor.withOpacity(0.3),
                onChanged: (value) {
                  setState(() {
                    _pointX = value;
                    _updatePointY();
                  });
                },
              ),
            ],
          ),

        const SizedBox(height: 16),

        // Examples and reset buttons
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            ElevatedButton(
              onPressed: _resetToDefault,
              style: ElevatedButton.styleFrom(
                backgroundColor: _secondaryColor,
                foregroundColor: Colors.white,
              ),
              child: const Text('Reset'),
            ),
            ElevatedButton(
              onPressed: () => _showExamplesDialog(context),
              style: ElevatedButton.styleFrom(
                backgroundColor: _primaryColor,
                foregroundColor: Colors.white,
              ),
              child: const Text('Examples'),
            ),
            ElevatedButton(
              onPressed: _markAsCompleted,
              style: ElevatedButton.styleFrom(
                backgroundColor: _accentColor,
                foregroundColor: Colors.white,
              ),
              child: const Text('Complete'),
            ),
          ],
        ),
      ],
    );
  }

  // Build a form selection button
  Widget _buildFormButton(String form, String label) {
    final isSelected = _currentForm == form;

    return ElevatedButton(
      onPressed: () => _changeForm(form),
      style: ElevatedButton.styleFrom(
        backgroundColor: isSelected ? _primaryColor : Colors.grey[300],
        foregroundColor: isSelected ? Colors.white : _textColor,
        padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      ),
      child: Text(label),
    );
  }

  // Build a control button
  Widget _buildControlButton({
    required IconData icon,
    required String label,
    required bool isActive,
    required VoidCallback onPressed,
  }) {
    return Column(
      children: [
        IconButton(
          icon: Icon(icon),
          color: isActive ? _primaryColor : Colors.grey,
          onPressed: onPressed,
        ),
        Text(
          label,
          style: TextStyle(
            fontSize: 12,
            color: isActive ? _primaryColor : Colors.grey,
          ),
        ),
      ],
    );
  }

  // Build the example section
  Widget _buildExampleSection() {
    final example = _examples[_currentExampleIndex];

    return Container(
      margin: const EdgeInsets.only(top: 16),
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: _accentColor.withOpacity(0.1),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: _accentColor),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Example: ${example['title']}',
            style: TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.bold,
              color: _accentColor,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            example['description'],
            style: TextStyle(
              fontSize: 14,
              color: _textColor,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            'Slope meaning: ${example['slope_meaning']}',
            style: TextStyle(
              fontSize: 14,
              color: _textColor,
            ),
          ),
          const SizedBox(height: 4),
          Text(
            'Y-intercept meaning: ${example['y_intercept_meaning']}',
            style: TextStyle(
              fontSize: 14,
              color: _textColor,
            ),
          ),
          const SizedBox(height: 8),
          Row(
            children: [
              Text(
                'X-axis: ${example['x_axis']}',
                style: TextStyle(
                  fontSize: 14,
                  fontStyle: FontStyle.italic,
                  color: _textColor,
                ),
              ),
              const SizedBox(width: 16),
              Text(
                'Y-axis: ${example['y_axis']}',
                style: TextStyle(
                  fontSize: 14,
                  fontStyle: FontStyle.italic,
                  color: _textColor,
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  // Show examples dialog
  void _showExamplesDialog(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(
          'Real-World Examples',
          style: TextStyle(
            color: _primaryColor,
            fontWeight: FontWeight.bold,
          ),
        ),
        content: Container(
          width: double.maxFinite,
          child: ListView.builder(
            shrinkWrap: true,
            itemCount: _examples.length,
            itemBuilder: (context, index) {
              final example = _examples[index];
              return ListTile(
                title: Text(example['title']),
                subtitle: Text(
                  example['description'],
                  maxLines: 2,
                  overflow: TextOverflow.ellipsis,
                ),
                onTap: () {
                  Navigator.of(context).pop();
                  _loadExample(index);
                },
              );
            },
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Cancel'),
          ),
        ],
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: _backgroundColor,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withOpacity(0.2),
            spreadRadius: 1,
            blurRadius: 3,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Title
          Text(
            widget.data['title'] ?? 'Linear Function Explorer',
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
              color: _textColor,
            ),
          ),

          const SizedBox(height: 8),

          // Description
          if (widget.data['description'] != null)
            Text(
              widget.data['description'],
              style: TextStyle(
                fontSize: 14,
                color: _textColor.withOpacity(0.8),
              ),
            ),

          const SizedBox(height: 16),

          // Graph and controls
          _buildGraphAndControls(),

          // Example section
          if (_showExample)
            _buildExampleSection(),

          // Widget name tag
          if (widget.data['showNameTag'] ?? true)
            Padding(
              padding: const EdgeInsets.only(top: 8),
              child: Text(
                'InteractiveLinearFunctionExplorer',
                style: TextStyle(
                  color: Colors.grey[600],
                  fontSize: 12,
                  fontStyle: FontStyle.italic,
                ),
              ),
            ),
        ],
      ),
    );
  }
}

/// Custom painter for drawing linear function graphs
class LinearFunctionGraphPainter extends CustomPainter {
  final double slope;
  final double yIntercept;
  final double pointX;
  final double pointY;
  final bool showGrid;
  final bool showKeyPoints;
  final bool showIntercepts;
  final Color primaryColor;
  final Color secondaryColor;
  final Color accentColor;

  // Constants for graph scaling
  final double minX = -5.0;
  final double maxX = 5.0;
  final double minY = -5.0;
  final double maxY = 5.0;

  LinearFunctionGraphPainter({
    required this.slope,
    required this.yIntercept,
    required this.pointX,
    required this.pointY,
    required this.showGrid,
    required this.showKeyPoints,
    required this.showIntercepts,
    required this.primaryColor,
    required this.secondaryColor,
    required this.accentColor,
  });

  @override
  void paint(Canvas canvas, Size size) {
    final paint = Paint()
      ..color = primaryColor
      ..strokeWidth = 2.0
      ..style = PaintingStyle.stroke;

    final gridPaint = Paint()
      ..color = Colors.grey[300]!
      ..strokeWidth = 0.5
      ..style = PaintingStyle.stroke;

    final axisPaint = Paint()
      ..color = Colors.black87
      ..strokeWidth = 1.0
      ..style = PaintingStyle.stroke;

    final pointPaint = Paint()
      ..color = secondaryColor
      ..strokeWidth = 4.0
      ..style = PaintingStyle.fill;

    final interceptPaint = Paint()
      ..color = accentColor
      ..strokeWidth = 4.0
      ..style = PaintingStyle.fill;

    // Draw grid
    if (showGrid) {
      _drawGrid(canvas, size, gridPaint);
    }

    // Draw axes
    _drawAxes(canvas, size, axisPaint);

    // Draw function
    _drawFunction(canvas, size, paint);

    // Draw key points
    if (showKeyPoints) {
      // Draw y-intercept
      final yInterceptScreenX = _mapXToScreen(0, size);
      final yInterceptScreenY = _mapYToScreen(yIntercept, size);

      canvas.drawCircle(
        Offset(yInterceptScreenX, yInterceptScreenY),
        6,
        pointPaint,
      );

      // Draw point for point-slope form
      final pointScreenX = _mapXToScreen(pointX, size);
      final pointScreenY = _mapYToScreen(pointY, size);

      canvas.drawCircle(
        Offset(pointScreenX, pointScreenY),
        6,
        pointPaint,
      );
    }

    // Draw intercepts
    if (showIntercepts) {
      // Y-intercept is already drawn if showKeyPoints is true
      if (!showKeyPoints) {
        final yInterceptScreenX = _mapXToScreen(0, size);
        final yInterceptScreenY = _mapYToScreen(yIntercept, size);

        canvas.drawCircle(
          Offset(yInterceptScreenX, yInterceptScreenY),
          6,
          interceptPaint,
        );
      }

      // Draw x-intercept if it exists within the visible range
      final xIntercept = slope != 0 ? -yIntercept / slope : null;
      if (xIntercept != null && xIntercept >= minX && xIntercept <= maxX) {
        final xInterceptScreenX = _mapXToScreen(xIntercept, size);
        final xInterceptScreenY = _mapYToScreen(0, size);

        canvas.drawCircle(
          Offset(xInterceptScreenX, xInterceptScreenY),
          6,
          interceptPaint,
        );
      }
    }

    // Draw labels
    _drawLabels(canvas, size);
  }

  void _drawGrid(Canvas canvas, Size size, Paint paint) {
    // Draw vertical grid lines
    for (double x = minX; x <= maxX; x += 1) {
      final screenX = _mapXToScreen(x, size);
      canvas.drawLine(
        Offset(screenX, 0),
        Offset(screenX, size.height),
        paint,
      );
    }

    // Draw horizontal grid lines
    for (double y = minY; y <= maxY; y += 1) {
      final screenY = _mapYToScreen(y, size);
      canvas.drawLine(
        Offset(0, screenY),
        Offset(size.width, screenY),
        paint,
      );
    }
  }

  void _drawAxes(Canvas canvas, Size size, Paint paint) {
    // Draw x-axis
    final yZero = _mapYToScreen(0, size);
    canvas.drawLine(
      Offset(0, yZero),
      Offset(size.width, yZero),
      paint,
    );

    // Draw y-axis
    final xZero = _mapXToScreen(0, size);
    canvas.drawLine(
      Offset(xZero, 0),
      Offset(xZero, size.height),
      paint,
    );

    // Draw axis ticks
    for (double x = minX; x <= maxX; x += 1) {
      if (x == 0) continue; // Skip origin
      final screenX = _mapXToScreen(x, size);
      canvas.drawLine(
        Offset(screenX, yZero - 5),
        Offset(screenX, yZero + 5),
        paint,
      );
    }

    for (double y = minY; y <= maxY; y += 1) {
      if (y == 0) continue; // Skip origin
      final screenY = _mapYToScreen(y, size);
      canvas.drawLine(
        Offset(xZero - 5, screenY),
        Offset(xZero + 5, screenY),
        paint,
      );
    }
  }

  void _drawFunction(Canvas canvas, Size size, Paint paint) {
    // Calculate y values for the left and right edges of the screen
    final leftX = minX;
    final rightX = maxX;
    final leftY = slope * leftX + yIntercept;
    final rightY = slope * rightX + yIntercept;

    // Map to screen coordinates
    final leftScreenX = _mapXToScreen(leftX, size);
    final leftScreenY = _mapYToScreen(leftY, size);
    final rightScreenX = _mapXToScreen(rightX, size);
    final rightScreenY = _mapYToScreen(rightY, size);

    // Draw the line
    canvas.drawLine(
      Offset(leftScreenX, leftScreenY),
      Offset(rightScreenX, rightScreenY),
      paint,
    );
  }

  void _drawLabels(Canvas canvas, Size size) {
    final textStyle = TextStyle(
      color: Colors.black87,
      fontSize: 10,
    );
    final textPainter = TextPainter(
      textDirection: TextDirection.ltr,
    );

    // Draw axis labels
    final yZero = _mapYToScreen(0, size);
    final xZero = _mapXToScreen(0, size);

    // X-axis labels
    for (int i = minX.toInt(); i <= maxX.toInt(); i++) {
      if (i == 0) continue; // Skip zero as it's the origin
      final x = i.toDouble();
      final screenX = _mapXToScreen(x, size);

      textPainter.text = TextSpan(
        text: i.toString(),
        style: textStyle,
      );
      textPainter.layout();
      textPainter.paint(
        canvas,
        Offset(screenX - textPainter.width / 2, yZero + 10),
      );
    }

    // Y-axis labels
    for (int i = minY.toInt(); i <= maxY.toInt(); i++) {
      if (i == 0) continue; // Skip zero as it's the origin
      final y = i.toDouble();
      final screenY = _mapYToScreen(y, size);

      textPainter.text = TextSpan(
        text: i.toString(),
        style: textStyle,
      );
      textPainter.layout();
      textPainter.paint(
        canvas,
        Offset(xZero + 10, screenY - textPainter.height / 2),
      );
    }

    // Origin label
    textPainter.text = TextSpan(
      text: "0",
      style: textStyle,
    );
    textPainter.layout();
    textPainter.paint(
      canvas,
      Offset(xZero + 5, yZero + 5),
    );

    // Draw point labels if showing key points
    if (showKeyPoints) {
      // Y-intercept label
      textPainter.text = TextSpan(
        text: "(0, ${yIntercept.toStringAsFixed(1)})",
        style: TextStyle(
          color: secondaryColor,
          fontSize: 10,
          fontWeight: FontWeight.bold,
        ),
      );
      textPainter.layout();
      textPainter.paint(
        canvas,
        Offset(
          _mapXToScreen(0, size) + 10,
          _mapYToScreen(yIntercept, size) - 15,
        ),
      );

      // Point label for point-slope form
      textPainter.text = TextSpan(
        text: "(${pointX.toStringAsFixed(1)}, ${pointY.toStringAsFixed(1)})",
        style: TextStyle(
          color: secondaryColor,
          fontSize: 10,
          fontWeight: FontWeight.bold,
        ),
      );
      textPainter.layout();
      textPainter.paint(
        canvas,
        Offset(
          _mapXToScreen(pointX, size) + 10,
          _mapYToScreen(pointY, size) - 15,
        ),
      );
    }

    // Draw intercept labels if showing intercepts
    if (showIntercepts) {
      // X-intercept label if it exists within the visible range
      final xIntercept = slope != 0 ? -yIntercept / slope : null;
      if (xIntercept != null && xIntercept >= minX && xIntercept <= maxX) {
        textPainter.text = TextSpan(
          text: "(${xIntercept.toStringAsFixed(1)}, 0)",
          style: TextStyle(
            color: accentColor,
            fontSize: 10,
            fontWeight: FontWeight.bold,
          ),
        );
        textPainter.layout();
        textPainter.paint(
          canvas,
          Offset(
            _mapXToScreen(xIntercept, size) + 10,
            _mapYToScreen(0, size) + 10,
          ),
        );
      }

      // Y-intercept label if not already shown by showKeyPoints
      if (!showKeyPoints) {
        textPainter.text = TextSpan(
          text: "(0, ${yIntercept.toStringAsFixed(1)})",
          style: TextStyle(
            color: accentColor,
            fontSize: 10,
            fontWeight: FontWeight.bold,
          ),
        );
        textPainter.layout();
        textPainter.paint(
          canvas,
          Offset(
            _mapXToScreen(0, size) + 10,
            _mapYToScreen(yIntercept, size) - 15,
          ),
        );
      }
    }
  }

  // Map x coordinate from math space to screen space
  double _mapXToScreen(double x, Size size) {
    return size.width * (x - minX) / (maxX - minX);
  }

  // Map y coordinate from math space to screen space
  double _mapYToScreen(double y, Size size) {
    // Note: Screen coordinates have y increasing downward, math has y increasing upward
    return size.height * (1 - (y - minY) / (maxY - minY));
  }

  @override
  bool shouldRepaint(covariant LinearFunctionGraphPainter oldDelegate) {
    return oldDelegate.slope != slope ||
        oldDelegate.yIntercept != yIntercept ||
        oldDelegate.pointX != pointX ||
        oldDelegate.pointY != pointY ||
        oldDelegate.showGrid != showGrid ||
        oldDelegate.showKeyPoints != showKeyPoints ||
        oldDelegate.showIntercepts != showIntercepts ||
        oldDelegate.primaryColor != primaryColor ||
        oldDelegate.secondaryColor != secondaryColor ||
        oldDelegate.accentColor != accentColor;
  }
}