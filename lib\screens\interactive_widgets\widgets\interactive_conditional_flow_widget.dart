import 'package:flutter/material.dart';
import 'dart:math' as math;

/// A widget that demonstrates conditional logic flow with interactive elements
/// Users can explore "if-then" statements and see how different conditions affect outcomes
class InteractiveConditionalFlowWidget extends StatefulWidget {
  /// The data for this widget
  final Map<String, dynamic> data;

  /// Optional callback when the widget state changes
  final Function(bool isCompleted)? onStateChanged;

  const InteractiveConditionalFlowWidget({
    Key? key,
    required this.data,
    this.onStateChanged,
  }) : super(key: key);

  /// Factory constructor to create the widget from JSON data
  factory InteractiveConditionalFlowWidget.fromData(Map<String, dynamic> data) {
    return InteractiveConditionalFlowWidget(
      data: data,
    );
  }

  @override
  State<InteractiveConditionalFlowWidget> createState() => _InteractiveConditionalFlowWidgetState();
}

class _InteractiveConditionalFlowWidgetState extends State<InteractiveConditionalFlowWidget> with SingleTickerProviderStateMixin {
  // Conditional statement data
  late String _title;
  late String _condition;
  late String _consequent;
  late String _alternative;
  late bool _showAlternative;
  late List<ConditionalScenario> _scenarios;
  
  // UI state
  late int _currentScenarioIndex;
  late bool _conditionValue;
  late bool _showResult;
  late bool _isCompleted;
  late bool _showExplanation;
  late int? _selectedOptionIndex;
  
  // Animation controller for flow visualization
  late AnimationController _animationController;
  late Animation<double> _flowAnimation;
  
  // Colors
  late Color _trueColor;
  late Color _falseColor;
  late Color _neutralColor;
  late Color _highlightColor;

  @override
  void initState() {
    super.initState();
    
    // Initialize from data
    _title = widget.data['title'] ?? 'Conditional Logic Flow';
    _condition = widget.data['condition'] ?? 'P';
    _consequent = widget.data['consequent'] ?? 'Q';
    _alternative = widget.data['alternative'] ?? '';
    _showAlternative = widget.data['show_alternative'] ?? false;
    
    // Initialize scenarios
    _scenarios = [];
    final scenariosData = widget.data['scenarios'] as List<dynamic>? ?? [];
    for (final scenario in scenariosData) {
      if (scenario is Map<String, dynamic>) {
        final options = <ConditionalOption>[];
        final optionsData = scenario['options'] as List<dynamic>? ?? [];
        
        for (final option in optionsData) {
          if (option is Map<String, dynamic>) {
            options.add(ConditionalOption(
              text: option['text'] ?? '',
              isCorrect: option['is_correct'] ?? false,
              explanation: option['explanation'] ?? '',
            ));
          }
        }
        
        _scenarios.add(ConditionalScenario(
          conditionText: scenario['condition_text'] ?? '',
          conditionValue: scenario['condition_value'] ?? false,
          consequentText: scenario['consequent_text'] ?? '',
          question: scenario['question'] ?? '',
          options: options,
        ));
      }
    }
    
    // Initialize UI state
    _currentScenarioIndex = 0;
    _conditionValue = _scenarios.isNotEmpty ? _scenarios[0].conditionValue : false;
    _showResult = false;
    _isCompleted = false;
    _showExplanation = false;
    _selectedOptionIndex = null;
    
    // Initialize colors
    _trueColor = _parseColor(widget.data['true_color'], Colors.green);
    _falseColor = _parseColor(widget.data['false_color'], Colors.red);
    _neutralColor = _parseColor(widget.data['neutral_color'], Colors.grey.shade300);
    _highlightColor = _parseColor(widget.data['highlight_color'], Colors.blue);
    
    // Initialize animation controller
    _animationController = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 1500),
    );
    
    _flowAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(
        parent: _animationController,
        curve: Curves.easeInOut,
      ),
    );
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  // Helper method to parse color from string
  Color _parseColor(dynamic colorValue, Color defaultColor) {
    if (colorValue == null) return defaultColor;
    if (colorValue is String) {
      try {
        return Color(int.parse(colorValue.replaceAll('#', '0xFF')));
      } catch (e) {
        return defaultColor;
      }
    }
    return defaultColor;
  }

  // Toggle the condition value
  void _toggleCondition() {
    if (_showResult) return;
    
    setState(() {
      _conditionValue = !_conditionValue;
    });
  }

  // Show the result of the conditional flow
  void _evaluateFlow() {
    setState(() {
      _showResult = true;
    });
    
    _animationController.forward(from: 0.0);
  }

  // Reset the flow visualization
  void _resetFlow() {
    setState(() {
      _showResult = false;
      _animationController.reset();
    });
  }

  // Select an option in the scenario
  void _selectOption(int index) {
    if (_showExplanation) return;
    
    setState(() {
      _selectedOptionIndex = index;
      _showExplanation = true;
      
      // Check if this was the last scenario
      if (_currentScenarioIndex == _scenarios.length - 1) {
        _isCompleted = true;
        widget.onStateChanged?.call(true);
      }
    });
  }

  // Move to the next scenario
  void _nextScenario() {
    if (_currentScenarioIndex < _scenarios.length - 1) {
      setState(() {
        _currentScenarioIndex++;
        _conditionValue = _scenarios[_currentScenarioIndex].conditionValue;
        _showResult = false;
        _showExplanation = false;
        _selectedOptionIndex = null;
        _animationController.reset();
      });
    }
  }

  // Reset the current scenario
  void _resetScenario() {
    setState(() {
      _showExplanation = false;
      _selectedOptionIndex = null;
    });
  }

  @override
  Widget build(BuildContext context) {
    final currentScenario = _scenarios.isNotEmpty ? _scenarios[_currentScenarioIndex] : null;
    
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withOpacity(0.2),
            spreadRadius: 1,
            blurRadius: 3,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Title
          Text(
            _title,
            style: const TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
            ),
          ),
          
          const SizedBox(height: 16),
          
          // Flow diagram
          AspectRatio(
            aspectRatio: 2,
            child: AnimatedBuilder(
              animation: _flowAnimation,
              builder: (context, child) {
                return CustomPaint(
                  painter: ConditionalFlowPainter(
                    condition: _condition,
                    consequent: _consequent,
                    alternative: _alternative,
                    showAlternative: _showAlternative,
                    conditionValue: _conditionValue,
                    showResult: _showResult,
                    flowProgress: _flowAnimation.value,
                    trueColor: _trueColor,
                    falseColor: _falseColor,
                    neutralColor: _neutralColor,
                    highlightColor: _highlightColor,
                  ),
                );
              },
            ),
          ),
          
          const SizedBox(height: 16),
          
          // Controls for basic flow
          if (currentScenario == null) ...[
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceEvenly,
              children: [
                // Condition toggle
                ElevatedButton.icon(
                  onPressed: _toggleCondition,
                  icon: Icon(_conditionValue ? Icons.check : Icons.close),
                  label: Text('$_condition is ${_conditionValue ? 'TRUE' : 'FALSE'}'),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: _conditionValue ? _trueColor : _falseColor,
                    foregroundColor: Colors.white,
                  ),
                ),
                
                // Evaluate button
                ElevatedButton.icon(
                  onPressed: _showResult ? _resetFlow : _evaluateFlow,
                  icon: Icon(_showResult ? Icons.refresh : Icons.play_arrow),
                  label: Text(_showResult ? 'Reset' : 'Evaluate'),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: _highlightColor,
                    foregroundColor: Colors.white,
                  ),
                ),
              ],
            ),
          ],
          
          // Scenario content
          if (currentScenario != null) ...[
            // Scenario description
            Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: Colors.grey.shade50,
                borderRadius: BorderRadius.circular(8),
                border: Border.all(color: Colors.grey.shade300),
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'Condition (P): ${currentScenario.conditionText}',
                    style: TextStyle(
                      fontWeight: FontWeight.bold,
                      color: currentScenario.conditionValue ? _trueColor : _falseColor,
                    ),
                  ),
                  const SizedBox(height: 8),
                  Text(
                    'Consequent (Q): ${currentScenario.consequentText}',
                    style: const TextStyle(
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  const SizedBox(height: 12),
                  Text(
                    'Question: ${currentScenario.question}',
                    style: const TextStyle(
                      fontStyle: FontStyle.italic,
                    ),
                  ),
                ],
              ),
            ),
            
            const SizedBox(height: 16),
            
            // Options
            ...currentScenario.options.asMap().entries.map((entry) {
              final index = entry.key;
              final option = entry.value;
              final isSelected = _selectedOptionIndex == index;
              final isCorrect = option.isCorrect;
              
              // Determine the option color based on selection and correctness
              Color optionColor = _neutralColor;
              if (_showExplanation) {
                if (isCorrect) {
                  optionColor = _trueColor.withOpacity(0.2);
                } else if (isSelected && !isCorrect) {
                  optionColor = _falseColor.withOpacity(0.2);
                }
              } else if (isSelected) {
                optionColor = _highlightColor.withOpacity(0.1);
              }
              
              // Determine the border color
              Color borderColor = Colors.grey.shade300;
              if (_showExplanation) {
                if (isCorrect) {
                  borderColor = _trueColor;
                } else if (isSelected && !isCorrect) {
                  borderColor = _falseColor;
                }
              } else if (isSelected) {
                borderColor = _highlightColor;
              }
              
              return GestureDetector(
                onTap: () => _selectOption(index),
                child: Container(
                  margin: const EdgeInsets.only(bottom: 8),
                  padding: const EdgeInsets.all(12),
                  decoration: BoxDecoration(
                    color: optionColor,
                    borderRadius: BorderRadius.circular(8),
                    border: Border.all(color: borderColor, width: 2),
                  ),
                  child: Text(option.text),
                ),
              );
            }).toList(),
            
            // Explanation
            if (_showExplanation && _selectedOptionIndex != null) ...[
              const SizedBox(height: 16),
              Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: currentScenario.options[_selectedOptionIndex!].isCorrect
                      ? _trueColor.withOpacity(0.1)
                      : _falseColor.withOpacity(0.1),
                  borderRadius: BorderRadius.circular(8),
                  border: Border.all(
                    color: currentScenario.options[_selectedOptionIndex!].isCorrect
                        ? _trueColor
                        : _falseColor,
                  ),
                ),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      currentScenario.options[_selectedOptionIndex!].isCorrect
                          ? 'Correct!'
                          : 'Incorrect',
                      style: TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.bold,
                        color: currentScenario.options[_selectedOptionIndex!].isCorrect
                            ? _trueColor
                            : _falseColor,
                      ),
                    ),
                    const SizedBox(height: 8),
                    Text(currentScenario.options[_selectedOptionIndex!].explanation),
                  ],
                ),
              ),
              
              const SizedBox(height: 16),
              
              // Navigation buttons
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  // Try again button
                  if (!currentScenario.options[_selectedOptionIndex!].isCorrect)
                    ElevatedButton.icon(
                      onPressed: _resetScenario,
                      icon: const Icon(Icons.refresh),
                      label: const Text('Try Again'),
                      style: ElevatedButton.styleFrom(
                        backgroundColor: Colors.grey.shade600,
                        foregroundColor: Colors.white,
                      ),
                    ),
                  
                  // Next button
                  if (currentScenario.options[_selectedOptionIndex!].isCorrect)
                    ElevatedButton.icon(
                      onPressed: _currentScenarioIndex < _scenarios.length - 1
                          ? _nextScenario
                          : null,
                      icon: const Icon(Icons.arrow_forward),
                      label: Text(
                        _currentScenarioIndex < _scenarios.length - 1
                            ? 'Next Scenario'
                            : 'Completed!',
                      ),
                      style: ElevatedButton.styleFrom(
                        backgroundColor: _highlightColor,
                        foregroundColor: Colors.white,
                      ),
                    ),
                ],
              ),
            ],
          ],
          
          // Widget name tag
          if (widget.data['showNameTag'] ?? true)
            Padding(
              padding: const EdgeInsets.only(top: 8),
              child: Text(
                'InteractiveConditionalFlowWidget',
                style: TextStyle(
                  color: Colors.grey[600],
                  fontSize: 12,
                  fontStyle: FontStyle.italic,
                ),
              ),
            ),
        ],
      ),
    );
  }
}

/// Custom painter for drawing the conditional flow diagram
class ConditionalFlowPainter extends CustomPainter {
  final String condition;
  final String consequent;
  final String alternative;
  final bool showAlternative;
  final bool conditionValue;
  final bool showResult;
  final double flowProgress;
  final Color trueColor;
  final Color falseColor;
  final Color neutralColor;
  final Color highlightColor;

  ConditionalFlowPainter({
    required this.condition,
    required this.consequent,
    required this.alternative,
    required this.showAlternative,
    required this.conditionValue,
    required this.showResult,
    required this.flowProgress,
    required this.trueColor,
    required this.falseColor,
    required this.neutralColor,
    required this.highlightColor,
  });

  @override
  void paint(Canvas canvas, Size size) {
    final paint = Paint()
      ..style = PaintingStyle.stroke
      ..strokeWidth = 2.0;
    
    final textPainter = TextPainter(
      textDirection: TextDirection.ltr,
      textAlign: TextAlign.center,
    );
    
    // Calculate positions
    final centerX = size.width / 2;
    final startY = size.height * 0.1;
    final conditionY = size.height * 0.3;
    final resultY = size.height * 0.7;
    final endY = size.height * 0.9;
    
    // Draw start node
    paint.color = neutralColor;
    paint.style = PaintingStyle.fill;
    canvas.drawCircle(Offset(centerX, startY), 10, paint);
    
    // Draw condition diamond
    final diamondPath = Path()
      ..moveTo(centerX, conditionY - 30)
      ..lineTo(centerX + 50, conditionY)
      ..lineTo(centerX, conditionY + 30)
      ..lineTo(centerX - 50, conditionY)
      ..close();
    
    paint.color = showResult
        ? (conditionValue ? trueColor.withOpacity(0.2) : falseColor.withOpacity(0.2))
        : neutralColor;
    canvas.drawPath(diamondPath, paint);
    
    paint.color = showResult
        ? (conditionValue ? trueColor : falseColor)
        : highlightColor;
    paint.style = PaintingStyle.stroke;
    canvas.drawPath(diamondPath, paint);
    
    // Draw condition text
    textPainter.text = TextSpan(
      text: condition,
      style: TextStyle(
        color: showResult
            ? (conditionValue ? trueColor : falseColor)
            : Colors.black87,
        fontWeight: FontWeight.bold,
      ),
    );
    textPainter.layout(minWidth: 0, maxWidth: 80);
    textPainter.paint(
      canvas,
      Offset(centerX - textPainter.width / 2, conditionY - textPainter.height / 2),
    );
    
    // Draw true/false labels
    textPainter.text = TextSpan(
      text: 'True',
      style: TextStyle(
        color: trueColor,
        fontWeight: FontWeight.bold,
      ),
    );
    textPainter.layout(minWidth: 0, maxWidth: 80);
    textPainter.paint(
      canvas,
      Offset(centerX + 60, conditionY - textPainter.height / 2),
    );
    
    textPainter.text = TextSpan(
      text: 'False',
      style: TextStyle(
        color: falseColor,
        fontWeight: FontWeight.bold,
      ),
    );
    textPainter.layout(minWidth: 0, maxWidth: 80);
    textPainter.paint(
      canvas,
      Offset(centerX - 60 - textPainter.width, conditionY - textPainter.height / 2),
    );
    
    // Draw result rectangles
    final trueRect = RRect.fromRectAndRadius(
      Rect.fromCenter(
        center: Offset(centerX + 100, resultY),
        width: 120,
        height: 50,
      ),
      const Radius.circular(10),
    );
    
    final falseRect = RRect.fromRectAndRadius(
      Rect.fromCenter(
        center: Offset(centerX - 100, resultY),
        width: 120,
        height: 50,
      ),
      const Radius.circular(10),
    );
    
    // Draw true path result
    paint.color = showResult && conditionValue
        ? trueColor.withOpacity(0.2)
        : neutralColor.withOpacity(0.2);
    paint.style = PaintingStyle.fill;
    canvas.drawRRect(trueRect, paint);
    
    paint.color = showResult && conditionValue ? trueColor : neutralColor;
    paint.style = PaintingStyle.stroke;
    canvas.drawRRect(trueRect, paint);
    
    // Draw false path result
    paint.color = showResult && !conditionValue
        ? falseColor.withOpacity(0.2)
        : neutralColor.withOpacity(0.2);
    paint.style = PaintingStyle.fill;
    canvas.drawRRect(falseRect, paint);
    
    paint.color = showResult && !conditionValue ? falseColor : neutralColor;
    paint.style = PaintingStyle.stroke;
    canvas.drawRRect(falseRect, paint);
    
    // Draw result text
    textPainter.text = TextSpan(
      text: consequent,
      style: TextStyle(
        color: showResult && conditionValue ? trueColor : Colors.black87,
        fontWeight: FontWeight.bold,
      ),
    );
    textPainter.layout(minWidth: 0, maxWidth: 100);
    textPainter.paint(
      canvas,
      Offset(
        centerX + 100 - textPainter.width / 2,
        resultY - textPainter.height / 2,
      ),
    );
    
    textPainter.text = TextSpan(
      text: alternative.isNotEmpty ? alternative : 'Not $consequent',
      style: TextStyle(
        color: showResult && !conditionValue ? falseColor : Colors.black87,
        fontWeight: FontWeight.bold,
      ),
    );
    textPainter.layout(minWidth: 0, maxWidth: 100);
    textPainter.paint(
      canvas,
      Offset(
        centerX - 100 - textPainter.width / 2,
        resultY - textPainter.height / 2,
      ),
    );
    
    // Draw end node
    paint.color = neutralColor;
    paint.style = PaintingStyle.fill;
    canvas.drawCircle(Offset(centerX, endY), 10, paint);
    
    // Draw flow lines
    if (showResult) {
      // Calculate animation progress points
      final startToConditionProgress = math.min(1.0, flowProgress * 3);
      final conditionToResultProgress = math.min(1.0, math.max(0.0, flowProgress * 3 - 1.0));
      final resultToEndProgress = math.min(1.0, math.max(0.0, flowProgress * 3 - 2.0));
      
      // Draw start to condition line
      paint.color = highlightColor;
      paint.strokeWidth = 3.0;
      
      if (startToConditionProgress > 0) {
        canvas.drawLine(
          Offset(centerX, startY + 10),
          Offset(centerX, startY + 10 + (conditionY - 30 - startY - 10) * startToConditionProgress),
          paint,
        );
      }
      
      // Draw condition to result line
      if (conditionToResultProgress > 0) {
        if (conditionValue) {
          // True path
          final controlPoint1 = Offset(centerX + 50, conditionY + 30);
          final controlPoint2 = Offset(centerX + 100, conditionY + 50);
          final endPoint = Offset(centerX + 100, resultY - 25);
          
          final path = Path()
            ..moveTo(centerX + 50, conditionY)
            ..cubicTo(
              controlPoint1.dx,
              controlPoint1.dy,
              controlPoint2.dx,
              controlPoint2.dy,
              endPoint.dx,
              endPoint.dy * conditionToResultProgress + (conditionY + 30) * (1 - conditionToResultProgress),
            );
          
          paint.color = trueColor;
          canvas.drawPath(path, paint);
        } else {
          // False path
          final controlPoint1 = Offset(centerX - 50, conditionY + 30);
          final controlPoint2 = Offset(centerX - 100, conditionY + 50);
          final endPoint = Offset(centerX - 100, resultY - 25);
          
          final path = Path()
            ..moveTo(centerX - 50, conditionY)
            ..cubicTo(
              controlPoint1.dx,
              controlPoint1.dy,
              controlPoint2.dx,
              controlPoint2.dy,
              endPoint.dx,
              endPoint.dy * conditionToResultProgress + (conditionY + 30) * (1 - conditionToResultProgress),
            );
          
          paint.color = falseColor;
          canvas.drawPath(path, paint);
        }
      }
      
      // Draw result to end line
      if (resultToEndProgress > 0) {
        if (conditionValue) {
          // True path to end
          final startPoint = Offset(centerX + 100, resultY + 25);
          final controlPoint1 = Offset(centerX + 100, resultY + 50);
          final controlPoint2 = Offset(centerX + 50, endY - 30);
          final endPoint = Offset(centerX, endY - 10);
          
          final path = Path()
            ..moveTo(startPoint.dx, startPoint.dy)
            ..cubicTo(
              controlPoint1.dx,
              controlPoint1.dy,
              controlPoint2.dx,
              controlPoint2.dy * resultToEndProgress + startPoint.dy * (1 - resultToEndProgress),
              endPoint.dx * resultToEndProgress + startPoint.dx * (1 - resultToEndProgress),
              endPoint.dy * resultToEndProgress + startPoint.dy * (1 - resultToEndProgress),
            );
          
          paint.color = trueColor;
          canvas.drawPath(path, paint);
        } else {
          // False path to end
          final startPoint = Offset(centerX - 100, resultY + 25);
          final controlPoint1 = Offset(centerX - 100, resultY + 50);
          final controlPoint2 = Offset(centerX - 50, endY - 30);
          final endPoint = Offset(centerX, endY - 10);
          
          final path = Path()
            ..moveTo(startPoint.dx, startPoint.dy)
            ..cubicTo(
              controlPoint1.dx,
              controlPoint1.dy,
              controlPoint2.dx,
              controlPoint2.dy * resultToEndProgress + startPoint.dy * (1 - resultToEndProgress),
              endPoint.dx * resultToEndProgress + startPoint.dx * (1 - resultToEndProgress),
              endPoint.dy * resultToEndProgress + startPoint.dy * (1 - resultToEndProgress),
            );
          
          paint.color = falseColor;
          canvas.drawPath(path, paint);
        }
      }
    } else {
      // Draw static flow lines
      paint.color = neutralColor;
      paint.strokeWidth = 2.0;
      
      // Start to condition
      canvas.drawLine(
        Offset(centerX, startY + 10),
        Offset(centerX, conditionY - 30),
        paint,
      );
      
      // Condition to true result
      final truePath = Path()
        ..moveTo(centerX + 50, conditionY)
        ..cubicTo(
          centerX + 50, conditionY + 30,
          centerX + 100, conditionY + 50,
          centerX + 100, resultY - 25,
        );
      canvas.drawPath(truePath, paint);
      
      // Condition to false result
      final falsePath = Path()
        ..moveTo(centerX - 50, conditionY)
        ..cubicTo(
          centerX - 50, conditionY + 30,
          centerX - 100, conditionY + 50,
          centerX - 100, resultY - 25,
        );
      canvas.drawPath(falsePath, paint);
      
      // True result to end
      final trueToEndPath = Path()
        ..moveTo(centerX + 100, resultY + 25)
        ..cubicTo(
          centerX + 100, resultY + 50,
          centerX + 50, endY - 30,
          centerX, endY - 10,
        );
      canvas.drawPath(trueToEndPath, paint);
      
      // False result to end
      final falseToEndPath = Path()
        ..moveTo(centerX - 100, resultY + 25)
        ..cubicTo(
          centerX - 100, resultY + 50,
          centerX - 50, endY - 30,
          centerX, endY - 10,
        );
      canvas.drawPath(falseToEndPath, paint);
    }
  }

  @override
  bool shouldRepaint(covariant ConditionalFlowPainter oldDelegate) {
    return oldDelegate.conditionValue != conditionValue ||
           oldDelegate.showResult != showResult ||
           oldDelegate.flowProgress != flowProgress;
  }
}

/// Represents a conditional scenario in the widget
class ConditionalScenario {
  final String conditionText;
  final bool conditionValue;
  final String consequentText;
  final String question;
  final List<ConditionalOption> options;

  ConditionalScenario({
    required this.conditionText,
    required this.conditionValue,
    required this.consequentText,
    required this.question,
    required this.options,
  });
}

/// Represents an option in a conditional scenario
class ConditionalOption {
  final String text;
  final bool isCorrect;
  final String explanation;

  ConditionalOption({
    required this.text,
    required this.isCorrect,
    required this.explanation,
  });
}
