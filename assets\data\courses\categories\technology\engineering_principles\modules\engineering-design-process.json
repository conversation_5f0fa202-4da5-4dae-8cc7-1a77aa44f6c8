{"id": "engineering-design-process", "title": "The Engineering Design Process in Detail", "description": "Deep dive into each stage of the engineering design process with practical examples.", "order": 1, "lessons": [{"id": "problem-definition-needs-assessment", "title": "Problem Definition and Needs Assessment", "description": "Clearly articulate the problem and user requirements.", "order": 1, "type": "interactive_lesson", "estimatedTimeMinutes": 20, "contentBlocks": []}, {"id": "ideation-brainstorming-techniques", "title": "Ideation and Brainstorming Techniques", "description": "Explore creative problem-solving methods.", "order": 2, "type": "interactive_lesson", "estimatedTimeMinutes": 25, "contentBlocks": []}, {"id": "concept-selection-feasibility-analysis", "title": "Concept Selection and Feasibility Analysis", "description": "Evaluate potential solutions against criteria.", "order": 3, "type": "interactive_lesson", "estimatedTimeMinutes": 20, "contentBlocks": []}, {"id": "detailed-design-prototyping", "title": "Detailed Design and Prototyping", "description": "Develop specifications and create working models.", "order": 4, "type": "interactive_lesson", "estimatedTimeMinutes": 30, "contentBlocks": []}, {"id": "testing-evaluation-iteration", "title": "Testing, Evaluation, and Iteration", "description": "Analyze performance and refine designs based on feedback.", "order": 5, "type": "interactive_lesson", "estimatedTimeMinutes": 25, "contentBlocks": []}, {"id": "module-test-master-designer", "title": "Module Test: The Master Designer", "description": "Apply the engineering design process to a given problem, outlining each stage.", "order": 6, "type": "module_test_interactive", "estimatedTimeMinutes": 35, "passingScorePercentage": 70, "contentBlocks": []}]}