import 'package:flutter/material.dart';
import 'dart:math' as math;
import 'dart:math' show Random;

/// Custom painter for number comparison visualization
class NumberComparisonVisualizationPainter extends CustomPainter {
  final double firstNumber;
  final double secondNumber;
  final int currentStep;
  final double animationValue;
  final Color primaryColor;
  final Color secondaryColor;
  final Color textColor;
  final String comparisonType;
  final bool showNumberLine;

  NumberComparisonVisualizationPainter({
    required this.firstNumber,
    required this.secondNumber,
    required this.currentStep,
    required this.animationValue,
    required this.primaryColor,
    required this.secondaryColor,
    required this.textColor,
    required this.comparisonType,
    required this.showNumberLine,
  });

  @override
  void paint(Canvas canvas, Size size) {
    if (showNumberLine) {
      _drawNumberLine(canvas, size);
    } else {
      _drawVisualComparison(canvas, size);
    }

    // Draw comparison symbol in step 2
    if (currentStep >= 2) {
      double symbolOpacity = currentStep == 2 ? animationValue : 1.0;

      String symbol = '';
      if (firstNumber < secondNumber) {
        symbol = '<';
      } else if (firstNumber > secondNumber) {
        symbol = '>';
      } else {
        symbol = '=';
      }

      _drawText(
        canvas,
        symbol,
        size.width / 2,
        size.height - 40,
        TextStyle(
          color: secondaryColor,
          fontSize: 48,
          fontWeight: FontWeight.bold,
        ),
        symbolOpacity,
      );
    }
  }

  void _drawNumberLine(Canvas canvas, Size size) {
    final double lineY = size.height / 2;
    final double maxNumber = math.max(firstNumber, secondNumber) + 5;
    final double minNumber = math.min(firstNumber, secondNumber) - 5;
    final double range = maxNumber - minNumber;
    final double pixelsPerUnit = (size.width - 40) / range;

    // Draw the number line
    final Paint linePaint = Paint()
      ..color = Colors.grey.shade400
      ..strokeWidth = 2
      ..style = PaintingStyle.stroke;

    canvas.drawLine(
      Offset(20, lineY),
      Offset(size.width - 20, lineY),
      linePaint,
    );

    // Draw tick marks and labels
    for (double i = minNumber; i <= maxNumber; i++) {
      final double x = 20 + (i - minNumber) * pixelsPerUnit;

      // Draw tick
      canvas.drawLine(
        Offset(x, lineY - 10),
        Offset(x, lineY + 10),
        linePaint,
      );

      // Draw label
      final textPainter = TextPainter(
        text: TextSpan(
          text: i.toString(),
          style: TextStyle(
            color: textColor,
            fontSize: 12,
          ),
        ),
        textDirection: TextDirection.ltr,
        textAlign: TextAlign.center,
      );
      textPainter.layout();
      textPainter.paint(
        canvas,
        Offset(x - textPainter.width / 2, lineY + 15),
      );
    }

    // Draw first number marker
    if (currentStep >= 0) {
      double firstOpacity = currentStep == 0 ? animationValue : 1.0;
      final double x1 = 20 + (firstNumber - minNumber) * pixelsPerUnit;

      canvas.drawCircle(
        Offset(x1, lineY),
        15,
        Paint()..color = primaryColor.withAlpha((255 * firstOpacity).toInt()),
      );

      // Draw label
      _drawText(
        canvas,
        firstNumber.toString(),
        x1,
        lineY - 30,
        TextStyle(
          color: primaryColor,
          fontSize: 18,
          fontWeight: FontWeight.bold,
        ),
        firstOpacity,
      );
    }

    // Draw second number marker
    if (currentStep >= 1) {
      double secondOpacity = currentStep == 1 ? animationValue : 1.0;
      final double x2 = 20 + (secondNumber - minNumber) * pixelsPerUnit;

      canvas.drawCircle(
        Offset(x2, lineY),
        15,
        Paint()..color = secondaryColor.withAlpha((255 * secondOpacity).toInt()),
      );

      // Draw label
      _drawText(
        canvas,
        secondNumber.toString(),
        x2,
        lineY - 30,
        TextStyle(
          color: secondaryColor,
          fontSize: 18,
          fontWeight: FontWeight.bold,
        ),
        secondOpacity,
      );
    }
  }

  void _drawVisualComparison(Canvas canvas, Size size) {
    final double objectSize = 20;
    final double objectSpacing = 10;
    final int maxObjectsPerRow = 10;

    // Calculate positions for first number objects
    final double startY1 = size.height / 4;
    final int rows1 = (firstNumber / maxObjectsPerRow).ceil();
    final double totalWidth1 = math.min(firstNumber, maxObjectsPerRow.toDouble()) * (objectSize + objectSpacing);
    final double startX1 = (size.width - totalWidth1) / 2;

    // Calculate positions for second number objects
    final double startY2 = size.height * 3 / 4;
    final int rows2 = (secondNumber / maxObjectsPerRow).ceil();
    final double totalWidth2 = math.min(secondNumber, maxObjectsPerRow.toDouble()) * (objectSize + objectSpacing);
    final double startX2 = (size.width - totalWidth2) / 2;

    // Draw first number objects
    if (currentStep >= 0) {
      double firstOpacity = currentStep == 0 ? animationValue : 1.0;
      int objectsToShow = currentStep == 0
          ? (firstNumber * animationValue).floor()
          : firstNumber.toInt();

      _drawText(
        canvas,
        'First Number: $firstNumber',
        size.width / 2,
        startY1 - 30,
        TextStyle(
          color: primaryColor,
          fontSize: 16,
          fontWeight: FontWeight.bold,
        ),
        firstOpacity,
      );

      for (int i = 0; i < objectsToShow; i++) {
        final int row = i ~/ maxObjectsPerRow;
        final int col = i % maxObjectsPerRow;

        final double x = startX1 + col * (objectSize + objectSpacing);
        final double y = startY1 + row * (objectSize + objectSpacing);

        canvas.drawCircle(
          Offset(x + objectSize / 2, y + objectSize / 2),
          objectSize / 2,
          Paint()..color = primaryColor.withAlpha((255 * firstOpacity).toInt()),
        );
      }
    }

    // Draw second number objects
    if (currentStep >= 1) {
      double secondOpacity = currentStep == 1 ? animationValue : 1.0;
      int objectsToShow = currentStep == 1
          ? (secondNumber * animationValue).floor()
          : secondNumber.toInt();

      _drawText(
        canvas,
        'Second Number: $secondNumber',
        size.width / 2,
        startY2 - 30,
        TextStyle(
          color: secondaryColor,
          fontSize: 16,
          fontWeight: FontWeight.bold,
        ),
        secondOpacity,
      );

      for (int i = 0; i < objectsToShow; i++) {
        final int row = i ~/ maxObjectsPerRow;
        final int col = i % maxObjectsPerRow;

        final double x = startX2 + col * (objectSize + objectSpacing);
        final double y = startY2 + row * (objectSize + objectSpacing);

        canvas.drawCircle(
          Offset(x + objectSize / 2, y + objectSize / 2),
          objectSize / 2,
          Paint()..color = secondaryColor.withAlpha((255 * secondOpacity).toInt()),
        );
      }
    }
  }

  void _drawText(Canvas canvas, String text, double x, double y, TextStyle style, double opacity) {
    final textPainter = TextPainter(
      text: TextSpan(
        text: text,
        style: style.copyWith(
          color: style.color!.withAlpha((style.color!.alpha * opacity).toInt()),
        ),
      ),
      textDirection: TextDirection.ltr,
      textAlign: TextAlign.center,
    );
    textPainter.layout();
    textPainter.paint(
      canvas,
      Offset(x - textPainter.width / 2, y - textPainter.height / 2),
    );
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) {
    return true;
  }
}

/// A widget that demonstrates number comparison for elementary math education.
class InteractiveNumberComparisonToolWidget extends StatefulWidget {
  final Map<String, dynamic> data;
  final Function(bool)? onStateChanged;
  final Color primaryColor;
  final Color secondaryColor;
  final Color backgroundColor;
  final Color textColor;

  const InteractiveNumberComparisonToolWidget({
    super.key,
    this.data = const {},
    this.onStateChanged,
    this.primaryColor = Colors.blue,
    this.secondaryColor = Colors.orange,
    this.backgroundColor = Colors.white,
    this.textColor = Colors.black87,
  });

  @override
  State<InteractiveNumberComparisonToolWidget> createState() =>
      _InteractiveNumberComparisonToolWidgetState();
}

class _InteractiveNumberComparisonToolWidgetState
    extends State<InteractiveNumberComparisonToolWidget>
    with SingleTickerProviderStateMixin {
  // Controllers
  late AnimationController _animationController;
  late Animation<double> _animation;
  final TextEditingController _firstNumberController = TextEditingController();
  final TextEditingController _secondNumberController = TextEditingController();

  // State variables
  bool _isAnimating = false;
  bool _isCompleted = false;
  int _currentStep = 0;
  List<String> _steps = [];
  String? _errorMessage;
  String? _feedbackMessage;
  bool _showQuestion = false;
  String? _selectedAnswer;
  String? _correctAnswer;
  List<String> _answerOptions = [];
  bool _showNumberLine = true;
  String _comparisonType = 'Visual';

  // Number values
  double _firstNumber = 0.0;
  double _secondNumber = 0.0;

  // Constants
  final int _maxNumber = 100;
  final List<String> _comparisonTypes = ['Visual', 'Number Line'];

  @override
  void initState() {
    super.initState();

    // Initialize animation controller
    _animationController = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 1500),
    );

    _animation = CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeInOut,
    );

    // Add listener to animation controller
    _animationController.addStatusListener((status) {
      if (status == AnimationStatus.completed) {
        if (_currentStep < _steps.length - 1) {
          setState(() {
            _currentStep++;
          });
          _animationController.reset();
          _animationController.forward();
        } else {
          setState(() {
            _isAnimating = false;
            _showQuestion = true;
          });
        }
      }
    });

    // Set initial values
    _firstNumberController.text = '5';
    _secondNumberController.text = '8';

    // Initialize with default values
    _updateNumbers();
  }

  @override
  void dispose() {
    _animationController.dispose();
    _firstNumberController.dispose();
    _secondNumberController.dispose();
    super.dispose();
  }

  void _updateNumbers() {
    try {
      _firstNumber = double.parse(_firstNumberController.text);
      _secondNumber = double.parse(_secondNumberController.text);

      // Validate input
      if (_firstNumber < 0 || _firstNumber > _maxNumber ||
          _secondNumber < 0 || _secondNumber > _maxNumber) {
        setState(() {
          _errorMessage = 'Please enter numbers between 0 and $_maxNumber';
        });
        return;
      }

      setState(() {
        _errorMessage = null;
        _feedbackMessage = null;
        _showQuestion = false;
        _selectedAnswer = null;
        _currentStep = 0;
      });

      // Generate steps
      _generateSteps();

      // Generate question and answers
      _generateQuestion();

    } catch (e) {
      setState(() {
        _errorMessage = 'Please enter valid numbers';
      });
    }
  }

  void _generateSteps() {
    _steps = [
      'Visualize the first number: $_firstNumber',
      'Visualize the second number: $_secondNumber',
      'Compare the numbers'
    ];
  }

  void _generateQuestion() {
    // Generate a question about number comparison
    String comparisonSymbol;
    if (_firstNumber < _secondNumber) {
      comparisonSymbol = '<';
      _correctAnswer = 'Less than';
    } else if (_firstNumber > _secondNumber) {
      comparisonSymbol = '>';
      _correctAnswer = 'Greater than';
    } else {
      comparisonSymbol = '=';
      _correctAnswer = 'Equal to';
    }

    // Generate answer options
    _answerOptions = ['Less than', 'Greater than', 'Equal to'];
    _answerOptions.shuffle();
  }

  void _startAnimation() {
    if (_steps.isEmpty) return;

    setState(() {
      _currentStep = 0;
      _isAnimating = true;
      _showQuestion = false;
    });

    _animationController.reset();
    _animationController.forward();
  }

  void _stopAnimation() {
    setState(() {
      _isAnimating = false;
    });
    _animationController.stop();
  }

  void _resetAnimation() {
    setState(() {
      _currentStep = 0;
      _isAnimating = false;
      _showQuestion = false;
      _selectedAnswer = null;
      _feedbackMessage = null;
    });
    _animationController.reset();
  }

  void _checkAnswer(String answer) {
    setState(() {
      _selectedAnswer = answer;
      if (answer == _correctAnswer) {
        _feedbackMessage = 'Correct! Great job!';
        _isCompleted = true;
      } else {
        _feedbackMessage = 'Not quite. Try again!';
      }
    });

    // Notify parent of completion status
    widget.onStateChanged?.call(_isCompleted);
  }

  void _generateRandomNumbers() {
    Random random = Random();
    int first = random.nextInt(_maxNumber);
    int second = random.nextInt(_maxNumber);

    setState(() {
      _firstNumberController.text = first.toString();
      _secondNumberController.text = second.toString();
    });

    _updateNumbers();
  }

  void _toggleVisualizationType() {
    setState(() {
      _showNumberLine = !_showNumberLine;
      _comparisonType = _showNumberLine ? 'Number Line' : 'Visual';
    });
  }

  Widget _buildInputControls() {
    return Column(
      children: [
        // Number inputs
        Row(
          children: [
            Expanded(
              child: TextField(
                controller: _firstNumberController,
                keyboardType: TextInputType.number,
                decoration: InputDecoration(
                  labelText: 'First Number',
                  border: OutlineInputBorder(),
                  contentPadding: EdgeInsets.symmetric(horizontal: 12, vertical: 8),
                ),
                onChanged: (_) => _updateNumbers(),
              ),
            ),
            Padding(
              padding: const EdgeInsets.symmetric(horizontal: 16),
              child: Text(
                '?',
                style: TextStyle(
                  fontSize: 24,
                  fontWeight: FontWeight.bold,
                  color: widget.primaryColor,
                ),
              ),
            ),
            Expanded(
              child: TextField(
                controller: _secondNumberController,
                keyboardType: TextInputType.number,
                decoration: InputDecoration(
                  labelText: 'Second Number',
                  border: OutlineInputBorder(),
                  contentPadding: EdgeInsets.symmetric(horizontal: 12, vertical: 8),
                ),
                onChanged: (_) => _updateNumbers(),
              ),
            ),
            const SizedBox(width: 16),
            ElevatedButton(
              onPressed: _generateRandomNumbers,
              style: ElevatedButton.styleFrom(
                backgroundColor: widget.secondaryColor,
                padding: EdgeInsets.symmetric(horizontal: 12, vertical: 8),
              ),
              child: Text('Random'),
            ),
          ],
        ),

        const SizedBox(height: 16),

        // Visualization type toggle
        Row(
          children: [
            Text(
              'Visualization Type:',
              style: TextStyle(
                fontWeight: FontWeight.bold,
                color: widget.textColor,
              ),
            ),
            const SizedBox(width: 16),
            SegmentedButton<String>(
              segments: _comparisonTypes.map((type) =>
                ButtonSegment<String>(
                  value: type,
                  label: Text(type),
                )
              ).toList(),
              selected: {_comparisonType},
              onSelectionChanged: (Set<String> newSelection) {
                if (newSelection.isNotEmpty) {
                  setState(() {
                    _comparisonType = newSelection.first;
                    _showNumberLine = _comparisonType == 'Number Line';
                  });
                }
              },
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildVisualizationArea() {
    return Container(
      height: 250,
      width: double.infinity,
      decoration: BoxDecoration(
        color: Colors.grey.shade50,
        borderRadius: BorderRadius.circular(8),
        border: Border.all(
          color: _isCompleted ? Colors.green : Colors.grey.shade300,
          width: _isCompleted ? 2 : 1,
        ),
      ),
      child: AnimatedBuilder(
        animation: _animation,
        builder: (context, child) {
          return CustomPaint(
            painter: NumberComparisonVisualizationPainter(
              firstNumber: _firstNumber,
              secondNumber: _secondNumber,
              currentStep: _currentStep,
              animationValue: _isAnimating ? _animation.value : 1.0,
              primaryColor: widget.primaryColor,
              secondaryColor: widget.secondaryColor,
              textColor: widget.textColor,
              comparisonType: _comparisonType,
              showNumberLine: _showNumberLine,
            ),
            child: Container(),
          );
        },
      ),
    );
  }

  Widget _buildAnimationControls() {
    return Row(
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        IconButton(
          icon: Icon(_isAnimating ? Icons.pause : Icons.play_arrow),
          onPressed: _isAnimating ? _stopAnimation : _startAnimation,
          color: widget.primaryColor,
          iconSize: 32,
        ),
        IconButton(
          icon: const Icon(Icons.replay),
          onPressed: _resetAnimation,
          color: widget.primaryColor,
          iconSize: 32,
        ),
      ],
    );
  }

  Widget _buildQuestionSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'How does $_firstNumber compare to $_secondNumber?',
          style: TextStyle(
            fontSize: 18,
            fontWeight: FontWeight.bold,
            color: widget.textColor,
          ),
        ),
        const SizedBox(height: 12),
        Wrap(
          spacing: 8,
          runSpacing: 8,
          children: _answerOptions.map((option) {
            bool isSelected = _selectedAnswer == option;
            bool isCorrect = option == _correctAnswer;

            Color buttonColor = isSelected
                ? (isCorrect ? Colors.green : Colors.red)
                : widget.primaryColor;

            return ElevatedButton(
              onPressed: _selectedAnswer == null ? () => _checkAnswer(option) : null,
              style: ElevatedButton.styleFrom(
                backgroundColor: buttonColor,
                padding: EdgeInsets.symmetric(horizontal: 16, vertical: 8),
              ),
              child: Text(
                option,
                style: TextStyle(
                  fontSize: 16,
                  color: Colors.white,
                ),
              ),
            );
          }).toList(),
        ),
      ],
    );
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: widget.backgroundColor,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withAlpha(50),
            spreadRadius: 1,
            blurRadius: 3,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Title
          Text(
            'Number Comparison Tool',
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
              color: widget.textColor,
            ),
          ),

          const SizedBox(height: 16),

          // Input controls
          _buildInputControls(),

          if (_errorMessage != null)
            Padding(
              padding: const EdgeInsets.only(top: 8.0),
              child: Text(
                _errorMessage!,
                style: TextStyle(
                  color: Colors.red,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ),

          const SizedBox(height: 16),

          // Visualization area
          _buildVisualizationArea(),

          const SizedBox(height: 16),

          // Step description
          if (_steps.isNotEmpty && _currentStep < _steps.length)
            Text(
              _steps[_currentStep],
              style: TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.bold,
                color: widget.primaryColor,
              ),
            ),

          const SizedBox(height: 16),

          // Animation controls
          _buildAnimationControls(),

          const SizedBox(height: 16),

          // Question and feedback
          if (_showQuestion) _buildQuestionSection(),

          if (_feedbackMessage != null)
            Padding(
              padding: const EdgeInsets.only(top: 8.0),
              child: Text(
                _feedbackMessage!,
                style: TextStyle(
                  color: _selectedAnswer == _correctAnswer
                      ? Colors.green
                      : Colors.red,
                  fontWeight: FontWeight.bold,
                  fontSize: 16,
                ),
              ),
            ),
        ],
      ),
    );
  }
}
