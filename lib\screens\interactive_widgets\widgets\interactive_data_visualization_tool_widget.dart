import 'package:flutter/material.dart';
import 'dart:math' as math;

/// Model class for a data point
class DataPoint {
  final double x;
  final double y;
  final String label;

  DataPoint({
    required this.x,
    required this.y,
    required this.label,
  });
}

/// Model class for a data set
class DataSet {
  final String title;
  final String description;
  final String xAxisLabel;
  final String yAxisLabel;
  final List<DataPoint> dataPoints;
  final String explanation;

  DataSet({
    required this.title,
    required this.description,
    required this.xAxisLabel,
    required this.yAxisLabel,
    required this.dataPoints,
    required this.explanation,
  });
}

/// Custom painter for line charts
class LineChartPainter extends CustomPainter {
  final List<DataPoint> dataPoints;
  final double maxY;
  final Color lineColor;
  final Color pointColor;

  LineChartPainter({
    required this.dataPoints,
    required this.maxY,
    required this.lineColor,
    required this.pointColor,
  });

  @override
  void paint(Canvas canvas, Size size) {
    final paint = Paint()
      ..color = lineColor
      ..strokeWidth = 2
      ..style = PaintingStyle.stroke;

    final pointPaint = Paint()
      ..color = pointColor
      ..strokeWidth = 1
      ..style = PaintingStyle.fill;

    final path = Path();

    // Draw grid lines
    final gridPaint = Paint()
      ..color = Colors.grey.withOpacity(0.2)
      ..strokeWidth = 1
      ..style = PaintingStyle.stroke;

    // Horizontal grid lines
    for (int i = 0; i <= 4; i++) {
      final y = size.height - (i * size.height / 4);
      canvas.drawLine(
        Offset(0, y),
        Offset(size.width, y),
        gridPaint,
      );
    }

    // Vertical grid lines
    for (int i = 0; i <= dataPoints.length - 1; i++) {
      final x = i * size.width / (dataPoints.length - 1);
      canvas.drawLine(
        Offset(x, 0),
        Offset(x, size.height),
        gridPaint,
      );
    }

    // Draw line chart
    for (int i = 0; i < dataPoints.length; i++) {
      final point = dataPoints[i];
      final x = i * size.width / (dataPoints.length - 1);
      final y = size.height - (point.y / maxY * size.height);

      if (i == 0) {
        path.moveTo(x, y);
      } else {
        path.lineTo(x, y);
      }

      // Draw points
      canvas.drawCircle(Offset(x, y), 4, pointPaint);
    }

    canvas.drawPath(path, paint);
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => true;
}

/// Custom painter for pie charts
class PieChartPainter extends CustomPainter {
  final List<DataPoint> dataPoints;
  final double total;
  final List<Color> colors;

  PieChartPainter({
    required this.dataPoints,
    required this.total,
    required this.colors,
  });

  @override
  void paint(Canvas canvas, Size size) {
    final center = Offset(size.width / 2, size.height / 2);
    final radius = math.min(size.width, size.height) / 2;

    double startAngle = -math.pi / 2; // Start from the top

    for (int i = 0; i < dataPoints.length; i++) {
      final point = dataPoints[i];
      final sweepAngle = 2 * math.pi * (point.y / total);

      final paint = Paint()
        ..color = colors[i % colors.length]
        ..style = PaintingStyle.fill;

      canvas.drawArc(
        Rect.fromCircle(center: center, radius: radius),
        startAngle,
        sweepAngle,
        true,
        paint,
      );

      startAngle += sweepAngle;
    }

    // Draw a small white circle in the center for a donut chart effect
    final centerPaint = Paint()
      ..color = Colors.white
      ..style = PaintingStyle.fill;

    canvas.drawCircle(center, radius * 0.3, centerPaint);
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => true;
}

/// Custom painter for scatter plots
class ScatterPlotPainter extends CustomPainter {
  final List<DataPoint> dataPoints;
  final double maxX;
  final double maxY;
  final Color pointColor;
  final Color gridColor;

  ScatterPlotPainter({
    required this.dataPoints,
    required this.maxX,
    required this.maxY,
    required this.pointColor,
    required this.gridColor,
  });

  @override
  void paint(Canvas canvas, Size size) {
    final paint = Paint()
      ..color = pointColor
      ..strokeWidth = 1
      ..style = PaintingStyle.fill;

    final gridPaint = Paint()
      ..color = gridColor
      ..strokeWidth = 1
      ..style = PaintingStyle.stroke;

    // Draw grid lines
    // Horizontal grid lines
    for (int i = 0; i <= 4; i++) {
      final y = size.height - (i * size.height / 4);
      canvas.drawLine(
        Offset(0, y),
        Offset(size.width, y),
        gridPaint,
      );
    }

    // Vertical grid lines
    for (int i = 0; i <= 4; i++) {
      final x = i * size.width / 4;
      canvas.drawLine(
        Offset(x, 0),
        Offset(x, size.height),
        gridPaint,
      );
    }

    // Draw points
    for (final point in dataPoints) {
      final x = (point.x / maxX) * size.width;
      final y = size.height - (point.y / maxY * size.height);

      canvas.drawCircle(Offset(x, y), 5, paint);
    }
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => true;
}

/// A widget that allows users to visualize and explore data through different chart types
/// Users can switch between different visualization methods and analyze data patterns
class InteractiveDataVisualizationToolWidget extends StatefulWidget {
  /// The data for this widget
  final Map<String, dynamic> data;

  /// Optional callback when the widget state changes
  final Function(bool isCompleted)? onStateChanged;

  const InteractiveDataVisualizationToolWidget({
    Key? key,
    required this.data,
    this.onStateChanged,
  }) : super(key: key);

  /// Factory constructor to create the widget from JSON data
  factory InteractiveDataVisualizationToolWidget.fromData(Map<String, dynamic> data) {
    return InteractiveDataVisualizationToolWidget(
      data: data,
    );
  }

  @override
  State<InteractiveDataVisualizationToolWidget> createState() => _InteractiveDataVisualizationToolWidgetState();
}

class _InteractiveDataVisualizationToolWidgetState extends State<InteractiveDataVisualizationToolWidget> {
  // Data sets
  late List<DataSet> _dataSets;
  late int _currentDataSetIndex;

  // Visualization options
  late List<String> _visualizationTypes;
  late String _currentVisualizationType;

  // UI state
  late bool _isCompleted;
  late bool _showExplanation;
  late bool _showStatistics;

  // Colors
  late Color _primaryColor;
  late Color _secondaryColor;
  late Color _accentColor;
  late Color _backgroundColor;
  late Color _textColor;
  late List<Color> _chartColors;

  @override
  void initState() {
    super.initState();

    // Initialize colors
    _primaryColor = _getColorFromHex(widget.data['primaryColor'] ?? '#2196F3');
    _secondaryColor = _getColorFromHex(widget.data['secondaryColor'] ?? '#FF9800');
    _accentColor = _getColorFromHex(widget.data['accentColor'] ?? '#4CAF50');
    _backgroundColor = _getColorFromHex(widget.data['backgroundColor'] ?? '#FFFFFF');
    _textColor = _getColorFromHex(widget.data['textColor'] ?? '#212121');

    // Initialize chart colors
    _chartColors = [
      _getColorFromHex('#2196F3'), // Blue
      _getColorFromHex('#FF9800'), // Orange
      _getColorFromHex('#4CAF50'), // Green
      _getColorFromHex('#F44336'), // Red
      _getColorFromHex('#9C27B0'), // Purple
      _getColorFromHex('#FFEB3B'), // Yellow
      _getColorFromHex('#795548'), // Brown
      _getColorFromHex('#607D8B'), // Blue Grey
    ];

    // Initialize data sets
    _initializeDataSets();

    // Initialize visualization types
    _visualizationTypes = [
      'Bar Chart',
      'Line Chart',
      'Pie Chart',
      'Scatter Plot',
    ];
    _currentVisualizationType = _visualizationTypes[0];

    // Initialize state
    _currentDataSetIndex = 0;
    _isCompleted = false;
    _showExplanation = false;
    _showStatistics = false;
  }

  // Initialize the data sets
  void _initializeDataSets() {
    final List<dynamic> dataSetsData = widget.data['dataSets'] ?? _getDefaultDataSets();

    _dataSets = dataSetsData.map((dataSetData) {
      return DataSet(
        title: dataSetData['title'] ?? '',
        description: dataSetData['description'] ?? '',
        xAxisLabel: dataSetData['xAxisLabel'] ?? '',
        yAxisLabel: dataSetData['yAxisLabel'] ?? '',
        dataPoints: List<DataPoint>.from(
          (dataSetData['dataPoints'] ?? []).map(
            (dataPoint) => DataPoint(
              x: dataPoint['x'] ?? 0,
              y: dataPoint['y'] ?? 0,
              label: dataPoint['label'] ?? '',
            ),
          ),
        ),
        explanation: dataSetData['explanation'] ?? '',
      );
    }).toList();
  }

  // Get default data sets if none are provided
  List<Map<String, dynamic>> _getDefaultDataSets() {
    return [
      {
        'title': 'Monthly Temperature Data',
        'description': 'Average monthly temperatures for a city over a year.',
        'xAxisLabel': 'Month',
        'yAxisLabel': 'Temperature (°C)',
        'dataPoints': [
          {'x': 1, 'y': 5, 'label': 'Jan'},
          {'x': 2, 'y': 7, 'label': 'Feb'},
          {'x': 3, 'y': 10, 'label': 'Mar'},
          {'x': 4, 'y': 14, 'label': 'Apr'},
          {'x': 5, 'y': 18, 'label': 'May'},
          {'x': 6, 'y': 22, 'label': 'Jun'},
          {'x': 7, 'y': 25, 'label': 'Jul'},
          {'x': 8, 'y': 24, 'label': 'Aug'},
          {'x': 9, 'y': 20, 'label': 'Sep'},
          {'x': 10, 'y': 15, 'label': 'Oct'},
          {'x': 11, 'y': 10, 'label': 'Nov'},
          {'x': 12, 'y': 6, 'label': 'Dec'},
        ],
        'explanation': 'This chart shows the seasonal temperature pattern with higher temperatures in summer months (June-August) and lower temperatures in winter months (December-February). The data follows a sinusoidal pattern typical of annual temperature cycles in temperate regions.',
      },
      {
        'title': 'Student Test Scores',
        'description': 'Distribution of test scores for a class of 30 students.',
        'xAxisLabel': 'Score Range',
        'yAxisLabel': 'Number of Students',
        'dataPoints': [
          {'x': 1, 'y': 2, 'label': '0-10'},
          {'x': 2, 'y': 3, 'label': '11-20'},
          {'x': 3, 'y': 4, 'label': '21-30'},
          {'x': 4, 'y': 5, 'label': '31-40'},
          {'x': 5, 'y': 7, 'label': '41-50'},
          {'x': 6, 'y': 4, 'label': '51-60'},
          {'x': 7, 'y': 3, 'label': '61-70'},
          {'x': 8, 'y': 1, 'label': '71-80'},
          {'x': 9, 'y': 1, 'label': '81-90'},
          {'x': 10, 'y': 0, 'label': '91-100'},
        ],
        'explanation': 'This chart shows a normal distribution of test scores with most students scoring in the middle range (41-50) and fewer students at the extreme ends. This is a typical pattern for test scores and follows a bell curve distribution.',
      },
      {
        'title': 'Plant Growth Experiment',
        'description': 'Plant height (cm) measured over 8 weeks with different amounts of sunlight.',
        'xAxisLabel': 'Week',
        'yAxisLabel': 'Height (cm)',
        'dataPoints': [
          {'x': 1, 'y': 2, 'label': 'Week 1'},
          {'x': 2, 'y': 5, 'label': 'Week 2'},
          {'x': 3, 'y': 9, 'label': 'Week 3'},
          {'x': 4, 'y': 14, 'label': 'Week 4'},
          {'x': 5, 'y': 18, 'label': 'Week 5'},
          {'x': 6, 'y': 21, 'label': 'Week 6'},
          {'x': 7, 'y': 23, 'label': 'Week 7'},
          {'x': 8, 'y': 24, 'label': 'Week 8'},
        ],
        'explanation': 'This chart shows an exponential growth pattern initially (weeks 1-4) followed by a plateau (weeks 6-8). This is typical of plant growth where rapid growth occurs in the early stages and then slows down as the plant matures.',
      },
      {
        'title': 'Energy Sources',
        'description': 'Distribution of energy sources for electricity generation.',
        'xAxisLabel': 'Energy Source',
        'yAxisLabel': 'Percentage (%)',
        'dataPoints': [
          {'x': 1, 'y': 38, 'label': 'Coal'},
          {'x': 2, 'y': 23, 'label': 'Natural Gas'},
          {'x': 3, 'y': 20, 'label': 'Nuclear'},
          {'x': 4, 'y': 7, 'label': 'Hydro'},
          {'x': 5, 'y': 6, 'label': 'Wind'},
          {'x': 6, 'y': 3, 'label': 'Solar'},
          {'x': 7, 'y': 3, 'label': 'Biomass'},
        ],
        'explanation': 'This chart shows that fossil fuels (coal and natural gas) still dominate electricity generation, accounting for 61% of the total. Renewable sources (hydro, wind, solar, biomass) together make up 19%, while nuclear provides 20%. This visualization helps understand the current energy mix and the potential for transitioning to more renewable sources.',
      },
    ];
  }

  // Get color from hex string
  Color _getColorFromHex(String hexColor) {
    hexColor = hexColor.replaceAll('#', '');
    if (hexColor.length == 6) {
      hexColor = 'FF$hexColor';
    }
    return Color(int.parse(hexColor, radix: 16));
  }

  // Go to the next data set
  void _nextDataSet() {
    if (_currentDataSetIndex < _dataSets.length - 1) {
      setState(() {
        _currentDataSetIndex++;
        _showExplanation = false;
        _showStatistics = false;
      });
    } else if (!_isCompleted) {
      setState(() {
        _isCompleted = true;
      });
      widget.onStateChanged?.call(true);
    }
  }

  // Go to the previous data set
  void _previousDataSet() {
    if (_currentDataSetIndex > 0) {
      setState(() {
        _currentDataSetIndex--;
        _showExplanation = false;
        _showStatistics = false;
      });
    }
  }

  // Change the visualization type
  void _changeVisualizationType(String type) {
    setState(() {
      _currentVisualizationType = type;
    });
  }

  // Toggle explanation visibility
  void _toggleExplanation() {
    setState(() {
      _showExplanation = !_showExplanation;
    });
  }

  // Toggle statistics visibility
  void _toggleStatistics() {
    setState(() {
      _showStatistics = !_showStatistics;
    });
  }

  // Reset the widget
  void _resetWidget() {
    setState(() {
      _currentDataSetIndex = 0;
      _currentVisualizationType = _visualizationTypes[0];
      _isCompleted = false;
      _showExplanation = false;
      _showStatistics = false;
    });
  }

  // Calculate basic statistics for the current data set
  Map<String, double> _calculateStatistics() {
    final dataSet = _dataSets[_currentDataSetIndex];
    final dataPoints = dataSet.dataPoints;

    // Calculate mean
    double sum = 0;
    for (var point in dataPoints) {
      sum += point.y;
    }
    double mean = sum / dataPoints.length;

    // Calculate median
    List<double> sortedValues = dataPoints.map((point) => point.y).toList()..sort();
    double median;
    if (sortedValues.length % 2 == 0) {
      median = (sortedValues[sortedValues.length ~/ 2 - 1] + sortedValues[sortedValues.length ~/ 2]) / 2;
    } else {
      median = sortedValues[sortedValues.length ~/ 2];
    }

    // Calculate min and max
    double min = sortedValues.first;
    double max = sortedValues.last;

    // Calculate standard deviation
    double sumSquaredDiff = 0;
    for (var point in dataPoints) {
      sumSquaredDiff += math.pow(point.y - mean, 2);
    }
    double variance = sumSquaredDiff / dataPoints.length;
    double stdDev = math.sqrt(variance);

    return {
      'mean': mean,
      'median': median,
      'min': min,
      'max': max,
      'stdDev': stdDev,
    };
  }

  // Build the appropriate visualization based on the selected type
  Widget _buildVisualization(DataSet dataSet) {
    switch (_currentVisualizationType) {
      case 'Bar Chart':
        return _buildBarChart(dataSet);
      case 'Line Chart':
        return _buildLineChart(dataSet);
      case 'Pie Chart':
        return _buildPieChart(dataSet);
      case 'Scatter Plot':
        return _buildScatterPlot(dataSet);
      default:
        return _buildBarChart(dataSet);
    }
  }

  // Build a bar chart visualization
  Widget _buildBarChart(DataSet dataSet) {
    final maxY = dataSet.dataPoints.map((point) => point.y).reduce(math.max);
    final barWidth = 300 / dataSet.dataPoints.length;

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Y-axis label
        Padding(
          padding: const EdgeInsets.only(bottom: 4),
          child: Text(
            dataSet.yAxisLabel,
            style: TextStyle(
              fontSize: 12,
              color: _textColor.withOpacity(0.7),
            ),
          ),
        ),

        // Chart area
        Expanded(
          child: Row(
            crossAxisAlignment: CrossAxisAlignment.end,
            children: [
              // Y-axis
              Column(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                crossAxisAlignment: CrossAxisAlignment.end,
                children: [
                  Text(
                    maxY.toStringAsFixed(0),
                    style: TextStyle(
                      fontSize: 10,
                      color: _textColor.withOpacity(0.7),
                    ),
                  ),
                  Text(
                    (maxY / 2).toStringAsFixed(0),
                    style: TextStyle(
                      fontSize: 10,
                      color: _textColor.withOpacity(0.7),
                    ),
                  ),
                  Text(
                    '0',
                    style: TextStyle(
                      fontSize: 10,
                      color: _textColor.withOpacity(0.7),
                    ),
                  ),
                ],
              ),

              const SizedBox(width: 8),

              // Bars
              Expanded(
                child: Row(
                  crossAxisAlignment: CrossAxisAlignment.end,
                  mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                  children: dataSet.dataPoints.asMap().entries.map((entry) {
                    final index = entry.key;
                    final point = entry.value;
                    final barHeight = (point.y / maxY) * 180; // Scale to fit in the container

                    return Column(
                      mainAxisAlignment: MainAxisAlignment.end,
                      children: [
                        // Bar
                        Container(
                          width: barWidth - 4,
                          height: barHeight,
                          decoration: BoxDecoration(
                            color: _chartColors[index % _chartColors.length],
                            borderRadius: const BorderRadius.only(
                              topLeft: Radius.circular(4),
                              topRight: Radius.circular(4),
                            ),
                          ),
                        ),

                        // X-axis label
                        Padding(
                          padding: const EdgeInsets.only(top: 4),
                          child: Text(
                            point.label,
                            style: TextStyle(
                              fontSize: 10,
                              color: _textColor.withOpacity(0.7),
                            ),
                            overflow: TextOverflow.ellipsis,
                          ),
                        ),
                      ],
                    );
                  }).toList(),
                ),
              ),
            ],
          ),
        ),

        // X-axis label
        Padding(
          padding: const EdgeInsets.only(top: 8),
          child: Center(
            child: Text(
              dataSet.xAxisLabel,
              style: TextStyle(
                fontSize: 12,
                color: _textColor.withOpacity(0.7),
              ),
            ),
          ),
        ),
      ],
    );
  }

  // Build a line chart visualization
  Widget _buildLineChart(DataSet dataSet) {
    final maxY = dataSet.dataPoints.map((point) => point.y).reduce(math.max);

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Y-axis label
        Padding(
          padding: const EdgeInsets.only(bottom: 4),
          child: Text(
            dataSet.yAxisLabel,
            style: TextStyle(
              fontSize: 12,
              color: _textColor.withOpacity(0.7),
            ),
          ),
        ),

        // Chart area
        Expanded(
          child: Row(
            crossAxisAlignment: CrossAxisAlignment.end,
            children: [
              // Y-axis
              Column(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                crossAxisAlignment: CrossAxisAlignment.end,
                children: [
                  Text(
                    maxY.toStringAsFixed(0),
                    style: TextStyle(
                      fontSize: 10,
                      color: _textColor.withOpacity(0.7),
                    ),
                  ),
                  Text(
                    (maxY / 2).toStringAsFixed(0),
                    style: TextStyle(
                      fontSize: 10,
                      color: _textColor.withOpacity(0.7),
                    ),
                  ),
                  Text(
                    '0',
                    style: TextStyle(
                      fontSize: 10,
                      color: _textColor.withOpacity(0.7),
                    ),
                  ),
                ],
              ),

              const SizedBox(width: 8),

              // Line chart
              Expanded(
                child: CustomPaint(
                  size: const Size(double.infinity, 180),
                  painter: LineChartPainter(
                    dataPoints: dataSet.dataPoints,
                    maxY: maxY,
                    lineColor: _primaryColor,
                    pointColor: _accentColor,
                  ),
                  child: Container(), // Empty container for the custom painter
                ),
              ),
            ],
          ),
        ),

        // X-axis labels
        SizedBox(
          height: 20,
          child: Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                dataSet.dataPoints.first.label,
                style: TextStyle(
                  fontSize: 10,
                  color: _textColor.withOpacity(0.7),
                ),
              ),
              if (dataSet.dataPoints.length > 2)
                Text(
                  dataSet.dataPoints[dataSet.dataPoints.length ~/ 2].label,
                  style: TextStyle(
                    fontSize: 10,
                    color: _textColor.withOpacity(0.7),
                  ),
                ),
              Text(
                dataSet.dataPoints.last.label,
                style: TextStyle(
                  fontSize: 10,
                  color: _textColor.withOpacity(0.7),
                ),
              ),
            ],
          ),
        ),

        // X-axis label
        Padding(
          padding: const EdgeInsets.only(top: 4),
          child: Center(
            child: Text(
              dataSet.xAxisLabel,
              style: TextStyle(
                fontSize: 12,
                color: _textColor.withOpacity(0.7),
              ),
            ),
          ),
        ),
      ],
    );
  }

  // Build a pie chart visualization
  Widget _buildPieChart(DataSet dataSet) {
    final total = dataSet.dataPoints.map((point) => point.y).reduce((a, b) => a + b);

    return Column(
      children: [
        // Chart
        Expanded(
          child: CustomPaint(
            size: const Size(200, 200),
            painter: PieChartPainter(
              dataPoints: dataSet.dataPoints,
              total: total,
              colors: _chartColors,
            ),
          ),
        ),

        // Legend
        SizedBox(
          height: 60,
          child: ListView.builder(
            scrollDirection: Axis.horizontal,
            itemCount: dataSet.dataPoints.length,
            itemBuilder: (context, index) {
              final point = dataSet.dataPoints[index];
              final percentage = (point.y / total * 100).toStringAsFixed(1);

              return Padding(
                padding: const EdgeInsets.symmetric(horizontal: 8),
                child: Row(
                  children: [
                    Container(
                      width: 12,
                      height: 12,
                      color: _chartColors[index % _chartColors.length],
                    ),
                    const SizedBox(width: 4),
                    Text(
                      '${point.label}: ${percentage}%',
                      style: TextStyle(
                        fontSize: 12,
                        color: _textColor,
                      ),
                    ),
                  ],
                ),
              );
            },
          ),
        ),
      ],
    );
  }

  // Build a scatter plot visualization
  Widget _buildScatterPlot(DataSet dataSet) {
    final maxY = dataSet.dataPoints.map((point) => point.y).reduce(math.max);
    final maxX = dataSet.dataPoints.map((point) => point.x).reduce(math.max);

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Y-axis label
        Padding(
          padding: const EdgeInsets.only(bottom: 4),
          child: Text(
            dataSet.yAxisLabel,
            style: TextStyle(
              fontSize: 12,
              color: _textColor.withOpacity(0.7),
            ),
          ),
        ),

        // Chart area
        Expanded(
          child: Row(
            crossAxisAlignment: CrossAxisAlignment.end,
            children: [
              // Y-axis
              Column(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                crossAxisAlignment: CrossAxisAlignment.end,
                children: [
                  Text(
                    maxY.toStringAsFixed(0),
                    style: TextStyle(
                      fontSize: 10,
                      color: _textColor.withOpacity(0.7),
                    ),
                  ),
                  Text(
                    (maxY / 2).toStringAsFixed(0),
                    style: TextStyle(
                      fontSize: 10,
                      color: _textColor.withOpacity(0.7),
                    ),
                  ),
                  Text(
                    '0',
                    style: TextStyle(
                      fontSize: 10,
                      color: _textColor.withOpacity(0.7),
                    ),
                  ),
                ],
              ),

              const SizedBox(width: 8),

              // Scatter plot
              Expanded(
                child: CustomPaint(
                  size: const Size(double.infinity, 180),
                  painter: ScatterPlotPainter(
                    dataPoints: dataSet.dataPoints,
                    maxX: maxX,
                    maxY: maxY,
                    pointColor: _accentColor,
                    gridColor: Colors.grey.withOpacity(0.2),
                  ),
                  child: Container(), // Empty container for the custom painter
                ),
              ),
            ],
          ),
        ),

        // X-axis labels
        SizedBox(
          height: 20,
          child: Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                '0',
                style: TextStyle(
                  fontSize: 10,
                  color: _textColor.withOpacity(0.7),
                ),
              ),
              Text(
                (maxX / 2).toStringAsFixed(0),
                style: TextStyle(
                  fontSize: 10,
                  color: _textColor.withOpacity(0.7),
                ),
              ),
              Text(
                maxX.toStringAsFixed(0),
                style: TextStyle(
                  fontSize: 10,
                  color: _textColor.withOpacity(0.7),
                ),
              ),
            ],
          ),
        ),

        // X-axis label
        Padding(
          padding: const EdgeInsets.only(top: 4),
          child: Center(
            child: Text(
              dataSet.xAxisLabel,
              style: TextStyle(
                fontSize: 12,
                color: _textColor.withOpacity(0.7),
              ),
            ),
          ),
        ),
      ],
    );
  }

  // Build the statistics panel
  Widget _buildStatisticsPanel() {
    final stats = _calculateStatistics();

    return Container(
      padding: const EdgeInsets.all(12),
      margin: const EdgeInsets.only(bottom: 16),
      decoration: BoxDecoration(
        color: _accentColor.withOpacity(0.1),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: _accentColor.withOpacity(0.3)),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Statistics:',
            style: TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.bold,
              color: _accentColor,
            ),
          ),
          const SizedBox(height: 8),
          Row(
            children: [
              _buildStatItem('Mean', stats['mean']!),
              _buildStatItem('Median', stats['median']!),
              _buildStatItem('Min', stats['min']!),
              _buildStatItem('Max', stats['max']!),
              _buildStatItem('Std Dev', stats['stdDev']!),
            ],
          ),
        ],
      ),
    );
  }

  // Build a single statistic item
  Widget _buildStatItem(String label, double value) {
    return Expanded(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            label,
            style: TextStyle(
              fontSize: 12,
              fontWeight: FontWeight.bold,
              color: _textColor,
            ),
          ),
          Text(
            value.toStringAsFixed(1),
            style: TextStyle(
              fontSize: 14,
              color: _textColor,
            ),
          ),
        ],
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    final dataSet = _dataSets[_currentDataSetIndex];

    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: _backgroundColor,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withOpacity(0.2),
            spreadRadius: 1,
            blurRadius: 3,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Title
          Text(
            widget.data['title'] ?? 'Data Visualization Tool',
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
              color: _textColor,
            ),
          ),

          const SizedBox(height: 8),

          // Data set navigation
          Row(
            children: [
              Text(
                'Dataset ${_currentDataSetIndex + 1} of ${_dataSets.length}: ${dataSet.title}',
                style: TextStyle(
                  fontSize: 14,
                  fontWeight: FontWeight.bold,
                  color: _primaryColor,
                ),
              ),
              const Spacer(),
              IconButton(
                icon: Icon(_showStatistics ? Icons.analytics_outlined : Icons.analytics),
                onPressed: _toggleStatistics,
                tooltip: _showStatistics ? 'Hide Statistics' : 'Show Statistics',
                color: _accentColor,
              ),
              IconButton(
                icon: Icon(_showExplanation ? Icons.info_outline : Icons.info),
                onPressed: _toggleExplanation,
                tooltip: _showExplanation ? 'Hide Explanation' : 'Show Explanation',
                color: _secondaryColor,
              ),
            ],
          ),

          const SizedBox(height: 8),

          // Data set description
          Text(
            dataSet.description,
            style: TextStyle(
              fontSize: 14,
              fontStyle: FontStyle.italic,
              color: _textColor.withOpacity(0.7),
            ),
          ),

          const SizedBox(height: 16),

          // Visualization type selector
          Row(
            children: [
              Text(
                'Visualization Type:',
                style: TextStyle(
                  fontSize: 14,
                  fontWeight: FontWeight.bold,
                  color: _textColor,
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: SingleChildScrollView(
                  scrollDirection: Axis.horizontal,
                  child: Row(
                    children: _visualizationTypes.map((type) {
                      return Padding(
                        padding: const EdgeInsets.only(right: 8),
                        child: ChoiceChip(
                          label: Text(type),
                          selected: _currentVisualizationType == type,
                          onSelected: (selected) {
                            if (selected) _changeVisualizationType(type);
                          },
                        ),
                      );
                    }).toList(),
                  ),
                ),
              ),
            ],
          ),

          const SizedBox(height: 16),

          // Chart visualization
          Container(
            height: 250,
            width: double.infinity,
            padding: const EdgeInsets.all(8),
            decoration: BoxDecoration(
              color: _backgroundColor,
              borderRadius: BorderRadius.circular(8),
              border: Border.all(color: _primaryColor.withOpacity(0.3)),
            ),
            child: _buildVisualization(dataSet),
          ),

          const SizedBox(height: 16),

          // Statistics
          if (_showStatistics)
            _buildStatisticsPanel(),

          // Explanation
          if (_showExplanation)
            Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: _secondaryColor.withOpacity(0.1),
                borderRadius: BorderRadius.circular(8),
                border: Border.all(color: _secondaryColor.withOpacity(0.3)),
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'Explanation:',
                    style: TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.bold,
                      color: _secondaryColor,
                    ),
                  ),
                  const SizedBox(height: 8),
                  Text(
                    dataSet.explanation,
                    style: TextStyle(
                      fontSize: 14,
                      color: _textColor,
                    ),
                  ),
                ],
              ),
            ),

          const SizedBox(height: 16),

          // Navigation buttons
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              ElevatedButton(
                onPressed: _currentDataSetIndex > 0 ? _previousDataSet : null,
                style: ElevatedButton.styleFrom(
                  backgroundColor: _secondaryColor,
                  foregroundColor: Colors.white,
                ),
                child: const Text('Previous'),
              ),
              ElevatedButton(
                onPressed: _currentDataSetIndex < _dataSets.length - 1 ? _nextDataSet : (_isCompleted ? _resetWidget : _nextDataSet),
                style: ElevatedButton.styleFrom(
                  backgroundColor: _primaryColor,
                  foregroundColor: Colors.white,
                ),
                child: Text(_currentDataSetIndex < _dataSets.length - 1 ? 'Next' : (_isCompleted ? 'Restart' : 'Complete')),
              ),
            ],
          ),

          // Widget name tag at the bottom
          if (widget.data['showNameTag'] ?? true)
            Padding(
              padding: const EdgeInsets.only(top: 8),
              child: Text(
                'InteractiveDataVisualizationToolWidget',
                style: TextStyle(
                  color: Colors.grey[600],
                  fontSize: 12,
                  fontStyle: FontStyle.italic,
                ),
              ),
            ),
        ],
      ),
    );
  }
}
