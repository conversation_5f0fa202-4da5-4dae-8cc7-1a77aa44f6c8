import 'package:flutter/material.dart';
import 'dart:math' as math;

/// A widget that helps students explore and understand inequality symbols.
class InteractiveInequalitySymbolExplorerWidget extends StatefulWidget {
  final Map<String, dynamic> data;
  final Function(bool)? onStateChanged;
  final Color primaryColor;
  final Color secondaryColor;
  final Color backgroundColor;
  final Color textColor;

  const InteractiveInequalitySymbolExplorerWidget({
    super.key,
    this.data = const {},
    this.onStateChanged,
    this.primaryColor = Colors.blue,
    this.secondaryColor = Colors.orange,
    this.backgroundColor = Colors.white,
    this.textColor = Colors.black87,
  });

  @override
  State<InteractiveInequalitySymbolExplorerWidget> createState() =>
      _InteractiveInequalitySymbolExplorerWidgetState();
}

class _InteractiveInequalitySymbolExplorerWidgetState
    extends State<InteractiveInequalitySymbolExplorerWidget> {
  // State variables
  bool _isCompleted = false;
  int _currentSymbolIndex = 0;
  List<InequalitySymbolData> _symbols = [];
  late InequalitySymbolData _currentSymbol;

  // Quiz variables
  bool _showQuiz = false;
  int _quizScore = 0;
  int _currentQuestionIndex = 0;
  List<Map<String, dynamic>> _quizQuestions = [];
  String? _selectedAnswer;
  bool? _isCorrect;
  String? _feedbackMessage;

  // Animation variables
  bool _isAnimating = false;
  double _leftValue = 5;
  double _rightValue = 10;

  @override
  void initState() {
    super.initState();
    _initializeSymbols();
    _currentSymbol = _symbols[_currentSymbolIndex];
    _initializeQuizQuestions();
  }

  void _initializeSymbols() {
    // Check if symbols are provided in the data
    if (widget.data.containsKey('symbols') &&
        widget.data['symbols'] is List &&
        widget.data['symbols'].isNotEmpty) {

      final symbolsData = widget.data['symbols'] as List;
      for (final symbolData in symbolsData) {
        if (symbolData is Map<String, dynamic>) {
          final symbol = InequalitySymbolData.fromJson(symbolData);
          _symbols.add(symbol);
        }
      }
    }

    // If no symbols were provided, create default ones
    if (_symbols.isEmpty) {
      _symbols = [
        InequalitySymbolData(
          symbol: '<',
          name: 'Less Than',
          description: 'The less than symbol (<) indicates that the value on the left side is smaller than the value on the right side.',
          examples: [
            '3 < 5 (3 is less than 5)',
            'x < 10 (x is less than 10)',
            '-2 < 0 (negative 2 is less than zero)',
          ],
          tips: [
            'The symbol points to the smaller value.',
            'Think of the symbol as a "hungry mouth" that always wants to eat the larger number.',
          ],
        ),
        InequalitySymbolData(
          symbol: '>',
          name: 'Greater Than',
          description: 'The greater than symbol (>) indicates that the value on the left side is larger than the value on the right side.',
          examples: [
            '7 > 2 (7 is greater than 2)',
            'x > 5 (x is greater than 5)',
            '0 > -3 (zero is greater than negative 3)',
          ],
          tips: [
            'The symbol points to the smaller value.',
            'The wider part of the symbol is next to the larger value.',
          ],
        ),
        InequalitySymbolData(
          symbol: '≤',
          name: 'Less Than or Equal To',
          description: 'The less than or equal to symbol (≤) indicates that the value on the left side is either smaller than or equal to the value on the right side.',
          examples: [
            '4 ≤ 4 (4 is equal to 4)',
            '3 ≤ 5 (3 is less than 5)',
            'x ≤ 10 (x is less than or equal to 10)',
          ],
          tips: [
            'This symbol combines < and =.',
            'It is satisfied when either the "less than" or the "equal to" condition is true.',
          ],
        ),
        InequalitySymbolData(
          symbol: '≥',
          name: 'Greater Than or Equal To',
          description: 'The greater than or equal to symbol (≥) indicates that the value on the left side is either larger than or equal to the value on the right side.',
          examples: [
            '6 ≥ 6 (6 is equal to 6)',
            '8 ≥ 5 (8 is greater than 5)',
            'x ≥ 0 (x is greater than or equal to 0)',
          ],
          tips: [
            'This symbol combines > and =.',
            'It is satisfied when either the "greater than" or the "equal to" condition is true.',
          ],
        ),
        InequalitySymbolData(
          symbol: '≠',
          name: 'Not Equal To',
          description: 'The not equal to symbol (≠) indicates that the value on the left side is not equal to the value on the right side.',
          examples: [
            '4 ≠ 5 (4 is not equal to 5)',
            'x ≠ 0 (x is not equal to 0)',
            '2 + 3 ≠ 6 (2 plus 3 is not equal to 6)',
          ],
          tips: [
            'This symbol means the two values are different.',
            'It is the opposite of the equals sign (=).',
          ],
        ),
      ];
    }
  }

  void _initializeQuizQuestions() {
    _quizQuestions = [
      {
        'question': 'Which symbol represents "less than"?',
        'options': ['<', '>', '≤', '≥'],
        'correctAnswer': '<',
        'explanation': 'The < symbol represents "less than". It points to the smaller value.',
      },
      {
        'question': 'Which symbol represents "greater than"?',
        'options': ['<', '>', '≤', '≥'],
        'correctAnswer': '>',
        'explanation': 'The > symbol represents "greater than". It points to the smaller value, with the wider part next to the larger value.',
      },
      {
        'question': 'Which symbol represents "less than or equal to"?',
        'options': ['<', '>', '≤', '≥'],
        'correctAnswer': '≤',
        'explanation': 'The ≤ symbol represents "less than or equal to". It combines < and =.',
      },
      {
        'question': 'Which symbol represents "greater than or equal to"?',
        'options': ['<', '>', '≤', '≥'],
        'correctAnswer': '≥',
        'explanation': 'The ≥ symbol represents "greater than or equal to". It combines > and =.',
      },
      {
        'question': 'Which inequality is true? (where x = 5)',
        'options': ['x < 3', 'x > 10', 'x ≤ 5', 'x < 5'],
        'correctAnswer': 'x ≤ 5',
        'explanation': 'Since x = 5, the statement x ≤ 5 is true because 5 is equal to 5.',
      },
      {
        'question': 'Which inequality is false? (where x = 7)',
        'options': ['x > 5', 'x ≥ 7', 'x ≠ 6', 'x < 10'],
        'correctAnswer': 'x < 10',
        'explanation': 'Since x = 7, the statement x < 10 is true (not false) because 7 is less than 10.',
      },
      {
        'question': 'If a < b, then:',
        'options': ['a is greater than b', 'a is less than b', 'a is equal to b', 'a is not related to b'],
        'correctAnswer': 'a is less than b',
        'explanation': 'The symbol < means "less than", so a < b means a is less than b.',
      },
      {
        'question': 'Which statement is true?',
        'options': ['5 > 8', '6 < 3', '4 ≥ 4', '7 ≠ 7'],
        'correctAnswer': '4 ≥ 4',
        'explanation': 'The statement 4 ≥ 4 is true because 4 is equal to 4, and the ≥ symbol means "greater than or equal to".',
      },
      {
        'question': 'Which symbol would make this statement true: 9 ___ 9',
        'options': ['<', '>', '≠', '≤'],
        'correctAnswer': '≤',
        'explanation': 'The statement 9 ≤ 9 is true because 9 is equal to 9, and the ≤ symbol means "less than or equal to".',
      },
      {
        'question': 'Which symbol would make this statement false: 6 ___ 6',
        'options': ['≤', '≥', '≠', '='],
        'correctAnswer': '≠',
        'explanation': 'The statement 6 ≠ 6 is false because 6 is equal to 6, and the ≠ symbol means "not equal to".',
      },
    ];
  }

  void _nextSymbol() {
    if (_currentSymbolIndex < _symbols.length - 1) {
      setState(() {
        _currentSymbolIndex++;
        _currentSymbol = _symbols[_currentSymbolIndex];
        _isAnimating = false;
      });
    } else {
      // All symbols explored
      setState(() {
        _showQuiz = true;
        _currentQuestionIndex = 0;
        _selectedAnswer = null;
        _isCorrect = null;
        _feedbackMessage = null;
      });
    }
  }

  void _previousSymbol() {
    if (_currentSymbolIndex > 0) {
      setState(() {
        _currentSymbolIndex--;
        _currentSymbol = _symbols[_currentSymbolIndex];
        _isAnimating = false;
      });
    }
  }

  void _toggleAnimation() {
    setState(() {
      _isAnimating = !_isAnimating;
      if (_isAnimating) {
        _startAnimation();
      }
    });
  }

  void _startAnimation() {
    // Reset values
    setState(() {
      _leftValue = 5;
      _rightValue = 10;
    });

    // Start animation loop
    _animateValues();
  }

  void _animateValues() {
    if (!_isAnimating) return;

    Future.delayed(const Duration(milliseconds: 1500), () {
      if (!mounted || !_isAnimating) return;

      setState(() {
        // Change values based on current symbol
        switch (_currentSymbol.symbol) {
          case '<':
            _leftValue = math.Random().nextInt(5).toDouble();
            _rightValue = _leftValue + 1 + math.Random().nextInt(5).toDouble();
            break;
          case '>':
            _rightValue = math.Random().nextInt(5).toDouble();
            _leftValue = _rightValue + 1 + math.Random().nextInt(5).toDouble();
            break;
          case '≤':
            _leftValue = math.Random().nextInt(10).toDouble();
            _rightValue = math.Random().nextBool()
                ? _leftValue
                : _leftValue + 1 + math.Random().nextInt(3).toDouble();
            break;
          case '≥':
            _rightValue = math.Random().nextInt(10).toDouble();
            _leftValue = math.Random().nextBool()
                ? _rightValue
                : _rightValue + 1 + math.Random().nextInt(3).toDouble();
            break;
          case '≠':
            _leftValue = math.Random().nextInt(10).toDouble();
            do {
              _rightValue = math.Random().nextInt(10).toDouble();
            } while (_leftValue == _rightValue);
            break;
          default:
            _leftValue = math.Random().nextInt(10).toDouble();
            _rightValue = math.Random().nextInt(10).toDouble();
        }
      });

      // Continue animation
      _animateValues();
    });
  }

  void _checkAnswer(String answer) {
    if (_currentQuestionIndex >= _quizQuestions.length) return;

    final currentQuestion = _quizQuestions[_currentQuestionIndex];
    final bool isCorrect = answer == currentQuestion['correctAnswer'];

    setState(() {
      _selectedAnswer = answer;
      _isCorrect = isCorrect;

      if (isCorrect) {
        _quizScore++;
        _feedbackMessage = 'Correct! ${currentQuestion['explanation']}';
      } else {
        _feedbackMessage = 'Incorrect. ${currentQuestion['explanation']}';
      }
    });

    // Move to next question after a delay
    Future.delayed(const Duration(seconds: 2), () {
      if (!mounted) return;

      setState(() {
        if (_currentQuestionIndex < _quizQuestions.length - 1) {
          _currentQuestionIndex++;
          _selectedAnswer = null;
          _isCorrect = null;
          _feedbackMessage = null;
        } else {
          // Quiz completed
          _isCompleted = true;
          widget.onStateChanged?.call(true);
        }
      });
    });
  }

  void _resetWidget() {
    setState(() {
      _currentSymbolIndex = 0;
      _currentSymbol = _symbols[_currentSymbolIndex];
      _isAnimating = false;
      _showQuiz = false;
      _quizScore = 0;
      _currentQuestionIndex = 0;
      _selectedAnswer = null;
      _isCorrect = null;
      _feedbackMessage = null;
      _isCompleted = false;
    });
    widget.onStateChanged?.call(false);
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: widget.backgroundColor,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withOpacity(0.2),
            spreadRadius: 1,
            blurRadius: 3,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: _isCompleted
          ? _buildCompletionScreen()
          : (_showQuiz ? _buildQuizScreen() : _buildExplorerScreen()),
    );
  }

  Widget _buildExplorerScreen() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Title and navigation
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Text(
              'Inequality Symbols',
              style: TextStyle(
                fontSize: 20,
                fontWeight: FontWeight.bold,
                color: widget.primaryColor,
              ),
            ),
            Text(
              'Symbol ${_currentSymbolIndex + 1} of ${_symbols.length}',
              style: TextStyle(
                fontSize: 14,
                color: widget.textColor.withOpacity(0.7),
              ),
            ),
          ],
        ),

        const SizedBox(height: 24),

        // Current symbol display
        Center(
          child: Container(
            width: 120,
            height: 120,
            decoration: BoxDecoration(
              color: widget.primaryColor.withOpacity(0.1),
              shape: BoxShape.circle,
              border: Border.all(
                color: widget.primaryColor,
                width: 2,
              ),
            ),
            child: Center(
              child: Text(
                _currentSymbol.symbol,
                style: TextStyle(
                  fontSize: 60,
                  fontWeight: FontWeight.bold,
                  color: widget.primaryColor,
                ),
              ),
            ),
          ),
        ),

        const SizedBox(height: 16),

        // Symbol name
        Center(
          child: Text(
            _currentSymbol.name,
            style: TextStyle(
              fontSize: 24,
              fontWeight: FontWeight.bold,
              color: widget.textColor,
            ),
          ),
        ),

        const SizedBox(height: 24),

        // Description
        Container(
          padding: const EdgeInsets.all(12),
          decoration: BoxDecoration(
            color: Colors.grey.shade50,
            borderRadius: BorderRadius.circular(8),
            border: Border.all(color: Colors.grey.shade300),
          ),
          child: Text(
            _currentSymbol.description,
            style: TextStyle(
              fontSize: 16,
              color: widget.textColor,
            ),
          ),
        ),

        const SizedBox(height: 16),

        // Examples
        Text(
          'Examples:',
          style: TextStyle(
            fontSize: 18,
            fontWeight: FontWeight.bold,
            color: widget.textColor,
          ),
        ),

        const SizedBox(height: 8),

        ...(_currentSymbol.examples.map((example) => Padding(
          padding: const EdgeInsets.only(bottom: 8),
          child: Row(
            children: [
              Icon(
                Icons.arrow_right,
                color: widget.secondaryColor,
                size: 20,
              ),
              const SizedBox(width: 8),
              Expanded(
                child: Text(
                  example,
                  style: TextStyle(
                    fontSize: 16,
                    color: widget.textColor,
                  ),
                ),
              ),
            ],
          ),
        )).toList()),

        const SizedBox(height: 16),

        // Tips
        Text(
          'Tips:',
          style: TextStyle(
            fontSize: 18,
            fontWeight: FontWeight.bold,
            color: widget.textColor,
          ),
        ),

        const SizedBox(height: 8),

        ...(_currentSymbol.tips.map((tip) => Padding(
          padding: const EdgeInsets.only(bottom: 8),
          child: Row(
            children: [
              Icon(
                Icons.lightbulb,
                color: widget.secondaryColor,
                size: 20,
              ),
              const SizedBox(width: 8),
              Expanded(
                child: Text(
                  tip,
                  style: TextStyle(
                    fontSize: 16,
                    color: widget.textColor,
                  ),
                ),
              ),
            ],
          ),
        )).toList()),

        const Spacer(),

        // Interactive demonstration
        if (!_isAnimating) ...[
          ElevatedButton.icon(
            onPressed: _toggleAnimation,
            icon: const Icon(Icons.play_arrow),
            label: const Text('Show Examples'),
            style: ElevatedButton.styleFrom(
              backgroundColor: widget.secondaryColor,
              foregroundColor: Colors.white,
            ),
          ),
        ] else ...[
          Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: widget.primaryColor.withOpacity(0.1),
              borderRadius: BorderRadius.circular(8),
              border: Border.all(color: widget.primaryColor.withOpacity(0.3)),
            ),
            child: Column(
              children: [
                Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Container(
                      width: 60,
                      height: 60,
                      decoration: BoxDecoration(
                        color: Colors.white,
                        shape: BoxShape.circle,
                        boxShadow: [
                          BoxShadow(
                            color: Colors.grey.withOpacity(0.3),
                            spreadRadius: 1,
                            blurRadius: 2,
                            offset: const Offset(0, 1),
                          ),
                        ],
                      ),
                      child: Center(
                        child: Text(
                          _leftValue.toStringAsFixed(1),
                          style: TextStyle(
                            fontSize: 20,
                            fontWeight: FontWeight.bold,
                            color: widget.textColor,
                          ),
                        ),
                      ),
                    ),
                    const SizedBox(width: 16),
                    Text(
                      _currentSymbol.symbol,
                      style: TextStyle(
                        fontSize: 40,
                        fontWeight: FontWeight.bold,
                        color: widget.primaryColor,
                      ),
                    ),
                    const SizedBox(width: 16),
                    Container(
                      width: 60,
                      height: 60,
                      decoration: BoxDecoration(
                        color: Colors.white,
                        shape: BoxShape.circle,
                        boxShadow: [
                          BoxShadow(
                            color: Colors.grey.withOpacity(0.3),
                            spreadRadius: 1,
                            blurRadius: 2,
                            offset: const Offset(0, 1),
                          ),
                        ],
                      ),
                      child: Center(
                        child: Text(
                          _rightValue.toStringAsFixed(1),
                          style: TextStyle(
                            fontSize: 20,
                            fontWeight: FontWeight.bold,
                            color: widget.textColor,
                          ),
                        ),
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 16),
                Text(
                  _getInequalityStatement(_leftValue, _currentSymbol.symbol, _rightValue),
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                    color: widget.textColor,
                  ),
                ),
              ],
            ),
          ),
          const SizedBox(height: 8),
          ElevatedButton.icon(
            onPressed: _toggleAnimation,
            icon: const Icon(Icons.stop),
            label: const Text('Stop Examples'),
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.red,
              foregroundColor: Colors.white,
            ),
          ),
        ],

        const SizedBox(height: 16),

        // Navigation buttons
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            ElevatedButton(
              onPressed: _currentSymbolIndex > 0 ? _previousSymbol : null,
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.grey.shade200,
                foregroundColor: Colors.black87,
              ),
              child: const Text('Previous'),
            ),
            ElevatedButton(
              onPressed: _nextSymbol,
              style: ElevatedButton.styleFrom(
                backgroundColor: widget.primaryColor,
                foregroundColor: Colors.white,
              ),
              child: Text(_currentSymbolIndex < _symbols.length - 1 ? 'Next' : 'Take Quiz'),
            ),
          ],
        ),
      ],
    );
  }

  String _getInequalityStatement(double left, String symbol, double right) {
    bool isTrue = false;

    switch (symbol) {
      case '<':
        isTrue = left < right;
        break;
      case '>':
        isTrue = left > right;
        break;
      case '≤':
        isTrue = left <= right;
        break;
      case '≥':
        isTrue = left >= right;
        break;
      case '≠':
        isTrue = left != right;
        break;
      default:
        isTrue = false;
    }

    return '${left.toStringAsFixed(1)} $symbol ${right.toStringAsFixed(1)} is ${isTrue ? 'TRUE' : 'FALSE'}';
  }

  Widget _buildQuizScreen() {
    if (_currentQuestionIndex >= _quizQuestions.length) {
      return Container();
    }

    final currentQuestion = _quizQuestions[_currentQuestionIndex];

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Title and progress
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Text(
              'Inequality Quiz',
              style: TextStyle(
                fontSize: 20,
                fontWeight: FontWeight.bold,
                color: widget.primaryColor,
              ),
            ),
            Text(
              'Question ${_currentQuestionIndex + 1} of ${_quizQuestions.length}',
              style: TextStyle(
                fontSize: 14,
                color: widget.textColor.withOpacity(0.7),
              ),
            ),
          ],
        ),

        const SizedBox(height: 24),

        // Question
        Container(
          width: double.infinity,
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            color: widget.primaryColor.withOpacity(0.1),
            borderRadius: BorderRadius.circular(8),
            border: Border.all(color: widget.primaryColor.withOpacity(0.3)),
          ),
          child: Text(
            currentQuestion['question'],
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
              color: widget.textColor,
            ),
          ),
        ),

        const SizedBox(height: 24),

        // Answer options
        ...(currentQuestion['options'] as List<String>).map((option) {
          final bool isSelected = _selectedAnswer == option;
          final bool isCorrect = option == currentQuestion['correctAnswer'];

          return Padding(
            padding: const EdgeInsets.only(bottom: 12),
            child: InkWell(
              onTap: _selectedAnswer == null ? () => _checkAnswer(option) : null,
              child: Container(
                width: double.infinity,
                padding: const EdgeInsets.all(16),
                decoration: BoxDecoration(
                  color: isSelected
                      ? (isCorrect ? Colors.green.withOpacity(0.1) : Colors.red.withOpacity(0.1))
                      : Colors.grey.shade50,
                  borderRadius: BorderRadius.circular(8),
                  border: Border.all(
                    color: isSelected
                        ? (isCorrect ? Colors.green : Colors.red)
                        : Colors.grey.shade300,
                  ),
                ),
                child: Row(
                  children: [
                    Expanded(
                      child: Text(
                        option,
                        style: TextStyle(
                          fontSize: 16,
                          fontWeight: isSelected ? FontWeight.bold : FontWeight.normal,
                          color: widget.textColor,
                        ),
                      ),
                    ),
                    if (isSelected)
                      Icon(
                        isCorrect ? Icons.check_circle : Icons.cancel,
                        color: isCorrect ? Colors.green : Colors.red,
                      ),
                  ],
                ),
              ),
            ),
          );
        }).toList(),

        const Spacer(),

        // Feedback message
        if (_feedbackMessage != null)
          Container(
            width: double.infinity,
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: _isCorrect == true
                  ? Colors.green.withOpacity(0.1)
                  : Colors.red.withOpacity(0.1),
              borderRadius: BorderRadius.circular(8),
              border: Border.all(
                color: _isCorrect == true ? Colors.green : Colors.red,
              ),
            ),
            child: Text(
              _feedbackMessage!,
              style: TextStyle(
                fontSize: 16,
                color: _isCorrect == true ? Colors.green : Colors.red,
              ),
            ),
          ),
      ],
    );
  }

  Widget _buildCompletionScreen() {
    // Calculate percentage score
    final percentage = (_quizScore / _quizQuestions.length * 100).round();
    final isPassing = percentage >= 70;

    return Column(
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        // Trophy icon for passing, or try again icon for failing
        Icon(
          isPassing ? Icons.emoji_events : Icons.refresh,
          size: 80,
          color: isPassing ? Colors.amber : Colors.grey,
        ),

        const SizedBox(height: 24),

        // Result title
        Text(
          isPassing ? 'Congratulations!' : 'Keep Practicing!',
          style: TextStyle(
            fontSize: 24,
            fontWeight: FontWeight.bold,
            color: isPassing ? widget.primaryColor : Colors.red,
          ),
        ),

        const SizedBox(height: 16),

        // Score
        Text(
          'Your Score: $_quizScore out of ${_quizQuestions.length} ($percentage%)',
          style: TextStyle(
            fontSize: 18,
            fontWeight: FontWeight.bold,
            color: widget.textColor,
          ),
        ),

        const SizedBox(height: 24),

        // Feedback message
        Text(
          isPassing
              ? 'Great job! You\'ve demonstrated a solid understanding of inequality symbols.'
              : 'You need a score of at least 70% to pass. Review the symbols and try again!',
          textAlign: TextAlign.center,
          style: TextStyle(
            fontSize: 16,
            color: widget.textColor,
          ),
        ),

        const SizedBox(height: 32),

        // Try again button
        ElevatedButton.icon(
          onPressed: _resetWidget,
          icon: Icon(Icons.refresh),
          label: Text('Start Over'),
          style: ElevatedButton.styleFrom(
            backgroundColor: widget.secondaryColor,
            padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
          ),
        ),
      ],
    );
  }
}

/// Data class for inequality symbol information
class InequalitySymbolData {
  final String symbol;
  final String name;
  final String description;
  final List<String> examples;
  final List<String> tips;

  InequalitySymbolData({
    required this.symbol,
    required this.name,
    required this.description,
    required this.examples,
    required this.tips,
  });

  factory InequalitySymbolData.fromJson(Map<String, dynamic> json) {
    return InequalitySymbolData(
      symbol: json['symbol'] ?? '<',
      name: json['name'] ?? 'Less Than',
      description: json['description'] ?? 'The less than symbol (<) indicates that the value on the left side is smaller than the value on the right side.',
      examples: List<String>.from(json['examples'] ?? ['3 < 5 (3 is less than 5)']),
      tips: List<String>.from(json['tips'] ?? ['The symbol points to the smaller value.']),
    );
  }
}
