{"id": "everyday-tech", "title": "Everyday Technology", "description": "Explore the inner workings of common technology devices and the fascinating science behind them.", "order": 1, "lessons": [{"id": "smartphone-sand-to-pocket", "title": "The Smartphone: From Sand to Pocket Computer", "description": "Uncover the history and inner workings of the device you use every day.", "order": 1, "type": "interactive_lesson", "estimatedTimeMinutes": 15, "contentBlocks": [{"id": "smartphone-screen-1", "type": "lesson_screen", "order": 1, "estimatedTimeSeconds": 60, "content": {"headline": "The Pocket Supercomputer", "body_md": "The smartphone in your pocket is more powerful than all of NASA's combined computing power that sent humans to the moon in 1969. Yet it began its journey as ordinary sand.\n\nHow did humble silicon transform into the most revolutionary device of our time?", "visual": {"type": "unsplash_search", "value": "smartphone technology"}, "interactive_element": {"type": "button", "button_text": "From Sand to Silicon", "action": "next_screen"}}}, {"id": "smartphone-screen-2", "type": "lesson_screen", "order": 2, "estimatedTimeSeconds": 90, "content": {"headline": "Step 1: From Sand to Pure Silicon", "body_md": "The journey begins with silicon dioxide (SiO₂) - ordinary sand. To create the foundation for microchips:\n\n1. Sand is heated to extremely high temperatures (over 1,900°C)\n2. The molten silicon is purified to 99.9999999% purity\n3. It's formed into a single crystal ingot using the Czochralski process\n4. The ingot is sliced into thin wafers that become the base for microchips\n\nThis ultra-pure silicon is the foundation for all modern computing.", "visual": {"type": "giphy_search", "value": "silicon wafer manufacturing"}, "interactive_element": {"type": "multiple_choice_text", "question": "Why must silicon be purified to such an extreme degree (99.9999999%) for microchips?", "options": [{"id": "opt1", "text": "To make it transparent", "is_correct": false, "feedback": "While pure silicon has optical properties, transparency isn't the primary reason for such extreme purification."}, {"id": "opt2", "text": "Because even tiny impurities can disrupt the precise electrical properties needed", "is_correct": true, "feedback": "Correct! Even microscopic impurities can create defects that disrupt the carefully designed electrical pathways in a microchip, potentially causing failures."}, {"id": "opt3", "text": "To make it harder and more durable", "is_correct": false, "feedback": "While purity affects physical properties, durability isn't the main reason for such extreme purification."}, {"id": "opt4", "text": "To reduce its weight", "is_correct": false, "feedback": "The weight of silicon isn't significantly affected by this level of purification."}]}}}, {"id": "smartphone-screen-3", "type": "lesson_screen", "order": 3, "estimatedTimeSeconds": 90, "content": {"headline": "Step 2: Creating the Microchips", "body_md": "Modern smartphones contain multiple specialized microchips:\n\n- **CPU (Central Processing Unit)**: The main 'brain' that executes instructions\n- **GPU (Graphics Processing Unit)**: Handles visual rendering and display\n- **Memory chips**: Both RAM (temporary) and storage (permanent)\n- **Modem**: Manages cellular communications\n- **Various sensors**: Camera, accelerometer, gyroscope, etc.\n\nThese chips are created through photolithography - using light to etch microscopic patterns onto silicon.", "visual": {"type": "giphy_search", "value": "microchip manufacturing"}, "interactive_element": {"type": "button", "button_text": "The Assembly Process", "action": "next_screen"}}}, {"id": "smartphone-screen-4", "type": "lesson_screen", "order": 4, "estimatedTimeSeconds": 90, "content": {"headline": "Step 3: Assembling the Device", "body_md": "A modern smartphone contains hundreds of components carefully assembled:\n\n- **Display**: Usually OLED or LCD technology with touch sensors\n- **Battery**: Lithium-ion technology for power storage\n- **Circuit boards**: Connecting all electronic components\n- **Cameras**: Multiple lenses and sensors for photography\n- **Speakers and microphones**: For audio input/output\n- **Antennas**: For cellular, WiFi, Bluetooth, GPS, etc.\n- **Casing**: Typically metal, glass, and/or plastic", "visual": {"type": "unsplash_search", "value": "smartphone disassembled components"}, "interactive_element": {"type": "multiple_choice_text", "question": "Which component typically uses the most power in a smartphone?", "options": [{"id": "opt1", "text": "The CPU", "is_correct": false, "feedback": "While the CPU does use significant power, especially during intensive tasks, it's not typically the biggest power consumer."}, {"id": "opt2", "text": "The display", "is_correct": true, "feedback": "Correct! The display is usually the biggest power consumer in a smartphone, especially at high brightness levels."}, {"id": "opt3", "text": "The camera", "is_correct": false, "feedback": "Cameras use power when active but aren't the biggest overall power consumers."}, {"id": "opt4", "text": "The speakers", "is_correct": false, "feedback": "Speakers use relatively little power compared to other components."}]}}}, {"id": "smartphone-screen-5", "type": "lesson_screen", "order": 5, "estimatedTimeSeconds": 90, "content": {"headline": "The Software Side: Bringing It to Life", "body_md": "Hardware is only half the story. Smartphones run complex software systems:\n\n- **Operating System**: iOS or Android typically, managing all resources\n- **Firmware**: Low-level software controlling hardware components\n- **Applications**: From basic utilities to complex games and tools\n\nModern smartphones run millions of lines of code, with multiple layers of software working together to create the seamless experience we take for granted.", "visual": {"type": "giphy_search", "value": "programming code animation"}, "interactive_element": {"type": "button", "button_text": "The Global Supply Chain", "action": "next_screen"}}}, {"id": "smartphone-screen-6", "type": "lesson_screen", "order": 6, "estimatedTimeSeconds": 90, "content": {"headline": "A Global Effort", "body_md": "Creating a smartphone involves a worldwide supply chain:\n\n- **Minerals** mined in Africa, South America, and Asia\n- **Chip fabrication** often in Taiwan, South Korea, or the US\n- **Component manufacturing** across Southeast Asia\n- **Assembly** typically in China, Vietnam, or India\n- **Software development** from teams around the world\n\nA single smartphone might contain materials and expertise from dozens of countries, representing one of the most complex supply chains for any consumer product.", "visual": {"type": "unsplash_search", "value": "global supply chain"}, "interactive_element": {"type": "multiple_choice_text", "question": "Approximately how many individual components might be found in a modern smartphone?", "options": [{"id": "opt1", "text": "Around 50", "is_correct": false, "feedback": "This is far too low. Modern smartphones are much more complex."}, {"id": "opt2", "text": "Around 200", "is_correct": false, "feedback": "While this might have been true for early mobile phones, modern smartphones contain many more components."}, {"id": "opt3", "text": "Around 500-1,000", "is_correct": true, "feedback": "Correct! A modern smartphone typically contains between 500-1,000 individual components, from tiny resistors to complex microprocessors."}, {"id": "opt4", "text": "Over 10,000", "is_correct": false, "feedback": "This is an overestimate. While smartphones are complex, they don't contain quite this many discrete components."}]}}}, {"id": "smartphone-screen-7", "type": "lesson_screen", "order": 7, "estimatedTimeSeconds": 60, "content": {"headline": "From Sand to Supercomputer: The Journey Complete", "body_md": "The next time you use your smartphone, consider the incredible journey it took:\n\n- From ordinary sand to ultra-pure silicon\n- From silicon wafers to complex microchips with billions of transistors\n- From individual components to an assembled device\n- From hardware to software creating a seamless experience\n\nAll of this technology, representing the culmination of decades of scientific advancement and global cooperation, fits in your pocket and responds to your touch.", "visual": {"type": "giphy_search", "value": "technology evolution"}, "interactive_element": {"type": "button", "button_text": "Finish Lesson", "action": "complete_lesson"}}}]}, {"id": "internet-connecting-world", "title": "The Internet: Connecting the World Wirelessly and Wired", "description": "Explore its origins and infrastructure.", "order": 2, "type": "interactive_lesson", "estimatedTimeMinutes": 15, "contentBlocks": []}, {"id": "automobile-century-motion", "title": "The Automobile: A Century of Motion and Innovation", "description": "Discover its evolution and key technologies.", "order": 3, "type": "interactive_lesson", "estimatedTimeMinutes": 15, "contentBlocks": []}, {"id": "television-vacuum-flat", "title": "The Television: From Vacuum Tubes to Flat Screens", "description": "Trace its development and underlying principles.", "order": 4, "type": "interactive_lesson", "estimatedTimeMinutes": 15, "contentBlocks": []}, {"id": "refrigerator-keeping-cool", "title": "The Refrigerator: Keeping Things Cool Through Science", "description": "Explore its history and thermodynamic principles.", "order": 5, "type": "interactive_lesson", "estimatedTimeMinutes": 15, "contentBlocks": []}]}