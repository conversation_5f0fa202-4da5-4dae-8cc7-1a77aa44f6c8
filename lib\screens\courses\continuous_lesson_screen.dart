import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_markdown/flutter_markdown.dart';
import '../../models/course_models.dart';
import '../../services/service_provider.dart';
import 'widgets/content_blocks/button_element_widget.dart';
import 'widgets/content_blocks/multiple_choice_text_widget.dart';
import 'widgets/content_blocks/multiple_choice_image_widget.dart';
import 'widgets/content_blocks/multiple_choice_icon_element_widget.dart';
import 'widgets/content_blocks/text_input_element_widget.dart';
import 'widgets/content_blocks/text_input_quick_element_widget.dart';
import 'widgets/content_blocks/interactive_expression_evaluator_widget.dart';
import 'widgets/content_blocks/interactive_like_terms_combiner_widget.dart';
import 'widgets/content_blocks/interactive_balance_scale_analogy_widget.dart';
import 'widgets/content_blocks/visual_element_handler.dart';

class ContinuousLessonScreen extends StatefulWidget {
  final String courseId;
  final String lessonId;

  const ContinuousLessonScreen({
    super.key,
    required this.courseId,
    required this.lessonId,
  });

  @override
  State<ContinuousLessonScreen> createState() => _ContinuousLessonScreenState();
}

class _ContinuousLessonScreenState extends State<ContinuousLessonScreen> {
  Course? _course;
  LessonDefinition? _lesson;
  List<LessonDefinition> _allCourseLessons = [];
  int _lessonIndex = 0;
  bool _isLoading = true;

  // For continuous scrolling
  late ScrollController _scrollController;
  int _currentContentBlockIndex = 0;
  final Map<int, bool> _revealedBlocks = {};
  final Map<int, GlobalKey> _contentBlockKeys = {};

  @override
  void initState() {
    super.initState();
    _scrollController = ScrollController();

    // Initialize with the first block revealed
    _revealedBlocks[0] = true;
  }

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
    _loadLessonData();
  }

  Future<void> _loadLessonData() async {
    setState(() {
      _isLoading = true;
    });

    final courseService = ServiceProvider.of(context).courseService;
    final course = await courseService.getCourseById(widget.courseId);

    if (course == null || !mounted) {
      if (mounted) Navigator.pop(context);
      return;
    }

    _course = course;
    _allCourseLessons =
        _course!.modules.expand((module) => module.lessons).toList();

    try {
      _lesson = _allCourseLessons.firstWhere(
        (lesson) => lesson.id == widget.lessonId,
        orElse: () => _allCourseLessons.first,
      );

      _lessonIndex = _allCourseLessons.indexOf(_lesson!);

      // Initialize content block keys
      for (int i = 0; i < _lesson!.contentBlocks.length; i++) {
        _contentBlockKeys[i] = GlobalKey();
      }

      setState(() {
        _isLoading = false;
      });
    } catch (e) {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error loading lesson: $e'),
            backgroundColor: Colors.red,
          ),
        );
        Navigator.pop(context);
      }
    }
  }

  void _revealNextBlock() {
    if (_lesson == null) return;

    if (_currentContentBlockIndex < _lesson!.contentBlocks.length - 1) {
      setState(() {
        _currentContentBlockIndex++;
        _revealedBlocks[_currentContentBlockIndex] = true;
      });

      // Scroll to the newly revealed block
      WidgetsBinding.instance.addPostFrameCallback((_) {
        // Delay slightly to ensure the widget is built
        Future.delayed(const Duration(milliseconds: 100), () {
          final key = _contentBlockKeys[_currentContentBlockIndex];
          if (key?.currentContext != null) {
            Scrollable.ensureVisible(
              key!.currentContext!,
              duration: const Duration(milliseconds: 800),
              curve: Curves.easeInOutCubic,
              alignment: 0.2, // Show a bit of the previous content for context
            );
          }
        });
      });
    } else {
      // Last content block of the current lesson is completed
      // Mark lesson as complete
      final courseService = ServiceProvider.of(context).courseService;
      if (courseService.currentUser != null) {
        final user = courseService.currentUser!;
        final updatedCourseProgress = Map<String, CourseProgress>.from(
          user.courseProgress,
        );
        final courseProgress =
            updatedCourseProgress[widget.courseId] ??
            CourseProgress(courseId: widget.courseId);
        final updatedLessonProgress = Map<String, LessonProgress>.from(
          courseProgress.lessonProgress,
        );

        // Create a completed lesson progress
        updatedLessonProgress[_lesson!.id] = LessonProgress(
          lessonId: _lesson!.id,
          isCompleted: true,
          progressPercentage: 100.0,
        );

        // Update course progress
        updatedCourseProgress[widget.courseId] = CourseProgress(
          courseId: widget.courseId,
          lessonProgress: updatedLessonProgress,
          lastAccessed: DateTime.now(),
          isCompleted: courseProgress.isCompleted,
        );

        // Save the updated progress
        courseService.updateUserCourseProgress(updatedCourseProgress);
      }

      // Show completion message
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('${_lesson!.title} completed!'),
          backgroundColor: Colors.green,
          duration: const Duration(seconds: 2),
        ),
      );

      // Navigate back to the roadmap screen
      Navigator.of(context).pop();
    }
  }

  @override
  void dispose() {
    _scrollController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    if (_isLoading || _lesson == null) {
      return const Scaffold(body: Center(child: CircularProgressIndicator()));
    }

    return Scaffold(
      backgroundColor: Colors.white,
      appBar: AppBar(
        title: Text(
          _lesson!.title,
          style: const TextStyle(
            color: Colors.black,
            fontWeight: FontWeight.w600,
            fontFamily: 'WorkSans',
          ),
        ),
        backgroundColor: Colors.white,
        elevation: 0,
        iconTheme: const IconThemeData(color: Colors.black),
        leading: IconButton(
          icon: const Icon(Icons.close),
          onPressed: () {
            Navigator.of(context).pop();
          },
        ),
        actions: [
          Center(
            child: Text(
              'Lesson ${_lessonIndex + 1}/${_allCourseLessons.length}',
              style: TextStyle(
                color: Colors.grey[700],
                fontSize: 14,
                fontFamily: 'WorkSans',
              ),
            ),
          ),
          const SizedBox(width: 16),
        ],
      ),
      body: Stack(
        children: [
          // Main content with vertical progress bar
          Row(
            children: [
              // Vertical progress bar
              Container(
                width: 4,
                margin: const EdgeInsets.only(left: 4),
                child: LayoutBuilder(
                  builder: (context, constraints) {
                    final double maxHeight = constraints.maxHeight;
                    final double progressHeight =
                        maxHeight *
                        (_currentContentBlockIndex + 1) /
                        _lesson!.contentBlocks.length;

                    return Stack(
                      children: [
                        // Background track
                        Container(
                          width: 4,
                          height: maxHeight,
                          color: Colors.grey[200],
                        ),
                        // Progress indicator (fills from top to bottom)
                        Container(
                          width: 4,
                          height: progressHeight,
                          color: const Color.fromRGBO(124, 66, 210, 1),
                        ),
                      ],
                    );
                  },
                ),
              ),

              // Main content area
              Expanded(
                child: SingleChildScrollView(
                  controller: _scrollController,
                  padding: const EdgeInsets.only(
                    top: 16.0,
                    bottom: 80.0, // Extra padding for the fixed button
                  ),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      // Lesson title and description
                      Padding(
                        padding: const EdgeInsets.fromLTRB(16, 0, 16, 24),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              _lesson!.title,
                              style: const TextStyle(
                                fontSize: 24,
                                fontWeight: FontWeight.bold,
                                color: Colors.black,
                              ),
                            ),
                            const SizedBox(height: 8),
                            Text(
                              _lesson!.description,
                              style: TextStyle(
                                fontSize: 16,
                                color: Colors.grey[700],
                              ),
                            ),
                          ],
                        ),
                      ),

                      // Content blocks
                      ...List.generate(_lesson!.contentBlocks.length, (index) {
                        // Only show blocks that have been revealed
                        if (_revealedBlocks[index] != true) {
                          return const SizedBox.shrink();
                        }

                        final contentBlock = _lesson!.contentBlocks[index];
                        return Container(
                          key: _contentBlockKeys[index],
                          margin: const EdgeInsets.only(bottom: 24.0),
                          child: _buildContentBlock(contentBlock),
                        );
                      }),
                    ],
                  ),
                ),
              ),
            ],
          ),

          // Fixed continue button at the bottom
          Positioned(
            left: 0,
            right: 0,
            bottom: 0,
            child: Container(
              decoration: BoxDecoration(
                color: Colors.white,
                boxShadow: [
                  BoxShadow(
                    color: Colors.black.withAlpha(25),
                    blurRadius: 8,
                    offset: const Offset(0, -2),
                  ),
                ],
              ),
              padding: const EdgeInsets.symmetric(
                horizontal: 16.0,
                vertical: 8.0,
              ),
              child: SafeArea(
                top: false,
                child: ElevatedButton(
                  onPressed: _revealNextBlock,
                  style: ElevatedButton.styleFrom(
                    backgroundColor: const Color.fromRGBO(124, 66, 210, 1),
                    foregroundColor: Colors.white,
                    elevation: 3,
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(12),
                    ),
                    padding: const EdgeInsets.symmetric(
                      horizontal: 20,
                      vertical: 14,
                    ),
                  ),
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Text(
                        (_currentContentBlockIndex >=
                                _lesson!.contentBlocks.length - 1)
                            ? 'Finish Lesson'
                            : 'Continue',
                        style: const TextStyle(
                          fontSize: 17,
                          fontWeight: FontWeight.w600,
                          fontFamily: 'WorkSans',
                        ),
                      ),
                      const SizedBox(width: 10),
                      Container(
                        padding: const EdgeInsets.all(3),
                        decoration: BoxDecoration(
                          color: Colors.white.withAlpha(60),
                          shape: BoxShape.circle,
                        ),
                        child: Icon(
                          (_currentContentBlockIndex >=
                                  _lesson!.contentBlocks.length - 1)
                              ? Icons.check_circle_outline
                              : Icons.arrow_forward_ios,
                          size: 20,
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildContentBlock(ContentScreen contentScreen) {
    final visual = contentScreen.content.visual;
    final interactive = contentScreen.content.interactive_element;
    final screenContent = contentScreen.content;
    List<Widget> children = [];

    // Headline
    if (screenContent.headline != null && screenContent.headline!.isNotEmpty) {
      children.add(
        Padding(
          padding: const EdgeInsets.only(bottom: 16.0),
          child: Text(
            screenContent.headline!,
            style: Theme.of(context).textTheme.headlineSmall?.copyWith(
              fontWeight: FontWeight.bold,
              fontSize: 24,
            ),
            textAlign: TextAlign.center,
          ),
        ),
      );
    }

    // Body Markdown - using Markdown renderer instead of Text
    children.add(
      Padding(
        padding: const EdgeInsets.only(bottom: 16.0),
        child: MarkdownBody(
          data: screenContent.body_md,
          styleSheet: MarkdownStyleSheet(
            p: Theme.of(
              context,
            ).textTheme.bodyLarge?.copyWith(fontSize: 18, height: 1.5),
            strong: const TextStyle(fontWeight: FontWeight.bold),
            em: const TextStyle(fontStyle: FontStyle.italic),
            blockSpacing: 16.0,
            listIndent: 24.0,
            blockquote: TextStyle(
              color: Colors.grey[700],
              fontStyle: FontStyle.italic,
              fontSize: 18,
            ),
            code: const TextStyle(
              fontFamily: 'monospace',
              backgroundColor: Color(0xFFf7f7f7),
            ),
            codeblockDecoration: BoxDecoration(
              color: const Color(0xFFf7f7f7),
              borderRadius: BorderRadius.circular(8.0),
            ),
          ),
          selectable: true,
        ),
      ),
    );

    // Hook Text
    if (screenContent.hook != null && screenContent.hook!.isNotEmpty) {
      children.add(
        Padding(
          padding: const EdgeInsets.only(top: 8.0, bottom: 16.0),
          child: Text(
            screenContent.hook!,
            style: Theme.of(context).textTheme.titleMedium?.copyWith(
              fontStyle: FontStyle.italic,
              color: Colors.grey[700],
              fontSize: 16,
            ),
            textAlign: TextAlign.center,
          ),
        ),
      );
    }

    // Visual Element
    children.add(VisualElementHandler(visual: visual));

    // Interactive Element
    if (interactive is ButtonElement) {
      children.add(
        Column(
          children: [
            ButtonElementWidget(
              buttonElement: interactive,
              onNextLesson: _revealNextBlock,
              onModuleComplete: _revealNextBlock,
              isLastSlideInLesson:
                  _currentContentBlockIndex >=
                  _lesson!.contentBlocks.length - 1,
            ),
            // Name tag for the interactive element
            Padding(
              padding: const EdgeInsets.only(top: 8.0),
              child: Text(
                '[ ButtonElement Widget ]',
                style: TextStyle(
                  fontSize: 12,
                  color: Colors.grey[600],
                  fontStyle: FontStyle.italic,
                ),
                textAlign: TextAlign.center,
              ),
            ),
          ],
        ),
      );
    } else if (interactive is MultipleChoiceTextElement) {
      children.add(
        Column(
          children: [
            MultipleChoiceTextWidget(
              mcqElement: interactive,
              onNextAction: _revealNextBlock,
              isLastSlideInLesson:
                  _currentContentBlockIndex >=
                  _lesson!.contentBlocks.length - 1,
            ),
            // Name tag for the interactive element
            Padding(
              padding: const EdgeInsets.only(top: 8.0),
              child: Text(
                '[ MultipleChoiceTextElement Widget ]',
                style: TextStyle(
                  fontSize: 12,
                  color: Colors.grey[600],
                  fontStyle: FontStyle.italic,
                ),
                textAlign: TextAlign.center,
              ),
            ),
          ],
        ),
      );
    } else if (interactive is MultipleChoiceImageElement) {
      children.add(
        Column(
          children: [
            MultipleChoiceImageWidget(
              mcqImageElement: interactive,
              onNextAction: _revealNextBlock,
              isLastSlideInLesson:
                  _currentContentBlockIndex >=
                  _lesson!.contentBlocks.length - 1,
            ),
            // Name tag for the interactive element
            Padding(
              padding: const EdgeInsets.only(top: 8.0),
              child: Text(
                '[ MultipleChoiceImageElement Widget ]',
                style: TextStyle(
                  fontSize: 12,
                  color: Colors.grey[600],
                  fontStyle: FontStyle.italic,
                ),
                textAlign: TextAlign.center,
              ),
            ),
          ],
        ),
      );
    } else if (interactive is TextInputElement) {
      children.add(
        Column(
          children: [
            TextInputElementWidget(
              textInputElement: interactive,
              onNextAction: _revealNextBlock,
              isLastSlideInLesson:
                  _currentContentBlockIndex >=
                  _lesson!.contentBlocks.length - 1,
            ),
            // Name tag for the interactive element
            Padding(
              padding: const EdgeInsets.only(top: 8.0),
              child: Text(
                '[ TextInputElement Widget ]',
                style: TextStyle(
                  fontSize: 12,
                  color: Colors.grey[600],
                  fontStyle: FontStyle.italic,
                ),
                textAlign: TextAlign.center,
              ),
            ),
          ],
        ),
      );
    } else if (interactive is TextInputQuickElement) {
      children.add(
        Column(
          children: [
            TextInputQuickElementWidget(
              textInputQuickElement: interactive,
              onNextAction: _revealNextBlock,
              isLastSlideInLesson:
                  _currentContentBlockIndex >=
                  _lesson!.contentBlocks.length - 1,
            ),
            // Name tag for the interactive element
            Padding(
              padding: const EdgeInsets.only(top: 8.0),
              child: Text(
                '[ TextInputQuickElement Widget ]',
                style: TextStyle(
                  fontSize: 12,
                  color: Colors.grey[600],
                  fontStyle: FontStyle.italic,
                ),
                textAlign: TextAlign.center,
              ),
            ),
          ],
        ),
      );
    } else if (interactive is MultipleChoiceIconElement) {
      children.add(
        Column(
          children: [
            MultipleChoiceIconElementWidget(
              mcqIconElement: interactive,
              onNextAction: _revealNextBlock,
              isLastSlideInLesson:
                  _currentContentBlockIndex >=
                  _lesson!.contentBlocks.length - 1,
            ),
            // Name tag for the interactive element
            Padding(
              padding: const EdgeInsets.only(top: 8.0),
              child: Text(
                '[ MultipleChoiceIconElement Widget ]',
                style: TextStyle(
                  fontSize: 12,
                  color: Colors.grey[600],
                  fontStyle: FontStyle.italic,
                ),
                textAlign: TextAlign.center,
              ),
            ),
          ],
        ),
      );
    } else if (interactive is InteractiveExpressionEvaluatorElement) {
      children.add(
        Column(
          children: [
            InteractiveExpressionEvaluatorWidget(
              expressionEvaluatorElement: interactive,
              onNextAction: _revealNextBlock,
              isLastSlideInLesson:
                  _currentContentBlockIndex >=
                  _lesson!.contentBlocks.length - 1,
            ),
            // Name tag for the interactive element
            Padding(
              padding: const EdgeInsets.only(top: 8.0),
              child: Text(
                '[ InteractiveExpressionEvaluator Widget ]',
                style: TextStyle(
                  fontSize: 12,
                  color: Colors.grey[600],
                  fontStyle: FontStyle.italic,
                ),
                textAlign: TextAlign.center,
              ),
            ),
          ],
        ),
      );
    } else if (interactive is InteractiveLikeTermsCombinerElement) {
      children.add(
        Column(
          children: [
            InteractiveLikeTermsCombinerWidget(
              likeTermsCombinerElement: interactive,
              onNextAction: _revealNextBlock,
              isLastSlideInLesson:
                  _currentContentBlockIndex >=
                  _lesson!.contentBlocks.length - 1,
            ),
            // Name tag for the interactive element
            Padding(
              padding: const EdgeInsets.only(top: 8.0),
              child: Text(
                '[ InteractiveLikeTermsCombiner Widget ]',
                style: TextStyle(
                  fontSize: 12,
                  color: Colors.grey[600],
                  fontStyle: FontStyle.italic,
                ),
                textAlign: TextAlign.center,
              ),
            ),
          ],
        ),
      );
    } else if (interactive is InteractiveBalanceScaleAnalogyElement) {
      children.add(
        Column(
          children: [
            InteractiveBalanceScaleAnalogyWidget(
              balanceScaleElement: interactive,
              onNextAction: _revealNextBlock,
              isLastSlideInLesson:
                  _currentContentBlockIndex >=
                  _lesson!.contentBlocks.length - 1,
            ),
            // Name tag for the interactive element
            Padding(
              padding: const EdgeInsets.only(top: 8.0),
              child: Text(
                '[ InteractiveBalanceScaleAnalogy Widget ]',
                style: TextStyle(
                  fontSize: 12,
                  color: Colors.grey[600],
                  fontStyle: FontStyle.italic,
                ),
                textAlign: TextAlign.center,
              ),
            ),
          ],
        ),
      );
    } else {
      // Generic placeholder for other interactive types
      if (interactive.type != 'placeholder_interactive' ||
          (interactive is PlaceholderInteractiveElement &&
              interactive.data['text'] != 'Missing interactive data')) {
        children.add(
          Column(
            children: [
              Padding(
                padding: const EdgeInsets.symmetric(vertical: 16.0),
                child: Container(
                  padding: const EdgeInsets.all(12),
                  decoration: BoxDecoration(
                    color: Colors.blue[50],
                    borderRadius: BorderRadius.circular(8),
                    border: Border.all(color: Colors.blue[200]!),
                  ),
                  child: Text(
                    'Interactive: ${interactive.type}',
                    textAlign: TextAlign.center,
                    style: TextStyle(
                      fontStyle: FontStyle.italic,
                      color: Colors.blue[700],
                    ),
                  ),
                ),
              ),
              // Name tag for the interactive element
              Padding(
                padding: const EdgeInsets.only(top: 8.0),
                child: Text(
                  '[ ${interactive.type} Widget ]',
                  style: TextStyle(
                    fontSize: 12,
                    color: Colors.grey[600],
                    fontStyle: FontStyle.italic,
                  ),
                  textAlign: TextAlign.center,
                ),
              ),
            ],
          ),
        );
      }
    }

    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 20.0, vertical: 24.0),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.start,
        crossAxisAlignment: CrossAxisAlignment.stretch,
        children: children,
      ),
    );
  }
}
