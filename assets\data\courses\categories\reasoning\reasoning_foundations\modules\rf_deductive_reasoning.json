{"id": "rf_deductive_reasoning", "title": "Deductive Reasoning: Certainty and Validity", "description": "Investigate arguments where the conclusion necessarily follows from the premises.", "estimated_lesson_duration_minutes": 60, "lessons": [{"id": "rf-dr-l1-what-is-deductive", "title": "What is Deductive Reasoning?", "description": "Understand the concept of logical necessity.", "xp_reward": 100, "content_blocks": [{"type": "text", "content": "Deductive reasoning is a powerful form of argument where, if the premises are true, the conclusion *must* also be true. There's no room for probability or uncertainty – it's about logical necessity."}, {"type": "heading", "content": "Certainty in Conclusions"}, {"type": "text", "content": "Unlike other forms of reasoning you might encounter, deduction aims for certainty. If a deductive argument is structured correctly (it's 'valid') and its starting points (premises) are true, then the conclusion is guaranteed."}, {"type": "example", "content": "Classic Example:\n1. All men are mortal. (Premise)\n2. Socrates is a man. (Premise)\nTherefore, Socrates is mortal. (Conclusion)\n\nIf premises 1 and 2 are true, the conclusion *cannot* be false."}, {"type": "interactive_element_placeholder", "interactive_element": {"type": "multiple_choice_text", "question_text": "In a valid deductive argument with true premises, how likely is the conclusion to be true?", "options": [{"id": "opt1", "text": "Very likely, but not guaranteed.", "is_correct": false, "feedback_incorrect": "Deduction aims for more than just high probability."}, {"id": "opt2", "text": "Guaranteed to be true.", "is_correct": true, "feedback_correct": "Correct! This is the hallmark of deductive reasoning."}, {"id": "opt3", "text": "Depends on the specific content.", "is_correct": false, "feedback_incorrect": "While content matters for truth of premises, the *guarantee* comes from the structure if premises are true."}], "action_button_text": "Check Answer"}}, {"type": "text", "content": "Deductive reasoning is fundamental in fields like mathematics, logic, and computer science, where precision and certainty are paramount."}]}, {"id": "rf-dr-l2-basic-deductive-forms", "title": "Basic Deductive Forms: <PERSON><PERSON>, <PERSON><PERSON>", "description": "Learn common valid argument structures.", "xp_reward": 100, "content_blocks": [{"type": "text", "content": "Certain patterns of deductive reasoning are so common and reliable that they have names. Let's explore two foundational ones: <PERSON><PERSON> and <PERSON><PERSON>."}, {"type": "heading", "content": "<PERSON><PERSON> (Affirming the Antecedent)"}, {"type": "text", "content": "Form:\n1. If P, then Q.\n2. P.\nTherefore, Q.\n\nExample: \n1. If it is raining (P), then the ground is wet (Q).\n2. It is raining (P).\nTherefore, the ground is wet (Q)."}, {"type": "heading", "content": "<PERSON><PERSON> (Denying the Consequent)"}, {"type": "text", "content": "Form:\n1. If P, then Q.\n2. Not Q.\nTherefore, Not P.\n\nExample:\n1. If it is raining (P), then the ground is wet (Q).\n2. The ground is not wet (Not Q).\nTherefore, it is not raining (Not P)."}, {"type": "interactive_element_placeholder", "interactive_element": {"type": "interactive_modus_ponens_tollens_builder", "prompt": "Complete the argument using the given form.", "scenarios": [{"form_type": "modus_ponens", "premise1_if": "If the alarm rings", "premise1_then": "it's time to wake up", "premise2_given": "The alarm rings", "conclusion_to_find": "it's time to wake up"}, {"form_type": "modus_tollens", "premise1_if": "If the car has gas", "premise1_then": "it will start", "premise2_given": "The car will not start", "conclusion_to_find": "The car does not have gas"}]}}, {"type": "tip", "content": "Be careful! Common mistakes involve affirming the consequent (If P then Q, Q, therefore P) or denying the antecedent (If P then Q, Not P, therefore Not Q). These are *invalid* forms."}]}, {"id": "rf-dr-l3-syllogisms", "title": "Syllogisms: Categorical Logic", "description": "Analyze arguments with quantified statements.", "xp_reward": 100, "content_blocks": [{"type": "text", "content": "A syllogism is a type of logical argument that applies deductive reasoning to arrive at a conclusion based on two propositions that are asserted or assumed to be true."}, {"type": "text", "content": "Categorical syllogisms are the most well-known type. They involve statements that categorize things using quantifiers like 'all', 'no', or 'some'."}, {"type": "heading", "content": "Structure of a Categorical Syllogism"}, {"type": "text", "content": "A standard categorical syllogism has:\n- Two premises.\n- One conclusion.\n- Three terms, each appearing twice: Major term (predicate of conclusion), Minor term (subject of conclusion), and Middle term (appears in both premises but not conclusion)."}, {"type": "example", "content": "1. All A are B. (Major Premise - contains Major term B and Middle term A)\n2. All C are A. (Minor Premise - contains Minor term C and Middle term A)\nTherefore, all C are B. (Conclusion - Minor term C, Major term B)"}, {"type": "interactive_element_placeholder", "interactive_element": {"type": "interactive_syllogism_evaluator", "prompt": "Identify the Major, Minor, and Middle terms, and determine if the syllogism is valid.", "syllogism": {"premise1": "All dogs are mammals.", "premise2": "Some pets are dogs.", "conclusion": "Some pets are mammals."}, "solution": {"major_term": "mammals", "minor_term": "pets", "middle_term": "dogs", "is_valid": true}}}, {"type": "text", "content": "There are rules to determine the validity of syllogisms (e.g., based on the distribution of terms). We'll explore these more advanced concepts later."}]}, {"id": "rf-dr-l4-testing-for-validity", "title": "Testing for Validity: Logical Form and Counterexamples", "description": "Evaluate the structure of deductive arguments.", "xp_reward": 100, "content_blocks": [{"type": "text", "content": "A deductive argument is **valid** if its conclusion logically follows from its premises. This means if the premises *were* true, the conclusion *would have to be* true. Validity is about the argument's structure (its logical form), not the actual truth of its premises."}, {"type": "heading", "content": "Logical Form"}, {"type": "text", "content": "We can represent the form of an argument using variables (like P and Q). For example, <PERSON><PERSON> has the form: If P then Q; P; Therefore Q. Any argument with this form is valid, regardless of what P and Q stand for."}, {"type": "heading", "content": "The Counterexample Method"}, {"type": "text", "content": "To show an argument form is *invalid*, we can find a **counterexample**: an instance of that form where all premises are true, but the conclusion is false. If you can find even one counterexample, the form is invalid."}, {"type": "example", "content": "Invalid Form: Affirming the Consequent\n1. If P, then Q.\n2. Q.\nTherefore, P.\n\nCounterexample:\n1. If something is a dog (P), then it is an animal (Q). (True)\n2. My cat is an animal (Q). (True)\nTherefore, my cat is a dog (P). (False!)"}, {"type": "interactive_element_placeholder", "interactive_element": {"type": "interactive_validity_tester_counterexample", "prompt": "Consider the argument form: 'If X then Y; Not X; Therefore Not Y'. Try to construct a counterexample (true premises, false conclusion) to test its validity.", "argument_form": {"premise1_template": "If {X} then {Y}.", "premise2_template": "Not {X}.", "conclusion_template": "Therefore, Not {Y}."}, "is_form_valid": false, "example_counterexample": {"X_instance": "it is a fish", "Y_instance": "it lives in water", "feedback": "If it is a fish, it lives in water (True). It is not a fish (e.g., a whale - True). Therefore, it does not live in water (False for a whale!). This shows the form is invalid."}}}]}, {"id": "rf-dr-l5-soundness", "title": "Soundness: Validity Plus True Premises", "description": "Understand what makes a deductive argument strong.", "xp_reward": 100, "content_blocks": [{"type": "text", "content": "While validity is about logical structure, we also care if an argument's premises are actually true. This brings us to the concept of **soundness**."}, {"type": "heading", "content": "Defining Soundness"}, {"type": "text", "content": "A deductive argument is **sound** if and only if:\n1. It is **valid** (the conclusion follows logically from the premises).\n2. All of its **premises are true**."}, {"type": "text", "content": "A sound argument guarantees a true conclusion. This is the gold standard for deductive arguments."}, {"type": "example", "content": "Valid but Unsound:\n1. All planets are made of cheese. (False Premise)\n2. Mars is a planet. (True Premise)\nTherefore, Mars is made of cheese. (False Conclusion, but argument is valid in form)\n\nSound Argument:\n1. All squares have four equal sides. (True Premise)\n2. This shape is a square. (True Premise, assuming it is)\nTherefore, this shape has four equal sides. (True Conclusion)"}, {"type": "interactive_element_placeholder", "interactive_element": {"type": "multiple_choice_text", "question_text": "An argument has a valid structure, but one of its premises is false. Is the argument sound?", "options": [{"id": "opt_s1", "text": "Yes, because it's valid.", "is_correct": false, "feedback_incorrect": "Validity is only one part of soundness."}, {"id": "opt_s2", "text": "No, because a premise is false.", "is_correct": true, "feedback_correct": "Correct! For soundness, an argument must be valid AND all its premises must be true."}, {"id": "opt_s3", "text": "It depends on the conclusion.", "is_correct": false, "feedback_incorrect": "Soundness is determined by validity and the truth of the premises, not directly by the conclusion's truth (though a sound argument guarantees a true conclusion)."}], "action_button_text": "Check Answer"}}, {"type": "tip", "content": "When evaluating a deductive argument, first check for validity. If it's invalid, it can't be sound. If it's valid, then check if all premises are true."}]}], "module_test": {"id": "rf-dr-mt1-deductive-analyst", "title": "Deductive Analyst", "description": "Evaluate the validity and soundness of deductive arguments.", "estimated_duration_minutes": 25, "questions": [{"id": "rf-dr-q1", "question_type": "test_deductive_argument_evaluator", "argument_text": "1. If a creature is a bird, it has wings.\n2. A bat has wings.\nTherefore, a bat is a bird.", "prompt_validity": "Is this argument valid?", "prompt_soundness_if_valid": "If valid, are all premises true (making it sound)?", "correct_is_valid": false, "validity_explanation_if_invalid": "This argument commits the fallacy of Affirming the Consequent. Just because bats have wings doesn't mean they are birds based on the first premise.", "correct_premises_true_for_soundness": null, "soundness_explanation": null, "feedback_correct": "Correct analysis!", "feedback_incorrect": "Review the definitions of validity (structure) and soundness (valid structure + true premises). This argument has an invalid form."}, {"id": "rf-dr-q2", "question_type": "test_deductive_argument_evaluator", "argument_text": "1. All cats are felines.\n2. All felines are mammals.\nTherefore, all cats are mammals.", "prompt_validity": "Is this argument valid?", "prompt_soundness_if_valid": "If valid, are all premises true (making it sound)?", "correct_is_valid": true, "validity_explanation_if_invalid": null, "correct_premises_true_for_soundness": true, "soundness_explanation": "Both premises are true, and the argument form is valid, so it is sound.", "feedback_correct": "Excellent! This is a sound argument.", "feedback_incorrect": "This argument is valid in form. Now consider if its premises are true to determine soundness."}, {"id": "rf-dr-q3", "question_type": "multiple_choice_text", "question_text": "Consider the argument: 'If it snows, the school will close. It did not snow. Therefore, the school will not close.' Which deductive form does this (incorrectly) resemble?", "options": [{"id": "q3opt1", "text": "<PERSON><PERSON>", "is_correct": false}, {"id": "q3opt2", "text": "<PERSON><PERSON>", "is_correct": false}, {"id": "q3opt3", "text": "Denying the Antecedent", "is_correct": true}, {"id": "q3opt4", "text": "Affirming the Consequent", "is_correct": false}], "feedback_correct": "Correct! This is the invalid form 'Denying the Antecedent'.", "feedback_incorrect": "Review the basic valid and invalid deductive forms."}]}}