import 'dart:io';

/// A script to check the status of interactive widgets in the project
/// 
/// Run this script with:
/// dart scripts/check_interactive_widgets.dart
///
/// This will output a report of all implemented and missing widgets

void main() async {
  print('Checking Interactive Widgets Status...\n');
  
  // Define the widgets directory
  final widgetsDir = Directory('lib/screens/interactive_widgets/widgets');
  
  // Check if the directory exists
  if (!await widgetsDir.exists()) {
    print('Error: Widgets directory not found at ${widgetsDir.path}');
    exit(1);
  }
  
  // Get all widget files
  final widgetFiles = await widgetsDir
      .list()
      .where((entity) => entity is File && entity.path.endsWith('.dart'))
      .toList();
  
  // Extract widget names from file names
  final implementedWidgets = widgetFiles.map((file) {
    final fileName = file.path.split(Platform.pathSeparator).last;
    return fileName.replaceAll('.dart', '');
  }).toList();
  
  // Sort alphabetically
  implementedWidgets.sort();
  
  // Count widgets
  final widgetCount = implementedWidgets.length;
  
  // Define expected widgets for Mathematics courses
  final expectedMathWidgets = [
    // Mathematical Thinking Module 1
    'interactive_pattern_animation_widget',
    'interactive_conditional_flow_widget',
    'interactive_proof_contradiction_widget',
    'interactive_logical_chain_constructor_widget',
    'interactive_fallacy_identification_widget',
    'interactive_logic_labyrinth_widget',
    
    // Mathematical Thinking Module 2
    'interactive_prime_number_explorer_widget',
    'interactive_divisibility_visualizer_widget',
    'interactive_mental_math_calculator_widget',
    'interactive_number_base_converter_widget',
    'interactive_numerical_playground_widget',
    
    // Mathematical Thinking Module 3
    'interactive_shape_identifier_widget',
    'interactive_transformation_identification_game_widget',
    'geometry_calculator_widget',
    'interactive_symmetry_explorer_widget',
    'rotation_interactive_game_widget',
    'interactive_shape_shifter_challenge_widget',
    
    // Mathematical Thinking Module 4
    'interactive_growing_patterns_visualizer_widget',
    'interactive_relationship_mapper_widget',
    'interactive_ratio_visualizer_widget',
    'interactive_data_visualization_tool_widget',
    'interactive_function_machine_widget',
    'interactive_pattern_prediction_puzzle_widget',
    
    // Mathematical Thinking Module 5
    'interactive_addition_subtraction_visualizer_widget',
    'interactive_multiplication_array_visualizer_widget',
    'interactive_division_visualizer_widget',
    'interactive_place_value_demonstrator_widget',
    'interactive_number_comparison_tool_widget',
    'interactive_number_navigator_challenge_widget',
    
    // Equations and Algebra Module 1
    'interactive_variable_explorer_widget',
    'interactive_expression_builder_widget',
    'interactive_expression_evaluator_widget',
    'interactive_like_terms_combiner_widget',
    'interactive_balance_scale_analogy_widget',
    'interactive_expression_explorer_widget',
    
    // Add more expected widgets here...
  ];
  
  // Find missing widgets
  final missingWidgets = expectedMathWidgets
      .where((widget) => !implementedWidgets.contains(widget))
      .toList();
  
  // Find implemented expected widgets
  final implementedExpectedWidgets = expectedMathWidgets
      .where((widget) => implementedWidgets.contains(widget))
      .toList();
  
  // Print report
  print('=== INTERACTIVE WIDGETS REPORT ===');
  print('Total widget files found: $widgetCount');
  print('Expected Mathematics widgets: ${expectedMathWidgets.length}');
  print('Implemented Mathematics widgets: ${implementedExpectedWidgets.length}');
  print('Missing Mathematics widgets: ${missingWidgets.length}');
  
  print('\n=== IMPLEMENTED MATHEMATICS WIDGETS ===');
  for (final widget in implementedExpectedWidgets) {
    print('✅ $widget');
  }
  
  print('\n=== MISSING MATHEMATICS WIDGETS ===');
  for (final widget in missingWidgets) {
    print('⚠️ $widget');
  }
  
  print('\n=== OTHER IMPLEMENTED WIDGETS ===');
  final otherWidgets = implementedWidgets
      .where((widget) => !expectedMathWidgets.contains(widget))
      .toList();
  
  for (final widget in otherWidgets) {
    print('- $widget');
  }
  
  // Check if widget factory includes all implemented widgets
  final factoryFile = File('lib/screens/interactive_widgets/interactive_widget_factory.dart');
  if (await factoryFile.exists()) {
    final factoryContent = await factoryFile.readAsString();
    
    print('\n=== WIDGET FACTORY REGISTRATION CHECK ===');
    int missingInFactory = 0;
    
    for (final widget in implementedWidgets) {
      // Convert from snake_case to camelCase for class name
      final className = widget
          .split('_')
          .map((part) => part.substring(0, 1).toUpperCase() + part.substring(1))
          .join('');
      
      if (!factoryContent.contains(className)) {
        print('⚠️ $widget is not registered in the widget factory');
        missingInFactory++;
      }
    }
    
    if (missingInFactory == 0) {
      print('✅ All implemented widgets are registered in the widget factory');
    } else {
      print('⚠️ $missingInFactory widgets are not registered in the widget factory');
    }
  } else {
    print('\n⚠️ Widget factory file not found at ${factoryFile.path}');
  }
  
  print('\nDone!');
}
