{"id": "supervised-learning-regression", "title": "Supervised Learning: Regression", "description": "Learn how to predict continuous values with regression models.", "order": 2, "lessons": [{"id": "understanding-supervised-learning", "title": "Understanding Supervised Learning", "description": "Learn the fundamentals of supervised learning, where models learn from labeled data.", "order": 1, "type": "interactive_lesson", "estimatedTimeMinutes": 10, "contentBlocks": [{"id": "slr_l1_s1_intro", "type": "lesson_screen", "order": 1, "estimatedTimeSeconds": 75, "content": {"headline": "Welcome to Supervised Learning!", "body_md": "Remember how Machine Learning enables systems to learn from data? **Supervised Learning** is one of the most common and powerful ways to do this.\n\nThe key idea? We 'supervise' the learning process by providing the algorithm with **labeled data** – that is, data where we already know the correct answer or outcome.", "visual": {"type": "giphy_search", "value": "teacher student learning"}, "interactive_element": {"type": "button", "button_text": "What is Labeled Data?"}, "audio_narration_url": null}}, {"id": "slr_l1_s2_labeled_data", "type": "lesson_screen", "order": 2, "estimatedTimeSeconds": 90, "content": {"headline": "The Power of Labeled Data", "body_md": "Labeled data consists of input features paired with corresponding output labels.\n\n**Example:**\n*   **Input (Features):** An email's text, sender, subject.\n*   **Output (Label):** Whether the email is \"Spam\" or \"Not Spam\".\n\nBy seeing many examples, the supervised learning algorithm learns to map inputs to the correct outputs. It learns the relationship between the features and the label.", "visual": {"type": "unsplash_search", "value": "data labels"}, "interactive_element": {"type": "multiple_choice_text", "question_text": "In an image dataset for recognizing handwritten digits, what would be the 'label' for an image of the number '7'?", "options": [{"text": "The pixel values of the image.", "is_correct": false, "feedback": "The pixel values are the input features, not the label."}, {"text": "The number '7'.", "is_correct": true, "feedback": "Correct! The label is the actual digit the image represents."}, {"text": "The algorithm used to classify it.", "is_correct": false, "feedback": "The algorithm is the learning mechanism, not the label for a data point."}]}, "audio_narration_url": null}}, {"id": "slr_l1_s3_goal", "type": "lesson_screen", "order": 3, "estimatedTimeSeconds": 75, "content": {"headline": "The Goal: Prediction", "body_md": "The ultimate goal of supervised learning is to create a model that can accurately predict the output label for **new, unseen data** based on its input features.\n\nIf our email spam filter is trained well, it should be able to correctly classify new emails it has never encountered before as either spam or not spam.\n\nThis predictive power is what makes supervised learning so useful!", "visual": {"type": "giphy_search", "value": "crystal ball prediction"}, "interactive_element": {"type": "button", "button_text": "Types of Problems?"}, "audio_narration_url": null}}, {"id": "slr_l1_s4_problem_types", "type": "lesson_screen", "order": 4, "estimatedTimeSeconds": 120, "content": {"headline": "Two Main Types of Supervised Problems", "body_md": "Supervised learning typically tackles two kinds of problems:\n\n1.  **Regression:** Predicting a **continuous numerical value**. \n    *   Example: Predicting the price of a house based on its features (size, location, etc.).\n    *   Example: Forecasting temperature for tomorrow.\n\n2.  **Classification:** Predicting a **discrete category or class**.\n    *   Example: Classifying an email as \"Spam\" or \"Not Spam.\"\n    *   Example: Identifying a handwritten digit (0-9).\n\nThis module focuses on **Regression**!", "visual": {"type": "unsplash_search", "value": "graph versus categories"}, "interactive_element": {"type": "multiple_choice_text", "question_text": "Predicting a student's exact score on a test (0-100) is a:", "options": [{"text": "Regression problem", "is_correct": true, "feedback": "Correct! The score is a continuous numerical value."}, {"text": "Classification problem", "is_correct": false, "feedback": "Not quite. Classification would be if you were predicting a grade (A, B, C) or pass/fail."}]}, "audio_narration_url": null}}, {"id": "slr_l1_s5_summary", "type": "lesson_screen", "order": 5, "estimatedTimeSeconds": 60, "content": {"headline": "Supervised Learning: Key Ideas", "body_md": "Let's recap what we've learned about Supervised Learning:\n\n*   It uses **labeled data** (inputs with known outputs).\n*   The goal is to create a model that can make **predictions on new data**.\n*   It solves two main types of problems: **Regression** (predicting continuous values) and **Classification** (predicting categories).\n\nNow, let's dive into Regression!", "visual": {"type": "giphy_search", "value": "summary checklist"}, "interactive_element": {"type": "button", "button_text": "Onwards to Regression!"}, "audio_narration_url": null}}]}, {"id": "linear-regression-basics", "title": "Linear Regression: Finding the Line", "description": "Explore the fundamentals of linear regression, a core technique for predicting continuous values.", "order": 2, "type": "interactive_lesson", "estimatedTimeMinutes": 12, "contentBlocks": [{"id": "slr_l2_s1_intro_linear_reg", "type": "lesson_screen", "order": 1, "estimatedTimeSeconds": 75, "content": {"headline": "Predicting with Lines: Linear Regression", "body_md": "Now that we understand supervised learning, let's look at a common **regression** technique: **Linear Regression**.\n\nIts goal is to find the **straight line** that best fits a set of data points. This line can then be used to make predictions.\n\nImagine plotting house prices against their sizes. Linear regression would try to draw a line through these points.", "visual": {"type": "giphy_search", "value": "drawing straight line graph"}, "interactive_element": {"type": "button", "button_text": "How does it work?"}, "audio_narration_url": null}}, {"id": "slr_l2_s2_equation_of_line", "type": "lesson_screen", "order": 2, "estimatedTimeSeconds": 90, "content": {"headline": "The Equation of a Line", "body_md": "Remember the equation of a straight line from algebra?  `y = mx + c`\n\n*   `y` is the value we want to predict (e.g., house price).\n*   `x` is our input feature (e.g., house size).\n*   `m` is the **slope** of the line (how much `y` changes for a one-unit change in `x`).\n*   `c` (or `b`) is the **y-intercept** (where the line crosses the y-axis).\n\nLinear regression aims to find the best `m` and `c` values.", "visual": {"type": "static_text", "value": "y = mx + c"}, "interactive_element": {"type": "multiple_choice_text", "question_text": "If 'm' (slope) is positive, what happens to 'y' as 'x' increases?", "options": [{"text": "y decreases", "is_correct": false, "feedback": "A positive slope means y increases as x increases."}, {"text": "y increases", "is_correct": true, "feedback": "Correct! A positive slope indicates a positive relationship."}, {"text": "y stays the same", "is_correct": false, "feedback": "A zero slope would mean y stays the same."}]}, "audio_narration_url": null}}, {"id": "slr_l2_s3_best_fit_line", "type": "lesson_screen", "order": 3, "estimatedTimeSeconds": 100, "content": {"headline": "Finding the \"Best Fit\" Line", "body_md": "But how does the algorithm find the *best* line? It tries to minimize the **error** between the predicted values (on the line) and the actual values (the data points).\n\nOne common method is called **Least Squares**. It aims to minimize the sum of the squared differences between the actual and predicted `y` values.\n\nThis sounds complex, but it's a mathematical way of finding the line that's closest to all data points overall.", "visual": {"type": "unsplash_search", "value": "line graph data points"}, "interactive_element": {"type": "button", "button_text": "Simple Example?"}, "audio_narration_url": null}}, {"id": "slr_l2_s4_simple_example", "type": "lesson_screen", "order": 4, "estimatedTimeSeconds": 120, "content": {"headline": "Example: Ice Cream Sales vs. Temperature", "body_md": "Let's say we have data on daily ice cream sales and the temperature on those days:\n\n| Temp (°C) | Sales ($) |\n|-----------|-----------|\n| 20        | 200       |\n| 25        | 250       |\n| 30        | 300       |\n| 35        | 350       |\n\nLinear regression would find a line like `Sales = 10 * Temperature + 0`. This model suggests that for every 1°C increase in temperature, sales increase by $10.", "visual": {"type": "unsplash_search", "value": "ice cream cone hot day"}, "interactive_element": {"type": "text_input", "question_text": "Using this model (Sales = 10*Temp), what would be the predicted sales if the temperature is 22°C?", "placeholder_text": "Enter predicted sales", "correct_answer_regex": "^(220|220.0)$", "feedback_correct": "Correct! Sales = 10 * 22 = $220.", "feedback_incorrect": "Hint: Sales = 10 * Temperature."}, "audio_narration_url": null}}, {"id": "slr_l2_s5_limitations", "type": "lesson_screen", "order": 5, "estimatedTimeSeconds": 90, "content": {"headline": "Limitations of Linear Regression", "body_md": "Linear regression is powerful but has limitations:\n\n*   It assumes a **linear relationship** between variables. If the true relationship is curved, linear regression won't fit well.\n*   It can be sensitive to **outliers** (extreme data points that don't follow the general trend).\n*   It assumes input features are **independent** (not strongly correlated with each other, in multiple linear regression).\n\nIt's a great starting point, but not always the perfect tool.", "visual": {"type": "giphy_search", "value": "warning sign caution"}, "interactive_element": {"type": "button", "button_text": "Got it!"}, "audio_narration_url": null}}, {"id": "slr_l2_s6_summary", "type": "lesson_screen", "order": 6, "estimatedTimeSeconds": 60, "content": {"headline": "Linear Regression: Recap", "body_md": "Key takeaways for Linear Regression:\n\n*   Aims to find the **best straight line** to fit data.\n*   Uses the equation `y = mx + c`.\n*   Often uses **Least Squares** to minimize errors.\n*   Great for predicting continuous values when relationships are linear.\n\nReady to explore more complex regression models?", "visual": {"type": "unsplash_search", "value": "graph line increasing"}, "interactive_element": {"type": "button", "button_text": "Next Lesson!"}, "audio_narration_url": null}}]}, {"id": "evaluating-regression-models", "title": "Evaluating Regression Models", "description": "Learn how to assess the performance of your regression models.", "order": 3, "type": "interactive_lesson", "estimatedTimeMinutes": 10, "contentBlocks": [{"id": "slr_l3_s1_why_evaluate", "type": "lesson_screen", "order": 1, "estimatedTimeSeconds": 75, "content": {"headline": "How Good is Your Prediction?", "body_md": "Creating a regression model is just the first step. We need to know how well it actually performs! **Model evaluation** helps us understand the accuracy and reliability of our predictions.\n\nWhy is this important? A model that makes wildly inaccurate predictions isn't very useful, and could even be harmful.", "visual": {"type": "giphy_search", "value": "measuring success"}, "interactive_element": {"type": "button", "button_text": "Common Metrics?"}, "audio_narration_url": null}}, {"id": "slr_l3_s2_mae", "type": "lesson_screen", "order": 2, "estimatedTimeSeconds": 90, "content": {"headline": "Mean Absolute Error (MAE)", "body_md": "One common metric is **Mean Absolute Error (MAE)**. It measures the average absolute difference between the actual values and the predicted values.\n\n`MAE = (1/n) * Σ |actual - predicted|`\n\n*   **Lower MAE is better.** It means your predictions are, on average, closer to the true values.\n*   It's easy to understand as it's in the same units as your target variable (e.g., if predicting price in dollars, MAE is in dollars).", "visual": {"type": "unsplash_search", "value": "ruler measuring"}, "interactive_element": {"type": "multiple_choice_text", "question_text": "If a model has an MAE of $50 for house price prediction, it means:", "options": [{"text": "All predictions are exactly $50 off.", "is_correct": false, "feedback": "Not necessarily all, but on average the predictions are $50 off."}, {"text": "The average prediction error is $50.", "is_correct": true, "feedback": "Correct! MAE tells you the average magnitude of errors."}, {"text": "The model is 50% accurate.", "is_correct": false, "feedback": "MAE measures error, not accuracy percentage directly."}]}, "audio_narration_url": null}}, {"id": "slr_l3_s3_mse_rmse", "type": "lesson_screen", "order": 3, "estimatedTimeSeconds": 100, "content": {"headline": "Mean Squared Error (MSE) & RMSE", "body_md": "**Mean Squared Error (MSE)** is another popular metric. It's the average of the squared differences between actual and predicted values.\n\n`MSE = (1/n) * Σ (actual - predicted)²`\n\n*   Squaring the errors penalizes larger errors more heavily.\n*   **Root Mean Squared Error (RMSE)** is simply the square root of MSE. This brings the metric back to the original units of the target variable, making it more interpretable like MAE.\n\n`RMSE = √MSE`\n\nRMSE is often preferred when large errors are particularly undesirable.", "visual": {"type": "giphy_search", "value": "calculator math"}, "interactive_element": {"type": "button", "button_text": "What about R-squared?"}, "audio_narration_url": null}}, {"id": "slr_l3_s4_r_squared", "type": "lesson_screen", "order": 4, "estimatedTimeSeconds": 120, "content": {"headline": "R-squared (Coefficient of Determination)", "body_md": "**R-squared** tells you the **proportion of the variance** in the dependent variable (what you're predicting) that is predictable from the independent variable(s) (your features).\n\n*   Ranges from 0 to 1 (or 0% to 100%).\n*   An R-squared of 0.75 means that 75% of the variation in the output can be explained by the input variable(s).\n*   **Higher R-squared is generally better**, indicating a better fit of the model to the data.\n\nHowever, R-squared alone can be misleading. A high R-squared doesn't always mean a good model, especially if you have many input features.", "visual": {"type": "unsplash_search", "value": "percentage chart"}, "interactive_element": {"type": "multiple_choice_text", "question_text": "If a model has an R-squared of 0.1, it means:", "options": [{"text": "The model explains 90% of the variance.", "is_correct": false, "feedback": "An R-squared of 0.1 means it explains 10% of the variance."}, {"text": "The model explains 10% of the variance.", "is_correct": true, "feedback": "Correct! This suggests the model doesn't fit the data very well."}, {"text": "The model is 10% accurate.", "is_correct": false, "feedback": "R-squared is about explained variance, not directly accuracy in that sense."}]}, "audio_narration_url": null}}, {"id": "slr_l3_s5_eval_summary", "type": "lesson_screen", "order": 5, "estimatedTimeSeconds": 75, "content": {"headline": "Evaluating Models: Recap", "body_md": "We've learned about key metrics for evaluating regression models:\n\n*   **MAE:** Average absolute error (easy to interpret).\n*   **MSE/RMSE:** Penalizes larger errors more (RMSE is in original units).\n*   **R-squared:** Proportion of variance explained by the model.\n\nChoosing the right metric depends on your specific problem and what aspects of performance are most important. It's often good to look at multiple metrics!", "visual": {"type": "giphy_search", "value": "report card check"}, "interactive_element": {"type": "button", "button_text": "Finish Lesson"}, "audio_narration_url": null}}]}], "moduleTest": {"id": "supervised-learning-regression-test", "title": "Module Test: Supervised Learning - Regression", "description": "Test your understanding of regression techniques in supervised learning.", "type": "module_test_interactive", "estimatedTimeMinutes": 15, "contentBlocks": [{"id": "slr_test_q1_labeled_data", "type": "question_screen", "order": 1, "estimatedTimeSeconds": 60, "content": {"headline": "Question 1: Labeled Data", "body_md": "In supervised learning, what is the primary characteristic of the data used for training?", "visual": {"type": "giphy_search", "value": "data tag"}, "interactive_element": {"type": "multiple_choice_text", "question_text": "Primary characteristic of training data in supervised learning?", "options": [{"text": "It is completely random and unstructured.", "is_correct": false, "feedback": "While data can be messy, supervised learning requires some structure, specifically labels."}, {"text": "It consists of input features paired with known output labels.", "is_correct": true, "feedback": "Correct! This 'supervision' is key to how the model learns."}, {"text": "It contains no output information, only input features.", "is_correct": false, "feedback": "This describes data used in unsupervised learning."}]}, "audio_narration_url": null}}, {"id": "slr_test_q2_regression_vs_classification", "type": "question_screen", "order": 2, "estimatedTimeSeconds": 75, "content": {"headline": "Question 2: Regression vs. Classification", "body_md": "Predicting whether a customer will click on an ad (Yes/No) is an example of what kind of supervised learning problem?", "visual": {"type": "unsplash_search", "value": "yes no options"}, "interactive_element": {"type": "multiple_choice_text", "question_text": "Predicting ad click (Yes/No) is a:", "options": [{"text": "Regression problem", "is_correct": false, "feedback": "Regression predicts continuous values (like price or temperature). Yes/No is a category."}, {"text": "Classification problem", "is_correct": true, "feedback": "Correct! We are classifying the outcome into discrete categories (Yes or No)."}, {"text": "Unsupervised learning problem", "is_correct": false, "feedback": "Since we'd train this with data on past clicks (labeled data), it's supervised."}]}, "audio_narration_url": null}}, {"id": "slr_test_q3_linear_regression_goal", "type": "question_screen", "order": 3, "estimatedTimeSeconds": 75, "content": {"headline": "Question 3: Linear Regression Goal", "body_md": "What is the primary goal of a simple linear regression model?", "visual": {"type": "giphy_search", "value": "straight line graph"}, "interactive_element": {"type": "multiple_choice_text", "question_text": "Primary goal of simple linear regression?", "options": [{"text": "To find the most complex curve that fits all data points perfectly.", "is_correct": false, "feedback": "Linear regression specifically looks for a straight line, and a perfect fit might indicate overfitting."}, {"text": "To find the straight line that best represents the relationship between an input feature and a continuous output.", "is_correct": true, "feedback": "Exactly! It's about finding that best-fitting straight line."}, {"text": "To group data points into distinct clusters.", "is_correct": false, "feedback": "This describes clustering, an unsupervised learning technique."}]}, "audio_narration_url": null}}, {"id": "slr_test_q4_mae_interpretation", "type": "question_screen", "order": 4, "estimatedTimeSeconds": 60, "content": {"headline": "Question 4: Interpreting MAE", "body_md": "If a regression model predicting house prices has a Mean Absolute Error (MAE) of $15,000, what does this signify?", "visual": {"type": "unsplash_search", "value": "house price tag"}, "interactive_element": {"type": "multiple_choice_text", "question_text": "What does an MAE of $15,000 mean?", "options": [{"text": "The model's predictions are always $15,000 higher than the actual price.", "is_correct": false, "feedback": "MAE is an average of absolute errors; it doesn't specify the direction of all errors."}, {"text": "On average, the model's price predictions are off by $15,000 (either higher or lower).", "is_correct": true, "feedback": "Correct! MAE gives the average magnitude of the prediction errors."}, {"text": "The model explains 15% of the variance in house prices.", "is_correct": false, "feedback": "This sounds more like R-squared, not MAE."}]}, "audio_narration_url": null}}, {"id": "slr_test_q5_r_squared_meaning", "type": "question_screen", "order": 5, "estimatedTimeSeconds": 75, "content": {"headline": "Question 5: R-squared", "body_md": "An R-squared value of 0.65 for a regression model suggests that:", "visual": {"type": "giphy_search", "value": "pie chart"}, "interactive_element": {"type": "multiple_choice_text", "question_text": "R-squared of 0.65 means:", "options": [{"text": "65% of the model's predictions are correct.", "is_correct": false, "feedback": "R-squared isn't a direct measure of prediction accuracy in that sense."}, {"text": "The model's predictions are, on average, 65% away from the actual values.", "is_correct": false, "feedback": "This sounds more like a percentage error, not R-squared."}, {"text": "65% of the variation in the output variable can be explained by the input variable(s).", "is_correct": true, "feedback": "Correct! R-squared measures the proportion of variance explained."}]}, "audio_narration_url": null}}]}}