import 'package:flutter/material.dart';
import 'dart:math' as math;

/// A comprehensive assessment widget for testing understanding of function fundamentals
class InteractiveFunctionFundamentalsTestWidget extends StatefulWidget {
  /// The data for this widget
  final Map<String, dynamic> data;

  /// Optional callback when the widget state changes
  final Function(bool isCompleted)? onStateChanged;

  const InteractiveFunctionFundamentalsTestWidget({
    Key? key,
    required this.data,
    this.onStateChanged,
  }) : super(key: key);

  /// Factory constructor to create the widget from JSON data
  factory InteractiveFunctionFundamentalsTestWidget.fromData(Map<String, dynamic> data) {
    return InteractiveFunctionFundamentalsTestWidget(
      data: data,
    );
  }

  @override
  State<InteractiveFunctionFundamentalsTestWidget> createState() => _InteractiveFunctionFundamentalsTestWidgetState();
}

class _InteractiveFunctionFundamentalsTestWidgetState extends State<InteractiveFunctionFundamentalsTestWidget> with SingleTickerProviderStateMixin {
  // Current question index
  int _currentQuestionIndex = 0;

  // List of questions
  late List<Map<String, dynamic>> _questions;

  // User's answers
  final List<int?> _userAnswers = [];

  // Whether the test is completed
  bool _isCompleted = false;

  // Whether to show results
  bool _showResults = false;

  // Colors
  late Color _primaryColor;
  late Color _secondaryColor;
  late Color _accentColor;
  late Color _backgroundColor;
  late Color _textColor;

  // Animation controller for transitions
  late AnimationController _animationController;
  late Animation<double> _animation;

  // Score
  int _score = 0;

  // Maximum score
  late int _maxScore;

  // Whether to show explanation for current question
  bool _showExplanation = false;

  @override
  void initState() {
    super.initState();

    // Initialize colors
    _primaryColor = _parseColor(widget.data['primary_color']) ?? Colors.blue;
    _secondaryColor = _parseColor(widget.data['secondary_color']) ?? Colors.orange;
    _accentColor = _parseColor(widget.data['accent_color']) ?? Colors.green;
    _backgroundColor = _parseColor(widget.data['background_color']) ?? Colors.white;
    _textColor = _parseColor(widget.data['text_color']) ?? Colors.black87;

    // Initialize animation controller
    _animationController = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 500),
    );

    _animation = CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeInOut,
    );

    // Initialize questions
    _questions = widget.data['questions'] != null
        ? List<Map<String, dynamic>>.from(widget.data['questions'])
        : _getDefaultQuestions();

    // Initialize user answers
    _userAnswers.addAll(List<int?>.filled(_questions.length, null));

    // Set maximum score
    _maxScore = _questions.length;
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  // Parse color from hex string
  Color? _parseColor(String? hexString) {
    if (hexString == null) return null;

    hexString = hexString.replaceAll('#', '');
    if (hexString.length == 6) {
      hexString = 'FF$hexString';
    }

    return Color(int.parse(hexString, radix: 16));
  }

  // Get default questions if none provided
  List<Map<String, dynamic>> _getDefaultQuestions() {
    return [
      {
        'type': 'multiple_choice',
        'question': 'Which of the following relations is a function?',
        'options': [
          'y = x²',
          'x² + y² = 1',
          'y² = x',
          'x = |y|',
        ],
        'correctAnswer': 0,
        'explanation': 'y = x² is a function because each x-value corresponds to exactly one y-value. The other options fail the vertical line test because some x-values correspond to multiple y-values.',
        'image': 'assets/images/function_test/function_identification.png',
      },
      {
        'type': 'multiple_choice',
        'question': 'If f(x) = 2x + 3, what is f(4)?',
        'options': [
          '7',
          '8',
          '11',
          '14',
        ],
        'correctAnswer': 2,
        'explanation': 'To evaluate f(4), substitute 4 for x in the function f(x) = 2x + 3:\nf(4) = 2(4) + 3 = 8 + 3 = 11',
      },
      {
        'type': 'multiple_choice',
        'question': 'What is the domain of the function f(x) = √x?',
        'options': [
          'All real numbers',
          'All non-negative real numbers',
          'All positive real numbers',
          'All real numbers except 0',
        ],
        'correctAnswer': 1,
        'explanation': 'The domain of f(x) = √x is all non-negative real numbers (x ≥ 0) because the square root of a negative number is not a real number.',
        'image': 'assets/images/function_test/sqrt_function.png',
      },
      {
        'type': 'multiple_choice',
        'question': 'What is the range of the function f(x) = x²?',
        'options': [
          'All real numbers',
          'All non-negative real numbers',
          'All positive real numbers',
          'All real numbers except 0',
        ],
        'correctAnswer': 1,
        'explanation': 'The range of f(x) = x² is all non-negative real numbers (y ≥ 0) because the square of any real number is always non-negative.',
        'image': 'assets/images/function_test/quadratic_function.png',
      },
      {
        'type': 'multiple_choice',
        'question': 'Which of the following is NOT a valid way to represent a function?',
        'options': [
          'Equation',
          'Table of values',
          'Graph',
          'Circle',
        ],
        'correctAnswer': 3,
        'explanation': 'A function can be represented as an equation, a table of values, or a graph. A circle is a shape, not a representation of a function (though a circle can be described by an equation that is not a function).',
      },
      {
        'type': 'multiple_choice',
        'question': 'If g(x) = |x - 3|, what is g(5)?',
        'options': [
          '2',
          '3',
          '5',
          '8',
        ],
        'correctAnswer': 0,
        'explanation': 'To evaluate g(5), substitute 5 for x in the function g(x) = |x - 3|:\ng(5) = |5 - 3| = |2| = 2',
      },
      {
        'type': 'multiple_choice',
        'question': 'Which of the following functions has a domain restriction?',
        'options': [
          'f(x) = 2x + 1',
          'f(x) = x²',
          'f(x) = 1/x',
          'f(x) = |x|',
        ],
        'correctAnswer': 2,
        'explanation': 'The function f(x) = 1/x has a domain restriction because division by zero is undefined. The domain is all real numbers except 0 (x ≠ 0).',
      },
      {
        'type': 'multiple_choice',
        'question': 'If h(x) = x² - 4 and h(a) = 5, what is the value of a?',
        'options': [
          '±3',
          '±1',
          '9',
          '3',
        ],
        'correctAnswer': 0,
        'explanation': 'If h(a) = 5, then a² - 4 = 5, which means a² = 9, so a = ±3. Both 3 and -3 are valid values for a.',
      },
      {
        'type': 'multiple_choice',
        'question': 'Which of the following is a one-to-one function?',
        'options': [
          'f(x) = x²',
          'f(x) = x³',
          'f(x) = |x|',
          'f(x) = 5',
        ],
        'correctAnswer': 1,
        'explanation': 'A one-to-one function maps each input to a unique output AND each output to a unique input. The function f(x) = x³ is one-to-one because different inputs always produce different outputs.',
        'image': 'assets/images/function_test/cubic_function.png',
      },
      {
        'type': 'multiple_choice',
        'question': 'What is the vertical line test used for?',
        'options': [
          'To determine if a relation is a function',
          'To determine if a function is one-to-one',
          'To find the domain of a function',
          'To find the range of a function',
        ],
        'correctAnswer': 0,
        'explanation': 'The vertical line test is used to determine if a relation is a function. If any vertical line intersects the graph more than once, the relation is not a function.',
        'image': 'assets/images/function_test/vertical_line_test.png',
      },
    ];
  }

  // Select an answer
  void _selectAnswer(int answerIndex) {
    setState(() {
      _userAnswers[_currentQuestionIndex] = answerIndex;
    });
  }

  // Move to the next question
  void _nextQuestion() {
    if (_currentQuestionIndex < _questions.length - 1) {
      // Start animation
      _animationController.forward().then((_) {
        setState(() {
          _currentQuestionIndex++;
          _showExplanation = false;
        });
        _animationController.reverse();
      });
    } else {
      // Show results if all questions have been answered
      _calculateScore();
      setState(() {
        _showResults = true;
      });
    }
  }

  // Move to the previous question
  void _previousQuestion() {
    if (_currentQuestionIndex > 0) {
      // Start animation
      _animationController.forward().then((_) {
        setState(() {
          _currentQuestionIndex--;
          _showExplanation = false;
        });
        _animationController.reverse();
      });
    }
  }

  // Calculate the score
  void _calculateScore() {
    _score = 0;
    for (int i = 0; i < _questions.length; i++) {
      if (_userAnswers[i] == _questions[i]['correctAnswer']) {
        _score++;
      }
    }

    // Mark as completed if score is at least 70%
    if (_score >= (_maxScore * 0.7).round()) {
      _isCompleted = true;
      widget.onStateChanged?.call(true);
    }
  }

  // Restart the test
  void _restartTest() {
    setState(() {
      _currentQuestionIndex = 0;
      _userAnswers.clear();
      _userAnswers.addAll(List<int?>.filled(_questions.length, null));
      _showResults = false;
      _isCompleted = false;
      _score = 0;
      _showExplanation = false;
    });
  }

  // Toggle explanation visibility
  void _toggleExplanation() {
    setState(() {
      _showExplanation = !_showExplanation;
    });
  }

  // Build the question view
  Widget _buildQuestion() {
    final currentQuestion = _questions[_currentQuestionIndex];
    final userAnswer = _userAnswers[_currentQuestionIndex];
    final hasAnswered = userAnswer != null;

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Title
        Text(
          widget.data['title'] ?? 'Function Fundamentals Test',
          style: TextStyle(
            fontSize: 18,
            fontWeight: FontWeight.bold,
            color: _textColor,
          ),
        ),

        const SizedBox(height: 8),

        // Progress indicator
        LinearProgressIndicator(
          value: (_currentQuestionIndex + 1) / _questions.length,
          backgroundColor: Colors.grey[300],
          valueColor: AlwaysStoppedAnimation<Color>(_primaryColor),
        ),

        Padding(
          padding: const EdgeInsets.symmetric(vertical: 8),
          child: Text(
            'Question ${_currentQuestionIndex + 1} of ${_questions.length}',
            style: TextStyle(
              fontSize: 14,
              color: _textColor.withOpacity(0.7),
            ),
          ),
        ),

        // Question
        Container(
          padding: const EdgeInsets.all(12),
          decoration: BoxDecoration(
            color: _primaryColor.withOpacity(0.1),
            borderRadius: BorderRadius.circular(8),
            border: Border.all(color: _primaryColor),
          ),
          child: Text(
            currentQuestion['question'],
            style: TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.bold,
              color: _textColor,
            ),
          ),
        ),

        const SizedBox(height: 12),

        // Image if available
        if (currentQuestion['image'] != null)
          Container(
            height: 150,
            width: double.infinity,
            margin: const EdgeInsets.only(bottom: 12),
            decoration: BoxDecoration(
              color: Colors.grey[200],
              borderRadius: BorderRadius.circular(8),
              border: Border.all(color: Colors.grey[300]!),
            ),
            child: Center(
              child: Text(
                'Image: ${currentQuestion['image']}',
                style: TextStyle(
                  fontSize: 14,
                  color: _textColor.withOpacity(0.7),
                ),
              ),
            ),
          ),

        // Options
        ...List.generate(
          (currentQuestion['options'] as List).length,
          (index) => Container(
            margin: const EdgeInsets.only(bottom: 8),
            child: InkWell(
              onTap: () => _selectAnswer(index),
              child: Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: userAnswer == index
                      ? _primaryColor.withOpacity(0.2)
                      : Colors.grey[100],
                  borderRadius: BorderRadius.circular(8),
                  border: Border.all(
                    color: userAnswer == index
                        ? _primaryColor
                        : Colors.grey[300]!,
                  ),
                ),
                child: Row(
                  children: [
                    Container(
                      width: 24,
                      height: 24,
                      decoration: BoxDecoration(
                        shape: BoxShape.circle,
                        color: userAnswer == index
                            ? _primaryColor
                            : Colors.white,
                        border: Border.all(
                          color: userAnswer == index
                              ? _primaryColor
                              : Colors.grey[400]!,
                        ),
                      ),
                      child: userAnswer == index
                          ? const Icon(
                              Icons.check,
                              size: 16,
                              color: Colors.white,
                            )
                          : null,
                    ),
                    const SizedBox(width: 12),
                    Expanded(
                      child: Text(
                        currentQuestion['options'][index],
                        style: TextStyle(
                          fontSize: 14,
                          color: _textColor,
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ),
        ),

        const SizedBox(height: 16),

        // Explanation
        if (hasAnswered && _showExplanation)
          Container(
            padding: const EdgeInsets.all(12),
            margin: const EdgeInsets.only(bottom: 16),
            decoration: BoxDecoration(
              color: _accentColor.withOpacity(0.1),
              borderRadius: BorderRadius.circular(8),
              border: Border.all(color: _accentColor),
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'Explanation:',
                  style: TextStyle(
                    fontSize: 14,
                    fontWeight: FontWeight.bold,
                    color: _accentColor,
                  ),
                ),
                const SizedBox(height: 4),
                Text(
                  currentQuestion['explanation'],
                  style: TextStyle(
                    fontSize: 14,
                    color: _textColor,
                  ),
                ),
              ],
            ),
          ),

        // Navigation buttons
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            // Previous button
            ElevatedButton(
              onPressed: _currentQuestionIndex > 0 ? _previousQuestion : null,
              style: ElevatedButton.styleFrom(
                backgroundColor: _secondaryColor,
                foregroundColor: Colors.white,
              ),
              child: const Text('Previous'),
            ),

            // Show explanation button
            if (hasAnswered)
              ElevatedButton(
                onPressed: _toggleExplanation,
                style: ElevatedButton.styleFrom(
                  backgroundColor: _accentColor,
                  foregroundColor: Colors.white,
                ),
                child: Text(_showExplanation ? 'Hide Explanation' : 'Show Explanation'),
              ),

            // Next button
            ElevatedButton(
              onPressed: hasAnswered ? _nextQuestion : null,
              style: ElevatedButton.styleFrom(
                backgroundColor: _primaryColor,
                foregroundColor: Colors.white,
              ),
              child: Text(_currentQuestionIndex < _questions.length - 1 ? 'Next' : 'Finish'),
            ),
          ],
        ),

        // Widget name tag
        if (widget.data['showNameTag'] ?? true)
          Padding(
            padding: const EdgeInsets.only(top: 8),
            child: Text(
              'InteractiveFunctionFundamentalsTest',
              style: TextStyle(
                color: Colors.grey[600],
                fontSize: 12,
                fontStyle: FontStyle.italic,
              ),
            ),
          ),
      ],
    );
  }

  // Build the results view
  Widget _buildResults() {
    final passThreshold = (_maxScore * 0.7).round();
    final isPassed = _score >= passThreshold;

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Title
        Text(
          widget.data['title'] ?? 'Function Fundamentals Test',
          style: TextStyle(
            fontSize: 18,
            fontWeight: FontWeight.bold,
            color: _textColor,
          ),
        ),

        const SizedBox(height: 16),

        // Results header
        Container(
          padding: const EdgeInsets.all(12),
          decoration: BoxDecoration(
            color: isPassed ? _accentColor.withOpacity(0.1) : _secondaryColor.withOpacity(0.1),
            borderRadius: BorderRadius.circular(8),
            border: Border.all(color: isPassed ? _accentColor : _secondaryColor),
          ),
          child: Column(
            children: [
              Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Icon(
                    isPassed ? Icons.check_circle : Icons.info,
                    color: isPassed ? _accentColor : _secondaryColor,
                    size: 32,
                  ),
                  const SizedBox(width: 8),
                  Text(
                    isPassed ? 'Congratulations!' : 'Keep Learning!',
                    style: TextStyle(
                      fontSize: 18,
                      fontWeight: FontWeight.bold,
                      color: isPassed ? _accentColor : _secondaryColor,
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 8),
              Text(
                isPassed
                    ? 'You have successfully completed the Function Fundamentals Test!'
                    : 'You need more practice with function fundamentals.',
                textAlign: TextAlign.center,
                style: TextStyle(
                  fontSize: 14,
                  color: _textColor,
                ),
              ),
            ],
          ),
        ),

        const SizedBox(height: 16),

        // Score
        Container(
          padding: const EdgeInsets.all(12),
          decoration: BoxDecoration(
            color: Colors.grey[100],
            borderRadius: BorderRadius.circular(8),
            border: Border.all(color: Colors.grey[300]!),
          ),
          child: Column(
            children: [
              Text(
                'Your Score',
                style: TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.bold,
                  color: _textColor,
                ),
              ),
              const SizedBox(height: 8),
              Text(
                '$_score / $_maxScore',
                style: TextStyle(
                  fontSize: 24,
                  fontWeight: FontWeight.bold,
                  color: isPassed ? _accentColor : _secondaryColor,
                ),
              ),
              const SizedBox(height: 8),
              Text(
                'Passing score: $passThreshold',
                style: TextStyle(
                  fontSize: 14,
                  color: _textColor.withOpacity(0.7),
                ),
              ),
            ],
          ),
        ),

        const SizedBox(height: 16),

        // Question summary
        Text(
          'Question Summary',
          style: TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.bold,
            color: _textColor,
          ),
        ),

        const SizedBox(height: 8),

        // List of questions with correct/incorrect indicators
        Container(
          height: 200,
          decoration: BoxDecoration(
            color: Colors.grey[100],
            borderRadius: BorderRadius.circular(8),
            border: Border.all(color: Colors.grey[300]!),
          ),
          child: ListView.builder(
            itemCount: _questions.length,
            itemBuilder: (context, index) {
              final question = _questions[index];
              final userAnswer = _userAnswers[index];
              final isCorrect = userAnswer == question['correctAnswer'];

              return Container(
                padding: const EdgeInsets.all(8),
                decoration: BoxDecoration(
                  border: Border(
                    bottom: BorderSide(
                      color: Colors.grey[300]!,
                      width: 1,
                    ),
                  ),
                ),
                child: Row(
                  children: [
                    Container(
                      width: 24,
                      height: 24,
                      decoration: BoxDecoration(
                        shape: BoxShape.circle,
                        color: isCorrect ? _accentColor : _secondaryColor,
                      ),
                      child: Icon(
                        isCorrect ? Icons.check : Icons.close,
                        size: 16,
                        color: Colors.white,
                      ),
                    ),
                    const SizedBox(width: 8),
                    Expanded(
                      child: Text(
                        'Question ${index + 1}: ${question['question']}',
                        maxLines: 1,
                        overflow: TextOverflow.ellipsis,
                        style: TextStyle(
                          fontSize: 14,
                          color: _textColor,
                        ),
                      ),
                    ),
                  ],
                ),
              );
            },
          ),
        ),

        const SizedBox(height: 16),

        // Action buttons
        Row(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            ElevatedButton(
              onPressed: _restartTest,
              style: ElevatedButton.styleFrom(
                backgroundColor: _primaryColor,
                foregroundColor: Colors.white,
                padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
              ),
              child: const Text('Restart Test'),
            ),
          ],
        ),

        // Widget name tag
        if (widget.data['showNameTag'] ?? true)
          Padding(
            padding: const EdgeInsets.only(top: 8),
            child: Text(
              'InteractiveFunctionFundamentalsTest',
              style: TextStyle(
                color: Colors.grey[600],
                fontSize: 12,
                fontStyle: FontStyle.italic,
              ),
            ),
          ),
      ],
    );
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: _backgroundColor,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withOpacity(0.2),
            spreadRadius: 1,
            blurRadius: 3,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: _showResults ? _buildResults() : _buildQuestion(),
    );
  }
}
