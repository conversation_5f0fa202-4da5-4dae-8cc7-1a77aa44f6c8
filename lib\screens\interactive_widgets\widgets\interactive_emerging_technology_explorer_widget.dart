import 'package:flutter/material.dart';
import 'dart:math' as math;

/// A widget that allows users to explore emerging technologies and their potential impacts
class InteractiveEmergingTechnologyExplorerWidget extends StatefulWidget {
  /// The data for this widget
  final Map<String, dynamic> data;

  /// Optional callback when the widget state changes
  final Function(bool isCompleted)? onStateChanged;

  const InteractiveEmergingTechnologyExplorerWidget({
    super.key,
    required this.data,
    this.onStateChanged,
  });

  /// Factory constructor to create the widget from JSON data
  factory InteractiveEmergingTechnologyExplorerWidget.fromData(Map<String, dynamic> data) {
    return InteractiveEmergingTechnologyExplorerWidget(
      data: data,
    );
  }

  @override
  State<InteractiveEmergingTechnologyExplorerWidget> createState() => _InteractiveEmergingTechnologyExplorerWidgetState();
}

class _InteractiveEmergingTechnologyExplorerWidgetState extends State<InteractiveEmergingTechnologyExplorerWidget> {
  // Technologies data
  late List<EmergingTechnology> _technologies;
  late int _currentTechIndex;
  
  // UI state
  late bool _isCompleted;
  late bool _showImpactAnalysis;
  late Map<String, double> _userImpactRatings;
  late List<String> _completedTechnologies;
  late PageController _pageController;
  
  // Colors
  late Color _primaryColor;
  late Color _secondaryColor;
  late Color _accentColor;
  late Color _backgroundColor;
  late Color _textColor;

  @override
  void initState() {
    super.initState();
    _initializeWidget();
  }

  @override
  void dispose() {
    _pageController.dispose();
    super.dispose();
  }

  void _initializeWidget() {
    // Initialize colors
    _primaryColor = _parseColor(widget.data['primaryColor'] ?? '#2196F3');
    _secondaryColor = _parseColor(widget.data['secondaryColor'] ?? '#FF9800');
    _accentColor = _parseColor(widget.data['accentColor'] ?? '#4CAF50');
    _backgroundColor = _parseColor(widget.data['backgroundColor'] ?? '#FFFFFF');
    _textColor = _parseColor(widget.data['textColor'] ?? '#212121');

    // Initialize technologies data
    final List<dynamic> technologiesData = widget.data['technologies'] ?? [];
    _technologies = technologiesData.map((techData) => EmergingTechnology.fromJson(techData)).toList();
    
    // Initialize UI state
    _currentTechIndex = 0;
    _isCompleted = false;
    _showImpactAnalysis = false;
    _userImpactRatings = {};
    _completedTechnologies = [];
    _pageController = PageController();
  }

  Color _parseColor(String colorString) {
    if (colorString.startsWith('#')) {
      return Color(int.parse('FF${colorString.substring(1)}', radix: 16));
    }
    return Colors.blue;
  }

  void _selectTechnology(int index) {
    setState(() {
      _currentTechIndex = index;
      _showImpactAnalysis = false;
      _pageController.animateToPage(
        0,
        duration: const Duration(milliseconds: 300),
        curve: Curves.easeInOut,
      );
    });
  }

  void _startImpactAnalysis() {
    setState(() {
      _showImpactAnalysis = true;
      
      // Initialize ratings if not already set
      EmergingTechnology tech = _technologies[_currentTechIndex];
      for (var impact in tech.potentialImpacts) {
        if (!_userImpactRatings.containsKey('${tech.id}_${impact.id}')) {
          _userImpactRatings['${tech.id}_${impact.id}'] = 0.0;
        }
      }
    });
  }

  void _updateImpactRating(String techId, String impactId, double value) {
    setState(() {
      _userImpactRatings['${techId}_${impactId}'] = value;
    });
  }

  void _submitAnalysis() {
    EmergingTechnology tech = _technologies[_currentTechIndex];
    
    // Mark this technology as completed
    if (!_completedTechnologies.contains(tech.id)) {
      setState(() {
        _completedTechnologies.add(tech.id);
        
        // Check if all technologies have been analyzed
        if (_completedTechnologies.length == _technologies.length && !_isCompleted) {
          _isCompleted = true;
          widget.onStateChanged?.call(true);
        }
      });
    }
    
    // Move to the next page
    _pageController.animateToPage(
      1,
      duration: const Duration(milliseconds: 300),
      curve: Curves.easeInOut,
    );
  }

  void _nextTechnology() {
    if (_currentTechIndex < _technologies.length - 1) {
      setState(() {
        _currentTechIndex++;
        _showImpactAnalysis = false;
        _pageController.animateToPage(
          0,
          duration: const Duration(milliseconds: 300),
          curve: Curves.easeInOut,
        );
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    if (_technologies.isEmpty) {
      return const Center(child: Text('No technologies available'));
    }

    EmergingTechnology currentTech = _technologies[_currentTechIndex];

    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
        side: BorderSide(color: _primaryColor.withOpacity(0.5), width: 1),
      ),
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Title
            Text(
              widget.data['title'] ?? 'Emerging Technology Explorer',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
                color: _textColor,
              ),
            ),

            const SizedBox(height: 16),

            // Technology selector
            Container(
              height: 50,
              decoration: BoxDecoration(
                color: _backgroundColor,
                borderRadius: BorderRadius.circular(8),
                border: Border.all(color: _primaryColor.withOpacity(0.3)),
              ),
              child: ListView.builder(
                scrollDirection: Axis.horizontal,
                itemCount: _technologies.length,
                itemBuilder: (context, index) {
                  bool isSelected = index == _currentTechIndex;
                  bool isCompleted = _completedTechnologies.contains(_technologies[index].id);
                  
                  return GestureDetector(
                    onTap: () => _selectTechnology(index),
                    child: Container(
                      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                      decoration: BoxDecoration(
                        color: isSelected ? _primaryColor : _backgroundColor,
                        borderRadius: BorderRadius.circular(8),
                      ),
                      child: Row(
                        children: [
                          if (isCompleted)
                            Icon(
                              Icons.check_circle,
                              size: 16,
                              color: isSelected ? Colors.white : _accentColor,
                            ),
                          if (isCompleted)
                            const SizedBox(width: 4),
                          Text(
                            _technologies[index].name,
                            style: TextStyle(
                              color: isSelected ? Colors.white : _primaryColor,
                              fontWeight: isSelected ? FontWeight.bold : FontWeight.normal,
                            ),
                          ),
                        ],
                      ),
                    ),
                  );
                },
              ),
            ),

            const SizedBox(height: 16),

            // Main content
            Expanded(
              child: PageView(
                controller: _pageController,
                physics: const NeverScrollableScrollPhysics(),
                children: [
                  // Technology information page
                  _buildTechnologyInfoPage(currentTech),
                  
                  // Results page
                  _buildResultsPage(currentTech),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildTechnologyInfoPage(EmergingTechnology tech) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Technology description
        Expanded(
          child: SingleChildScrollView(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Technology name and status
                Container(
                  padding: const EdgeInsets.all(12),
                  decoration: BoxDecoration(
                    color: _primaryColor.withOpacity(0.1),
                    borderRadius: BorderRadius.circular(8),
                    border: Border.all(color: _primaryColor.withOpacity(0.3)),
                  ),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Row(
                        children: [
                          Expanded(
                            child: Text(
                              tech.name,
                              style: TextStyle(
                                fontWeight: FontWeight.bold,
                                color: _textColor,
                                fontSize: 16,
                              ),
                            ),
                          ),
                          Container(
                            padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                            decoration: BoxDecoration(
                              color: _getStatusColor(tech.status),
                              borderRadius: BorderRadius.circular(12),
                            ),
                            child: Text(
                              tech.status,
                              style: const TextStyle(
                                color: Colors.white,
                                fontSize: 12,
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                          ),
                        ],
                      ),
                      const SizedBox(height: 8),
                      Text(
                        tech.description,
                        style: TextStyle(
                          color: _textColor.withOpacity(0.8),
                        ),
                      ),
                    ],
                  ),
                ),

                const SizedBox(height: 16),

                // Key features
                Text(
                  'Key Features:',
                  style: TextStyle(
                    fontWeight: FontWeight.bold,
                    color: _textColor,
                    fontSize: 16,
                  ),
                ),
                const SizedBox(height: 8),
                ...tech.keyFeatures.map((feature) => Padding(
                  padding: const EdgeInsets.only(bottom: 8.0),
                  child: Row(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Icon(Icons.arrow_right, color: _primaryColor),
                      Expanded(
                        child: Text(
                          feature,
                          style: TextStyle(
                            color: _textColor.withOpacity(0.8),
                          ),
                        ),
                      ),
                    ],
                  ),
                )).toList(),

                const SizedBox(height: 16),

                // Current applications
                Text(
                  'Current Applications:',
                  style: TextStyle(
                    fontWeight: FontWeight.bold,
                    color: _textColor,
                    fontSize: 16,
                  ),
                ),
                const SizedBox(height: 8),
                ...tech.currentApplications.map((application) => Padding(
                  padding: const EdgeInsets.only(bottom: 8.0),
                  child: Row(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Icon(Icons.check_circle_outline, color: _accentColor),
                      const SizedBox(width: 8),
                      Expanded(
                        child: Text(
                          application,
                          style: TextStyle(
                            color: _textColor.withOpacity(0.8),
                          ),
                        ),
                      ),
                    ],
                  ),
                )).toList(),

                const SizedBox(height: 16),

                // Future possibilities
                Text(
                  'Future Possibilities:',
                  style: TextStyle(
                    fontWeight: FontWeight.bold,
                    color: _textColor,
                    fontSize: 16,
                  ),
                ),
                const SizedBox(height: 8),
                ...tech.futurePossibilities.map((possibility) => Padding(
                  padding: const EdgeInsets.only(bottom: 8.0),
                  child: Row(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Icon(Icons.lightbulb_outline, color: _secondaryColor),
                      const SizedBox(width: 8),
                      Expanded(
                        child: Text(
                          possibility,
                          style: TextStyle(
                            color: _textColor.withOpacity(0.8),
                          ),
                        ),
                      ),
                    ],
                  ),
                )).toList(),
              ],
            ),
          ),
        ),

        const SizedBox(height: 16),

        // Impact analysis button
        Center(
          child: ElevatedButton.icon(
            onPressed: _startImpactAnalysis,
            icon: const Icon(Icons.assessment),
            label: const Text('Analyze Potential Impacts'),
            style: ElevatedButton.styleFrom(
              backgroundColor: _secondaryColor,
              foregroundColor: Colors.white,
              padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(8),
              ),
            ),
          ),
        ),

        // Impact analysis section
        if (_showImpactAnalysis) ...[
          const SizedBox(height: 16),
          Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: _backgroundColor,
              borderRadius: BorderRadius.circular(8),
              border: Border.all(color: _secondaryColor),
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'Rate the potential impacts of this technology:',
                  style: TextStyle(
                    fontWeight: FontWeight.bold,
                    color: _textColor,
                    fontSize: 16,
                  ),
                ),
                const SizedBox(height: 16),
                ...tech.potentialImpacts.map((impact) => _buildImpactRatingSlider(tech.id, impact)).toList(),
                const SizedBox(height: 16),
                Center(
                  child: ElevatedButton(
                    onPressed: _submitAnalysis,
                    style: ElevatedButton.styleFrom(
                      backgroundColor: _secondaryColor,
                      foregroundColor: Colors.white,
                      padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
                    ),
                    child: const Text('Submit Analysis'),
                  ),
                ),
              ],
            ),
          ),
        ],
      ],
    );
  }

  Widget _buildImpactRatingSlider(String techId, PotentialImpact impact) {
    double rating = _userImpactRatings['${techId}_${impact.id}'] ?? 0.0;
    
    return Padding(
      padding: const EdgeInsets.only(bottom: 16.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Expanded(
                child: Text(
                  impact.name,
                  style: TextStyle(
                    fontWeight: FontWeight.w500,
                    color: _textColor,
                  ),
                ),
              ),
              Text(
                '${rating.toStringAsFixed(1)}/5.0',
                style: TextStyle(
                  fontWeight: FontWeight.bold,
                  color: _getImpactColor(rating),
                ),
              ),
            ],
          ),
          const SizedBox(height: 4),
          Text(
            impact.description,
            style: TextStyle(
              fontSize: 12,
              color: _textColor.withOpacity(0.7),
            ),
          ),
          Slider(
            value: rating,
            min: 0.0,
            max: 5.0,
            divisions: 10,
            label: rating.toStringAsFixed(1),
            onChanged: (value) => _updateImpactRating(techId, impact.id, value),
            activeColor: _getImpactColor(rating),
            inactiveColor: _getImpactColor(rating).withOpacity(0.2),
          ),
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                'Low Impact',
                style: TextStyle(
                  fontSize: 10,
                  color: _textColor.withOpacity(0.6),
                ),
              ),
              Text(
                'High Impact',
                style: TextStyle(
                  fontSize: 10,
                  color: _textColor.withOpacity(0.6),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildResultsPage(EmergingTechnology tech) {
    // Calculate average ratings for each category
    double societalAvg = 0.0;
    double economicAvg = 0.0;
    double environmentalAvg = 0.0;
    double ethicalAvg = 0.0;
    
    int societalCount = 0;
    int economicCount = 0;
    int environmentalCount = 0;
    int ethicalCount = 0;
    
    for (var impact in tech.potentialImpacts) {
      double rating = _userImpactRatings['${tech.id}_${impact.id}'] ?? 0.0;
      
      switch (impact.category) {
        case 'Societal':
          societalAvg += rating;
          societalCount++;
          break;
        case 'Economic':
          economicAvg += rating;
          economicCount++;
          break;
        case 'Environmental':
          environmentalAvg += rating;
          environmentalCount++;
          break;
        case 'Ethical':
          ethicalAvg += rating;
          ethicalCount++;
          break;
      }
    }
    
    societalAvg = societalCount > 0 ? societalAvg / societalCount : 0.0;
    economicAvg = economicCount > 0 ? economicAvg / economicCount : 0.0;
    environmentalAvg = environmentalCount > 0 ? environmentalAvg / environmentalCount : 0.0;
    ethicalAvg = ethicalCount > 0 ? ethicalAvg / ethicalCount : 0.0;
    
    // Calculate overall impact score
    double overallScore = (societalAvg + economicAvg + environmentalAvg + ethicalAvg) / 4;
    
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Results header
        Text(
          'Your Impact Analysis Results',
          style: TextStyle(
            fontWeight: FontWeight.bold,
            color: _textColor,
            fontSize: 18,
          ),
        ),
        
        const SizedBox(height: 16),
        
        // Overall score
        Container(
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            color: _getImpactColor(overallScore).withOpacity(0.1),
            borderRadius: BorderRadius.circular(8),
            border: Border.all(color: _getImpactColor(overallScore)),
          ),
          child: Row(
            children: [
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Overall Impact Score',
                      style: TextStyle(
                        fontWeight: FontWeight.bold,
                        color: _textColor,
                      ),
                    ),
                    const SizedBox(height: 4),
                    Text(
                      _getImpactDescription(overallScore),
                      style: TextStyle(
                        color: _textColor.withOpacity(0.8),
                      ),
                    ),
                  ],
                ),
              ),
              Container(
                width: 60,
                height: 60,
                decoration: BoxDecoration(
                  color: _getImpactColor(overallScore),
                  shape: BoxShape.circle,
                ),
                child: Center(
                  child: Text(
                    overallScore.toStringAsFixed(1),
                    style: const TextStyle(
                      color: Colors.white,
                      fontWeight: FontWeight.bold,
                      fontSize: 18,
                    ),
                  ),
                ),
              ),
            ],
          ),
        ),
        
        const SizedBox(height: 16),
        
        // Category scores
        Expanded(
          child: SingleChildScrollView(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Category breakdown
                Text(
                  'Impact by Category',
                  style: TextStyle(
                    fontWeight: FontWeight.bold,
                    color: _textColor,
                    fontSize: 16,
                  ),
                ),
                
                const SizedBox(height: 8),
                
                // Societal impacts
                _buildCategoryScoreCard(
                  'Societal Impacts',
                  societalAvg,
                  Icons.people,
                  Colors.blue,
                ),
                
                // Economic impacts
                _buildCategoryScoreCard(
                  'Economic Impacts',
                  economicAvg,
                  Icons.attach_money,
                  Colors.green,
                ),
                
                // Environmental impacts
                _buildCategoryScoreCard(
                  'Environmental Impacts',
                  environmentalAvg,
                  Icons.eco,
                  Colors.teal,
                ),
                
                // Ethical impacts
                _buildCategoryScoreCard(
                  'Ethical Impacts',
                  ethicalAvg,
                  Icons.balance,
                  Colors.purple,
                ),
                
                const SizedBox(height: 16),
                
                // Expert analysis
                Container(
                  padding: const EdgeInsets.all(16),
                  decoration: BoxDecoration(
                    color: _secondaryColor.withOpacity(0.1),
                    borderRadius: BorderRadius.circular(8),
                    border: Border.all(color: _secondaryColor.withOpacity(0.3)),
                  ),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'Expert Analysis',
                        style: TextStyle(
                          fontWeight: FontWeight.bold,
                          color: _secondaryColor,
                          fontSize: 16,
                        ),
                      ),
                      const SizedBox(height: 8),
                      Text(
                        tech.expertAnalysis,
                        style: TextStyle(
                          color: _textColor.withOpacity(0.8),
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),
        ),
        
        const SizedBox(height: 16),
        
        // Navigation buttons
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceEvenly,
          children: [
            OutlinedButton.icon(
              onPressed: () {
                _pageController.animateToPage(
                  0,
                  duration: const Duration(milliseconds: 300),
                  curve: Curves.easeInOut,
                );
              },
              icon: const Icon(Icons.arrow_back),
              label: const Text('Back to Info'),
              style: OutlinedButton.styleFrom(
                foregroundColor: _primaryColor,
                side: BorderSide(color: _primaryColor),
                padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
              ),
            ),
            if (_currentTechIndex < _technologies.length - 1)
              ElevatedButton.icon(
                onPressed: _nextTechnology,
                icon: const Icon(Icons.arrow_forward),
                label: const Text('Next Technology'),
                style: ElevatedButton.styleFrom(
                  backgroundColor: _primaryColor,
                  foregroundColor: Colors.white,
                  padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                ),
              ),
          ],
        ),
      ],
    );
  }

  Widget _buildCategoryScoreCard(String title, double score, IconData icon, Color color) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 8.0),
      child: Card(
        elevation: 1,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(8),
          side: BorderSide(color: color.withOpacity(0.3)),
        ),
        child: Padding(
          padding: const EdgeInsets.all(12.0),
          child: Row(
            children: [
              Icon(icon, color: color),
              const SizedBox(width: 12),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      title,
                      style: TextStyle(
                        fontWeight: FontWeight.bold,
                        color: _textColor,
                      ),
                    ),
                    const SizedBox(height: 4),
                    LinearProgressIndicator(
                      value: score / 5.0,
                      backgroundColor: color.withOpacity(0.1),
                      valueColor: AlwaysStoppedAnimation<Color>(color),
                    ),
                  ],
                ),
              ),
              const SizedBox(width: 12),
              Container(
                width: 36,
                height: 36,
                decoration: BoxDecoration(
                  color: color.withOpacity(0.1),
                  shape: BoxShape.circle,
                ),
                child: Center(
                  child: Text(
                    score.toStringAsFixed(1),
                    style: TextStyle(
                      color: color,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Color _getStatusColor(String status) {
    switch (status.toLowerCase()) {
      case 'emerging':
        return Colors.orange;
      case 'developing':
        return Colors.blue;
      case 'established':
        return Colors.green;
      case 'theoretical':
        return Colors.purple;
      default:
        return Colors.grey;
    }
  }

  Color _getImpactColor(double score) {
    if (score >= 4.0) {
      return Colors.red;
    } else if (score >= 3.0) {
      return Colors.orange;
    } else if (score >= 2.0) {
      return Colors.yellow.shade800;
    } else if (score >= 1.0) {
      return Colors.blue;
    } else {
      return Colors.grey;
    }
  }

  String _getImpactDescription(double score) {
    if (score >= 4.0) {
      return 'Very High Impact - Transformative effects across multiple domains';
    } else if (score >= 3.0) {
      return 'High Impact - Significant effects on society and industry';
    } else if (score >= 2.0) {
      return 'Moderate Impact - Notable effects in specific areas';
    } else if (score >= 1.0) {
      return 'Low Impact - Limited effects on existing systems';
    } else {
      return 'Minimal Impact - Negligible effects on current practices';
    }
  }
}

/// Represents an emerging technology
class EmergingTechnology {
  final String id;
  final String name;
  final String description;
  final String status;
  final List<String> keyFeatures;
  final List<String> currentApplications;
  final List<String> futurePossibilities;
  final List<PotentialImpact> potentialImpacts;
  final String expertAnalysis;

  EmergingTechnology({
    required this.id,
    required this.name,
    required this.description,
    required this.status,
    required this.keyFeatures,
    required this.currentApplications,
    required this.futurePossibilities,
    required this.potentialImpacts,
    required this.expertAnalysis,
  });

  factory EmergingTechnology.fromJson(Map<String, dynamic> json) {
    final List<dynamic> featuresData = json['keyFeatures'] as List<dynamic>;
    final List<String> features = featuresData.map((feature) => feature as String).toList();
    
    final List<dynamic> applicationsData = json['currentApplications'] as List<dynamic>;
    final List<String> applications = applicationsData.map((app) => app as String).toList();
    
    final List<dynamic> possibilitiesData = json['futurePossibilities'] as List<dynamic>;
    final List<String> possibilities = possibilitiesData.map((possibility) => possibility as String).toList();
    
    final List<dynamic> impactsData = json['potentialImpacts'] as List<dynamic>;
    final List<PotentialImpact> impacts = impactsData
        .map((impactData) => PotentialImpact.fromJson(impactData))
        .toList();

    return EmergingTechnology(
      id: json['id'] as String,
      name: json['name'] as String,
      description: json['description'] as String,
      status: json['status'] as String,
      keyFeatures: features,
      currentApplications: applications,
      futurePossibilities: possibilities,
      potentialImpacts: impacts,
      expertAnalysis: json['expertAnalysis'] as String,
    );
  }
}

/// Represents a potential impact of an emerging technology
class PotentialImpact {
  final String id;
  final String name;
  final String description;
  final String category;

  PotentialImpact({
    required this.id,
    required this.name,
    required this.description,
    required this.category,
  });

  factory PotentialImpact.fromJson(Map<String, dynamic> json) {
    return PotentialImpact(
      id: json['id'] as String,
      name: json['name'] as String,
      description: json['description'] as String,
      category: json['category'] as String,
    );
  }
}
