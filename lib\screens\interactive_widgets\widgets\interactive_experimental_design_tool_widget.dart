import 'package:flutter/material.dart';

/// A widget that helps users design and evaluate scientific experiments
/// Users can define variables, create control groups, and evaluate experimental designs
class InteractiveExperimentalDesignToolWidget extends StatefulWidget {
  /// The data for this widget
  final Map<String, dynamic> data;

  /// Optional callback when the widget state changes
  final Function(bool isCompleted)? onStateChanged;

  const InteractiveExperimentalDesignToolWidget({
    Key? key,
    required this.data,
    this.onStateChanged,
  }) : super(key: key);

  /// Factory constructor to create the widget from JSON data
  factory InteractiveExperimentalDesignToolWidget.fromData(Map<String, dynamic> data) {
    return InteractiveExperimentalDesignToolWidget(
      data: data,
    );
  }

  @override
  State<InteractiveExperimentalDesignToolWidget> createState() => _InteractiveExperimentalDesignToolWidgetState();
}

class _InteractiveExperimentalDesignToolWidgetState extends State<InteractiveExperimentalDesignToolWidget> {
  // Experiment components
  late List<String> _researchQuestions;
  late List<String> _independentVariables;
  late List<String> _dependentVariables;
  late List<String> _controlledVariables;
  late List<String> _experimentalGroups;
  late List<String> _controlGroups;
  late List<String> _dataCollectionMethods;

  // Selected components
  late String _selectedResearchQuestion;
  late String _selectedIndependentVariable;
  late String _selectedDependentVariable;
  late List<String> _selectedControlledVariables;
  late List<String> _selectedExperimentalGroups;
  late List<String> _selectedControlGroups;
  late List<String> _selectedDataCollectionMethods;

  // Custom input
  late TextEditingController _customResearchQuestionController;
  late TextEditingController _customIndependentVariableController;
  late TextEditingController _customDependentVariableController;
  late TextEditingController _customControlledVariableController;
  late TextEditingController _customExperimentalGroupController;
  late TextEditingController _customControlGroupController;
  late TextEditingController _customDataCollectionMethodController;

  // UI state
  late int _currentStep;
  late bool _isCompleted;
  late bool _showFeedback;
  late bool _isExperimentValid;
  late String _feedbackMessage;

  // Colors
  late Color _primaryColor;
  late Color _secondaryColor;
  late Color _accentColor;
  late Color _backgroundColor;
  late Color _textColor;

  @override
  void initState() {
    super.initState();

    // Initialize colors
    _primaryColor = _getColorFromHex(widget.data['primaryColor'] ?? '#2196F3');
    _secondaryColor = _getColorFromHex(widget.data['secondaryColor'] ?? '#FF9800');
    _accentColor = _getColorFromHex(widget.data['accentColor'] ?? '#4CAF50');
    _backgroundColor = _getColorFromHex(widget.data['backgroundColor'] ?? '#FFFFFF');
    _textColor = _getColorFromHex(widget.data['textColor'] ?? '#212121');

    // Initialize experiment components
    _researchQuestions = List<String>.from(widget.data['researchQuestions'] ?? _getDefaultResearchQuestions());
    _independentVariables = List<String>.from(widget.data['independentVariables'] ?? _getDefaultIndependentVariables());
    _dependentVariables = List<String>.from(widget.data['dependentVariables'] ?? _getDefaultDependentVariables());
    _controlledVariables = List<String>.from(widget.data['controlledVariables'] ?? _getDefaultControlledVariables());
    _experimentalGroups = List<String>.from(widget.data['experimentalGroups'] ?? _getDefaultExperimentalGroups());
    _controlGroups = List<String>.from(widget.data['controlGroups'] ?? _getDefaultControlGroups());
    _dataCollectionMethods = List<String>.from(widget.data['dataCollectionMethods'] ?? _getDefaultDataCollectionMethods());

    // Initialize selected components
    _selectedResearchQuestion = '';
    _selectedIndependentVariable = '';
    _selectedDependentVariable = '';
    _selectedControlledVariables = [];
    _selectedExperimentalGroups = [];
    _selectedControlGroups = [];
    _selectedDataCollectionMethods = [];

    // Initialize text controllers
    _customResearchQuestionController = TextEditingController();
    _customIndependentVariableController = TextEditingController();
    _customDependentVariableController = TextEditingController();
    _customControlledVariableController = TextEditingController();
    _customExperimentalGroupController = TextEditingController();
    _customControlGroupController = TextEditingController();
    _customDataCollectionMethodController = TextEditingController();

    // Initialize UI state
    _currentStep = 0;
    _isCompleted = false;
    _showFeedback = false;
    _isExperimentValid = false;
    _feedbackMessage = '';
  }

  @override
  void dispose() {
    _customResearchQuestionController.dispose();
    _customIndependentVariableController.dispose();
    _customDependentVariableController.dispose();
    _customControlledVariableController.dispose();
    _customExperimentalGroupController.dispose();
    _customControlGroupController.dispose();
    _customDataCollectionMethodController.dispose();
    super.dispose();
  }

  // Get default research questions
  List<String> _getDefaultResearchQuestions() {
    return [
      'How does the amount of sunlight affect plant growth?',
      'Does temperature affect the rate of a chemical reaction?',
      'How does study time affect test scores?',
      'Does exercise frequency affect weight loss?',
      'How does water temperature affect the rate of dissolution?',
    ];
  }

  // Get default independent variables
  List<String> _getDefaultIndependentVariables() {
    return [
      'Amount of sunlight',
      'Temperature',
      'Study time',
      'Exercise frequency',
      'Water temperature',
    ];
  }

  // Get default dependent variables
  List<String> _getDefaultDependentVariables() {
    return [
      'Plant height',
      'Reaction rate',
      'Test scores',
      'Weight loss',
      'Dissolution rate',
    ];
  }

  // Get default controlled variables
  List<String> _getDefaultControlledVariables() {
    return [
      'Type of plant',
      'Type of reactants',
      'Study material',
      'Diet',
      'Type of solute',
      'Amount of water',
      'Soil type',
      'Container size',
      'Room humidity',
    ];
  }

  // Get default experimental groups
  List<String> _getDefaultExperimentalGroups() {
    return [
      'Plants receiving 12 hours of sunlight',
      'Reaction at 50°C',
      'Students studying for 3 hours',
      'Exercise 5 times per week',
      'Water at 80°C',
    ];
  }

  // Get default control groups
  List<String> _getDefaultControlGroups() {
    return [
      'Plants receiving 6 hours of sunlight',
      'Reaction at 25°C',
      'Students studying for 1 hour',
      'Exercise 2 times per week',
      'Water at 20°C',
    ];
  }

  // Get default data collection methods
  List<String> _getDefaultDataCollectionMethods() {
    return [
      'Measure plant height with a ruler',
      'Time the reaction with a stopwatch',
      'Record test scores',
      'Weigh participants on a scale',
      'Measure the amount of dissolved solute',
    ];
  }

  // Get color from hex string
  Color _getColorFromHex(String hexColor) {
    hexColor = hexColor.replaceAll('#', '');
    if (hexColor.length == 6) {
      hexColor = 'FF$hexColor';
    }
    return Color(int.parse(hexColor, radix: 16));
  }

  // Add a custom research question
  void _addCustomResearchQuestion() {
    if (_customResearchQuestionController.text.isNotEmpty) {
      setState(() {
        _researchQuestions.add(_customResearchQuestionController.text);
        _selectedResearchQuestion = _customResearchQuestionController.text;
        _customResearchQuestionController.clear();
      });
    }
  }

  // Add a custom independent variable
  void _addCustomIndependentVariable() {
    if (_customIndependentVariableController.text.isNotEmpty) {
      setState(() {
        _independentVariables.add(_customIndependentVariableController.text);
        _selectedIndependentVariable = _customIndependentVariableController.text;
        _customIndependentVariableController.clear();
      });
    }
  }

  // Add a custom dependent variable
  void _addCustomDependentVariable() {
    if (_customDependentVariableController.text.isNotEmpty) {
      setState(() {
        _dependentVariables.add(_customDependentVariableController.text);
        _selectedDependentVariable = _customDependentVariableController.text;
        _customDependentVariableController.clear();
      });
    }
  }

  // Add a custom controlled variable
  void _addCustomControlledVariable() {
    if (_customControlledVariableController.text.isNotEmpty) {
      setState(() {
        _controlledVariables.add(_customControlledVariableController.text);
        _selectedControlledVariables.add(_customControlledVariableController.text);
        _customControlledVariableController.clear();
      });
    }
  }

  // Add a custom experimental group
  void _addCustomExperimentalGroup() {
    if (_customExperimentalGroupController.text.isNotEmpty) {
      setState(() {
        _experimentalGroups.add(_customExperimentalGroupController.text);
        _selectedExperimentalGroups.add(_customExperimentalGroupController.text);
        _customExperimentalGroupController.clear();
      });
    }
  }

  // Add a custom control group
  void _addCustomControlGroup() {
    if (_customControlGroupController.text.isNotEmpty) {
      setState(() {
        _controlGroups.add(_customControlGroupController.text);
        _selectedControlGroups.add(_customControlGroupController.text);
        _customControlGroupController.clear();
      });
    }
  }

  // Add a custom data collection method
  void _addCustomDataCollectionMethod() {
    if (_customDataCollectionMethodController.text.isNotEmpty) {
      setState(() {
        _dataCollectionMethods.add(_customDataCollectionMethodController.text);
        _selectedDataCollectionMethods.add(_customDataCollectionMethodController.text);
        _customDataCollectionMethodController.clear();
      });
    }
  }

  // Select a research question
  void _selectResearchQuestion(String question) {
    setState(() {
      _selectedResearchQuestion = question;
    });
  }

  // Select an independent variable
  void _selectIndependentVariable(String variable) {
    setState(() {
      _selectedIndependentVariable = variable;
    });
  }

  // Select a dependent variable
  void _selectDependentVariable(String variable) {
    setState(() {
      _selectedDependentVariable = variable;
    });
  }

  // Toggle a controlled variable selection
  void _toggleControlledVariable(String variable) {
    setState(() {
      if (_selectedControlledVariables.contains(variable)) {
        _selectedControlledVariables.remove(variable);
      } else {
        _selectedControlledVariables.add(variable);
      }
    });
  }

  // Toggle an experimental group selection
  void _toggleExperimentalGroup(String group) {
    setState(() {
      if (_selectedExperimentalGroups.contains(group)) {
        _selectedExperimentalGroups.remove(group);
      } else {
        _selectedExperimentalGroups.add(group);
      }
    });
  }

  // Toggle a control group selection
  void _toggleControlGroup(String group) {
    setState(() {
      if (_selectedControlGroups.contains(group)) {
        _selectedControlGroups.remove(group);
      } else {
        _selectedControlGroups.add(group);
      }
    });
  }

  // Toggle a data collection method selection
  void _toggleDataCollectionMethod(String method) {
    setState(() {
      if (_selectedDataCollectionMethods.contains(method)) {
        _selectedDataCollectionMethods.remove(method);
      } else {
        _selectedDataCollectionMethods.add(method);
      }
    });
  }

  // Go to the next step
  void _nextStep() {
    if (_currentStep < 5) {
      setState(() {
        _currentStep++;
      });
    } else {
      _evaluateExperiment();
    }
  }

  // Go to the previous step
  void _previousStep() {
    if (_currentStep > 0) {
      setState(() {
        _currentStep--;
      });
    }
  }

  // Evaluate the experiment
  void _evaluateExperiment() {
    // Check if all components are selected
    bool hasResearchQuestion = _selectedResearchQuestion.isNotEmpty;
    bool hasIndependentVariable = _selectedIndependentVariable.isNotEmpty;
    bool hasDependentVariable = _selectedDependentVariable.isNotEmpty;
    bool hasControlledVariables = _selectedControlledVariables.isNotEmpty;
    bool hasExperimentalGroups = _selectedExperimentalGroups.isNotEmpty;
    bool hasControlGroups = _selectedControlGroups.isNotEmpty;
    bool hasDataCollectionMethods = _selectedDataCollectionMethods.isNotEmpty;

    // Evaluate the experiment
    _isExperimentValid = hasResearchQuestion && hasIndependentVariable && hasDependentVariable &&
                         hasControlledVariables && hasExperimentalGroups && hasControlGroups &&
                         hasDataCollectionMethods;

    // Generate feedback
    if (_isExperimentValid) {
      _feedbackMessage = 'Great job! Your experimental design is well-structured and follows scientific principles.';
    } else {
      List<String> missingComponents = [];
      if (!hasResearchQuestion) missingComponents.add('Research Question');
      if (!hasIndependentVariable) missingComponents.add('Independent Variable');
      if (!hasDependentVariable) missingComponents.add('Dependent Variable');
      if (!hasControlledVariables) missingComponents.add('Controlled Variables');
      if (!hasExperimentalGroups) missingComponents.add('Experimental Groups');
      if (!hasControlGroups) missingComponents.add('Control Groups');
      if (!hasDataCollectionMethods) missingComponents.add('Data Collection Methods');

      _feedbackMessage = 'Your experimental design needs improvement. Missing: ${missingComponents.join(', ')}.';
    }

    setState(() {
      _showFeedback = true;
      _isCompleted = true;
    });

    // Notify parent of completion
    widget.onStateChanged?.call(true);
  }

  // Reset the experiment designer
  void _resetDesigner() {
    setState(() {
      _selectedResearchQuestion = '';
      _selectedIndependentVariable = '';
      _selectedDependentVariable = '';
      _selectedControlledVariables = [];
      _selectedExperimentalGroups = [];
      _selectedControlGroups = [];
      _selectedDataCollectionMethods = [];
      _currentStep = 0;
      _isCompleted = false;
      _showFeedback = false;
    });
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: _backgroundColor,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withOpacity(0.2),
            spreadRadius: 1,
            blurRadius: 3,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Title
          Text(
            widget.data['title'] ?? 'Experimental Design Tool',
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
              color: _textColor,
            ),
          ),

          const SizedBox(height: 16),

          // Stepper
          LinearProgressIndicator(
            value: (_currentStep + 1) / 6,
            backgroundColor: Colors.grey[200],
            valueColor: AlwaysStoppedAnimation<Color>(_primaryColor),
          ),

          Padding(
            padding: const EdgeInsets.symmetric(vertical: 8.0),
            child: Text(
              'Step ${_currentStep + 1} of 6: ${_getStepTitle(_currentStep)}',
              style: TextStyle(
                fontSize: 14,
                fontWeight: FontWeight.bold,
                color: _primaryColor,
              ),
            ),
          ),

          const SizedBox(height: 8),

          // Current step content
          _buildStepContent(),

          const SizedBox(height: 16),

          // Navigation buttons
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              ElevatedButton(
                onPressed: _currentStep > 0 ? _previousStep : null,
                style: ElevatedButton.styleFrom(
                  backgroundColor: _secondaryColor,
                  foregroundColor: Colors.white,
                ),
                child: const Text('Previous'),
              ),
              if (_showFeedback)
                ElevatedButton(
                  onPressed: _resetDesigner,
                  style: ElevatedButton.styleFrom(
                    backgroundColor: _accentColor,
                    foregroundColor: Colors.white,
                  ),
                  child: const Text('Start Over'),
                )
              else
                ElevatedButton(
                  onPressed: _nextStep,
                  style: ElevatedButton.styleFrom(
                    backgroundColor: _primaryColor,
                    foregroundColor: Colors.white,
                  ),
                  child: Text(_currentStep < 5 ? 'Next' : 'Evaluate'),
                ),
            ],
          ),

          // Widget name tag at the bottom
          if (widget.data['showNameTag'] ?? true)
            Padding(
              padding: const EdgeInsets.only(top: 8),
              child: Text(
                'InteractiveExperimentalDesignToolWidget',
                style: TextStyle(
                  color: Colors.grey[600],
                  fontSize: 12,
                  fontStyle: FontStyle.italic,
                ),
              ),
            ),
        ],
      ),
    );
  }

  // Get the title for the current step
  String _getStepTitle(int step) {
    switch (step) {
      case 0:
        return 'Define Research Question';
      case 1:
        return 'Identify Variables';
      case 2:
        return 'Select Controlled Variables';
      case 3:
        return 'Define Experimental Groups';
      case 4:
        return 'Define Control Groups';
      case 5:
        return 'Plan Data Collection';
      default:
        return '';
    }
  }

  // Build the content for the current step
  Widget _buildStepContent() {
    switch (_currentStep) {
      case 0:
        return _buildResearchQuestionStep();
      case 1:
        return _buildVariablesStep();
      case 2:
        return _buildControlledVariablesStep();
      case 3:
        return _buildExperimentalGroupsStep();
      case 4:
        return _buildControlGroupsStep();
      case 5:
        return _buildDataCollectionStep();
      default:
        return Container();
    }
  }

  // Build the research question step
  Widget _buildResearchQuestionStep() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Select or create a research question:',
          style: TextStyle(
            fontSize: 14,
            fontWeight: FontWeight.bold,
            color: _textColor,
          ),
        ),
        const SizedBox(height: 4),
        Text(
          'A good research question is clear, specific, and testable.',
          style: TextStyle(
            fontSize: 12,
            fontStyle: FontStyle.italic,
            color: _textColor.withOpacity(0.7),
          ),
        ),
        const SizedBox(height: 8),
        Wrap(
          spacing: 8,
          runSpacing: 8,
          children: _researchQuestions.map((question) {
            return ChoiceChip(
              label: Text(
                question,
                overflow: TextOverflow.ellipsis,
              ),
              selected: _selectedResearchQuestion == question,
              onSelected: (selected) {
                if (selected) _selectResearchQuestion(question);
              },
            );
          }).toList(),
        ),
        const SizedBox(height: 8),
        Row(
          children: [
            Expanded(
              child: TextField(
                controller: _customResearchQuestionController,
                decoration: const InputDecoration(
                  hintText: 'Create your own research question',
                  border: OutlineInputBorder(),
                ),
              ),
            ),
            const SizedBox(width: 8),
            ElevatedButton(
              onPressed: _addCustomResearchQuestion,
              child: const Text('Add'),
            ),
          ],
        ),
        const SizedBox(height: 16),
        if (_selectedResearchQuestion.isNotEmpty)
          Container(
            padding: const EdgeInsets.all(12),
            decoration: BoxDecoration(
              color: _primaryColor.withOpacity(0.1),
              borderRadius: BorderRadius.circular(8),
              border: Border.all(color: _primaryColor.withOpacity(0.3)),
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'Selected Research Question:',
                  style: TextStyle(
                    fontSize: 14,
                    fontWeight: FontWeight.bold,
                    color: _textColor,
                  ),
                ),
                const SizedBox(height: 4),
                Text(
                  _selectedResearchQuestion,
                  style: TextStyle(
                    fontSize: 14,
                    color: _textColor,
                  ),
                ),
              ],
            ),
          ),
      ],
    );
  }

  // Build the variables step
  Widget _buildVariablesStep() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Select or create an independent variable:',
          style: TextStyle(
            fontSize: 14,
            fontWeight: FontWeight.bold,
            color: _textColor,
          ),
        ),
        const SizedBox(height: 4),
        Text(
          'The independent variable is what you change in your experiment.',
          style: TextStyle(
            fontSize: 12,
            fontStyle: FontStyle.italic,
            color: _textColor.withOpacity(0.7),
          ),
        ),
        const SizedBox(height: 8),
        Wrap(
          spacing: 8,
          runSpacing: 8,
          children: _independentVariables.map((variable) {
            return ChoiceChip(
              label: Text(variable),
              selected: _selectedIndependentVariable == variable,
              onSelected: (selected) {
                if (selected) _selectIndependentVariable(variable);
              },
            );
          }).toList(),
        ),
        const SizedBox(height: 8),
        Row(
          children: [
            Expanded(
              child: TextField(
                controller: _customIndependentVariableController,
                decoration: const InputDecoration(
                  hintText: 'Create your own independent variable',
                  border: OutlineInputBorder(),
                ),
              ),
            ),
            const SizedBox(width: 8),
            ElevatedButton(
              onPressed: _addCustomIndependentVariable,
              child: const Text('Add'),
            ),
          ],
        ),
        const SizedBox(height: 16),
        Text(
          'Select or create a dependent variable:',
          style: TextStyle(
            fontSize: 14,
            fontWeight: FontWeight.bold,
            color: _textColor,
          ),
        ),
        const SizedBox(height: 4),
        Text(
          'The dependent variable is what you measure in your experiment.',
          style: TextStyle(
            fontSize: 12,
            fontStyle: FontStyle.italic,
            color: _textColor.withOpacity(0.7),
          ),
        ),
        const SizedBox(height: 8),
        Wrap(
          spacing: 8,
          runSpacing: 8,
          children: _dependentVariables.map((variable) {
            return ChoiceChip(
              label: Text(variable),
              selected: _selectedDependentVariable == variable,
              onSelected: (selected) {
                if (selected) _selectDependentVariable(variable);
              },
            );
          }).toList(),
        ),
        const SizedBox(height: 8),
        Row(
          children: [
            Expanded(
              child: TextField(
                controller: _customDependentVariableController,
                decoration: const InputDecoration(
                  hintText: 'Create your own dependent variable',
                  border: OutlineInputBorder(),
                ),
              ),
            ),
            const SizedBox(width: 8),
            ElevatedButton(
              onPressed: _addCustomDependentVariable,
              child: const Text('Add'),
            ),
          ],
        ),
        const SizedBox(height: 16),
        if (_selectedIndependentVariable.isNotEmpty && _selectedDependentVariable.isNotEmpty)
          Container(
            padding: const EdgeInsets.all(12),
            decoration: BoxDecoration(
              color: _primaryColor.withOpacity(0.1),
              borderRadius: BorderRadius.circular(8),
              border: Border.all(color: _primaryColor.withOpacity(0.3)),
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'Selected Variables:',
                  style: TextStyle(
                    fontSize: 14,
                    fontWeight: FontWeight.bold,
                    color: _textColor,
                  ),
                ),
                const SizedBox(height: 4),
                Row(
                  children: [
                    Text(
                      'Independent Variable: ',
                      style: TextStyle(
                        fontSize: 14,
                        fontWeight: FontWeight.bold,
                        color: _textColor,
                      ),
                    ),
                    Text(
                      _selectedIndependentVariable,
                      style: TextStyle(
                        fontSize: 14,
                        color: _textColor,
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 4),
                Row(
                  children: [
                    Text(
                      'Dependent Variable: ',
                      style: TextStyle(
                        fontSize: 14,
                        fontWeight: FontWeight.bold,
                        color: _textColor,
                      ),
                    ),
                    Text(
                      _selectedDependentVariable,
                      style: TextStyle(
                        fontSize: 14,
                        color: _textColor,
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),
      ],
    );
  }

  // Build the controlled variables step
  Widget _buildControlledVariablesStep() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Select or create controlled variables:',
          style: TextStyle(
            fontSize: 14,
            fontWeight: FontWeight.bold,
            color: _textColor,
          ),
        ),
        const SizedBox(height: 4),
        Text(
          'Controlled variables are factors that you keep constant during your experiment.',
          style: TextStyle(
            fontSize: 12,
            fontStyle: FontStyle.italic,
            color: _textColor.withOpacity(0.7),
          ),
        ),
        const SizedBox(height: 8),
        Wrap(
          spacing: 8,
          runSpacing: 8,
          children: _controlledVariables.map((variable) {
            return FilterChip(
              label: Text(variable),
              selected: _selectedControlledVariables.contains(variable),
              onSelected: (selected) {
                _toggleControlledVariable(variable);
              },
            );
          }).toList(),
        ),
        const SizedBox(height: 8),
        Row(
          children: [
            Expanded(
              child: TextField(
                controller: _customControlledVariableController,
                decoration: const InputDecoration(
                  hintText: 'Add a new controlled variable',
                  border: OutlineInputBorder(),
                ),
              ),
            ),
            const SizedBox(width: 8),
            ElevatedButton(
              onPressed: _addCustomControlledVariable,
              child: const Text('Add'),
            ),
          ],
        ),
        const SizedBox(height: 16),
        if (_selectedControlledVariables.isNotEmpty)
          Container(
            padding: const EdgeInsets.all(12),
            decoration: BoxDecoration(
              color: _primaryColor.withOpacity(0.1),
              borderRadius: BorderRadius.circular(8),
              border: Border.all(color: _primaryColor.withOpacity(0.3)),
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'Selected Controlled Variables:',
                  style: TextStyle(
                    fontSize: 14,
                    fontWeight: FontWeight.bold,
                    color: _textColor,
                  ),
                ),
                const SizedBox(height: 4),
                ...(_selectedControlledVariables.map((variable) => Padding(
                  padding: const EdgeInsets.only(bottom: 4),
                  child: Row(
                    children: [
                      Icon(Icons.check_circle, color: _accentColor, size: 16),
                      const SizedBox(width: 4),
                      Text(variable),
                    ],
                  ),
                ))),
              ],
            ),
          ),
      ],
    );
  }

  // Build the experimental groups step
  Widget _buildExperimentalGroupsStep() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Select or create experimental groups:',
          style: TextStyle(
            fontSize: 14,
            fontWeight: FontWeight.bold,
            color: _textColor,
          ),
        ),
        const SizedBox(height: 4),
        Text(
          'Experimental groups receive different levels of the independent variable.',
          style: TextStyle(
            fontSize: 12,
            fontStyle: FontStyle.italic,
            color: _textColor.withOpacity(0.7),
          ),
        ),
        const SizedBox(height: 8),
        Wrap(
          spacing: 8,
          runSpacing: 8,
          children: _experimentalGroups.map((group) {
            return FilterChip(
              label: Text(group),
              selected: _selectedExperimentalGroups.contains(group),
              onSelected: (selected) {
                _toggleExperimentalGroup(group);
              },
            );
          }).toList(),
        ),
        const SizedBox(height: 8),
        Row(
          children: [
            Expanded(
              child: TextField(
                controller: _customExperimentalGroupController,
                decoration: const InputDecoration(
                  hintText: 'Add a new experimental group',
                  border: OutlineInputBorder(),
                ),
              ),
            ),
            const SizedBox(width: 8),
            ElevatedButton(
              onPressed: _addCustomExperimentalGroup,
              child: const Text('Add'),
            ),
          ],
        ),
        const SizedBox(height: 16),
        if (_selectedExperimentalGroups.isNotEmpty)
          Container(
            padding: const EdgeInsets.all(12),
            decoration: BoxDecoration(
              color: _primaryColor.withOpacity(0.1),
              borderRadius: BorderRadius.circular(8),
              border: Border.all(color: _primaryColor.withOpacity(0.3)),
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'Selected Experimental Groups:',
                  style: TextStyle(
                    fontSize: 14,
                    fontWeight: FontWeight.bold,
                    color: _textColor,
                  ),
                ),
                const SizedBox(height: 4),
                ...(_selectedExperimentalGroups.map((group) => Padding(
                  padding: const EdgeInsets.only(bottom: 4),
                  child: Row(
                    children: [
                      Icon(Icons.check_circle, color: _accentColor, size: 16),
                      const SizedBox(width: 4),
                      Text(group),
                    ],
                  ),
                ))),
              ],
            ),
          ),
      ],
    );
  }

  // Build the control groups step
  Widget _buildControlGroupsStep() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Select or create control groups:',
          style: TextStyle(
            fontSize: 14,
            fontWeight: FontWeight.bold,
            color: _textColor,
          ),
        ),
        const SizedBox(height: 4),
        Text(
          'Control groups provide a baseline for comparison with experimental groups.',
          style: TextStyle(
            fontSize: 12,
            fontStyle: FontStyle.italic,
            color: _textColor.withOpacity(0.7),
          ),
        ),
        const SizedBox(height: 8),
        Wrap(
          spacing: 8,
          runSpacing: 8,
          children: _controlGroups.map((group) {
            return FilterChip(
              label: Text(group),
              selected: _selectedControlGroups.contains(group),
              onSelected: (selected) {
                _toggleControlGroup(group);
              },
            );
          }).toList(),
        ),
        const SizedBox(height: 8),
        Row(
          children: [
            Expanded(
              child: TextField(
                controller: _customControlGroupController,
                decoration: const InputDecoration(
                  hintText: 'Add a new control group',
                  border: OutlineInputBorder(),
                ),
              ),
            ),
            const SizedBox(width: 8),
            ElevatedButton(
              onPressed: _addCustomControlGroup,
              child: const Text('Add'),
            ),
          ],
        ),
        const SizedBox(height: 16),
        if (_selectedControlGroups.isNotEmpty)
          Container(
            padding: const EdgeInsets.all(12),
            decoration: BoxDecoration(
              color: _primaryColor.withOpacity(0.1),
              borderRadius: BorderRadius.circular(8),
              border: Border.all(color: _primaryColor.withOpacity(0.3)),
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'Selected Control Groups:',
                  style: TextStyle(
                    fontSize: 14,
                    fontWeight: FontWeight.bold,
                    color: _textColor,
                  ),
                ),
                const SizedBox(height: 4),
                ...(_selectedControlGroups.map((group) => Padding(
                  padding: const EdgeInsets.only(bottom: 4),
                  child: Row(
                    children: [
                      Icon(Icons.check_circle, color: _accentColor, size: 16),
                      const SizedBox(width: 4),
                      Text(group),
                    ],
                  ),
                ))),
              ],
            ),
          ),
      ],
    );
  }

  // Build the data collection step
  Widget _buildDataCollectionStep() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Select or create data collection methods:',
          style: TextStyle(
            fontSize: 14,
            fontWeight: FontWeight.bold,
            color: _textColor,
          ),
        ),
        const SizedBox(height: 4),
        Text(
          'Data collection methods describe how you will measure the dependent variable.',
          style: TextStyle(
            fontSize: 12,
            fontStyle: FontStyle.italic,
            color: _textColor.withOpacity(0.7),
          ),
        ),
        const SizedBox(height: 8),
        Wrap(
          spacing: 8,
          runSpacing: 8,
          children: _dataCollectionMethods.map((method) {
            return FilterChip(
              label: Text(method),
              selected: _selectedDataCollectionMethods.contains(method),
              onSelected: (selected) {
                _toggleDataCollectionMethod(method);
              },
            );
          }).toList(),
        ),
        const SizedBox(height: 8),
        Row(
          children: [
            Expanded(
              child: TextField(
                controller: _customDataCollectionMethodController,
                decoration: const InputDecoration(
                  hintText: 'Add a new data collection method',
                  border: OutlineInputBorder(),
                ),
              ),
            ),
            const SizedBox(width: 8),
            ElevatedButton(
              onPressed: _addCustomDataCollectionMethod,
              child: const Text('Add'),
            ),
          ],
        ),
        const SizedBox(height: 16),
        if (_selectedDataCollectionMethods.isNotEmpty)
          Container(
            padding: const EdgeInsets.all(12),
            decoration: BoxDecoration(
              color: _primaryColor.withOpacity(0.1),
              borderRadius: BorderRadius.circular(8),
              border: Border.all(color: _primaryColor.withOpacity(0.3)),
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'Selected Data Collection Methods:',
                  style: TextStyle(
                    fontSize: 14,
                    fontWeight: FontWeight.bold,
                    color: _textColor,
                  ),
                ),
                const SizedBox(height: 4),
                ...(_selectedDataCollectionMethods.map((method) => Padding(
                  padding: const EdgeInsets.only(bottom: 4),
                  child: Row(
                    children: [
                      Icon(Icons.check_circle, color: _accentColor, size: 16),
                      const SizedBox(width: 4),
                      Text(method),
                    ],
                  ),
                ))),
              ],
            ),
          ),
        const SizedBox(height: 16),
        if (_showFeedback)
          Container(
            padding: const EdgeInsets.all(12),
            decoration: BoxDecoration(
              color: _isExperimentValid ? _accentColor.withOpacity(0.1) : _secondaryColor.withOpacity(0.1),
              borderRadius: BorderRadius.circular(8),
              border: Border.all(
                color: _isExperimentValid ? _accentColor.withOpacity(0.3) : _secondaryColor.withOpacity(0.3),
              ),
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'Feedback:',
                  style: TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                    color: _isExperimentValid ? _accentColor : _secondaryColor,
                  ),
                ),
                const SizedBox(height: 8),
                Text(
                  _feedbackMessage,
                  style: TextStyle(
                    fontSize: 14,
                    color: _textColor,
                  ),
                ),
                const SizedBox(height: 12),
                Text(
                  'Experimental Design Summary:',
                  style: TextStyle(
                    fontSize: 14,
                    fontWeight: FontWeight.bold,
                    color: _textColor,
                  ),
                ),
                const SizedBox(height: 4),
                Text(
                  'Research Question: $_selectedResearchQuestion',
                  style: TextStyle(
                    fontSize: 14,
                    color: _textColor,
                  ),
                ),
                const SizedBox(height: 4),
                Text(
                  'Independent Variable: $_selectedIndependentVariable',
                  style: TextStyle(
                    fontSize: 14,
                    color: _textColor,
                  ),
                ),
                const SizedBox(height: 4),
                Text(
                  'Dependent Variable: $_selectedDependentVariable',
                  style: TextStyle(
                    fontSize: 14,
                    color: _textColor,
                  ),
                ),
              ],
            ),
          ),
      ],
    );
  }
}
