import 'dart:ui' as ui;
import 'package:flutter/material.dart';
import '../../../models/interactive_widget_model.dart';

class MathWhiteboardWidget extends StatefulWidget {
  final InteractiveWidgetModel widget;

  const MathWhiteboardWidget({super.key, required this.widget});

  @override
  State<MathWhiteboardWidget> createState() => _MathWhiteboardWidgetState();
}

class _MathWhiteboardWidgetState extends State<MathWhiteboardWidget> {
  late String _selectedTool;
  late String _selectedColor;
  late String _selectedGrid;
  final List<DrawingPoint?> _drawingPoints = [];
  final List<DrawingPoint?> _redoStack = [];
  
  // Drawing properties
  late Color _drawColor;
  double _strokeWidth = 3.0;
  
  @override
  void initState() {
    super.initState();
    _initializeWhiteboard();
  }
  
  void _initializeWhiteboard() {
    final tools = List<String>.from(widget.widget.data['tools'] ?? []);
    final colors = List<String>.from(widget.widget.data['colors'] ?? []);
    final gridOptions = List<String>.from(widget.widget.data['gridOptions'] ?? []);
    
    _selectedTool = widget.widget.data['defaultTool'] as String? ?? 
        (tools.isNotEmpty ? tools[0] : 'Pen');
    
    _selectedColor = widget.widget.data['defaultColor'] as String? ?? 
        (colors.isNotEmpty ? colors[0] : 'Black');
    
    _selectedGrid = widget.widget.data['defaultGrid'] as String? ?? 
        (gridOptions.isNotEmpty ? gridOptions[0] : 'None');
    
    _drawColor = _getColorFromName(_selectedColor);
  }
  
  Color _getColorFromName(String colorName) {
    switch (colorName) {
      case 'Black': return Colors.black;
      case 'Blue': return Colors.blue;
      case 'Red': return Colors.red;
      case 'Green': return Colors.green;
      default: return Colors.black;
    }
  }
  
  void _selectTool(String tool) {
    setState(() {
      _selectedTool = tool;
      if (tool == 'Eraser') {
        _strokeWidth = 20.0; // Wider stroke for eraser
      } else {
        _strokeWidth = 3.0;
      }
    });
  }
  
  void _selectColor(String color) {
    setState(() {
      _selectedColor = color;
      _drawColor = _getColorFromName(color);
      // If eraser is selected, don't change the color
      if (_selectedTool == 'Eraser') {
        _selectTool('Pen'); // Switch to pen when color is changed
      }
    });
  }
  
  void _selectGrid(String grid) {
    setState(() {
      _selectedGrid = grid;
    });
  }
  
  void _undo() {
    if (_drawingPoints.isNotEmpty) {
      setState(() {
        // Find the last non-null point (end of a stroke)
        int index = _drawingPoints.length - 1;
        while (index >= 0 && _drawingPoints[index] == null) {
          index--;
        }
        
        if (index >= 0) {
          // Add the removed stroke to redo stack
          _redoStack.add(_drawingPoints[index]);
          _redoStack.add(null); // Mark end of stroke
          
          // Remove the stroke
          _drawingPoints.removeAt(index);
          
          // If there are more points in this stroke, remove them too
          while (index > 0 && _drawingPoints[index - 1] != null) {
            index--;
            _redoStack.add(_drawingPoints[index]);
            _drawingPoints.removeAt(index);
          }
        }
      });
    }
  }
  
  void _redo() {
    if (_redoStack.isNotEmpty) {
      setState(() {
        // Find the last stroke in redo stack
        int index = _redoStack.length - 1;
        while (index >= 0 && _redoStack[index] != null) {
          index--;
        }
        
        if (index >= 0) {
          // Remove the null marker
          _redoStack.removeAt(index);
          
          // Add all points from this stroke back to drawing points
          while (index < _redoStack.length) {
            _drawingPoints.add(_redoStack[index]);
            _redoStack.removeAt(index);
          }
          
          // Add null marker to drawing points
          _drawingPoints.add(null);
        }
      });
    }
  }
  
  void _clear() {
    setState(() {
      _drawingPoints.clear();
      _redoStack.clear();
    });
  }
  
  @override
  Widget build(BuildContext context) {
    final tools = List<String>.from(widget.widget.data['tools'] ?? []);
    final colors = List<String>.from(widget.widget.data['colors'] ?? []);
    final gridOptions = List<String>.from(widget.widget.data['gridOptions'] ?? []);
    
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Drawing area
        Container(
          height: 300,
          width: double.infinity,
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(8),
            border: Border.all(color: Colors.grey[300]!),
          ),
          child: ClipRRect(
            borderRadius: BorderRadius.circular(8),
            child: Stack(
              children: [
                // Grid background
                if (_selectedGrid != 'None')
                  CustomPaint(
                    size: Size.infinite,
                    painter: GridPainter(
                      gridSize: _selectedGrid == '1cm' ? 40.0 : 20.0,
                    ),
                  ),
                
                // Drawing canvas
                GestureDetector(
                  onPanStart: (details) {
                    if (_selectedTool == 'Pen' || _selectedTool == 'Eraser') {
                      setState(() {
                        _redoStack.clear(); // Clear redo stack on new drawing
                        _drawingPoints.add(
                          DrawingPoint(
                            offset: details.localPosition,
                            color: _selectedTool == 'Eraser' ? Colors.white : _drawColor,
                            strokeWidth: _strokeWidth,
                          ),
                        );
                      });
                    }
                  },
                  onPanUpdate: (details) {
                    if (_selectedTool == 'Pen' || _selectedTool == 'Eraser') {
                      setState(() {
                        _drawingPoints.add(
                          DrawingPoint(
                            offset: details.localPosition,
                            color: _selectedTool == 'Eraser' ? Colors.white : _drawColor,
                            strokeWidth: _strokeWidth,
                          ),
                        );
                      });
                    }
                  },
                  onPanEnd: (_) {
                    if (_selectedTool == 'Pen' || _selectedTool == 'Eraser') {
                      setState(() {
                        _drawingPoints.add(null); // Mark end of stroke
                      });
                    }
                  },
                  child: CustomPaint(
                    size: Size.infinite,
                    painter: DrawingPainter(
                      drawingPoints: _drawingPoints,
                    ),
                  ),
                ),
              ],
            ),
          ),
        ),
        
        // Tool selection
        Padding(
          padding: const EdgeInsets.only(top: 16.0),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              const Text(
                'Tools:',
                style: TextStyle(fontWeight: FontWeight.bold, fontSize: 16),
              ),
              const SizedBox(height: 8),
              SingleChildScrollView(
                scrollDirection: Axis.horizontal,
                child: Row(
                  children: tools.map((tool) {
                    return Padding(
                      padding: const EdgeInsets.only(right: 8.0),
                      child: ChoiceChip(
                        label: Text(tool),
                        selected: _selectedTool == tool,
                        onSelected: (selected) {
                          if (selected) {
                            _selectTool(tool);
                          }
                        },
                      ),
                    );
                  }).toList(),
                ),
              ),
            ],
          ),
        ),
        
        // Color selection
        Padding(
          padding: const EdgeInsets.only(top: 16.0),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              const Text(
                'Colors:',
                style: TextStyle(fontWeight: FontWeight.bold, fontSize: 16),
              ),
              const SizedBox(height: 8),
              SingleChildScrollView(
                scrollDirection: Axis.horizontal,
                child: Row(
                  children: colors.map((color) {
                    return Padding(
                      padding: const EdgeInsets.only(right: 8.0),
                      child: ChoiceChip(
                        label: Text(color),
                        selected: _selectedColor == color,
                        backgroundColor: _getColorFromName(color).withOpacity(0.2),
                        selectedColor: _getColorFromName(color).withOpacity(0.5),
                        onSelected: (selected) {
                          if (selected) {
                            _selectColor(color);
                          }
                        },
                      ),
                    );
                  }).toList(),
                ),
              ),
            ],
          ),
        ),
        
        // Grid options
        Padding(
          padding: const EdgeInsets.only(top: 16.0),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              const Text(
                'Grid:',
                style: TextStyle(fontWeight: FontWeight.bold, fontSize: 16),
              ),
              const SizedBox(height: 8),
              SingleChildScrollView(
                scrollDirection: Axis.horizontal,
                child: Row(
                  children: gridOptions.map((grid) {
                    return Padding(
                      padding: const EdgeInsets.only(right: 8.0),
                      child: ChoiceChip(
                        label: Text(grid),
                        selected: _selectedGrid == grid,
                        onSelected: (selected) {
                          if (selected) {
                            _selectGrid(grid);
                          }
                        },
                      ),
                    );
                  }).toList(),
                ),
              ),
            ],
          ),
        ),
        
        // Action buttons
        Padding(
          padding: const EdgeInsets.only(top: 16.0),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.spaceEvenly,
            children: [
              ElevatedButton.icon(
                onPressed: _undo,
                icon: const Icon(Icons.undo),
                label: const Text('Undo'),
                style: ElevatedButton.styleFrom(
                  backgroundColor: Colors.blue[100],
                  foregroundColor: Colors.blue[900],
                ),
              ),
              ElevatedButton.icon(
                onPressed: _redo,
                icon: const Icon(Icons.redo),
                label: const Text('Redo'),
                style: ElevatedButton.styleFrom(
                  backgroundColor: Colors.green[100],
                  foregroundColor: Colors.green[900],
                ),
              ),
              ElevatedButton.icon(
                onPressed: _clear,
                icon: const Icon(Icons.clear),
                label: const Text('Clear'),
                style: ElevatedButton.styleFrom(
                  backgroundColor: Colors.red[100],
                  foregroundColor: Colors.red[900],
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }
}

class DrawingPoint {
  final Offset offset;
  final Color color;
  final double strokeWidth;
  
  DrawingPoint({
    required this.offset,
    required this.color,
    required this.strokeWidth,
  });
}

class DrawingPainter extends CustomPainter {
  final List<DrawingPoint?> drawingPoints;
  
  DrawingPainter({required this.drawingPoints});
  
  @override
  void paint(Canvas canvas, Size size) {
    for (int i = 0; i < drawingPoints.length - 1; i++) {
      if (drawingPoints[i] != null && drawingPoints[i + 1] != null) {
        canvas.drawLine(
          drawingPoints[i]!.offset,
          drawingPoints[i + 1]!.offset,
          Paint()
            ..color = drawingPoints[i]!.color
            ..strokeWidth = drawingPoints[i]!.strokeWidth
            ..strokeCap = StrokeCap.round,
        );
      } else if (drawingPoints[i] != null && drawingPoints[i + 1] == null) {
        // Draw a single point if it's the only one in the stroke
        canvas.drawPoints(
          ui.PointMode.points,
          [drawingPoints[i]!.offset],
          Paint()
            ..color = drawingPoints[i]!.color
            ..strokeWidth = drawingPoints[i]!.strokeWidth
            ..strokeCap = StrokeCap.round,
        );
      }
    }
  }
  
  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => true;
}

class GridPainter extends CustomPainter {
  final double gridSize;
  
  GridPainter({required this.gridSize});
  
  @override
  void paint(Canvas canvas, Size size) {
    final paint = Paint()
      ..color = Colors.grey[300]!
      ..strokeWidth = 0.5;
    
    // Draw vertical lines
    for (double i = 0; i <= size.width; i += gridSize) {
      canvas.drawLine(Offset(i, 0), Offset(i, size.height), paint);
    }
    
    // Draw horizontal lines
    for (double i = 0; i <= size.height; i += gridSize) {
      canvas.drawLine(Offset(0, i), Offset(size.width, i), paint);
    }
  }
  
  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => false;
}
